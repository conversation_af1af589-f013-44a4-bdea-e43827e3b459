#!/usr/bin/env python3
"""
Script to optimize TTS performance by pre-generating common phrases.
"""

import os
import sys
import asyncio
import argparse
import time
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

try:
    # ...removed SparkTTSService import...
    print("Using real Spark-TTS service")
except ImportError:
    try:
    # ...removed mock SparkTTSService import...
        print("Using mock Spark-TTS service")
    except ImportError:
    print("Error: Could not import TTS service. Make sure you're running this script from the backend directory.")
    sys.exit(1)

# Common phrases that are frequently used in the application
COMMON_PHRASES = [
    "Hello, how can I help you today?",
    "I'm sorry, I didn't understand that.",
    "Could you please repeat that?",
    "Let me think about that for a moment.",
    "Here's what I found for you.",
    "Is there anything else you'd like to know?",
    "Thank you for your question.",
    "I'm processing your request.",
    "I've completed the task.",
    "Would you like me to continue?",
    "Let me show you some examples.",
    "This might take a moment.",
    "I've found several options for you.",
    "Let me know if you need any clarification.",
    "I hope that answers your question.",
    "Is there anything specific you're looking for?",
    "I'm here to assist you with any questions.",
    "Let me know if you'd like more information.",
    "I'll help you with that right away.",
    "I'm analyzing your request.",
]

# Greetings for different times of day
GREETINGS = [
    "Good morning!",
    "Good afternoon!",
    "Good evening!",
    "Welcome back!",
    "Hello there!",
    "Hi, nice to see you!",
]

# Common error messages
ERROR_MESSAGES = [
    "I'm sorry, but I encountered an error.",
    "There was a problem processing your request.",
    "I couldn't complete that action.",
    "Something went wrong. Please try again.",
    "I'm having trouble with that right now.",
]

async def generate_cached_audio(tts_service, phrases, voice_preset_id="default"):
    """Generate and cache audio for common phrases."""
    print(f"Generating cached audio for {len(phrases)} phrases with voice preset: {voice_preset_id}")

    total_start_time = time.time()
    success_count = 0

    for i, phrase in enumerate(phrases):
        try:
            print(f"[{i+1}/{len(phrases)}] Generating: \"{phrase}\"")
            start_time = time.time()

            # Generate speech with caching enabled
            await tts_service.synthesize_speech(
                text=phrase,
                preset_id=voice_preset_id,
                use_cache=True
            )

            elapsed = time.time() - start_time
            print(f"  ✓ Completed in {elapsed:.2f} seconds")
            success_count += 1

        except Exception as e:
            print(f"  ✗ Error: {str(e)}")

    total_elapsed = time.time() - total_start_time
    print(f"\nGenerated {success_count}/{len(phrases)} phrases in {total_elapsed:.2f} seconds")
    print(f"Average time per phrase: {total_elapsed/len(phrases):.2f} seconds")

async def main():
    parser = argparse.ArgumentParser(description="Optimize TTS performance by pre-generating common phrases")
    parser.add_argument("--voice", type=str, default="default", help="Voice preset ID to use")
    parser.add_argument("--all", action="store_true", help="Generate all phrase categories")
    parser.add_argument("--common", action="store_true", help="Generate common phrases")
    parser.add_argument("--greetings", action="store_true", help="Generate greetings")
    parser.add_argument("--errors", action="store_true", help="Generate error messages")
    args = parser.parse_args()

    # If no specific category is selected, default to --common
    if not (args.all or args.common or args.greetings or args.errors):
        args.common = True

    # Initialize TTS service
    print("Initializing Spark-TTS service...")
    # ...removed SparkTTSService usage...
    print("Initialization complete")

    # Create list of phrases to generate
    phrases_to_generate = []

    if args.all or args.common:
        phrases_to_generate.extend(COMMON_PHRASES)

    if args.all or args.greetings:
        phrases_to_generate.extend(GREETINGS)

    if args.all or args.errors:
        phrases_to_generate.extend(ERROR_MESSAGES)

    # Generate cached audio
    # ...removed tts_service usage...

    print("\nOptimization complete! The next time these phrases are used, they will be served from cache.")

if __name__ == "__main__":
    asyncio.run(main())
