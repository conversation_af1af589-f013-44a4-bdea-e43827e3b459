"""
AI Request Logger Middleware

This middleware automatically logs AI requests and responses for analytics purposes.
It intercepts requests to AI endpoints and logs them to the AI Data Collector.
"""

import logging
import time
import uuid
from typing import Dict, Any, Callable, Awaitable
from fastapi import Request, Response
import json

from backend.services.monitoring.ai_data_collector import AIDataCollector

logger = logging.getLogger(__name__)

class AIRequestLoggerMiddleware:
    """
    Middleware to log AI requests and responses for analytics.
    """
    
    def __init__(
        self,
        app: Any,
        ai_endpoints: list[str] = None,
    ):
        """
        Initialize the middleware.
        
        Args:
            app: The FastAPI application
            ai_endpoints: List of endpoint paths to monitor (e.g., ['/api/v1/ai/generate'])
        """
        self.app = app
        self.ai_endpoints = ai_endpoints or [
            '/api/v1/ai/generate',
            '/api/v1/ai/chat',
            '/api/v1/ai/analyze-image',
            '/api/v1/ai/analyze-audio'
        ]
    
    async def __call__(
        self,
        request: Request,
        call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """
        Process the request and log AI requests.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            The response
        """
        # Check if this is an AI endpoint
        path = request.url.path
        if not any(path.startswith(endpoint) for endpoint in self.ai_endpoints):
            # Not an AI endpoint, skip logging
            return await call_next(request)
        
        # Generate a request ID
        request_id = str(uuid.uuid4())
        
        # Get the request body
        body = await request.body()
        body_str = body.decode('utf-8')
        
        try:
            body_json = json.loads(body_str)
        except json.JSONDecodeError:
            body_json = {}
        
        # Extract request data
        feature_id = self._extract_feature_id(path, body_json)
        provider = body_json.get('provider', 'unknown')
        model = body_json.get('model', 'unknown')
        parameters = body_json.get('parameters', {})
        prompt = body_json.get('prompt', '')
        if not prompt and 'messages' in body_json:
            # Extract prompt from chat messages
            messages = body_json.get('messages', [])
            prompt = '\n'.join([f"{msg.get('role', 'user')}: {msg.get('content', '')}" for msg in messages])
        
        # Get user ID if available
        user_id = None
        if request.headers.get('Authorization'):
            # In a real implementation, you would extract the user ID from the token
            # For now, we'll just use a placeholder
            user_id = 'user-123'
        
        # Measure response time
        start_time = time.time()
        
        # Create a modified request with the same body for the next handler
        request._body = body
        
        # Call the next handler
        response = await call_next(request)
        
        # Calculate response time
        response_time_ms = (time.time() - start_time) * 1000
        
        # Get response data
        response_body = b''
        async for chunk in response.body_iterator:
            response_body += chunk
        
        # Create a new response with the same body
        new_response = Response(
            content=response_body,
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.media_type
        )
        
        # Parse response body
        try:
            response_json = json.loads(response_body.decode('utf-8'))
            response_text = response_json.get('response', '')
            token_count = response_json.get('token_count', None)
        except (json.JSONDecodeError, UnicodeDecodeError):
            response_text = ''
            token_count = None
        
        # Determine if the request was successful
        success = 200 <= response.status_code < 300
        
        # Extract error information if the request failed
        error_type = None
        error_message = None
        if not success:
            try:
                error_data = json.loads(response_body.decode('utf-8'))
                error_type = error_data.get('error', 'unknown')
                error_message = error_data.get('detail', 'Unknown error')
            except (json.JSONDecodeError, UnicodeDecodeError):
                error_type = 'unknown'
                error_message = f'HTTP {response.status_code}'
        
        # Log the request
        try:
            AIDataCollector.log_request(
                request_id=request_id,
                feature_id=feature_id,
                provider=provider,
                model=model,
                parameters=parameters,
                prompt=prompt,
                response=response_text,
                response_time_ms=response_time_ms,
                token_count=token_count,
                success=success,
                error_type=error_type,
                error_message=error_message,
                user_id=user_id,
                metadata={
                    'path': path,
                    'method': request.method,
                    'status_code': response.status_code
                }
            )
        except Exception as e:
            logger.error(f"Error logging AI request: {str(e)}")
        
        return new_response
    
    def _extract_feature_id(self, path: str, body_json: Dict[str, Any]) -> str:
        """
        Extract the feature ID from the request path or body.
        
        Args:
            path: The request path
            body_json: The request body as JSON
            
        Returns:
            The feature ID
        """
        # Try to extract from body first
        if 'feature_id' in body_json:
            return body_json['feature_id']
        
        # Extract from path
        parts = path.strip('/').split('/')
        if len(parts) >= 3:
            return parts[2]  # e.g., /api/v1/ai/generate -> 'generate'
        
        return 'unknown'
