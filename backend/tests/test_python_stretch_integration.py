"""
Test python-stretch library integration for beatmatching time stretching.

This test verifies that python-stretch works correctly with our audio processing pipeline
and can perform high-quality time stretching for beatmatching purposes.
"""

import pytest
import numpy as np
import librosa
import tempfile
import os
from pathlib import Path
import soundfile as sf

try:
    import python_stretch.Signalsmith as ps
    PYTHON_STRETCH_AVAILABLE = True
except ImportError:
    PYTHON_STRETCH_AVAILABLE = False


@pytest.mark.skipif(not PYTHON_STRETCH_AVAILABLE, reason="python-stretch not available")
class TestPythonStretchIntegration:
    """Test python-stretch integration for beatmatching."""

    def create_test_audio(self, duration=5.0, sample_rate=44100, frequency=440.0):
        """Create a test audio signal (sine wave)."""
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        # Create a simple sine wave with some harmonics for more realistic audio
        audio = (
            0.5 * np.sin(2 * np.pi * frequency * t) +
            0.3 * np.sin(2 * np.pi * frequency * 2 * t) +
            0.2 * np.sin(2 * np.pi * frequency * 3 * t)
        )
        return audio.astype(np.float32)

    def stretch_audio_with_python_stretch(self, audio, time_factor, sample_rate=44100):
        """
        Stretch audio using python-stretch library.

        Args:
            audio: Input audio as numpy array (mono or stereo)
            time_factor: Time stretching factor (>1 = slower, <1 = faster)
            sample_rate: Sample rate of the audio

        Returns:
            Stretched audio as numpy array
        """
        # Ensure audio is 2D (channels, samples)
        if audio.ndim == 1:
            audio_2d = audio.reshape(1, -1)
            n_channels = 1
        else:
            audio_2d = audio.T  # python-stretch expects (channels, samples)
            n_channels = audio_2d.shape[0]

        # Create and configure the stretch processor
        stretch = ps.Stretch()
        stretch.preset(n_channels, sample_rate)
        stretch.setTimeFactor(time_factor)

        # Process the audio
        stretched_2d = stretch.process(audio_2d)

        # Convert back to original format
        if n_channels == 1:
            return stretched_2d.flatten()
        else:
            return stretched_2d.T

    def test_python_stretch_basic_functionality(self):
        """Test basic python-stretch functionality."""
        # Create test audio
        audio = self.create_test_audio(duration=2.0)
        sample_rate = 44100

        # Test time stretching (slower) - note: time_factor > 1 means slower
        time_factor = 1.25  # 25% slower (0.8x speed)
        stretched_audio = self.stretch_audio_with_python_stretch(audio, time_factor, sample_rate)

        # Verify output
        assert isinstance(stretched_audio, np.ndarray)
        assert stretched_audio.dtype == np.float32

        # Stretched audio should be longer
        expected_length = int(len(audio) * time_factor)
        actual_length = len(stretched_audio)
        length_diff = abs(actual_length - expected_length)
        tolerance = sample_rate * 0.1  # 100ms tolerance

        print(f"   Debug: Original length: {len(audio)}, Expected: {expected_length}, Actual: {actual_length}")
        print(f"   Debug: Length difference: {length_diff}, Tolerance: {tolerance}")

        # Allow some tolerance for processing differences
        assert length_diff < tolerance, f"Length difference {length_diff} exceeds tolerance {tolerance}"

        print(f"✅ Basic stretch test passed:")
        print(f"   Original length: {len(audio)} samples ({len(audio)/sample_rate:.2f}s)")
        print(f"   Stretched length: {len(stretched_audio)} samples ({len(stretched_audio)/sample_rate:.2f}s)")
        print(f"   Time factor: {time_factor} (expected {time_factor:.2f}x longer)")

    def test_python_stretch_speed_up(self):
        """Test python-stretch for speeding up audio."""
        # Create test audio
        audio = self.create_test_audio(duration=2.0)
        sample_rate = 44100

        # Test time stretching (faster) - note: time_factor < 1 means faster
        time_factor = 0.8  # 20% faster (1.25x speed)
        stretched_audio = self.stretch_audio_with_python_stretch(audio, time_factor, sample_rate)

        # Verify output
        assert isinstance(stretched_audio, np.ndarray)
        assert stretched_audio.dtype == np.float32

        # Stretched audio should be shorter
        expected_length = int(len(audio) * time_factor)
        # Allow some tolerance for processing differences
        assert abs(len(stretched_audio) - expected_length) < sample_rate * 0.1  # 100ms tolerance

        print(f"✅ Speed up test passed:")
        print(f"   Original length: {len(audio)} samples ({len(audio)/sample_rate:.2f}s)")
        print(f"   Stretched length: {len(stretched_audio)} samples ({len(stretched_audio)/sample_rate:.2f}s)")
        print(f"   Time factor: {time_factor} (expected {time_factor:.2f}x shorter)")

    def test_beatmatching_stretch_ratios(self):
        """Test stretch ratios commonly used in beatmatching."""
        audio = self.create_test_audio(duration=1.0)
        sample_rate = 44100

        # Common beatmatching scenarios - convert to time factors
        test_cases = [
            (120, 115, 120/115),  # 120 BPM -> 115 BPM (slower)
            (128, 120, 128/120),  # 128 BPM -> 120 BPM (slower)
            (110, 120, 110/120),  # 110 BPM -> 120 BPM (faster)
            (140, 130, 140/130),  # 140 BPM -> 130 BPM (slower)
        ]

        for original_bpm, target_bpm, time_factor in test_cases:
            stretched_audio = self.stretch_audio_with_python_stretch(audio, time_factor, sample_rate)

            assert isinstance(stretched_audio, np.ndarray)
            assert len(stretched_audio) > 0

            direction = "slower" if time_factor > 1 else "faster"
            print(f"✅ Beatmatching test: {original_bpm} BPM -> {target_bpm} BPM (time factor: {time_factor:.3f}, {direction})")

    def test_python_stretch_with_librosa_integration(self):
        """Test python-stretch integration with librosa workflow."""
        # Create a temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
            try:
                # Create and save test audio
                audio = self.create_test_audio(duration=3.0)
                sample_rate = 44100
                sf.write(tmp_file.name, audio, sample_rate)
                
                # Load with librosa (our standard workflow)
                y, sr = librosa.load(tmp_file.name, sr=None)
                
                # Apply time stretching
                time_factor = 1.1  # Slightly slower (0.9x speed)
                stretched_y = self.stretch_audio_with_python_stretch(y, time_factor, sr)
                
                # Verify integration
                assert isinstance(stretched_y, np.ndarray)
                assert stretched_y.dtype == np.float32
                
                # Test that we can still use librosa functions on stretched audio
                tempo, beats = librosa.beat.beat_track(y=stretched_y, sr=sr)
                assert tempo > 0
                assert len(beats) > 0
                
                print(f"✅ Librosa integration test passed:")
                print(f"   Detected tempo in stretched audio: {tempo:.1f} BPM")
                print(f"   Detected {len(beats)} beats")
                
            finally:
                # Clean up
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)

    def test_python_stretch_quality_assessment(self):
        """Test audio quality after stretching."""
        # Create test audio with more complex content
        duration = 2.0
        sample_rate = 44100
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Create more complex audio (multiple frequencies)
        audio = (
            0.4 * np.sin(2 * np.pi * 220 * t) +  # A3
            0.3 * np.sin(2 * np.pi * 330 * t) +  # E4
            0.2 * np.sin(2 * np.pi * 440 * t) +  # A4
            0.1 * np.sin(2 * np.pi * 660 * t)    # E5
        ).astype(np.float32)
        
        # Test different time factors
        time_factors = [1.25, 1.1, 1.0, 0.9, 0.8]  # slower to faster

        for time_factor in time_factors:
            if time_factor == 1.0:
                stretched_audio = audio  # No stretching
            else:
                stretched_audio = self.stretch_audio_with_python_stretch(audio, time_factor, sample_rate)
            
            # Basic quality checks
            assert not np.any(np.isnan(stretched_audio)), f"NaN values found with time factor {time_factor}"
            assert not np.any(np.isinf(stretched_audio)), f"Inf values found with time factor {time_factor}"

            # Check dynamic range is preserved
            original_range = np.max(audio) - np.min(audio)
            stretched_range = np.max(stretched_audio) - np.min(stretched_audio)
            range_ratio = stretched_range / original_range

            # Dynamic range should be reasonably preserved (within 50%)
            assert 0.5 <= range_ratio <= 2.0, f"Dynamic range changed too much with time factor {time_factor}: {range_ratio:.2f}"

            print(f"✅ Quality test passed for time factor {time_factor:.1f}: dynamic range ratio {range_ratio:.2f}")


if __name__ == "__main__":
    # Run tests directly
    test_instance = TestPythonStretchIntegration()
    
    if not PYTHON_STRETCH_AVAILABLE:
        print("❌ python-stretch not available. Please install with: pip install python-stretch")
        exit(1)
    
    print("🎵 Testing python-stretch integration for beatmatching...")
    print("=" * 60)
    
    try:
        test_instance.test_python_stretch_basic_functionality()
        test_instance.test_python_stretch_speed_up()
        test_instance.test_beatmatching_stretch_ratios()
        test_instance.test_python_stretch_with_librosa_integration()
        test_instance.test_python_stretch_quality_assessment()
        
        print("=" * 60)
        print("🎉 All python-stretch integration tests passed!")
        print("✅ Ready for Phase 2 beatmatching implementation")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
