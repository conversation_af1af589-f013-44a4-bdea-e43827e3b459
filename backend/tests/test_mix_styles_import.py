import pytest
from fastapi.testclient import TestClient
from backend.main import app
from backend import __version__ as app_version

client = TestClient(app)

API_PREFIX = "/api/v1"
IMPORT_PATH = f"{API_PREFIX}/mix-styles/import"

# Minimal valid style import payload
def style_payload(version, style_id="test_style", name="Test Style"):
    return {
        "style_id": style_id,
        "name": name,
        "description": "A test style",
        "energy_pattern": [10, 20, 30],
        "min_bpm": 120,
        "max_bpm": 130,
        "key_rules": ["PERFECT", "ENERGY"],
        "constraints": {},
        "generator_types": ["smart"],
        "icon": None,
        "version": version
    }

def test_import_style_matching_version():
    payload = style_payload("1.0.0", style_id="style_match", name="Test Style Match")
    response = client.post(IMPORT_PATH, json=payload)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "imported successfully" in data["message"].lower()
    assert data["warnings"] == [] or isinstance(data["warnings"], list)

def test_import_style_older_version():
    payload = style_payload("0.9.0", style_id="style_old", name="Test Style Old")
    response = client.post(IMPORT_PATH, json=payload)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert any("older version" in w.lower() for w in data["warnings"])

def test_import_style_newer_version():
    payload = style_payload("99.0.0", style_id="style_new", name="Test Style New")
    response = client.post(IMPORT_PATH, json=payload)
    assert response.status_code == 400
    data = response.json()
    assert "newer version" in data["detail"].lower()

def test_import_style_missing_version():
    payload = style_payload("1.0.0", style_id="style_noversion", name="Test Style NoVersion")
    del payload["version"]
    response = client.post(IMPORT_PATH, json=payload)
    # Should import without version check (treat as compatible)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "imported successfully" in data["message"].lower()
