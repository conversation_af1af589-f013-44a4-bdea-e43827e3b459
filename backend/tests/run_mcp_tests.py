#!/usr/bin/env python
"""
Test runner for MCP tests.
"""

import unittest
import sys
import os
import pytest

# Add the parent directory to the path so we can import the backend modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

def run_unittest_tests():
    """Run unittest-based tests."""
    # Discover and run all tests in the services/ai directory
    loader = unittest.TestLoader()
    start_dir = os.path.join(os.path.dirname(__file__), 'services/ai')
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return True if all tests passed, False otherwise
    return result.wasSuccessful()

def run_pytest_tests():
    """Run pytest-based tests."""
    # Run all tests in the services/ai directory
    start_dir = os.path.join(os.path.dirname(__file__), 'services/ai')
    result = pytest.main(['-xvs', start_dir])
    
    # Return True if all tests passed, False otherwise
    return result == 0

if __name__ == '__main__':
    # Run both unittest and pytest tests
    unittest_success = run_unittest_tests()
    pytest_success = run_pytest_tests()
    
    # Exit with non-zero status if any tests failed
    if not (unittest_success and pytest_success):
        sys.exit(1)
