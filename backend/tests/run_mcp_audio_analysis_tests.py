#!/usr/bin/env python3
"""
Test runner for MCP audio analysis tests.
"""

import os
import sys
import unittest
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))


def run_tests(test_type='all', verbose=False):
    """
    Run the specified tests.
    
    Args:
        test_type: Type of tests to run ('unit', 'integration', 'all')
        verbose: Whether to show verbose output
    
    Returns:
        True if all tests pass, False otherwise
    """
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add tests based on test_type
    if test_type in ['unit', 'all']:
        logger.info("Adding unit tests to test suite")
        from backend.tests.services.ai.test_mcp_audio_analysis import TestMCPAudioAnalysisServer
        unit_tests = unittest.TestLoader().loadTestsFromTestCase(TestMCPAudioAnalysisServer)
        test_suite.addTest(unit_tests)
    
    if test_type in ['integration', 'all']:
        logger.info("Adding integration tests to test suite")
        from backend.tests.services.ai.test_mcp_audio_analysis_integration import TestMCPAudioAnalysisIntegration
        integration_tests = unittest.TestLoader().loadTestsFromTestCase(TestMCPAudioAnalysisIntegration)
        test_suite.addTest(integration_tests)
    
    # Run the tests
    verbosity = 2 if verbose else 1
    test_runner = unittest.TextTestRunner(verbosity=verbosity)
    result = test_runner.run(test_suite)
    
    # Return True if all tests pass
    return result.wasSuccessful()


def main():
    """Main entry point for the test runner."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run MCP audio analysis tests')
    parser.add_argument('--type', choices=['unit', 'integration', 'all'], default='all',
                        help='Type of tests to run (default: all)')
    parser.add_argument('--verbose', action='store_true',
                        help='Show verbose output')
    args = parser.parse_args()
    
    # Run the tests
    logger.info(f"Running {args.type} tests")
    success = run_tests(args.type, args.verbose)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
