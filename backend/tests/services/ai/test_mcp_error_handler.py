"""
Unit tests for the MCP error handler component.
"""

import unittest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
import pytest

from backend.services.ai.mcp_error_handler import (
    MCPError<PERSON><PERSON><PERSON>, MCPError, MCPErrorType, MCPErrorCategory, MCPErrorSeverity
)


class TestMCPError(unittest.TestCase):
    """Test cases for the MCPError class."""

    def test_initialization(self):
        """Test error initialization."""
        error = MCPError(
            message="Test error",
            error_type=MCPErrorType.CONNECTION_ERROR,
            severity=MCPErrorSeverity.HIGH,
            category=MCPErrorCategory.TRANSIENT,
            details={"test": "detail"}
        )
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.error_type, MCPErrorType.CONNECTION_ERROR)
        self.assertEqual(error.severity, MCPErrorSeverity.HIGH)
        self.assertEqual(error.category, MCPErrorCategory.TRANSIENT)
        self.assertEqual(error.details["test"], "detail")
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        error = MCPError(
            message="Test error",
            error_type=MCPErrorType.CONNECTION_ERROR,
            severity=MCPErrorSeverity.HIGH,
            category=MCPErrorCategory.TRANSIENT,
            details={"test": "detail"}
        )
        
        error_dict = error.to_dict()
        
        self.assertEqual(error_dict["error"], "Test error")
        self.assertEqual(error_dict["error_type"], MCPErrorType.CONNECTION_ERROR)
        self.assertEqual(error_dict["severity"], MCPErrorSeverity.HIGH)
        self.assertEqual(error_dict["category"], MCPErrorCategory.TRANSIENT)
        self.assertEqual(error_dict["details"]["test"], "detail")
    
    def test_is_retryable(self):
        """Test retryable check."""
        # Transient error should be retryable
        error1 = MCPError(
            message="Test error",
            category=MCPErrorCategory.TRANSIENT
        )
        self.assertTrue(error1.is_retryable())
        
        # Permanent error should not be retryable
        error2 = MCPError(
            message="Test error",
            category=MCPErrorCategory.PERMANENT
        )
        self.assertFalse(error2.is_retryable())


class TestMCPErrorHandler(unittest.TestCase):
    """Test cases for the MCPErrorHandler class."""

    def test_categorize_error_connection(self):
        """Test categorization of connection errors."""
        # Test connection error
        error = Exception("Connection failed")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.CONNECTION_ERROR)
        self.assertEqual(mcp_error.category, MCPErrorCategory.TRANSIENT)
    
    def test_categorize_error_timeout(self):
        """Test categorization of timeout errors."""
        # Test timeout error
        error = TimeoutError("Operation timed out")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.TIMEOUT_ERROR)
        self.assertEqual(mcp_error.category, MCPErrorCategory.TRANSIENT)
    
    def test_categorize_error_validation(self):
        """Test categorization of validation errors."""
        # Test validation error
        error = Exception("Invalid parameter: expected string, got integer")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.VALIDATION_ERROR)
        self.assertEqual(mcp_error.category, MCPErrorCategory.VALIDATION)
    
    def test_categorize_error_tool_not_found(self):
        """Test categorization of tool not found errors."""
        # Test tool not found error
        error = Exception("Tool 'test_tool' not found")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.TOOL_NOT_FOUND)
        self.assertEqual(mcp_error.category, MCPErrorCategory.PERMANENT)
    
    def test_categorize_error_permission(self):
        """Test categorization of permission errors."""
        # Test permission error
        error = Exception("Permission denied")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.PERMISSION_ERROR)
        self.assertEqual(mcp_error.category, MCPErrorCategory.SECURITY)
    
    def test_categorize_error_resource(self):
        """Test categorization of resource errors."""
        # Test resource error
        error = Exception("Out of memory")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.RESOURCE_ERROR)
        self.assertEqual(mcp_error.category, MCPErrorCategory.RESOURCE)
    
    def test_categorize_error_unknown(self):
        """Test categorization of unknown errors."""
        # Test unknown error
        error = Exception("Something went wrong")
        mcp_error = MCPErrorHandler.categorize_error(error)
        
        self.assertEqual(mcp_error.error_type, MCPErrorType.UNKNOWN_ERROR)
        self.assertEqual(mcp_error.category, MCPErrorCategory.UNKNOWN)
    
    def test_format_error_response_mcp_error(self):
        """Test formatting of MCPError as response."""
        # Test MCPError
        error = MCPError(
            message="Test error",
            error_type=MCPErrorType.CONNECTION_ERROR,
            severity=MCPErrorSeverity.HIGH,
            category=MCPErrorCategory.TRANSIENT
        )
        response = MCPErrorHandler.format_error_response(error)
        
        self.assertEqual(response["error"], "Test error")
        self.assertEqual(response["error_type"], MCPErrorType.CONNECTION_ERROR)
        self.assertEqual(response["severity"], MCPErrorSeverity.HIGH)
        self.assertEqual(response["category"], MCPErrorCategory.TRANSIENT)
    
    def test_format_error_response_exception(self):
        """Test formatting of Exception as response."""
        # Test Exception
        error = Exception("Test error")
        response = MCPErrorHandler.format_error_response(error)
        
        self.assertEqual(response["error"], "Unknown error: Test error")
        self.assertEqual(response["error_type"], MCPErrorType.UNKNOWN_ERROR)
    
    def test_format_error_response_dict(self):
        """Test formatting of dictionary as response."""
        # Test dictionary
        error = {"error": "Test error"}
        response = MCPErrorHandler.format_error_response(error)
        
        self.assertEqual(response["error"], "Test error")


@pytest.mark.asyncio
class TestMCPErrorHandlerAsync:
    """Async test cases for the MCPErrorHandler class."""
    
    async def test_with_retry_success(self):
        """Test retry logic with successful function."""
        # Create a mock function that succeeds
        mock_func = AsyncMock(return_value="success")
        
        # Call with_retry
        result = await MCPErrorHandler.with_retry(
            mock_func,
            "arg1",
            "arg2",
            kwarg1="value1",
            max_retries=3
        )
        
        # Verify the result
        assert result == "success"
        mock_func.assert_called_once_with("arg1", "arg2", kwarg1="value1")
    
    async def test_with_retry_transient_error(self):
        """Test retry logic with transient error."""
        # Create a mock function that fails with a transient error
        mock_func = AsyncMock(side_effect=[
            MCPError(
                message="Transient error",
                category=MCPErrorCategory.TRANSIENT
            ),
            "success"
        ])
        
        # Call with_retry
        result = await MCPErrorHandler.with_retry(
            mock_func,
            max_retries=3,
            initial_delay=0.01
        )
        
        # Verify the result
        assert result == "success"
        assert mock_func.call_count == 2
    
    async def test_with_retry_permanent_error(self):
        """Test retry logic with permanent error."""
        # Create a mock function that fails with a permanent error
        mock_func = AsyncMock(side_effect=MCPError(
            message="Permanent error",
            category=MCPErrorCategory.PERMANENT
        ))
        
        # Call with_retry and expect an exception
        with pytest.raises(MCPError) as excinfo:
            await MCPErrorHandler.with_retry(
                mock_func,
                max_retries=3,
                initial_delay=0.01
            )
        
        # Verify the exception
        assert "Permanent error" in str(excinfo.value)
        assert mock_func.call_count == 1
    
    async def test_with_retry_max_retries(self):
        """Test retry logic with maximum retries reached."""
        # Create a mock function that always fails with a transient error
        mock_func = AsyncMock(side_effect=MCPError(
            message="Transient error",
            category=MCPErrorCategory.TRANSIENT
        ))
        
        # Call with_retry and expect an exception
        with pytest.raises(MCPError) as excinfo:
            await MCPErrorHandler.with_retry(
                mock_func,
                max_retries=2,
                initial_delay=0.01
            )
        
        # Verify the exception
        assert "Transient error" in str(excinfo.value)
        assert mock_func.call_count == 3  # Initial call + 2 retries


if __name__ == '__main__':
    unittest.main()
