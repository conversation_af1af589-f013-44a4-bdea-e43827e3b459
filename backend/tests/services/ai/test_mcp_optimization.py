"""
Unit tests for the MCP optimization service.
"""

import unittest
from unittest.mock import patch, MagicMock
import pytest

from backend.services.ai.mcp_optimization import MCPOptimizationService


class TestMCPOptimizationService(unittest.TestCase):
    """Test cases for the MCPOptimizationService class."""

    def test_collect_performance_metrics(self):
        """Test collecting performance metrics."""
        # Mock the dependencies
        with patch('backend.services.ai.mcp_optimization.AISettingsMonitor') as mock_monitor, \
             patch('backend.services.ai.mcp_optimization.mcp_cache') as mock_cache, \
             patch('backend.services.ai.mcp_optimization.mcp_async_executor') as mock_executor:
            # Configure the mocks
            mock_monitor.get_tool_metrics.return_value = {"tool1": {"calls": 10}}
            mock_cache.get_stats.return_value = {"hit_rate": 75.0}
            mock_executor.tasks = {
                "task-1": MagicMock(status="completed"),
                "task-2": MagicMock(status="running")
            }
            
            # Call the method
            metrics = MCPOptimizationService.collect_performance_metrics()
            
            # Verify the result
            self.assertIn("tool_metrics", metrics)
            self.assertIn("cache_stats", metrics)
            self.assertIn("async_stats", metrics)
            self.assertIn("timestamp", metrics)
            
            # Verify the tool metrics
            self.assertEqual(metrics["tool_metrics"]["tool1"]["calls"], 10)
            
            # Verify the cache stats
            self.assertEqual(metrics["cache_stats"]["hit_rate"], 75.0)
            
            # Verify the async stats
            self.assertEqual(metrics["async_stats"]["total_tasks"], 2)
            self.assertEqual(metrics["async_stats"]["completed_tasks"], 1)
            self.assertEqual(metrics["async_stats"]["running_tasks"], 1)
    
    def test_analyze_tool_performance(self):
        """Test analyzing tool performance."""
        # Mock the dependencies
        with patch('backend.services.ai.mcp_optimization.AISettingsMonitor') as mock_monitor:
            # Configure the mocks
            mock_monitor.get_tool_metrics.return_value = {
                "execution_times": [100, 200, 300]
            }
            
            # Call the method
            metrics = MCPOptimizationService.analyze_tool_performance("test_tool")
            
            # Verify the result
            self.assertIn("execution_times", metrics)
            self.assertIn("avg_execution_time", metrics)
            self.assertIn("median_execution_time", metrics)
            self.assertIn("min_execution_time", metrics)
            self.assertIn("max_execution_time", metrics)
            
            # Verify the calculated metrics
            self.assertEqual(metrics["avg_execution_time"], 200)
            self.assertEqual(metrics["median_execution_time"], 200)
            self.assertEqual(metrics["min_execution_time"], 100)
            self.assertEqual(metrics["max_execution_time"], 300)
    
    def test_generate_optimization_recommendations(self):
        """Test generating optimization recommendations."""
        # Mock the dependencies
        with patch.object(MCPOptimizationService, 'collect_performance_metrics') as mock_collect:
            # Configure the mocks
            mock_collect.return_value = {
                "cache_stats": {
                    "hit_rate": 40.0,
                    "size": 90,
                    "max_size": 100
                },
                "async_stats": {
                    "pending_tasks": 10,
                    "running_tasks": 2
                },
                "tool_metrics": {
                    "slow_tool": {
                        "avg_execution_time": 1500,
                        "error_rate": 0.05
                    },
                    "error_tool": {
                        "avg_execution_time": 500,
                        "error_rate": 0.15
                    }
                }
            }
            
            # Call the method
            recommendations = MCPOptimizationService.generate_optimization_recommendations()
            
            # Verify the result
            self.assertIsInstance(recommendations, list)
            self.assertGreater(len(recommendations), 0)
            
            # Verify that we have recommendations for different categories
            categories = set()
            for rec in recommendations:
                self.assertIn("id", rec)
                self.assertIn("feature_id", rec)
                self.assertIn("parameter_name", rec)
                self.assertIn("current_value", rec)
                self.assertIn("recommended_value", rec)
                self.assertIn("impact_score", rec)
                self.assertIn("description", rec)
                self.assertIn("reason", rec)
                
                categories.add(rec["parameter_name"].split("_")[0])
            
            # We should have recommendations for cache, async, and tool settings
            self.assertGreaterEqual(len(categories), 2)
    
    def test_apply_optimization_not_found(self):
        """Test applying a nonexistent optimization."""
        # Mock the dependencies
        with patch.object(MCPOptimizationService, 'generate_optimization_recommendations') as mock_generate:
            # Configure the mocks
            mock_generate.return_value = []
            
            # Call the method
            result = MCPOptimizationService.apply_optimization("nonexistent")
            
            # Verify the result
            self.assertFalse(result["success"])
            self.assertIn("not found", result["message"])
    
    def test_apply_optimization_tool_ttl(self):
        """Test applying a tool TTL optimization."""
        # Mock the dependencies
        with patch.object(MCPOptimizationService, 'generate_optimization_recommendations') as mock_generate, \
             patch('backend.services.ai.mcp_optimization.mcp_cache') as mock_cache, \
             patch('backend.services.ai.mcp_optimization.logger') as mock_logger:
            # Configure the mocks
            mock_generate.return_value = [
                {
                    "id": "test-rec",
                    "feature_id": "mcp",
                    "parameter_name": "tool_ttl_settings[test_tool]",
                    "current_value": 3600,
                    "recommended_value": 7200,
                    "impact_score": 75,
                    "description": "Increase cache TTL for test_tool",
                    "reason": "Tool is slow"
                }
            ]
            mock_cache.tool_ttl_settings = {}
            
            # Call the method
            result = MCPOptimizationService.apply_optimization("test-rec")
            
            # Verify the result
            self.assertTrue(result["success"])
            self.assertEqual(mock_cache.tool_ttl_settings["test_tool"], 7200)
    
    def test_apply_optimization_error(self):
        """Test applying an optimization that fails."""
        # Mock the dependencies
        with patch.object(MCPOptimizationService, 'generate_optimization_recommendations') as mock_generate, \
             patch('backend.services.ai.mcp_optimization.logger') as mock_logger:
            # Configure the mocks
            mock_generate.return_value = [
                {
                    "id": "test-rec",
                    "feature_id": "mcp",
                    "parameter_name": "MCP_CACHE_MAX_SIZE",
                    "current_value": 100,
                    "recommended_value": 200,
                    "impact_score": 75,
                    "description": "Increase cache size",
                    "reason": "Cache is almost full"
                }
            ]
            
            # Make the update raise an exception
            mock_logger.info.side_effect = Exception("Test error")
            
            # Call the method
            result = MCPOptimizationService.apply_optimization("test-rec")
            
            # Verify the result
            self.assertFalse(result["success"])
            self.assertIn("Error", result["message"])


if __name__ == '__main__':
    unittest.main()
