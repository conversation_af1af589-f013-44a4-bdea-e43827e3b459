"""
Unit tests for the MCP cache component.
"""

import unittest
import time
from unittest.mock import patch, MagicMock
import pytest

from backend.services.ai.mcp_cache import MCPToolResultsCache


class TestMCPCache(unittest.TestCase):
    """Test cases for the MCP cache component."""

    def setUp(self):
        """Set up test fixtures."""
        self.cache = MCPToolResultsCache(max_size=10, default_ttl=60)
    
    def test_initialization(self):
        """Test cache initialization."""
        self.assertIsNotNone(self.cache)
        self.assertEqual(self.cache.max_size, 10)
        self.assertEqual(self.cache.default_ttl, 60)
        self.assertTrue(self.cache.enabled)
        self.assertEqual(len(self.cache.cache), 0)
    
    def test_generate_key(self):
        """Test key generation."""
        # Generate keys for different tools and parameters
        key1 = self.cache._generate_key("tool1", {"param1": "value1"})
        key2 = self.cache._generate_key("tool1", {"param1": "value2"})
        key3 = self.cache._generate_key("tool2", {"param1": "value1"})
        
        # Verify that the keys are different
        self.assertNotEqual(key1, key2)
        self.assertNotEqual(key1, key3)
        self.assertNotEqual(key2, key3)
        
        # Verify that the same tool and parameters generate the same key
        key4 = self.cache._generate_key("tool1", {"param1": "value1"})
        self.assertEqual(key1, key4)
    
    def test_set_and_get(self):
        """Test setting and getting cache entries."""
        # Set a cache entry
        self.cache.set("tool1", {"param1": "value1"}, "result1")
        
        # Get the cache entry
        result = self.cache.get("tool1", {"param1": "value1"})
        
        # Verify the result
        self.assertEqual(result, "result1")
    
    def test_get_nonexistent(self):
        """Test getting a nonexistent cache entry."""
        # Get a nonexistent cache entry
        result = self.cache.get("tool1", {"param1": "value1"})
        
        # Verify the result
        self.assertIsNone(result)
    
    def test_get_expired(self):
        """Test getting an expired cache entry."""
        # Set a cache entry with a short TTL
        self.cache.set("tool1", {"param1": "value1"}, "result1", ttl=0.1)
        
        # Wait for the entry to expire
        time.sleep(0.2)
        
        # Get the cache entry
        result = self.cache.get("tool1", {"param1": "value1"})
        
        # Verify the result
        self.assertIsNone(result)
    
    def test_clear(self):
        """Test clearing the cache."""
        # Set some cache entries
        self.cache.set("tool1", {"param1": "value1"}, "result1")
        self.cache.set("tool2", {"param1": "value1"}, "result2")
        
        # Clear the cache
        self.cache.clear()
        
        # Verify that the cache is empty
        self.assertEqual(len(self.cache.cache), 0)
        
        # Verify that the entries are gone
        self.assertIsNone(self.cache.get("tool1", {"param1": "value1"}))
        self.assertIsNone(self.cache.get("tool2", {"param1": "value1"}))
    
    def test_evict_oldest_entries(self):
        """Test eviction of oldest entries when cache is full."""
        # Set the max size to 2
        self.cache.max_size = 2
        
        # Set more entries than the max size
        self.cache.set("tool1", {"param1": "value1"}, "result1")
        time.sleep(0.1)  # Ensure different timestamps
        self.cache.set("tool2", {"param1": "value1"}, "result2")
        time.sleep(0.1)  # Ensure different timestamps
        self.cache.set("tool3", {"param1": "value1"}, "result3")
        
        # Verify that the oldest entry was evicted
        self.assertIsNone(self.cache.get("tool1", {"param1": "value1"}))
        self.assertIsNotNone(self.cache.get("tool2", {"param1": "value1"}))
        self.assertIsNotNone(self.cache.get("tool3", {"param1": "value1"}))
    
    def test_get_stats(self):
        """Test getting cache statistics."""
        # Set some cache entries
        self.cache.set("tool1", {"param1": "value1"}, "result1")
        self.cache.get("tool1", {"param1": "value1"})  # Hit
        self.cache.get("tool2", {"param1": "value1"})  # Miss
        
        # Get the stats
        stats = self.cache.get_stats()
        
        # Verify the stats
        self.assertEqual(stats["size"], 1)
        self.assertEqual(stats["max_size"], 10)
        self.assertEqual(stats["hit_count"], 1)
        self.assertEqual(stats["miss_count"], 1)
        self.assertEqual(stats["hit_rate"], 50.0)
        self.assertEqual(stats["enabled"], True)
    
    def test_set_enabled(self):
        """Test enabling and disabling the cache."""
        # Disable the cache
        self.cache.set_enabled(False)
        
        # Verify that the cache is disabled
        self.assertFalse(self.cache.enabled)
        self.assertFalse(self.cache.is_enabled())
        
        # Set a cache entry
        self.cache.set("tool1", {"param1": "value1"}, "result1")
        
        # Verify that the entry was not cached
        self.assertEqual(len(self.cache.cache), 0)
        
        # Enable the cache
        self.cache.set_enabled(True)
        
        # Verify that the cache is enabled
        self.assertTrue(self.cache.enabled)
        self.assertTrue(self.cache.is_enabled())
        
        # Set a cache entry
        self.cache.set("tool1", {"param1": "value1"}, "result1")
        
        # Verify that the entry was cached
        self.assertEqual(len(self.cache.cache), 1)
    
    def test_non_cacheable_tools(self):
        """Test non-cacheable tools."""
        # Set a cache entry for a non-cacheable tool
        self.cache.set("load_track_to_timeline", {"param1": "value1"}, "result1")
        
        # Verify that the entry was not cached
        self.assertEqual(len(self.cache.cache), 0)
        
        # Get the cache entry
        result = self.cache.get("load_track_to_timeline", {"param1": "value1"})
        
        # Verify the result
        self.assertIsNone(result)


if __name__ == '__main__':
    unittest.main()
