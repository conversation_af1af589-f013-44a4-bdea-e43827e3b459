"""
End-to-end tests for the MCP components.
"""

import unittest
import async<PERSON>
import json
from unittest.mock import patch, MagicMock, AsyncMock
import pytest
import requests
from fastapi.testclient import TestClient

from backend.main import app
from backend.services.ai.mcp_server import DJMixConstructorMCPServer
from backend.services.ai.mcp_client import DJMixConstructorMCPClient
from backend.services.ai.mcp_cache import mcp_cache
from backend.services.ai.mcp_error_handler import MCPErrorHandler
from backend.services.ai.mcp_async_executor import mcp_async_executor


@pytest.mark.e2e
class TestMCPEndToEnd:
    """End-to-end tests for the MCP components."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    def test_mcp_status_endpoint(self, client):
        """Test the MCP status endpoint."""
        # Mock the MCP client
        with patch('backend.routes.ai_mcp.DJMixConstructorMCPClient') as mock_client_class:
            # Configure the mock
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            mock_client.is_connected = True
            mock_client.get_tools_for_function_calling.return_value = [
                {
                    "name": "test_tool",
                    "description": "A test tool",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "param": {
                                "type": "string",
                                "description": "A test parameter"
                            }
                        },
                        "required": ["param"]
                    }
                }
            ]
            
            # Make the request
            response = client.get("/api/v1/ai/mcp/status")
            
            # Verify the response
            assert response.status_code == 200
            data = response.json()
            assert data["is_enabled"] is True
            assert "available_tools" in data
            assert len(data["available_tools"]) == 1
            assert data["available_tools"][0]["name"] == "test_tool"
    
    def test_mcp_async_execute_endpoint(self, client):
        """Test the MCP async execute endpoint."""
        # Mock the MCP client and async executor
        with patch('backend.routes.ai_mcp_async.DJMixConstructorMCPClient') as mock_client_class, \
             patch('backend.routes.ai_mcp_async.mcp_async_executor') as mock_executor, \
             patch('backend.routes.ai_mcp_async.settings') as mock_settings:
            # Configure the mocks
            mock_settings.MCP_ASYNC_ENABLED = True
            mock_settings.MCP_ENABLED = True
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            mock_executor.submit_task.return_value = "task-id"
            
            # Make the request
            response = client.post(
                "/api/v1/ai/mcp/async/execute",
                json={
                    "tool_name": "test_tool",
                    "parameters": {"param": "value"}
                }
            )
            
            # Verify the response
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "task-id"
    
    def test_mcp_async_status_endpoint(self, client):
        """Test the MCP async status endpoint."""
        # Mock the async executor
        with patch('backend.routes.ai_mcp_async.mcp_async_executor') as mock_executor, \
             patch('backend.routes.ai_mcp_async.settings') as mock_settings:
            # Configure the mocks
            mock_settings.MCP_ASYNC_ENABLED = True
            mock_executor.get_task_status.return_value = {
                "task_id": "task-id",
                "tool_name": "test_tool",
                "status": "running",
                "progress": 0.5,
                "progress_message": "Halfway there",
                "created_at": 1234567890,
                "started_at": 1234567891,
                "completed_at": None,
                "result": None,
                "error": None
            }
            
            # Make the request
            response = client.get("/api/v1/ai/mcp/async/status/task-id")
            
            # Verify the response
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "task-id"
            assert data["status"] == "running"
            assert data["progress"] == 0.5
            assert data["progress_message"] == "Halfway there"
    
    def test_mcp_optimization_recommendations_endpoint(self, client):
        """Test the MCP optimization recommendations endpoint."""
        # Mock the optimization service
        with patch('backend.routes.ai_mcp_optimization.mcp_optimization_service') as mock_service:
            # Configure the mock
            mock_service.generate_optimization_recommendations.return_value = [
                {
                    "id": "rec-1",
                    "feature_id": "mcp",
                    "parameter_name": "MCP_CACHE_MAX_SIZE",
                    "current_value": 100,
                    "recommended_value": 200,
                    "impact_score": 75,
                    "description": "Increase cache size",
                    "reason": "Cache is almost full"
                }
            ]
            
            # Make the request
            response = client.get("/api/v1/ai/mcp/optimization/recommendations")
            
            # Verify the response
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["id"] == "rec-1"
            assert data[0]["parameter_name"] == "MCP_CACHE_MAX_SIZE"
            assert data[0]["impact_score"] == 75
    
    def test_mcp_optimization_apply_endpoint(self, client):
        """Test the MCP optimization apply endpoint."""
        # Mock the optimization service
        with patch('backend.routes.ai_mcp_optimization.mcp_optimization_service') as mock_service:
            # Configure the mock
            mock_service.apply_optimization.return_value = {
                "success": True,
                "message": "Applied recommendation rec-1",
                "parameter": "MCP_CACHE_MAX_SIZE",
                "old_value": 100,
                "new_value": 200
            }
            
            # Make the request
            response = client.post("/api/v1/ai/mcp/optimization/apply/rec-1")
            
            # Verify the response
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["parameter"] == "MCP_CACHE_MAX_SIZE"
            assert data["old_value"] == 100
            assert data["new_value"] == 200


@pytest.mark.e2e
@pytest.mark.asyncio
class TestMCPWorkflows:
    """End-to-end tests for MCP workflows."""
    
    async def test_complete_workflow(self):
        """Test a complete MCP workflow."""
        # Mock the MCP client and server
        with patch('backend.services.ai.mcp_client.mcp.Client') as mock_client_class, \
             patch('backend.services.ai.mcp_cache.settings') as mock_settings:
            # Configure the mocks
            mock_settings.MCP_CACHE_ENABLED = True
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.connect = AsyncMock()
            mock_client.get_tools = AsyncMock(return_value=[])
            mock_client.call_tool = AsyncMock(return_value={"result": "success"})
            
            # Create a client
            client = DJMixConstructorMCPClient()
            
            # Connect to the server
            connected = await client.connect()
            assert connected is True
            
            # Call a tool
            result1 = await client.call_tool("test_tool", {"param": "value"})
            assert result1["result"] == "success"
            
            # Call the same tool again (should use cache)
            result2 = await client.call_tool("test_tool", {"param": "value"})
            assert result2["result"] == "success"
            
            # Call a tool asynchronously
            task_id = client.call_tool_async("test_tool", {"param": "value"})
            assert task_id is not None
            
            # Get the task status
            status = mcp_async_executor.get_task_status(task_id)
            assert status is not None
            assert status["task_id"] == task_id
            
            # Disconnect from the server
            await client.disconnect()
    
    async def test_error_handling_workflow(self):
        """Test error handling in a workflow."""
        # Mock the MCP client
        with patch('backend.services.ai.mcp_client.mcp.Client') as mock_client_class:
            # Configure the mock
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.connect = AsyncMock()
            mock_client.get_tools = AsyncMock(return_value=[])
            mock_client.call_tool = AsyncMock(side_effect=[
                Exception("First error"),  # First call fails
                {"result": "success"}      # Second call succeeds
            ])
            
            # Create a client
            client = DJMixConstructorMCPClient()
            
            # Connect to the server
            connected = await client.connect()
            assert connected is True
            
            # Call a tool (should fail)
            result1 = await client.call_tool("test_tool", {"param": "value"})
            assert "error" in result1
            assert "First error" in result1["error"]
            
            # Call the tool again (should succeed)
            result2 = await client.call_tool("test_tool", {"param": "value"})
            assert result2["result"] == "success"
            
            # Disconnect from the server
            await client.disconnect()


if __name__ == '__main__':
    unittest.main()
