"""
Unit tests for the MCP async executor component.
"""

import unittest
import asyncio
import time
from unittest.mock import patch, MagicMock, AsyncMock
import pytest
import threading

from backend.services.ai.mcp_async_executor import (
    MCPAsyncExecutor, AsyncTask, TaskStatus
)


class TestAsyncTask(unittest.TestCase):
    """Test cases for the AsyncTask class."""

    def test_initialization(self):
        """Test task initialization."""
        task = AsyncTask("task-1", "test_tool", {"param": "value"})
        
        self.assertEqual(task.task_id, "task-1")
        self.assertEqual(task.tool_name, "test_tool")
        self.assertEqual(task.parameters, {"param": "value"})
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertIsNone(task.result)
        self.assertIsNone(task.error)
        self.assertIsNotNone(task.created_at)
        self.assertIsNone(task.started_at)
        self.assertIsNone(task.completed_at)
        self.assertEqual(task.progress, 0.0)
        self.assertEqual(task.progress_message, "Waiting to start")
        self.assertIsNone(task.future)


class TestMCPAsyncExecutor(unittest.TestCase):
    """Test cases for the MCPAsyncExecutor class."""

    def setUp(self):
        """Set up test fixtures."""
        self.executor = MCPAsyncExecutor(max_workers=2)
        self.executor.mcp_client = MagicMock()
        self.executor.mcp_client.call_tool = AsyncMock(return_value={"result": "success"})
    
    def test_initialization(self):
        """Test executor initialization."""
        self.assertIsNotNone(self.executor)
        self.assertEqual(len(self.executor.tasks), 0)
        self.assertIsNotNone(self.executor.executor)
        self.assertIsNotNone(self.executor.lock)
        self.assertIsNone(self.executor.mcp_client)
    
    def test_set_mcp_client(self):
        """Test setting the MCP client."""
        client = MagicMock()
        self.executor.set_mcp_client(client)
        
        self.assertEqual(self.executor.mcp_client, client)
    
    def test_submit_task_no_client(self):
        """Test submitting a task with no client."""
        self.executor.mcp_client = None
        
        with self.assertRaises(ValueError):
            self.executor.submit_task("test_tool", {"param": "value"})
    
    def test_submit_task(self):
        """Test submitting a task."""
        # Mock the executor's submit method
        self.executor.executor.submit = MagicMock()
        
        # Submit a task
        task_id = self.executor.submit_task("test_tool", {"param": "value"})
        
        # Verify the task was created
        self.assertIsNotNone(task_id)
        self.assertIn(task_id, self.executor.tasks)
        
        # Verify the task properties
        task = self.executor.tasks[task_id]
        self.assertEqual(task.tool_name, "test_tool")
        self.assertEqual(task.parameters, {"param": "value"})
        self.assertEqual(task.status, TaskStatus.PENDING)
        
        # Verify the executor's submit method was called
        self.executor.executor.submit.assert_called_once()
    
    def test_get_task_status_not_found(self):
        """Test getting status of a nonexistent task."""
        status = self.executor.get_task_status("nonexistent")
        
        self.assertIsNone(status)
    
    def test_get_task_status(self):
        """Test getting task status."""
        # Create a task
        task = AsyncTask("task-1", "test_tool", {"param": "value"})
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()
        task.progress = 0.5
        task.progress_message = "Halfway there"
        
        # Add the task to the executor
        with self.executor.lock:
            self.executor.tasks["task-1"] = task
        
        # Get the task status
        status = self.executor.get_task_status("task-1")
        
        # Verify the status
        self.assertIsNotNone(status)
        self.assertEqual(status["task_id"], "task-1")
        self.assertEqual(status["tool_name"], "test_tool")
        self.assertEqual(status["status"], TaskStatus.RUNNING)
        self.assertEqual(status["progress"], 0.5)
        self.assertEqual(status["progress_message"], "Halfway there")
    
    def test_get_task_result_not_found(self):
        """Test getting result of a nonexistent task."""
        result = self.executor.get_task_result("nonexistent")
        
        self.assertIsNone(result)
    
    def test_get_task_result_not_completed(self):
        """Test getting result of a non-completed task."""
        # Create a task
        task = AsyncTask("task-1", "test_tool", {"param": "value"})
        task.status = TaskStatus.RUNNING
        
        # Add the task to the executor
        with self.executor.lock:
            self.executor.tasks["task-1"] = task
        
        # Get the task result
        result = self.executor.get_task_result("task-1")
        
        # Verify the result
        self.assertIsNone(result)
    
    def test_get_task_result(self):
        """Test getting task result."""
        # Create a task
        task = AsyncTask("task-1", "test_tool", {"param": "value"})
        task.status = TaskStatus.COMPLETED
        task.result = {"result": "success"}
        
        # Add the task to the executor
        with self.executor.lock:
            self.executor.tasks["task-1"] = task
        
        # Get the task result
        result = self.executor.get_task_result("task-1")
        
        # Verify the result
        self.assertEqual(result, {"result": "success"})
    
    def test_cancel_task_not_found(self):
        """Test cancelling a nonexistent task."""
        cancelled = self.executor.cancel_task("nonexistent")
        
        self.assertFalse(cancelled)
    
    def test_cancel_task_already_completed(self):
        """Test cancelling an already completed task."""
        # Create a task
        task = AsyncTask("task-1", "test_tool", {"param": "value"})
        task.status = TaskStatus.COMPLETED
        
        # Add the task to the executor
        with self.executor.lock:
            self.executor.tasks["task-1"] = task
        
        # Cancel the task
        cancelled = self.executor.cancel_task("task-1")
        
        # Verify the result
        self.assertFalse(cancelled)
        self.assertEqual(task.status, TaskStatus.COMPLETED)
    
    def test_cancel_task(self):
        """Test cancelling a task."""
        # Create a task
        task = AsyncTask("task-1", "test_tool", {"param": "value"})
        task.status = TaskStatus.RUNNING
        task.future = MagicMock()
        task.future.done.return_value = False
        
        # Add the task to the executor
        with self.executor.lock:
            self.executor.tasks["task-1"] = task
        
        # Cancel the task
        cancelled = self.executor.cancel_task("task-1")
        
        # Verify the result
        self.assertTrue(cancelled)
        self.assertEqual(task.status, TaskStatus.CANCELLED)
        task.future.cancel.assert_called_once()
    
    def test_cleanup_old_tasks(self):
        """Test cleaning up old tasks."""
        # Create some tasks
        task1 = AsyncTask("task-1", "test_tool", {"param": "value"})
        task1.status = TaskStatus.COMPLETED
        task1.completed_at = time.time() - 3600  # 1 hour ago
        
        task2 = AsyncTask("task-2", "test_tool", {"param": "value"})
        task2.status = TaskStatus.COMPLETED
        task2.completed_at = time.time() - 1800  # 30 minutes ago
        
        task3 = AsyncTask("task-3", "test_tool", {"param": "value"})
        task3.status = TaskStatus.RUNNING
        
        # Add the tasks to the executor
        with self.executor.lock:
            self.executor.tasks["task-1"] = task1
            self.executor.tasks["task-2"] = task2
            self.executor.tasks["task-3"] = task3
        
        # Clean up old tasks
        removed = self.executor.cleanup_old_tasks(max_age_seconds=3000)  # 50 minutes
        
        # Verify the result
        self.assertEqual(removed, 1)
        self.assertNotIn("task-1", self.executor.tasks)
        self.assertIn("task-2", self.executor.tasks)
        self.assertIn("task-3", self.executor.tasks)


if __name__ == '__main__':
    unittest.main()
