"""
Integration tests for the MCP audio analysis server with the MCP registry.
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock, ANY
import json
import numpy as np
import asyncio

# Import the modules to test
from backend.services.ai.mcp_audio_analysis import MCPAudioAnalysisServer
from backend.services.ai.mcp_audio_analysis_registry import register_audio_analysis_server
from backend.services.ai.mcp_registry import MCPRegistry


class TestMCPAudioAnalysisIntegration(unittest.TestCase):
    """Integration tests for the MCP audio analysis server with the MCP registry."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a test audio file (just a dummy file for testing)
        self.test_audio_path = os.path.join(self.temp_dir.name, "test_audio.mp3")
        with open(self.test_audio_path, "wb") as f:
            f.write(b"dummy audio data")
        
        # Create a mock database session
        self.mock_db = MagicMock()
        
        # Create a mock MCP registry
        self.mock_registry = MagicMock(spec=MCPRegistry)
        
        # Patch the global mcp_registry instance
        self.registry_patcher = patch("backend.services.ai.mcp_audio_analysis_registry.mcp_registry", self.mock_registry)
        self.registry_patcher.start()

    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        self.temp_dir.cleanup()
        
        # Stop the registry patcher
        self.registry_patcher.stop()

    def test_register_audio_analysis_server(self):
        """Test registering the audio analysis server with the registry."""
        # Set up mock for add_server
        self.mock_registry.get_server.return_value = None
        self.mock_registry.add_server.return_value = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "status": "registered"
        }
        
        # Call the function
        result = register_audio_analysis_server(self.mock_db)
        
        # Check the result
        self.assertEqual(result["server_id"], "audio-analysis")
        self.assertEqual(result["name"], "Audio Analysis")
        
        # Check that the mocks were called
        self.mock_registry.get_server.assert_called_once_with("audio-analysis")
        self.mock_registry.add_server.assert_called_once()
        
        # Check the server configuration
        server_config = self.mock_registry.add_server.call_args[0][0]
        self.assertEqual(server_config["server_id"], "audio-analysis")
        self.assertEqual(server_config["name"], "Audio Analysis")
        self.assertEqual(server_config["transport"], "stdio")
        self.assertTrue(server_config["enabled"])
        self.assertIn("analyze_", server_config["tool_prefixes"])
        self.assertIn("extract_", server_config["tool_prefixes"])
        self.assertIn("detect_", server_config["tool_prefixes"])
        self.assertIn("download_", server_config["tool_prefixes"])

    def test_update_existing_server(self):
        """Test updating an existing audio analysis server in the registry."""
        # Set up mock for update_server
        self.mock_registry.get_server.return_value = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "status": "registered"
        }
        self.mock_registry.update_server.return_value = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "status": "updated"
        }
        
        # Call the function
        result = register_audio_analysis_server(self.mock_db)
        
        # Check the result
        self.assertEqual(result["server_id"], "audio-analysis")
        self.assertEqual(result["name"], "Audio Analysis")
        self.assertEqual(result["status"], "updated")
        
        # Check that the mocks were called
        self.mock_registry.get_server.assert_called_once_with("audio-analysis")
        self.mock_registry.update_server.assert_called_once()
        
        # Check the server configuration
        server_id = self.mock_registry.update_server.call_args[0][0]
        server_config = self.mock_registry.update_server.call_args[0][1]
        self.assertEqual(server_id, "audio-analysis")
        self.assertEqual(server_config["server_id"], "audio-analysis")
        self.assertEqual(server_config["name"], "Audio Analysis")

    @patch("backend.services.ai.mcp_registry.MCPRegistry.call_tool")
    def test_call_analyze_track_tool(self, mock_call_tool):
        """Test calling the analyze_track tool through the registry."""
        # Set up mock for call_tool
        mock_call_tool.return_value = {
            "tempo": 120.0,
            "key": "C",
            "energy": 0.75,
            "duration": 180.0
        }
        
        # Create a server instance
        server = MCPAudioAnalysisServer(self.mock_db)
        
        # Register the server with the registry
        self.mock_registry.get_server.return_value = None
        self.mock_registry.add_server.return_value = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "status": "registered"
        }
        
        register_audio_analysis_server(self.mock_db)
        
        # Call the tool through the registry
        result = self.mock_registry.call_tool("analyze_track", {"file_path": self.test_audio_path})
        
        # Check the result
        self.assertEqual(result["tempo"], 120.0)
        self.assertEqual(result["key"], "C")
        self.assertEqual(result["energy"], 0.75)
        self.assertEqual(result["duration"], 180.0)
        
        # Check that the mock was called
        mock_call_tool.assert_called_once_with("analyze_track", {"file_path": self.test_audio_path})

    @patch("backend.services.ai.mcp_registry.MCPRegistry.call_tool")
    def test_call_extract_beat_grid_tool(self, mock_call_tool):
        """Test calling the extract_beat_grid tool through the registry."""
        # Set up mock for call_tool
        mock_call_tool.return_value = {
            "tempo": 120.0,
            "beat_times": [1.0, 2.0, 3.0],
            "confidence": 0.85
        }
        
        # Create a server instance
        server = MCPAudioAnalysisServer(self.mock_db)
        
        # Register the server with the registry
        self.mock_registry.get_server.return_value = None
        self.mock_registry.add_server.return_value = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "status": "registered"
        }
        
        register_audio_analysis_server(self.mock_db)
        
        # Call the tool through the registry
        result = self.mock_registry.call_tool("extract_beat_grid", {
            "file_path": self.test_audio_path,
            "enhanced": True
        })
        
        # Check the result
        self.assertEqual(result["tempo"], 120.0)
        self.assertEqual(result["beat_times"], [1.0, 2.0, 3.0])
        self.assertEqual(result["confidence"], 0.85)
        
        # Check that the mock was called
        mock_call_tool.assert_called_once_with("extract_beat_grid", {
            "file_path": self.test_audio_path,
            "enhanced": True
        })

    @patch("backend.services.ai.mcp_registry.MCPRegistry.call_tool")
    def test_call_detect_segments_tool(self, mock_call_tool):
        """Test calling the detect_segments tool through the registry."""
        # Set up mock for call_tool
        mock_call_tool.return_value = [
            {
                "type": "intro",
                "start_time": 0.0,
                "end_time": 15.0,
                "confidence": 0.8,
                "label": "Intro"
            },
            {
                "type": "verse",
                "start_time": 15.0,
                "end_time": 45.0,
                "confidence": 0.9,
                "label": "Verse"
            }
        ]
        
        # Create a server instance
        server = MCPAudioAnalysisServer(self.mock_db)
        
        # Register the server with the registry
        self.mock_registry.get_server.return_value = None
        self.mock_registry.add_server.return_value = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "status": "registered"
        }
        
        register_audio_analysis_server(self.mock_db)
        
        # Call the tool through the registry
        result = self.mock_registry.call_tool("detect_segments", {"file_path": self.test_audio_path})
        
        # Check the result
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["type"], "intro")
        self.assertEqual(result[1]["type"], "verse")
        
        # Check that the mock was called
        mock_call_tool.assert_called_once_with("detect_segments", {"file_path": self.test_audio_path})


if __name__ == "__main__":
    unittest.main()
