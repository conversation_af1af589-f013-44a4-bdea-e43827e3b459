"""
Integration tests for the MCP components.
"""

import unittest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
import pytest

from backend.services.ai.mcp_server import DJMixConstructorMCPServer
from backend.services.ai.mcp_client import DJMixConstructorMC<PERSON>lient
from backend.services.ai.mcp_cache import mcp_cache
from backend.services.ai.mcp_error_handler import <PERSON><PERSON><PERSON>rrorHand<PERSON>, MCPError
from backend.services.ai.mcp_async_executor import mcp_async_executor


@pytest.mark.integration
@pytest.mark.asyncio
class TestMCPIntegration:
    """Integration tests for the MCP components."""
    
    @pytest.fixture
    async def server(self):
        """Create a server instance for testing."""
        # Create a server instance
        server = DJMixConstructorMCPServer()
        
        # Mock the server's run method
        server.server.run = AsyncMock()
        
        yield server
    
    @pytest.fixture
    async def client(self):
        """Create a client instance for testing."""
        # Create a client instance
        client = DJMixConstructorMCPClient()
        
        # Mock the client's connect method
        client.client = AsyncMock()
        client.client.connect = AsyncMock()
        client.client.get_tools = AsyncMock(return_value=[])
        client.client.call_tool = AsyncMock(return_value={"result": "success"})
        client.is_connected = True
        
        yield client
    
    async def test_client_server_integration(self, server, client):
        """Test integration between client and server."""
        # Mock the server's add_tool method to add a test tool
        test_tool = MagicMock()
        test_tool.name = "test_tool"
        test_tool.description = "A test tool"
        test_tool.parameters = []
        server.server.tools = [test_tool]
        
        # Mock the client's get_tools method to return the test tool
        client.client.get_tools = AsyncMock(return_value=[test_tool])
        
        # Call the client's get_tools method
        tools = await client.get_tools()
        
        # Verify the result
        assert len(tools) == 1
        assert tools[0]["name"] == "test_tool"
        assert tools[0]["description"] == "A test tool"
    
    async def test_client_cache_integration(self, client):
        """Test integration between client and cache."""
        # Mock the cache
        with patch('backend.services.ai.mcp_client.mcp_cache') as mock_cache, \
             patch('backend.services.ai.mcp_client.settings') as mock_settings:
            # Configure the mocks
            mock_settings.MCP_CACHE_ENABLED = True
            mock_cache.get.return_value = {"cached": "result"}
            
            # Call the client's call_tool method
            result = await client.call_tool("test_tool", {"param": "value"})
            
            # Verify the result
            assert result == {"cached": "result"}
            mock_cache.get.assert_called_once_with("test_tool", {"param": "value"})
    
    async def test_client_error_handler_integration(self, client):
        """Test integration between client and error handler."""
        # Mock the client's _execute_tool_call method to raise an error
        client._execute_tool_call = AsyncMock(side_effect=Exception("Test error"))
        
        # Call the client's call_tool method
        result = await client.call_tool("test_tool", {"param": "value"})
        
        # Verify the result
        assert "error" in result
        assert "Test error" in result["error"]
        assert "tool_name" in result
        assert result["tool_name"] == "test_tool"
    
    async def test_client_async_executor_integration(self, client):
        """Test integration between client and async executor."""
        # Mock the async executor
        with patch('backend.services.ai.mcp_client.mcp_async_executor') as mock_executor, \
             patch('backend.services.ai.mcp_client.settings') as mock_settings:
            # Configure the mocks
            mock_settings.MCP_ASYNC_ENABLED = True
            mock_executor.submit_task.return_value = "task-id"
            
            # Call the client's handle_function_call method with async_execution=True
            result = await client.handle_function_call({
                "name": "test_tool",
                "arguments": {"param": "value"}
            }, async_execution=True)
            
            # Verify the result
            assert result["task_id"] == "task-id"
            assert result["status"] == "pending"
            assert result["async"] is True
            assert result["tool_name"] == "test_tool"
            mock_executor.submit_task.assert_called_once_with("test_tool", {"param": "value"})


@pytest.mark.integration
class TestMCPComponentsIntegration:
    """Integration tests for the MCP components."""
    
    def test_cache_integration(self):
        """Test integration with the cache."""
        # Clear the cache
        mcp_cache.clear()
        
        # Set a cache entry
        mcp_cache.set("test_tool", {"param": "value"}, "result1")
        
        # Get the cache entry
        result = mcp_cache.get("test_tool", {"param": "value"})
        
        # Verify the result
        assert result == "result1"
        
        # Get the cache stats
        stats = mcp_cache.get_stats()
        
        # Verify the stats
        assert stats["hit_count"] == 1
        assert stats["miss_count"] == 0
        assert stats["hit_rate"] == 100.0
    
    def test_error_handler_integration(self):
        """Test integration with the error handler."""
        # Create an error
        error = MCPError(
            message="Test error",
            error_type="test_error",
            severity="high",
            category="transient"
        )
        
        # Format the error as a response
        response = MCPErrorHandler.format_error_response(error)
        
        # Verify the response
        assert response["error"] == "Test error"
        assert response["error_type"] == "test_error"
        assert response["severity"] == "high"
        assert response["category"] == "transient"
    
    def test_async_executor_integration(self):
        """Test integration with the async executor."""
        # Set the MCP client
        mcp_async_executor.mcp_client = MagicMock()
        mcp_async_executor.mcp_client.call_tool = AsyncMock(return_value={"result": "success"})
        
        # Submit a task
        task_id = mcp_async_executor.submit_task("test_tool", {"param": "value"})
        
        # Verify the task was created
        assert task_id in mcp_async_executor.tasks
        
        # Get the task status
        status = mcp_async_executor.get_task_status(task_id)
        
        # Verify the status
        assert status["task_id"] == task_id
        assert status["tool_name"] == "test_tool"
        assert status["status"] in ["pending", "running"]


if __name__ == '__main__':
    unittest.main()
