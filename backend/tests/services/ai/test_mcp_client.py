"""
Unit tests for the MCP client component.
"""

import unittest
import async<PERSON>
import json
from unittest.mock import patch, MagicMock, AsyncMock
import pytest

from backend.services.ai.mcp_client import DJMixConstructorMCPClient
from backend.services.ai.mcp_error_handler import MCPError


class TestMCPClient(unittest.TestCase):
    """Test cases for the MCP client component."""

    def setUp(self):
        """Set up test fixtures."""
        self.client = DJMixConstructorMCPClient()
    
    def test_initialization(self):
        """Test client initialization."""
        self.assertIsNotNone(self.client)
        self.assertIsNone(self.client.client)
        self.assertIsNone(self.client.server_process)
        self.assertIsNone(self.client.tools_cache)
        self.assertFalse(self.client.is_connected)
    
    def test_get_tools_for_function_calling(self):
        """Test conversion of tools to function calling format."""
        # Set up mock tools cache
        self.client.tools_cache = [
            {
                "name": "test_tool",
                "description": "A test tool",
                "parameters": [
                    {
                        "name": "param1",
                        "description": "A test parameter",
                        "type": "string",
                        "required": True
                    },
                    {
                        "name": "param2",
                        "description": "Another test parameter",
                        "type": "integer",
                        "required": False
                    }
                ]
            }
        ]
        
        # Call the method
        result = self.client.get_tools_for_function_calling()
        
        # Verify the result
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["name"], "test_tool")
        self.assertEqual(result[0]["description"], "A test tool")
        self.assertEqual(len(result[0]["parameters"]["properties"]), 2)
        self.assertEqual(len(result[0]["parameters"]["required"]), 1)
        self.assertEqual(result[0]["parameters"]["required"][0], "param1")


@pytest.mark.asyncio
class TestMCPClientAsync:
    """Async test cases for the MCP client component."""
    
    @pytest.fixture
    async def client(self):
        """Create a client instance for testing."""
        client = DJMixConstructorMCPClient()
        yield client
    
    async def test_connect(self, client):
        """Test client connection."""
        # Mock the MCP client
        with patch('mcp.Client') as mock_client_class:
            # Configure the mock
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.connect = AsyncMock()
            mock_client.get_tools = AsyncMock(return_value=[])
            
            # Call the connect method
            result = await client.connect()
            
            # Verify the result
            assert result is True
            assert client.is_connected is True
            assert client.client is mock_client
            mock_client.connect.assert_called_once()
    
    async def test_connect_failure(self, client):
        """Test client connection failure."""
        # Mock the MCP client
        with patch('mcp.Client') as mock_client_class:
            # Configure the mock to raise an exception
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.connect = AsyncMock(side_effect=Exception("Connection failed"))
            
            # Call the connect method
            result = await client.connect()
            
            # Verify the result
            assert result is False
            assert client.is_connected is False
    
    async def test_disconnect(self, client):
        """Test client disconnection."""
        # Set up the client
        client.client = AsyncMock()
        client.client.disconnect = AsyncMock()
        client.server_process = MagicMock()
        client.is_connected = True
        
        # Call the disconnect method
        await client.disconnect()
        
        # Verify the result
        assert client.is_connected is False
        assert client.client is None
        client.client.disconnect.assert_called_once()
        client.server_process.terminate.assert_called_once()
    
    async def test_get_tools(self, client):
        """Test getting tools from the server."""
        # Set up the client
        client.client = AsyncMock()
        mock_tool = MagicMock()
        mock_tool.name = "test_tool"
        mock_tool.description = "A test tool"
        mock_tool.parameters = []
        client.client.get_tools = AsyncMock(return_value=[mock_tool])
        client.is_connected = True
        
        # Call the get_tools method
        result = await client.get_tools()
        
        # Verify the result
        assert len(result) == 1
        assert result[0]["name"] == "test_tool"
        assert result[0]["description"] == "A test tool"
        client.client.get_tools.assert_called_once()
    
    async def test_call_tool(self, client):
        """Test calling a tool."""
        # Set up the client
        client.client = AsyncMock()
        client.client.call_tool = AsyncMock(return_value={"result": "success"})
        client.is_connected = True
        
        # Call the call_tool method
        result = await client.call_tool("test_tool", {"param": "value"})
        
        # Verify the result
        assert "_execution_time_ms" in result
        assert result["result"] == "success"
        client.client.call_tool.assert_called_once_with("test_tool", {"param": "value"})
    
    async def test_call_tool_with_cache(self, client):
        """Test calling a tool with cache."""
        # Mock the cache
        with patch('backend.services.ai.mcp_client.mcp_cache') as mock_cache, \
             patch('backend.services.ai.mcp_client.settings') as mock_settings:
            # Configure the mocks
            mock_settings.MCP_CACHE_ENABLED = True
            mock_cache.get.return_value = {"cached": "result"}
            
            # Call the call_tool method
            result = await client.call_tool("test_tool", {"param": "value"})
            
            # Verify the result
            assert result == {"cached": "result"}
            mock_cache.get.assert_called_once_with("test_tool", {"param": "value"})
    
    async def test_call_tool_error(self, client):
        """Test error handling when calling a tool."""
        # Set up the client
        client.client = AsyncMock()
        client.client.call_tool = AsyncMock(side_effect=Exception("Tool call failed"))
        client.is_connected = True
        
        # Call the call_tool method
        result = await client.call_tool("test_tool", {"param": "value"})
        
        # Verify the result
        assert "error" in result
        assert "Tool call failed" in result["error"]
        assert "tool_name" in result
        assert result["tool_name"] == "test_tool"
    
    async def test_handle_function_call(self, client):
        """Test handling a function call."""
        # Mock the call_tool method
        client.call_tool = AsyncMock(return_value={"result": "success"})
        
        # Call the handle_function_call method
        result = await client.handle_function_call({
            "name": "test_tool",
            "arguments": json.dumps({"param": "value"})
        })
        
        # Verify the result
        assert result == {"result": "success"}
        client.call_tool.assert_called_once_with("test_tool", {"param": "value"}, use_cache=True)
    
    async def test_handle_function_call_async(self, client):
        """Test handling a function call asynchronously."""
        # Mock the call_tool_async method and settings
        with patch('backend.services.ai.mcp_client.settings') as mock_settings:
            mock_settings.MCP_ASYNC_ENABLED = True
            client.call_tool_async = MagicMock(return_value="task-id")
            
            # Call the handle_function_call method
            result = await client.handle_function_call({
                "name": "test_tool",
                "arguments": json.dumps({"param": "value"})
            }, async_execution=True)
            
            # Verify the result
            assert result["task_id"] == "task-id"
            assert result["status"] == "pending"
            assert result["async"] is True
            assert result["tool_name"] == "test_tool"
            client.call_tool_async.assert_called_once_with("test_tool", {"param": "value"})


if __name__ == '__main__':
    unittest.main()
