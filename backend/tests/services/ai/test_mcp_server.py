"""
Unit tests for the MCP server component.
"""

import unittest
import asyncio
import json
from unittest.mock import patch, MagicMock, AsyncMock
import pytest

from backend.services.ai.mcp_server import DJMixConstructorMCPServer


class TestMCPServer(unittest.TestCase):
    """Test cases for the MCP server component."""

    def setUp(self):
        """Set up test fixtures."""
        self.server = DJMixConstructorMCPServer()
    
    def test_initialization(self):
        """Test server initialization."""
        self.assertIsNotNone(self.server)
        self.assertIsNotNone(self.server.server)
    
    @patch('fastmcp.FastMCP.add_tool')
    def test_register_tools(self, mock_add_tool):
        """Test tool registration."""
        # Call the private method to register tools
        self.server._register_tools()
        
        # Verify that add_tool was called multiple times
        self.assertTrue(mock_add_tool.called)
        
        # We expect multiple tools to be registered
        self.assertGreater(mock_add_tool.call_count, 0)
    
    @patch('fastmcp.FastMCP.run')
    async def test_start(self, mock_run):
        """Test server start method."""
        # Configure the mock
        mock_run.return_value = None
        
        # Call the start method
        await self.server.start(transport='stdio')
        
        # Verify that run was called with the correct transport
        mock_run.assert_called_once_with(transport='stdio')
    
    @patch('asyncio.new_event_loop')
    @patch('asyncio.set_event_loop')
    def test_start_background(self, mock_set_event_loop, mock_new_event_loop):
        """Test background start method."""
        # Configure the mocks
        mock_loop = MagicMock()
        mock_new_event_loop.return_value = mock_loop
        
        # Call the start_background method
        self.server.start_background()
        
        # Verify that the event loop was created and set
        mock_new_event_loop.assert_called_once()
        mock_set_event_loop.assert_called_once_with(mock_loop)
        
        # Verify that run_until_complete was called
        self.assertTrue(mock_loop.run_until_complete.called)


@pytest.mark.asyncio
class TestMCPServerAsync:
    """Async test cases for the MCP server component."""
    
    @pytest.fixture
    async def server(self):
        """Create a server instance for testing."""
        server = DJMixConstructorMCPServer()
        yield server
    
    async def test_get_tracks_tool(self, server):
        """Test the get_tracks tool."""
        # Mock the database session and query
        with patch('backend.services.ai.mcp_server.get_db') as mock_get_db:
            # Configure the mock
            mock_db = MagicMock()
            mock_query = MagicMock()
            mock_db.query.return_value = mock_query
            mock_query.filter.return_value = mock_query
            mock_query.limit.return_value = mock_query
            mock_query.all.return_value = [
                MagicMock(id=1, title="Test Track", artist="Test Artist", 
                          album="Test Album", genre="Test Genre", bpm=120, key="C")
            ]
            mock_get_db.return_value.__next__.return_value = mock_db
            
            # Get the tool function
            get_tracks_tool = None
            for tool in server.server.tools:
                if tool.name == "get_tracks":
                    get_tracks_tool = tool.function
                    break
            
            # Verify that the tool was found
            assert get_tracks_tool is not None
            
            # Call the tool function
            result = await get_tracks_tool(collection_id="test", limit=10)
            
            # Verify the result
            assert "tracks" in result
            assert len(result["tracks"]) > 0
    
    async def test_search_tracks_tool(self, server):
        """Test the search_tracks tool."""
        # Mock the database session and query
        with patch('backend.services.ai.mcp_server.get_db') as mock_get_db:
            # Configure the mock
            mock_db = MagicMock()
            mock_query = MagicMock()
            mock_db.query.return_value = mock_query
            mock_query.filter.return_value = mock_query
            mock_query.limit.return_value = mock_query
            mock_query.all.return_value = [
                MagicMock(id=1, title="Test Track", artist="Test Artist", 
                          album="Test Album", genre="Test Genre", bpm=120, key="C")
            ]
            mock_get_db.return_value.__next__.return_value = mock_db
            
            # Get the tool function
            search_tracks_tool = None
            for tool in server.server.tools:
                if tool.name == "search_tracks":
                    search_tracks_tool = tool.function
                    break
            
            # Verify that the tool was found
            assert search_tracks_tool is not None
            
            # Call the tool function
            result = await search_tracks_tool(query="test", limit=10)
            
            # Verify the result
            assert "tracks" in result
            assert len(result["tracks"]) > 0
            assert "query" in result
            assert result["query"] == "test"


if __name__ == '__main__':
    unittest.main()
