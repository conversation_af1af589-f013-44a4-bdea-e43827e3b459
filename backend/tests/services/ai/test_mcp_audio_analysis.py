"""
Unit tests for the MCP audio analysis server.
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock, ANY
import json
import numpy as np

# Import the module to test
from backend.services.ai.mcp_audio_analysis import MCPAudioAnalysisServer


class TestMCPAudioAnalysisServer(unittest.TestCase):
    """Test cases for the MCP audio analysis server."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a test audio file (just a dummy file for testing)
        self.test_audio_path = os.path.join(self.temp_dir.name, "test_audio.mp3")
        with open(self.test_audio_path, "wb") as f:
            f.write(b"dummy audio data")
        
        # Create a mock database session
        self.mock_db = MagicMock()
        
        # Create the server instance with the mock database
        self.server = MCPAudioAnalysisServer(self.mock_db)

    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        self.temp_dir.cleanup()

    @patch("librosa.load")
    @patch("librosa.beat.beat_track")
    @patch("librosa.feature.chroma_cqt")
    @patch("librosa.feature.rms")
    @patch("librosa.get_duration")
    def test_analyze_with_librosa(self, mock_get_duration, mock_rms, mock_chroma_cqt, 
                                 mock_beat_track, mock_load):
        """Test the _analyze_with_librosa method."""
        # Set up mocks
        mock_load.return_value = (np.zeros(1000), 44100)
        mock_beat_track.return_value = (120.0, np.array([100, 200, 300]))
        mock_chroma_cqt.return_value = np.zeros((12, 100))
        mock_rms.return_value = [np.array([0.5])]
        mock_get_duration.return_value = 60.0
        
        # Call the method
        result = self.server._analyze_with_librosa(self.test_audio_path)
        
        # Check the result
        self.assertIsInstance(result, dict)
        self.assertIn("tempo", result)
        self.assertIn("key", result)
        self.assertIn("energy", result)
        self.assertIn("duration", result)
        
        # Check that the mocks were called
        mock_load.assert_called_once_with(self.test_audio_path, sr=None, duration=120)
        mock_beat_track.assert_called_once()
        mock_chroma_cqt.assert_called_once()
        mock_rms.assert_called_once()
        mock_get_duration.assert_called_once()

    @patch("librosa.load")
    @patch("librosa.beat.beat_track")
    @patch("librosa.frames_to_time")
    @patch("librosa.onset.onset_strength")
    def test_extract_beat_grid_with_librosa(self, mock_onset_strength, mock_frames_to_time, 
                                           mock_beat_track, mock_load):
        """Test the _extract_beat_grid_with_librosa method."""
        # Set up mocks
        mock_load.return_value = (np.zeros(1000), 44100)
        mock_beat_track.return_value = (120.0, np.array([100, 200, 300]))
        mock_frames_to_time.return_value = np.array([1.0, 2.0, 3.0])
        mock_onset_strength.return_value = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        
        # Call the method
        result = self.server._extract_beat_grid_with_librosa(self.test_audio_path)
        
        # Check the result
        self.assertIsInstance(result, dict)
        self.assertIn("tempo", result)
        self.assertIn("beat_times", result)
        self.assertIn("confidence", result)
        
        # Check that the mocks were called
        mock_load.assert_called_once_with(self.test_audio_path, sr=None)
        mock_beat_track.assert_called_once()
        mock_frames_to_time.assert_called_once()
        mock_onset_strength.assert_called_once()

    @patch("librosa.load")
    @patch("librosa.feature.mfcc")
    @patch("librosa.segment.agglomerative")
    @patch("librosa.frames_to_time")
    def test_detect_segments_with_librosa(self, mock_frames_to_time, mock_agglomerative, 
                                         mock_mfcc, mock_load):
        """Test the _detect_segments_with_librosa method."""
        # Set up mocks
        mock_load.return_value = (np.zeros(1000), 44100)
        mock_mfcc.return_value = np.zeros((13, 100))
        mock_agglomerative.return_value = np.array([0, 20, 40, 60, 80, 100])
        mock_frames_to_time.return_value = np.array([0.0, 10.0, 20.0, 30.0, 40.0, 50.0])
        
        # Call the method
        result = self.server._detect_segments_with_librosa(self.test_audio_path)
        
        # Check the result
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 5)  # 6 boundaries = 5 segments
        
        # Check segment structure
        for segment in result:
            self.assertIn("type", segment)
            self.assertIn("start_time", segment)
            self.assertIn("end_time", segment)
            self.assertIn("confidence", segment)
            self.assertIn("label", segment)
        
        # Check that the mocks were called
        mock_load.assert_called_once_with(self.test_audio_path, sr=None)
        mock_mfcc.assert_called_once()
        mock_agglomerative.assert_called_once()
        mock_frames_to_time.assert_called_once()

    @patch("librosa.load")
    @patch("librosa.feature.chroma_cqt")
    @patch("librosa.frames_to_time")
    @patch("builtins.open", new_callable=unittest.mock.mock_open)
    def test_extract_chroma_to_csv(self, mock_open, mock_frames_to_time, mock_chroma_cqt, mock_load):
        """Test the _extract_chroma_to_csv method."""
        # Set up mocks
        mock_load.return_value = (np.zeros(1000), 44100)
        mock_chroma_cqt.return_value = np.ones((12, 10)) * 0.5  # 12 notes, 10 time frames
        mock_frames_to_time.return_value = np.linspace(0, 10, 10)
        
        # Call the method
        result = self.server._extract_chroma_to_csv(self.test_audio_path)
        
        # Check the result
        self.assertIsInstance(result, str)
        
        # Check that the mocks were called
        mock_load.assert_called_once_with(self.test_audio_path, sr=None)
        mock_chroma_cqt.assert_called_once()
        mock_frames_to_time.assert_called_once()
        mock_open.assert_called_once()
        
        # Check that the CSV header was written
        mock_open().write.assert_any_call("note,time,amplitude\n")

    @patch("librosa.load")
    @patch("librosa.feature.mfcc")
    @patch("numpy.savetxt")
    def test_extract_mfcc_to_csv(self, mock_savetxt, mock_mfcc, mock_load):
        """Test the _extract_mfcc_to_csv method."""
        # Set up mocks
        mock_load.return_value = (np.zeros(1000), 44100)
        mock_mfcc.return_value = np.ones((13, 100)) * 0.5  # 13 coefficients, 100 time frames
        
        # Call the method
        result = self.server._extract_mfcc_to_csv(self.test_audio_path)
        
        # Check the result
        self.assertIsInstance(result, str)
        
        # Check that the mocks were called
        mock_load.assert_called_once_with(self.test_audio_path, sr=None)
        mock_mfcc.assert_called_once()
        mock_savetxt.assert_called_once()

    @patch("requests.get")
    def test_download_from_url_success(self, mock_get):
        """Test the download_from_url tool with a successful response."""
        # Set up mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"dummy audio data"
        mock_get.return_value = mock_response
        
        # Register the tool
        self.server._register_tools()
        
        # Find the download_from_url tool
        download_tool = None
        for tool in self.server.mcp.tools:
            if tool.name == "download_from_url":
                download_tool = tool
                break
        
        self.assertIsNotNone(download_tool, "download_from_url tool not found")
        
        # Call the tool
        with patch("builtins.open", unittest.mock.mock_open()) as mock_open:
            result = download_tool.function("http://example.com/audio.mp3")
        
        # Check the result
        self.assertIsInstance(result, str)
        
        # Check that the mocks were called
        mock_get.assert_called_once_with("http://example.com/audio.mp3")
        mock_open.assert_called_once()
        mock_open().write.assert_called_once_with(b"dummy audio data")

    @patch("requests.get")
    def test_download_from_url_failure(self, mock_get):
        """Test the download_from_url tool with a failed response."""
        # Set up mock
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        # Register the tool
        self.server._register_tools()
        
        # Find the download_from_url tool
        download_tool = None
        for tool in self.server.mcp.tools:
            if tool.name == "download_from_url":
                download_tool = tool
                break
        
        self.assertIsNotNone(download_tool, "download_from_url tool not found")
        
        # Call the tool and expect an exception
        with self.assertRaises(ValueError):
            download_tool.function("http://example.com/audio.mp3")
        
        # Check that the mock was called
        mock_get.assert_called_once_with("http://example.com/audio.mp3")

    @patch("pytubefix.YouTube")
    def test_download_from_youtube(self, mock_youtube):
        """Test the download_from_youtube tool."""
        # Set up mocks
        mock_stream = MagicMock()
        mock_stream.download.return_value = "/tmp/video_id.mp4"
        
        mock_yt = MagicMock()
        mock_yt.streams.get_audio_only.return_value = mock_stream
        mock_yt.video_id = "video_id"
        
        mock_youtube.return_value = mock_yt
        
        # Register the tool
        self.server._register_tools()
        
        # Find the download_from_youtube tool
        download_tool = None
        for tool in self.server.mcp.tools:
            if tool.name == "download_from_youtube":
                download_tool = tool
                break
        
        self.assertIsNotNone(download_tool, "download_from_youtube tool not found")
        
        # Call the tool
        result = download_tool.function("https://www.youtube.com/watch?v=video_id")
        
        # Check the result
        self.assertEqual(result, "/tmp/video_id.mp4")
        
        # Check that the mocks were called
        mock_youtube.assert_called_once_with("https://www.youtube.com/watch?v=video_id")
        mock_yt.streams.get_audio_only.assert_called_once()
        mock_stream.download.assert_called_once()


if __name__ == "__main__":
    unittest.main()
