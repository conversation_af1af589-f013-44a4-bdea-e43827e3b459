"""
Pytest configuration file.
"""

import pytest
import sys
import os
import asyncio
from unittest.mock import MagicMock, AsyncMock

# Add the parent directory to the path so we can import the backend modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure pytest-asyncio
pytest_plugins = ['pytest_asyncio']

@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    session = MagicMock()
    return session

@pytest.fixture
def mock_mcp_client():
    """Create a mock MCP client."""
    client = MagicMock()
    client.call_tool = AsyncMock(return_value={"result": "success"})
    client.is_connected = True
    return client

@pytest.fixture
def mock_mcp_cache():
    """Create a mock MCP cache."""
    cache = MagicMock()
    cache.get = MagicMock(return_value=None)
    cache.set = MagicMock(return_value=True)
    cache.is_enabled = MagicMock(return_value=True)
    return cache

@pytest.fixture
def mock_mcp_async_executor():
    """Create a mock MCP async executor."""
    executor = MagicMock()
    executor.submit_task = MagicMock(return_value="task-id")
    executor.get_task_status = MagicMock(return_value={
        "task_id": "task-id",
        "status": "running",
        "progress": 0.5
    })
    return executor

@pytest.fixture
def mock_settings():
    """Create mock settings."""
    settings = MagicMock()
    settings.MCP_ENABLED = True
    settings.MCP_CACHE_ENABLED = True
    settings.MCP_ASYNC_ENABLED = True
    settings.MCP_MAX_RETRIES = 3
    settings.MCP_RETRY_INITIAL_DELAY = 0.01
    settings.MCP_RETRY_BACKOFF_FACTOR = 2.0
    settings.MCP_FALLBACK_ENABLED = True
    return settings
