from sqlalchemy import Column, String, DateTime, Integer
from sqlalchemy.sql import func
import uuid

from backend.db.database import Base

class SuggestionInteraction(Base):
    """
    Model for tracking user interactions with suggestions
    """
    __tablename__ = "suggestion_interactions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    suggestion_id = Column(String, nullable=False, index=True)
    interaction = Column(String, nullable=False)  # 'accepted' or 'dismissed'
    timestamp = Column(DateTime, server_default=func.now())
    user_id = Column(String, nullable=True)  # Optional user ID
