"""
FastAPI router for Text-to-Speech endpoints using Coqui TTS.
"""

import os
import tempfile
import logging
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from backend.services.coqui_tts_service import CoquiTTSService

# Initialize router
router = APIRouter(prefix="/tts", tags=["Text-to-Speech"])

# Initialize TTS service (singleton)
_tts_service = None

def get_tts_service() -> CoquiTTSService:
    """
    Get or initialize the Coqui TTS service.
    Returns:
        CoquiTTSService instance
    """
    global _tts_service
    if _tts_service is None:
        try:
            _tts_service = CoquiTTSService()
            logger.info(f"Initialized Coqui TTS service.")
        except Exception as e:
            logger.error(f"Failed to initialize Coqui TTS service: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize TTS service: {str(e)}"
            )
    return _tts_service

# Request/Response models
class SynthesizeRequest(BaseModel):
    text: str
    # CoquiTTSService does not use preset_id or voice_params by default
    use_cache: bool = True

class VoicePresetRequest(BaseModel):
    name: str
    preset_type: str
    gender: Optional[str] = None
    pitch: Optional[int] = None
    speed: Optional[int] = None

class VoicePreset(BaseModel):
    id: str
    name: str
    type: str
    gender: Optional[str] = None
    pitch: Optional[int] = None
    speed: Optional[int] = None
    sample_path: Optional[str] = None
    created_at: str

@router.post("/synthesize")
async def synthesize_speech(
    request: SynthesizeRequest,
    tts_service: CoquiTTSService = Depends(get_tts_service)
):
    """
    Convert text to speech using Coqui TTS.
    """
    try:
        logger.info(f"Synthesizing speech for text: '{request.text[:50]}...'")
        audio_path = tts_service.synthesize(request.text)
        return FileResponse(
            path=audio_path,
            media_type="audio/wav",
            filename="speech.wav"
        )
    except Exception as e:
        logger.error(f"Error synthesizing speech: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to synthesize speech: {str(e)}"
        )