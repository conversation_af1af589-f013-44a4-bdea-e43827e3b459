"""
MCP API endpoints for AI features.
"""

from fastapi import APIRouter, Depends, HTTPException, Body
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
from sqlalchemy.orm import Session

from backend.dependencies import get_db_fastapi
from backend.services.ai.mcp_client import DJMixConstructorMCPClient
from backend.services.ai.mcp_server_runner import MCPServerRunner
from backend.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ai/mcp",
    tags=["AI MCP"]
)

# Models for request and response
class MCPStatusResponse(BaseModel):
    is_enabled: bool
    is_running: bool
    server_status: Dict[str, Any]
    available_tools: List[Dict[str, Any]]

class MCPToolRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any]

class MCPToolResponse(BaseModel):
    result: Dict[str, Any]
    success: bool
    error: Optional[str] = None

class MCPToggleRequest(BaseModel):
    enabled: bool

class MCPToggleResponse(BaseModel):
    success: bool
    message: str
    is_enabled: bool

# Singleton instances
mcp_client = DJMixConstructorMCPClient()
mcp_server_runner = MCPServerRunner()

@router.get("/status", response_model=MCPStatusResponse)
async def get_mcp_status(db: Session = Depends(get_db_fastapi)):
    """Get the status of the MCP server and available tools."""
    try:
        # Get server status
        server_status = mcp_server_runner.get_status()
        
        # Get available tools if the server is running
        available_tools = []
        if server_status["is_running"] and mcp_client.is_connected:
            available_tools = await mcp_client.get_tools()
        
        return {
            "is_enabled": settings.MCP_ENABLED,
            "is_running": server_status["is_running"],
            "server_status": server_status,
            "available_tools": available_tools
        }
    except Exception as e:
        logger.error(f"Error getting MCP status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/toggle", response_model=MCPToggleResponse)
async def toggle_mcp(
    request: MCPToggleRequest = Body(...),
    db: Session = Depends(get_db_fastapi)
):
    """Enable or disable the MCP server."""
    try:
        if request.enabled:
            # Start the MCP server if it's not already running
            if not mcp_server_runner.is_running:
                server_started = mcp_server_runner.start()
                if not server_started:
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to start MCP server"
                    )
                
                # Connect the MCP client
                client_connected = await mcp_client.connect()
                if not client_connected:
                    # Stop the server if client connection fails
                    mcp_server_runner.stop()
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to connect MCP client"
                    )
                
                return {
                    "success": True,
                    "message": "MCP server started and client connected",
                    "is_enabled": True
                }
            else:
                return {
                    "success": True,
                    "message": "MCP server is already running",
                    "is_enabled": True
                }
        else:
            # Stop the MCP server if it's running
            if mcp_server_runner.is_running:
                # Disconnect the client
                await mcp_client.disconnect()
                
                # Stop the server
                server_stopped = mcp_server_runner.stop()
                if not server_stopped:
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to stop MCP server"
                    )
                
                return {
                    "success": True,
                    "message": "MCP server stopped and client disconnected",
                    "is_enabled": False
                }
            else:
                return {
                    "success": True,
                    "message": "MCP server is already stopped",
                    "is_enabled": False
                }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling MCP: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/restart", response_model=MCPToggleResponse)
async def restart_mcp(db: Session = Depends(get_db_fastapi)):
    """Restart the MCP server."""
    try:
        # Disconnect the client
        await mcp_client.disconnect()
        
        # Restart the server
        server_restarted = mcp_server_runner.restart()
        if not server_restarted:
            raise HTTPException(
                status_code=500,
                detail="Failed to restart MCP server"
            )
        
        # Connect the client
        client_connected = await mcp_client.connect()
        if not client_connected:
            # Stop the server if client connection fails
            mcp_server_runner.stop()
            raise HTTPException(
                status_code=500,
                detail="Failed to connect MCP client after restart"
            )
        
        return {
            "success": True,
            "message": "MCP server restarted and client reconnected",
            "is_enabled": True
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error restarting MCP: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/call-tool", response_model=MCPToolResponse)
async def call_mcp_tool(
    request: MCPToolRequest = Body(...),
    db: Session = Depends(get_db_fastapi)
):
    """Call a tool on the MCP server."""
    try:
        # Check if MCP is enabled and running
        if not settings.MCP_ENABLED:
            raise HTTPException(
                status_code=400,
                detail="MCP is disabled in settings"
            )
        
        if not mcp_server_runner.is_running or not mcp_client.is_connected:
            raise HTTPException(
                status_code=400,
                detail="MCP server is not running or client is not connected"
            )
        
        # Call the tool
        result = await mcp_client.call_tool(request.tool_name, request.parameters)
        
        # Check for error in result
        if "error" in result:
            return {
                "result": result,
                "success": False,
                "error": result["error"]
            }
        
        return {
            "result": result,
            "success": True
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calling MCP tool: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tools", response_model=List[Dict[str, Any]])
async def get_mcp_tools(db: Session = Depends(get_db_fastapi)):
    """Get the list of available tools from the MCP server."""
    try:
        # Check if MCP is enabled and running
        if not settings.MCP_ENABLED:
            raise HTTPException(
                status_code=400,
                detail="MCP is disabled in settings"
            )
        
        if not mcp_server_runner.is_running or not mcp_client.is_connected:
            raise HTTPException(
                status_code=400,
                detail="MCP server is not running or client is not connected"
            )
        
        # Get the tools
        tools = await mcp_client.get_tools()
        return tools
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MCP tools: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
