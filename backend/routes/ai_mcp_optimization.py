"""
MCP Optimization API endpoints.

This module provides API endpoints for MCP optimization.
"""

from fastapi import APIRouter, Depends, HTTPException, Body, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging

from backend.services.ai.mcp_optimization import mcp_optimization_service
from backend.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ai/mcp/optimization",
    tags=["AI MCP Optimization"]
)

# Models for request and response
class OptimizationRecommendation(BaseModel):
    """Model for an optimization recommendation."""
    id: str
    feature_id: str
    parameter_name: str
    current_value: Any
    recommended_value: Any
    impact_score: float
    description: str
    reason: str

class OptimizationResult(BaseModel):
    """Model for the result of applying an optimization."""
    success: bool
    message: str
    parameter: Optional[str] = None
    old_value: Optional[Any] = None
    new_value: Optional[Any] = None

class PerformanceMetrics(BaseModel):
    """Model for performance metrics."""
    tool_metrics: Dict[str, Any]
    cache_stats: Dict[str, Any]
    async_stats: Dict[str, Any]
    timestamp: float

@router.get("/recommendations", response_model=List[OptimizationRecommendation])
async def get_optimization_recommendations():
    """
    Get optimization recommendations for MCP settings.
    
    Returns:
        List of optimization recommendations
    """
    try:
        recommendations = mcp_optimization_service.generate_optimization_recommendations()
        return recommendations
    except Exception as e:
        logger.error(f"Error getting optimization recommendations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/apply/{recommendation_id}", response_model=OptimizationResult)
async def apply_optimization(recommendation_id: str):
    """
    Apply an optimization recommendation.
    
    Args:
        recommendation_id: ID of the recommendation to apply
        
    Returns:
        Result of the operation
    """
    try:
        result = mcp_optimization_service.apply_optimization(recommendation_id)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error applying optimization: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics", response_model=PerformanceMetrics)
async def get_performance_metrics():
    """
    Get performance metrics for MCP tools.
    
    Returns:
        Dictionary of performance metrics
    """
    try:
        metrics = mcp_optimization_service.collect_performance_metrics()
        return metrics
    except Exception as e:
        logger.error(f"Error getting performance metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tool-performance/{tool_name}")
async def get_tool_performance(
    tool_name: str,
    time_range_hours: int = Query(24, description="Time range in hours to analyze")
):
    """
    Analyze performance of a specific MCP tool.
    
    Args:
        tool_name: Name of the tool to analyze
        time_range_hours: Time range in hours to analyze
        
    Returns:
        Dictionary of performance metrics for the tool
    """
    try:
        metrics = mcp_optimization_service.analyze_tool_performance(
            tool_name=tool_name,
            time_range_hours=time_range_hours
        )
        return metrics
    except Exception as e:
        logger.error(f"Error analyzing tool performance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
