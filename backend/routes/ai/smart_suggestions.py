from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Body
from pydantic import BaseModel, Field
import uuid
from datetime import datetime
import logging

from backend.services.ai_service import AIService
from backend.services.user_service import UserService
from backend.dependencies import get_ai_service, get_user_service
from backend.models.user_preferences import UserPreferences

router = APIRouter()
logger = logging.getLogger(__name__)

class RecentAction(BaseModel):
    type: str
    description: str
    timestamp: datetime
    data: Optional[Dict[str, Any]] = None

class ActiveTrack(BaseModel):
    id: str
    title: Optional[str] = None
    artist: Optional[str] = None
    bpm: Optional[float] = None
    key: Optional[str] = None
    energy: Optional[int] = None

class AppState(BaseModel):
    current_page: str
    current_view: Optional[str] = None
    active_tracks: List[ActiveTrack] = []
    active_mix: Optional[Dict[str, Any]] = None
    recent_actions: List[RecentAction] = []
    current_bpm: Optional[float] = None
    current_key: Optional[str] = None
    user_skill_level: Optional[str] = "beginner"

class UserPreferencesInput(BaseModel):
    skill_level: str = "beginner"
    preferred_genres: List[str] = []
    preferred_bpm_ranges: List[List[int]] = []
    preferred_transitions: List[str] = []
    complexity_setting: int = 50
    prefer_exploration: bool = False
    feature_usage: Dict[str, Dict[str, Any]] = {}

class SuggestionRequest(BaseModel):
    app_state: AppState
    user_preferences: UserPreferencesInput
    max_suggestions: int = 5

class Suggestion(BaseModel):
    id: str = Field(default_factory=lambda: f"suggestion-{uuid.uuid4()}")
    title: str
    description: str
    type: str
    context: Optional[str] = None
    action: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    confidence: float
    source: str = "ai"

class SuggestionResponse(BaseModel):
    suggestions: List[Suggestion]

class SuggestionInteraction(BaseModel):
    suggestion_id: str
    interaction: str  # 'accepted' or 'dismissed'

@router.post("/smart-suggestions", response_model=SuggestionResponse)
async def get_smart_suggestions(
    request: SuggestionRequest = Body(...),
    ai_service: AIService = Depends(get_ai_service),
    user_service: UserService = Depends(get_user_service)
):
    """
    Generate smart suggestions based on application state and user preferences
    """
    try:
        # Get user preferences from database if available
        user_preferences = None
        try:
            # This would typically use a user ID from auth, but we're simplifying here
            user_preferences = await user_service.get_user_preferences("current_user")
        except Exception as e:
            logger.warning(f"Could not retrieve user preferences: {str(e)}")
            # Continue with the provided preferences
        
        # Generate suggestions using AI
        suggestions = await generate_ai_suggestions(
            ai_service=ai_service,
            app_state=request.app_state,
            user_preferences=user_preferences or request.user_preferences,
            max_suggestions=request.max_suggestions
        )
        
        return SuggestionResponse(suggestions=suggestions)
    except Exception as e:
        logger.error(f"Error generating smart suggestions: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error generating suggestions: {str(e)}")

@router.post("/track-suggestion")
async def track_suggestion_interaction(
    interaction: SuggestionInteraction = Body(...),
    user_service: UserService = Depends(get_user_service)
):
    """
    Track user interaction with a suggestion
    """
    try:
        # Log the interaction
        logger.info(f"Suggestion interaction: {interaction.suggestion_id} - {interaction.interaction}")
        
        # Update user preferences based on interaction
        await user_service.track_suggestion_interaction(
            suggestion_id=interaction.suggestion_id,
            interaction=interaction.interaction
        )
        
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error tracking suggestion interaction: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error tracking interaction: {str(e)}")

async def generate_ai_suggestions(
    ai_service: AIService,
    app_state: AppState,
    user_preferences: UserPreferencesInput,
    max_suggestions: int
) -> List[Suggestion]:
    """
    Generate suggestions using AI based on application state and user preferences
    """
    # Create context for AI
    context = {
        "current_page": app_state.current_page,
        "current_view": app_state.current_view,
        "active_tracks_count": len(app_state.active_tracks),
        "active_tracks": [
            {
                "title": track.title,
                "artist": track.artist,
                "bpm": track.bpm,
                "key": track.key,
                "energy": track.energy
            }
            for track in app_state.active_tracks[:5]  # Limit to 5 tracks for context
        ],
        "has_active_mix": app_state.active_mix is not None,
        "current_bpm": app_state.current_bpm,
        "current_key": app_state.current_key,
        "recent_actions": [
            {
                "type": action.type,
                "description": action.description,
                "time_ago_seconds": (datetime.now() - action.timestamp).total_seconds()
            }
            for action in app_state.recent_actions[:10]  # Limit to 10 recent actions
        ],
        "user_preferences": {
            "skill_level": user_preferences.skill_level,
            "preferred_genres": user_preferences.preferred_genres,
            "preferred_bpm_ranges": user_preferences.preferred_bpm_ranges,
            "preferred_transitions": user_preferences.preferred_transitions,
            "complexity_setting": user_preferences.complexity_setting,
            "prefer_exploration": user_preferences.prefer_exploration
        }
    }
    
    # Call AI service to generate suggestions
    ai_response = await ai_service.generate_suggestions(context, max_suggestions)
    
    # Convert AI response to Suggestion objects
    suggestions = []
    for item in ai_response:
        try:
            suggestion = Suggestion(
                title=item["title"],
                description=item["description"],
                type=item["type"],
                context=item.get("context"),
                action=item.get("action"),
                confidence=item.get("confidence", 0.8),
                source="ai"
            )
            suggestions.append(suggestion)
        except KeyError as e:
            logger.warning(f"Invalid suggestion format: {str(e)}")
            continue
    
    return suggestions
