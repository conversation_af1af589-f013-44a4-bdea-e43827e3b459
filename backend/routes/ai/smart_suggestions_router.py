from fastapi import APIRouter, Depends, HTTPException, Body
from typing import List, Dict, Any, Optional
import logging

from backend.routes.ai.smart_suggestions import (
    SuggestionRequest,
    SuggestionResponse,
    SuggestionInteraction,
    get_smart_suggestions,
    track_suggestion_interaction
)

router = APIRouter(prefix="/ai", tags=["AI Smart Suggestions"])

# Re-export the routes from smart_suggestions.py
router.add_api_route(
    "/smart-suggestions",
    get_smart_suggestions,
    methods=["POST"],
    response_model=SuggestionResponse,
    summary="Generate smart suggestions",
    description="Generate smart suggestions based on application state and user preferences"
)

router.add_api_route(
    "/track-suggestion",
    track_suggestion_interaction,
    methods=["POST"],
    summary="Track suggestion interaction",
    description="Track user interaction with a suggestion (accepted or dismissed)"
)
