"""
Beat Alignment API Routes for Phase 3 Beat Grid Integration.

Provides REST API endpoints for beat-perfect track alignment and synchronization.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import logging

from ..dependencies import get_db_fastapi as get_db
from ..services.beat_alignment_service import BeatAlignmentService, BeatAlignment, BeatSyncResult
from ..repositories.track_repository import TrackRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/beat-alignment", tags=["beat-alignment"])

# Request/Response Models
class BeatAlignmentRequest(BaseModel):
    track_id: int
    master_bpm: float
    master_beat_grid: Optional[List[float]] = None
    target_position: float = 0.0

class BeatAlignmentResponse(BaseModel):
    success: bool
    track_id: int
    beat_alignment: Optional[dict] = None
    optimal_position: float
    sync_quality_score: float
    warnings: List[str] = []
    error: Optional[str] = None

class BeatSnapRequest(BaseModel):
    position: float
    beat_times: List[float]
    tolerance: float = 0.1

class BeatSnapResponse(BaseModel):
    original_position: float
    snapped_position: float
    distance_to_beat: float
    snapped: bool

class BatchBeatAlignmentRequest(BaseModel):
    track_ids: List[int]
    master_bpm: float
    master_beat_grid: Optional[List[float]] = None

class BatchBeatAlignmentResponse(BaseModel):
    results: List[BeatAlignmentResponse]
    summary: dict

@router.post("/calculate", response_model=BeatAlignmentResponse)
async def calculate_beat_alignment(
    request: BeatAlignmentRequest,
    db: Session = Depends(get_db)
):
    """
    Calculate beat-perfect alignment for a track against master BPM/beat grid.
    
    This endpoint integrates beat grid detection with beatmatching to provide
    beat-perfect track alignment and synchronization quality metrics.
    """
    try:
        logger.info(f"Calculating beat alignment for track {request.track_id}")
        
        # Get track from database
        track_repo = TrackRepository(db)
        track = await track_repo.get_track_by_id(request.track_id)
        
        if not track:
            raise HTTPException(
                status_code=404,
                detail=f"Track {request.track_id} not found"
            )
        
        # Initialize beat alignment service
        alignment_service = BeatAlignmentService(db)
        
        # Calculate beat alignment
        result = await alignment_service.calculate_beat_alignment(
            track=track,
            master_bpm=request.master_bpm,
            master_beat_grid=request.master_beat_grid,
            target_position=request.target_position
        )
        
        # Convert BeatAlignment to dict for response
        beat_alignment_dict = None
        if result.beat_alignment:
            beat_alignment_dict = {
                "track_id": result.beat_alignment.track_id,
                "original_beat_times": result.beat_alignment.original_beat_times,
                "aligned_beat_times": result.beat_alignment.aligned_beat_times,
                "beat_offset": result.beat_alignment.beat_offset,
                "sync_quality": result.beat_alignment.sync_quality,
                "alignment_confidence": result.beat_alignment.alignment_confidence,
                "stretch_ratio": result.beat_alignment.stretch_ratio,
                "master_bpm": result.beat_alignment.master_bpm,
                "original_bpm": result.beat_alignment.original_bpm
            }
        
        return BeatAlignmentResponse(
            success=result.success,
            track_id=request.track_id,
            beat_alignment=beat_alignment_dict,
            optimal_position=result.optimal_position,
            sync_quality_score=result.sync_quality_score,
            warnings=result.warnings,
            error=result.error
        )
        
    except Exception as e:
        logger.error(f"Error calculating beat alignment: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to calculate beat alignment: {str(e)}"
        )

@router.post("/snap-to-beat", response_model=BeatSnapResponse)
async def snap_to_nearest_beat(request: BeatSnapRequest, db: Session = Depends(get_db)):
    """
    Snap a position to the nearest beat boundary.

    Useful for beat-accurate track positioning and alignment in the timeline.
    """
    try:
        # Use full BeatAlignmentService for comprehensive beat snapping
        alignment_service = BeatAlignmentService(db)

        snapped_position, distance = alignment_service.snap_to_nearest_beat(
            position=request.position,
            beat_times=request.beat_times,
            tolerance=request.tolerance
        )

        return BeatSnapResponse(
            original_position=request.position,
            snapped_position=snapped_position,
            distance_to_beat=distance,
            snapped=distance <= request.tolerance
        )

    except Exception as e:
        logger.error(f"Error snapping to beat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to snap to beat: {str(e)}"
        )

@router.post("/batch-calculate", response_model=BatchBeatAlignmentResponse)
async def batch_calculate_beat_alignment(
    request: BatchBeatAlignmentRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Calculate beat alignment for multiple tracks in batch.
    
    Useful for aligning entire playlists or collections to a master BPM.
    """
    try:
        logger.info(f"Batch calculating beat alignment for {len(request.track_ids)} tracks")
        
        track_repo = TrackRepository(db)
        alignment_service = BeatAlignmentService(db)
        
        results = []
        successful_alignments = 0
        total_quality_score = 0.0
        
        for track_id in request.track_ids:
            try:
                # Get track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    results.append(BeatAlignmentResponse(
                        success=False,
                        track_id=track_id,
                        optimal_position=0.0,
                        sync_quality_score=0.0,
                        error=f"Track {track_id} not found"
                    ))
                    continue
                
                # Calculate alignment
                result = await alignment_service.calculate_beat_alignment(
                    track=track,
                    master_bpm=request.master_bpm,
                    master_beat_grid=request.master_beat_grid,
                    target_position=0.0
                )
                
                # Convert to response format
                beat_alignment_dict = None
                if result.beat_alignment:
                    beat_alignment_dict = {
                        "track_id": result.beat_alignment.track_id,
                        "original_beat_times": result.beat_alignment.original_beat_times,
                        "aligned_beat_times": result.beat_alignment.aligned_beat_times,
                        "beat_offset": result.beat_alignment.beat_offset,
                        "sync_quality": result.beat_alignment.sync_quality,
                        "alignment_confidence": result.beat_alignment.alignment_confidence,
                        "stretch_ratio": result.beat_alignment.stretch_ratio,
                        "master_bpm": result.beat_alignment.master_bpm,
                        "original_bpm": result.beat_alignment.original_bpm
                    }
                
                response = BeatAlignmentResponse(
                    success=result.success,
                    track_id=track_id,
                    beat_alignment=beat_alignment_dict,
                    optimal_position=result.optimal_position,
                    sync_quality_score=result.sync_quality_score,
                    warnings=result.warnings,
                    error=result.error
                )
                
                results.append(response)
                
                if result.success:
                    successful_alignments += 1
                    total_quality_score += result.sync_quality_score
                    
            except Exception as e:
                logger.error(f"Error processing track {track_id}: {e}")
                results.append(BeatAlignmentResponse(
                    success=False,
                    track_id=track_id,
                    optimal_position=0.0,
                    sync_quality_score=0.0,
                    error=str(e)
                ))
        
        # Calculate summary statistics
        average_quality = (total_quality_score / successful_alignments) if successful_alignments > 0 else 0.0
        
        summary = {
            "total_tracks": len(request.track_ids),
            "successful_alignments": successful_alignments,
            "failed_alignments": len(request.track_ids) - successful_alignments,
            "average_quality_score": average_quality,
            "master_bpm": request.master_bpm
        }
        
        return BatchBeatAlignmentResponse(
            results=results,
            summary=summary
        )
        
    except Exception as e:
        logger.error(f"Error in batch beat alignment: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process batch beat alignment: {str(e)}"
        )

@router.get("/quality-metrics/{track_id}")
async def get_beat_alignment_quality_metrics(
    track_id: int,
    master_bpm: float,
    db: Session = Depends(get_db)
):
    """
    Get detailed quality metrics for beat alignment of a specific track.
    
    Provides comprehensive analysis of beat grid quality, alignment confidence,
    and synchronization recommendations.
    """
    try:
        track_repo = TrackRepository(db)
        track = await track_repo.get_track_by_id(track_id)
        
        if not track:
            raise HTTPException(
                status_code=404,
                detail=f"Track {track_id} not found"
            )
        
        alignment_service = BeatAlignmentService(db)
        result = await alignment_service.calculate_beat_alignment(
            track=track,
            master_bpm=master_bpm,
            target_position=0.0
        )
        
        if not result.success or not result.beat_alignment:
            return {
                "track_id": track_id,
                "quality_available": False,
                "error": result.error
            }
        
        alignment = result.beat_alignment
        
        # Calculate additional quality metrics
        stretch_percentage = abs((alignment.stretch_ratio - 1.0) * 100)
        compatibility_rating = "excellent" if stretch_percentage <= 5 else \
                             "good" if stretch_percentage <= 10 else \
                             "fair" if stretch_percentage <= 15 else "poor"
        
        return {
            "track_id": track_id,
            "quality_available": True,
            "sync_quality": alignment.sync_quality,
            "alignment_confidence": alignment.alignment_confidence,
            "beat_offset_ms": alignment.beat_offset * 1000,
            "stretch_percentage": stretch_percentage,
            "compatibility_rating": compatibility_rating,
            "original_bpm": alignment.original_bpm,
            "target_bpm": alignment.master_bpm,
            "stretch_ratio": alignment.stretch_ratio,
            "quality_score": result.sync_quality_score,
            "warnings": result.warnings,
            "recommendations": [
                "Perfect alignment - ready for professional mixing" if alignment.sync_quality == "perfect" else
                "Good alignment - suitable for most mixing scenarios" if alignment.sync_quality == "good" else
                "Poor alignment - consider manual adjustment or different track"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting quality metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get quality metrics: {str(e)}"
        )
