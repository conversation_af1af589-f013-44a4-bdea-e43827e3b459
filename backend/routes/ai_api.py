"""
AI API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Body
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
from backend.services.ai import AIProvider, get_ai_provider
from backend.dependencies import get_db_fastapi
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ai",
    tags=["ai"]
)

# Models for request and response
class StyleGenerationRequest(BaseModel):
    description: str
    constraints: Optional[Dict[str, Any]] = None

class StyleGenerationResponse(BaseModel):
    name: str
    min_bpm: float
    max_bpm: float
    energy_pattern: List[int]
    key_rules: List[str]
    description: str
    icon: str

class StyleDocumentationRequest(BaseModel):
    style: Dict[str, Any]

class StyleDocumentationResponse(BaseModel):
    documentation: str

class TransitionSuggestionRequest(BaseModel):
    current_track: Dict[str, Any]
    next_track: Dict[str, Any]

class TransitionSuggestion(BaseModel):
    name: str
    description: str
    technique: str
    reason: Optional[str] = None

class CollectionAnalysisRequest(BaseModel):
    collection_data: Dict[str, Any]

class CollectionAnalysisResponse(BaseModel):
    summary: str
    strengths: List[str]
    gaps: List[str]
    recommendations: List[str]
    suitable_mix_styles: List[str]

class QuestionRequest(BaseModel):
    question: str
    context: Optional[str] = None

class QuestionResponse(BaseModel):
    answer: str

class FeedbackRequest(BaseModel):
    featureId: str
    featureType: str
    contentId: Optional[str] = None
    contentType: Optional[str] = None
    rating: str
    stars: Optional[int] = None
    comment: Optional[str] = None
    user_preferences: Optional[Dict[str, Any]] = None

# Dependency to get AI provider
def get_ai_service() -> AIProvider:
    return get_ai_provider()

@router.post("/generate-style", response_model=StyleGenerationResponse)
async def generate_style(
    request: StyleGenerationRequest = Body(...),
    ai_service: AIProvider = Depends(get_ai_service),
    db: Session = Depends(get_db_fastapi)
):
    """Generate mix style parameters from a description."""
    try:
        parameters = await ai_service.generate_style_parameters(
            request.description,
            request.constraints
        )
        return parameters
    except Exception as e:
        logger.error(f"Error generating style parameters: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-documentation", response_model=StyleDocumentationResponse)
async def generate_documentation(
    request: StyleDocumentationRequest = Body(...),
    ai_service: AIProvider = Depends(get_ai_service),
    db: Session = Depends(get_db_fastapi)
):
    """Generate documentation for a mix style."""
    try:
        documentation = await ai_service.generate_style_documentation(request.style)
        return {"documentation": documentation}
    except Exception as e:
        logger.error(f"Error generating style documentation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/transition-suggestions", response_model=List[TransitionSuggestion])
async def get_transition_suggestions(
    request: TransitionSuggestionRequest = Body(...),
    ai_service: AIProvider = Depends(get_ai_service),
    db: Session = Depends(get_db_fastapi)
):
    """Get transition suggestions between two tracks."""
    try:
        suggestions = await ai_service.generate_transition_suggestions(
            request.current_track,
            request.next_track
        )
        return suggestions
    except Exception as e:
        logger.error(f"Error generating transition suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze-collection", response_model=CollectionAnalysisResponse)
async def analyze_collection(
    request: CollectionAnalysisRequest = Body(...),
    ai_service: AIProvider = Depends(get_ai_service),
    db: Session = Depends(get_db_fastapi)
):
    """Analyze a music collection and provide insights."""
    try:
        analysis = await ai_service.analyze_collection(request.collection_data)
        return analysis
    except Exception as e:
        logger.error(f"Error analyzing collection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/answer-question", response_model=QuestionResponse)
async def answer_question(
    request: QuestionRequest = Body(...),
    ai_service: AIProvider = Depends(get_ai_service),
    db: Session = Depends(get_db_fastapi)
):
    """Answer a user question about DJing or music production."""
    try:
        answer = await ai_service.answer_question(request.question, request.context)
        return {"answer": answer}
    except Exception as e:
        logger.error(f"Error answering question: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/feedback")
async def submit_feedback(
    request: FeedbackRequest = Body(...)
):
    """Submit feedback for AI features."""
    try:
        # Log the feedback
        logger.info(f"Received feedback for {request.featureType} - {request.featureId}")
        logger.info(f"Rating: {request.rating}, Stars: {request.stars}")
        if request.comment:
            logger.info(f"Comment: {request.comment}")

        # In a real implementation, you would store this in a database
        # For now, we'll just return a success response
        return {"status": "success", "message": "Feedback received"}
    except Exception as e:
        logger.error(f"Error submitting feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
