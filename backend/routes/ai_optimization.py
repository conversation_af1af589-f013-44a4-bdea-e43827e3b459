"""
API routes for AI optimization.
"""

from fastapi import APIRouter, Depends, HTTPException, Body, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging

from backend.services.optimization.ai_optimization_service import AIOptimizationService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/ai-optimization",
    tags=["ai-optimization"],
    responses={404: {"description": "Not found"}},
)

class OptimizationResult(BaseModel):
    success: bool
    message: str
    feature_id: Optional[str] = None
    parameter_name: Optional[str] = None
    old_value: Optional[Any] = None
    new_value: Optional[Any] = None

class ApplyRecommendationRequest(BaseModel):
    feature_id: str
    parameter_name: str
    new_value: str
    applied_by: Optional[str] = "user"

@router.get("/recommendations")
async def get_recommendations(
    feature_id: Optional[str] = None,
    time_range_hours: Optional[int] = None,
    limit: int = 5
):
    """Get optimization recommendations."""
    try:
        recommendations = AIOptimizationService.get_optimization_recommendations(
            feature_id=feature_id,
            time_range_hours=time_range_hours,
            limit=limit
        )
        return recommendations
    except Exception as e:
        logger.error(f"Error getting recommendations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/apply/{recommendation_id}", response_model=OptimizationResult)
async def apply_recommendation(
    recommendation_id: str,
    request: ApplyRecommendationRequest
):
    """Apply a specific recommendation."""
    try:
        result = AIOptimizationService.apply_recommendation(
            recommendation_id=recommendation_id,
            feature_id=request.feature_id,
            parameter_name=request.parameter_name,
            new_value=request.new_value,
            applied_by=request.applied_by
        )
        return result
    except Exception as e:
        logger.error(f"Error applying recommendation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history")
async def get_optimization_history(
    feature_id: Optional[str] = None,
    time_range_hours: Optional[int] = None,
    limit: int = 10
):
    """Get optimization history."""
    try:
        history = AIOptimizationService.get_optimization_history(
            feature_id=feature_id,
            time_range_hours=time_range_hours,
            limit=limit
        )
        return history
    except Exception as e:
        logger.error(f"Error getting optimization history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
