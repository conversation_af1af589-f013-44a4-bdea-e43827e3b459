"""
Asynchronous MCP API endpoints.

This module provides API endpoints for asynchronous MCP tool execution.
"""

from fastapi import APIRouter, Depends, HTTPException, Body, BackgroundTasks
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
import time

from backend.services.ai.mcp_async_executor import mcp_async_executor
from backend.services.ai.mcp_client import DJMixConstructorMCPClient
from backend.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ai/mcp/async",
    tags=["AI MCP Async"]
)

# Models for request and response
class AsyncToolRequest(BaseModel):
    """Request model for async tool execution."""
    tool_name: str
    parameters: Dict[str, Any]

class AsyncTaskResponse(BaseModel):
    """Response model for async task status."""
    task_id: str
    tool_name: str
    status: str
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    progress: float
    progress_message: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class AsyncTaskListResponse(BaseModel):
    """Response model for async task list."""
    tasks: List[AsyncTaskResponse]
    total: int

@router.post("/execute", response_model=Dict[str, str])
async def execute_tool_async(
    request: AsyncToolRequest,
    mcp_client: DJMixConstructorMCPClient = Depends(lambda: DJMixConstructorMCPClient())
):
    """
    Execute an MCP tool asynchronously.
    
    Args:
        request: Tool execution request
        mcp_client: MCP client instance
        
    Returns:
        Task ID for tracking the execution
    """
    if not settings.MCP_ASYNC_ENABLED:
        raise HTTPException(status_code=400, detail="Async execution is disabled")
    
    if not settings.MCP_ENABLED:
        raise HTTPException(status_code=400, detail="MCP is disabled")
    
    try:
        # Set the MCP client if not already set
        if not mcp_async_executor.mcp_client:
            mcp_async_executor.set_mcp_client(mcp_client)
        
        # Submit the task
        task_id = mcp_async_executor.submit_task(request.tool_name, request.parameters)
        
        return {"task_id": task_id}
    
    except Exception as e:
        logger.error(f"Error submitting async task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}", response_model=AsyncTaskResponse)
async def get_task_status(task_id: str):
    """
    Get the status of an async task.
    
    Args:
        task_id: ID of the task
        
    Returns:
        Task status information
    """
    if not settings.MCP_ASYNC_ENABLED:
        raise HTTPException(status_code=400, detail="Async execution is disabled")
    
    status = mcp_async_executor.get_task_status(task_id)
    
    if not status:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
    
    return status

@router.get("/result/{task_id}", response_model=Dict[str, Any])
async def get_task_result(task_id: str):
    """
    Get the result of a completed async task.
    
    Args:
        task_id: ID of the task
        
    Returns:
        Task result
    """
    if not settings.MCP_ASYNC_ENABLED:
        raise HTTPException(status_code=400, detail="Async execution is disabled")
    
    status = mcp_async_executor.get_task_status(task_id)
    
    if not status:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
    
    if status["status"] != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"Task {task_id} is not completed (status: {status['status']})"
        )
    
    return status["result"]

@router.delete("/cancel/{task_id}", response_model=Dict[str, bool])
async def cancel_task(task_id: str):
    """
    Cancel an async task.
    
    Args:
        task_id: ID of the task
        
    Returns:
        Whether the task was cancelled
    """
    if not settings.MCP_ASYNC_ENABLED:
        raise HTTPException(status_code=400, detail="Async execution is disabled")
    
    cancelled = mcp_async_executor.cancel_task(task_id)
    
    if not cancelled:
        raise HTTPException(
            status_code=400, 
            detail=f"Task {task_id} could not be cancelled (not found or already completed)"
        )
    
    return {"cancelled": True}

@router.get("/list", response_model=AsyncTaskListResponse)
async def list_tasks():
    """
    List all async tasks.
    
    Returns:
        List of tasks
    """
    if not settings.MCP_ASYNC_ENABLED:
        raise HTTPException(status_code=400, detail="Async execution is disabled")
    
    tasks = []
    
    for task_id in list(mcp_async_executor.tasks.keys()):
        status = mcp_async_executor.get_task_status(task_id)
        if status:
            tasks.append(status)
    
    return {
        "tasks": tasks,
        "total": len(tasks)
    }

@router.post("/cleanup", response_model=Dict[str, int])
async def cleanup_tasks(
    background_tasks: BackgroundTasks,
    max_age_seconds: int = Body(3600, embed=True)
):
    """
    Clean up old completed, failed, or cancelled tasks.
    
    Args:
        background_tasks: FastAPI background tasks
        max_age_seconds: Maximum age of tasks to keep
        
    Returns:
        Number of tasks removed
    """
    if not settings.MCP_ASYNC_ENABLED:
        raise HTTPException(status_code=400, detail="Async execution is disabled")
    
    # Run cleanup in the background
    def do_cleanup():
        return mcp_async_executor.cleanup_old_tasks(max_age_seconds)
    
    background_tasks.add_task(do_cleanup)
    
    return {"scheduled_cleanup": True}
