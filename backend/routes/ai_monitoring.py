"""
API routes for AI monitoring.
"""

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Body, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
import uuid

from backend.services.monitoring.ai_data_collector import AIDataCollector
from backend.services.monitoring.ai_analytics_service import AIAnalyticsService
from backend.services.monitoring.ai_settings_impact_analyzer import AISettingsImpactAnalyzer

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/ai-monitoring",
    tags=["ai-monitoring"],
    responses={404: {"description": "Not found"}},
)

class UserFeedback(BaseModel):
    feature_id: str
    rating: int
    feedback_text: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    advanced_settings: Optional[Dict[str, Any]] = None
    request_id: Optional[str] = None

class AIRequest(BaseModel):
    feature_id: str
    provider: str
    model: str
    parameters: Dict[str, Any]
    prompt: str
    response: Optional[str] = None
    response_time_ms: Optional[float] = None
    token_count: Optional[int] = None
    success: bool = True
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    user_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@router.get("/performance")
async def get_performance_metrics(
    feature_id: Optional[str] = None,
    time_range_hours: Optional[int] = None
):
    """Get performance metrics for AI requests."""
    try:
        metrics = AIAnalyticsService.get_performance_metrics(
            feature_id=feature_id,
            time_range_hours=time_range_hours
        )
        return metrics
    except Exception as e:
        logger.error(f"Error getting performance metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/satisfaction")
async def get_user_satisfaction_metrics(
    feature_id: Optional[str] = None,
    time_range_hours: Optional[int] = None
):
    """Get user satisfaction metrics."""
    try:
        metrics = AIAnalyticsService.get_user_satisfaction_metrics(
            feature_id=feature_id,
            time_range_hours=time_range_hours
        )
        return metrics
    except Exception as e:
        logger.error(f"Error getting user satisfaction metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/feedback")
async def submit_user_feedback(feedback: UserFeedback):
    """Submit user feedback on an AI response."""
    try:
        feedback_id = str(uuid.uuid4())
        request_id = feedback.request_id or str(uuid.uuid4())

        AIDataCollector.log_feedback(
            feedback_id=feedback_id,
            request_id=request_id,
            feature_id=feedback.feature_id,
            rating=feedback.rating,
            feedback_text=feedback.feedback_text,
            metadata={
                "parameters": feedback.parameters,
                "advanced_settings": feedback.advanced_settings
            }
        )
        return {"success": True, "feedback_id": feedback_id}
    except Exception as e:
        logger.error(f"Error submitting user feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/request")
async def log_ai_request(request: AIRequest):
    """Log an AI request with its details."""
    try:
        request_id = str(uuid.uuid4())

        AIDataCollector.log_request(
            request_id=request_id,
            feature_id=request.feature_id,
            provider=request.provider,
            model=request.model,
            parameters=request.parameters,
            prompt=request.prompt,
            response=request.response,
            response_time_ms=request.response_time_ms,
            token_count=request.token_count,
            success=request.success,
            error_type=request.error_type,
            error_message=request.error_message,
            user_id=request.user_id,
            metadata=request.metadata
        )
        return {"success": True, "request_id": request_id}
    except Exception as e:
        logger.error(f"Error logging AI request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/settings-usage")
async def get_settings_usage_stats(time_range_hours: Optional[int] = None):
    """Get settings usage statistics."""
    try:
        stats = AIAnalyticsService.get_settings_usage_stats(time_range_hours=time_range_hours)
        return stats
    except Exception as e:
        logger.error(f"Error getting settings usage stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/settings-performance")
async def get_settings_performance_comparison(
    feature_id: str,
    param_name: str,
    time_range_hours: Optional[int] = None
):
    """Get performance comparison for different settings."""
    try:
        comparison = AISettingsImpactAnalyzer.analyze_parameter_performance_impact(
            feature_id=feature_id,
            param_name=param_name,
            time_range_hours=time_range_hours
        )
        return comparison
    except Exception as e:
        logger.error(f"Error getting settings performance comparison: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/settings-satisfaction")
async def get_settings_satisfaction_comparison(
    feature_id: str,
    param_name: str,
    time_range_hours: Optional[int] = None
):
    """Get user satisfaction comparison for different settings."""
    try:
        comparison = AISettingsImpactAnalyzer.analyze_parameter_satisfaction_impact(
            feature_id=feature_id,
            param_name=param_name,
            time_range_hours=time_range_hours
        )
        return comparison
    except Exception as e:
        logger.error(f"Error getting settings satisfaction comparison: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/ab-test")
async def get_ab_test_results(
    feature_id: str,
    param_name: str,
    variant_a: str,
    variant_b: str,
    time_range_hours: Optional[int] = None
):
    """Get A/B test results for a parameter."""
    try:
        results = AISettingsImpactAnalyzer.get_ab_test_results(
            feature_id=feature_id,
            param_name=param_name,
            variant_a=variant_a,
            variant_b=variant_b,
            time_range_hours=time_range_hours
        )
        return results
    except Exception as e:
        logger.error(f"Error getting A/B test results: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
