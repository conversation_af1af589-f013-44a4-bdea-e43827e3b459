"""
Track Analysis Management API Routes
Provides endpoints for managing track analysis data including:
- Reverting to Mixed in Key metadata
- Stripping AI analysis data
- Selective data removal
- Re-analysis options
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
import logging

from backend.db.database import get_db
from backend.models.track import Track
from backend.models.track_analysis import TrackAnalysis
from backend.models.cue_point import CuePoint
from backend.models.track_segment import TrackSegment
from backend.models.beat_grid import BeatGrid
from backend.services.audio_analyzer import AudioAnalyzer

router = APIRouter(prefix="/tracks", tags=["track-analysis-management"])
logger = logging.getLogger(__name__)

@router.post("/{track_id}/analysis/revert-to-mixed-in-key")
async def revert_to_mixed_in_key(
    track_id: int,
    db: Session = Depends(get_db)
):
    """
    Revert track metadata to original Mixed in Key values
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")
        
        # Check if Mixed in Key data is available
        if not track.mixed_in_key_key and not track.mixed_in_key_energy:
            raise HTTPException(
                status_code=400, 
                detail="No Mixed in Key metadata available for this track"
            )
        
        # Store current values as librosa values (if they're not already from MiK)
        if track.key_source != "Mixed in Key" and track.key:
            track.librosa_key = track.key
        if track.energy_source != "Mixed in Key" and track.energy:
            track.librosa_energy = track.energy
        
        # Revert to Mixed in Key values
        if track.mixed_in_key_key:
            track.key = track.mixed_in_key_key
        if track.mixed_in_key_energy:
            track.energy = track.mixed_in_key_energy
        
        db.commit()
        db.refresh(track)
        
        logger.info(f"Reverted track {track_id} to Mixed in Key metadata")
        
        return {
            "message": "Successfully reverted to Mixed in Key metadata",
            "track": track.to_dict()
        }
        
    except Exception as e:
        logger.error(f"Error reverting track {track_id} to Mixed in Key: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{track_id}/analysis/ai-data")
async def strip_ai_analysis_data(
    track_id: int,
    components: Optional[List[str]] = Query(
        default=None,
        description="Specific components to remove: 'analysis', 'segments', 'cue_points', 'beat_grid'"
    ),
    db: Session = Depends(get_db)
):
    """
    Strip AI analysis data from track
    If components is None, removes all AI data
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")
        
        removed_components = []
        
        # If no specific components specified, remove all
        if components is None:
            components = ['analysis', 'segments', 'cue_points', 'beat_grid']
        
        # Remove track analysis data and revert metadata
        if 'analysis' in components:
            analysis = db.query(TrackAnalysis).filter(TrackAnalysis.track_id == track_id).first()
            if analysis:
                db.delete(analysis)
                removed_components.append('analysis')

            # Store current AI-derived values as librosa values before reverting
            if track.key_source == "Librosa" and track.key:
                track.librosa_key = track.key
            if track.energy_source == "Librosa" and track.energy:
                track.librosa_energy = track.energy
            if track.bpm_source == "Librosa" and track.bpm:
                track.librosa_bpm = track.bpm

            # Revert to Mixed in Key values if available, otherwise clear
            if track.mixed_in_key_key:
                track.key = track.mixed_in_key_key
                track.key_source = "Mixed in Key"
            else:
                track.key = None
                track.key_source = "Default"

            if track.mixed_in_key_energy:
                track.energy = track.mixed_in_key_energy
                track.energy_source = "Mixed in Key"
            else:
                track.energy = None
                track.energy_source = "Default"

            # Reset BPM source (keep the value but mark source)
            if track.bpm:
                track.bpm_source = "Mixed in Key" if track.mixed_in_key_key else "Default"
        
        # Remove segments
        if 'segments' in components:
            segments = db.query(TrackSegment).filter(TrackSegment.track_id == track_id).all()
            for segment in segments:
                db.delete(segment)
            if segments:
                removed_components.append(f'segments ({len(segments)})')
        
        # Remove cue points
        if 'cue_points' in components:
            cue_points = db.query(CuePoint).filter(CuePoint.track_id == track_id).all()
            for cue_point in cue_points:
                db.delete(cue_point)
            if cue_points:
                removed_components.append(f'cue_points ({len(cue_points)})')
                track.cue_points_count = 0
        
        # Remove beat grid
        if 'beat_grid' in components:
            beat_grid = db.query(BeatGrid).filter(BeatGrid.track_id == track_id).first()
            if beat_grid:
                db.delete(beat_grid)
                removed_components.append('beat_grid')
        
        db.commit()
        db.refresh(track)
        
        logger.info(f"Stripped AI analysis components from track {track_id}: {removed_components}")
        
        return {
            "message": f"Successfully removed AI analysis components: {', '.join(removed_components)}",
            "removed_components": removed_components,
            "track": track.to_dict()
        }
        
    except Exception as e:
        logger.error(f"Error stripping AI analysis from track {track_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{track_id}/analysis/reset-metadata")
async def reset_track_metadata(
    track_id: int,
    reset_to_defaults: bool = Query(default=False, description="Reset to default values instead of clearing"),
    db: Session = Depends(get_db)
):
    """
    Reset track metadata to defaults or clear AI-derived values
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")
        
        # Store current values as librosa if they came from AI analysis
        if track.key_source == "Librosa" and track.key:
            track.librosa_key = track.key
        if track.energy_source == "Librosa" and track.energy:
            track.librosa_energy = track.energy
        
        if reset_to_defaults:
            # Reset to default values
            if not track.mixed_in_key_key:
                track.key = "1A"  # Default key
            if not track.mixed_in_key_energy:
                track.energy = 5  # Default energy
        else:
            # Clear AI-derived values, keep Mixed in Key if available
            if track.key_source == "Librosa":
                track.key = track.mixed_in_key_key or None
            if track.energy_source == "Librosa":
                track.energy = track.mixed_in_key_energy or None
        
        db.commit()
        db.refresh(track)
        
        action = "reset to defaults" if reset_to_defaults else "cleared AI-derived values"
        logger.info(f"Reset metadata for track {track_id}: {action}")
        
        return {
            "message": f"Successfully {action}",
            "track": track.to_dict()
        }
        
    except Exception as e:
        logger.error(f"Error resetting metadata for track {track_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{track_id}/analysis/status")
async def get_analysis_status(
    track_id: int,
    db: Session = Depends(get_db)
):
    """
    Get detailed analysis status for a track
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")
        
        # Get analysis data
        analysis = db.query(TrackAnalysis).filter(TrackAnalysis.track_id == track_id).first()
        segments = db.query(TrackSegment).filter(TrackSegment.track_id == track_id).all()
        cue_points = db.query(CuePoint).filter(CuePoint.track_id == track_id).all()
        beat_grid = db.query(BeatGrid).filter(BeatGrid.track_id == track_id).first()
        
        # Calculate completeness
        completeness = 0
        if track.bpm and track.bpm > 0:
            completeness += 20
        if track.key and track.key != '1A':
            completeness += 20
        if track.energy and track.energy > 0:
            completeness += 15
        if analysis:
            completeness += 15
        if beat_grid:
            completeness += 10
        if segments:
            completeness += 10
        if cue_points:
            completeness += 10
        
        status = {
            "track_id": track_id,
            "completeness": completeness,
            "has_analysis": analysis is not None,
            "has_beat_grid": beat_grid is not None,
            "segments_count": len(segments),
            "cue_points_count": len(cue_points),
            "key_source": track.key_source or "Default",
            "energy_source": track.energy_source or "Default",
            "bpm_source": "Mixed in Key" if track.mixed_in_key_key else ("Librosa" if analysis else "Default"),
            "mixed_in_key_available": bool(track.mixed_in_key_key or track.mixed_in_key_energy),
            "analysis_data": {
                "analysis_id": analysis.id if analysis else None,
                "analysis_status": analysis.status if analysis else None,
                "beat_grid_id": beat_grid.id if beat_grid else None,
                "beat_grid_confidence": beat_grid.confidence if beat_grid else None,
                "segments": [
                    {
                        "id": seg.id,
                        "type": seg.type,
                        "start_time": seg.start_time,
                        "end_time": seg.end_time,
                        "confidence": seg.confidence
                    } for seg in segments
                ],
                "cue_points": [
                    {
                        "id": cp.id,
                        "time": cp.time,
                        "label": cp.label,
                        "type": cp.type,
                        "confidence": cp.confidence
                    } for cp in cue_points
                ]
            }
        }
        
        return status

    except Exception as e:
        logger.error(f"Error getting analysis status for track {track_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{track_id}/analysis/full-data")
async def get_full_analysis_data(
    track_id: int,
    db: Session = Depends(get_db)
):
    """
    Get the complete analysis data for a track including all librosa analysis results
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")

        # Get the analysis record
        analysis = db.query(TrackAnalysis).filter(TrackAnalysis.track_id == track_id).first()

        if not analysis or not analysis.analysis_data:
            return {
                "track_id": track_id,
                "has_analysis": False,
                "message": "No analysis data available"
            }

        # Return the full analysis data
        return {
            "track_id": track_id,
            "has_analysis": True,
            "analysis_id": analysis.id,
            "status": analysis.status,
            "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
            "updated_at": analysis.updated_at.isoformat() if analysis.updated_at else None,
            "analysis_data": analysis.analysis_data  # This contains all the rich librosa data
        }

    except Exception as e:
        logger.error(f"Error getting full analysis data for track {track_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{track_id}/analysis/extract-mixed-in-key")
async def extract_mixed_in_key_metadata(
    track_id: int,
    db: Session = Depends(get_db)
):
    """
    Extract ONLY Mixed in Key metadata from track (fast, no librosa analysis)
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")

        # Initialize audio analyzer
        analyzer = AudioAnalyzer(db)

        # Extract Mixed in Key metadata only
        result = await analyzer.extract_mixed_in_key_only(track_id)

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        logger.info(f"Mixed in Key extraction completed for track {track_id}")

        return {
            "message": "Mixed in Key metadata extraction completed successfully",
            "track_id": track_id,
            "status": "completed",
            "results": result
        }

    except Exception as e:
        logger.error(f"Error extracting Mixed in Key metadata for track {track_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{track_id}/analysis/analyze-with-librosa")
async def analyze_with_librosa(
    track_id: int,
    db: Session = Depends(get_db)
):
    """
    Analyze track with enhanced librosa analysis (CPU intensive)
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")

        # Initialize audio analyzer
        analyzer = AudioAnalyzer(db)

        # Perform librosa analysis
        result = await analyzer.analyze_track(track_id)

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        logger.info(f"Librosa analysis completed for track {track_id}")

        return {
            "message": "Librosa analysis completed successfully",
            "track_id": track_id,
            "status": "completed",
            "results": result
        }

    except Exception as e:
        logger.error(f"Error analyzing track with librosa {track_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{track_id}/analysis/selective-reanalyze")
async def selective_reanalyze(
    track_id: int,
    components: List[str] = Query(
        description="Components to re-analyze: 'bpm', 'key', 'energy', 'segments', 'cue_points', 'beat_grid'"
    ),
    db: Session = Depends(get_db)
):
    """
    Re-analyze specific components of a track
    """
    try:
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise HTTPException(status_code=404, detail="Track not found")

        # Initialize audio analyzer
        analyzer = AudioAnalyzer(db)

        # TODO: Implement selective re-analysis
        # This would require modifying the AudioAnalyzer to support selective analysis

        logger.info(f"Selective re-analysis requested for track {track_id}: {components}")

        return {
            "message": f"Selective re-analysis started for components: {', '.join(components)}",
            "track_id": track_id,
            "components": components,
            "status": "started"
        }

    except Exception as e:
        logger.error(f"Error starting selective re-analysis for track {track_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
