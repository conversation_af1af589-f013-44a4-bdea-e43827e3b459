"""
Application settings management routes.
Handles user preferences and application configuration settings.
"""

import logging
import json
import os
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from sqlalchemy.orm import Session

from backend.dependencies import get_db_fastapi
from backend.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(tags=["app-settings"])

# Settings file path
SETTINGS_FILE = os.path.join(settings.BASE_DIR, "data", "app_settings.json")

class AppSettingsModel(BaseModel):
    """Application settings model"""
    auto_analyze_on_import: bool = False
    auto_analyze_batch_size: int = 5
    auto_analyze_timeout: int = 300
    hide_timeline_tracks_in_selector: bool = True  # Default to True for better UX

class AppSettingsUpdateRequest(BaseModel):
    """Request model for updating app settings"""
    auto_analyze_on_import: bool = None
    hide_timeline_tracks_in_selector: bool = None

def load_app_settings() -> AppSettingsModel:
    """Load application settings from file"""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r') as f:
                data = json.load(f)
                return AppSettingsModel(**data)
    except Exception as e:
        logger.warning(f"Error loading app settings: {e}")
    
    # Return default settings
    return AppSettingsModel()

def save_app_settings(app_settings: AppSettingsModel) -> None:
    """Save application settings to file"""
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(SETTINGS_FILE), exist_ok=True)
        
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(app_settings.model_dump(), f, indent=2)
        
        # Update runtime settings
        settings.AUTO_ANALYZE_ON_IMPORT = app_settings.auto_analyze_on_import
        settings.AUTO_ANALYZE_BATCH_SIZE = app_settings.auto_analyze_batch_size
        settings.AUTO_ANALYZE_TIMEOUT = app_settings.auto_analyze_timeout
        
        logger.info(f"App settings saved: auto_analyze_on_import={app_settings.auto_analyze_on_import}")
    except Exception as e:
        logger.error(f"Error saving app settings: {e}")
        raise HTTPException(status_code=500, detail=f"Error saving settings: {e}")

# Load settings on module import
try:
    current_settings = load_app_settings()
    settings.AUTO_ANALYZE_ON_IMPORT = current_settings.auto_analyze_on_import
    settings.AUTO_ANALYZE_BATCH_SIZE = current_settings.auto_analyze_batch_size
    settings.AUTO_ANALYZE_TIMEOUT = current_settings.auto_analyze_timeout
    logger.info(f"Loaded app settings: auto_analyze_on_import={current_settings.auto_analyze_on_import}")
except Exception as e:
    logger.warning(f"Error loading app settings on startup: {e}")

@router.get("/settings", response_model=AppSettingsModel)
async def get_app_settings():
    """Get current application settings"""
    current_settings = load_app_settings()
    return AppSettingsModel(
        auto_analyze_on_import=settings.AUTO_ANALYZE_ON_IMPORT,
        auto_analyze_batch_size=settings.AUTO_ANALYZE_BATCH_SIZE,
        auto_analyze_timeout=settings.AUTO_ANALYZE_TIMEOUT,
        hide_timeline_tracks_in_selector=current_settings.hide_timeline_tracks_in_selector
    )

@router.put("/settings", response_model=AppSettingsModel)
async def update_app_settings(
    update_request: AppSettingsUpdateRequest,
    db: Session = Depends(get_db_fastapi)
):
    """Update application settings"""
    try:
        # Get current settings
        current_settings = load_app_settings()

        # Update with new values (only if provided)
        if update_request.auto_analyze_on_import is not None:
            current_settings.auto_analyze_on_import = update_request.auto_analyze_on_import
        if update_request.hide_timeline_tracks_in_selector is not None:
            current_settings.hide_timeline_tracks_in_selector = update_request.hide_timeline_tracks_in_selector
        
        # Save updated settings
        save_app_settings(current_settings)
        
        logger.info(f"Updated app settings: auto_analyze_on_import={update_request.auto_analyze_on_import}")
        
        return current_settings
    except Exception as e:
        logger.error(f"Error updating app settings: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating settings: {e}")

@router.post("/settings/reset")
async def reset_app_settings():
    """Reset application settings to defaults"""
    try:
        default_settings = AppSettingsModel()
        save_app_settings(default_settings)
        
        logger.info("App settings reset to defaults")
        
        return {"message": "Settings reset to defaults", "settings": default_settings}
    except Exception as e:
        logger.error(f"Error resetting app settings: {e}")
        raise HTTPException(status_code=500, detail=f"Error resetting settings: {e}")
