"""
Style compatibility analysis routes.
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.dependencies import get_db_fastapi
from backend.models.track import Track
from backend.models.mix_style import MixStyle
from backend.services.compatibility_service import CompatibilityService

logger = logging.getLogger(__name__)
router = APIRouter()

class StyleCompatibilityRequest(BaseModel):
    """Request model for style compatibility analysis."""
    track_ids: List[str]

class StyleCompatibilityResponse(BaseModel):
    """Response model for style compatibility analysis."""
    compatibility_score: float
    energy_score: float
    bpm_score: float
    key_score: float
    compatible_track_count: int
    total_track_count: int
    sample_tracks: List[Dict[str, Any]]
    recommendations: List[str]

@router.post("/{style_id}/compatibility", response_model=StyleCompatibilityResponse)
async def analyze_style_compatibility(
    style_id: str,
    request: StyleCompatibilityRequest = Body(...),
    db: Session = Depends(get_db_fastapi)
):
    """
    Analyze compatibility between a mix style and a set of tracks.

    Args:
        style_id: ID of the mix style
        request: Request with track IDs
        db: Database session

    Returns:
        Compatibility analysis results
    """
    try:
        # Get the style
        style = db.query(MixStyle).filter(MixStyle.id == style_id).first()
        if not style:
            raise HTTPException(status_code=404, detail=f"Style with ID {style_id} not found")

        # Get the tracks
        track_ids = request.track_ids
        tracks = db.query(Track).filter(Track.id.in_(track_ids)).all()
        if not tracks:
            raise HTTPException(status_code=404, detail="No tracks found with the provided IDs")

        # Initialize compatibility service
        compatibility_service = CompatibilityService(db)

        # Analyze compatibility
        total_track_count = len(tracks)
        compatible_track_count = 0
        compatible_tracks = []

        # Get style parameters
        style_min_bpm = style.min_bpm
        style_max_bpm = style.max_bpm

        # Check each track for compatibility
        for track in tracks:
            # Check BPM compatibility
            bpm_compatible = style_min_bpm <= track.bpm <= style_max_bpm

            # Check energy compatibility (simplified)
            energy_compatible = True  # Simplified check

            # Check key compatibility (simplified)
            key_compatible = True  # Simplified check

            # Track is compatible if it meets all criteria
            if bpm_compatible and energy_compatible and key_compatible:
                compatible_track_count += 1
                compatible_tracks.append(track)

        # Calculate scores
        if total_track_count > 0:
            compatibility_score = compatible_track_count / total_track_count

            # Calculate BPM score
            bpm_score = sum(1 for track in tracks if style_min_bpm <= track.bpm <= style_max_bpm) / total_track_count

            # Calculate energy score (simplified)
            energy_score = 0.7  # Placeholder

            # Calculate key score (simplified)
            key_score = 0.8  # Placeholder
        else:
            compatibility_score = 0.0
            energy_score = 0.0
            bpm_score = 0.0
            key_score = 0.0

        # Get sample tracks (up to 5)
        sample_tracks = compatible_tracks[:5]
        sample_tracks_dict = [
            {
                "id": str(track.id),
                "title": track.title,
                "artist": track.artist,
                "bpm": track.bpm,
                "key": track.key
            }
            for track in sample_tracks
        ]

        # Generate recommendations
        recommendations = []
        if bpm_score < 0.5:
            recommendations.append(f"Your collection's BPM range doesn't align well with this style's range ({style_min_bpm}-{style_max_bpm})")

        if key_score < 0.5:
            recommendations.append("Consider adding more tracks with compatible keys to increase compatibility")

        if energy_score < 0.5:
            recommendations.append("The energy pattern may not work well with your collection's energy distribution")

        return {
            "compatibility_score": compatibility_score,
            "energy_score": energy_score,
            "bpm_score": bpm_score,
            "key_score": key_score,
            "compatible_track_count": compatible_track_count,
            "total_track_count": total_track_count,
            "sample_tracks": sample_tracks_dict,
            "recommendations": recommendations
        }

    except Exception as e:
        logger.error(f"Error analyzing style compatibility: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
