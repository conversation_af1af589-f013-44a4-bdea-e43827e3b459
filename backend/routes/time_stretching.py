"""
Time Stretching API Routes for Phase 2 Beatmatching Implementation.

This module provides FastAPI endpoints for high-quality time stretching
using the EnhancedAudioProcessor service.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any
import logging
from pathlib import Path

from backend.services.enhanced_audio_processor import EnhancedAudioProcessor
from backend.utils.stretched_audio_cache import StretchedAudioCache
from backend.repositories.track_repository import TrackRepository
from backend.dependencies import get_track_repository, get_db_fastapi as get_db
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/time-stretching",
    tags=["Time Stretching"],
)

# Pydantic models for request/response
class StretchRequest(BaseModel):
    track_id: int = Field(..., description="ID of the track to stretch")
    target_bpm: float = Field(..., gt=0, le=300, description="Target BPM (1-300)")
    quality_mode: str = Field("high", description="Quality mode: high, medium, fast")
    
    class Config:
        schema_extra = {
            "example": {
                "track_id": 1,
                "target_bpm": 125.0,
                "quality_mode": "high"
            }
        }

class StretchValidationRequest(BaseModel):
    original_bpm: float = Field(..., gt=0, le=300, description="Original BPM")
    target_bpm: float = Field(..., gt=0, le=300, description="Target BPM")
    
    class Config:
        schema_extra = {
            "example": {
                "original_bpm": 120.0,
                "target_bpm": 125.0
            }
        }

class StretchResponse(BaseModel):
    success: bool
    track_id: int
    original_bpm: float
    target_bpm: float
    time_factor: float
    quality_metrics: Dict[str, Any]
    cache_key: str
    processing_time: float
    message: str

class ValidationResponse(BaseModel):
    valid: bool
    time_factor: float
    stretch_percentage: float
    quality_prediction: str
    recommendation: str

class CacheInfoResponse(BaseModel):
    cache_directory: str
    total_files: int
    total_size_gb: float
    usage_percentage: float
    cleanup_needed: bool

# Dependencies
def get_enhanced_audio_processor(
    track_repo: TrackRepository = Depends(get_track_repository),
    db: Session = Depends(get_db)
) -> EnhancedAudioProcessor:
    """Get EnhancedAudioProcessor instance."""
    return EnhancedAudioProcessor(track_repo, db)

def get_cache_manager() -> StretchedAudioCache:
    """Get StretchedAudioCache instance."""
    return StretchedAudioCache()

@router.post("/stretch", response_model=StretchResponse, status_code=status.HTTP_200_OK)
async def stretch_track(
    request: StretchRequest,
    background_tasks: BackgroundTasks,
    processor: EnhancedAudioProcessor = Depends(get_enhanced_audio_processor)
):
    """
    Stretch a track to a target BPM using high-quality time stretching.
    
    This endpoint processes the audio in the background and returns the result
    with quality metrics and cache information.
    """
    logger.info(f"🎵 Time stretching request: Track {request.track_id} to {request.target_bpm} BPM")
    
    try:
        # Check if python-stretch is available
        if not processor.is_stretch_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Time stretching service unavailable. python-stretch library not installed."
            )
        
        # Validate quality mode
        if request.quality_mode not in ["high", "medium", "fast"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid quality_mode. Must be 'high', 'medium', or 'fast'."
            )
        
        # Process the stretching
        result = await processor.stretch_track_to_bpm(
            request.track_id,
            request.target_bpm,
            request.quality_mode
        )
        
        # Return success response
        return StretchResponse(
            success=True,
            track_id=result["track_id"],
            original_bpm=result["original_bpm"],
            target_bpm=result["target_bpm"],
            time_factor=result["time_factor"],
            quality_metrics=result["quality_metrics"],
            cache_key=result["cache_key"],
            processing_time=result["processing_time"],
            message=f"Successfully stretched track {request.track_id} to {request.target_bpm} BPM"
        )
        
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Audio file not found for track {request.track_id}"
        )
    except Exception as e:
        logger.error(f"Time stretching failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Time stretching failed: {str(e)}"
        )

@router.post("/validate", response_model=ValidationResponse, status_code=status.HTTP_200_OK)
async def validate_stretch_ratio(
    request: StretchValidationRequest,
    processor: EnhancedAudioProcessor = Depends(get_enhanced_audio_processor)
):
    """
    Validate if a stretch ratio is acceptable for quality.
    
    This endpoint helps users understand the quality implications
    of stretching between different BPMs before processing.
    """
    try:
        result = await processor.validate_stretch_ratio(
            request.original_bpm,
            request.target_bpm
        )
        
        return ValidationResponse(**result)
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )

@router.get("/stream/{cache_key}")
async def stream_stretched_audio(
    cache_key: str,
    cache_manager: StretchedAudioCache = Depends(get_cache_manager)
):
    """
    Stream a stretched audio file from cache.
    
    Returns the processed audio file for playback in the frontend.
    """
    try:
        # Check if cache entry exists
        if not cache_manager.cache_exists(cache_key):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Stretched audio not found for cache key: {cache_key}"
            )
        
        # Get file paths
        paths = cache_manager.get_cache_file_paths(cache_key)
        audio_path = paths["audio"]
        
        if not audio_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cached audio file not found on disk"
            )
        
        logger.info(f"Streaming stretched audio: {cache_key}")
        
        # Return the audio file
        return FileResponse(
            path=str(audio_path),
            media_type="audio/wav",
            filename=f"stretched_{cache_key}.wav"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error streaming stretched audio: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error streaming stretched audio"
        )

@router.get("/cache/info", response_model=CacheInfoResponse, status_code=status.HTTP_200_OK)
async def get_cache_info(
    cache_manager: StretchedAudioCache = Depends(get_cache_manager)
):
    """
    Get information about the stretched audio cache.
    
    Returns cache statistics including size, file count, and usage.
    """
    try:
        cache_info = await cache_manager.get_cache_info()
        
        if "error" in cache_info:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get cache info: {cache_info['error']}"
            )
        
        return CacheInfoResponse(
            cache_directory=cache_info["cache_directory"],
            total_files=cache_info["total_files"],
            total_size_gb=cache_info["total_size_gb"],
            usage_percentage=cache_info["usage_percentage"],
            cleanup_needed=cache_info["cleanup_needed"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting cache info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting cache information"
        )

@router.post("/cache/cleanup", status_code=status.HTTP_200_OK)
async def cleanup_cache(
    force: bool = False,
    cache_manager: StretchedAudioCache = Depends(get_cache_manager)
):
    """
    Clean up old cached files.
    
    Removes old or excess cached files to free up space.
    """
    try:
        result = await cache_manager.cleanup_cache(force=force)
        
        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Cache cleanup failed: {result['error']}"
            )
        
        return {
            "success": True,
            "cleaned": result["cleaned"],
            "removed_files": result.get("removed_files", 0),
            "removed_size_mb": result.get("removed_size_mb", 0),
            "message": "Cache cleanup completed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cache cleanup error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Cache cleanup failed"
        )

@router.get("/cache/statistics", status_code=status.HTTP_200_OK)
async def get_cache_statistics(
    cache_manager: StretchedAudioCache = Depends(get_cache_manager)
):
    """
    Get detailed cache statistics.
    
    Returns comprehensive statistics about cached files including
    quality distribution, BPM ranges, and usage patterns.
    """
    try:
        stats = await cache_manager.get_cache_statistics()
        
        if "error" in stats:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get cache statistics: {stats['error']}"
            )
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting cache statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting cache statistics"
        )

@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check(
    processor: EnhancedAudioProcessor = Depends(get_enhanced_audio_processor)
):
    """
    Health check for the time stretching service.
    
    Returns the status of the time stretching service and dependencies.
    """
    try:
        return {
            "status": "healthy",
            "python_stretch_available": processor.is_stretch_available(),
            "service": "time_stretching",
            "version": "2.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Time stretching service unhealthy"
        )
