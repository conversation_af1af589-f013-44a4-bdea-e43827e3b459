"""
MCP Server Registry API endpoints.

This module provides API endpoints for managing MCP servers.
"""

from fastapi import APIRouter, Depends, HTTPException, Body, Query
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import logging

from backend.services.ai.mcp_registry import mcp_registry
from backend.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ai/mcp/registry",
    tags=["AI MCP Registry"]
)

# Models for request and response
class ServerConfig(BaseModel):
    """Model for a server configuration."""
    server_id: Optional[str] = None
    name: str
    description: str
    host: str
    port: int
    transport: str
    enabled: bool
    server_command: Optional[List[str]] = None
    tool_prefixes: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class ServerStatus(BaseModel):
    """Model for server status."""
    server_id: str
    name: str
    description: str
    host: str
    port: int
    transport: str
    enabled: bool
    server_command: Optional[List[str]] = None
    tool_prefixes: List[str]
    metadata: Dict[str, Any]
    created_at: float
    updated_at: float
    last_connected_at: Optional[float] = None
    status: str
    error: Optional[str] = None

class ServerUpdate(BaseModel):
    """Model for updating a server configuration."""
    name: Optional[str] = None
    description: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    transport: Optional[str] = None
    enabled: Optional[bool] = None
    server_command: Optional[List[str]] = None
    tool_prefixes: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

@router.get("/servers", response_model=List[ServerStatus])
async def get_servers():
    """
    Get all server configurations.
    
    Returns:
        List of server configurations
    """
    try:
        servers = mcp_registry.get_servers()
        return servers
    except Exception as e:
        logger.error(f"Error getting MCP servers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/servers/{server_id}", response_model=ServerStatus)
async def get_server(server_id: str):
    """
    Get a server configuration.
    
    Args:
        server_id: Server ID
        
    Returns:
        Server configuration
    """
    try:
        server = mcp_registry.get_server(server_id)
        if not server:
            raise HTTPException(status_code=404, detail=f"Server {server_id} not found")
        return server
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers", response_model=ServerStatus)
async def add_server(config: ServerConfig):
    """
    Add a new server configuration.
    
    Args:
        config: Server configuration
        
    Returns:
        Added server configuration
    """
    try:
        server = mcp_registry.add_server(config.dict())
        return server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding MCP server: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/servers/{server_id}", response_model=ServerStatus)
async def update_server(server_id: str, config: ServerUpdate):
    """
    Update a server configuration.
    
    Args:
        server_id: Server ID
        config: Updated server configuration
        
    Returns:
        Updated server configuration
    """
    try:
        server = mcp_registry.update_server(server_id, config.dict(exclude_unset=True))
        return server
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/servers/{server_id}", response_model=Dict[str, bool])
async def delete_server(server_id: str):
    """
    Delete a server configuration.
    
    Args:
        server_id: Server ID
        
    Returns:
        Whether the server was deleted
    """
    try:
        deleted = mcp_registry.delete_server(server_id)
        if not deleted:
            raise HTTPException(status_code=404, detail=f"Server {server_id} not found")
        return {"deleted": True}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_id}/enable", response_model=ServerStatus)
async def enable_server(server_id: str):
    """
    Enable a server.
    
    Args:
        server_id: Server ID
        
    Returns:
        Updated server configuration
    """
    try:
        server = mcp_registry.enable_server(server_id)
        return server
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error enabling MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_id}/disable", response_model=ServerStatus)
async def disable_server(server_id: str):
    """
    Disable a server.
    
    Args:
        server_id: Server ID
        
    Returns:
        Updated server configuration
    """
    try:
        server = mcp_registry.disable_server(server_id)
        return server
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error disabling MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_id}/connect", response_model=ServerStatus)
async def connect_server(server_id: str):
    """
    Connect to a server.
    
    Args:
        server_id: Server ID
        
    Returns:
        Server status
    """
    try:
        server = await mcp_registry.connect_server(server_id)
        return server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error connecting to MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_id}/disconnect", response_model=ServerStatus)
async def disconnect_server(server_id: str):
    """
    Disconnect from a server.
    
    Args:
        server_id: Server ID
        
    Returns:
        Server status
    """
    try:
        server = await mcp_registry.disconnect_server(server_id)
        return server
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error disconnecting from MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/servers/{server_id}/tools")
async def get_server_tools(server_id: str):
    """
    Get tools from a server.
    
    Args:
        server_id: Server ID
        
    Returns:
        List of tools
    """
    try:
        tools = await mcp_registry.get_server_tools(server_id)
        return tools
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting tools from MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_id}/tools/{tool_name}")
async def call_server_tool(
    server_id: str,
    tool_name: str,
    parameters: Dict[str, Any] = Body(...),
    use_cache: bool = Query(True, description="Whether to use the cache")
):
    """
    Call a tool on a server.
    
    Args:
        server_id: Server ID
        tool_name: Tool name
        parameters: Tool parameters
        use_cache: Whether to use the cache
        
    Returns:
        Tool result
    """
    try:
        result = await mcp_registry.call_server_tool(
            server_id,
            tool_name,
            parameters,
            use_cache=use_cache
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calling tool {tool_name} on MCP server {server_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
