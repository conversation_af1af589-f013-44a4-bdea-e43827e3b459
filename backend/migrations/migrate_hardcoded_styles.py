#!/usr/bin/env python3
"""
Migration script to import hardcoded mix styles from styles.py into the database.
This creates built-in styles that can be used by all generators.
"""

import sqlite3
import json
from datetime import datetime

# Database path - relative to script location
import os
DB_PATH = os.path.join(os.path.dirname(__file__), "..", "db", "harmonymix.db")

# Hardcoded styles data (copied from styles.py to avoid import issues)
AVAILABLE_MIX_STYLES_DATA = {
    'downtempo': {
        'name': 'Downtempo Mix',
        'description': 'Relaxed, low-energy flow suitable for starting or ending a night.',
        'icon': '🧘',
        'energy_pattern': [4, 5, 4, 5, 4, 5],
        'key_rules': ['PERFECT', 'ADJACENT', 'SAME_KEY_DIFFERENT_MODE'],
        'constraints': {
            'bpm_ranges': [(-float('inf'), 92), (90, 98), (96, 102)],
            'min_energy': 4, 'max_energy': 5, 'target_low_energy_ratio': 0.7,
            'max_consecutive_high': 2, 'energy_priority': 'stable', 'max_energy_jump': 1,
            'plateau_penalty': -2.0, 'end_low_bonus': 2.0, 'end_high_penalty': -2.0,
            'phase_modifiers': {'final_third_low': 1.5, 'final_quarter_high': -1.5},
            'strict_bpm_limits': False, 'bpm_increment': 2.0, 'preferred_energy_start': 4
        }
    },
    'deep_house': {
        'name': 'Deep/Slow House Mix',
        'description': 'Groovy and smooth, suitable for early hours or lounge settings.',
        'icon': '🌃',
        'energy_pattern': [4, 5, 5, 4, 5, 6, 5, 5, 4],
        'key_rules': ['PERFECT', 'ADJACENT', 'SAME_KEY_DIFFERENT_MODE', 'DIAGONAL_MIX'],
        'constraints': {
            'bpm_ranges': [(94, 100), (98, 104), (102, 108)],
            'min_energy': 4, 'max_energy': 6, 'target_mid_energy_ratio': 0.4,
            'max_high_energy_ratio': 0.3, 'plateau_penalty': -2.5,
            'consecutive_high_penalty': -1.5, 'strategic_high_bonus': 1.5,
            'phase_modifiers': {'mid_energy_bonus': 1.0, 'consecutive_high': -1.5},
            'strict_bpm_limits': True, 'bpm_increment': 3.0, 'preferred_energy_start': 4
        }
    },
    'club': {
        'name': 'Club Mix',
        'description': 'Energetic and dancefloor-focused with harmonic progression.',
        'icon': '🕺',
        'energy_pattern': [5, 6, 6, 7, 6, 7, 7, 6, 7],
        'key_rules': ['PERFECT', 'ADJACENT', 'ENERGY_BOOST_MIX', 'PERFECT_FIFTH'],
        'constraints': {
            'bpm_ranges': [(118, 124), (122, 128)],
            'min_energy': 5, 'max_energy': 7, 'target_high_energy_ratio': 0.4,
            'final_quarter_alternating': True, 'plateau_penalty': -2.0,
            'second_half_high_bonus': 1.5, 'final_quarter_low_penalty': -2.0,
            'strict_bpm_limits': True, 'bpm_increment': 3.0, 'preferred_energy_start': 5
        }
    },
    'peak': {
        'name': 'Peak Hour Mix',
        'description': 'High energy throughout, designed for the main part of the night.',
        'icon': '🔥',
        'energy_pattern': [6, 7, 6, 7, 7, 6, 7, 7, 6, 7],
        'key_rules': ['PERFECT', 'ADJACENT', 'ENERGY_BOOST_MIX', 'PERFECT_FIFTH'],
        'constraints': {
            'bpm_ranges': [(122, 128), (126, 130), (130, float('inf'))],
            'min_energy': 6, 'max_energy': 7, 'target_high_energy_ratio': 0.5,
            'max_consecutive_low': 2, 'final_high_energy_tracks': 4,
            'plateau_penalty': -3.0, 'additional_plateau_penalty': -2.0,
            'end_high_bonus': 2.0, 'end_low_penalty': -2.5,
            'final_third_high_bonus': 1.5, 'final_quarter_low_penalty': -2.0,
            'strict_bpm_limits': True, 'bpm_increment': 3.0, 'preferred_energy_start': 6
        }
    },
    'progressive': {
        'name': 'Progressive Mix',
        'description': 'Builds energy steadily with harmonic complexity.',
        'icon': '🚀',
        'energy_pattern': [4, 5, 5, 6, 6, 7],
        'key_rules': ['PERFECT', 'ADJACENT', 'DIAGONAL_MIX', 'PERFECT_FIFTH'],
        'constraints': {
            'bpm_ranges': [(106, 112), (110, 116), (114, 120), (118, 124), (122, 128)],
            'min_energy': 4, 'max_energy': 7, 'phase_count': 5,
            'regression_penalty': -3.0, 'phase_match_bonus': 2.0, 'progression_bonus': 1.5,
            'phase_mismatch_penalty': -2.5, 'strict_bpm_limits': True, 'bpm_increment': 3.0,
            'preferred_energy_start': 4
        }
    },
    'lounge': {
        'name': 'Lounge/Bar Mix',
        'description': 'Chill and sophisticated vibes, maintaining a steady mood.',
        'icon': '🍸',
        'energy_pattern': [4, 5, 4, 5, 6, 5, 4, 5, 6, 5],
        'key_rules': ['PERFECT', 'ADJACENT'],
        'constraints': {
            'bpm_ranges': [(102, 108), (106, 112), (110, 116)],
            'min_energy': 4, 'max_energy': 6, 'target_mid_energy_ratio': 0.5,
            'max_high_energy_ratio': 0.25, 'plateau_penalty': -2.0,
            'aggressive_change_penalty': -2.0, 'smooth_transition_bonus': 1.0,
            'venue_energy_bonus': 1.5, 'strict_bpm_limits': True, 'bpm_increment': 3.0,
            'preferred_energy_start': 4
        }
    },
    'warmup': {
        'name': 'Warm-up Set',
        'description': 'Gradually builds energy, preparing the dance floor.',
        'icon': '📈',
        'energy_pattern': [4, 5, 5, 6, 5, 6, 5, 6, 6, 5],
        'key_rules': ['PERFECT', 'ADJACENT', 'SAME_KEY_DIFFERENT_MODE'],
        'constraints': {
            'bpm_ranges': [(106, 112), (110, 116), (114, 120)],
            'min_energy': 4, 'max_energy': 6,
            'phase_thresholds': {'first_third': 0.3, 'middle': 0.7},
            'plateau_penalty': -2.0, 'phase_match_bonus': 1.5, 'progression_bonus': 1.0,
            'regression_penalty': -2.0, 'strict_bpm_limits': True, 'bpm_increment': 3.0,
            'preferred_energy_start': 4
        }
    },
    'underground': {
        'name': 'Underground Mix',
        'description': 'Deeper, driving rhythms with consistent energy.',
        'icon': '🚇',
        'energy_pattern': [5, 6, 5, 6, 7, 6, 7, 6, 5, 6],
        'key_rules': ['PERFECT', 'ADJACENT', 'SAME_KEY_DIFFERENT_MODE', 'DIAGONAL_MIX', 'ENERGY_DROP_MIX'],
        'constraints': {
            'bpm_ranges': [(114, 120), (118, 124)],
            'min_energy': 5, 'max_energy': 7, 'target_high_energy_ratio': 0.3,
            'max_consecutive_high': 2, 'plateau_penalty': -2.0,
            'groove_maintenance_bonus': 1.5, 'strategic_high_bonus': 1.0,
            'groove_disruption_penalty': -2.0, 'strict_bpm_limits': True, 'bpm_increment': 3.0,
            'preferred_energy_start': 5
        }
    }
}

def migrate_hardcoded_styles():
    """
    Import all hardcoded styles from AVAILABLE_MIX_STYLES_DATA into the database.
    """
    print("Starting migration of hardcoded mix styles to database...")

    # Connect to SQLite database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        migrated_count = 0
        skipped_count = 0

        for style_id, style_data in AVAILABLE_MIX_STYLES_DATA.items():
            print(f"\nProcessing style: {style_id} - {style_data['name']}")

            # Check if style already exists
            cursor.execute("SELECT id FROM mix_styles WHERE style_id = ?", (style_id,))
            existing_style = cursor.fetchone()
            if existing_style:
                print(f"  ⚠️  Style '{style_id}' already exists in database, skipping...")
                skipped_count += 1
                continue

            # Prepare style data for database
            try:
                # Extract BPM range from constraints
                min_bpm = 120
                max_bpm = 130
                if 'constraints' in style_data and 'bpm_ranges' in style_data['constraints']:
                    bpm_ranges = style_data['constraints']['bpm_ranges']
                    if bpm_ranges and len(bpm_ranges) > 0:
                        # Use the first BPM range for min/max
                        first_range = bpm_ranges[0]
                        min_bpm = first_range[0] if first_range[0] != float('-inf') else 80
                        max_bpm = first_range[1] if first_range[1] != float('inf') else 150

                # Insert new mix style
                cursor.execute("""
                    INSERT INTO mix_styles (
                        style_id, name, description, energy_pattern, min_bpm, max_bpm,
                        key_rules, constraints, generator_types, icon, is_custom,
                        version, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    style_id,
                    style_data['name'],
                    style_data.get('description', ''),
                    json.dumps(style_data.get('energy_pattern', [])),
                    min_bpm,
                    max_bpm,
                    json.dumps(style_data.get('key_rules', [])),
                    json.dumps(style_data.get('constraints', {})),
                    json.dumps(['smart']),  # These styles are for smart generator
                    style_data.get('icon', '🎵'),
                    0,  # is_custom = False (built-in style)
                    "1.0.0",
                    datetime.utcnow().isoformat(),
                    datetime.utcnow().isoformat()
                ))

                conn.commit()
                print(f"  ✅ Successfully migrated style '{style_id}'")
                migrated_count += 1

            except Exception as e:
                print(f"  ❌ Error migrating style '{style_id}': {str(e)}")
                conn.rollback()
                continue

        print(f"\n🎉 Migration completed!")
        print(f"   Migrated: {migrated_count} styles")
        print(f"   Skipped: {skipped_count} styles (already existed)")
        print(f"   Total processed: {len(AVAILABLE_MIX_STYLES_DATA)} styles")

    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        conn.rollback()
        raise
    finally:
        conn.close()

def verify_migration():
    """
    Verify that all styles were migrated correctly.
    """
    print("\nVerifying migration...")

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # Count total styles in database
        cursor.execute("SELECT COUNT(*) FROM mix_styles")
        total_styles = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM mix_styles WHERE is_custom = 0")
        built_in_styles = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM mix_styles WHERE is_custom = 1")
        custom_styles = cursor.fetchone()[0]

        print(f"Database contains:")
        print(f"  Total styles: {total_styles}")
        print(f"  Built-in styles: {built_in_styles}")
        print(f"  Custom styles: {custom_styles}")

        # Check if all hardcoded styles are present
        missing_styles = []
        for style_id in AVAILABLE_MIX_STYLES_DATA.keys():
            cursor.execute("SELECT id FROM mix_styles WHERE style_id = ?", (style_id,))
            style = cursor.fetchone()
            if not style:
                missing_styles.append(style_id)

        if missing_styles:
            print(f"⚠️  Missing styles: {missing_styles}")
        else:
            print("✅ All hardcoded styles are present in database")

    finally:
        conn.close()

if __name__ == "__main__":
    print("Mix Styles Migration Tool")
    print("=" * 50)
    
    # Run migration
    migrate_hardcoded_styles()
    
    # Verify results
    verify_migration()
    
    print("\n✨ Migration process completed!")
