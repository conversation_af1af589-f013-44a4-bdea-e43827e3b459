"""
Add source tracking fields to tracks table

This migration adds fields to track the source of BPM, key, and energy values,
as well as storing librosa BPM results separately.
"""

import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def run_migration():
    """Add source tracking fields to tracks table"""
    
    # Get database path
    db_path = Path(__file__).parent.parent / "db" / "harmonymix.db"
    
    if not db_path.exists():
        logger.warning(f"Database not found at {db_path}")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(tracks)")
        columns = [column[1] for column in cursor.fetchall()]
        
        migrations_needed = []
        
        # Check which columns need to be added
        if 'librosa_bpm' not in columns:
            migrations_needed.append("ALTER TABLE tracks ADD COLUMN librosa_bpm REAL")
            
        if 'key_source' not in columns:
            migrations_needed.append("ALTER TABLE tracks ADD COLUMN key_source TEXT")
            
        if 'energy_source' not in columns:
            migrations_needed.append("ALTER TABLE tracks ADD COLUMN energy_source TEXT")
            
        if 'bpm_source' not in columns:
            migrations_needed.append("ALTER TABLE tracks ADD COLUMN bpm_source TEXT")
        
        if not migrations_needed:
            logger.info("All source tracking fields already exist")
            return
        
        # Execute migrations
        for migration in migrations_needed:
            logger.info(f"Executing: {migration}")
            cursor.execute(migration)
        
        # Update existing records to set source fields based on existing data
        logger.info("Updating existing records with source information...")
        
        # Set key_source based on existing data
        cursor.execute("""
            UPDATE tracks 
            SET key_source = CASE 
                WHEN mixed_in_key_key IS NOT NULL THEN 'Mixed in Key'
                WHEN librosa_key IS NOT NULL THEN 'Librosa'
                ELSE 'Default'
            END
            WHERE key_source IS NULL
        """)
        
        # Set energy_source based on existing data
        cursor.execute("""
            UPDATE tracks 
            SET energy_source = CASE 
                WHEN mixed_in_key_energy IS NOT NULL THEN 'Mixed in Key'
                WHEN librosa_energy IS NOT NULL THEN 'Librosa'
                ELSE 'Default'
            END
            WHERE energy_source IS NULL
        """)
        
        # Set bpm_source based on existing data
        cursor.execute("""
            UPDATE tracks 
            SET bpm_source = CASE 
                WHEN mixed_in_key_key IS NOT NULL THEN 'Mixed in Key'
                WHEN bpm IS NOT NULL THEN 'Librosa'
                ELSE 'Default'
            END
            WHERE bpm_source IS NULL
        """)
        
        conn.commit()
        logger.info("✅ Source tracking fields migration completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        if 'conn' in locals():
            conn.rollback()
        raise
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    run_migration()
