"""
Database models for AI monitoring.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Float, Boolean, DateTime, ForeignKey, JSON, Text
from sqlalchemy.orm import relationship
from datetime import datetime

from backend.db.base import Base

class AIRequest(Base):
    """
    Model for storing AI requests.
    """
    __tablename__ = "ai_requests"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(String(50), unique=True, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    feature_id = Column(String(50), index=True)
    provider = Column(String(50), index=True)
    model = Column(String(50), index=True)
    parameters = Column(JSON)
    prompt_length = Column(Integer)
    response_length = Column(Integer, nullable=True)
    response_time_ms = Column(Float, nullable=True)
    token_count = Column(Integer, nullable=True)
    success = Column(Boolean, default=True)
    error_type = Column(String(50), nullable=True)
    error_message = Column(Text, nullable=True)
    user_id = Column(String(50), nullable=True, index=True)
    meta_data = Column(JSON, nullable=True)

    # Relationship with feedback
    feedback = relationship("AIFeedback", back_populates="request")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "request_id": self.request_id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "feature_id": self.feature_id,
            "provider": self.provider,
            "model": self.model,
            "parameters": self.parameters,
            "prompt_length": self.prompt_length,
            "response_length": self.response_length,
            "response_time_ms": self.response_time_ms,
            "token_count": self.token_count,
            "success": self.success,
            "error_type": self.error_type,
            "error_message": self.error_message,
            "user_id": self.user_id,
            "metadata": self.meta_data
        }

class AIFeedback(Base):
    """
    Model for storing user feedback on AI responses.
    """
    __tablename__ = "ai_feedback"

    id = Column(Integer, primary_key=True, index=True)
    feedback_id = Column(String(50), unique=True, index=True)
    request_id = Column(String(50), ForeignKey("ai_requests.request_id"), index=True)
    timestamp = Column(DateTime, default=datetime.now)
    feature_id = Column(String(50), index=True)
    rating = Column(Integer)
    feedback_text = Column(Text, nullable=True)
    user_id = Column(String(50), nullable=True, index=True)
    meta_data = Column(JSON, nullable=True)

    # Relationship with request
    request = relationship("AIRequest", back_populates="feedback")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "feedback_id": self.feedback_id,
            "request_id": self.request_id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "feature_id": self.feature_id,
            "rating": self.rating,
            "feedback_text": self.feedback_text,
            "user_id": self.user_id,
            "metadata": self.meta_data
        }

class AIUsageStats(Base):
    """
    Model for storing aggregated AI usage statistics.
    """
    __tablename__ = "ai_usage_stats"

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    feature_id = Column(String(50), nullable=True, index=True)
    provider = Column(String(50), nullable=True, index=True)
    model = Column(String(50), nullable=True, index=True)
    parameter_name = Column(String(50), nullable=True, index=True)
    parameter_value = Column(String(50), nullable=True, index=True)
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    avg_response_time_ms = Column(Float, default=0)
    avg_token_count = Column(Float, default=0)
    avg_rating = Column(Float, nullable=True)
    total_feedback = Column(Integer, default=0)

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "feature_id": self.feature_id,
            "provider": self.provider,
            "model": self.model,
            "parameter_name": self.parameter_name,
            "parameter_value": self.parameter_value,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "avg_response_time_ms": self.avg_response_time_ms,
            "avg_token_count": self.avg_token_count,
            "avg_rating": self.avg_rating,
            "total_feedback": self.total_feedback
        }

class AIOptimizationHistory(Base):
    """
    Model for storing AI optimization history.
    """
    __tablename__ = "ai_optimization_history"

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    feature_id = Column(String(50), index=True)
    parameter_name = Column(String(50), index=True)
    old_value = Column(String(50))
    new_value = Column(String(50))
    reason = Column(Text)
    impact_score = Column(Float)
    applied_by = Column(String(50), nullable=True)  # User ID or "system"

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "feature_id": self.feature_id,
            "parameter_name": self.parameter_name,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "reason": self.reason,
            "impact_score": self.impact_score,
            "applied_by": self.applied_by
        }
