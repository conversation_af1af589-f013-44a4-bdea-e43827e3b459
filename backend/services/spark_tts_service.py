"""
Spark-TTS Service for high-quality text-to-speech synthesis.
"""

import os
import re
import torch
import logging
import soundfile as sf
import tempfile
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, Tuple, List, Union
from datetime import datetime
from huggingface_hub import snapshot_download

# Import Spark-TTS components
from transformers import AutoTokenizer, AutoModelForCausalLM

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

