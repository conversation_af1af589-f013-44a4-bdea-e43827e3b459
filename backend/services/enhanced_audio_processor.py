"""
Enhanced Audio Processor for Phase 2 Beatmatching Implementation.

This service provides high-quality time stretching capabilities using python-stretch
for professional DJ beatmatching, building on the existing audio processing architecture.
"""

import os
import asyncio
import logging
import tempfile
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import numpy as np
import librosa
import soundfile as sf
from sqlalchemy.orm import Session

# Import existing services
from .audio_processor import AudioProcessor as BaseAudioProcessor
from ..repositories.track_repository import TrackRepository
from ..models.track import Track

logger = logging.getLogger(__name__)

class EnhancedAudioProcessor:
    """
    Enhanced Audio Processor with high-quality time stretching for beatmatching.
    
    Features:
    - High-quality time stretching using python-stretch
    - Intelligent caching system for processed audio
    - Quality assessment and validation
    - Integration with existing audio processing pipeline
    """
    
    def __init__(self, track_repository: TrackRepository, db: Session):
        """
        Initialize the Enhanced Audio Processor.
        
        Args:
            track_repository: Repository for track database operations
            db: Database session
        """
        self.track_repository = track_repository
        self.db = db
        self.base_audio_processor = BaseAudioProcessor(track_repository)
        
        # Setup directories
        self.base_dir = Path(__file__).parent.parent
        self.cache_dir = self.base_dir / "static" / "stretched_audio"
        self.temp_dir = self.base_dir / "temp" / "audio_processing"
        
        # Create directories if they don't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache settings
        self.max_cache_size_gb = 5.0  # Maximum cache size in GB
        self.cache_cleanup_threshold = 0.8  # Clean up when 80% full
        
        logger.info(f"EnhancedAudioProcessor initialized")
        logger.info(f"Cache directory: {self.cache_dir}")
        logger.info(f"Temp directory: {self.temp_dir}")
        
        # Import python-stretch (lazy import to handle missing dependency gracefully)
        self._stretch_module = None
        self._init_stretch_module()
    
    def _init_stretch_module(self):
        """Initialize python-stretch module with error handling."""
        try:
            import python_stretch.Signalsmith as ps
            self._stretch_module = ps
            logger.info("✅ python-stretch module loaded successfully")
        except ImportError as e:
            logger.error(f"❌ Failed to import python-stretch: {e}")
            logger.error("Install with: pip install python-stretch")
            self._stretch_module = None
    
    def is_stretch_available(self) -> bool:
        """Check if python-stretch is available."""
        return self._stretch_module is not None
    
    async def stretch_track_to_bpm(
        self, 
        track_id: int, 
        target_bpm: float,
        quality_mode: str = "high"
    ) -> Dict[str, Any]:
        """
        Stretch a track to match a target BPM using high-quality time stretching.
        
        Args:
            track_id: ID of the track to stretch
            target_bpm: Target BPM to stretch to
            quality_mode: Quality mode ("high", "medium", "fast")
            
        Returns:
            Dictionary with stretched audio information and quality metrics
        """
        if not self.is_stretch_available():
            raise RuntimeError("python-stretch library not available. Install with: pip install python-stretch")
        
        logger.info(f"🎵 Starting high-quality time stretching for track {track_id} to {target_bpm} BPM")
        
        try:
            # Get track from database
            track = await self._get_track(track_id)
            if not track:
                raise ValueError(f"Track {track_id} not found")
            
            # Get original audio file path
            audio_file_path = await self._get_audio_file_path(track)
            
            # Calculate time factor
            original_bpm = track.bpm
            if not original_bpm or original_bpm <= 0:
                raise ValueError(f"Track {track_id} has invalid BPM: {original_bpm}")
            
            time_factor = original_bpm / target_bpm
            logger.info(f"Time factor: {time_factor:.3f} ({original_bpm} BPM -> {target_bpm} BPM)")
            
            # Check cache first
            cache_key = self._generate_cache_key(track_id, target_bpm, quality_mode)
            cached_result = await self._get_cached_stretched_audio(cache_key)
            if cached_result:
                logger.info(f"✅ Using cached stretched audio for track {track_id}")
                return cached_result
            
            # Load and process audio
            stretched_path = await self._process_audio_stretching(
                audio_file_path, time_factor, cache_key, quality_mode
            )
            
            # Assess quality
            quality_metrics = await self._assess_stretched_audio_quality(
                audio_file_path, stretched_path, time_factor
            )
            
            # Create result
            result = {
                "track_id": track_id,
                "original_bpm": original_bpm,
                "target_bpm": target_bpm,
                "time_factor": time_factor,
                "stretched_path": str(stretched_path),
                "quality_metrics": quality_metrics,
                "cache_key": cache_key,
                "quality_mode": quality_mode,
                "processing_time": quality_metrics.get("processing_time", 0)
            }
            
            # Cache the result
            await self._cache_stretched_audio(cache_key, result)
            
            logger.info(f"✅ Successfully stretched track {track_id} to {target_bpm} BPM")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to stretch track {track_id}: {e}", exc_info=True)
            raise
    
    async def _get_track(self, track_id: int) -> Optional[Track]:
        """Get track from database."""
        return self.track_repository.get_track_by_id(track_id)
    
    async def _get_audio_file_path(self, track: Track) -> Path:
        """Get the audio file path for a track."""
        # Use the existing audio processor to find the file
        audio_path = await self.base_audio_processor.find_audio_file(track.id, track)
        if not audio_path or not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found for track {track.id}")
        return audio_path
    
    def _generate_cache_key(self, track_id: int, target_bpm: float, quality_mode: str) -> str:
        """Generate a unique cache key for stretched audio."""
        key_string = f"track_{track_id}_bpm_{target_bpm:.1f}_quality_{quality_mode}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def _get_cached_stretched_audio(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Check if stretched audio is already cached."""
        cache_file = self.cache_dir / f"{cache_key}.json"
        audio_file = self.cache_dir / f"{cache_key}.wav"
        
        if cache_file.exists() and audio_file.exists():
            try:
                import json
                with open(cache_file, 'r') as f:
                    cached_data = json.load(f)
                cached_data["stretched_path"] = str(audio_file)
                return cached_data
            except Exception as e:
                logger.warning(f"Failed to load cached data: {e}")
        
        return None
    
    async def _process_audio_stretching(
        self, 
        input_path: Path, 
        time_factor: float, 
        cache_key: str,
        quality_mode: str
    ) -> Path:
        """Process audio stretching using python-stretch."""
        import time
        start_time = time.time()
        
        logger.info(f"🎛️ Processing audio stretching with time factor {time_factor:.3f}")
        
        # Load audio
        y, sr = librosa.load(str(input_path), sr=None)
        logger.info(f"Loaded audio: {len(y)} samples at {sr} Hz ({len(y)/sr:.1f}s)")
        
        # Configure stretch processor based on quality mode
        stretch = self._stretch_module.Stretch()
        
        if quality_mode == "high":
            stretch.preset(1, sr, cheaper=False)  # High quality
        elif quality_mode == "medium":
            stretch.preset(1, sr, cheaper=False)  # Medium quality
        else:  # fast
            stretch.preset(1, sr, cheaper=True)   # Fast processing
        
        stretch.setTimeFactor(time_factor)
        
        # Process audio (reshape for mono processing)
        audio_2d = y.reshape(1, -1)
        stretched_2d = stretch.process(audio_2d)
        stretched_audio = stretched_2d.flatten()
        
        # Save stretched audio
        output_path = self.cache_dir / f"{cache_key}.wav"
        sf.write(str(output_path), stretched_audio, sr)
        
        processing_time = time.time() - start_time
        logger.info(f"✅ Audio stretching completed in {processing_time:.2f}s")
        logger.info(f"Output: {len(stretched_audio)} samples ({len(stretched_audio)/sr:.1f}s)")
        
        return output_path

    async def _assess_stretched_audio_quality(
        self,
        original_path: Path,
        stretched_path: Path,
        time_factor: float
    ) -> Dict[str, Any]:
        """Assess the quality of stretched audio."""
        try:
            # Load both original and stretched audio
            original_y, sr = librosa.load(str(original_path), sr=None, duration=30)  # First 30s for comparison
            stretched_y, _ = librosa.load(str(stretched_path), sr=sr, duration=30)

            # Basic quality metrics
            metrics = {
                "time_factor": time_factor,
                "original_duration": len(original_y) / sr,
                "stretched_duration": len(stretched_y) / sr,
                "sample_rate": sr,
                "processing_time": 0  # Will be set by caller
            }

            # Dynamic range comparison
            original_range = np.max(original_y) - np.min(original_y)
            stretched_range = np.max(stretched_y) - np.min(stretched_y)
            metrics["dynamic_range_preservation"] = stretched_range / original_range if original_range > 0 else 1.0

            # RMS energy comparison
            original_rms = np.sqrt(np.mean(original_y**2))
            stretched_rms = np.sqrt(np.mean(stretched_y**2))
            metrics["energy_preservation"] = stretched_rms / original_rms if original_rms > 0 else 1.0

            # Spectral centroid comparison (brightness)
            original_centroid = np.mean(librosa.feature.spectral_centroid(y=original_y, sr=sr))
            stretched_centroid = np.mean(librosa.feature.spectral_centroid(y=stretched_y, sr=sr))
            metrics["spectral_preservation"] = stretched_centroid / original_centroid if original_centroid > 0 else 1.0

            # Quality score (0-1, higher is better)
            quality_factors = [
                min(metrics["dynamic_range_preservation"], 2.0) / 2.0,  # Cap at 2.0, normalize to 0-1
                min(metrics["energy_preservation"], 2.0) / 2.0,
                min(metrics["spectral_preservation"], 2.0) / 2.0
            ]
            metrics["quality_score"] = np.mean(quality_factors)

            # Quality rating
            if metrics["quality_score"] >= 0.9:
                metrics["quality_rating"] = "excellent"
            elif metrics["quality_score"] >= 0.8:
                metrics["quality_rating"] = "good"
            elif metrics["quality_score"] >= 0.7:
                metrics["quality_rating"] = "acceptable"
            else:
                metrics["quality_rating"] = "poor"

            logger.info(f"Quality assessment: {metrics['quality_rating']} (score: {metrics['quality_score']:.3f})")
            return metrics

        except Exception as e:
            logger.warning(f"Quality assessment failed: {e}")
            return {
                "time_factor": time_factor,
                "quality_score": 0.5,  # Default neutral score
                "quality_rating": "unknown",
                "error": str(e)
            }

    async def _cache_stretched_audio(self, cache_key: str, result: Dict[str, Any]):
        """Cache the stretched audio result."""
        try:
            import json
            cache_file = self.cache_dir / f"{cache_key}.json"

            # Create a copy without the file path for JSON serialization
            cache_data = result.copy()
            cache_data.pop("stretched_path", None)  # Remove path as it's stored separately

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.debug(f"Cached stretched audio metadata: {cache_key}")

        except Exception as e:
            logger.warning(f"Failed to cache metadata: {e}")

    async def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the stretched audio cache."""
        try:
            cache_files = list(self.cache_dir.glob("*.wav"))
            total_size = sum(f.stat().st_size for f in cache_files)
            total_size_gb = total_size / (1024**3)

            return {
                "cache_directory": str(self.cache_dir),
                "cached_files": len(cache_files),
                "total_size_bytes": total_size,
                "total_size_gb": round(total_size_gb, 2),
                "max_size_gb": self.max_cache_size_gb,
                "usage_percentage": round((total_size_gb / self.max_cache_size_gb) * 100, 1),
                "cleanup_needed": total_size_gb > (self.max_cache_size_gb * self.cache_cleanup_threshold)
            }
        except Exception as e:
            logger.error(f"Failed to get cache info: {e}")
            return {"error": str(e)}

    async def cleanup_cache(self, force: bool = False) -> Dict[str, Any]:
        """Clean up old cached files."""
        try:
            cache_info = await self.get_cache_info()

            if not force and not cache_info.get("cleanup_needed", False):
                return {
                    "cleaned": False,
                    "reason": "Cleanup not needed",
                    "cache_info": cache_info
                }

            # Get all cache files sorted by modification time (oldest first)
            cache_files = []
            for wav_file in self.cache_dir.glob("*.wav"):
                json_file = wav_file.with_suffix(".json")
                cache_files.append({
                    "wav": wav_file,
                    "json": json_file,
                    "mtime": wav_file.stat().st_mtime,
                    "size": wav_file.stat().st_size
                })

            cache_files.sort(key=lambda x: x["mtime"])

            # Remove oldest files until we're under the threshold
            target_size = self.max_cache_size_gb * 0.7 * (1024**3)  # Target 70% of max
            current_size = sum(f["size"] for f in cache_files)
            removed_count = 0
            removed_size = 0

            for file_info in cache_files:
                if current_size <= target_size:
                    break

                # Remove both wav and json files
                try:
                    file_info["wav"].unlink()
                    if file_info["json"].exists():
                        file_info["json"].unlink()

                    current_size -= file_info["size"]
                    removed_size += file_info["size"]
                    removed_count += 1

                except Exception as e:
                    logger.warning(f"Failed to remove cache file {file_info['wav']}: {e}")

            logger.info(f"Cache cleanup: removed {removed_count} files ({removed_size / (1024**2):.1f} MB)")

            return {
                "cleaned": True,
                "removed_files": removed_count,
                "removed_size_mb": round(removed_size / (1024**2), 1),
                "cache_info": await self.get_cache_info()
            }

        except Exception as e:
            logger.error(f"Cache cleanup failed: {e}")
            return {"error": str(e)}

    async def validate_stretch_ratio(self, original_bpm: float, target_bpm: float) -> Dict[str, Any]:
        """Validate if a stretch ratio is acceptable for quality."""
        if original_bpm <= 0 or target_bpm <= 0:
            return {
                "valid": False,
                "reason": "Invalid BPM values",
                "recommendation": "Ensure both BPMs are positive numbers"
            }

        time_factor = original_bpm / target_bpm
        stretch_percentage = abs(1.0 - time_factor) * 100

        # Quality thresholds
        if stretch_percentage <= 5:
            quality = "excellent"
            valid = True
        elif stretch_percentage <= 10:
            quality = "good"
            valid = True
        elif stretch_percentage <= 20:
            quality = "acceptable"
            valid = True
        elif stretch_percentage <= 30:
            quality = "poor"
            valid = True  # Still possible but warn user
        else:
            quality = "unacceptable"
            valid = False

        return {
            "valid": valid,
            "time_factor": time_factor,
            "stretch_percentage": round(stretch_percentage, 1),
            "quality_prediction": quality,
            "recommendation": self._get_stretch_recommendation(stretch_percentage, time_factor)
        }

    def _get_stretch_recommendation(self, stretch_percentage: float, time_factor: float) -> str:
        """Get recommendation based on stretch parameters."""
        if stretch_percentage <= 5:
            return "Minimal stretching - excellent quality expected"
        elif stretch_percentage <= 10:
            return "Moderate stretching - good quality expected"
        elif stretch_percentage <= 20:
            return "Significant stretching - quality may be noticeable but acceptable"
        elif stretch_percentage <= 30:
            direction = "slower" if time_factor > 1 else "faster"
            return f"Heavy stretching ({direction}) - quality degradation likely, consider alternative tracks"
        else:
            return "Extreme stretching - not recommended, find tracks with closer BPMs"
