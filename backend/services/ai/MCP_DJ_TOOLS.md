# DJ Mix Constructor MCP Tools

This document provides detailed information about the DJ tools available through the Model Context Protocol (MCP) in DJ Mix Constructor.

## Music Library Tools

### Browse Tracks

**Tool Name**: `browse_tracks`

**Description**: Browse tracks in the user's music collection with optional filters.

**Parameters**:
- `collection_id` (optional): ID of the collection to browse
- `folder_id` (optional): ID of the folder to browse
- `genre` (optional): Genre filter
- `bpm_min` (optional): Minimum BPM filter
- `bpm_max` (optional): Maximum BPM filter
- `key` (optional): Musical key filter
- `limit` (optional): Maximum number of tracks to return (default: 10)

**Example Response**:
```json
{
  "tracks": [
    {
      "id": "123",
      "title": "Summer Vibes",
      "artist": "DJ Example",
      "album": "Beach Party",
      "genre": "House",
      "bpm": 128,
      "key": "C minor",
      "energy": 7,
      "length": 360,
      "file_path": "/path/to/track.mp3"
    }
  ],
  "total_matches": 1,
  "collection_id": "collection1",
  "folder_id": "folder1"
}
```

### Analyze Collection

**Tool Name**: `analyze_collection`

**Description**: Analyze a music collection to provide insights.

**Parameters**:
- `collection_id`: ID of the collection to analyze

**Example Response**:
```json
{
  "collection_id": "collection1",
  "collection_name": "My House Collection",
  "track_count": 100,
  "summary": "Analysis of My House Collection with 100 tracks",
  "bpm_analysis": {
    "mean": 128,
    "median": 126,
    "range": [120, 135],
    "clusters": [
      {"center": 124, "count": 30},
      {"center": 128, "count": 45},
      {"center": 132, "count": 25}
    ]
  },
  "key_distribution": {
    "C minor": 15,
    "G major": 12,
    "D minor": 10
  },
  "health_score": {
    "overall": 0.85,
    "key_balance": 0.9,
    "bpm_coverage": 0.8,
    "energy_balance": 0.85,
    "artist_diversity": 0.75
  },
  "recommendations": [
    "Overall health score: 0.85/1.0",
    "Key balance: 0.90/1.0",
    "BPM coverage: 0.80/1.0",
    "Energy balance: 0.85/1.0",
    "Artist diversity: 0.75/1.0"
  ]
}
```

### Get Track Metadata

**Tool Name**: `get_track_metadata`

**Description**: Get detailed metadata for a specific track.

**Parameters**:
- `track_id`: ID of the track to retrieve metadata for

**Example Response**:
```json
{
  "id": "123",
  "title": "Summer Vibes",
  "artist": "DJ Example",
  "album": "Beach Party",
  "genre": "House",
  "bpm": 128,
  "key": "C minor",
  "energy": 7,
  "length": 360,
  "file_path": "/path/to/track.mp3",
  "waveform_available": true,
  "directory_id": "folder1",
  "directory_path": "/music/house",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-02T12:00:00Z"
}
```

### Search Tracks

**Tool Name**: `search_tracks`

**Description**: Search for tracks by title, artist, album, or genre.

**Parameters**:
- `query`: Search query
- `limit` (optional): Maximum number of tracks to return (default: 10)

**Example Response**:
```json
{
  "query": "summer",
  "tracks": [
    {
      "id": "123",
      "title": "Summer Vibes",
      "artist": "DJ Example",
      "album": "Beach Party",
      "genre": "House",
      "bpm": 128,
      "key": "C minor"
    }
  ],
  "total_matches": 1
}
```

### List Collections

**Tool Name**: `list_collections`

**Description**: List all available music collections.

**Parameters**: None

**Example Response**:
```json
{
  "collections": [
    {
      "id": "collection1",
      "name": "My House Collection",
      "type": "local",
      "track_count": 100,
      "path": "/music/house",
      "is_active": true
    }
  ],
  "total": 1
}
```

### Get Collection Details

**Tool Name**: `get_collection_details`

**Description**: Get detailed information about a specific collection.

**Parameters**:
- `collection_id`: ID of the collection

**Example Response**:
```json
{
  "id": "collection1",
  "name": "My House Collection",
  "type": "local",
  "track_count": 100,
  "path": "/music/house",
  "is_active": true,
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-02T12:00:00Z",
  "last_verified_at": "2023-01-02T12:00:00Z",
  "metadata": {
    "description": "My collection of house tracks",
    "tags": ["house", "electronic", "dance"]
  }
}
```

### Get Collection Folders

**Tool Name**: `get_collection_folders`

**Description**: Get folders within a collection.

**Parameters**:
- `collection_id`: ID of the collection

**Example Response**:
```json
{
  "collection_id": "collection1",
  "collection_name": "My House Collection",
  "folders": [
    {
      "id": "folder1",
      "path": "/music/house/deep",
      "name": "deep"
    },
    {
      "id": "folder2",
      "path": "/music/house/tech",
      "name": "tech"
    }
  ],
  "total": 2
}
```

## Audio Analysis Tools

### Detect BPM

**Tool Name**: `detect_bpm`

**Description**: Detect the BPM (beats per minute) of a track.

**Parameters**:
- `track_id`: ID of the track to analyze

**Example Response**:
```json
{
  "track_id": "123",
  "bpm": 128.5,
  "confidence": 0.95,
  "alternatives": [
    {"bpm": 128.5, "confidence": 0.95},
    {"bpm": 129.0, "confidence": 0.85}
  ],
  "source": "analysis"
}
```

### Analyze Key

**Tool Name**: `analyze_key`

**Description**: Analyze the musical key of a track.

**Parameters**:
- `track_id`: ID of the track to analyze

**Example Response**:
```json
{
  "track_id": "123",
  "key": "C minor",
  "camelot_key": "5A",
  "confidence": 0.92,
  "source": "analysis"
}
```

### Analyze Energy

**Tool Name**: `analyze_energy`

**Description**: Analyze the energy level and dynamics of a track.

**Parameters**:
- `track_id`: ID of the track to analyze

**Example Response**:
```json
{
  "track_id": "123",
  "overall_energy": 7,
  "energy_progression": [5, 6, 7, 8, 9, 8, 7, 6],
  "dynamics": 0.8,
  "segments": [
    {"start": 0, "end": 60, "energy": 5, "type": "intro"},
    {"start": 60, "end": 120, "energy": 7, "type": "verse"},
    {"start": 120, "end": 180, "energy": 9, "type": "chorus"}
  ],
  "source": "analysis"
}
```

### Analyze Track Structure

**Tool Name**: `analyze_track_structure`

**Description**: Analyze the structure of a track (intro, verse, chorus, etc.).

**Parameters**:
- `track_id`: ID of the track to analyze

**Example Response**:
```json
{
  "track_id": "123",
  "sections": [
    {"start": 0, "end": 60, "type": "intro", "confidence": 0.9},
    {"start": 60, "end": 120, "type": "verse", "confidence": 0.85},
    {"start": 120, "end": 180, "type": "chorus", "confidence": 0.95}
  ],
  "total_sections": 3,
  "source": "analysis"
}
```

### Analyze Danceability

**Tool Name**: `analyze_danceability`

**Description**: Analyze the danceability of a track.

**Parameters**:
- `track_id`: ID of the track to analyze

**Example Response**:
```json
{
  "track_id": "123",
  "danceability": 0.85,
  "source": "analysis"
}
```

### Full Audio Analysis

**Tool Name**: `analyze_audio`

**Description**: Perform a comprehensive analysis of a track.

**Parameters**:
- `track_id`: ID of the track to analyze

**Example Response**:
```json
{
  "track_id": "123",
  "title": "Summer Vibes",
  "artist": "DJ Example",
  "analysis": {
    "tempo": 128.5,
    "key": "C minor",
    "energy_analysis": {
      "overall_energy": 7,
      "segments": [
        {"start": 0, "end": 60, "energy": 5, "type": "intro"},
        {"start": 60, "end": 120, "energy": 7, "type": "verse"},
        {"start": 120, "end": 180, "energy": 9, "type": "chorus"}
      ]
    },
    "structure_analysis": {
      "sections": [
        {"start": 0, "end": 60, "type": "intro", "confidence": 0.9},
        {"start": 60, "end": 120, "type": "verse", "confidence": 0.85},
        {"start": 120, "end": 180, "type": "chorus", "confidence": 0.95}
      ]
    },
    "danceability": 0.85,
    "bpm_confidence": 0.95,
    "key_confidence": 0.92
  }
}
```

## Mix Creation Tools

### Load Track to Timeline

**Tool Name**: `load_track_to_timeline`

**Description**: Load a track to the mix timeline at the specified position.

**Parameters**:
- `track_id`: ID of the track to load
- `position` (optional): Position in the timeline (0 = beginning, default: 0)

**Example Response**:
```json
{
  "success": true,
  "track_id": "123",
  "position": 0,
  "track_info": {
    "id": "123",
    "title": "Summer Vibes",
    "artist": "DJ Example",
    "bpm": 128,
    "key": "C minor",
    "energy": 7,
    "length": 360,
    "file_path": "/path/to/track.mp3",
    "position": 0
  },
  "message": "Track 'Summer Vibes' by DJ Example added to timeline at position 0"
}
```

### Create Transition

**Tool Name**: `create_transition`

**Description**: Create a transition between two tracks in the timeline.

**Parameters**:
- `from_track_id`: ID of the first track
- `to_track_id`: ID of the second track
- `transition_type` (optional): Type of transition (beatmatch, cut, fade, filter, echo, loop, spinback, reverb, default: "beatmatch")
- `duration` (optional): Duration of the transition in beats (default: 16)

**Example Response**:
```json
{
  "success": true,
  "transition_id": "t_123_456_beatmatch",
  "from_track_id": "123",
  "to_track_id": "456",
  "from_track": {
    "title": "Summer Vibes",
    "artist": "DJ Example",
    "bpm": 128,
    "key": "C minor"
  },
  "to_track": {
    "title": "Winter Chill",
    "artist": "DJ Sample",
    "bpm": 126,
    "key": "G major"
  },
  "transition_type": "beatmatch",
  "duration": 16,
  "parameters": {
    "bpm_from": 128,
    "bpm_to": 126,
    "bpm_ratio": 0.984375,
    "duration_beats": 16,
    "duration_seconds": 7.5
  },
  "message": "Created beatmatch transition from 'Summer Vibes' to 'Winter Chill'"
}
```

### Get Transition Suggestions

**Tool Name**: `get_transition_suggestions`

**Description**: Get transition suggestions between two tracks.

**Parameters**:
- `from_track_id`: ID of the first track
- `to_track_id`: ID of the second track

**Example Response**:
```json
{
  "success": true,
  "from_track": {
    "id": "123",
    "title": "Summer Vibes",
    "artist": "DJ Example",
    "bpm": 128,
    "key": "C minor",
    "energy": 7
  },
  "to_track": {
    "id": "456",
    "title": "Winter Chill",
    "artist": "DJ Sample",
    "bpm": 126,
    "key": "G major",
    "energy": 5
  },
  "suggestions": [
    {
      "type": "beatmatch",
      "name": "Tempo Adjustment Transition",
      "description": "Gradually adjust tempo from 128 to 126 BPM while beatmatching",
      "confidence": 0.85,
      "duration": 32
    },
    {
      "type": "filter",
      "name": "Modal Shift Filter",
      "description": "Use filter transition to smooth the major/minor shift",
      "confidence": 0.85,
      "duration": 16
    },
    {
      "type": "echo",
      "name": "Energy Breakdown",
      "description": "Reduce energy with echo and reverb from 7 to 5",
      "confidence": 0.80,
      "duration": 16
    }
  ],
  "total_suggestions": 3
}
```

### Get Compatible Tracks

**Tool Name**: `get_compatible_tracks`

**Description**: Get tracks that are compatible with the specified track.

**Parameters**:
- `track_id`: ID of the reference track
- `compatibility_type` (optional): Type of compatibility to check (key, bpm, energy, all, default: "all")
- `limit` (optional): Maximum number of tracks to return (default: 10)

**Example Response**:
```json
{
  "success": true,
  "track": {
    "id": "123",
    "title": "Summer Vibes",
    "artist": "DJ Example",
    "bpm": 128,
    "key": "C minor",
    "energy": 7
  },
  "compatibility_type": "all",
  "compatible_tracks": [
    {
      "id": "456",
      "title": "Winter Chill",
      "artist": "DJ Sample",
      "bpm": 126,
      "key": "C minor",
      "energy": 6,
      "compatibility_score": 0.95
    },
    {
      "id": "789",
      "title": "Autumn Groove",
      "artist": "DJ Test",
      "bpm": 130,
      "key": "G minor",
      "energy": 8,
      "compatibility_score": 0.85
    }
  ],
  "total_matches": 2
}
```
