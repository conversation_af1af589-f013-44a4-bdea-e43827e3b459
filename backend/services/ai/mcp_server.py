"""
Main MCP Server for DJ Mix Constructor application.
Integrates music library, audio analysis, and mix creation tools.
"""

import asyncio
import logging
from typing import Dict, Any
from mcp.server import Server
from mcp.server.stdio import stdio_server

from .music_library_tools import MusicLibraryTools
from .audio_analysis_tools import AudioAnalysisTools
from .mix_creation_tools import MixCreationTools

logger = logging.getLogger(__name__)

class DJMixConstructorMCPServer:
    """MCP Server for DJ Mix Constructor application."""
    
    def __init__(self):
        """Initialize the MCP server with all tool modules."""
        self.server = Server("dj-mix-constructor")
        
        # Initialize tool modules
        self.music_library_tools = MusicLibraryTools(self.server)
        self.audio_analysis_tools = AudioAnalysisTools(self.server)
        self.mix_creation_tools = MixCreationTools(self.server)
        
        logger.info("DJMixConstructorMCPServer initialized with all tool modules")
    
    async def start(self, transport: str = 'stdio'):
        """Start the MCP server with the specified transport."""
        logger.info(f"Starting MCP server with transport: {transport}")
        
        try:
            if transport == 'stdio':
                # Run the server using stdio transport
                async with stdio_server() as (read_stream, write_stream):
                    await self.server.run(
                        read_stream,
                        write_stream,
                        self.server.create_initialization_options()
                    )
            else:
                raise ValueError(f"Unsupported transport: {transport}")
                
        except Exception as e:
            logger.error(f"Error starting MCP server: {e}")
            raise

# Global server instance
mcp_server = DJMixConstructorMCPServer()

async def main():
    """Main entry point for the MCP server."""
    try:
        await mcp_server.start()
    except KeyboardInterrupt:
        logger.info("MCP server stopped by user")
    except Exception as e:
        logger.error(f"MCP server error: {e}")
        raise

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the server
    asyncio.run(main())
