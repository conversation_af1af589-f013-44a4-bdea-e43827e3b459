"""
Advanced Settings Manager for AI services.

This module provides functions to manage advanced settings used by AI providers.
It allows getting, saving, and resetting custom advanced settings.
"""

import json
import os
from typing import Dict, Any, List, Optional, Union
import logging

from backend.config import settings

logger = logging.getLogger(__name__)

# Path to the custom advanced settings file
CUSTOM_ADVANCED_SETTINGS_FILE = os.path.join(settings.BASE_DIR, "data", "custom_advanced_settings.json")

def get_advanced_settings(category: Optional[str] = None) -> Dict[str, Any]:
    """
    Get advanced settings, with custom override if available.
    
    Args:
        category: Optional category to retrieve settings for
        
    Returns:
        The settings dictionary, either custom or default
    """
    # Check if advanced settings are enabled
    if not settings.ADVANCED_SETTINGS_ENABLED:
        if category:
            return settings.DEFAULT_ADVANCED_SETTINGS.get(category, {})
        return settings.DEFAULT_ADVANCED_SETTINGS
    
    # Check if custom settings exist
    if os.path.exists(CUSTOM_ADVANCED_SETTINGS_FILE):
        try:
            with open(CUSTOM_ADVANCED_SETTINGS_FILE, 'r') as f:
                custom_settings = json.load(f)
                
                if category:
                    if category in custom_settings:
                        logger.debug(f"Using custom settings for category: {category}")
                        return custom_settings[category]
                    else:
                        logger.debug(f"No custom settings for category: {category}, using defaults")
                        return settings.DEFAULT_ADVANCED_SETTINGS.get(category, {})
                else:
                    logger.debug("Using custom advanced settings")
                    # Merge with defaults to ensure all settings are present
                    merged_settings = settings.DEFAULT_ADVANCED_SETTINGS.copy()
                    for category, category_settings in custom_settings.items():
                        if category in merged_settings:
                            merged_settings[category].update(category_settings)
                        else:
                            merged_settings[category] = category_settings
                    return merged_settings
        except Exception as e:
            logger.error(f"Error loading custom advanced settings: {str(e)}")
    
    # Fall back to default settings
    if category:
        logger.debug(f"Using default settings for category: {category}")
        return settings.DEFAULT_ADVANCED_SETTINGS.get(category, {})
    
    logger.debug("Using default advanced settings")
    return settings.DEFAULT_ADVANCED_SETTINGS

def save_advanced_settings(category: str, settings_data: Dict[str, Any]) -> bool:
    """
    Save custom advanced settings for a specific category.
    
    Args:
        category: The category to save settings for
        settings_data: The settings dictionary to save
        
    Returns:
        True if successful, False otherwise
    """
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(CUSTOM_ADVANCED_SETTINGS_FILE), exist_ok=True)
    
    custom_settings = {}
    
    # Load existing custom settings if available
    if os.path.exists(CUSTOM_ADVANCED_SETTINGS_FILE):
        try:
            with open(CUSTOM_ADVANCED_SETTINGS_FILE, 'r') as f:
                custom_settings = json.load(f)
        except Exception as e:
            logger.error(f"Error loading custom advanced settings: {str(e)}")
            return False
    
    # Update the settings
    if category not in custom_settings:
        custom_settings[category] = {}
    
    custom_settings[category] = settings_data
    
    # Save back to file
    try:
        with open(CUSTOM_ADVANCED_SETTINGS_FILE, 'w') as f:
            json.dump(custom_settings, f, indent=2)
        logger.info(f"Saved custom advanced settings for {category}")
        return True
    except Exception as e:
        logger.error(f"Error saving custom advanced settings: {str(e)}")
        return False

def reset_advanced_settings(category: Optional[str] = None) -> bool:
    """
    Reset advanced settings to their default values.
    
    Args:
        category: Optional category to reset settings for. If None, resets all settings.
        
    Returns:
        True if successful, False otherwise
    """
    if not os.path.exists(CUSTOM_ADVANCED_SETTINGS_FILE):
        logger.debug(f"No custom advanced settings file exists, nothing to reset")
        return False
    
    try:
        with open(CUSTOM_ADVANCED_SETTINGS_FILE, 'r') as f:
            custom_settings = json.load(f)
        
        if category:
            if category in custom_settings:
                del custom_settings[category]
                logger.info(f"Reset custom advanced settings for {category}")
            else:
                logger.debug(f"No custom settings found for {category}, nothing to reset")
                return False
        else:
            # Reset all settings
            custom_settings = {}
            logger.info("Reset all custom advanced settings")
        
        # Save back to file
        with open(CUSTOM_ADVANCED_SETTINGS_FILE, 'w') as f:
            json.dump(custom_settings, f, indent=2)
        
        return True
    except Exception as e:
        logger.error(f"Error resetting custom advanced settings: {str(e)}")
        return False

def get_all_categories() -> List[str]:
    """
    Get all available advanced settings categories.
    
    Returns:
        List of category names
    """
    return list(settings.DEFAULT_ADVANCED_SETTINGS.keys())

def get_setting_metadata() -> Dict[str, Dict[str, Any]]:
    """
    Get metadata for all advanced settings.
    
    Returns:
        Dictionary of setting categories and their metadata
    """
    return {
        "context_management": {
            "name": "Context Management",
            "description": "Control how context is managed for AI interactions",
            "settings": {
                "max_context_items": {
                    "name": "Max Context Items",
                    "description": "Maximum number of context items to include",
                    "type": "integer",
                    "min": 1,
                    "max": 50,
                    "default": 10
                },
                "context_retention_hours": {
                    "name": "Context Retention Hours",
                    "description": "How long to retain context items in hours",
                    "type": "integer",
                    "min": 1,
                    "max": 168,
                    "default": 24
                },
                "include_conversation_history": {
                    "name": "Include Conversation History",
                    "description": "Include previous conversation in context",
                    "type": "boolean",
                    "default": True
                },
                "include_app_state": {
                    "name": "Include App State",
                    "description": "Include current application state in context",
                    "type": "boolean",
                    "default": True
                }
            }
        },
        "fallback_behavior": {
            "name": "Fallback Behavior",
            "description": "Control how the system handles errors and fallbacks",
            "settings": {
                "retry_on_error": {
                    "name": "Retry On Error",
                    "description": "Automatically retry failed requests",
                    "type": "boolean",
                    "default": True
                },
                "max_retries": {
                    "name": "Max Retries",
                    "description": "Maximum number of retry attempts",
                    "type": "integer",
                    "min": 1,
                    "max": 10,
                    "default": 3
                },
                "fallback_to_simpler_model": {
                    "name": "Fallback To Simpler Model",
                    "description": "Try a simpler model if the primary model fails",
                    "type": "boolean",
                    "default": True
                },
                "fallback_to_cached_response": {
                    "name": "Fallback To Cached Response",
                    "description": "Use cached responses if available when requests fail",
                    "type": "boolean",
                    "default": True
                }
            }
        },
        "response_formatting": {
            "name": "Response Formatting",
            "description": "Control how AI responses are formatted",
            "settings": {
                "prefer_markdown": {
                    "name": "Prefer Markdown",
                    "description": "Format responses using Markdown",
                    "type": "boolean",
                    "default": True
                },
                "include_code_snippets": {
                    "name": "Include Code Snippets",
                    "description": "Include code snippets in responses when relevant",
                    "type": "boolean",
                    "default": True
                },
                "include_examples": {
                    "name": "Include Examples",
                    "description": "Include examples in responses when relevant",
                    "type": "boolean",
                    "default": True
                },
                "max_response_length": {
                    "name": "Max Response Length",
                    "description": "Maximum length of responses in tokens",
                    "type": "integer",
                    "min": 256,
                    "max": 8192,
                    "default": 2048
                }
            }
        },
        "performance_optimization": {
            "name": "Performance Optimization",
            "description": "Control performance-related settings",
            "settings": {
                "use_streaming": {
                    "name": "Use Streaming",
                    "description": "Stream responses when available",
                    "type": "boolean",
                    "default": True
                },
                "parallel_requests": {
                    "name": "Parallel Requests",
                    "description": "Make parallel requests when possible",
                    "type": "boolean",
                    "default": False
                },
                "cache_embeddings": {
                    "name": "Cache Embeddings",
                    "description": "Cache embeddings for faster retrieval",
                    "type": "boolean",
                    "default": True
                },
                "precompute_common_queries": {
                    "name": "Precompute Common Queries",
                    "description": "Precompute responses for common queries",
                    "type": "boolean",
                    "default": False
                }
            }
        },
        "safety_filters": {
            "name": "Safety Filters",
            "description": "Control content safety settings",
            "settings": {
                "enable_content_filtering": {
                    "name": "Enable Content Filtering",
                    "description": "Filter potentially unsafe content",
                    "type": "boolean",
                    "default": True
                },
                "filter_level": {
                    "name": "Filter Level",
                    "description": "Level of content filtering to apply",
                    "type": "select",
                    "options": ["low", "medium", "high"],
                    "default": "medium"
                },
                "block_unsafe_content": {
                    "name": "Block Unsafe Content",
                    "description": "Block requests with potentially unsafe content",
                    "type": "boolean",
                    "default": True
                },
                "log_filtered_content": {
                    "name": "Log Filtered Content",
                    "description": "Log content that was filtered",
                    "type": "boolean",
                    "default": False
                }
            }
        }
    }
