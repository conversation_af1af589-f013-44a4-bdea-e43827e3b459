"""
MCP Server Runner for DJ Mix Constructor application.

This module provides a runner for the MCP server that manages its lifecycle.
"""

import asyncio
import logging
import os
import signal
import subprocess
import sys
import threading
import time
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

class MCPServerRunner:
    """Runner for the MCP server that manages its lifecycle."""
    
    def __init__(self):
        """Initialize the MCP server runner."""
        self.process = None
        self.is_running = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
    
    def start(self, python_path: Optional[str] = None) -> bool:
        """
        Start the MCP server process.
        
        Args:
            python_path: Optional path to the Python executable
            
        Returns:
            True if the server was started successfully, False otherwise
        """
        if self.is_running:
            logger.warning("MCP server is already running")
            return True
        
        try:
            # Determine the Python executable to use
            python_exec = python_path or sys.executable
            
            # Construct the command to run the server
            server_script = os.path.join(os.path.dirname(__file__), "run_mcp_server.py")
            
            # Create the run_mcp_server.py script if it doesn't exist
            if not os.path.exists(server_script):
                self._create_server_script(server_script)
            
            # Start the server process
            logger.info(f"Starting MCP server with Python: {python_exec}")
            self.process = subprocess.Popen(
                [python_exec, server_script],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            # Start monitoring the process
            self.stop_event.clear()
            self.monitor_thread = threading.Thread(target=self._monitor_process)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
            # Wait a moment for the server to start
            time.sleep(1)
            
            # Check if the process is still running
            if self.process.poll() is None:
                self.is_running = True
                logger.info("MCP server started successfully")
                return True
            else:
                logger.error(f"MCP server failed to start. Exit code: {self.process.returncode}")
                stderr = self.process.stderr.read()
                logger.error(f"Server error output: {stderr}")
                return False
        
        except Exception as e:
            logger.error(f"Failed to start MCP server: {str(e)}")
            return False
    
    def _create_server_script(self, script_path: str) -> None:
        """
        Create the run_mcp_server.py script.
        
        Args:
            script_path: Path where the script should be created
        """
        script_content = """#!/usr/bin/env python
'''
Script to run the MCP server.
'''

import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)

# Add the parent directory to the path so we can import the server
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the server
from services.ai.mcp_server import DJMixConstructorMCPServer

async def main():
    '''Run the MCP server.'''
    server = DJMixConstructorMCPServer()
    await server.start(transport='stdio')

if __name__ == '__main__':
    asyncio.run(main())
"""
        
        with open(script_path, "w") as f:
            f.write(script_content)
        
        # Make the script executable
        os.chmod(script_path, 0o755)
        
        logger.info(f"Created MCP server script at {script_path}")
    
    def _monitor_process(self) -> None:
        """Monitor the MCP server process and log any output."""
        while not self.stop_event.is_set() and self.process and self.process.poll() is None:
            # Read output from the process
            if self.process.stdout:
                line = self.process.stdout.readline()
                if line:
                    logger.info(f"MCP server: {line.strip()}")
            
            # Read error output from the process
            if self.process.stderr:
                line = self.process.stderr.readline()
                if line:
                    logger.error(f"MCP server error: {line.strip()}")
            
            # Sleep briefly to avoid consuming too much CPU
            time.sleep(0.1)
        
        # Process has exited or stop was requested
        if self.process and self.process.poll() is not None:
            self.is_running = False
            logger.info(f"MCP server process exited with code {self.process.returncode}")
    
    def stop(self) -> bool:
        """
        Stop the MCP server process.
        
        Returns:
            True if the server was stopped successfully, False otherwise
        """
        if not self.is_running or not self.process:
            logger.warning("MCP server is not running")
            return True
        
        try:
            # Signal the monitor thread to stop
            self.stop_event.set()
            
            # Send SIGTERM to the process
            logger.info("Stopping MCP server")
            self.process.terminate()
            
            # Wait for the process to exit
            try:
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # Force kill if it doesn't exit within the timeout
                logger.warning("MCP server did not exit gracefully, forcing kill")
                self.process.kill()
                self.process.wait(timeout=5)
            
            # Wait for the monitor thread to exit
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            
            self.is_running = False
            self.process = None
            self.monitor_thread = None
            
            logger.info("MCP server stopped successfully")
            return True
        
        except Exception as e:
            logger.error(f"Failed to stop MCP server: {str(e)}")
            return False
    
    def restart(self, python_path: Optional[str] = None) -> bool:
        """
        Restart the MCP server process.
        
        Args:
            python_path: Optional path to the Python executable
            
        Returns:
            True if the server was restarted successfully, False otherwise
        """
        logger.info("Restarting MCP server")
        
        # Stop the server if it's running
        if self.is_running:
            if not self.stop():
                logger.error("Failed to stop MCP server during restart")
                return False
        
        # Start the server again
        return self.start(python_path)
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the status of the MCP server.
        
        Returns:
            Dictionary containing status information
        """
        status = {
            "is_running": self.is_running,
            "pid": self.process.pid if self.process else None,
            "uptime": None,
            "exit_code": None
        }
        
        if self.process:
            # Check if the process is still running
            if self.process.poll() is None:
                # Process is running, get uptime
                status["uptime"] = time.time() - self.process.create_time() if hasattr(self.process, "create_time") else None
            else:
                # Process has exited
                status["is_running"] = False
                status["exit_code"] = self.process.returncode
        
        return status
