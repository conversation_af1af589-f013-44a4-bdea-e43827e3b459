"""
MCP Server for DJ Mix Constructor application.

This module implements a Model Context Protocol (MCP) server that exposes
DJ Mix Constructor's features as tools that can be called by AI models.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from fastmcp import FastMCP

logger = logging.getLogger(__name__)

class DJMixConstructorMCPServer:
    """MCP Server for DJ Mix Constructor application."""

    def __init__(self):
        """Initialize the MCP server."""
        self.server = FastMCP(
            name="dj-mix-constructor-mcp",
            version="1.0.0",
            description="MCP Server for DJ Mix Constructor application"
        )
        self._register_tools()

    def _register_tools(self):
        """Register all available tools with the MCP server."""
        # Register music library tools
        self._register_music_library_tools()

        # Register audio analysis tools
        self._register_audio_analysis_tools()

        # Register mix creation tools
        self._register_mix_creation_tools()

    def _register_music_library_tools(self):
        """Register music library tools."""
        # Import necessary modules
        from sqlalchemy.orm import Session
        from backend.dependencies import get_db
        from backend.repositories.track_repository import TrackRepository
        from backend.repositories.collection_repository import CollectionRepository
        from backend.services.collection_service import CollectionService
        from backend.services.analysis_service import AnalysisService
        from backend.models.track import Track
        from backend.models.collection import Collection
        from sqlalchemy import and_, or_, func

        # Track browsing tool
        @self.server.tool()
        async def browse_tracks(
            collection_id: Optional[str] = None,
            folder_id: Optional[str] = None,
            genre: Optional[str] = None,
            bpm_min: Optional[float] = None,
            bpm_max: Optional[float] = None,
            key: Optional[str] = None,
            limit: int = 10
        ) -> Dict[str, Any]:
            """
            Browse tracks in the user's music collection with optional filters.

            Args:
                collection_id: Optional ID of the collection to browse
                folder_id: Optional ID of the folder to browse
                genre: Optional genre filter
                bpm_min: Optional minimum BPM filter
                bpm_max: Optional maximum BPM filter
                key: Optional musical key filter
                limit: Maximum number of tracks to return

            Returns:
                Dictionary containing matching tracks and metadata
            """
            logger.info(f"Browse tracks called with: collection={collection_id}, folder={folder_id}, genre={genre}")
            logger.info(f"BPM range: {bpm_min}-{bpm_max}, key: {key}, limit: {limit}")

            try:
                # Get database session
                db = next(get_db())

                # Create query
                query = db.query(Track)

                # Apply filters
                if collection_id:
                    # Get track IDs for the collection
                    collection_repo = CollectionRepository()
                    track_ids = collection_repo.get_track_ids_for_collection_or_folder(
                        db, collection_id, folder_id
                    )
                    if track_ids:
                        query = query.filter(Track.id.in_(track_ids))
                    else:
                        # No tracks in collection, return empty result
                        return {
                            "tracks": [],
                            "total_matches": 0,
                            "collection_id": collection_id,
                            "folder_id": folder_id
                        }

                # Apply folder filter if no collection_id was specified
                if folder_id and not collection_id:
                    query = query.filter(Track.directory_id == folder_id)

                # Apply genre filter
                if genre:
                    query = query.filter(Track.genre.ilike(f"%{genre}%"))

                # Apply BPM range filter
                if bpm_min is not None:
                    query = query.filter(Track.bpm >= bpm_min)
                if bpm_max is not None:
                    query = query.filter(Track.bpm <= bpm_max)

                # Apply key filter
                if key:
                    query = query.filter(Track.key.ilike(f"%{key}%"))

                # Get total count before applying limit
                total_matches = query.count()

                # Apply limit
                query = query.limit(limit)

                # Execute query
                tracks = query.all()

                # Format results
                result_tracks = []
                for track in tracks:
                    result_tracks.append({
                        "id": str(track.id),
                        "title": track.title,
                        "artist": track.artist,
                        "album": track.album,
                        "genre": track.genre,
                        "bpm": track.bpm,
                        "key": track.key,
                        "energy": track.energy,
                        "length": track.duration,
                        "file_path": track.file_path
                    })

                return {
                    "tracks": result_tracks,
                    "total_matches": total_matches,
                    "collection_id": collection_id,
                    "folder_id": folder_id
                }

            except Exception as e:
                logger.error(f"Error browsing tracks: {str(e)}")
                return {
                    "error": f"Error browsing tracks: {str(e)}",
                    "tracks": [],
                    "total_matches": 0,
                    "collection_id": collection_id,
                    "folder_id": folder_id
                }

        # Collection analysis tool
        @self.server.tool()
        async def analyze_collection(collection_id: str) -> Dict[str, Any]:
            """
            Analyze a music collection to provide insights.

            Args:
                collection_id: ID of the collection to analyze

            Returns:
                Dictionary containing analysis results
            """
            logger.info(f"Analyze collection called with: collection_id={collection_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get the collection
                collection = CollectionService.get_collection(db, collection_id)
                if not collection:
                    return {
                        "error": f"Collection not found: {collection_id}",
                        "collection_id": collection_id
                    }

                # Create analysis service
                analysis_service = AnalysisService(db)

                # Get BPM analysis
                bpm_analysis = await analysis_service.analyze_bpm_distribution(directory_id=collection_id)

                # Get key distribution
                key_analysis = await analysis_service.analyze_key_distribution(directory_id=collection_id)

                # Get health analysis
                health_analysis = await analysis_service.analyze_collection_health(directory_id=collection_id)

                # Format results
                return {
                    "collection_id": collection_id,
                    "collection_name": collection.name,
                    "track_count": collection.track_count,
                    "summary": f"Analysis of {collection.name} with {collection.track_count} tracks",
                    "bpm_analysis": {
                        "mean": bpm_analysis.get("mean", 0),
                        "median": bpm_analysis.get("median", 0),
                        "range": bpm_analysis.get("bpm_range", [0, 0]),
                        "clusters": bpm_analysis.get("clusters", [])
                    },
                    "key_distribution": key_analysis.get("key_counts", {}),
                    "health_score": {
                        "overall": health_analysis.get("overall", 0.0),
                        "key_balance": health_analysis.get("key_balance", 0.0),
                        "bpm_coverage": health_analysis.get("bpm_coverage", 0.0),
                        "energy_balance": health_analysis.get("energy_balance", 0.0),
                        "artist_diversity": health_analysis.get("artist_diversity", 0.0)
                    },
                    "recommendations": [
                        f"Overall health score: {health_analysis.get('overall', 0.0):.2f}/1.0",
                        f"Key balance: {health_analysis.get('key_balance', 0.0):.2f}/1.0",
                        f"BPM coverage: {health_analysis.get('bpm_coverage', 0.0):.2f}/1.0",
                        f"Energy balance: {health_analysis.get('energy_balance', 0.0):.2f}/1.0",
                        f"Artist diversity: {health_analysis.get('artist_diversity', 0.0):.2f}/1.0"
                    ]
                }

            except Exception as e:
                logger.error(f"Error analyzing collection: {str(e)}")
                return {
                    "error": f"Error analyzing collection: {str(e)}",
                    "collection_id": collection_id
                }

        # Track metadata tool
        @self.server.tool()
        async def get_track_metadata(track_id: str) -> Dict[str, Any]:
            """
            Get detailed metadata for a specific track.

            Args:
                track_id: ID of the track to retrieve metadata for

            Returns:
                Dictionary containing track metadata
            """
            logger.info(f"Get track metadata called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "id": track_id
                    }

                # Format result
                return {
                    "id": str(track.id),
                    "title": track.title,
                    "artist": track.artist,
                    "album": track.album,
                    "genre": track.genre,
                    "bpm": track.bpm,
                    "key": track.key,
                    "energy": track.energy,
                    "length": track.duration,
                    "file_path": track.file_path,
                    "waveform_available": bool(track.waveform_path),
                    "directory_id": track.directory_id,
                    "directory_path": track.directory_path,
                    "created_at": track.created_at.isoformat() if track.created_at else None,
                    "updated_at": track.updated_at.isoformat() if track.updated_at else None
                }

            except Exception as e:
                logger.error(f"Error getting track metadata: {str(e)}")
                return {
                    "error": f"Error getting track metadata: {str(e)}",
                    "id": track_id
                }

        # Search tracks tool
        @self.server.tool()
        async def search_tracks(
            query: str,
            limit: int = 10
        ) -> Dict[str, Any]:
            """
            Search for tracks by title, artist, album, or genre.

            Args:
                query: Search query
                limit: Maximum number of tracks to return

            Returns:
                Dictionary containing matching tracks
            """
            logger.info(f"Search tracks called with: query={query}, limit={limit}")

            try:
                # Get database session
                db = next(get_db())

                # Create search query
                search = f"%{query}%"
                db_query = db.query(Track).filter(
                    or_(
                        Track.title.ilike(search),
                        Track.artist.ilike(search),
                        Track.album.ilike(search),
                        Track.genre.ilike(search)
                    )
                ).limit(limit)

                # Execute query
                tracks = db_query.all()

                # Format results
                result_tracks = []
                for track in tracks:
                    result_tracks.append({
                        "id": str(track.id),
                        "title": track.title,
                        "artist": track.artist,
                        "album": track.album,
                        "genre": track.genre,
                        "bpm": track.bpm,
                        "key": track.key
                    })

                return {
                    "query": query,
                    "tracks": result_tracks,
                    "total_matches": len(result_tracks)
                }

            except Exception as e:
                logger.error(f"Error searching tracks: {str(e)}")
                return {
                    "error": f"Error searching tracks: {str(e)}",
                    "query": query,
                    "tracks": [],
                    "total_matches": 0
                }

        # List collections tool
        @self.server.tool()
        async def list_collections() -> Dict[str, Any]:
            """
            List all available music collections.

            Returns:
                Dictionary containing collections
            """
            logger.info("List collections called")

            try:
                # Get database session
                db = next(get_db())

                # Query collections
                collections = db.query(Collection).all()

                # Format results
                result_collections = []
                for collection in collections:
                    result_collections.append({
                        "id": collection.id,
                        "name": collection.name,
                        "type": collection.collection_type,
                        "track_count": collection.track_count,
                        "path": collection.path,
                        "is_active": collection.is_active
                    })

                return {
                    "collections": result_collections,
                    "total": len(result_collections)
                }

            except Exception as e:
                logger.error(f"Error listing collections: {str(e)}")
                return {
                    "error": f"Error listing collections: {str(e)}",
                    "collections": [],
                    "total": 0
                }

        # Get collection details tool
        @self.server.tool()
        async def get_collection_details(collection_id: str) -> Dict[str, Any]:
            """
            Get detailed information about a specific collection.

            Args:
                collection_id: ID of the collection

            Returns:
                Dictionary containing collection details
            """
            logger.info(f"Get collection details called with: collection_id={collection_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get the collection
                collection = CollectionService.get_collection(db, collection_id)
                if not collection:
                    return {
                        "error": f"Collection not found: {collection_id}",
                        "id": collection_id
                    }

                # Format result
                return {
                    "id": collection.id,
                    "name": collection.name,
                    "type": collection.collection_type,
                    "track_count": collection.track_count,
                    "path": collection.path,
                    "is_active": collection.is_active,
                    "created_at": collection.created_at.isoformat() if collection.created_at else None,
                    "updated_at": collection.updated_at.isoformat() if collection.updated_at else None,
                    "last_verified_at": collection.last_verified_at.isoformat() if collection.last_verified_at else None,
                    "metadata": collection.collection_metadata
                }

            except Exception as e:
                logger.error(f"Error getting collection details: {str(e)}")
                return {
                    "error": f"Error getting collection details: {str(e)}",
                    "id": collection_id
                }

        # Get collection folders tool
        @self.server.tool()
        async def get_collection_folders(collection_id: str) -> Dict[str, Any]:
            """
            Get folders within a collection.

            Args:
                collection_id: ID of the collection

            Returns:
                Dictionary containing folders
            """
            logger.info(f"Get collection folders called with: collection_id={collection_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get the collection
                collection = CollectionService.get_collection(db, collection_id)
                if not collection:
                    return {
                        "error": f"Collection not found: {collection_id}",
                        "id": collection_id,
                        "folders": []
                    }

                # Query distinct directories
                directories = db.query(Track.directory_id, Track.directory_path).filter(
                    Track.directory_id.isnot(None)
                ).distinct().all()

                # Format results
                folders = []
                for dir_id, dir_path in directories:
                    if dir_id:
                        folders.append({
                            "id": dir_id,
                            "path": dir_path,
                            "name": os.path.basename(dir_path) if dir_path else dir_id
                        })

                return {
                    "collection_id": collection_id,
                    "collection_name": collection.name,
                    "folders": folders,
                    "total": len(folders)
                }

            except Exception as e:
                logger.error(f"Error getting collection folders: {str(e)}")
                return {
                    "error": f"Error getting collection folders: {str(e)}",
                    "collection_id": collection_id,
                    "folders": []
                }

        # Import necessary module for get_collection_folders
        import os

    def _register_audio_analysis_tools(self):
        """Register audio analysis tools."""
        # Import necessary modules
        from backend.dependencies import get_db
        from backend.repositories.track_repository import TrackRepository
        from backend.services.audio_analyzer import AudioAnalyzer
        from backend.services.audio_processor import AudioProcessor
        import os
        import numpy as np

        # BPM detection tool
        @self.server.tool()
        async def detect_bpm(track_id: str) -> Dict[str, Any]:
            """
            Detect the BPM (beats per minute) of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing BPM analysis results
            """
            logger.info(f"Detect BPM called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id
                    }

                # Check if track already has BPM
                if track.bpm:
                    logger.info(f"Track {track_id} already has BPM: {track.bpm}")
                    return {
                        "track_id": track_id,
                        "bpm": track.bpm,
                        "confidence": 0.95,  # High confidence since it's already in the database
                        "source": "database"
                    }

                # Check if file exists
                file_path = track.file_path
                if not file_path or not os.path.exists(file_path):
                    return {
                        "error": f"Track file not found: {file_path}",
                        "track_id": track_id
                    }

                # Create audio analyzer
                audio_analyzer = AudioAnalyzer()

                # Analyze the track
                analysis_result = await audio_analyzer.analyze_track(track_id, file_path)

                # Extract BPM information
                bpm = analysis_result.get("tempo", 0)
                bpm_confidence = analysis_result.get("bpm_confidence", 0) / 100.0  # Convert to 0-1 scale
                bpm_alternatives = analysis_result.get("bpm_alternatives", [])

                # Format alternatives
                alternatives = []
                for alt in bpm_alternatives:
                    alternatives.append({
                        "bpm": alt.get("tempo", 0),
                        "confidence": alt.get("confidence", 0) / 100.0  # Convert to 0-1 scale
                    })

                # Update track in database
                if bpm and not track.bpm:
                    track.bpm = bpm
                    db.commit()

                return {
                    "track_id": track_id,
                    "bpm": bpm,
                    "confidence": bpm_confidence,
                    "alternatives": alternatives,
                    "source": "analysis"
                }

            except Exception as e:
                logger.error(f"Error detecting BPM: {str(e)}")
                return {
                    "error": f"Error detecting BPM: {str(e)}",
                    "track_id": track_id
                }

        # Key analysis tool
        @self.server.tool()
        async def analyze_key(track_id: str) -> Dict[str, Any]:
            """
            Analyze the musical key of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing key analysis results
            """
            logger.info(f"Analyze key called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id
                    }

                # Check if track already has key
                if track.key:
                    logger.info(f"Track {track_id} already has key: {track.key}")

                    # Convert to camelot notation if needed
                    camelot_key = track.key
                    if not any(c in track.key for c in "ABd"):
                        # It's probably already in camelot notation
                        pass
                    else:
                        # Convert from traditional to camelot (simplified)
                        key_map = {
                            "C major": "8B", "A minor": "8A",
                            "G major": "9B", "E minor": "9A",
                            "D major": "10B", "B minor": "10A",
                            "A major": "11B", "F# minor": "11A",
                            "E major": "12B", "C# minor": "12A",
                            "B major": "1B", "G# minor": "1A",
                            "F# major": "2B", "D# minor": "2A",
                            "C# major": "3B", "A# minor": "3A",
                            "G# major": "4B", "F minor": "4A",
                            "D# major": "5B", "C minor": "5A",
                            "A# major": "6B", "G minor": "6A",
                            "F major": "7B", "D minor": "7A"
                        }
                        camelot_key = key_map.get(track.key, track.key)

                    return {
                        "track_id": track_id,
                        "key": track.key,
                        "camelot_key": camelot_key,
                        "confidence": 0.95,  # High confidence since it's already in the database
                        "source": "database"
                    }

                # Check if file exists
                file_path = track.file_path
                if not file_path or not os.path.exists(file_path):
                    return {
                        "error": f"Track file not found: {file_path}",
                        "track_id": track_id
                    }

                # Create audio analyzer
                audio_analyzer = AudioAnalyzer()

                # Analyze the track
                analysis_result = await audio_analyzer.analyze_track(track_id, file_path)

                # Extract key information
                key = analysis_result.get("key", "")
                key_confidence = analysis_result.get("key_confidence", 0) / 100.0  # Convert to 0-1 scale

                # Convert to camelot notation if needed
                camelot_key = key
                if not any(c in key for c in "ABd"):
                    # It's probably already in camelot notation
                    pass
                else:
                    # Convert from traditional to camelot (simplified)
                    key_map = {
                        "C major": "8B", "A minor": "8A",
                        "G major": "9B", "E minor": "9A",
                        "D major": "10B", "B minor": "10A",
                        "A major": "11B", "F# minor": "11A",
                        "E major": "12B", "C# minor": "12A",
                        "B major": "1B", "G# minor": "1A",
                        "F# major": "2B", "D# minor": "2A",
                        "C# major": "3B", "A# minor": "3A",
                        "G# major": "4B", "F minor": "4A",
                        "D# major": "5B", "C minor": "5A",
                        "A# major": "6B", "G minor": "6A",
                        "F major": "7B", "D minor": "7A"
                    }
                    camelot_key = key_map.get(key, key)

                # Update track in database
                if key and not track.key:
                    track.key = key
                    db.commit()

                return {
                    "track_id": track_id,
                    "key": key,
                    "camelot_key": camelot_key,
                    "confidence": key_confidence,
                    "source": "analysis"
                }

            except Exception as e:
                logger.error(f"Error analyzing key: {str(e)}")
                return {
                    "error": f"Error analyzing key: {str(e)}",
                    "track_id": track_id
                }

        # Energy analysis tool
        @self.server.tool()
        async def analyze_energy(track_id: str) -> Dict[str, Any]:
            """
            Analyze the energy level and dynamics of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing energy analysis results
            """
            logger.info(f"Analyze energy called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id
                    }

                # Check if track already has energy
                if track.energy:
                    logger.info(f"Track {track_id} already has energy: {track.energy}")
                    return {
                        "track_id": track_id,
                        "overall_energy": track.energy,
                        "confidence": 0.95,  # High confidence since it's already in the database
                        "source": "database"
                    }

                # Check if file exists
                file_path = track.file_path
                if not file_path or not os.path.exists(file_path):
                    return {
                        "error": f"Track file not found: {file_path}",
                        "track_id": track_id
                    }

                # Create audio analyzer
                audio_analyzer = AudioAnalyzer()

                # Analyze the track
                analysis_result = await audio_analyzer.analyze_track(track_id, file_path)

                # Extract energy information
                energy_result = analysis_result.get("energy_analysis", {})
                overall_energy = energy_result.get("overall_energy", 0)
                energy_segments = energy_result.get("segments", [])

                # Format energy progression
                energy_progression = []
                for segment in energy_segments:
                    energy_progression.append(segment.get("energy", 0))

                # Calculate dynamics (variation in energy)
                if energy_progression:
                    dynamics = np.std(energy_progression) / 10.0  # Normalize to 0-1 scale
                else:
                    dynamics = 0.0

                # Update track in database
                if overall_energy and not track.energy:
                    track.energy = overall_energy
                    db.commit()

                return {
                    "track_id": track_id,
                    "overall_energy": overall_energy,
                    "energy_progression": energy_progression,
                    "dynamics": float(dynamics),
                    "segments": energy_segments,
                    "source": "analysis"
                }

            except Exception as e:
                logger.error(f"Error analyzing energy: {str(e)}")
                return {
                    "error": f"Error analyzing energy: {str(e)}",
                    "track_id": track_id
                }

        # Track structure analysis tool
        @self.server.tool()
        async def analyze_track_structure(track_id: str) -> Dict[str, Any]:
            """
            Analyze the structure of a track (intro, verse, chorus, etc.).

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing structure analysis results
            """
            logger.info(f"Analyze track structure called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id
                    }

                # Check if file exists
                file_path = track.file_path
                if not file_path or not os.path.exists(file_path):
                    return {
                        "error": f"Track file not found: {file_path}",
                        "track_id": track_id
                    }

                # Create audio analyzer
                audio_analyzer = AudioAnalyzer()

                # Analyze the track
                analysis_result = await audio_analyzer.analyze_track(track_id, file_path)

                # Extract structure information
                structure_result = analysis_result.get("structure_analysis", {})
                sections = structure_result.get("sections", [])

                return {
                    "track_id": track_id,
                    "sections": sections,
                    "total_sections": len(sections),
                    "source": "analysis"
                }

            except Exception as e:
                logger.error(f"Error analyzing track structure: {str(e)}")
                return {
                    "error": f"Error analyzing track structure: {str(e)}",
                    "track_id": track_id
                }

        # Danceability analysis tool
        @self.server.tool()
        async def analyze_danceability(track_id: str) -> Dict[str, Any]:
            """
            Analyze the danceability of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing danceability analysis results
            """
            logger.info(f"Analyze danceability called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id
                    }

                # Check if file exists
                file_path = track.file_path
                if not file_path or not os.path.exists(file_path):
                    return {
                        "error": f"Track file not found: {file_path}",
                        "track_id": track_id
                    }

                # Create audio analyzer
                audio_analyzer = AudioAnalyzer()

                # Analyze the track
                analysis_result = await audio_analyzer.analyze_track(track_id, file_path)

                # Extract danceability information
                danceability = analysis_result.get("danceability", 0.0)

                return {
                    "track_id": track_id,
                    "danceability": danceability,
                    "source": "analysis"
                }

            except Exception as e:
                logger.error(f"Error analyzing danceability: {str(e)}")
                return {
                    "error": f"Error analyzing danceability: {str(e)}",
                    "track_id": track_id
                }

        # Full audio analysis tool
        @self.server.tool()
        async def analyze_audio(track_id: str) -> Dict[str, Any]:
            """
            Perform a comprehensive analysis of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing all analysis results
            """
            logger.info(f"Analyze audio called with: track_id={track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id
                    }

                # Check if file exists
                file_path = track.file_path
                if not file_path or not os.path.exists(file_path):
                    return {
                        "error": f"Track file not found: {file_path}",
                        "track_id": track_id
                    }

                # Create audio analyzer
                audio_analyzer = AudioAnalyzer()

                # Analyze the track
                analysis_result = await audio_analyzer.analyze_track(track_id, file_path)

                # Update track in database
                if "tempo" in analysis_result and not track.bpm:
                    track.bpm = analysis_result["tempo"]
                if "key" in analysis_result and not track.key:
                    track.key = analysis_result["key"]
                if "energy_analysis" in analysis_result and "overall_energy" in analysis_result["energy_analysis"] and not track.energy:
                    track.energy = analysis_result["energy_analysis"]["overall_energy"]
                db.commit()

                return {
                    "track_id": track_id,
                    "title": track.title,
                    "artist": track.artist,
                    "analysis": analysis_result
                }

            except Exception as e:
                logger.error(f"Error analyzing audio: {str(e)}")
                return {
                    "error": f"Error analyzing audio: {str(e)}",
                    "track_id": track_id
                }

    def _register_mix_creation_tools(self):
        """Register mix creation tools."""
        # Import necessary modules
        from backend.dependencies import get_db
        from backend.repositories.track_repository import TrackRepository
        from backend.services.transition_service import TransitionService
        from backend.services.mix_service import MixService

        # Track loading tool
        @self.server.tool()
        async def load_track_to_timeline(track_id: str, position: int = 0) -> Dict[str, Any]:
            """
            Load a track to the mix timeline at the specified position.

            Args:
                track_id: ID of the track to load
                position: Position in the timeline (0 = beginning)

            Returns:
                Dictionary containing the result of the operation
            """
            logger.info(f"Load track to timeline called with: track_id={track_id}, position={position}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "track_id": track_id,
                        "success": False
                    }

                # Create mix service
                mix_service = MixService(db)

                # Add track to timeline
                # Note: This is a simplified implementation since we don't have direct access to the frontend timeline
                # In a real implementation, this would interact with the frontend timeline through a WebSocket or API
                track_info = {
                    "id": str(track.id),
                    "title": track.title,
                    "artist": track.artist,
                    "bpm": track.bpm,
                    "key": track.key,
                    "energy": track.energy,
                    "length": track.duration,
                    "file_path": track.file_path,
                    "position": position
                }

                # Return success response
                return {
                    "success": True,
                    "track_id": track_id,
                    "position": position,
                    "track_info": track_info,
                    "message": f"Track '{track.title}' by {track.artist} added to timeline at position {position}"
                }

            except Exception as e:
                logger.error(f"Error loading track to timeline: {str(e)}")
                return {
                    "error": f"Error loading track to timeline: {str(e)}",
                    "track_id": track_id,
                    "position": position,
                    "success": False
                }

        # Transition creation tool
        @self.server.tool()
        async def create_transition(
            from_track_id: str,
            to_track_id: str,
            transition_type: str = "beatmatch",
            duration: int = 16  # beats
        ) -> Dict[str, Any]:
            """
            Create a transition between two tracks in the timeline.

            Args:
                from_track_id: ID of the first track
                to_track_id: ID of the second track
                transition_type: Type of transition (beatmatch, cut, fade, filter, echo)
                duration: Duration of the transition in beats

            Returns:
                Dictionary containing the created transition
            """
            logger.info(f"Create transition called with: from={from_track_id}, to={to_track_id}")
            logger.info(f"Type: {transition_type}, duration: {duration} beats")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the tracks
                from_track = await track_repo.get_track_by_id(from_track_id)
                to_track = await track_repo.get_track_by_id(to_track_id)

                if not from_track:
                    return {
                        "error": f"From track not found: {from_track_id}",
                        "success": False
                    }

                if not to_track:
                    return {
                        "error": f"To track not found: {to_track_id}",
                        "success": False
                    }

                # Create transition service
                transition_service = TransitionService(db)

                # Validate transition type
                valid_types = ["beatmatch", "cut", "fade", "filter", "echo", "loop", "spinback", "reverb"]
                if transition_type not in valid_types:
                    return {
                        "error": f"Invalid transition type: {transition_type}. Valid types: {', '.join(valid_types)}",
                        "success": False
                    }

                # Create transition
                # Note: This is a simplified implementation since we don't have direct access to the frontend timeline
                # In a real implementation, this would interact with the frontend timeline through a WebSocket or API
                transition_id = f"t_{from_track_id}_{to_track_id}_{transition_type}"

                # Calculate transition parameters based on type
                transition_params = {}

                if transition_type == "beatmatch":
                    # Calculate beatmatch parameters
                    from_bpm = from_track.bpm or 120
                    to_bpm = to_track.bpm or 120
                    bpm_ratio = to_bpm / from_bpm if from_bpm > 0 else 1.0

                    transition_params = {
                        "bpm_from": from_bpm,
                        "bpm_to": to_bpm,
                        "bpm_ratio": bpm_ratio,
                        "duration_beats": duration,
                        "duration_seconds": (duration / from_bpm) * 60
                    }

                elif transition_type == "fade":
                    # Calculate fade parameters
                    transition_params = {
                        "fade_curve": "linear",
                        "duration_beats": duration,
                        "duration_seconds": (duration / (from_track.bpm or 120)) * 60
                    }

                elif transition_type == "filter":
                    # Calculate filter parameters
                    transition_params = {
                        "filter_type": "lowpass",
                        "filter_frequency": 1000,
                        "filter_resonance": 1.0,
                        "duration_beats": duration
                    }

                # Return success response
                return {
                    "success": True,
                    "transition_id": transition_id,
                    "from_track_id": from_track_id,
                    "to_track_id": to_track_id,
                    "from_track": {
                        "title": from_track.title,
                        "artist": from_track.artist,
                        "bpm": from_track.bpm,
                        "key": from_track.key
                    },
                    "to_track": {
                        "title": to_track.title,
                        "artist": to_track.artist,
                        "bpm": to_track.bpm,
                        "key": to_track.key
                    },
                    "transition_type": transition_type,
                    "duration": duration,
                    "parameters": transition_params,
                    "message": f"Created {transition_type} transition from '{from_track.title}' to '{to_track.title}'"
                }

            except Exception as e:
                logger.error(f"Error creating transition: {str(e)}")
                return {
                    "error": f"Error creating transition: {str(e)}",
                    "from_track_id": from_track_id,
                    "to_track_id": to_track_id,
                    "success": False
                }

        # Get transition suggestions tool
        @self.server.tool()
        async def get_transition_suggestions(
            from_track_id: str,
            to_track_id: str
        ) -> Dict[str, Any]:
            """
            Get transition suggestions between two tracks.

            Args:
                from_track_id: ID of the first track
                to_track_id: ID of the second track

            Returns:
                Dictionary containing transition suggestions
            """
            logger.info(f"Get transition suggestions called with: from={from_track_id}, to={to_track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Get the tracks
                from_track = await track_repo.get_track_by_id(from_track_id)
                to_track = await track_repo.get_track_by_id(to_track_id)

                if not from_track:
                    return {
                        "error": f"From track not found: {from_track_id}",
                        "success": False
                    }

                if not to_track:
                    return {
                        "error": f"To track not found: {to_track_id}",
                        "success": False
                    }

                # Create transition service
                transition_service = TransitionService(db)

                # Get track details
                from_track_details = {
                    "id": str(from_track.id),
                    "title": from_track.title,
                    "artist": from_track.artist,
                    "bpm": from_track.bpm,
                    "key": from_track.key,
                    "energy": from_track.energy
                }

                to_track_details = {
                    "id": str(to_track.id),
                    "title": to_track.title,
                    "artist": to_track.artist,
                    "bpm": to_track.bpm,
                    "key": to_track.key,
                    "energy": to_track.energy
                }

                # Generate suggestions based on track properties
                suggestions = []

                # Check BPM compatibility
                from_bpm = from_track.bpm or 120
                to_bpm = to_track.bpm or 120
                bpm_ratio = to_bpm / from_bpm if from_bpm > 0 else 1.0
                bpm_difference = abs(to_bpm - from_bpm)

                if bpm_difference < 5:
                    # Small BPM difference - beatmatch is ideal
                    suggestions.append({
                        "type": "beatmatch",
                        "name": "Beatmatch Transition",
                        "description": "Gradually blend the tracks with matched beats",
                        "confidence": 0.95,
                        "duration": 16  # beats
                    })
                elif bpm_difference < 15:
                    # Medium BPM difference - beatmatch with tempo adjustment
                    suggestions.append({
                        "type": "beatmatch",
                        "name": "Tempo Adjustment Transition",
                        "description": f"Gradually adjust tempo from {from_bpm} to {to_bpm} BPM while beatmatching",
                        "confidence": 0.85,
                        "duration": 32  # beats
                    })
                else:
                    # Large BPM difference - use a cut or echo freeze
                    suggestions.append({
                        "type": "cut",
                        "name": "Hard Cut Transition",
                        "description": f"Use a hard cut due to large BPM difference ({bpm_difference} BPM)",
                        "confidence": 0.75,
                        "duration": 4  # beats
                    })

                    suggestions.append({
                        "type": "echo",
                        "name": "Echo Freeze Transition",
                        "description": "Use echo freeze effect to mask the BPM change",
                        "confidence": 0.70,
                        "duration": 8  # beats
                    })

                # Check key compatibility
                if from_track.key and to_track.key:
                    # Convert keys to camelot notation if needed
                    key_map = {
                        "C major": "8B", "A minor": "8A",
                        "G major": "9B", "E minor": "9A",
                        "D major": "10B", "B minor": "10A",
                        "A major": "11B", "F# minor": "11A",
                        "E major": "12B", "C# minor": "12A",
                        "B major": "1B", "G# minor": "1A",
                        "F# major": "2B", "D# minor": "2A",
                        "C# major": "3B", "A# minor": "3A",
                        "G# major": "4B", "F minor": "4A",
                        "D# major": "5B", "C minor": "5A",
                        "A# major": "6B", "G minor": "6A",
                        "F major": "7B", "D minor": "7A"
                    }

                    from_camelot = key_map.get(from_track.key, from_track.key)
                    to_camelot = key_map.get(to_track.key, to_track.key)

                    # Check if keys are compatible
                    if from_camelot == to_camelot:
                        # Perfect match
                        suggestions.append({
                            "type": "fade",
                            "name": "Harmonic Fade",
                            "description": "Smooth fade transition with perfect key match",
                            "confidence": 0.95,
                            "duration": 16  # beats
                        })
                    elif from_camelot[:-1] == to_camelot[:-1]:
                        # Same number, different letter (e.g., 1A to 1B)
                        suggestions.append({
                            "type": "filter",
                            "name": "Modal Shift Filter",
                            "description": "Use filter transition to smooth the major/minor shift",
                            "confidence": 0.85,
                            "duration": 16  # beats
                        })
                    elif abs(int(from_camelot[:-1]) - int(to_camelot[:-1])) == 1 or abs(int(from_camelot[:-1]) - int(to_camelot[:-1])) == 11:
                        # Adjacent keys (e.g., 1A to 2A or 12A to 1A)
                        suggestions.append({
                            "type": "filter",
                            "name": "Adjacent Key Filter",
                            "description": "Use filter transition to smooth the adjacent key change",
                            "confidence": 0.80,
                            "duration": 16  # beats
                        })
                    else:
                        # Distant keys - use FX heavy transition
                        suggestions.append({
                            "type": "echo",
                            "name": "FX Transition",
                            "description": "Use heavy effects to mask the key change",
                            "confidence": 0.65,
                            "duration": 8  # beats
                        })

                # Check energy levels
                from_energy = from_track.energy or 5
                to_energy = to_track.energy or 5
                energy_difference = to_energy - from_energy

                if energy_difference > 2:
                    # Energy increase - build up
                    suggestions.append({
                        "type": "filter",
                        "name": "Energy Build-up",
                        "description": f"Build energy with filter sweep from {from_energy} to {to_energy}",
                        "confidence": 0.85,
                        "duration": 32  # beats
                    })
                elif energy_difference < -2:
                    # Energy decrease - breakdown
                    suggestions.append({
                        "type": "echo",
                        "name": "Energy Breakdown",
                        "description": f"Reduce energy with echo and reverb from {from_energy} to {to_energy}",
                        "confidence": 0.80,
                        "duration": 16  # beats
                    })

                # Return suggestions
                return {
                    "success": True,
                    "from_track": from_track_details,
                    "to_track": to_track_details,
                    "suggestions": suggestions,
                    "total_suggestions": len(suggestions)
                }

            except Exception as e:
                logger.error(f"Error getting transition suggestions: {str(e)}")
                return {
                    "error": f"Error getting transition suggestions: {str(e)}",
                    "from_track_id": from_track_id,
                    "to_track_id": to_track_id,
                    "success": False
                }

        # Get compatible tracks tool
        @self.server.tool()
        async def get_compatible_tracks(
            track_id: str,
            compatibility_type: str = "all",
            limit: int = 10
        ) -> Dict[str, Any]:
            """
            Get tracks that are compatible with the specified track.

            Args:
                track_id: ID of the reference track
                compatibility_type: Type of compatibility to check (key, bpm, energy, all)
                limit: Maximum number of tracks to return

            Returns:
                Dictionary containing compatible tracks
            """
            logger.info(f"Get compatible tracks called with: track_id={track_id}, type={compatibility_type}, limit={limit}")

            try:
                # Get database session
                db = next(get_db())

                # Create track repository
                track_repo = TrackRepository(db)

                # Import Track model
                from backend.models.track import Track

                # Get the reference track
                track = await track_repo.get_track_by_id(track_id)
                if not track:
                    return {
                        "error": f"Track not found: {track_id}",
                        "success": False
                    }

                # Get track details
                track_details = {
                    "id": str(track.id),
                    "title": track.title,
                    "artist": track.artist,
                    "bpm": track.bpm,
                    "key": track.key,
                    "energy": track.energy
                }

                # Create query based on compatibility type
                query = db.query(Track).filter(Track.id != track.id)

                if compatibility_type in ["key", "all"] and track.key:
                    # Convert key to camelot notation if needed
                    key_map = {
                        "C major": "8B", "A minor": "8A",
                        "G major": "9B", "E minor": "9A",
                        "D major": "10B", "B minor": "10A",
                        "A major": "11B", "F# minor": "11A",
                        "E major": "12B", "C# minor": "12A",
                        "B major": "1B", "G# minor": "1A",
                        "F# major": "2B", "D# minor": "2A",
                        "C# major": "3B", "A# minor": "3A",
                        "G# major": "4B", "F minor": "4A",
                        "D# major": "5B", "C minor": "5A",
                        "A# major": "6B", "G minor": "6A",
                        "F major": "7B", "D minor": "7A"
                    }

                    track_camelot = key_map.get(track.key, track.key)

                    # Get compatible keys
                    compatible_keys = []

                    # Perfect match
                    compatible_keys.append(track.key)

                    # Same number, different letter (e.g., 1A to 1B)
                    if track_camelot[-1] == "A":
                        compatible_keys.append(track_camelot[:-1] + "B")
                    else:
                        compatible_keys.append(track_camelot[:-1] + "A")

                    # Adjacent keys (e.g., 1A to 2A or 12A to 1A)
                    track_number = int(track_camelot[:-1])
                    track_letter = track_camelot[-1]

                    # Next number
                    next_number = track_number + 1 if track_number < 12 else 1
                    compatible_keys.append(f"{next_number}{track_letter}")

                    # Previous number
                    prev_number = track_number - 1 if track_number > 1 else 12
                    compatible_keys.append(f"{prev_number}{track_letter}")

                    # Convert back to traditional notation if needed
                    reverse_key_map = {v: k for k, v in key_map.items()}
                    for i, key in enumerate(compatible_keys):
                        if key in reverse_key_map:
                            compatible_keys[i] = reverse_key_map[key]

                    # Add to query
                    query = query.filter(Track.key.in_(compatible_keys))

                if compatibility_type in ["bpm", "all"] and track.bpm:
                    # Get tracks with similar BPM (±8%)
                    bpm_min = track.bpm * 0.92
                    bpm_max = track.bpm * 1.08
                    query = query.filter(Track.bpm.between(bpm_min, bpm_max))

                if compatibility_type in ["energy", "all"] and track.energy:
                    # Get tracks with similar energy (±2)
                    energy_min = max(1, track.energy - 2)
                    energy_max = min(10, track.energy + 2)
                    query = query.filter(Track.energy.between(energy_min, energy_max))

                # Apply limit
                query = query.limit(limit)

                # Execute query
                compatible_tracks = query.all()

                # Format results
                result_tracks = []
                for t in compatible_tracks:
                    result_tracks.append({
                        "id": str(t.id),
                        "title": t.title,
                        "artist": t.artist,
                        "bpm": t.bpm,
                        "key": t.key,
                        "energy": t.energy,
                        "compatibility_score": self._calculate_compatibility_score(track, t)
                    })

                # Sort by compatibility score
                result_tracks.sort(key=lambda x: x["compatibility_score"], reverse=True)

                # Return results
                return {
                    "success": True,
                    "track": track_details,
                    "compatibility_type": compatibility_type,
                    "compatible_tracks": result_tracks,
                    "total_matches": len(result_tracks)
                }

            except Exception as e:
                logger.error(f"Error getting compatible tracks: {str(e)}")
                return {
                    "error": f"Error getting compatible tracks: {str(e)}",
                    "track_id": track_id,
                    "success": False
                }

    def _calculate_compatibility_score(self, track1, track2):
        """Calculate compatibility score between two tracks."""
        score = 0.0

        # Key compatibility
        if track1.key and track2.key:
            if track1.key == track2.key:
                # Perfect match
                score += 1.0
            elif track1.key.split()[0] == track2.key.split()[0]:
                # Same root note
                score += 0.8
            elif (track1.key.endswith("minor") and track2.key.endswith("minor")) or \
                 (track1.key.endswith("major") and track2.key.endswith("major")):
                # Same mode
                score += 0.6
            else:
                # Different key
                score += 0.3

        # BPM compatibility
        if track1.bpm and track2.bpm:
            bpm_ratio = min(track1.bpm, track2.bpm) / max(track1.bpm, track2.bpm)
            score += bpm_ratio

        # Energy compatibility
        if track1.energy and track2.energy:
            energy_diff = abs(track1.energy - track2.energy)
            energy_score = 1.0 - (energy_diff / 10.0)
            score += energy_score

        # Normalize score
        return score / 3.0

    async def start(self, transport: str = 'stdio'):
        """Start the MCP server with the specified transport."""
        logger.info(f"Starting MCP server with transport: {transport}")
        await self.server.run(transport=transport)

    def start_background(self):
        """Start the MCP server in the background."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.start())
