"""
MCP Optimization Service

This module integrates MCP with the AI optimization service to optimize MCP settings
based on performance metrics and user feedback.
"""

import logging
import time
from typing import Dict, List, Any, Optional
import json
import statistics
from datetime import datetime, timedelta

from backend.config import settings
from backend.services.monitoring.ai_settings_monitor import AISettingsMonitor
from backend.services.ai.mcp_cache import mcp_cache
from backend.services.ai.mcp_async_executor import mcp_async_executor

logger = logging.getLogger(__name__)

class MCPOptimizationService:
    """Service for optimizing MCP settings based on performance metrics."""
    
    @staticmethod
    def collect_performance_metrics() -> Dict[str, Any]:
        """
        Collect performance metrics for MCP tools.
        
        Returns:
            Dictionary of performance metrics
        """
        # Get tool execution metrics from AISettingsMonitor
        tool_metrics = AISettingsMonitor.get_tool_metrics() if hasattr(AISettingsMonitor, 'get_tool_metrics') else {}
        
        # Get cache statistics
        cache_stats = mcp_cache.get_stats()
        
        # Get async executor statistics
        async_stats = {
            "total_tasks": len(mcp_async_executor.tasks),
            "pending_tasks": sum(1 for task in mcp_async_executor.tasks.values() if task.status == "pending"),
            "running_tasks": sum(1 for task in mcp_async_executor.tasks.values() if task.status == "running"),
            "completed_tasks": sum(1 for task in mcp_async_executor.tasks.values() if task.status == "completed"),
            "failed_tasks": sum(1 for task in mcp_async_executor.tasks.values() if task.status == "failed"),
            "cancelled_tasks": sum(1 for task in mcp_async_executor.tasks.values() if task.status == "cancelled")
        }
        
        # Combine all metrics
        return {
            "tool_metrics": tool_metrics,
            "cache_stats": cache_stats,
            "async_stats": async_stats,
            "timestamp": time.time()
        }
    
    @staticmethod
    def analyze_tool_performance(tool_name: str, time_range_hours: int = 24) -> Dict[str, Any]:
        """
        Analyze performance of a specific MCP tool.
        
        Args:
            tool_name: Name of the tool to analyze
            time_range_hours: Time range in hours to analyze
            
        Returns:
            Dictionary of performance metrics for the tool
        """
        # Get tool execution metrics from AISettingsMonitor
        tool_metrics = AISettingsMonitor.get_tool_metrics(tool_name=tool_name, time_range_hours=time_range_hours) if hasattr(AISettingsMonitor, 'get_tool_metrics') else {}
        
        # Calculate additional metrics if data is available
        if tool_metrics and "execution_times" in tool_metrics and tool_metrics["execution_times"]:
            execution_times = tool_metrics["execution_times"]
            
            # Calculate statistics
            avg_execution_time = statistics.mean(execution_times)
            median_execution_time = statistics.median(execution_times)
            min_execution_time = min(execution_times)
            max_execution_time = max(execution_times)
            
            # Add to metrics
            tool_metrics["avg_execution_time"] = avg_execution_time
            tool_metrics["median_execution_time"] = median_execution_time
            tool_metrics["min_execution_time"] = min_execution_time
            tool_metrics["max_execution_time"] = max_execution_time
        
        return tool_metrics
    
    @staticmethod
    def generate_optimization_recommendations() -> List[Dict[str, Any]]:
        """
        Generate optimization recommendations for MCP settings.
        
        Returns:
            List of recommendation dictionaries
        """
        recommendations = []
        
        # Collect performance metrics
        metrics = MCPOptimizationService.collect_performance_metrics()
        
        # Analyze cache usage
        cache_stats = metrics.get("cache_stats", {})
        if cache_stats:
            # Check cache hit rate
            hit_rate = cache_stats.get("hit_rate", 0)
            if hit_rate < 50 and cache_stats.get("size", 0) > 0:
                # Low hit rate but cache is being used
                recommendations.append({
                    "id": f"mcp-cache-ttl-{int(time.time())}",
                    "feature_id": "mcp",
                    "parameter_name": "MCP_CACHE_DEFAULT_TTL",
                    "current_value": settings.MCP_CACHE_DEFAULT_TTL,
                    "recommended_value": settings.MCP_CACHE_DEFAULT_TTL * 2,
                    "impact_score": 75,
                    "description": "Increase cache TTL to improve hit rate",
                    "reason": f"Current cache hit rate is {hit_rate:.1f}%, which is low. Increasing the TTL may improve cache efficiency."
                })
            
            # Check cache size
            size = cache_stats.get("size", 0)
            max_size = cache_stats.get("max_size", 100)
            if size >= max_size * 0.9:
                # Cache is almost full
                recommendations.append({
                    "id": f"mcp-cache-size-{int(time.time())}",
                    "feature_id": "mcp",
                    "parameter_name": "MCP_CACHE_MAX_SIZE",
                    "current_value": settings.MCP_CACHE_MAX_SIZE,
                    "recommended_value": settings.MCP_CACHE_MAX_SIZE * 2,
                    "impact_score": 80,
                    "description": "Increase cache size to reduce evictions",
                    "reason": f"Cache is {size}/{max_size} items ({size/max_size*100:.1f}% full). Increasing the size may reduce cache evictions."
                })
        
        # Analyze async executor usage
        async_stats = metrics.get("async_stats", {})
        if async_stats:
            # Check if there are many pending tasks
            pending_tasks = async_stats.get("pending_tasks", 0)
            running_tasks = async_stats.get("running_tasks", 0)
            if pending_tasks > running_tasks * 2 and running_tasks > 0:
                # Many pending tasks compared to running tasks
                recommendations.append({
                    "id": f"mcp-async-workers-{int(time.time())}",
                    "feature_id": "mcp",
                    "parameter_name": "MCP_ASYNC_MAX_WORKERS",
                    "current_value": settings.MCP_ASYNC_MAX_WORKERS,
                    "recommended_value": min(settings.MCP_ASYNC_MAX_WORKERS + 2, 10),
                    "impact_score": 85,
                    "description": "Increase async worker count to handle more tasks",
                    "reason": f"There are {pending_tasks} pending tasks but only {running_tasks} running tasks. Increasing the worker count may improve throughput."
                })
        
        # Analyze tool metrics
        tool_metrics = metrics.get("tool_metrics", {})
        for tool_name, tool_data in tool_metrics.items():
            # Check for slow tools
            avg_execution_time = tool_data.get("avg_execution_time", 0)
            if avg_execution_time > 1000:  # More than 1 second
                # Slow tool, recommend caching
                recommendations.append({
                    "id": f"mcp-tool-cache-{tool_name}-{int(time.time())}",
                    "feature_id": "mcp",
                    "parameter_name": f"tool_ttl_settings[{tool_name}]",
                    "current_value": mcp_cache.tool_ttl_settings.get(tool_name, mcp_cache.default_ttl),
                    "recommended_value": max(mcp_cache.tool_ttl_settings.get(tool_name, mcp_cache.default_ttl), 3600),
                    "impact_score": 70,
                    "description": f"Increase cache TTL for slow tool: {tool_name}",
                    "reason": f"Tool {tool_name} has an average execution time of {avg_execution_time:.1f}ms. Increasing the cache TTL may improve performance."
                })
            
            # Check for error-prone tools
            error_rate = tool_data.get("error_rate", 0)
            if error_rate > 0.1:  # More than 10% errors
                # Error-prone tool, recommend more retries
                recommendations.append({
                    "id": f"mcp-tool-retries-{tool_name}-{int(time.time())}",
                    "feature_id": "mcp",
                    "parameter_name": "MCP_MAX_RETRIES",
                    "current_value": settings.MCP_MAX_RETRIES,
                    "recommended_value": min(settings.MCP_MAX_RETRIES + 2, 5),
                    "impact_score": 75,
                    "description": f"Increase retry count for error-prone tool: {tool_name}",
                    "reason": f"Tool {tool_name} has an error rate of {error_rate*100:.1f}%. Increasing the retry count may improve reliability."
                })
        
        return recommendations
    
    @staticmethod
    def apply_optimization(recommendation_id: str) -> Dict[str, Any]:
        """
        Apply an optimization recommendation.
        
        Args:
            recommendation_id: ID of the recommendation to apply
            
        Returns:
            Result of the operation
        """
        # Get all recommendations
        recommendations = MCPOptimizationService.generate_optimization_recommendations()
        
        # Find the recommendation with the given ID
        recommendation = next((r for r in recommendations if r["id"] == recommendation_id), None)
        
        if not recommendation:
            return {
                "success": False,
                "message": f"Recommendation {recommendation_id} not found"
            }
        
        # Apply the recommendation
        parameter_name = recommendation["parameter_name"]
        new_value = recommendation["recommended_value"]
        
        try:
            # Handle special cases
            if parameter_name.startswith("tool_ttl_settings["):
                # Update tool TTL setting
                tool_name = parameter_name.split("[")[1].split("]")[0]
                mcp_cache.tool_ttl_settings[tool_name] = new_value
                logger.info(f"Updated TTL for tool {tool_name} to {new_value}")
            else:
                # For other settings, we would need to update the settings object
                # This is a simplified implementation
                logger.info(f"Would update {parameter_name} to {new_value}")
                # In a real implementation, we would update the settings
            
            return {
                "success": True,
                "message": f"Applied recommendation {recommendation_id}",
                "parameter": parameter_name,
                "old_value": recommendation["current_value"],
                "new_value": new_value
            }
        
        except Exception as e:
            logger.error(f"Error applying recommendation {recommendation_id}: {str(e)}")
            return {
                "success": False,
                "message": f"Error applying recommendation: {str(e)}"
            }

# Create a singleton instance
mcp_optimization_service = MCPOptimizationService()
