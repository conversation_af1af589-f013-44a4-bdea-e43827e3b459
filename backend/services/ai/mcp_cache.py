"""
MCP Tool Results Cache

This module implements a caching layer for MCP tool results to improve performance
and reduce redundant tool calls.
"""

import json
import logging
import time
from typing import Dict, Any, Optional, Tuple, List
import hashlib
import threading

logger = logging.getLogger(__name__)

class MCPToolResultsCache:
    """Cache for MCP tool results to improve performance."""

    def __init__(self, max_size: int = 100, default_ttl: int = 3600):
        """
        Initialize the MCP tool results cache.

        Args:
            max_size: Maximum number of items to store in the cache
            default_ttl: Default time-to-live in seconds (default: 1 hour)
        """
        self.cache: Dict[str, Tuple[Any, float]] = {}  # {key: (value, expiry_time)}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
        self.enabled = True

        # Tool-specific TTL settings
        self.tool_ttl_settings: Dict[str, int] = {
            # Music library tools (longer TTL as library changes less frequently)
            "get_tracks": 3600 * 24,  # 24 hours
            "get_track_metadata": 3600 * 24,  # 24 hours
            "search_tracks": 3600 * 12,  # 12 hours
            "get_collection_folders": 3600 * 24,  # 24 hours

            # Audio analysis tools (medium TTL as analysis results don't change)
            "detect_bpm": 3600 * 24,  # 24 hours
            "analyze_key": 3600 * 24,  # 24 hours
            "analyze_energy": 3600 * 24,  # 24 hours
            "get_beat_grid": 3600 * 24,  # 24 hours

            # Mix creation tools (shorter TTL as mix state changes frequently)
            "get_transition_suggestions": 3600,  # 1 hour
            "get_compatible_tracks": 3600,  # 1 hour

            # Default for other tools
            "default": 3600  # 1 hour
        }

        # Tools that should never be cached
        self.non_cacheable_tools = [
            "load_track_to_timeline",
            "create_transition",
            "update_track_metadata"
        ]

    def _generate_key(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """
        Generate a cache key for the tool call.

        Args:
            tool_name: Name of the tool
            parameters: Parameters passed to the tool

        Returns:
            Cache key as a string
        """
        # Sort parameters to ensure consistent key generation
        sorted_params = json.dumps(parameters, sort_keys=True)

        # Create a hash of the tool name and parameters
        key_string = f"{tool_name}:{sorted_params}"
        return hashlib.md5(key_string.encode()).hexdigest()

    def get(self, tool_name: str, parameters: Dict[str, Any]) -> Optional[Any]:
        """
        Get a cached result for a tool call.

        Args:
            tool_name: Name of the tool
            parameters: Parameters passed to the tool

        Returns:
            Cached result or None if not found or expired
        """
        if not self.enabled or tool_name in self.non_cacheable_tools:
            return None

        key = self._generate_key(tool_name, parameters)

        with self.lock:
            if key in self.cache:
                result, expiry_time = self.cache[key]

                # Check if the result has expired
                if time.time() < expiry_time:
                    self.hit_count += 1
                    logger.debug(f"Cache hit for tool: {tool_name}")
                    return result
                else:
                    # Remove expired result
                    del self.cache[key]

            self.miss_count += 1
            logger.debug(f"Cache miss for tool: {tool_name}")
            return None

    def set(self, tool_name: str, parameters: Dict[str, Any], result: Any, ttl: Optional[int] = None) -> None:
        """
        Cache a tool result.

        Args:
            tool_name: Name of the tool
            parameters: Parameters passed to the tool
            result: Result to cache
            ttl: Time-to-live in seconds (optional)
        """
        if not self.enabled or tool_name in self.non_cacheable_tools:
            return

        key = self._generate_key(tool_name, parameters)

        # Determine TTL to use
        if ttl is None:
            ttl = self.tool_ttl_settings.get(tool_name, self.default_ttl)

        expiry_time = time.time() + ttl

        with self.lock:
            # Add to cache
            self.cache[key] = (result, expiry_time)

            # Enforce max size by removing oldest entries
            if len(self.cache) > self.max_size:
                self._evict_oldest_entries()

        logger.debug(f"Cached result for tool: {tool_name} with TTL: {ttl}s")

    def _evict_oldest_entries(self) -> None:
        """Remove the oldest entries when cache exceeds max size."""
        # Sort by expiry time and remove oldest entries
        sorted_items = sorted(self.cache.items(), key=lambda x: x[1][1])

        # Remove oldest entries until we're under max size
        entries_to_remove = len(self.cache) - self.max_size
        for i in range(entries_to_remove):
            if i < len(sorted_items):
                del self.cache[sorted_items[i][0]]

    def clear(self) -> None:
        """Clear all cached results."""
        with self.lock:
            self.cache.clear()
            logger.info("MCP tool results cache cleared")

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = (self.hit_count / total_requests) * 100 if total_requests > 0 else 0

            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": hit_rate,
                "enabled": self.enabled
            }

    def set_enabled(self, enabled: bool) -> None:
        """
        Enable or disable the cache.

        Args:
            enabled: Whether to enable the cache
        """
        self.enabled = enabled
        logger.info(f"MCP tool results cache {'enabled' if enabled else 'disabled'}")

    def is_enabled(self) -> bool:
        """
        Check if the cache is enabled.

        Returns:
            Whether the cache is enabled
        """
        return self.enabled

# Import settings
from backend.config import settings

# Create a singleton instance with settings from config
mcp_cache = MCPToolResultsCache(
    max_size=settings.MCP_CACHE_MAX_SIZE,
    default_ttl=settings.MCP_CACHE_DEFAULT_TTL
)

# Update tool-specific TTL settings based on config
mcp_cache.tool_ttl_settings.update({
    # Music library tools (longer TTL as library changes less frequently)
    "get_tracks": settings.MCP_CACHE_LONG_TTL,
    "get_track_metadata": settings.MCP_CACHE_LONG_TTL,
    "search_tracks": settings.MCP_CACHE_LONG_TTL,
    "get_collection_folders": settings.MCP_CACHE_LONG_TTL,

    # Audio analysis tools (medium TTL as analysis results don't change)
    "detect_bpm": settings.MCP_CACHE_LONG_TTL,
    "analyze_key": settings.MCP_CACHE_LONG_TTL,
    "analyze_energy": settings.MCP_CACHE_LONG_TTL,
    "get_beat_grid": settings.MCP_CACHE_LONG_TTL,

    # Mix creation tools (shorter TTL as mix state changes frequently)
    "get_transition_suggestions": settings.MCP_CACHE_DEFAULT_TTL,
    "get_compatible_tracks": settings.MCP_CACHE_DEFAULT_TTL,

    # Default for other tools
    "default": settings.MCP_CACHE_DEFAULT_TTL
})
