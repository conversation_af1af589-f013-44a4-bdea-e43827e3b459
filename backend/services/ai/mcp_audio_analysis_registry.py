"""
MCP Audio Analysis Registry Integration

This module provides functions to register the audio analysis MCP server with the MCP registry.
"""

import logging
from typing import Dict, Any, Optional

from backend.services.ai.mcp_registry import mcp_registry
from backend.services.ai.mcp_audio_analysis import MCPAudioAnalysisServer

logger = logging.getLogger(__name__)

def register_audio_analysis_server(db) -> Dict[str, Any]:
    """
    Register the audio analysis MCP server with the registry.
    
    Args:
        db: Database session
        
    Returns:
        Server configuration
    """
    try:
        # Create server configuration
        server_config = {
            "server_id": "audio-analysis",
            "name": "Audio Analysis",
            "description": "Advanced audio analysis for DJ mixing",
            "host": "127.0.0.1",
            "port": 0,  # Use dynamic port
            "transport": "stdio",
            "enabled": True,
            "server_command": ["python", "-m", "backend.services.ai.mcp_audio_analysis"],
            "tool_prefixes": [
                "analyze_", 
                "extract_", 
                "detect_", 
                "download_"
            ],
            "metadata": {
                "category": "audio",
                "version": "1.0.0",
                "features": [
                    "BPM detection",
                    "Key detection",
                    "Beat grid extraction",
                    "Segment detection",
                    "Chroma analysis",
                    "MFCC analysis",
                    "YouTube downloading",
                    "URL downloading"
                ]
            }
        }
        
        # Check if server already exists
        existing_server = mcp_registry.get_server("audio-analysis")
        
        if existing_server:
            # Update existing server
            logger.info("Updating existing audio analysis MCP server")
            return mcp_registry.update_server("audio-analysis", server_config)
        else:
            # Add new server
            logger.info("Registering new audio analysis MCP server")
            return mcp_registry.add_server(server_config)
    
    except Exception as e:
        logger.error(f"Error registering audio analysis MCP server: {str(e)}")
        raise
