"""
MCP Client for DJ Mix Constructor application.

This module implements a Model Context Protocol (MCP) client that connects to the MCP server
and handles function calls from AI models.
"""

import asyncio
import json
import logging
import subprocess
import time
from typing import Dict, List, Any, Optional, Callable, Awaitable, <PERSON><PERSON>
import mcp

from backend.services.ai.mcp_cache import mcp_cache
from backend.services.ai.mcp_async_executor import mcp_async_executor
from backend.services.ai.mcp_error_handler import <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>, MCPError, MCPErrorType, MCPErrorCategory
from backend.config import settings

logger = logging.getLogger(__name__)

class DJMixConstructorMCPClient:
    """MCP Client for DJ Mix Constructor application."""

    def __init__(self):
        """Initialize the MCP client."""
        self.client = None
        self.server_process = None
        self.tools_cache = None
        self.is_connected = False

    async def connect(self, server_command: Optional[List[str]] = None) -> bool:
        """
        Connect to the MCP server.

        Args:
            server_command: Optional command to start the server process

        Returns:
            True if connection was successful, False otherwise
        """
        try:
            # Start the server process if a command was provided
            if server_command:
                await self._start_server_process(server_command)

            # Create and connect the MCP client
            self.client = mcp.Client()
            await self.client.connect()

            # Cache the available tools
            self.tools_cache = await self.get_tools()

            # Set the MCP client for the async executor
            mcp_async_executor.set_mcp_client(self)

            self.is_connected = True
            logger.info("Successfully connected to MCP server")
            return True

        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {str(e)}")
            self.is_connected = False
            return False

    async def _start_server_process(self, command: List[str]) -> None:
        """
        Start the MCP server process.

        Args:
            command: Command to start the server process
        """
        try:
            # Start the server process
            logger.info(f"Starting MCP server process with command: {' '.join(command)}")
            self.server_process = subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )

            # Wait a moment for the server to start
            await asyncio.sleep(1)

            logger.info("MCP server process started")

        except Exception as e:
            logger.error(f"Failed to start MCP server process: {str(e)}")
            raise

    async def disconnect(self) -> None:
        """Disconnect from the MCP server and clean up resources."""
        try:
            # Disconnect the client
            if self.client:
                await self.client.disconnect()
                self.client = None

            # Terminate the server process
            if self.server_process:
                self.server_process.terminate()
                self.server_process = None

            self.is_connected = False
            logger.info("Disconnected from MCP server")

        except Exception as e:
            logger.error(f"Error during MCP client disconnect: {str(e)}")

    async def get_tools(self) -> List[Dict[str, Any]]:
        """
        Get the list of available tools from the MCP server.

        Returns:
            List of tool definitions
        """
        if not self.client:
            logger.error("Cannot get tools: MCP client not connected")
            return []

        try:
            # Get the tools from the server
            tools = await self.client.get_tools()

            # Convert to a more usable format
            tool_defs = []
            for tool in tools:
                tool_def = {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": []
                }

                # Add parameters
                for param in tool.parameters:
                    param_def = {
                        "name": param.name,
                        "description": param.description,
                        "type": param.type,
                        "required": param.required
                    }

                    # Add enum values if available
                    if hasattr(param, "enum") and param.enum:
                        param_def["enum"] = param.enum

                    tool_def["parameters"].append(param_def)

                tool_defs.append(tool_def)

            logger.info(f"Retrieved {len(tool_defs)} tools from MCP server")
            return tool_defs

        except Exception as e:
            logger.error(f"Failed to get tools from MCP server: {str(e)}")
            return []

    async def _execute_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool call without error handling or caching.

        Args:
            tool_name: Name of the tool to call
            parameters: Parameters to pass to the tool

        Returns:
            Result of the tool call
        """
        if not self.client or not self.is_connected:
            raise MCPError(
                message="MCP client not connected",
                error_type=MCPErrorType.CONNECTION_ERROR,
                category=MCPErrorCategory.TRANSIENT
            )

        # Measure execution time for performance monitoring
        start_time = time.time()

        # Call the tool
        logger.info(f"Calling MCP tool: {tool_name} with parameters: {parameters}")
        result = await self.client.call_tool(tool_name, parameters)

        # Calculate execution time
        execution_time = time.time() - start_time

        # Convert result to a dictionary
        if isinstance(result, dict):
            result_dict = result
        elif hasattr(result, "__dict__"):
            result_dict = result.__dict__
        else:
            result_dict = {"result": str(result)}

        # Add execution time to result for monitoring
        result_dict["_execution_time_ms"] = int(execution_time * 1000)

        return result_dict

    async def call_tool(self, tool_name: str, parameters: Dict[str, Any], use_cache: bool = True, max_retries: int = None) -> Dict[str, Any]:
        """
        Call a tool on the MCP server with error handling and caching.

        Args:
            tool_name: Name of the tool to call
            parameters: Parameters to pass to the tool
            use_cache: Whether to use the cache (default: True)
            max_retries: Maximum number of retries (default: from settings)

        Returns:
            Result of the tool call
        """
        # Use default max_retries from settings if not specified
        if max_retries is None:
            max_retries = settings.MCP_MAX_RETRIES if hasattr(settings, 'MCP_MAX_RETRIES') else 3

        # Check cache if enabled and caching is requested for this call
        if use_cache and settings.MCP_CACHE_ENABLED:
            cached_result = mcp_cache.get(tool_name, parameters)
            if cached_result is not None:
                logger.info(f"Using cached result for MCP tool: {tool_name}")
                return cached_result

        try:
            # Execute the tool call with retry logic
            result_dict = await MCPErrorHandler.with_retry(
                self._execute_tool_call,
                tool_name,
                parameters,
                max_retries=max_retries
            )

            # Cache the result if caching is enabled
            if use_cache and settings.MCP_CACHE_ENABLED:
                mcp_cache.set(tool_name, parameters, result_dict)

            return result_dict

        except Exception as e:
            # Format the error response
            error_response = MCPErrorHandler.format_error_response(e)

            # Add tool-specific information
            error_response["tool_name"] = tool_name
            error_response["parameters"] = parameters

            logger.error(f"Error calling MCP tool {tool_name}: {error_response['error']}")

            return error_response

    def get_tools_for_function_calling(self) -> List[Dict[str, Any]]:
        """
        Get the tools in a format suitable for function calling.

        Returns:
            List of tool definitions in function calling format
        """
        if not self.tools_cache:
            logger.warning("Tools cache is empty, cannot format for function calling")
            return []

        function_tools = []

        for tool in self.tools_cache:
            function_tool = {
                "name": tool["name"],
                "description": tool["description"],
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }

            # Add parameters
            for param in tool["parameters"]:
                param_def = {
                    "type": param["type"],
                    "description": param["description"]
                }

                # Add enum values if available
                if "enum" in param:
                    param_def["enum"] = param["enum"]

                function_tool["parameters"]["properties"][param["name"]] = param_def

                # Add to required list if parameter is required
                if param.get("required", False):
                    function_tool["parameters"]["required"].append(param["name"])

            function_tools.append(function_tool)

        return function_tools

    def call_tool_async(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """
        Call a tool asynchronously.

        Args:
            tool_name: Name of the tool to call
            parameters: Parameters for the tool

        Returns:
            Task ID for tracking the execution
        """
        if not self.is_connected:
            raise ValueError("MCP client not connected")

        # Submit the task to the async executor
        return mcp_async_executor.submit_task(tool_name, parameters)

    async def handle_function_call(self, function_call: Dict[str, Any], use_cache: bool = True, async_execution: bool = False) -> Dict[str, Any]:
        """
        Handle a function call from an AI model.

        Args:
            function_call: Function call details from the AI model
            use_cache: Whether to use the cache (default: True)
            async_execution: Whether to execute the tool asynchronously (default: False)

        Returns:
            Result of the function call or task ID if async_execution is True
        """
        if not function_call or "name" not in function_call:
            logger.error("Invalid function call format")
            return {"error": "Invalid function call format"}

        tool_name = function_call["name"]

        # Parse arguments
        arguments = {}
        if "arguments" in function_call:
            try:
                if isinstance(function_call["arguments"], str):
                    arguments = json.loads(function_call["arguments"])
                else:
                    arguments = function_call["arguments"]
            except json.JSONDecodeError:
                logger.error(f"Failed to parse function arguments: {function_call['arguments']}")
                return {"error": "Invalid function arguments format"}

        # Execute asynchronously if requested
        if async_execution and settings.MCP_ASYNC_ENABLED:
            try:
                task_id = self.call_tool_async(tool_name, arguments)
                return {
                    "task_id": task_id,
                    "status": "pending",
                    "async": True,
                    "tool_name": tool_name
                }
            except Exception as e:
                logger.error(f"Error submitting async task: {str(e)}")
                # Fall back to synchronous execution
                logger.info("Falling back to synchronous execution")
                return await self.call_tool(tool_name, arguments, use_cache=use_cache)

        # Call the tool synchronously with cache option
        return await self.call_tool(tool_name, arguments, use_cache=use_cache)
