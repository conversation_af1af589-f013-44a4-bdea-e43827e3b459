# Model Context Protocol (MCP) Implementation

This directory contains the implementation of the Model Context Protocol (MCP) for DJ Mix Constructor. MCP enables AI models to directly call application tools, allowing for more interactive and powerful AI assistance.

## Overview

The MCP implementation consists of several components:

1. **MCP Server**: Exposes DJ Mix Constructor's features as tools that can be called by AI models
2. **MCP Client**: Connects to the MCP server and handles function calls from AI models
3. **MCP Server Runner**: Manages the lifecycle of the MCP server
4. **AI Provider Integration**: Updates to Gemini and Anthropic providers to support MCP

## Components

### MCP Server (`mcp_server.py`)

The MCP server exposes DJ Mix Constructor's features as tools that can be called by AI models. It uses the FastMCP library to create a server that can be connected to by AI models.

Key features:
- Tool registration for music library, audio analysis, and mix creation tools
- Server initialization and configuration
- Support for stdio transport for local communication
- Server lifecycle management (start/stop)

### MCP Client (`mcp_client.py`)

The MCP client connects to the MCP server and handles function calls from AI models. It provides a bridge between the AI providers and the MCP server.

Key features:
- Connection management for the MCP server
- Tool discovery and caching
- Function call parsing and execution
- Error handling and fallbacks

### MCP Server Runner (`mcp_server_runner.py`)

The MCP server runner manages the lifecycle of the MCP server. It provides a convenient way to start, stop, and monitor the MCP server.

Key features:
- Server process management
- Server monitoring
- Error handling and logging
- Status reporting

### AI Provider Integration

#### Gemini Provider (`gemini_provider_mcp.py`)

The Gemini provider has been enhanced to support MCP function calling. It can now call tools directly from the AI model.

Key features:
- MCP initialization and cleanup
- Function calling capabilities
- Tool response processing
- Backward compatibility with existing code

#### Anthropic Provider (`anthropic_provider.py`)

The Anthropic provider has been fully implemented with MCP support. It can now call tools directly from the Claude model.

Key features:
- Claude API integration
- MCP support with tool calling
- Comprehensive error handling
- Full implementation of all AIProvider methods

## Configuration

MCP-specific settings have been added to `config.py`:

```python
# MCP settings
MCP_ENABLED: bool = True
MCP_SERVER_HOST: str = "127.0.0.1"
MCP_SERVER_PORT: int = 8000
MCP_TRANSPORT: Literal["stdio", "http", "websocket"] = "stdio"
MCP_LOG_LEVEL: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"
MCP_TOOL_TIMEOUT: int = 30  # seconds
```

## API Endpoints

MCP-specific API endpoints have been added to `ai_mcp.py`:

- `GET /api/v1/ai/mcp/status`: Get the status of the MCP server and available tools
- `POST /api/v1/ai/mcp/toggle`: Enable or disable the MCP server
- `POST /api/v1/ai/mcp/restart`: Restart the MCP server
- `POST /api/v1/ai/mcp/call-tool`: Call a tool on the MCP server
- `GET /api/v1/ai/mcp/tools`: Get the list of available tools from the MCP server

## Available Tools

### Music Library Tools

- `browse_tracks`: Browse tracks in the user's music collection with optional filters
- `analyze_collection`: Analyze a music collection to provide insights
- `get_track_metadata`: Get detailed metadata for a specific track

### Audio Analysis Tools

- `detect_bpm`: Detect the BPM (beats per minute) of a track
- `analyze_key`: Analyze the musical key of a track
- `analyze_energy`: Analyze the energy level and dynamics of a track

### Mix Creation Tools

- `load_track_to_timeline`: Load a track to the mix timeline at the specified position
- `create_transition`: Create a transition between two tracks in the timeline

## Usage

### Starting the MCP Server

The MCP server can be started using the MCP server runner:

```python
from backend.services.ai.mcp_server_runner import MCPServerRunner

# Create the server runner
server_runner = MCPServerRunner()

# Start the server
server_runner.start()
```

### Connecting to the MCP Server

The MCP client can be used to connect to the MCP server:

```python
from backend.services.ai.mcp_client import DJMixConstructorMCPClient

# Create the client
client = DJMixConstructorMCPClient()

# Connect to the server
await client.connect()
```

### Calling a Tool

Tools can be called using the MCP client:

```python
# Call a tool
result = await client.call_tool("browse_tracks", {
    "genre": "House",
    "bpm_min": 120,
    "bpm_max": 130,
    "limit": 5
})
```

### Using MCP with AI Providers

The AI providers have been updated to support MCP:

```python
from backend.services.ai.gemini_provider_mcp import GeminiProviderMCP

# Create the provider
provider = GeminiProviderMCP(api_key="your_api_key")

# Initialize MCP
await provider.initialize_mcp()

# Generate text with tools
response = await provider.generate_text_with_tools(
    prompt="Find me some house tracks between 120 and 130 BPM",
    feature_id="music_search"
)
```

## Next Steps

The next phase of the MCP implementation will focus on:

1. **Core DJ Tools Implementation**: Implementing more advanced tools for music library, audio analysis, and mix creation
2. **Advanced Integration**: Enhancing the UI, context awareness, and monitoring capabilities
3. **Optimization & Refinement**: Improving performance, error handling, and user experience

See the `MCP_IMPLEMENTATION_PLAN.md` file for more details on the roadmap.

## Dependencies

- `mcp>=1.2.0`: The core MCP library
- `fastmcp`: A high-performance MCP server implementation

## References

- [MCP Documentation](https://github.com/anthropics/anthropic-cookbook/tree/main/mcp)
- [FastMCP Documentation](https://github.com/anthropics/fastmcp)
- [Anthropic MCP Cookbook](https://github.com/anthropics/anthropic-cookbook/tree/main/mcp)
