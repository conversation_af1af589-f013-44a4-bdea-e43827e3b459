"""
MCP Error Handler

This module provides comprehensive error handling for MCP operations,
including error categorization, retry mechanisms, and fallback strategies.
"""

import logging
import time
from typing import Dict, Any, Optional, Callable, Awaitable, List, Tuple, Union
import asyncio
import traceback
import json

from backend.config import settings

logger = logging.getLogger(__name__)

class MCPErrorType:
    """Error types for MCP operations."""
    CONNECTION_ERROR = "connection_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    TOOL_NOT_FOUND = "tool_not_found"
    TOOL_EXECUTION_ERROR = "tool_execution_error"
    PERMISSION_ERROR = "permission_error"
    RESOURCE_ERROR = "resource_error"
    UNKNOWN_ERROR = "unknown_error"

class MCPErrorSeverity:
    """Severity levels for MCP errors."""
    LOW = "low"  # Minor issue, can be ignored
    MEDIUM = "medium"  # Issue that should be addressed but doesn't prevent operation
    HIGH = "high"  # Serious issue that prevents normal operation
    CRITICAL = "critical"  # Critical issue that requires immediate attention

class MCPErrorCategory:
    """Categories for MCP errors."""
    TRANSIENT = "transient"  # Temporary error that may resolve on retry
    PERMANENT = "permanent"  # Permanent error that won't resolve on retry
    CONFIGURATION = "configuration"  # Error related to configuration
    RESOURCE = "resource"  # Error related to resource availability
    SECURITY = "security"  # Error related to security or permissions
    VALIDATION = "validation"  # Error related to input validation
    UNKNOWN = "unknown"  # Unknown error category

class MCPError(Exception):
    """Custom exception for MCP errors with additional metadata."""

    def __init__(
        self,
        message: str,
        error_type: str = MCPErrorType.UNKNOWN_ERROR,
        severity: str = MCPErrorSeverity.MEDIUM,
        category: str = MCPErrorCategory.UNKNOWN,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        """
        Initialize an MCP error.

        Args:
            message: Error message
            error_type: Type of error
            severity: Severity level
            category: Error category
            details: Additional details about the error
            original_exception: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.severity = severity
        self.category = category
        self.details = details or {}
        self.original_exception = original_exception
        self.timestamp = time.time()

        # Add stack trace if available
        if original_exception:
            self.details["original_error"] = str(original_exception)
            self.details["original_error_type"] = type(original_exception).__name__
            self.details["stack_trace"] = traceback.format_exc()

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the error to a dictionary.

        Returns:
            Dictionary representation of the error
        """
        return {
            "error": self.message,
            "error_type": self.error_type,
            "severity": self.severity,
            "category": self.category,
            "details": self.details,
            "timestamp": self.timestamp
        }

    def is_retryable(self) -> bool:
        """
        Check if the error is retryable.

        Returns:
            True if the error is retryable, False otherwise
        """
        return self.category == MCPErrorCategory.TRANSIENT

class MCPErrorHandler:
    """Handler for MCP errors with retry and fallback mechanisms."""

    @staticmethod
    def categorize_error(error: Exception) -> MCPError:
        """
        Categorize an exception as an MCP error.

        Args:
            error: Exception to categorize

        Returns:
            Categorized MCP error
        """
        if isinstance(error, MCPError):
            return error

        error_str = str(error)
        error_type = type(error).__name__

        # Connection errors
        if any(term in error_str.lower() for term in ["connection", "connect", "socket", "network"]):
            return MCPError(
                message=f"Connection error: {error_str}",
                error_type=MCPErrorType.CONNECTION_ERROR,
                severity=MCPErrorSeverity.HIGH,
                category=MCPErrorCategory.TRANSIENT,
                original_exception=error
            )

        # Timeout errors
        elif any(term in error_str.lower() for term in ["timeout", "timed out"]) or "TimeoutError" in error_type:
            return MCPError(
                message=f"Timeout error: {error_str}",
                error_type=MCPErrorType.TIMEOUT_ERROR,
                severity=MCPErrorSeverity.MEDIUM,
                category=MCPErrorCategory.TRANSIENT,
                original_exception=error
            )

        # Validation errors
        elif any(term in error_str.lower() for term in ["invalid", "validation", "schema", "type error"]):
            return MCPError(
                message=f"Validation error: {error_str}",
                error_type=MCPErrorType.VALIDATION_ERROR,
                severity=MCPErrorSeverity.MEDIUM,
                category=MCPErrorCategory.VALIDATION,
                original_exception=error
            )

        # Tool not found errors
        elif "not found" in error_str.lower() or "no such tool" in error_str.lower():
            return MCPError(
                message=f"Tool not found: {error_str}",
                error_type=MCPErrorType.TOOL_NOT_FOUND,
                severity=MCPErrorSeverity.HIGH,
                category=MCPErrorCategory.PERMANENT,
                original_exception=error
            )

        # Permission errors
        elif any(term in error_str.lower() for term in ["permission", "access", "unauthorized"]):
            return MCPError(
                message=f"Permission error: {error_str}",
                error_type=MCPErrorType.PERMISSION_ERROR,
                severity=MCPErrorSeverity.HIGH,
                category=MCPErrorCategory.SECURITY,
                original_exception=error
            )

        # Resource errors
        elif any(term in error_str.lower() for term in ["resource", "memory", "disk", "capacity"]):
            return MCPError(
                message=f"Resource error: {error_str}",
                error_type=MCPErrorType.RESOURCE_ERROR,
                severity=MCPErrorSeverity.HIGH,
                category=MCPErrorCategory.RESOURCE,
                original_exception=error
            )

        # Default to unknown error
        else:
            return MCPError(
                message=f"Unknown error: {error_str}",
                error_type=MCPErrorType.UNKNOWN_ERROR,
                severity=MCPErrorSeverity.MEDIUM,
                category=MCPErrorCategory.UNKNOWN,
                original_exception=error
            )

    @staticmethod
    async def with_retry(
        func: Callable[..., Awaitable[Any]],
        *args,
        max_retries: int = None,
        initial_delay: float = None,
        backoff_factor: float = None,
        **kwargs
    ) -> Any:
        """
        Execute a function with retry logic.

        Args:
            func: Async function to execute
            *args: Arguments to pass to the function
            max_retries: Maximum number of retries
            initial_delay: Initial delay between retries in seconds
            backoff_factor: Factor to increase delay between retries
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Result of the function

        Raises:
            MCPError: If all retries fail
        """
        # Use default values from settings if not specified
        if max_retries is None:
            max_retries = settings.MCP_MAX_RETRIES if hasattr(settings, 'MCP_MAX_RETRIES') else 3

        if initial_delay is None:
            initial_delay = settings.MCP_RETRY_INITIAL_DELAY if hasattr(settings, 'MCP_RETRY_INITIAL_DELAY') else 1.0

        if backoff_factor is None:
            backoff_factor = settings.MCP_RETRY_BACKOFF_FACTOR if hasattr(settings, 'MCP_RETRY_BACKOFF_FACTOR') else 2.0

        last_error = None
        delay = initial_delay

        for attempt in range(max_retries + 1):
            try:
                return await func(*args, **kwargs)

            except Exception as e:
                # Categorize the error
                mcp_error = MCPErrorHandler.categorize_error(e)
                last_error = mcp_error

                # Log the error
                logger.warning(
                    f"Attempt {attempt + 1}/{max_retries + 1} failed: {mcp_error.message} "
                    f"(type: {mcp_error.error_type}, category: {mcp_error.category})"
                )

                # If this is the last attempt or the error is not retryable, raise it
                if attempt >= max_retries or not mcp_error.is_retryable():
                    break

                # Wait before retrying
                logger.info(f"Retrying in {delay:.2f} seconds...")
                await asyncio.sleep(delay)

                # Increase delay for next retry
                delay *= backoff_factor

        # If we get here, all retries failed
        if last_error:
            logger.error(f"All {max_retries + 1} attempts failed: {last_error.message}")
            raise last_error
        else:
            # This should never happen, but just in case
            generic_error = MCPError(
                message=f"All {max_retries + 1} attempts failed with unknown error",
                error_type=MCPErrorType.UNKNOWN_ERROR,
                severity=MCPErrorSeverity.HIGH,
                category=MCPErrorCategory.UNKNOWN
            )
            logger.error(generic_error.message)
            raise generic_error

    @staticmethod
    def format_error_response(error: Union[Exception, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Format an error as a response dictionary.

        Args:
            error: Exception or error dictionary

        Returns:
            Formatted error response
        """
        if isinstance(error, MCPError):
            return error.to_dict()
        elif isinstance(error, Exception):
            return MCPErrorHandler.categorize_error(error).to_dict()
        elif isinstance(error, dict) and "error" in error:
            # Already an error response
            return error
        else:
            # Convert to error response
            return {
                "error": str(error) if not isinstance(error, dict) else json.dumps(error),
                "error_type": MCPErrorType.UNKNOWN_ERROR,
                "severity": MCPErrorSeverity.MEDIUM,
                "category": MCPErrorCategory.UNKNOWN,
                "details": {},
                "timestamp": time.time()
            }
