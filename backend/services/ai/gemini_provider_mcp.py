"""
Enhanced Gemini provider with MCP support.

This module extends the GeminiProvider with Model Context Protocol (MCP) support,
allowing the AI model to call tools directly.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union

import google.generativeai as genai
from fastapi import HTT<PERSON>Exception

from backend.config import settings
from backend.services.ai.gemini_provider import GeminiProvider
from backend.services.ai.mcp_client import DJMixConstructorMCPClient
from backend.services.ai.mcp_server_runner import MCPServerRunner
from backend.services.ai.prompt_manager import get_system_prompt
from backend.services.ai.parameter_manager import get_feature_parameters

logger = logging.getLogger(__name__)

class GeminiProviderMCP(GeminiProvider):
    """Enhanced Gemini provider with MCP support."""
    
    def __init__(self, api_key: str, model_name: str = None):
        """Initialize the Gemini provider with MCP support."""
        # Initialize the base GeminiProvider
        super().__init__(api_key, model_name)
        
        # Initialize MCP components
        self.mcp_client = DJMixConstructorMCPClient()
        self.mcp_server_runner = MCPServerRunner()
        self.mcp_enabled = settings.MCP_ENABLED
        self.mcp_connected = False
    
    async def initialize_mcp(self) -> bool:
        """
        Initialize the MCP server and client.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        if not self.mcp_enabled:
            logger.info("MCP is disabled in settings")
            return False
        
        try:
            # Start the MCP server
            server_started = self.mcp_server_runner.start()
            if not server_started:
                logger.error("Failed to start MCP server")
                return False
            
            # Connect the MCP client
            client_connected = await self.mcp_client.connect()
            if not client_connected:
                logger.error("Failed to connect MCP client")
                self.mcp_server_runner.stop()
                return False
            
            self.mcp_connected = True
            logger.info("MCP initialized successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error initializing MCP: {str(e)}")
            # Clean up resources
            await self.cleanup_mcp()
            return False
    
    async def cleanup_mcp(self) -> None:
        """Clean up MCP resources."""
        try:
            # Disconnect the MCP client
            if self.mcp_client:
                await self.mcp_client.disconnect()
            
            # Stop the MCP server
            if self.mcp_server_runner:
                self.mcp_server_runner.stop()
            
            self.mcp_connected = False
            logger.info("MCP resources cleaned up")
        
        except Exception as e:
            logger.error(f"Error cleaning up MCP resources: {str(e)}")
    
    async def generate_text_with_tools(
        self,
        prompt: str,
        temperature: Optional[float] = None,
        feature_id: Optional[str] = None,
        max_response_length: int = 4096
    ) -> str:
        """
        Generate text using Gemini with function calling capabilities.
        
        Args:
            prompt: The prompt to send to the model
            temperature: Optional temperature parameter
            feature_id: Optional feature ID for parameter customization
            max_response_length: Maximum response length
            
        Returns:
            Generated text
        """
        # Check if MCP is enabled and connected
        if not self.mcp_enabled or not self.mcp_connected:
            # Try to initialize MCP if it's enabled but not connected
            if self.mcp_enabled and not self.mcp_connected:
                mcp_initialized = await self.initialize_mcp()
                if not mcp_initialized:
                    logger.warning("MCP initialization failed, falling back to standard generation")
                    return await self.generate_text(prompt, temperature, feature_id, max_response_length)
            else:
                # MCP is disabled, use standard generation
                return await self.generate_text(prompt, temperature, feature_id, max_response_length)
        
        try:
            # Get the tools from the MCP client
            tools = self.mcp_client.get_tools_for_function_calling()
            if not tools:
                logger.warning("No tools available from MCP, falling back to standard generation")
                return await self.generate_text(prompt, temperature, feature_id, max_response_length)
            
            # Configure generation parameters
            generation_config = {
                "temperature": temperature or settings.AI_TEMPERATURE,
                "max_output_tokens": min(settings.AI_MAX_TOKENS, max_response_length),
                "top_p": 0.95,
            }
            
            # Apply feature-specific parameters if available
            if feature_id:
                feature_params = get_feature_parameters(feature_id)
                generation_config = {
                    "temperature": feature_params.get("temperature", settings.AI_TEMPERATURE),
                    "max_output_tokens": min(feature_params.get("max_tokens", settings.AI_MAX_TOKENS), max_response_length),
                    "top_p": feature_params.get("top_p", 0.95),
                }
            
            # Generate content with tools
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=generation_config,
                tools=tools
            )
            
            # Process the response
            result = await self._process_tool_response(response)
            return result
        
        except Exception as e:
            logger.error(f"Error generating text with tools: {str(e)}")
            # Fall back to standard generation
            logger.info("Falling back to standard generation without tools")
            return await self.generate_text(prompt, temperature, feature_id, max_response_length)
    
    async def _process_tool_response(self, response: Any) -> str:
        """
        Process a response that may contain function calls.
        
        Args:
            response: The response from the model
            
        Returns:
            Processed text
        """
        try:
            # Check if the response has function calls
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                
                if hasattr(candidate, 'content') and candidate.content:
                    content = candidate.content
                    
                    # Check for function calls
                    if hasattr(content, 'parts') and content.parts:
                        for part in content.parts:
                            if hasattr(part, 'function_call') and part.function_call:
                                # Process the function call
                                function_call = {
                                    "name": part.function_call.name,
                                    "arguments": json.loads(part.function_call.args)
                                }
                                
                                # Call the tool via MCP
                                tool_result = await self.mcp_client.handle_function_call(function_call)
                                
                                # Format the tool result as a response
                                return self._format_tool_result(function_call["name"], tool_result)
            
            # If no function calls, return the text response
            if hasattr(response, 'text'):
                return response.text
            
            # Fallback for unexpected response format
            return str(response)
        
        except Exception as e:
            logger.error(f"Error processing tool response: {str(e)}")
            return f"Error processing response: {str(e)}"
    
    def _format_tool_result(self, tool_name: str, result: Dict[str, Any]) -> str:
        """
        Format a tool result as a user-friendly response.
        
        Args:
            tool_name: Name of the tool that was called
            result: Result from the tool
            
        Returns:
            Formatted response
        """
        try:
            # Check for error
            if "error" in result:
                return f"I tried to use the {tool_name} tool, but encountered an error: {result['error']}"
            
            # Format the result based on the tool type
            if tool_name == "browse_tracks":
                return self._format_browse_tracks_result(result)
            elif tool_name == "analyze_collection":
                return self._format_analyze_collection_result(result)
            elif tool_name == "get_track_metadata":
                return self._format_track_metadata_result(result)
            elif tool_name == "detect_bpm":
                return self._format_detect_bpm_result(result)
            elif tool_name == "analyze_key":
                return self._format_analyze_key_result(result)
            elif tool_name == "analyze_energy":
                return self._format_analyze_energy_result(result)
            elif tool_name == "load_track_to_timeline":
                return self._format_load_track_result(result)
            elif tool_name == "create_transition":
                return self._format_create_transition_result(result)
            else:
                # Generic formatting for other tools
                return f"I used the {tool_name} tool and got the following result:\n\n{json.dumps(result, indent=2)}"
        
        except Exception as e:
            logger.error(f"Error formatting tool result: {str(e)}")
            return f"I used the {tool_name} tool, but couldn't format the result properly due to an error."
    
    def _format_browse_tracks_result(self, result: Dict[str, Any]) -> str:
        """Format browse_tracks result."""
        tracks = result.get("tracks", [])
        if not tracks:
            return "I searched for tracks, but didn't find any matching your criteria."
        
        response = f"I found {len(tracks)} tracks"
        if "total_matches" in result:
            response += f" (out of {result['total_matches']} total matches)"
        response += ":\n\n"
        
        for i, track in enumerate(tracks, 1):
            response += f"{i}. **{track.get('title', 'Unknown')}** by {track.get('artist', 'Unknown Artist')}"
            if "bpm" in track:
                response += f" - {track.get('bpm')} BPM"
            if "key" in track:
                response += f", {track.get('key')}"
            response += "\n"
        
        return response
    
    # Add more formatting methods for other tools here
    
    # Override AIProvider methods to use MCP when appropriate
    
    async def generate_text(
        self,
        prompt: str,
        temperature: Optional[float] = None,
        feature_id: Optional[str] = None,
        max_response_length: int = 4096
    ) -> str:
        """Generate text using the AI provider."""
        # If MCP is enabled and connected, use it
        if self.mcp_enabled and self.mcp_connected:
            return await self.generate_text_with_tools(prompt, temperature, feature_id, max_response_length)
        
        # Otherwise, use the standard implementation
        return await super().generate_text(prompt, temperature, feature_id, max_response_length)
    
    async def answer_question(self, question: str, context: Optional[str] = None) -> str:
        """Answer a user question about DJing or music production."""
        context_text = f"\nContext information:\n{context}" if context else ""
        
        # Get the prompt template from the prompt manager
        prompt_template = get_system_prompt("question_answering")
        
        # Format the prompt with the question and context
        prompt = prompt_template.format(question=question, context_text=context_text)
        
        # Use MCP-enabled text generation
        return await self.generate_text(prompt, feature_id="question_answering")
