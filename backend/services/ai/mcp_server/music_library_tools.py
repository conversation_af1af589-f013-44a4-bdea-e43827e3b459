"""
Music library tools for MCP server.
Handles track browsing, collection management, and metadata operations.
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from backend.dependencies import get_db
from backend.repositories.track_repository import TrackRepository
from backend.repositories.collection_repository import CollectionRepository
from backend.services.collection_service import CollectionService
from backend.services.analysis_service import AnalysisService
from backend.models.track import Track
from backend.models.collection import Collection
from sqlalchemy import and_, or_, func

logger = logging.getLogger(__name__)

class MusicLibraryTools:
    """Music library tools for MCP server"""
    
    def __init__(self, server):
        self.server = server
        self._register_tools()
    
    def _register_tools(self):
        """Register music library tools with the MCP server"""
        
        @self.server.tool()
        async def browse_tracks(
            collection_id: Optional[str] = None,
            folder_id: Optional[str] = None,
            genre: Optional[str] = None,
            bpm_min: Optional[float] = None,
            bpm_max: Optional[float] = None,
            key: Optional[str] = None,
            limit: int = 10
        ) -> Dict[str, Any]:
            """
            Browse tracks in the user's music collection with optional filters.

            Args:
                collection_id: Optional ID of the collection to browse
                folder_id: Optional ID of the folder to browse
                genre: Optional genre filter
                bpm_min: Optional minimum BPM filter
                bpm_max: Optional maximum BPM filter
                key: Optional musical key filter
                limit: Maximum number of tracks to return

            Returns:
                Dictionary containing matching tracks and metadata
            """
            logger.info(f"Browse tracks called with: collection={collection_id}, folder={folder_id}, genre={genre}")
            logger.info(f"BPM range: {bpm_min}-{bpm_max}, key: {key}, limit: {limit}")

            try:
                # Get database session
                db = next(get_db())

                # Create query
                query = db.query(Track)

                # Apply filters
                if collection_id:
                    # Get track IDs for the collection
                    collection_repo = CollectionRepository()
                    track_ids = collection_repo.get_track_ids_for_collection_or_folder(
                        db, collection_id, folder_id
                    )
                    if track_ids:
                        query = query.filter(Track.id.in_(track_ids))
                    else:
                        # No tracks in collection, return empty result
                        return {
                            "tracks": [],
                            "total_matches": 0,
                            "message": "No tracks found in the specified collection/folder"
                        }

                # Apply other filters
                if genre:
                    query = query.filter(Track.genre.ilike(f"%{genre}%"))

                if bpm_min is not None:
                    query = query.filter(Track.bpm >= bpm_min)

                if bpm_max is not None:
                    query = query.filter(Track.bpm <= bpm_max)

                if key:
                    query = query.filter(Track.key == key)

                # Get total count before limiting
                total_matches = query.count()

                # Apply limit and get results
                tracks = query.limit(limit).all()

                # Format track data
                track_data = []
                for track in tracks:
                    track_info = {
                        "id": track.id,
                        "title": track.title,
                        "artist": track.artist,
                        "album": track.album,
                        "genre": track.genre,
                        "bpm": track.bpm,
                        "key": track.key,
                        "energy": track.energy,
                        "duration": track.duration,
                        "file_path": track.file_path
                    }
                    track_data.append(track_info)

                return {
                    "tracks": track_data,
                    "total_matches": total_matches,
                    "returned_count": len(track_data),
                    "filters_applied": {
                        "collection_id": collection_id,
                        "folder_id": folder_id,
                        "genre": genre,
                        "bpm_range": f"{bpm_min}-{bpm_max}" if bpm_min or bpm_max else None,
                        "key": key
                    }
                }

            except Exception as e:
                logger.error(f"Error browsing tracks: {e}")
                return {
                    "error": str(e),
                    "tracks": [],
                    "total_matches": 0
                }

        @self.server.tool()
        async def analyze_collection(collection_id: str) -> Dict[str, Any]:
            """
            Analyze a music collection to provide insights.

            Args:
                collection_id: ID of the collection to analyze

            Returns:
                Dictionary containing collection analysis results
            """
            logger.info(f"Analyze collection called with: {collection_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get collection
                collection_repo = CollectionRepository()
                collection = collection_repo.get_by_id(db, collection_id)

                if not collection:
                    return {
                        "error": f"Collection with ID {collection_id} not found"
                    }

                # Get tracks in collection
                track_ids = collection_repo.get_track_ids_for_collection_or_folder(
                    db, collection_id, None
                )

                if not track_ids:
                    return {
                        "collection_name": collection.name,
                        "total_tracks": 0,
                        "message": "No tracks found in collection"
                    }

                # Get track data
                tracks = db.query(Track).filter(Track.id.in_(track_ids)).all()

                # Analyze collection
                analysis = {
                    "collection_name": collection.name,
                    "total_tracks": len(tracks),
                    "genres": {},
                    "bpm_distribution": {
                        "min": None,
                        "max": None,
                        "average": None,
                        "ranges": {
                            "slow (60-90)": 0,
                            "medium (90-130)": 0,
                            "fast (130-180)": 0,
                            "very_fast (180+)": 0
                        }
                    },
                    "key_distribution": {},
                    "energy_distribution": {
                        "low (1-3)": 0,
                        "medium (4-6)": 0,
                        "high (7-10)": 0
                    },
                    "total_duration": 0
                }

                # Collect data for analysis
                bpms = []
                for track in tracks:
                    # Genre analysis
                    if track.genre:
                        genre = track.genre.lower()
                        analysis["genres"][genre] = analysis["genres"].get(genre, 0) + 1

                    # BPM analysis
                    if track.bpm:
                        bpms.append(track.bpm)
                        if track.bpm <= 90:
                            analysis["bpm_distribution"]["ranges"]["slow (60-90)"] += 1
                        elif track.bpm <= 130:
                            analysis["bpm_distribution"]["ranges"]["medium (90-130)"] += 1
                        elif track.bpm <= 180:
                            analysis["bpm_distribution"]["ranges"]["fast (130-180)"] += 1
                        else:
                            analysis["bpm_distribution"]["ranges"]["very_fast (180+)"] += 1

                    # Key analysis
                    if track.key:
                        analysis["key_distribution"][track.key] = analysis["key_distribution"].get(track.key, 0) + 1

                    # Energy analysis
                    if track.energy:
                        if track.energy <= 3:
                            analysis["energy_distribution"]["low (1-3)"] += 1
                        elif track.energy <= 6:
                            analysis["energy_distribution"]["medium (4-6)"] += 1
                        else:
                            analysis["energy_distribution"]["high (7-10)"] += 1

                    # Duration
                    if track.duration:
                        analysis["total_duration"] += track.duration

                # Calculate BPM statistics
                if bpms:
                    analysis["bpm_distribution"]["min"] = min(bpms)
                    analysis["bpm_distribution"]["max"] = max(bpms)
                    analysis["bpm_distribution"]["average"] = sum(bpms) / len(bpms)

                # Sort genres by count
                analysis["genres"] = dict(sorted(analysis["genres"].items(), key=lambda x: x[1], reverse=True))

                return analysis

            except Exception as e:
                logger.error(f"Error analyzing collection: {e}")
                return {
                    "error": str(e)
                }

        @self.server.tool()
        async def get_track_metadata(track_id: str) -> Dict[str, Any]:
            """
            Get detailed metadata for a specific track.

            Args:
                track_id: ID of the track

            Returns:
                Dictionary containing track metadata
            """
            logger.info(f"Get track metadata called with: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()

                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Return comprehensive metadata
                metadata = {
                    "id": track.id,
                    "title": track.title,
                    "artist": track.artist,
                    "album": track.album,
                    "genre": track.genre,
                    "year": track.year,
                    "bpm": track.bpm,
                    "key": track.key,
                    "energy": track.energy,
                    "danceability": track.danceability,
                    "valence": track.valence,
                    "duration": track.duration,
                    "file_path": track.file_path,
                    "file_size": track.file_size,
                    "created_at": track.created_at.isoformat() if track.created_at else None,
                    "updated_at": track.updated_at.isoformat() if track.updated_at else None
                }

                return metadata

            except Exception as e:
                logger.error(f"Error getting track metadata: {e}")
                return {
                    "error": str(e)
                }

        @self.server.tool()
        async def search_tracks(
            query: str,
            limit: int = 10
        ) -> Dict[str, Any]:
            """
            Search for tracks by title, artist, or album.

            Args:
                query: Search query string
                limit: Maximum number of results to return

            Returns:
                Dictionary containing search results
            """
            logger.info(f"Search tracks called with query: '{query}', limit: {limit}")

            try:
                # Get database session
                db = next(get_db())

                # Create search query
                search_filter = or_(
                    Track.title.ilike(f"%{query}%"),
                    Track.artist.ilike(f"%{query}%"),
                    Track.album.ilike(f"%{query}%")
                )

                # Execute search
                tracks = db.query(Track).filter(search_filter).limit(limit).all()

                # Format results
                results = []
                for track in tracks:
                    track_info = {
                        "id": track.id,
                        "title": track.title,
                        "artist": track.artist,
                        "album": track.album,
                        "genre": track.genre,
                        "bpm": track.bpm,
                        "key": track.key,
                        "energy": track.energy,
                        "duration": track.duration
                    }
                    results.append(track_info)

                return {
                    "query": query,
                    "results": results,
                    "count": len(results),
                    "message": f"Found {len(results)} tracks matching '{query}'"
                }

            except Exception as e:
                logger.error(f"Error searching tracks: {e}")
                return {
                    "error": str(e),
                    "query": query,
                    "results": []
                }

        @self.server.tool()
        async def list_collections() -> Dict[str, Any]:
            """
            List all available music collections.

            Returns:
                Dictionary containing list of collections
            """
            logger.info("List collections called")

            try:
                # Get database session
                db = next(get_db())

                # Get all collections
                collections = db.query(Collection).all()

                # Format collection data
                collection_data = []
                for collection in collections:
                    collection_info = {
                        "id": collection.id,
                        "name": collection.name,
                        "path": collection.path,
                        "created_at": collection.created_at.isoformat() if collection.created_at else None
                    }
                    collection_data.append(collection_info)

                return {
                    "collections": collection_data,
                    "count": len(collection_data)
                }

            except Exception as e:
                logger.error(f"Error listing collections: {e}")
                return {
                    "error": str(e),
                    "collections": []
                }

        @self.server.tool()
        async def get_collection_details(collection_id: str) -> Dict[str, Any]:
            """
            Get detailed information about a specific collection.

            Args:
                collection_id: ID of the collection

            Returns:
                Dictionary containing collection details
            """
            logger.info(f"Get collection details called with: {collection_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get collection
                collection_repo = CollectionRepository()
                collection = collection_repo.get_by_id(db, collection_id)

                if not collection:
                    return {
                        "error": f"Collection with ID {collection_id} not found"
                    }

                # Get track count
                track_ids = collection_repo.get_track_ids_for_collection_or_folder(
                    db, collection_id, None
                )

                return {
                    "id": collection.id,
                    "name": collection.name,
                    "path": collection.path,
                    "track_count": len(track_ids) if track_ids else 0,
                    "created_at": collection.created_at.isoformat() if collection.created_at else None,
                    "updated_at": collection.updated_at.isoformat() if collection.updated_at else None
                }

            except Exception as e:
                logger.error(f"Error getting collection details: {e}")
                return {
                    "error": str(e)
                }

        @self.server.tool()
        async def get_collection_folders(collection_id: str) -> Dict[str, Any]:
            """
            Get folders within a collection.

            Args:
                collection_id: ID of the collection

            Returns:
                Dictionary containing folder information
            """
            logger.info(f"Get collection folders called with: {collection_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get collection service
                collection_service = CollectionService(db)

                # Get folder structure
                folders = collection_service.get_collection_folder_structure(collection_id)

                return {
                    "collection_id": collection_id,
                    "folders": folders
                }

            except Exception as e:
                logger.error(f"Error getting collection folders: {e}")
                return {
                    "error": str(e),
                    "collection_id": collection_id,
                    "folders": []
                }
