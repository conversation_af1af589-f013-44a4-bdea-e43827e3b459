"""
MCP Server Package for DJ Mix Constructor

Modular MCP server with specialized tool modules.
"""

# Export the main server
from .mcp_server import DJMixConstructorMCPServer, mcp_server

# Export individual tool modules for direct access if needed
from .music_library_tools import MusicLibraryTools
from .audio_analysis_tools import AudioAnalysisTools
from .mix_creation_tools import MixCreationTools

__all__ = [
    'DJMixConstructorMCPServer',
    'mcp_server',
    'MusicLibraryTools',
    'AudioAnalysisTools',
    'MixCreationTools'
]
