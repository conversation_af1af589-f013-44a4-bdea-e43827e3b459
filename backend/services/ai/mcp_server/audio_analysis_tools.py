"""
Audio analysis tools for MCP server.
Handles BPM detection, key analysis, energy analysis, and other audio features.
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from backend.dependencies import get_db
from backend.services.analysis_service import AnalysisService
from backend.models.track import Track

logger = logging.getLogger(__name__)

class AudioAnalysisTools:
    """Audio analysis tools for MCP server"""
    
    def __init__(self, server):
        self.server = server
        self._register_tools()
    
    def _register_tools(self):
        """Register audio analysis tools with the MCP server"""
        
        @self.server.tool()
        async def detect_bpm(track_id: str) -> Dict[str, Any]:
            """
            Detect the BPM (beats per minute) of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing BPM analysis results
            """
            logger.info(f"Detect BPM called with track_id: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Check if BPM is already available
                if track.bpm:
                    return {
                        "track_id": track_id,
                        "track_title": track.title,
                        "track_artist": track.artist,
                        "bpm": track.bpm,
                        "source": "cached",
                        "message": "BPM retrieved from database"
                    }

                # Perform BPM detection
                analysis_service = AnalysisService(db)
                
                # Check if file exists
                if not track.file_path:
                    return {
                        "error": "Track file path not available"
                    }

                # Analyze BPM
                bpm_result = await analysis_service.analyze_bpm(track.file_path)
                
                if bpm_result.get("error"):
                    return {
                        "error": f"BPM detection failed: {bpm_result['error']}"
                    }

                # Update track with detected BPM
                if "bpm" in bpm_result:
                    track.bpm = bpm_result["bpm"]
                    db.commit()

                return {
                    "track_id": track_id,
                    "track_title": track.title,
                    "track_artist": track.artist,
                    "bpm": bpm_result.get("bpm"),
                    "confidence": bpm_result.get("confidence"),
                    "source": "analyzed",
                    "analysis_details": bpm_result
                }

            except Exception as e:
                logger.error(f"Error detecting BPM: {e}")
                return {
                    "error": str(e),
                    "track_id": track_id
                }

        @self.server.tool()
        async def analyze_key(track_id: str) -> Dict[str, Any]:
            """
            Analyze the musical key of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing key analysis results
            """
            logger.info(f"Analyze key called with track_id: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Check if key is already available
                if track.key:
                    return {
                        "track_id": track_id,
                        "track_title": track.title,
                        "track_artist": track.artist,
                        "key": track.key,
                        "source": "cached",
                        "message": "Key retrieved from database"
                    }

                # Perform key analysis
                analysis_service = AnalysisService(db)
                
                if not track.file_path:
                    return {
                        "error": "Track file path not available"
                    }

                # Analyze key
                key_result = await analysis_service.analyze_key(track.file_path)
                
                if key_result.get("error"):
                    return {
                        "error": f"Key analysis failed: {key_result['error']}"
                    }

                # Update track with detected key
                if "key" in key_result:
                    track.key = key_result["key"]
                    db.commit()

                return {
                    "track_id": track_id,
                    "track_title": track.title,
                    "track_artist": track.artist,
                    "key": key_result.get("key"),
                    "confidence": key_result.get("confidence"),
                    "source": "analyzed",
                    "analysis_details": key_result
                }

            except Exception as e:
                logger.error(f"Error analyzing key: {e}")
                return {
                    "error": str(e),
                    "track_id": track_id
                }

        @self.server.tool()
        async def analyze_energy(track_id: str) -> Dict[str, Any]:
            """
            Analyze the energy level and dynamics of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing energy analysis results
            """
            logger.info(f"Analyze energy called with track_id: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Check if energy is already available
                if track.energy:
                    return {
                        "track_id": track_id,
                        "track_title": track.title,
                        "track_artist": track.artist,
                        "energy": track.energy,
                        "source": "cached",
                        "message": "Energy retrieved from database"
                    }

                # Perform energy analysis
                analysis_service = AnalysisService(db)
                
                if not track.file_path:
                    return {
                        "error": "Track file path not available"
                    }

                # Analyze energy
                energy_result = await analysis_service.analyze_energy(track.file_path)
                
                if energy_result.get("error"):
                    return {
                        "error": f"Energy analysis failed: {energy_result['error']}"
                    }

                # Update track with detected energy
                if "energy" in energy_result:
                    track.energy = energy_result["energy"]
                    db.commit()

                return {
                    "track_id": track_id,
                    "track_title": track.title,
                    "track_artist": track.artist,
                    "energy": energy_result.get("energy"),
                    "energy_description": self._get_energy_description(energy_result.get("energy", 0)),
                    "source": "analyzed",
                    "analysis_details": energy_result
                }

            except Exception as e:
                logger.error(f"Error analyzing energy: {e}")
                return {
                    "error": str(e),
                    "track_id": track_id
                }

        @self.server.tool()
        async def analyze_track_structure(track_id: str) -> Dict[str, Any]:
            """
            Analyze the structure of a track (intro, verse, chorus, etc.).

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing structure analysis results
            """
            logger.info(f"Analyze track structure called with track_id: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Perform structure analysis
                analysis_service = AnalysisService(db)
                
                if not track.file_path:
                    return {
                        "error": "Track file path not available"
                    }

                # Analyze structure
                structure_result = await analysis_service.analyze_structure(track.file_path)
                
                if structure_result.get("error"):
                    return {
                        "error": f"Structure analysis failed: {structure_result['error']}"
                    }

                return {
                    "track_id": track_id,
                    "track_title": track.title,
                    "track_artist": track.artist,
                    "structure": structure_result.get("structure", []),
                    "segments": structure_result.get("segments", []),
                    "total_duration": track.duration,
                    "analysis_details": structure_result
                }

            except Exception as e:
                logger.error(f"Error analyzing track structure: {e}")
                return {
                    "error": str(e),
                    "track_id": track_id
                }

        @self.server.tool()
        async def analyze_danceability(track_id: str) -> Dict[str, Any]:
            """
            Analyze the danceability of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing danceability analysis results
            """
            logger.info(f"Analyze danceability called with track_id: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Check if danceability is already available
                if track.danceability:
                    return {
                        "track_id": track_id,
                        "track_title": track.title,
                        "track_artist": track.artist,
                        "danceability": track.danceability,
                        "danceability_description": self._get_danceability_description(track.danceability),
                        "source": "cached",
                        "message": "Danceability retrieved from database"
                    }

                # Perform danceability analysis
                analysis_service = AnalysisService(db)
                
                if not track.file_path:
                    return {
                        "error": "Track file path not available"
                    }

                # Analyze danceability
                danceability_result = await analysis_service.analyze_danceability(track.file_path)
                
                if danceability_result.get("error"):
                    return {
                        "error": f"Danceability analysis failed: {danceability_result['error']}"
                    }

                # Update track with detected danceability
                if "danceability" in danceability_result:
                    track.danceability = danceability_result["danceability"]
                    db.commit()

                danceability_score = danceability_result.get("danceability", 0)
                
                return {
                    "track_id": track_id,
                    "track_title": track.title,
                    "track_artist": track.artist,
                    "danceability": danceability_score,
                    "danceability_description": self._get_danceability_description(danceability_score),
                    "source": "analyzed",
                    "analysis_details": danceability_result
                }

            except Exception as e:
                logger.error(f"Error analyzing danceability: {e}")
                return {
                    "error": str(e),
                    "track_id": track_id
                }

        @self.server.tool()
        async def analyze_audio(track_id: str) -> Dict[str, Any]:
            """
            Perform a comprehensive analysis of a track.

            Args:
                track_id: ID of the track to analyze

            Returns:
                Dictionary containing comprehensive analysis results
            """
            logger.info(f"Comprehensive audio analysis called with track_id: {track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                if not track.file_path:
                    return {
                        "error": "Track file path not available"
                    }

                # Perform comprehensive analysis
                analysis_service = AnalysisService(db)
                
                # Run all analyses
                results = {
                    "track_id": track_id,
                    "track_title": track.title,
                    "track_artist": track.artist,
                    "file_path": track.file_path,
                    "analysis_results": {}
                }

                # BPM Analysis
                if not track.bpm:
                    bpm_result = await analysis_service.analyze_bpm(track.file_path)
                    if not bpm_result.get("error") and "bpm" in bpm_result:
                        track.bpm = bpm_result["bpm"]
                        results["analysis_results"]["bpm"] = bpm_result
                else:
                    results["analysis_results"]["bpm"] = {"bpm": track.bpm, "source": "cached"}

                # Key Analysis
                if not track.key:
                    key_result = await analysis_service.analyze_key(track.file_path)
                    if not key_result.get("error") and "key" in key_result:
                        track.key = key_result["key"]
                        results["analysis_results"]["key"] = key_result
                else:
                    results["analysis_results"]["key"] = {"key": track.key, "source": "cached"}

                # Energy Analysis
                if not track.energy:
                    energy_result = await analysis_service.analyze_energy(track.file_path)
                    if not energy_result.get("error") and "energy" in energy_result:
                        track.energy = energy_result["energy"]
                        results["analysis_results"]["energy"] = energy_result
                else:
                    results["analysis_results"]["energy"] = {"energy": track.energy, "source": "cached"}

                # Danceability Analysis
                if not track.danceability:
                    danceability_result = await analysis_service.analyze_danceability(track.file_path)
                    if not danceability_result.get("error") and "danceability" in danceability_result:
                        track.danceability = danceability_result["danceability"]
                        results["analysis_results"]["danceability"] = danceability_result
                else:
                    results["analysis_results"]["danceability"] = {"danceability": track.danceability, "source": "cached"}

                # Commit any updates
                db.commit()

                # Add summary
                results["summary"] = {
                    "bpm": track.bpm,
                    "key": track.key,
                    "energy": track.energy,
                    "energy_description": self._get_energy_description(track.energy or 0),
                    "danceability": track.danceability,
                    "danceability_description": self._get_danceability_description(track.danceability or 0),
                    "duration": track.duration
                }

                return results

            except Exception as e:
                logger.error(f"Error in comprehensive audio analysis: {e}")
                return {
                    "error": str(e),
                    "track_id": track_id
                }

    def _get_energy_description(self, energy: float) -> str:
        """Get descriptive text for energy level"""
        if energy <= 2:
            return "Very Low - Calm, ambient"
        elif energy <= 4:
            return "Low - Relaxed, mellow"
        elif energy <= 6:
            return "Medium - Moderate energy"
        elif energy <= 8:
            return "High - Energetic, driving"
        else:
            return "Very High - Intense, powerful"

    def _get_danceability_description(self, danceability: float) -> str:
        """Get descriptive text for danceability level"""
        if danceability <= 0.2:
            return "Very Low - Not suitable for dancing"
        elif danceability <= 0.4:
            return "Low - Limited dance appeal"
        elif danceability <= 0.6:
            return "Medium - Moderately danceable"
        elif danceability <= 0.8:
            return "High - Very danceable"
        else:
            return "Very High - Extremely danceable"
