"""
Mix creation tools for MCP server.
Handles timeline operations, transitions, and track compatibility analysis.
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from backend.dependencies import get_db
from backend.models.track import Track
from backend.services.analysis_service import AnalysisService

logger = logging.getLogger(__name__)

class MixCreationTools:
    """Mix creation tools for MCP server"""
    
    def __init__(self, server):
        self.server = server
        self._register_tools()
    
    def _register_tools(self):
        """Register mix creation tools with the MCP server"""
        
        @self.server.tool()
        async def load_track_to_timeline(track_id: str, position: int = 0) -> Dict[str, Any]:
            """
            Load a track to the mix timeline at the specified position.

            Args:
                track_id: ID of the track to load
                position: Position in the timeline (in seconds)

            Returns:
                Dictionary containing operation result
            """
            logger.info(f"Load track to timeline called with track_id: {track_id}, position: {position}")

            try:
                # Get database session
                db = next(get_db())

                # Get track
                track = db.query(Track).filter(Track.id == track_id).first()
                if not track:
                    return {
                        "error": f"Track with ID {track_id} not found"
                    }

                # Simulate loading track to timeline
                # In a real implementation, this would interact with the timeline service
                timeline_track = {
                    "id": track.id,
                    "title": track.title,
                    "artist": track.artist,
                    "duration": track.duration,
                    "bpm": track.bpm,
                    "key": track.key,
                    "position": position,
                    "file_path": track.file_path
                }

                return {
                    "success": True,
                    "message": f"Track '{track.title}' by {track.artist} loaded to timeline at position {position}s",
                    "timeline_track": timeline_track
                }

            except Exception as e:
                logger.error(f"Error loading track to timeline: {e}")
                return {
                    "error": str(e),
                    "success": False
                }

        @self.server.tool()
        async def create_transition(
            from_track_id: str,
            to_track_id: str,
            transition_type: str = "crossfade",
            duration: float = 8.0
        ) -> Dict[str, Any]:
            """
            Create a transition between two tracks.

            Args:
                from_track_id: ID of the source track
                to_track_id: ID of the destination track
                transition_type: Type of transition (crossfade, cut, etc.)
                duration: Duration of the transition in seconds

            Returns:
                Dictionary containing transition details
            """
            logger.info(f"Create transition called: {from_track_id} -> {to_track_id}, type: {transition_type}, duration: {duration}s")

            try:
                # Get database session
                db = next(get_db())

                # Get both tracks
                from_track = db.query(Track).filter(Track.id == from_track_id).first()
                to_track = db.query(Track).filter(Track.id == to_track_id).first()

                if not from_track:
                    return {
                        "error": f"Source track with ID {from_track_id} not found"
                    }

                if not to_track:
                    return {
                        "error": f"Destination track with ID {to_track_id} not found"
                    }

                # Analyze compatibility
                compatibility = self._analyze_track_compatibility(from_track, to_track)

                # Create transition object
                transition = {
                    "from_track": {
                        "id": from_track.id,
                        "title": from_track.title,
                        "artist": from_track.artist,
                        "bpm": from_track.bpm,
                        "key": from_track.key
                    },
                    "to_track": {
                        "id": to_track.id,
                        "title": to_track.title,
                        "artist": to_track.artist,
                        "bpm": to_track.bpm,
                        "key": to_track.key
                    },
                    "transition_type": transition_type,
                    "duration": duration,
                    "compatibility": compatibility,
                    "recommendations": self._get_transition_recommendations(from_track, to_track, compatibility)
                }

                return {
                    "success": True,
                    "message": f"Transition created from '{from_track.title}' to '{to_track.title}'",
                    "transition": transition
                }

            except Exception as e:
                logger.error(f"Error creating transition: {e}")
                return {
                    "error": str(e),
                    "success": False
                }

        @self.server.tool()
        async def get_transition_suggestions(
            from_track_id: str,
            to_track_id: str
        ) -> Dict[str, Any]:
            """
            Get suggestions for transitioning between two tracks.

            Args:
                from_track_id: ID of the source track
                to_track_id: ID of the destination track

            Returns:
                Dictionary containing transition suggestions
            """
            logger.info(f"Get transition suggestions called: {from_track_id} -> {to_track_id}")

            try:
                # Get database session
                db = next(get_db())

                # Get both tracks
                from_track = db.query(Track).filter(Track.id == from_track_id).first()
                to_track = db.query(Track).filter(Track.id == to_track_id).first()

                if not from_track:
                    return {
                        "error": f"Source track with ID {from_track_id} not found"
                    }

                if not to_track:
                    return {
                        "error": f"Destination track with ID {to_track_id} not found"
                    }

                # Analyze compatibility
                compatibility = self._analyze_track_compatibility(from_track, to_track)

                # Generate suggestions
                suggestions = {
                    "track_pair": {
                        "from": f"{from_track.title} by {from_track.artist}",
                        "to": f"{to_track.title} by {to_track.artist}"
                    },
                    "compatibility_score": compatibility["overall_score"],
                    "compatibility_analysis": compatibility,
                    "transition_suggestions": []
                }

                # Add specific transition suggestions based on compatibility
                if compatibility["bpm_compatible"]:
                    suggestions["transition_suggestions"].append({
                        "type": "crossfade",
                        "duration": 8.0,
                        "description": "Standard crossfade - BPMs are compatible",
                        "difficulty": "Easy"
                    })

                if compatibility["key_compatible"]:
                    suggestions["transition_suggestions"].append({
                        "type": "harmonic_mix",
                        "duration": 16.0,
                        "description": "Harmonic mixing - Keys are compatible",
                        "difficulty": "Easy"
                    })

                if not compatibility["bpm_compatible"]:
                    bpm_diff = abs(from_track.bpm - to_track.bpm) if from_track.bpm and to_track.bpm else 0
                    if bpm_diff > 10:
                        suggestions["transition_suggestions"].append({
                            "type": "tempo_transition",
                            "duration": 32.0,
                            "description": f"Gradual tempo change needed (BPM difference: {bpm_diff:.1f})",
                            "difficulty": "Hard"
                        })
                    else:
                        suggestions["transition_suggestions"].append({
                            "type": "quick_cut",
                            "duration": 2.0,
                            "description": "Quick cut transition - Small BPM difference",
                            "difficulty": "Medium"
                        })

                # Energy-based suggestions
                if from_track.energy and to_track.energy:
                    energy_diff = to_track.energy - from_track.energy
                    if energy_diff > 2:
                        suggestions["transition_suggestions"].append({
                            "type": "build_up",
                            "duration": 16.0,
                            "description": "Build-up transition - Increasing energy",
                            "difficulty": "Medium"
                        })
                    elif energy_diff < -2:
                        suggestions["transition_suggestions"].append({
                            "type": "breakdown",
                            "duration": 12.0,
                            "description": "Breakdown transition - Decreasing energy",
                            "difficulty": "Medium"
                        })

                return suggestions

            except Exception as e:
                logger.error(f"Error getting transition suggestions: {e}")
                return {
                    "error": str(e)
                }

        @self.server.tool()
        async def get_compatible_tracks(
            track_id: str,
            compatibility_type: str = "all",
            limit: int = 10
        ) -> Dict[str, Any]:
            """
            Find tracks that are compatible with the given track for mixing.

            Args:
                track_id: ID of the reference track
                compatibility_type: Type of compatibility to check (bpm, key, energy, all)
                limit: Maximum number of compatible tracks to return

            Returns:
                Dictionary containing compatible tracks
            """
            logger.info(f"Get compatible tracks called with track_id: {track_id}, type: {compatibility_type}, limit: {limit}")

            try:
                # Get database session
                db = next(get_db())

                # Get reference track
                reference_track = db.query(Track).filter(Track.id == track_id).first()
                if not reference_track:
                    return {
                        "error": f"Reference track with ID {track_id} not found"
                    }

                # Build compatibility query
                query = db.query(Track).filter(Track.id != track_id)

                compatible_tracks = []
                
                # Get all potential tracks
                all_tracks = query.all()

                # Analyze compatibility for each track
                for track in all_tracks:
                    compatibility = self._analyze_track_compatibility(reference_track, track)
                    
                    # Filter based on compatibility type
                    is_compatible = False
                    if compatibility_type == "all":
                        is_compatible = compatibility["overall_score"] >= 0.6
                    elif compatibility_type == "bpm":
                        is_compatible = compatibility["bpm_compatible"]
                    elif compatibility_type == "key":
                        is_compatible = compatibility["key_compatible"]
                    elif compatibility_type == "energy":
                        is_compatible = compatibility["energy_compatible"]

                    if is_compatible:
                        track_info = {
                            "id": track.id,
                            "title": track.title,
                            "artist": track.artist,
                            "bpm": track.bpm,
                            "key": track.key,
                            "energy": track.energy,
                            "compatibility_score": compatibility["overall_score"],
                            "compatibility_details": compatibility
                        }
                        compatible_tracks.append(track_info)

                # Sort by compatibility score
                compatible_tracks.sort(key=lambda x: x["compatibility_score"], reverse=True)

                # Limit results
                compatible_tracks = compatible_tracks[:limit]

                return {
                    "reference_track": {
                        "id": reference_track.id,
                        "title": reference_track.title,
                        "artist": reference_track.artist,
                        "bpm": reference_track.bpm,
                        "key": reference_track.key,
                        "energy": reference_track.energy
                    },
                    "compatibility_type": compatibility_type,
                    "compatible_tracks": compatible_tracks,
                    "count": len(compatible_tracks),
                    "message": f"Found {len(compatible_tracks)} compatible tracks"
                }

            except Exception as e:
                logger.error(f"Error finding compatible tracks: {e}")
                return {
                    "error": str(e)
                }

    def _analyze_track_compatibility(self, track1: Track, track2: Track) -> Dict[str, Any]:
        """
        Analyze compatibility between two tracks for mixing.
        
        Args:
            track1: First track
            track2: Second track
            
        Returns:
            Dictionary containing compatibility analysis
        """
        compatibility = {
            "bpm_compatible": False,
            "key_compatible": False,
            "energy_compatible": False,
            "overall_score": 0.0,
            "details": {}
        }

        # BPM compatibility
        if track1.bpm and track2.bpm:
            bpm_diff = abs(track1.bpm - track2.bpm)
            bpm_ratio = max(track1.bpm, track2.bpm) / min(track1.bpm, track2.bpm)
            
            # Compatible if within 5 BPM or exact ratio (2:1, 3:2, etc.)
            compatibility["bpm_compatible"] = (
                bpm_diff <= 5 or 
                abs(bpm_ratio - 2.0) < 0.1 or 
                abs(bpm_ratio - 1.5) < 0.1 or
                abs(bpm_ratio - 0.75) < 0.1 or
                abs(bpm_ratio - 0.5) < 0.1
            )
            
            compatibility["details"]["bpm_difference"] = bpm_diff
            compatibility["details"]["bpm_ratio"] = bpm_ratio

        # Key compatibility (Camelot wheel)
        if track1.key and track2.key:
            compatibility["key_compatible"] = self._are_keys_compatible(track1.key, track2.key)
            compatibility["details"]["key_relationship"] = self._get_key_relationship(track1.key, track2.key)

        # Energy compatibility
        if track1.energy and track2.energy:
            energy_diff = abs(track1.energy - track2.energy)
            compatibility["energy_compatible"] = energy_diff <= 2
            compatibility["details"]["energy_difference"] = energy_diff

        # Calculate overall score
        score_components = []
        if track1.bpm and track2.bpm:
            score_components.append(1.0 if compatibility["bpm_compatible"] else 0.0)
        if track1.key and track2.key:
            score_components.append(1.0 if compatibility["key_compatible"] else 0.0)
        if track1.energy and track2.energy:
            score_components.append(1.0 if compatibility["energy_compatible"] else 0.0)

        if score_components:
            compatibility["overall_score"] = sum(score_components) / len(score_components)

        return compatibility

    def _are_keys_compatible(self, key1: str, key2: str) -> bool:
        """Check if two musical keys are compatible for mixing"""
        # Simplified key compatibility - in practice, this would use the Camelot wheel
        # or Circle of Fifths for more accurate harmonic mixing
        
        # Same key is always compatible
        if key1 == key2:
            return True
        
        # Define some basic compatible key relationships
        compatible_keys = {
            "1A": ["1A", "1B", "2A", "12A"],
            "1B": ["1A", "1B", "2B", "12B"],
            "2A": ["1A", "2A", "2B", "3A"],
            "2B": ["1B", "2A", "2B", "3B"],
            # Add more relationships as needed
        }
        
        return key2 in compatible_keys.get(key1, [])

    def _get_key_relationship(self, key1: str, key2: str) -> str:
        """Get the relationship between two keys"""
        if key1 == key2:
            return "Same key"
        elif self._are_keys_compatible(key1, key2):
            return "Compatible keys"
        else:
            return "Incompatible keys"

    def _get_transition_recommendations(self, from_track: Track, to_track: Track, compatibility: Dict[str, Any]) -> List[str]:
        """Get specific recommendations for the transition"""
        recommendations = []
        
        if compatibility["overall_score"] >= 0.8:
            recommendations.append("Excellent compatibility - any transition type will work well")
        elif compatibility["overall_score"] >= 0.6:
            recommendations.append("Good compatibility - standard crossfade recommended")
        else:
            recommendations.append("Challenging transition - consider using effects or longer transition time")
        
        if not compatibility["bpm_compatible"]:
            recommendations.append("BPM mismatch - consider tempo adjustment or quick cut")
        
        if not compatibility["key_compatible"]:
            recommendations.append("Key clash - use EQ or effects to smooth the transition")
        
        if compatibility.get("details", {}).get("energy_difference", 0) > 3:
            recommendations.append("Large energy difference - use gradual transition or breakdown/buildup")
        
        return recommendations
