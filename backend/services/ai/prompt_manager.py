"""
Prompt Manager for AI services.

This module provides functions to manage system prompts used by AI providers.
It allows getting, saving, and resetting custom prompts.
"""

import json
import os
from typing import Dict, Optional, List
import logging

from backend.config import settings

logger = logging.getLogger(__name__)

# Path to the custom prompts file
CUSTOM_PROMPTS_FILE = os.path.join(settings.BASE_DIR, "data", "custom_prompts.json")

def get_system_prompt(prompt_id: str) -> str:
    """
    Get a system prompt by ID, with custom override if available.
    
    Args:
        prompt_id: The ID of the prompt to retrieve
        
    Returns:
        The prompt text, either custom or default
    """
    # Check if custom prompts are enabled
    if not settings.SYSTEM_PROMPTS_ENABLED:
        return settings.DEFAULT_SYSTEM_PROMPTS.get(prompt_id, "")
    
    # Check if custom prompts exist
    if os.path.exists(CUSTOM_PROMPTS_FILE):
        try:
            with open(CUSTOM_PROMPTS_FILE, 'r') as f:
                custom_prompts = json.load(f)
                if prompt_id in custom_prompts:
                    logger.debug(f"Using custom prompt for {prompt_id}")
                    return custom_prompts[prompt_id]
        except Exception as e:
            logger.error(f"Error loading custom prompts: {str(e)}")
    
    # Fall back to default prompts
    logger.debug(f"Using default prompt for {prompt_id}")
    return settings.DEFAULT_SYSTEM_PROMPTS.get(prompt_id, "")

def save_system_prompt(prompt_id: str, prompt_text: str) -> bool:
    """
    Save a custom system prompt.
    
    Args:
        prompt_id: The ID of the prompt to save
        prompt_text: The text of the prompt
        
    Returns:
        True if successful, False otherwise
    """
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(CUSTOM_PROMPTS_FILE), exist_ok=True)
    
    custom_prompts = {}
    
    # Load existing custom prompts if available
    if os.path.exists(CUSTOM_PROMPTS_FILE):
        try:
            with open(CUSTOM_PROMPTS_FILE, 'r') as f:
                custom_prompts = json.load(f)
        except Exception as e:
            logger.error(f"Error loading custom prompts: {str(e)}")
            return False
    
    # Update the prompt
    custom_prompts[prompt_id] = prompt_text
    
    # Save back to file
    try:
        with open(CUSTOM_PROMPTS_FILE, 'w') as f:
            json.dump(custom_prompts, f, indent=2)
        logger.info(f"Saved custom prompt for {prompt_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving custom prompt: {str(e)}")
        return False

def reset_system_prompt(prompt_id: str) -> bool:
    """
    Reset a system prompt to its default value.
    
    Args:
        prompt_id: The ID of the prompt to reset
        
    Returns:
        True if successful, False otherwise
    """
    if not os.path.exists(CUSTOM_PROMPTS_FILE):
        logger.debug(f"No custom prompts file exists, nothing to reset for {prompt_id}")
        return False
    
    try:
        with open(CUSTOM_PROMPTS_FILE, 'r') as f:
            custom_prompts = json.load(f)
        
        if prompt_id in custom_prompts:
            del custom_prompts[prompt_id]
            
            with open(CUSTOM_PROMPTS_FILE, 'w') as f:
                json.dump(custom_prompts, f, indent=2)
            
            logger.info(f"Reset custom prompt for {prompt_id}")
            return True
        else:
            logger.debug(f"No custom prompt found for {prompt_id}, nothing to reset")
            return False
    except Exception as e:
        logger.error(f"Error resetting custom prompt: {str(e)}")
        return False

def get_all_prompt_ids() -> List[str]:
    """
    Get all available prompt IDs.
    
    Returns:
        List of prompt IDs
    """
    return list(settings.DEFAULT_SYSTEM_PROMPTS.keys())

def get_all_prompts() -> Dict[str, str]:
    """
    Get all system prompts with their current values.
    
    Returns:
        Dictionary of prompt IDs to prompt texts
    """
    prompt_ids = get_all_prompt_ids()
    return {prompt_id: get_system_prompt(prompt_id) for prompt_id in prompt_ids}

def get_prompt_variables(prompt_id: str) -> List[str]:
    """
    Get the variables used in a prompt.
    
    Args:
        prompt_id: The ID of the prompt
        
    Returns:
        List of variable names
    """
    variables = []
    
    # Define known variables for each prompt type
    known_variables = {
        "question_answering": ["question", "context_text"],
        "collection_analysis": ["tracks_summary"],
        "album_artwork_analysis": [],
        "audio_analysis": [],
        "mix_flow_analysis": ["track_sequence_json"],
        "style_generation": ["description", "constraints"],
        "transition_suggestions": ["current_track_details", "next_track_details"]
    }
    
    if prompt_id in known_variables:
        return known_variables[prompt_id]
    
    return variables
