"""
MCP Server Registry

This module implements a registry system to manage multiple MCP servers.
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Any, Optional, Set
import uuid
import threading

from backend.config import settings
from backend.services.ai.mcp_client import DJMixConstructorMCPClient

logger = logging.getLogger(__name__)

class MCPServerConfig:
    """Configuration for an MCP server."""
    
    def __init__(
        self,
        server_id: str,
        name: str,
        description: str,
        host: str,
        port: int,
        transport: str = "stdio",
        enabled: bool = True,
        server_command: Optional[List[str]] = None,
        tool_prefixes: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize an MCP server configuration.
        
        Args:
            server_id: Unique identifier for the server
            name: Human-readable name for the server
            description: Description of the server
            host: Server host
            port: Server port
            transport: Transport protocol (stdio, http, websocket)
            enabled: Whether the server is enabled
            server_command: Command to start the server process
            tool_prefixes: Prefixes for tools provided by this server
            metadata: Additional metadata for the server
        """
        self.server_id = server_id
        self.name = name
        self.description = description
        self.host = host
        self.port = port
        self.transport = transport
        self.enabled = enabled
        self.server_command = server_command
        self.tool_prefixes = tool_prefixes or []
        self.metadata = metadata or {}
        self.created_at = time.time()
        self.updated_at = time.time()
        self.last_connected_at = None
        self.status = "disconnected"
        self.error = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the configuration to a dictionary.
        
        Returns:
            Dictionary representation of the configuration
        """
        return {
            "server_id": self.server_id,
            "name": self.name,
            "description": self.description,
            "host": self.host,
            "port": self.port,
            "transport": self.transport,
            "enabled": self.enabled,
            "server_command": self.server_command,
            "tool_prefixes": self.tool_prefixes,
            "metadata": self.metadata,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "last_connected_at": self.last_connected_at,
            "status": self.status,
            "error": self.error
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPServerConfig':
        """
        Create a configuration from a dictionary.
        
        Args:
            data: Dictionary representation of the configuration
            
        Returns:
            MCPServerConfig instance
        """
        config = cls(
            server_id=data.get("server_id", str(uuid.uuid4())),
            name=data.get("name", ""),
            description=data.get("description", ""),
            host=data.get("host", "127.0.0.1"),
            port=data.get("port", 8000),
            transport=data.get("transport", "stdio"),
            enabled=data.get("enabled", True),
            server_command=data.get("server_command"),
            tool_prefixes=data.get("tool_prefixes", []),
            metadata=data.get("metadata", {})
        )
        
        # Set additional fields
        if "created_at" in data:
            config.created_at = data["created_at"]
        if "updated_at" in data:
            config.updated_at = data["updated_at"]
        if "last_connected_at" in data:
            config.last_connected_at = data["last_connected_at"]
        if "status" in data:
            config.status = data["status"]
        if "error" in data:
            config.error = data["error"]
        
        return config

class MCPServerRegistry:
    """Registry for MCP servers."""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the MCP server registry.
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file or os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "data",
            "mcp_servers.json"
        )
        self.servers: Dict[str, MCPServerConfig] = {}
        self.clients: Dict[str, DJMixConstructorMCPClient] = {}
        self.lock = threading.RLock()
        
        # Create default server if no config file exists
        if not os.path.exists(self.config_file):
            self._create_default_server()
        else:
            self._load_config()
    
    def _create_default_server(self) -> None:
        """Create a default server configuration."""
        default_server = MCPServerConfig(
            server_id="default",
            name="Default MCP Server",
            description="Default MCP server for DJ Mix Constructor",
            host=settings.MCP_SERVER_HOST,
            port=settings.MCP_SERVER_PORT,
            transport=settings.MCP_TRANSPORT,
            enabled=settings.MCP_ENABLED
        )
        
        with self.lock:
            self.servers["default"] = default_server
            self._save_config()
    
    def _load_config(self) -> None:
        """Load server configurations from the config file."""
        try:
            with open(self.config_file, "r") as f:
                data = json.load(f)
            
            with self.lock:
                self.servers = {}
                for server_data in data:
                    server_config = MCPServerConfig.from_dict(server_data)
                    self.servers[server_config.server_id] = server_config
            
            logger.info(f"Loaded {len(self.servers)} MCP server configurations")
        
        except Exception as e:
            logger.error(f"Error loading MCP server configurations: {str(e)}")
            self._create_default_server()
    
    def _save_config(self) -> None:
        """Save server configurations to the config file."""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with self.lock:
                data = [server.to_dict() for server in self.servers.values()]
            
            with open(self.config_file, "w") as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Saved {len(self.servers)} MCP server configurations")
        
        except Exception as e:
            logger.error(f"Error saving MCP server configurations: {str(e)}")
    
    def get_servers(self) -> List[Dict[str, Any]]:
        """
        Get all server configurations.
        
        Returns:
            List of server configurations
        """
        with self.lock:
            return [server.to_dict() for server in self.servers.values()]
    
    def get_server(self, server_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a server configuration.
        
        Args:
            server_id: Server ID
            
        Returns:
            Server configuration or None if not found
        """
        with self.lock:
            server = self.servers.get(server_id)
            return server.to_dict() if server else None
    
    def add_server(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new server configuration.
        
        Args:
            config: Server configuration
            
        Returns:
            Added server configuration
        """
        # Generate a server ID if not provided
        if "server_id" not in config:
            config["server_id"] = str(uuid.uuid4())
        
        # Create the server configuration
        server_config = MCPServerConfig.from_dict(config)
        
        with self.lock:
            # Check if a server with this ID already exists
            if server_config.server_id in self.servers:
                raise ValueError(f"Server with ID {server_config.server_id} already exists")
            
            # Add the server
            self.servers[server_config.server_id] = server_config
            self._save_config()
        
        logger.info(f"Added MCP server: {server_config.name} ({server_config.server_id})")
        return server_config.to_dict()
    
    def update_server(self, server_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a server configuration.
        
        Args:
            server_id: Server ID
            config: Updated server configuration
            
        Returns:
            Updated server configuration
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Get the existing server
            existing_server = self.servers[server_id]
            
            # Update the server configuration
            for key, value in config.items():
                if key not in ["server_id", "created_at"]:
                    setattr(existing_server, key, value)
            
            # Update the updated_at timestamp
            existing_server.updated_at = time.time()
            
            # Save the configuration
            self._save_config()
        
        logger.info(f"Updated MCP server: {existing_server.name} ({server_id})")
        return existing_server.to_dict()
    
    def delete_server(self, server_id: str) -> bool:
        """
        Delete a server configuration.
        
        Args:
            server_id: Server ID
            
        Returns:
            True if the server was deleted, False otherwise
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                return False
            
            # Disconnect the client if connected
            if server_id in self.clients:
                asyncio.create_task(self.disconnect_server(server_id))
                del self.clients[server_id]
            
            # Delete the server
            server_name = self.servers[server_id].name
            del self.servers[server_id]
            
            # Save the configuration
            self._save_config()
        
        logger.info(f"Deleted MCP server: {server_name} ({server_id})")
        return True
    
    def enable_server(self, server_id: str) -> Dict[str, Any]:
        """
        Enable a server.
        
        Args:
            server_id: Server ID
            
        Returns:
            Updated server configuration
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Enable the server
            self.servers[server_id].enabled = True
            self.servers[server_id].updated_at = time.time()
            
            # Save the configuration
            self._save_config()
        
        logger.info(f"Enabled MCP server: {self.servers[server_id].name} ({server_id})")
        return self.servers[server_id].to_dict()
    
    def disable_server(self, server_id: str) -> Dict[str, Any]:
        """
        Disable a server.
        
        Args:
            server_id: Server ID
            
        Returns:
            Updated server configuration
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Disable the server
            self.servers[server_id].enabled = False
            self.servers[server_id].updated_at = time.time()
            
            # Disconnect the client if connected
            if server_id in self.clients:
                asyncio.create_task(self.disconnect_server(server_id))
                del self.clients[server_id]
            
            # Save the configuration
            self._save_config()
        
        logger.info(f"Disabled MCP server: {self.servers[server_id].name} ({server_id})")
        return self.servers[server_id].to_dict()
    
    async def connect_server(self, server_id: str) -> Dict[str, Any]:
        """
        Connect to a server.
        
        Args:
            server_id: Server ID
            
        Returns:
            Server status
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Check if the server is enabled
            if not self.servers[server_id].enabled:
                raise ValueError(f"Server {server_id} is disabled")
            
            # Get the server configuration
            server_config = self.servers[server_id]
            
            # Create a client if it doesn't exist
            if server_id not in self.clients:
                self.clients[server_id] = DJMixConstructorMCPClient()
            
            # Update server status
            server_config.status = "connecting"
            server_config.error = None
        
        try:
            # Connect to the server
            client = self.clients[server_id]
            
            # Prepare server command if needed
            server_command = None
            if server_config.server_command:
                server_command = server_config.server_command
            
            # Connect to the server
            connected = await client.connect(server_command)
            
            with self.lock:
                if connected:
                    # Update server status
                    server_config.status = "connected"
                    server_config.last_connected_at = time.time()
                    server_config.error = None
                    
                    logger.info(f"Connected to MCP server: {server_config.name} ({server_id})")
                else:
                    # Update server status
                    server_config.status = "error"
                    server_config.error = "Failed to connect to server"
                    
                    # Remove the client
                    del self.clients[server_id]
                    
                    logger.error(f"Failed to connect to MCP server: {server_config.name} ({server_id})")
            
            # Save the configuration
            self._save_config()
            
            return server_config.to_dict()
        
        except Exception as e:
            with self.lock:
                # Update server status
                server_config.status = "error"
                server_config.error = str(e)
                
                # Remove the client
                if server_id in self.clients:
                    del self.clients[server_id]
                
                # Save the configuration
                self._save_config()
            
            logger.error(f"Error connecting to MCP server {server_id}: {str(e)}")
            raise
    
    async def disconnect_server(self, server_id: str) -> Dict[str, Any]:
        """
        Disconnect from a server.
        
        Args:
            server_id: Server ID
            
        Returns:
            Server status
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Check if the client exists
            if server_id not in self.clients:
                # Server is already disconnected
                self.servers[server_id].status = "disconnected"
                self._save_config()
                return self.servers[server_id].to_dict()
            
            # Get the client
            client = self.clients[server_id]
        
        try:
            # Disconnect from the server
            await client.disconnect()
            
            with self.lock:
                # Update server status
                self.servers[server_id].status = "disconnected"
                self.servers[server_id].error = None
                
                # Remove the client
                del self.clients[server_id]
                
                # Save the configuration
                self._save_config()
            
            logger.info(f"Disconnected from MCP server: {self.servers[server_id].name} ({server_id})")
            return self.servers[server_id].to_dict()
        
        except Exception as e:
            with self.lock:
                # Update server status
                self.servers[server_id].status = "error"
                self.servers[server_id].error = str(e)
                
                # Remove the client
                if server_id in self.clients:
                    del self.clients[server_id]
                
                # Save the configuration
                self._save_config()
            
            logger.error(f"Error disconnecting from MCP server {server_id}: {str(e)}")
            raise
    
    async def get_server_tools(self, server_id: str) -> List[Dict[str, Any]]:
        """
        Get tools from a server.
        
        Args:
            server_id: Server ID
            
        Returns:
            List of tools
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Check if the server is enabled
            if not self.servers[server_id].enabled:
                raise ValueError(f"Server {server_id} is disabled")
            
            # Check if the client exists
            if server_id not in self.clients:
                # Connect to the server
                await self.connect_server(server_id)
            
            # Get the client
            client = self.clients[server_id]
        
        try:
            # Get tools from the server
            tools = await client.get_tools()
            
            # Add server ID to each tool
            for tool in tools:
                tool["server_id"] = server_id
            
            return tools
        
        except Exception as e:
            logger.error(f"Error getting tools from MCP server {server_id}: {str(e)}")
            raise
    
    async def call_server_tool(
        self,
        server_id: str,
        tool_name: str,
        parameters: Dict[str, Any],
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Call a tool on a server.
        
        Args:
            server_id: Server ID
            tool_name: Tool name
            parameters: Tool parameters
            use_cache: Whether to use the cache
            
        Returns:
            Tool result
        """
        with self.lock:
            # Check if the server exists
            if server_id not in self.servers:
                raise ValueError(f"Server with ID {server_id} not found")
            
            # Check if the server is enabled
            if not self.servers[server_id].enabled:
                raise ValueError(f"Server {server_id} is disabled")
            
            # Check if the client exists
            if server_id not in self.clients:
                # Connect to the server
                await self.connect_server(server_id)
            
            # Get the client
            client = self.clients[server_id]
        
        try:
            # Call the tool
            result = await client.call_tool(tool_name, parameters, use_cache=use_cache)
            
            # Add server ID to the result
            result["server_id"] = server_id
            
            return result
        
        except Exception as e:
            logger.error(f"Error calling tool {tool_name} on MCP server {server_id}: {str(e)}")
            raise

# Create a singleton instance
mcp_registry = MCPServerRegistry()
