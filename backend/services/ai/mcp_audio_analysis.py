"""
MCP server for audio analysis using librosa.
This module provides a Model Context Protocol (MCP) server that exposes
our existing librosa-based audio analysis capabilities to AI models.
"""

import os
import tempfile
import logging
import asyncio
import json
import time
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

# Import FastMCP
from fastmcp import FastMCP

# Import audio processing libraries
import librosa
import soundfile as sf
import requests
from pytubefix import YouTube

# Import our existing services
from backend.services.audio_analyzer_service import AudioAnalyzerService
from backend.services.beat_grid_service import BeatGridService
# Removed: AudioSegmentationService - replaced with Phase 5 enhanced segmentation in AudioAnalyzer

# Import cache
from backend.services.ai.mcp_audio_analysis_cache import audio_analysis_cache

logger = logging.getLogger(__name__)

class MCPAudioAnalysisServer:
    """MCP server for audio analysis using librosa"""

    def __init__(self, db=None):
        """
        Initialize the MCP audio analysis server

        Args:
            db: Database session (optional, can be None for standalone mode)
        """
        self.db = db

        # Initialize services if db is provided
        if db is not None:
            self.audio_analyzer = AudioAnalyzerService(db)
            self.beat_grid_service = BeatGridService(db)
            # Note: Segmentation now handled by enhanced AudioAnalyzer (Phase 5)
        else:
            # In standalone mode, we'll use direct librosa calls
            self.audio_analyzer = None
            self.beat_grid_service = None

        # Create MCP server
        self.mcp = FastMCP(
            "DJ Mix Constructor Audio Analysis",
            dependencies=["librosa", "numpy", "requests", "pytubefix"],
            description="Advanced audio analysis for DJ mixing"
        )

        # Register tools
        self._register_tools()

    def _register_tools(self):
        """Register all tools with the MCP server"""

        @self.mcp.tool()
        def analyze_track(file_path: str) -> dict:
            """
            Analyze a track and return comprehensive audio features

            Args:
                file_path: Path to the audio file

            Returns:
                Dictionary with audio features including BPM, key, energy, etc.
            """
            # Check cache first
            cached_result = audio_analysis_cache.get(file_path, 'track', {})
            if cached_result is not None:
                logger.info(f"Using cached analysis for {file_path}")
                return cached_result

            # Not in cache, perform analysis
            start_time = time.time()

            if self.audio_analyzer:
                # Use our existing service if available
                result = asyncio.run(self.audio_analyzer.analyze_track(file_path))
            else:
                # Fallback to direct librosa implementation
                result = self._analyze_with_librosa(file_path)

            # Cache the result
            audio_analysis_cache.set(file_path, 'track', result, {})

            logger.info(f"Analyzed track in {time.time() - start_time:.2f} seconds: {file_path}")
            return result

        @self.mcp.tool()
        def extract_beat_grid(file_path: str, enhanced: bool = True) -> dict:
            """
            Extract beat grid from a track

            Args:
                file_path: Path to the audio file
                enhanced: Whether to use enhanced beat detection

            Returns:
                Dictionary with beat grid data
            """
            # Check cache first
            options = {"enhanced": enhanced}
            cached_result = audio_analysis_cache.get(file_path, 'beat_grid', options)
            if cached_result is not None:
                logger.info(f"Using cached beat grid for {file_path}")
                return cached_result

            # Not in cache, perform analysis
            start_time = time.time()

            if self.beat_grid_service:
                # Use our existing service if available
                result = self.beat_grid_service.extract_beat_grid(file_path, enhanced)
            else:
                # Fallback to direct librosa implementation
                result = self._extract_beat_grid_with_librosa(file_path, enhanced)

            # Cache the result
            audio_analysis_cache.set(file_path, 'beat_grid', result, options)

            logger.info(f"Extracted beat grid in {time.time() - start_time:.2f} seconds: {file_path}")
            return result

        @self.mcp.tool()
        def detect_segments(file_path: str) -> List[Dict[str, Any]]:
            """
            Detect segments in a track (intro, verse, chorus, etc.)

            Args:
                file_path: Path to the audio file

            Returns:
                List of detected segments
            """
            # Check cache first
            cached_result = audio_analysis_cache.get(file_path, 'segments', {})
            if cached_result is not None:
                logger.info(f"Using cached segments for {file_path}")
                return cached_result

            # Not in cache, perform analysis
            start_time = time.time()

            if self.audio_analyzer:
                # Use enhanced analyzer with Phase 5 segmentation
                analysis_result = asyncio.run(self.audio_analyzer.analyze_track(file_path))
                # Extract segmentation data from enhanced analysis
                if "advanced_segmentation" in analysis_result:
                    result = analysis_result["advanced_segmentation"].get("segments", [])
                else:
                    result = self._detect_segments_with_librosa(file_path)
            else:
                # Fallback to direct librosa implementation
                result = self._detect_segments_with_librosa(file_path)

            # Cache the result
            audio_analysis_cache.set(file_path, 'segments', result, {})

            logger.info(f"Detected segments in {time.time() - start_time:.2f} seconds: {file_path}")
            return result

        @self.mcp.tool()
        def extract_chroma(file_path: str, hop_length: int = 512, n_chroma: int = 12) -> str:
            """
            Extract chroma features from a track

            Args:
                file_path: Path to the audio file
                hop_length: Number of samples between frames
                n_chroma: Number of chroma bins

            Returns:
                Path to CSV file containing chroma data
            """
            # Check cache first
            options = {"hop_length": hop_length, "n_chroma": n_chroma}
            cached_result = audio_analysis_cache.get(file_path, 'chroma_csv', options)
            if cached_result is not None and os.path.exists(cached_result):
                logger.info(f"Using cached chroma features for {file_path}")
                return cached_result

            # Not in cache, perform extraction
            start_time = time.time()

            result = self._extract_chroma_to_csv(file_path, hop_length, n_chroma)

            # Cache the result
            audio_analysis_cache.set(file_path, 'chroma_csv', result, options)

            logger.info(f"Extracted chroma features in {time.time() - start_time:.2f} seconds: {file_path}")
            return result

        @self.mcp.tool()
        def extract_mfcc(file_path: str, n_mfcc: int = 13) -> str:
            """
            Extract MFCC features from a track

            Args:
                file_path: Path to the audio file
                n_mfcc: Number of MFCC coefficients

            Returns:
                Path to CSV file containing MFCC data
            """
            # Check cache first
            options = {"n_mfcc": n_mfcc}
            cached_result = audio_analysis_cache.get(file_path, 'mfcc_csv', options)
            if cached_result is not None and os.path.exists(cached_result):
                logger.info(f"Using cached MFCC features for {file_path}")
                return cached_result

            # Not in cache, perform extraction
            start_time = time.time()

            result = self._extract_mfcc_to_csv(file_path, n_mfcc)

            # Cache the result
            audio_analysis_cache.set(file_path, 'mfcc_csv', result, options)

            logger.info(f"Extracted MFCC features in {time.time() - start_time:.2f} seconds: {file_path}")
            return result

        @self.mcp.tool()
        def download_from_url(url: str) -> str:
            """
            Download audio from a URL

            Args:
                url: URL to download from

            Returns:
                Path to the downloaded file
            """
            if not url.endswith(".mp3") and not url.endswith(".wav"):
                raise ValueError(f"URL: {url} is not a valid audio file")

            response = requests.get(url)
            if response.status_code == 200:
                file_path = os.path.join(tempfile.gettempdir(), "downloaded_file")
                with open(file_path, "wb") as file:
                    file.write(response.content)
                return file_path
            else:
                raise ValueError(f"Failed to download file from URL: {url}")

        @self.mcp.tool()
        def download_from_youtube(youtube_url: str) -> str:
            """
            Download audio from a YouTube URL

            Args:
                youtube_url: YouTube URL to download from

            Returns:
                Path to the downloaded file
            """
            try:
                yt = YouTube(youtube_url)
                ys = yt.streams.get_audio_only()
                path = ys.download(filename=yt.video_id + ".mp4", output_path=tempfile.gettempdir())
                return path
            except Exception as e:
                logger.error(f"Error downloading from YouTube: {str(e)}")
                raise ValueError(f"Failed to download from YouTube: {str(e)}")

    def _analyze_with_librosa(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze a track with librosa (standalone implementation)

        Args:
            file_path: Path to the audio file

        Returns:
            Dictionary with audio features
        """
        # Load the audio file
        y, sr = librosa.load(file_path, sr=None, duration=120)  # Analyze first 2 minutes

        features = {}

        # Extract tempo and beat frames
        tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr)
        features["tempo"] = float(tempo)

        # Extract key
        chroma = librosa.feature.chroma_cqt(y=y, sr=sr)
        key_weights = np.sum(chroma, axis=1)
        key_index = np.argmax(key_weights)
        pitch_classes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        pitch_class = pitch_classes[key_index]

        # Determine if major or minor
        major_weight = key_weights[key_index] + key_weights[(key_index + 4) % 12] + key_weights[(key_index + 7) % 12]
        minor_weight = key_weights[key_index] + key_weights[(key_index + 3) % 12] + key_weights[(key_index + 7) % 12]
        is_major = major_weight >= minor_weight

        features["key"] = f"{pitch_class}{'m' if not is_major else ''}"

        # Extract energy
        rms = librosa.feature.rms(y=y)[0]
        energy = float(np.mean(rms))
        features["energy"] = energy

        # Extract duration
        features["duration"] = float(librosa.get_duration(y=y, sr=sr))

        return features

    def _extract_beat_grid_with_librosa(self, file_path: str, enhanced: bool = True) -> Dict[str, Any]:
        """
        Extract beat grid with librosa (standalone implementation)

        Args:
            file_path: Path to the audio file
            enhanced: Whether to use enhanced beat detection

        Returns:
            Dictionary with beat grid data
        """
        # Load the audio file
        y, sr = librosa.load(file_path, sr=None)

        # Extract tempo and beat frames
        tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr)

        # Convert frames to time (seconds)
        beat_times = librosa.frames_to_time(beat_frames, sr=sr)

        # Calculate confidence based on beat strength
        onset_env = librosa.onset.onset_strength(y=y, sr=sr)
        beat_strengths = onset_env[beat_frames]
        confidence = float(np.mean(beat_strengths) / np.max(onset_env)) if len(beat_strengths) > 0 and np.max(onset_env) > 0 else 0.0

        return {
            "tempo": float(tempo),
            "beat_times": beat_times.tolist(),
            "confidence": confidence
        }

    def _detect_segments_with_librosa(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Detect segments with librosa (standalone implementation)

        Args:
            file_path: Path to the audio file

        Returns:
            List of detected segments
        """
        # Load the audio file
        y, sr = librosa.load(file_path, sr=None)

        # Extract features for segmentation
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)

        # Perform agglomerative clustering for segmentation
        boundaries = librosa.segment.agglomerative(mfcc, k=6)

        # Convert frame indices to time
        boundary_times = librosa.frames_to_time(boundaries, sr=sr)

        # Create segments from boundaries
        segments = []
        segment_types = ["intro", "verse", "chorus", "break", "outro", "custom"]

        # Add segments
        for i in range(len(boundary_times) - 1):
            segment_type = segment_types[min(i, len(segment_types) - 1)]
            segments.append({
                "type": segment_type,
                "start_time": float(boundary_times[i]),
                "end_time": float(boundary_times[i + 1]),
                "confidence": 0.7,
                "label": segment_type.capitalize()
            })

        return segments

    def _extract_chroma_to_csv(self, file_path: str, hop_length: int = 512, n_chroma: int = 12) -> str:
        """
        Extract chroma features and save to CSV

        Args:
            file_path: Path to the audio file
            hop_length: Number of samples between frames
            n_chroma: Number of chroma bins

        Returns:
            Path to CSV file containing chroma data
        """
        # Load the audio file
        y, sr = librosa.load(file_path, sr=None)

        # Extract chroma features
        chroma = librosa.feature.chroma_cqt(y=y, sr=sr, hop_length=hop_length, n_chroma=n_chroma)

        # Convert to CSV format
        notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        time_frames = np.arange(chroma.shape[1])
        time_seconds = librosa.frames_to_time(time_frames, sr=sr, hop_length=hop_length)

        # Create CSV file
        file_name = os.path.basename(file_path).split('.')[0] + "_chroma.csv"
        csv_path = os.path.join(tempfile.gettempdir(), file_name)

        with open(csv_path, "w") as f:
            f.write("note,time,amplitude\n")
            for i, note in enumerate(notes[:n_chroma]):
                for t_index, amplitude in enumerate(chroma[i]):
                    t = time_seconds[t_index]
                    f.write(f"{note},{t},{amplitude}\n")

        return csv_path

    def _extract_mfcc_to_csv(self, file_path: str, n_mfcc: int = 13) -> str:
        """
        Extract MFCC features and save to CSV

        Args:
            file_path: Path to the audio file
            n_mfcc: Number of MFCC coefficients

        Returns:
            Path to CSV file containing MFCC data
        """
        # Load the audio file
        y, sr = librosa.load(file_path, sr=None)

        # Extract MFCC features
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=n_mfcc)

        # Create CSV file
        file_name = os.path.basename(file_path).split('.')[0] + "_mfcc.csv"
        csv_path = os.path.join(tempfile.gettempdir(), file_name)

        # Save as CSV
        np.savetxt(csv_path, mfcc, delimiter=',')

        return csv_path

    def run(self):
        """Run the MCP server"""
        logger.info("Starting MCP Audio Analysis Server")
        self.mcp.run()


def main():
    """Main entry point for running the server standalone"""
    logging.basicConfig(level=logging.INFO)
    server = MCPAudioAnalysisServer()
    server.run()


if __name__ == "__main__":
    main()
