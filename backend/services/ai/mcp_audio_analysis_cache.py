"""
Cache for MCP audio analysis results.

This module provides a caching mechanism for audio analysis results to improve performance
by avoiding redundant analysis of the same audio files.
"""

import os
import json
import time
import hashlib
import logging
from typing import Dict, Any, Optional, List, Tuple
import threading

logger = logging.getLogger(__name__)

class AudioAnalysisCache:
    """Cache for audio analysis results."""
    
    def __init__(self, cache_dir: Optional[str] = None, max_cache_size: int = 100, 
                 cache_ttl: int = 86400):
        """
        Initialize the audio analysis cache.
        
        Args:
            cache_dir: Directory to store cache files (default: ~/.dj_mix_constructor/cache/audio_analysis)
            max_cache_size: Maximum number of items to keep in the cache (default: 100)
            cache_ttl: Time-to-live for cache items in seconds (default: 86400 = 24 hours)
        """
        # Set cache directory
        if cache_dir is None:
            home_dir = os.path.expanduser("~")
            cache_dir = os.path.join(home_dir, ".dj_mix_constructor", "cache", "audio_analysis")
        
        self.cache_dir = cache_dir
        self.max_cache_size = max_cache_size
        self.cache_ttl = cache_ttl
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # In-memory cache for faster access
        self.memory_cache: Dict[str, Tuple[Any, float]] = {}
        
        # Lock for thread safety
        self.lock = threading.RLock()
        
        # Load existing cache items
        self._load_cache()
    
    def _load_cache(self):
        """Load existing cache items from disk."""
        with self.lock:
            try:
                # Get all cache files
                cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith(".json")]
                
                # Load each file into memory cache
                for filename in cache_files:
                    try:
                        file_path = os.path.join(self.cache_dir, filename)
                        cache_key = filename[:-5]  # Remove .json extension
                        
                        # Check if file is too old
                        file_mtime = os.path.getmtime(file_path)
                        if time.time() - file_mtime > self.cache_ttl:
                            # File is too old, delete it
                            os.remove(file_path)
                            continue
                        
                        # Load file content
                        with open(file_path, "r") as f:
                            data = json.load(f)
                        
                        # Add to memory cache
                        self.memory_cache[cache_key] = (data, file_mtime)
                    except Exception as e:
                        logger.warning(f"Failed to load cache file {filename}: {str(e)}")
                
                # Trim cache if it's too large
                self._trim_cache()
                
                logger.info(f"Loaded {len(self.memory_cache)} items into audio analysis cache")
            except Exception as e:
                logger.error(f"Failed to load audio analysis cache: {str(e)}")
    
    def _trim_cache(self):
        """Trim the cache to the maximum size."""
        with self.lock:
            if len(self.memory_cache) <= self.max_cache_size:
                return
            
            # Sort cache items by timestamp (oldest first)
            sorted_items = sorted(self.memory_cache.items(), key=lambda x: x[1][1])
            
            # Remove oldest items
            items_to_remove = len(self.memory_cache) - self.max_cache_size
            for i in range(items_to_remove):
                cache_key, _ = sorted_items[i]
                
                # Remove from memory cache
                del self.memory_cache[cache_key]
                
                # Remove from disk
                file_path = os.path.join(self.cache_dir, f"{cache_key}.json")
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.warning(f"Failed to remove cache file {file_path}: {str(e)}")
            
            logger.info(f"Trimmed audio analysis cache, removed {items_to_remove} items")
    
    def _get_cache_key(self, file_path: str, analysis_type: str, options: Dict[str, Any]) -> str:
        """
        Generate a cache key for the given file path and analysis options.
        
        Args:
            file_path: Path to the audio file
            analysis_type: Type of analysis (e.g., 'track', 'beat_grid', 'segments')
            options: Analysis options
            
        Returns:
            Cache key
        """
        # Get file metadata
        try:
            file_size = os.path.getsize(file_path)
            file_mtime = os.path.getmtime(file_path)
        except Exception:
            # If file doesn't exist or can't be accessed, use defaults
            file_size = 0
            file_mtime = 0
        
        # Create a string with all the relevant information
        key_data = f"{file_path}:{file_size}:{file_mtime}:{analysis_type}:{json.dumps(options, sort_keys=True)}"
        
        # Hash the string to create a cache key
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, file_path: str, analysis_type: str, options: Dict[str, Any] = None) -> Optional[Any]:
        """
        Get a cached analysis result.
        
        Args:
            file_path: Path to the audio file
            analysis_type: Type of analysis (e.g., 'track', 'beat_grid', 'segments')
            options: Analysis options (default: {})
            
        Returns:
            Cached result or None if not found
        """
        if options is None:
            options = {}
        
        with self.lock:
            # Generate cache key
            cache_key = self._get_cache_key(file_path, analysis_type, options)
            
            # Check memory cache
            if cache_key in self.memory_cache:
                data, timestamp = self.memory_cache[cache_key]
                
                # Check if data is still valid
                if time.time() - timestamp <= self.cache_ttl:
                    logger.debug(f"Cache hit for {analysis_type} analysis of {file_path}")
                    return data
                
                # Data is too old, remove it
                del self.memory_cache[cache_key]
                
                # Remove from disk
                file_path = os.path.join(self.cache_dir, f"{cache_key}.json")
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.warning(f"Failed to remove cache file {file_path}: {str(e)}")
            
            logger.debug(f"Cache miss for {analysis_type} analysis of {file_path}")
            return None
    
    def set(self, file_path: str, analysis_type: str, result: Any, options: Dict[str, Any] = None) -> None:
        """
        Set a cached analysis result.
        
        Args:
            file_path: Path to the audio file
            analysis_type: Type of analysis (e.g., 'track', 'beat_grid', 'segments')
            result: Analysis result
            options: Analysis options (default: {})
        """
        if options is None:
            options = {}
        
        with self.lock:
            # Generate cache key
            cache_key = self._get_cache_key(file_path, analysis_type, options)
            
            # Add to memory cache
            timestamp = time.time()
            self.memory_cache[cache_key] = (result, timestamp)
            
            # Save to disk
            file_path = os.path.join(self.cache_dir, f"{cache_key}.json")
            try:
                with open(file_path, "w") as f:
                    json.dump(result, f)
            except Exception as e:
                logger.warning(f"Failed to save cache file {file_path}: {str(e)}")
            
            # Trim cache if it's too large
            self._trim_cache()
            
            logger.debug(f"Cached {analysis_type} analysis of {file_path}")
    
    def clear(self) -> None:
        """Clear the entire cache."""
        with self.lock:
            # Clear memory cache
            self.memory_cache.clear()
            
            # Clear disk cache
            try:
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith(".json"):
                        file_path = os.path.join(self.cache_dir, filename)
                        os.remove(file_path)
            except Exception as e:
                logger.warning(f"Failed to clear cache directory: {str(e)}")
            
            logger.info("Cleared audio analysis cache")
    
    def remove(self, file_path: str) -> None:
        """
        Remove all cached results for a specific file.
        
        Args:
            file_path: Path to the audio file
        """
        with self.lock:
            # Find all cache keys for this file
            keys_to_remove = []
            for cache_key, (data, timestamp) in self.memory_cache.items():
                # Check if the cache key is for this file
                # This is an approximation since we can't reverse the hash
                if file_path in cache_key:
                    keys_to_remove.append(cache_key)
            
            # Remove from memory cache
            for cache_key in keys_to_remove:
                del self.memory_cache[cache_key]
                
                # Remove from disk
                file_path = os.path.join(self.cache_dir, f"{cache_key}.json")
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.warning(f"Failed to remove cache file {file_path}: {str(e)}")
            
            logger.info(f"Removed {len(keys_to_remove)} cached items for {file_path}")


# Create a global instance of the cache
audio_analysis_cache = AudioAnalysisCache()
