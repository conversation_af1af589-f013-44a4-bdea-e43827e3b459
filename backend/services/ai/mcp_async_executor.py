"""
MCP Asynchronous Tool Executor

This module implements asynchronous execution of MCP tools to prevent blocking
operations from affecting the responsiveness of the application.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Awaitable, Tuple
import threading
from concurrent.futures import ThreadPoolExecutor

from backend.config import settings

logger = logging.getLogger(__name__)

class TaskStatus:
    """Status of an asynchronous task."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class AsyncTask:
    """Represents an asynchronous task."""
    
    def __init__(self, task_id: str, tool_name: str, parameters: Dict[str, Any]):
        """
        Initialize an asynchronous task.
        
        Args:
            task_id: Unique identifier for the task
            tool_name: Name of the tool to execute
            parameters: Parameters for the tool
        """
        self.task_id = task_id
        self.tool_name = tool_name
        self.parameters = parameters
        self.status = TaskStatus.PENDING
        self.result = None
        self.error = None
        self.created_at = time.time()
        self.started_at = None
        self.completed_at = None
        self.progress = 0.0
        self.progress_message = "Waiting to start"
        self.future = None

class MCPAsyncExecutor:
    """Executor for asynchronous MCP tool calls."""
    
    def __init__(self, max_workers: int = 5):
        """
        Initialize the async executor.
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.tasks: Dict[str, AsyncTask] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.lock = threading.RLock()
        self.mcp_client = None  # Will be set later
    
    def set_mcp_client(self, mcp_client):
        """
        Set the MCP client to use for tool calls.
        
        Args:
            mcp_client: MCP client instance
        """
        self.mcp_client = mcp_client
    
    def submit_task(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """
        Submit a task for asynchronous execution.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Parameters for the tool
            
        Returns:
            Task ID
        """
        if not self.mcp_client:
            raise ValueError("MCP client not set")
        
        # Generate a unique task ID
        task_id = str(uuid.uuid4())
        
        # Create the task
        task = AsyncTask(task_id, tool_name, parameters)
        
        # Submit the task to the executor
        future = self.executor.submit(self._execute_task, task)
        task.future = future
        
        # Store the task
        with self.lock:
            self.tasks[task_id] = task
        
        logger.info(f"Submitted async task {task_id} for tool {tool_name}")
        return task_id
    
    def _execute_task(self, task: AsyncTask) -> Dict[str, Any]:
        """
        Execute a task in a worker thread.
        
        Args:
            task: Task to execute
            
        Returns:
            Result of the task
        """
        try:
            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = time.time()
            task.progress = 0.1
            task.progress_message = "Starting execution"
            
            # Create an event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Execute the tool call
            task.progress = 0.3
            task.progress_message = "Calling tool"
            result = loop.run_until_complete(
                self.mcp_client.call_tool(task.tool_name, task.parameters, use_cache=True)
            )
            
            # Update task status
            task.status = TaskStatus.COMPLETED
            task.result = result
            task.completed_at = time.time()
            task.progress = 1.0
            task.progress_message = "Task completed"
            
            logger.info(f"Async task {task.task_id} completed successfully")
            return result
        
        except Exception as e:
            # Update task status on error
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.completed_at = time.time()
            task.progress = 1.0
            task.progress_message = f"Task failed: {str(e)}"
            
            logger.error(f"Async task {task.task_id} failed: {str(e)}")
            return {"error": str(e), "error_type": type(e).__name__}
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a task.
        
        Args:
            task_id: ID of the task
            
        Returns:
            Task status information or None if not found
        """
        with self.lock:
            task = self.tasks.get(task_id)
            
            if not task:
                return None
            
            return {
                "task_id": task.task_id,
                "tool_name": task.tool_name,
                "status": task.status,
                "created_at": task.created_at,
                "started_at": task.started_at,
                "completed_at": task.completed_at,
                "progress": task.progress,
                "progress_message": task.progress_message,
                "result": task.result if task.status == TaskStatus.COMPLETED else None,
                "error": task.error if task.status == TaskStatus.FAILED else None
            }
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the result of a completed task.
        
        Args:
            task_id: ID of the task
            
        Returns:
            Task result or None if not found or not completed
        """
        with self.lock:
            task = self.tasks.get(task_id)
            
            if not task or task.status != TaskStatus.COMPLETED:
                return None
            
            return task.result
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending or running task.
        
        Args:
            task_id: ID of the task
            
        Returns:
            True if the task was cancelled, False otherwise
        """
        with self.lock:
            task = self.tasks.get(task_id)
            
            if not task or task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                return False
            
            # Cancel the future if it exists
            if task.future and not task.future.done():
                task.future.cancel()
            
            # Update task status
            task.status = TaskStatus.CANCELLED
            task.completed_at = time.time()
            task.progress = 1.0
            task.progress_message = "Task cancelled"
            
            logger.info(f"Async task {task_id} cancelled")
            return True
    
    def cleanup_old_tasks(self, max_age_seconds: int = 3600) -> int:
        """
        Remove old completed, failed, or cancelled tasks.
        
        Args:
            max_age_seconds: Maximum age of tasks to keep
            
        Returns:
            Number of tasks removed
        """
        now = time.time()
        tasks_to_remove = []
        
        with self.lock:
            for task_id, task in self.tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    if task.completed_at and now - task.completed_at > max_age_seconds:
                        tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.tasks[task_id]
        
        logger.info(f"Cleaned up {len(tasks_to_remove)} old tasks")
        return len(tasks_to_remove)

# Create a singleton instance
mcp_async_executor = MCPAsyncExecutor(max_workers=settings.MCP_ASYNC_MAX_WORKERS if hasattr(settings, 'MCP_ASYNC_MAX_WORKERS') else 5)
