"""
Parameter Manager for AI services.

This module provides functions to manage feature-specific parameters used by AI providers.
It allows getting, saving, and resetting custom parameters for different AI features.
"""

import json
import os
from typing import Dict, Any, List, Optional
import logging

from backend.config import settings

logger = logging.getLogger(__name__)

# Path to the custom parameters file
CUSTOM_PARAMETERS_FILE = os.path.join(settings.BASE_DIR, "data", "custom_parameters.json")

def get_feature_parameters(feature_id: str) -> Dict[str, Any]:
    """
    Get parameters for a specific feature, with custom override if available.
    
    Args:
        feature_id: The ID of the feature to retrieve parameters for
        
    Returns:
        The parameters dictionary, either custom or default
    """
    # Check if feature parameters are enabled
    if not settings.FEATURE_PARAMETERS_ENABLED:
        return settings.DEFAULT_FEATURE_PARAMETERS.get(feature_id, {})
    
    # Check if custom parameters exist
    if os.path.exists(CUSTOM_PARAMETERS_FILE):
        try:
            with open(CUSTOM_PARAMETERS_FILE, 'r') as f:
                custom_parameters = json.load(f)
                if feature_id in custom_parameters:
                    logger.debug(f"Using custom parameters for {feature_id}")
                    return custom_parameters[feature_id]
        except Exception as e:
            logger.error(f"Error loading custom parameters: {str(e)}")
    
    # Fall back to default parameters
    logger.debug(f"Using default parameters for {feature_id}")
    return settings.DEFAULT_FEATURE_PARAMETERS.get(feature_id, {})

def save_feature_parameters(feature_id: str, parameters: Dict[str, Any]) -> bool:
    """
    Save custom parameters for a specific feature.
    
    Args:
        feature_id: The ID of the feature to save parameters for
        parameters: The parameters dictionary to save
        
    Returns:
        True if successful, False otherwise
    """
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(CUSTOM_PARAMETERS_FILE), exist_ok=True)
    
    custom_parameters = {}
    
    # Load existing custom parameters if available
    if os.path.exists(CUSTOM_PARAMETERS_FILE):
        try:
            with open(CUSTOM_PARAMETERS_FILE, 'r') as f:
                custom_parameters = json.load(f)
        except Exception as e:
            logger.error(f"Error loading custom parameters: {str(e)}")
            return False
    
    # Update the parameters
    custom_parameters[feature_id] = parameters
    
    # Save back to file
    try:
        with open(CUSTOM_PARAMETERS_FILE, 'w') as f:
            json.dump(custom_parameters, f, indent=2)
        logger.info(f"Saved custom parameters for {feature_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving custom parameters: {str(e)}")
        return False

def reset_feature_parameters(feature_id: str) -> bool:
    """
    Reset parameters for a specific feature to their default values.
    
    Args:
        feature_id: The ID of the feature to reset parameters for
        
    Returns:
        True if successful, False otherwise
    """
    if not os.path.exists(CUSTOM_PARAMETERS_FILE):
        logger.debug(f"No custom parameters file exists, nothing to reset for {feature_id}")
        return False
    
    try:
        with open(CUSTOM_PARAMETERS_FILE, 'r') as f:
            custom_parameters = json.load(f)
        
        if feature_id in custom_parameters:
            del custom_parameters[feature_id]
            
            with open(CUSTOM_PARAMETERS_FILE, 'w') as f:
                json.dump(custom_parameters, f, indent=2)
            
            logger.info(f"Reset custom parameters for {feature_id}")
            return True
        else:
            logger.debug(f"No custom parameters found for {feature_id}, nothing to reset")
            return False
    except Exception as e:
        logger.error(f"Error resetting custom parameters: {str(e)}")
        return False

def get_all_feature_ids() -> List[str]:
    """
    Get all available feature IDs.
    
    Returns:
        List of feature IDs
    """
    return list(settings.DEFAULT_FEATURE_PARAMETERS.keys())

def get_all_feature_parameters() -> Dict[str, Dict[str, Any]]:
    """
    Get parameters for all features with their current values.
    
    Returns:
        Dictionary of feature IDs to parameter dictionaries
    """
    feature_ids = get_all_feature_ids()
    return {feature_id: get_feature_parameters(feature_id) for feature_id in feature_ids}

def get_parameter_metadata() -> Dict[str, Dict[str, Any]]:
    """
    Get metadata for all parameters.
    
    Returns:
        Dictionary of parameter names to metadata dictionaries
    """
    return {
        "temperature": {
            "name": "Temperature",
            "description": "Controls randomness: higher values produce more creative results",
            "min": 0.0,
            "max": 1.0,
            "step": 0.1,
            "default": 0.7
        },
        "max_tokens": {
            "name": "Max Tokens",
            "description": "Maximum number of tokens to generate",
            "min": 256,
            "max": 4096,
            "step": 256,
            "default": 1024
        },
        "top_p": {
            "name": "Top P",
            "description": "Controls diversity via nucleus sampling",
            "min": 0.0,
            "max": 1.0,
            "step": 0.05,
            "default": 0.9
        },
        "frequency_penalty": {
            "name": "Frequency Penalty",
            "description": "Reduces repetition of token sequences",
            "min": 0.0,
            "max": 2.0,
            "step": 0.1,
            "default": 0.0
        },
        "presence_penalty": {
            "name": "Presence Penalty",
            "description": "Reduces repetition of topics",
            "min": 0.0,
            "max": 2.0,
            "step": 0.1,
            "default": 0.0
        }
    }
