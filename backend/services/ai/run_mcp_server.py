#!/usr/bin/env python
'''
Script to run the MCP server.
'''

import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)

# Add the parent directory to the path so we can import the server
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the server
from backend.services.ai.mcp_server import DJMixConstructorMCPServer

async def main():
    '''Run the MCP server.'''
    server = DJMixConstructorMCPServer()
    await server.run()

if __name__ == '__main__':
    asyncio.run(main())
