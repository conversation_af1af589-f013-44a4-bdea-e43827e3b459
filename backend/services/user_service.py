from typing import Dict, Any, List, Optional
import logging
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.future import select

from backend.models.user import User
from backend.models.user_preferences import UserPreferences
from backend.models.suggestion_interaction import SuggestionInteraction

logger = logging.getLogger(__name__)

class UserService:
    """
    Service for user-related operations
    """
    
    def __init__(self, db: Session):
        self.db = db
        
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Get user preferences
        
        Args:
            user_id: User ID
            
        Returns:
            User preferences as a dictionary
        """
        try:
            # Get user preferences from database
            result = await self.db.execute(
                select(UserPreferences).where(UserPreferences.user_id == user_id)
            )
            preferences = result.scalars().first()
            
            if not preferences:
                # Create default preferences if not found
                preferences = UserPreferences(
                    user_id=user_id,
                    skill_level="beginner",
                    preferred_genres=[],
                    preferred_bpm_ranges=[],
                    preferred_transitions=[],
                    complexity_setting=50,
                    prefer_exploration=False,
                    feature_usage={}
                )
                self.db.add(preferences)
                await self.db.commit()
                
            # Convert to dictionary
            return {
                "skill_level": preferences.skill_level,
                "preferred_genres": preferences.preferred_genres,
                "preferred_bpm_ranges": preferences.preferred_bpm_ranges,
                "preferred_transitions": preferences.preferred_transitions,
                "complexity_setting": preferences.complexity_setting,
                "prefer_exploration": preferences.prefer_exploration,
                "feature_usage": preferences.feature_usage
            }
        except Exception as e:
            logger.error(f"Error getting user preferences: {str(e)}", exc_info=True)
            # Return default preferences
            return {
                "skill_level": "beginner",
                "preferred_genres": [],
                "preferred_bpm_ranges": [],
                "preferred_transitions": [],
                "complexity_setting": 50,
                "prefer_exploration": False,
                "feature_usage": {}
            }
            
    async def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user preferences
        
        Args:
            user_id: User ID
            preferences: User preferences to update
            
        Returns:
            Updated user preferences
        """
        try:
            # Get existing preferences
            result = await self.db.execute(
                select(UserPreferences).where(UserPreferences.user_id == user_id)
            )
            user_preferences = result.scalars().first()
            
            if not user_preferences:
                # Create new preferences if not found
                user_preferences = UserPreferences(user_id=user_id)
                self.db.add(user_preferences)
                
            # Update preferences
            for key, value in preferences.items():
                if hasattr(user_preferences, key):
                    setattr(user_preferences, key, value)
                    
            # Save changes
            await self.db.commit()
            
            # Return updated preferences
            return await self.get_user_preferences(user_id)
        except Exception as e:
            logger.error(f"Error updating user preferences: {str(e)}", exc_info=True)
            await self.db.rollback()
            raise
            
    async def track_feature_usage(self, user_id: str, feature_id: str, accepted: bool = True) -> Dict[str, Any]:
        """
        Track feature usage
        
        Args:
            user_id: User ID
            feature_id: Feature ID
            accepted: Whether the feature was accepted
            
        Returns:
            Updated user preferences
        """
        try:
            # Get user preferences
            result = await self.db.execute(
                select(UserPreferences).where(UserPreferences.user_id == user_id)
            )
            preferences = result.scalars().first()
            
            if not preferences:
                # Create default preferences if not found
                preferences = UserPreferences(
                    user_id=user_id,
                    feature_usage={}
                )
                self.db.add(preferences)
                
            # Update feature usage
            feature_usage = preferences.feature_usage or {}
            if feature_id not in feature_usage:
                feature_usage[feature_id] = {
                    "used_count": 0,
                    "accepted_count": 0,
                    "rejected_count": 0,
                    "last_used": None
                }
                
            feature_usage[feature_id]["used_count"] += 1
            if accepted:
                feature_usage[feature_id]["accepted_count"] += 1
            else:
                feature_usage[feature_id]["rejected_count"] += 1
                
            feature_usage[feature_id]["last_used"] = datetime.now().isoformat()
            
            # Update preferences
            preferences.feature_usage = feature_usage
            await self.db.commit()
            
            # Return updated preferences
            return await self.get_user_preferences(user_id)
        except Exception as e:
            logger.error(f"Error tracking feature usage: {str(e)}", exc_info=True)
            await self.db.rollback()
            raise
            
    async def track_suggestion_interaction(self, suggestion_id: str, interaction: str) -> None:
        """
        Track user interaction with a suggestion
        
        Args:
            suggestion_id: Suggestion ID
            interaction: Interaction type ('accepted' or 'dismissed')
        """
        try:
            # Create interaction record
            suggestion_interaction = SuggestionInteraction(
                suggestion_id=suggestion_id,
                interaction=interaction,
                timestamp=datetime.now()
            )
            
            # Save to database
            self.db.add(suggestion_interaction)
            await self.db.commit()
        except Exception as e:
            logger.error(f"Error tracking suggestion interaction: {str(e)}", exc_info=True)
            await self.db.rollback()
            raise
