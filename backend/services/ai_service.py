from typing import List, Dict, Any, Optional
import logging
import json
import os
import httpx
from datetime import datetime

logger = logging.getLogger(__name__)

class AIService:
    """
    Service for interacting with AI models
    """
    
    def __init__(self, api_key: str, model: str = "gpt-4o", base_url: str = None):
        self.api_key = api_key
        self.model = model
        self.base_url = base_url or "https://api.openai.com/v1"
        
    async def generate_suggestions(
        self, 
        context: Dict[str, Any], 
        max_suggestions: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Generate smart suggestions based on application context
        
        Args:
            context: Application context including current state and user preferences
            max_suggestions: Maximum number of suggestions to generate
            
        Returns:
            List of suggestion objects
        """
        try:
            # Create prompt for suggestion generation
            prompt = self._create_suggestion_prompt(context, max_suggestions)
            
            # Call AI model
            response = await self._call_ai_model(
                prompt=prompt,
                response_format={"type": "json_object"},
                temperature=0.7,
                max_tokens=1000
            )
            
            # Parse response
            try:
                suggestions = json.loads(response).get("suggestions", [])
                
                # Validate and clean suggestions
                valid_suggestions = []
                for suggestion in suggestions:
                    if "title" in suggestion and "description" in suggestion and "type" in suggestion:
                        # Ensure type is valid
                        if suggestion["type"] not in ["tip", "optimization", "feature", "workflow"]:
                            suggestion["type"] = "tip"
                        
                        # Add confidence if not present
                        if "confidence" not in suggestion:
                            suggestion["confidence"] = 0.8
                            
                        valid_suggestions.append(suggestion)
                
                return valid_suggestions[:max_suggestions]
            except json.JSONDecodeError:
                logger.error(f"Failed to parse AI response as JSON: {response}")
                return []
                
        except Exception as e:
            logger.error(f"Error generating suggestions: {str(e)}", exc_info=True)
            return []
            
    def _create_suggestion_prompt(self, context: Dict[str, Any], max_suggestions: int) -> str:
        """
        Create a prompt for generating suggestions
        
        Args:
            context: Application context
            max_suggestions: Maximum number of suggestions
            
        Returns:
            Prompt string
        """
        current_page = context.get("current_page", "")
        current_view = context.get("current_view", "")
        active_tracks = context.get("active_tracks", [])
        recent_actions = context.get("recent_actions", [])
        user_preferences = context.get("user_preferences", {})
        
        prompt = f"""
        You are an AI assistant for a DJ mixing application called DJ Mix Constructor. 
        Your task is to generate smart suggestions for the user based on their current context.
        
        Current application state:
        - Page: {current_page}
        - View: {current_view}
        - Active tracks: {len(active_tracks)}
        """
        
        if active_tracks:
            prompt += "\nActive tracks details:\n"
            for i, track in enumerate(active_tracks):
                prompt += f"  {i+1}. {track.get('title', 'Unknown')} by {track.get('artist', 'Unknown')}"
                if track.get('bpm'):
                    prompt += f" (BPM: {track.get('bpm')})"
                if track.get('key'):
                    prompt += f" (Key: {track.get('key')})"
                prompt += "\n"
        
        if recent_actions:
            prompt += "\nRecent user actions:\n"
            for action in recent_actions:
                time_ago = action.get("time_ago_seconds", 0)
                if time_ago < 60:
                    time_str = f"{int(time_ago)} seconds ago"
                elif time_ago < 3600:
                    time_str = f"{int(time_ago / 60)} minutes ago"
                else:
                    time_str = f"{int(time_ago / 3600)} hours ago"
                    
                prompt += f"- {action.get('description', '')} ({time_str})\n"
        
        prompt += f"""
        User preferences:
        - Skill level: {user_preferences.get('skill_level', 'beginner')}
        - Preferred genres: {', '.join(user_preferences.get('preferred_genres', []))}
        - Complexity setting: {user_preferences.get('complexity_setting', 50)}/100
        - Prefers exploration: {user_preferences.get('prefer_exploration', False)}
        
        Based on this context, generate up to {max_suggestions} smart suggestions for the user.
        Each suggestion should include:
        - title: A short, catchy title
        - description: A brief description of the suggestion
        - type: One of "tip", "optimization", "feature", or "workflow"
        - context: A question or prompt the user might ask about this suggestion
        - action: (optional) A call-to-action button text
        - confidence: A number between 0 and 1 indicating your confidence in this suggestion
        
        Return your response as a JSON object with a "suggestions" array containing the suggestions.
        """
        
        return prompt
    
    async def _call_ai_model(
        self, 
        prompt: str, 
        response_format: Dict[str, Any] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> str:
        """
        Call the AI model with the given prompt
        
        Args:
            prompt: The prompt to send to the model
            response_format: Format specification for the response
            temperature: Temperature parameter for generation
            max_tokens: Maximum tokens to generate
            
        Returns:
            Model response as a string
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            payload = {
                "model": self.model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            if response_format:
                payload["response_format"] = response_format
                
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    logger.error(f"AI API error: {response.status_code} - {response.text}")
                    return ""
                    
                response_data = response.json()
                return response_data["choices"][0]["message"]["content"]
                
        except Exception as e:
            logger.error(f"Error calling AI model: {str(e)}", exc_info=True)
            return ""
            
    async def generate_transition_suggestions(
        self,
        current_track: Dict[str, Any],
        next_track: Dict[str, Any],
        count: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Generate transition suggestions between two tracks
        
        Args:
            current_track: Current track data
            next_track: Next track data
            count: Number of suggestions to generate
            
        Returns:
            List of transition suggestion objects
        """
        # Implementation for transition suggestions
        # This would be similar to the generate_suggestions method
        pass
        
    async def answer_question(
        self,
        question: str,
        context: Optional[str] = None,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Answer a user question with context
        
        Args:
            question: User question
            context: Additional context
            user_preferences: User preferences
            
        Returns:
            Answer as a string
        """
        # Implementation for answering questions
        pass
