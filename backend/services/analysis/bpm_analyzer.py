"""
BPM analysis functionality.
Advanced BPM clustering, transition analysis, and tempo-based insights.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score

from ...models.track import Track
from ...models.folder import Folder
from ...models.analysis import BPMCluster, BPMAnalysis

logger = logging.getLogger(__name__)

class BPMAnalyzer:
    """Analyzes BPM patterns and clustering in music collections"""
    
    def __init__(self, db: Session):
        self.db = db

    async def get_advanced_bpm_analysis(self, directory_id: Optional[str] = None) -> BPMAnalysis:
        """Get advanced BPM analysis including clusters and transition matrix"""
        try:
            # Build query
            query = self.db.query(Track).filter(Track.bpm.isnot(None), Track.bpm > 0)
            
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            tracks = query.all()
            
            if not tracks:
                return BPMAnalysis(
                    total_tracks=0,
                    bpm_range={"min": 0, "max": 0},
                    clusters=[],
                    transition_matrix={},
                    statistics={}
                )

            # Extract BPM values
            bpm_values = np.array([track.bpm for track in tracks])
            
            # Create DataFrame for analysis
            df = pd.DataFrame([{
                'id': track.id,
                'title': track.title,
                'artist': track.artist,
                'bpm': track.bpm,
                'key': track.key,
                'energy': track.energy
            } for track in tracks])

            # Calculate statistics
            statistics = self._calculate_advanced_bpm_stats(df)
            
            # Identify clusters
            clusters = self._identify_bpm_clusters(bpm_values)
            
            # Calculate transition matrix
            transition_matrix = self._calculate_transition_matrix(bpm_values)

            return BPMAnalysis(
                total_tracks=len(tracks),
                bpm_range={
                    "min": float(np.min(bpm_values)),
                    "max": float(np.max(bpm_values))
                },
                clusters=[BPMCluster(**cluster) for cluster in clusters],
                transition_matrix=transition_matrix,
                statistics=statistics
            )

        except Exception as e:
            logger.error(f"Error in advanced BPM analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error in advanced BPM analysis: {str(e)}"
            )

    def _calculate_transition_matrix(self, bpm_values: np.ndarray) -> Dict[int, Dict[int, float]]:
        """Calculate BPM transition difficulty matrix"""
        if len(bpm_values) < 2:
            return {}

        # Create BPM bins (rounded to nearest 5)
        bpm_bins = np.unique(np.round(bpm_values / 5) * 5).astype(int)
        
        transition_matrix = {}
        
        for from_bpm in bpm_bins:
            transition_matrix[from_bpm] = {}
            
            for to_bpm in bpm_bins:
                if from_bpm == to_bpm:
                    difficulty = 0.0  # Same BPM = easy
                else:
                    # Calculate difficulty based on BPM difference
                    bpm_diff = abs(to_bpm - from_bpm)
                    ratio = max(from_bpm, to_bpm) / min(from_bpm, to_bpm)
                    
                    # Easy transitions: small differences or exact ratios
                    if bpm_diff <= 5:
                        difficulty = 0.1
                    elif abs(ratio - 2.0) < 0.1 or abs(ratio - 1.5) < 0.1 or abs(ratio - 0.75) < 0.1:
                        difficulty = 0.3  # Harmonic ratios
                    elif bpm_diff <= 10:
                        difficulty = 0.4
                    elif bpm_diff <= 20:
                        difficulty = 0.6
                    else:
                        difficulty = 0.9  # Very difficult
                
                transition_matrix[from_bpm][to_bpm] = difficulty

        return transition_matrix

    def _identify_bpm_clusters(self, bpm_values: np.ndarray) -> List[Dict[str, float]]:
        """Identify natural BPM clusters in collection"""
        if len(bpm_values) < 2:
            return []

        # Reshape for sklearn
        bpm_reshaped = bpm_values.reshape(-1, 1)
        
        # Try different numbers of clusters
        best_clusters = []
        best_score = -1
        
        for n_clusters in range(2, min(8, len(bpm_values))):
            try:
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(bpm_reshaped)
                
                # Calculate silhouette score
                if len(np.unique(cluster_labels)) > 1:
                    score = silhouette_score(bpm_reshaped, cluster_labels)
                    
                    if score > best_score:
                        best_score = score
                        
                        # Create cluster information
                        clusters = []
                        for i in range(n_clusters):
                            cluster_bpms = bpm_values[cluster_labels == i]
                            if len(cluster_bpms) > 0:
                                clusters.append({
                                    "center": float(np.mean(cluster_bpms)),
                                    "min_bpm": float(np.min(cluster_bpms)),
                                    "max_bpm": float(np.max(cluster_bpms)),
                                    "track_count": int(len(cluster_bpms)),
                                    "std_dev": float(np.std(cluster_bpms))
                                })
                        
                        best_clusters = clusters
                        
            except Exception as e:
                logger.warning(f"Error in clustering with {n_clusters} clusters: {e}")
                continue

        return best_clusters

    def _calculate_advanced_bpm_stats(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate advanced BPM statistics"""
        if len(df) == 0 or df['bpm'].isna().all():
            return {}

        bpm_values = df['bpm'].dropna()
        
        stats = {
            "mean": float(bpm_values.mean()),
            "median": float(bpm_values.median()),
            "std_dev": float(bpm_values.std()),
            "min": float(bpm_values.min()),
            "max": float(bpm_values.max()),
            "range": float(bpm_values.max() - bpm_values.min()),
            "q25": float(bpm_values.quantile(0.25)),
            "q75": float(bpm_values.quantile(0.75)),
            "iqr": float(bpm_values.quantile(0.75) - bpm_values.quantile(0.25)),
            "coefficient_of_variation": float(bpm_values.std() / bpm_values.mean()) if bpm_values.mean() > 0 else 0
        }

        # Calculate mode (most common BPM range)
        bpm_rounded = (bpm_values / 5).round() * 5
        mode_bpm = bpm_rounded.mode().iloc[0] if not bpm_rounded.mode().empty else stats["mean"]
        stats["mode"] = float(mode_bpm)

        return stats

    async def get_bpm_analysis(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """Get advanced BPM analysis with clustering"""
        try:
            # Build query
            query = self.db.query(Track).filter(Track.bpm.isnot(None), Track.bpm > 0)
            
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)
            
            if folder_id:
                # Get folder and its subfolders
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_ids = [folder_id]
                    # Add subfolder IDs recursively
                    subfolders = self.db.query(Folder).filter(Folder.parent_id == folder_id).all()
                    for subfolder in subfolders:
                        folder_ids.append(subfolder.id)
                    
                    query = query.filter(Track.folder_id.in_(folder_ids))

            tracks = query.all()
            
            if not tracks:
                return {
                    "total_tracks": 0,
                    "message": "No tracks with BPM data found"
                }

            # Create DataFrame
            df = pd.DataFrame([{
                'id': track.id,
                'title': track.title,
                'artist': track.artist,
                'bpm': track.bpm,
                'key': track.key,
                'energy': track.energy
            } for track in tracks])

            # BPM distribution analysis
            bpm_distribution = {
                "60-90": len(df[(df['bpm'] >= 60) & (df['bpm'] < 90)]),
                "90-110": len(df[(df['bpm'] >= 90) & (df['bpm'] < 110)]),
                "110-130": len(df[(df['bpm'] >= 110) & (df['bpm'] < 130)]),
                "130-150": len(df[(df['bpm'] >= 130) & (df['bpm'] < 150)]),
                "150-180": len(df[(df['bpm'] >= 150) & (df['bpm'] < 180)]),
                "180+": len(df[df['bpm'] >= 180])
            }

            # Clustering analysis
            bpm_values = df['bpm'].values
            clusters = self._identify_bpm_clusters(bpm_values)
            
            # Transition matrix
            transition_matrix = self._calculate_transition_matrix(bpm_values)
            
            # Statistics
            statistics = self._calculate_advanced_bpm_stats(df)

            # Mix compatibility analysis
            compatibility_analysis = self._analyze_mix_compatibility(df)

            return {
                "total_tracks": len(df),
                "bpm_range": {
                    "min": float(df['bpm'].min()),
                    "max": float(df['bpm'].max())
                },
                "distribution": bpm_distribution,
                "clusters": clusters,
                "transition_matrix": transition_matrix,
                "statistics": statistics,
                "compatibility_analysis": compatibility_analysis
            }

        except Exception as e:
            logger.error(f"Error in BPM analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error in BPM analysis: {str(e)}"
            )

    def _analyze_mix_compatibility(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze how well tracks can be mixed together based on BPM"""
        if len(df) < 2:
            return {"compatibility_score": 0.0, "analysis": "Not enough tracks for analysis"}

        bpm_values = df['bpm'].values
        total_pairs = 0
        compatible_pairs = 0

        # Check all pairs of tracks
        for i, bpm1 in enumerate(bpm_values):
            for j, bpm2 in enumerate(bpm_values[i+1:], i+1):
                total_pairs += 1
                
                # Check if BPMs are compatible for mixing
                bpm_diff = abs(bpm1 - bpm2)
                ratio = max(bpm1, bpm2) / min(bpm1, bpm2)
                
                # Compatible if:
                # 1. Within 10 BPM
                # 2. Exact ratio (2:1, 3:2, etc.)
                if (bpm_diff <= 10 or 
                    abs(ratio - 2.0) < 0.1 or 
                    abs(ratio - 1.5) < 0.1 or
                    abs(ratio - 0.75) < 0.1):
                    compatible_pairs += 1

        compatibility_score = compatible_pairs / total_pairs if total_pairs > 0 else 0

        # Analyze BPM gaps
        sorted_bpms = np.sort(bpm_values)
        gaps = np.diff(sorted_bpms)
        large_gaps = gaps[gaps > 15]  # Gaps larger than 15 BPM

        return {
            "compatibility_score": float(compatibility_score),
            "total_pairs": total_pairs,
            "compatible_pairs": compatible_pairs,
            "large_gaps_count": len(large_gaps),
            "largest_gap": float(np.max(gaps)) if len(gaps) > 0 else 0,
            "analysis": self._get_compatibility_analysis_text(compatibility_score, len(large_gaps))
        }

    def _get_compatibility_analysis_text(self, score: float, gap_count: int) -> str:
        """Generate text analysis of BPM compatibility"""
        if score >= 0.8:
            base_text = "Excellent BPM compatibility - most tracks can be mixed together easily"
        elif score >= 0.6:
            base_text = "Good BPM compatibility - many tracks can be mixed with standard techniques"
        elif score >= 0.4:
            base_text = "Moderate BPM compatibility - some tracks may require advanced mixing techniques"
        else:
            base_text = "Limited BPM compatibility - many tracks will be difficult to mix together"

        if gap_count > 5:
            base_text += f". {gap_count} large BPM gaps detected - consider adding tracks to fill these gaps"
        elif gap_count > 0:
            base_text += f". {gap_count} BPM gaps detected"

        return base_text
