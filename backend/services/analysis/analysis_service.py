"""
Main analysis service that orchestrates all analysis functionality.
Integrates track analysis jobs, collection analysis, BPM analysis, and key analysis.
"""

import logging
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException

from .track_analysis_jobs import TrackAnalysisJobManager
from .collection_analyzer import CollectionAnalyzer
from .bpm_analyzer import BPMAnalyzer
from .key_analyzer import KeyAnalyzer
from ...models.health_score import HealthScoreConfig
from ...models.track_analysis import TrackAnalysis, AnalysisStatus

logger = logging.getLogger(__name__)

class AnalysisService:
    """Main analysis service that orchestrates all functionality"""
    
    def __init__(self, db: Session):
        self.db = db
        
        # Initialize all analysis components
        self.job_manager = TrackAnalysisJobManager(db)
        self.collection_analyzer = CollectionAnalyzer(db)
        self.bpm_analyzer = BPMAnalyzer(db)
        self.key_analyzer = KeyAnalyzer(db)
        
        logger.info("AnalysisService initialized with all components")

    # Track Analysis Job Management Methods
    async def create_analysis_job(self, track_id: int) -> TrackAnalysis:
        """Create a new track analysis job"""
        return await self.job_manager.create_analysis_job(track_id)

    async def update_analysis_status(
        self,
        analysis_id: int,
        status: AnalysisStatus,
        progress: Optional[float] = None,
        error_message: Optional[str] = None,
        results: Optional[dict] = None
    ) -> TrackAnalysis:
        """Update the status of an analysis job"""
        return await self.job_manager.update_analysis_status(
            analysis_id, status, progress, error_message, results
        )

    async def get_analysis_status(self, track_id: int) -> Optional[TrackAnalysis]:
        """Get the analysis status for a track"""
        return await self.job_manager.get_analysis_status(track_id)

    async def delete_analysis(self, track_id: int) -> bool:
        """Delete analysis data for a track"""
        return await self.job_manager.delete_analysis(track_id)

    async def get_pending_analyses(self, limit: int = 10) -> list[TrackAnalysis]:
        """Get pending analysis jobs"""
        return await self.job_manager.get_pending_analyses(limit)

    async def get_processing_analyses(self) -> list[TrackAnalysis]:
        """Get currently processing analysis jobs"""
        return await self.job_manager.get_processing_analyses()

    async def get_failed_analyses(self, limit: int = 50) -> list[TrackAnalysis]:
        """Get failed analysis jobs"""
        return await self.job_manager.get_failed_analyses(limit)

    async def retry_failed_analysis(self, analysis_id: int) -> TrackAnalysis:
        """Retry a failed analysis job"""
        return await self.job_manager.retry_failed_analysis(analysis_id)

    async def cancel_analysis(self, analysis_id: int) -> TrackAnalysis:
        """Cancel a running analysis job"""
        return await self.job_manager.cancel_analysis(analysis_id)

    async def cleanup_old_analyses(self, days_old: int = 30) -> int:
        """Clean up old completed or failed analyses"""
        return await self.job_manager.cleanup_old_analyses(days_old)

    async def get_analysis_statistics(self) -> dict:
        """Get statistics about analysis jobs"""
        return await self.job_manager.get_analysis_statistics()

    # Collection Analysis Methods
    async def analyze_collection(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive analysis of the collection"""
        return await self.collection_analyzer.analyze_collection(directory_id, folder_id)

    def get_collection_health(self, folder_id: Optional[str] = None) -> Dict[str, float]:
        """Get the current health metrics for the collection"""
        try:
            # Use collection analyzer to get health metrics
            analysis_result = self.collection_analyzer.analyze_collection(folder_id=folder_id)
            
            if isinstance(analysis_result, dict) and "health_metrics" in analysis_result:
                return analysis_result["health_metrics"]
            else:
                # Fallback to basic health calculation
                return {"overall_health": 0.5, "message": "Limited health data available"}
                
        except Exception as e:
            logger.error(f"Error getting collection health: {str(e)}", exc_info=True)
            return {"overall_health": 0.0, "error": str(e)}

    # BPM Analysis Methods
    async def get_advanced_bpm_analysis(self, directory_id: Optional[str] = None):
        """Get advanced BPM analysis including clusters and transition matrix"""
        return await self.bpm_analyzer.get_advanced_bpm_analysis(directory_id)

    async def get_bpm_analysis(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """Get advanced BPM analysis with clustering"""
        return await self.bpm_analyzer.get_bpm_analysis(directory_id, folder_id)

    # Key Analysis Methods
    async def get_key_compatibility_network(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get key compatibility network for visualization"""
        return await self.key_analyzer.get_key_compatibility_network(directory_id, folder_id)

    async def analyze_key_relationships(self, directory_id: Optional[str] = None) -> Dict[str, Any]:
        """Analyze key relationships and compatibility patterns"""
        return await self.key_analyzer.analyze_key_relationships(directory_id)

    # Track Relationships and 3D Analysis Methods
    async def analyze_track_relationships(self, directory_id: Optional[str] = None) -> Dict:
        """Analyze track relationships in 3D space with optimized data processing"""
        try:
            from ...models.track import Track
            from ...models.analysis import Track3DPosition
            import numpy as np
            
            # Build query
            query = self.db.query(Track).filter(
                Track.bpm.isnot(None),
                Track.key.isnot(None),
                Track.energy.isnot(None)
            )
            
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            tracks = query.all()
            
            if len(tracks) < 2:
                return {
                    "positions": [],
                    "total_tracks": len(tracks),
                    "message": "Not enough tracks with complete data for 3D analysis"
                }

            # Create 3D positions based on BPM, Key, and Energy
            positions = []
            
            for track in tracks:
                # Normalize key to numeric value (1A=1, 1B=13, 2A=2, etc.)
                key_numeric = self._key_to_numeric(track.key)
                
                position = Track3DPosition(
                    track_id=track.id,
                    x=float(track.bpm) if track.bpm else 120.0,  # BPM as X
                    y=float(key_numeric),  # Key as Y
                    z=float(track.energy) if track.energy else 5.0,  # Energy as Z
                    title=track.title,
                    artist=track.artist,
                    bpm=track.bpm,
                    key=track.key,
                    energy=track.energy
                )
                positions.append(position.dict())

            # Calculate clustering and relationships
            relationships = self._calculate_3d_relationships(positions)

            return {
                "positions": positions,
                "relationships": relationships,
                "total_tracks": len(tracks),
                "analysis": self._generate_3d_analysis(positions, relationships)
            }

        except Exception as e:
            logger.error(f"Error analyzing track relationships: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error analyzing track relationships: {str(e)}"
            )

    async def get_track_relationships(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get track relationships for 3D visualization"""
        try:
            # Use the main analysis method
            analysis_result = await self.analyze_track_relationships(directory_id)
            
            # Format for visualization
            return {
                "nodes": analysis_result.get("positions", []),
                "edges": analysis_result.get("relationships", []),
                "metadata": {
                    "total_tracks": analysis_result.get("total_tracks", 0),
                    "analysis": analysis_result.get("analysis", "")
                }
            }

        except Exception as e:
            logger.error(f"Error getting track relationships: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting track relationships: {str(e)}"
            )

    # Health Score Configuration Methods
    async def update_health_score_config(self, config: Dict[str, float]) -> Dict[str, float]:
        """Update health score configuration weights"""
        try:
            # Validate config values
            required_keys = [
                "key_diversity", "artist_diversity", "genre_diversity",
                "bpm_completeness", "key_completeness", "energy_completeness",
                "bpm_balance", "mix_readiness"
            ]
            
            for key in required_keys:
                if key not in config:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Missing required config key: {key}"
                    )
                
                if not isinstance(config[key], (int, float)) or config[key] < 0 or config[key] > 1:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid value for {key}: must be between 0 and 1"
                    )

            # Check if weights sum to 1.0 (with some tolerance)
            total_weight = sum(config.values())
            if abs(total_weight - 1.0) > 0.01:
                raise HTTPException(
                    status_code=400,
                    detail=f"Config weights must sum to 1.0, got {total_weight}"
                )

            # Update or create config in database
            health_config = self.db.query(HealthScoreConfig).first()
            
            if health_config:
                # Update existing config
                for key, value in config.items():
                    setattr(health_config, key, value)
            else:
                # Create new config
                health_config = HealthScoreConfig(**config)
                self.db.add(health_config)

            self.db.commit()
            self.db.refresh(health_config)

            # Return the updated config
            return {
                "key_diversity": health_config.key_diversity,
                "artist_diversity": health_config.artist_diversity,
                "genre_diversity": health_config.genre_diversity,
                "bpm_completeness": health_config.bpm_completeness,
                "key_completeness": health_config.key_completeness,
                "energy_completeness": health_config.energy_completeness,
                "bpm_balance": health_config.bpm_balance,
                "mix_readiness": health_config.mix_readiness
            }

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating health score config: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error updating health score config: {str(e)}"
            )

    # Helper Methods
    def _key_to_numeric(self, key: str) -> float:
        """Convert Camelot key to numeric value for 3D positioning"""
        if not key:
            return 12.0  # Default middle value
        
        normalized_key = self.key_analyzer._normalize_key(key)
        
        try:
            # Extract number (1-12) and letter (A/B)
            number = int(''.join(filter(str.isdigit, normalized_key)))
            letter = normalized_key[-1] if normalized_key and normalized_key[-1] in ['A', 'B'] else 'A'
            
            # A keys: 1-12, B keys: 13-24
            return float(number + (12 if letter == 'B' else 0))
        except:
            return 12.0  # Default fallback

    def _calculate_3d_relationships(self, positions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate relationships between tracks in 3D space"""
        relationships = []
        
        for i, pos1 in enumerate(positions):
            for j, pos2 in enumerate(positions[i+1:], i+1):
                # Calculate 3D distance
                distance = (
                    (pos1['x'] - pos2['x']) ** 2 +
                    (pos1['y'] - pos2['y']) ** 2 +
                    (pos1['z'] - pos2['z']) ** 2
                ) ** 0.5
                
                # Only include close relationships (arbitrary threshold)
                if distance < 50:  # Adjust threshold as needed
                    relationships.append({
                        "from": pos1['track_id'],
                        "to": pos2['track_id'],
                        "distance": distance,
                        "strength": max(0, 1 - distance / 50)  # Normalize to 0-1
                    })
        
        return relationships

    def _generate_3d_analysis(self, positions: List[Dict[str, Any]], relationships: List[Dict[str, Any]]) -> str:
        """Generate text analysis of 3D track relationships"""
        if not positions:
            return "No tracks available for analysis"
        
        analysis_parts = []
        
        # Clustering analysis
        if len(relationships) > len(positions) * 0.1:
            analysis_parts.append("Tracks show good clustering - many similar tracks for smooth mixing")
        else:
            analysis_parts.append("Tracks are widely distributed - may require more advanced mixing techniques")
        
        # Density analysis
        total_possible_relationships = len(positions) * (len(positions) - 1) / 2
        relationship_density = len(relationships) / total_possible_relationships if total_possible_relationships > 0 else 0
        
        if relationship_density > 0.3:
            analysis_parts.append("High relationship density indicates good mix compatibility")
        elif relationship_density > 0.1:
            analysis_parts.append("Moderate relationship density")
        else:
            analysis_parts.append("Low relationship density - collection may benefit from more similar tracks")
        
        return ". ".join(analysis_parts) + "."
