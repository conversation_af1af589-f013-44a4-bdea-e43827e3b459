"""
Collection analysis functionality.
Comprehensive analysis of music collections including health metrics and recommendations.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException

from ...models.track import Track
from ...models.folder import Folder
from ...models.health_score import HealthScoreConfig
from ...config import settings

logger = logging.getLogger(__name__)

class CollectionAnalyzer:
    """Analyzes music collections for health metrics and recommendations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.mix_styles = settings.mix_styles
        self.bpm_ranges = {}

        for style, config in self.mix_styles.items():
            if 'min_bpm' in config and 'max_bpm' in config:
                self.bpm_ranges[style] = (float(config['min_bpm']), float(config['max_bpm']))
            elif 'bpm_range' in config and isinstance(config['bpm_range'], list) and len(config['bpm_range']) >= 2:
                self.bpm_ranges[style] = (float(config['bpm_range'][0]), float(config['bpm_range'][1]))
            else:
                self.bpm_ranges[style] = (settings.DEFAULT_BPM_RANGE_MIN, settings.DEFAULT_BPM_RANGE_MAX)

    async def analyze_collection(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive analysis of the collection"""
        try:
            # Build query based on parameters
            query = self.db.query(Track)
            
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)
            
            if folder_id:
                # Get folder and its subfolders
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_ids = [folder_id]
                    # Add subfolder IDs recursively
                    subfolders = self.db.query(Folder).filter(Folder.parent_id == folder_id).all()
                    for subfolder in subfolders:
                        folder_ids.append(subfolder.id)
                    
                    query = query.filter(Track.folder_id.in_(folder_ids))

            tracks = query.all()
            
            if not tracks:
                return {
                    "total_tracks": 0,
                    "message": "No tracks found in the specified collection"
                }

            # Convert to DataFrame for analysis
            track_data = []
            for track in tracks:
                track_data.append({
                    'id': track.id,
                    'title': track.title or '',
                    'artist': track.artist or '',
                    'album': track.album or '',
                    'genre': track.genre or '',
                    'bpm': track.bpm or 0,
                    'key': track.key or '',
                    'energy': track.energy or 0,
                    'danceability': track.danceability or 0,
                    'valence': track.valence or 0,
                    'duration': track.duration or 0
                })

            df = pd.DataFrame(track_data)

            # Perform comprehensive analysis
            analysis = {
                "total_tracks": len(df),
                "key_distribution": self._analyze_key_distribution(df),
                "artist_distribution": self._analyze_artist_distribution(df),
                "bpm_distribution": self._analyze_bpm_distribution(df),
                "energy_distribution": self._analyze_energy_distribution(df),
                "genre_coverage": self._analyze_genre_coverage(df),
                "mix_style_coverage": self._analyze_mix_style_coverage(df),
                "collection_gaps": self._identify_collection_gaps(df),
                "recommendations": self._generate_recommendations(df),
                "health_metrics": self._calculate_collection_health(df)
            }

            return analysis

        except Exception as e:
            logger.error(f"Error analyzing collection: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error analyzing collection: {str(e)}"
            )

    def _analyze_key_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze key distribution and identify imbalances"""
        return df['key'].value_counts().to_dict()

    def _analyze_artist_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze artist distribution and identify potential overrepresentation"""
        return df['artist'].value_counts().to_dict()

    def _analyze_bpm_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze BPM distribution across common ranges"""
        try:
            bpm_ranges = {
                "60-90 (Slow)": 0,
                "90-110 (Medium-Slow)": 0,
                "110-130 (Medium)": 0,
                "130-150 (Medium-Fast)": 0,
                "150-180 (Fast)": 0,
                "180+ (Very Fast)": 0
            }

            for _, row in df.iterrows():
                bpm = row['bpm']
                if pd.isna(bpm) or bpm == 0:
                    continue
                
                if bpm < 90:
                    bpm_ranges["60-90 (Slow)"] += 1
                elif bpm < 110:
                    bpm_ranges["90-110 (Medium-Slow)"] += 1
                elif bpm < 130:
                    bpm_ranges["110-130 (Medium)"] += 1
                elif bpm < 150:
                    bpm_ranges["130-150 (Medium-Fast)"] += 1
                elif bpm < 180:
                    bpm_ranges["150-180 (Fast)"] += 1
                else:
                    bpm_ranges["180+ (Very Fast)"] += 1

            return bpm_ranges

        except Exception as e:
            logger.error(f"Error analyzing BPM distribution: {e}")
            return {}

    def _analyze_energy_distribution(self, df: pd.DataFrame) -> Dict[str, float]:
        """Analyze energy level distribution"""
        energy_ranges = {
            "Low (1-3)": 0,
            "Medium (4-6)": 0,
            "High (7-10)": 0
        }

        for _, row in df.iterrows():
            energy = row['energy']
            if pd.isna(energy) or energy == 0:
                continue
            
            if energy <= 3:
                energy_ranges["Low (1-3)"] += 1
            elif energy <= 6:
                energy_ranges["Medium (4-6)"] += 1
            else:
                energy_ranges["High (7-10)"] += 1

        return energy_ranges

    def _analyze_genre_coverage(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze genre coverage and identify missing genres"""
        if 'genre' not in df.columns:
            return {}

        # Get actual genre distribution
        genre_counts = df['genre'].value_counts().to_dict()
        
        # Remove empty genres
        genre_counts = {k: v for k, v in genre_counts.items() if k and k.strip()}
        
        # If no genres found, try to infer from metadata
        if not genre_counts:
            inferred_genres = self._infer_genres_from_metadata(df)
            if inferred_genres:
                genre_counts.update(inferred_genres)

        return genre_counts

    def _infer_genres_from_metadata(self, df: pd.DataFrame) -> Dict[str, int]:
        """Infer genres from track titles and artists when genre data is missing"""
        genre_keywords = {
            'house': ['house', 'deep house', 'tech house', 'progressive house'],
            'techno': ['techno', 'minimal', 'detroit'],
            'trance': ['trance', 'uplifting', 'progressive trance'],
            'drum_and_bass': ['drum and bass', 'dnb', 'd&b', 'jungle'],
            'dubstep': ['dubstep', 'brostep', 'future bass'],
            'ambient': ['ambient', 'chillout', 'downtempo'],
            'hip_hop': ['hip hop', 'rap', 'trap'],
            'pop': ['pop', 'mainstream', 'radio'],
            'rock': ['rock', 'alternative', 'indie'],
            'electronic': ['electronic', 'edm', 'dance']
        }

        inferred_counts = {}
        
        for _, row in df.iterrows():
            title = str(row['title']).lower()
            artist = str(row['artist']).lower()
            combined_text = f"{title} {artist}"
            
            for genre, keywords in genre_keywords.items():
                if any(keyword in combined_text for keyword in keywords):
                    inferred_counts[genre] = inferred_counts.get(genre, 0) + 1
                    break

        return inferred_counts

    def _analyze_mix_style_coverage(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Analyze coverage for different mix styles based on BPM, key, and energy"""
        if len(df) == 0:
            return {}

        style_coverage = {}
        
        for style, bpm_range in self.bpm_ranges.items():
            min_bpm, max_bpm = bpm_range
            
            # Filter tracks that match this style's BPM range
            style_tracks = df[
                (df['bpm'] >= min_bpm) & 
                (df['bpm'] <= max_bpm) & 
                (df['bpm'] > 0)
            ]
            
            coverage = {
                "track_count": len(style_tracks),
                "percentage": (len(style_tracks) / len(df)) * 100 if len(df) > 0 else 0,
                "bpm_range": f"{min_bpm}-{max_bpm}",
                "avg_energy": style_tracks['energy'].mean() if len(style_tracks) > 0 else 0,
                "key_variety": len(style_tracks['key'].unique()) if len(style_tracks) > 0 else 0,
                "top_keys": style_tracks['key'].value_counts().head(3).to_dict() if len(style_tracks) > 0 else {},
                "adequacy": "Good" if len(style_tracks) >= 10 else "Limited" if len(style_tracks) >= 3 else "Poor"
            }
            
            style_coverage[style] = coverage

        return style_coverage

    def _identify_collection_gaps(self, df: pd.DataFrame) -> Dict[str, List[Dict[str, Any]]]:
        """Identify gaps in the collection that could affect mix generation"""
        gaps = {
            "bpm_gaps": [],
            "key_gaps": [],
            "energy_gaps": [],
            "style_gaps": []
        }

        # BPM gaps - identify ranges with few tracks
        bpm_distribution = self._analyze_bpm_distribution(df)
        for range_name, count in bpm_distribution.items():
            if count < 5:  # Threshold for "gap"
                gaps["bpm_gaps"].append({
                    "range": range_name,
                    "current_count": count,
                    "recommended_minimum": 5,
                    "severity": "High" if count == 0 else "Medium"
                })

        # Key gaps - identify missing or underrepresented keys
        key_distribution = self._analyze_key_distribution(df)
        common_keys = ['1A', '2A', '3A', '4A', '5A', '6A', '7A', '8A', '9A', '10A', '11A', '12A',
                      '1B', '2B', '3B', '4B', '5B', '6B', '7B', '8B', '9B', '10B', '11B', '12B']
        
        for key in common_keys:
            count = key_distribution.get(key, 0)
            if count < 3:  # Threshold for key gap
                gaps["key_gaps"].append({
                    "key": key,
                    "current_count": count,
                    "recommended_minimum": 3,
                    "severity": "High" if count == 0 else "Medium"
                })

        # Energy gaps
        energy_distribution = self._analyze_energy_distribution(df)
        for range_name, count in energy_distribution.items():
            if count < 10:  # Threshold for energy gap
                gaps["energy_gaps"].append({
                    "range": range_name,
                    "current_count": count,
                    "recommended_minimum": 10,
                    "severity": "High" if count == 0 else "Medium"
                })

        # Style gaps
        style_coverage = self._analyze_mix_style_coverage(df)
        for style, coverage in style_coverage.items():
            if coverage["adequacy"] in ["Poor", "Limited"]:
                gaps["style_gaps"].append({
                    "style": style,
                    "current_count": coverage["track_count"],
                    "adequacy": coverage["adequacy"],
                    "bpm_range": coverage["bpm_range"],
                    "severity": "High" if coverage["adequacy"] == "Poor" else "Medium"
                })

        return gaps

    def _generate_recommendations(self, df: pd.DataFrame) -> Dict[str, List[Dict[str, Any]]]:
        """Generate recommendations for improving the collection"""
        recommendations = {
            "acquisition": [],
            "organization": [],
            "analysis": []
        }

        # Acquisition recommendations based on gaps
        gaps = self._identify_collection_gaps(df)

        # BPM-based recommendations
        for gap in gaps["bpm_gaps"]:
            if gap["severity"] == "High":
                recommendations["acquisition"].append({
                    "type": "bpm_range",
                    "priority": "High",
                    "description": f"Add more tracks in {gap['range']} BPM range",
                    "current_count": gap["current_count"],
                    "target_count": gap["recommended_minimum"],
                    "example_artists": self._get_example_artists_for_bpm_range(gap["range"])
                })

        # Key-based recommendations
        for gap in gaps["key_gaps"]:
            if gap["severity"] == "High":
                recommendations["acquisition"].append({
                    "type": "key",
                    "priority": "High",
                    "description": f"Add more tracks in key {gap['key']}",
                    "current_count": gap["current_count"],
                    "target_count": gap["recommended_minimum"],
                    "example_tracks": self._get_example_tracks_for_key(gap["key"])
                })

        # Style-based recommendations
        for gap in gaps["style_gaps"]:
            if gap["severity"] == "High":
                recommendations["acquisition"].append({
                    "type": "style",
                    "priority": "High",
                    "description": f"Add more {gap['style']} tracks ({gap['bpm_range']} BPM)",
                    "current_count": gap["current_count"],
                    "target_count": 10,
                    "example_artists": self._get_example_artists_for_style(gap["style"])
                })

        # Organization recommendations
        genre_coverage = self._analyze_genre_coverage(df)
        if not genre_coverage or len(genre_coverage) < 3:
            recommendations["organization"].append({
                "type": "genre_tagging",
                "priority": "Medium",
                "description": "Improve genre tagging for better organization",
                "affected_tracks": len(df[df['genre'].isin(['', None])]) if 'genre' in df.columns else len(df)
            })

        # Analysis recommendations
        missing_bpm = len(df[df['bpm'] == 0]) if 'bpm' in df.columns else 0
        if missing_bpm > 0:
            recommendations["analysis"].append({
                "type": "bpm_analysis",
                "priority": "High",
                "description": "Analyze BPM for tracks missing tempo information",
                "affected_tracks": missing_bpm
            })

        missing_key = len(df[df['key'].isin(['', None])]) if 'key' in df.columns else 0
        if missing_key > 0:
            recommendations["analysis"].append({
                "type": "key_analysis",
                "priority": "Medium",
                "description": "Analyze musical key for tracks missing key information",
                "affected_tracks": missing_key
            })

        return recommendations

    def _get_example_artists_for_bpm_range(self, bpm_range: str) -> List[str]:
        """Get example artists for a specific BPM range"""
        bpm_artists = {
            "60-90 (Slow)": ["Bonobo", "Thievery Corporation", "Zero 7"],
            "90-110 (Medium-Slow)": ["Disclosure", "ODESZA", "Flume"],
            "110-130 (Medium)": ["Calvin Harris", "David Guetta", "Swedish House Mafia"],
            "130-150 (Medium-Fast)": ["Deadmau5", "Eric Prydz", "Above & Beyond"],
            "150-180 (Fast)": ["Armin van Buuren", "Paul van Dyk", "Ferry Corsten"],
            "180+ (Very Fast)": ["Angerfist", "Headhunterz", "Noisia"]
        }
        return bpm_artists.get(bpm_range, [])

    def _get_example_artists_for_style(self, style: str) -> List[str]:
        """Get example artists for a specific style"""
        style_artists = {
            "house": ["Disclosure", "Duke Dumont", "MK", "Gorgon City"],
            "techno": ["Carl Cox", "Adam Beyer", "Charlotte de Witte", "Amelie Lens"],
            "trance": ["Armin van Buuren", "Above & Beyond", "Paul van Dyk", "Ferry Corsten"],
            "progressive": ["Eric Prydz", "Deadmau5", "Sasha", "John Digweed"],
            "deep_house": ["Bonobo", "Kiara", "Lane 8", "Yotto"],
            "drum_and_bass": ["Netsky", "London Elektricity", "High Contrast", "LTJ Bukem"]
        }
        return style_artists.get(style, [])

    def _get_example_tracks_for_key(self, key: str) -> List[str]:
        """Get example track suggestions for a specific key"""
        # This would ideally come from a database of popular tracks by key
        return [f"Popular track in {key}", f"Classic track in {key}", f"Recent hit in {key}"]

    def _calculate_collection_health(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate various health metrics for the collection"""
        if len(df) == 0:
            return {"overall_health": 0.0}

        health_metrics = {}

        # Diversity metrics
        key_diversity = len(df['key'].unique()) / 24.0 if 'key' in df.columns else 0  # 24 possible keys
        artist_diversity = min(len(df['artist'].unique()) / len(df), 1.0) if 'artist' in df.columns else 0
        genre_diversity = len(df['genre'].unique()) / 10.0 if 'genre' in df.columns else 0  # Assume 10 main genres

        health_metrics["key_diversity"] = min(key_diversity, 1.0)
        health_metrics["artist_diversity"] = artist_diversity
        health_metrics["genre_diversity"] = min(genre_diversity, 1.0)

        # Completeness metrics
        bpm_completeness = len(df[df['bpm'] > 0]) / len(df) if 'bpm' in df.columns else 0
        key_completeness = len(df[df['key'] != '']) / len(df) if 'key' in df.columns else 0
        energy_completeness = len(df[df['energy'] > 0]) / len(df) if 'energy' in df.columns else 0

        health_metrics["bpm_completeness"] = bpm_completeness
        health_metrics["key_completeness"] = key_completeness
        health_metrics["energy_completeness"] = energy_completeness

        # Balance metrics
        bpm_distribution = self._analyze_bpm_distribution(df)
        bpm_balance = 1.0 - (np.std(list(bpm_distribution.values())) / np.mean(list(bpm_distribution.values()))) if bpm_distribution else 0
        health_metrics["bpm_balance"] = max(0.0, min(1.0, bpm_balance))

        # Mix readiness - how ready the collection is for mixing
        style_coverage = self._analyze_mix_style_coverage(df)
        adequate_styles = sum(1 for coverage in style_coverage.values() if coverage["adequacy"] == "Good")
        mix_readiness = adequate_styles / len(style_coverage) if style_coverage else 0
        health_metrics["mix_readiness"] = mix_readiness

        # Overall health score (weighted average)
        weights = {
            "key_diversity": 0.15,
            "artist_diversity": 0.10,
            "genre_diversity": 0.10,
            "bpm_completeness": 0.20,
            "key_completeness": 0.15,
            "energy_completeness": 0.10,
            "bpm_balance": 0.10,
            "mix_readiness": 0.10
        }

        overall_health = sum(health_metrics[metric] * weight for metric, weight in weights.items())
        health_metrics["overall_health"] = overall_health

        return health_metrics
