"""
Key compatibility analysis functionality.
Analyzes musical key relationships and compatibility for harmonic mixing.
"""

import logging
import networkx as nx
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException

from ...models.track import Track
from ...models.folder import Folder
from ...models.analysis import Key<PERSON>workNode, KeyNetworkEdge, KeyNetworkData
from ...utils.camelot_rules import CamelotRules

logger = logging.getLogger(__name__)

class KeyAnalyzer:
    """Analyzes musical key compatibility and relationships"""
    
    def __init__(self, db: Session):
        self.db = db

    def _normalize_key(self, key: str) -> str:
        """Normalize key to standard format (e.g., '7A/2', '7Am', '7a' -> '7A')"""
        if not key:
            return ""
        
        # Remove common separators and extra characters
        key = key.replace('/', '').replace('-', '').replace('_', '').strip()
        
        # Handle different formats
        if len(key) >= 2:
            # Extract number and letter
            number_part = ""
            letter_part = ""
            
            for char in key:
                if char.isdigit():
                    number_part += char
                elif char.upper() in ['A', 'B']:
                    letter_part = char.upper()
                    break
            
            if number_part and letter_part:
                return f"{number_part}{letter_part}"
        
        return key.upper()

    async def get_key_compatibility_network(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get key compatibility network for visualization"""
        try:
            # Build query
            query = self.db.query(Track).filter(Track.key.isnot(None), Track.key != '')
            
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)
            
            if folder_id:
                # Get folder and its subfolders
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_ids = [folder_id]
                    # Add subfolder IDs recursively
                    subfolders = self.db.query(Folder).filter(Folder.parent_id == folder_id).all()
                    for subfolder in subfolders:
                        folder_ids.append(subfolder.id)
                    
                    query = query.filter(Track.folder_id.in_(folder_ids))

            tracks = query.all()
            
            if not tracks:
                return {
                    "nodes": [],
                    "edges": [],
                    "message": "No tracks with key data found"
                }

            # Normalize keys and count tracks per key
            key_counts = {}
            key_tracks = {}
            
            for track in tracks:
                normalized_key = self._normalize_key(track.key)
                if normalized_key:
                    key_counts[normalized_key] = key_counts.get(normalized_key, 0) + 1
                    if normalized_key not in key_tracks:
                        key_tracks[normalized_key] = []
                    key_tracks[normalized_key].append({
                        'id': track.id,
                        'title': track.title,
                        'artist': track.artist,
                        'bpm': track.bpm
                    })

            # Create nodes
            nodes = []
            for key, count in key_counts.items():
                # Calculate node size based on track count
                size = min(50, max(10, count * 5))
                
                # Determine color based on key type (A = minor, B = major)
                color = "#FF6B6B" if key.endswith('A') else "#4ECDC4"
                
                nodes.append({
                    "id": key,
                    "label": key,
                    "size": size,
                    "color": color,
                    "track_count": count,
                    "tracks": key_tracks[key][:5]  # Limit to first 5 tracks for display
                })

            # Create edges based on Camelot wheel compatibility
            edges = []
            camelot_rules = CamelotRules()
            
            for key1 in key_counts.keys():
                for key2 in key_counts.keys():
                    if key1 != key2:
                        # Check if keys are compatible
                        compatibility = camelot_rules.get_compatibility(key1, key2)
                        
                        if compatibility > 0:
                            # Calculate edge weight based on compatibility and track counts
                            weight = compatibility * min(key_counts[key1], key_counts[key2])
                            
                            # Determine edge color based on compatibility type
                            if compatibility >= 0.9:
                                color = "#2ECC71"  # Perfect compatibility
                            elif compatibility >= 0.7:
                                color = "#F39C12"  # Good compatibility
                            else:
                                color = "#E74C3C"  # Limited compatibility
                            
                            edges.append({
                                "from": key1,
                                "to": key2,
                                "weight": weight,
                                "color": color,
                                "compatibility": compatibility,
                                "label": f"{compatibility:.1f}"
                            })

            # Calculate network statistics
            G = nx.Graph()
            for node in nodes:
                G.add_node(node["id"], **node)
            for edge in edges:
                G.add_edge(edge["from"], edge["to"], weight=edge["weight"])

            # Network metrics
            network_stats = {
                "total_keys": len(nodes),
                "total_connections": len(edges),
                "density": nx.density(G) if len(nodes) > 1 else 0,
                "average_clustering": nx.average_clustering(G) if len(nodes) > 2 else 0,
                "connected_components": nx.number_connected_components(G)
            }

            # Identify key gaps (missing keys that would improve connectivity)
            all_camelot_keys = [f"{i}{letter}" for i in range(1, 13) for letter in ['A', 'B']]
            missing_keys = [key for key in all_camelot_keys if key not in key_counts]
            
            # Find which missing keys would create the most connections
            key_gaps = []
            for missing_key in missing_keys[:10]:  # Limit to top 10
                potential_connections = 0
                for existing_key in key_counts.keys():
                    if camelot_rules.get_compatibility(missing_key, existing_key) > 0:
                        potential_connections += 1
                
                if potential_connections > 0:
                    key_gaps.append({
                        "key": missing_key,
                        "potential_connections": potential_connections,
                        "priority": "High" if potential_connections >= 5 else "Medium"
                    })

            # Sort by potential connections
            key_gaps.sort(key=lambda x: x["potential_connections"], reverse=True)

            return {
                "nodes": nodes,
                "edges": edges,
                "statistics": network_stats,
                "key_gaps": key_gaps[:5],  # Top 5 missing keys
                "analysis": self._generate_key_network_analysis(network_stats, key_counts, key_gaps)
            }

        except Exception as e:
            logger.error(f"Error in key compatibility network analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error in key compatibility network analysis: {str(e)}"
            )

    def _generate_key_network_analysis(self, stats: Dict[str, Any], key_counts: Dict[str, int], gaps: List[Dict[str, Any]]) -> str:
        """Generate text analysis of the key network"""
        analysis_parts = []
        
        # Overall assessment
        if stats["total_keys"] >= 20:
            analysis_parts.append("Excellent key diversity with comprehensive coverage")
        elif stats["total_keys"] >= 15:
            analysis_parts.append("Good key diversity with solid coverage")
        elif stats["total_keys"] >= 10:
            analysis_parts.append("Moderate key diversity")
        else:
            analysis_parts.append("Limited key diversity - consider expanding key range")

        # Connectivity assessment
        if stats["density"] >= 0.7:
            analysis_parts.append("High key connectivity - excellent for harmonic mixing")
        elif stats["density"] >= 0.5:
            analysis_parts.append("Good key connectivity for mixing")
        elif stats["density"] >= 0.3:
            analysis_parts.append("Moderate key connectivity")
        else:
            analysis_parts.append("Low key connectivity - may limit mixing options")

        # Gap analysis
        if len(gaps) > 0:
            top_gap = gaps[0]
            analysis_parts.append(f"Adding tracks in key {top_gap['key']} would create {top_gap['potential_connections']} new mixing possibilities")

        # Balance analysis
        key_values = list(key_counts.values())
        if len(key_values) > 1:
            max_count = max(key_values)
            min_count = min(key_values)
            if max_count / min_count > 5:
                analysis_parts.append("Key distribution is unbalanced - some keys are overrepresented")

        return ". ".join(analysis_parts) + "."

    async def analyze_key_relationships(self, directory_id: Optional[str] = None) -> Dict[str, Any]:
        """Analyze key relationships and compatibility patterns"""
        try:
            # Build query
            query = self.db.query(Track).filter(Track.key.isnot(None), Track.key != '')
            
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            tracks = query.all()
            
            if not tracks:
                return {
                    "total_tracks": 0,
                    "message": "No tracks with key data found"
                }

            # Normalize keys and analyze
            key_data = {}
            camelot_rules = CamelotRules()
            
            for track in tracks:
                normalized_key = self._normalize_key(track.key)
                if normalized_key:
                    if normalized_key not in key_data:
                        key_data[normalized_key] = {
                            "count": 0,
                            "tracks": [],
                            "avg_bpm": 0,
                            "avg_energy": 0
                        }
                    
                    key_data[normalized_key]["count"] += 1
                    key_data[normalized_key]["tracks"].append(track.id)
                    
                    if track.bpm:
                        key_data[normalized_key]["avg_bpm"] += track.bpm
                    if track.energy:
                        key_data[normalized_key]["avg_energy"] += track.energy

            # Calculate averages
            for key, data in key_data.items():
                if data["count"] > 0:
                    data["avg_bpm"] /= data["count"]
                    data["avg_energy"] /= data["count"]

            # Analyze compatibility patterns
            compatibility_matrix = {}
            for key1 in key_data.keys():
                compatibility_matrix[key1] = {}
                for key2 in key_data.keys():
                    if key1 != key2:
                        compatibility = camelot_rules.get_compatibility(key1, key2)
                        compatibility_matrix[key1][key2] = compatibility

            # Find most/least compatible keys
            most_compatible = []
            least_compatible = []
            
            for key1, compatibilities in compatibility_matrix.items():
                total_compatibility = sum(compatibilities.values())
                avg_compatibility = total_compatibility / len(compatibilities) if compatibilities else 0
                
                most_compatible.append({
                    "key": key1,
                    "avg_compatibility": avg_compatibility,
                    "track_count": key_data[key1]["count"]
                })

            most_compatible.sort(key=lambda x: x["avg_compatibility"], reverse=True)
            least_compatible = most_compatible[-5:]  # Bottom 5
            most_compatible = most_compatible[:5]   # Top 5

            return {
                "total_tracks": len(tracks),
                "total_keys": len(key_data),
                "key_distribution": {k: v["count"] for k, v in key_data.items()},
                "compatibility_matrix": compatibility_matrix,
                "most_compatible_keys": most_compatible,
                "least_compatible_keys": least_compatible,
                "key_statistics": self._calculate_key_statistics(key_data),
                "recommendations": self._generate_key_recommendations(key_data, most_compatible, least_compatible)
            }

        except Exception as e:
            logger.error(f"Error analyzing key relationships: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error analyzing key relationships: {str(e)}"
            )

    def _calculate_key_statistics(self, key_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistical metrics for key distribution"""
        if not key_data:
            return {}

        counts = [data["count"] for data in key_data.values()]
        
        # Separate major and minor keys
        major_keys = {k: v for k, v in key_data.items() if k.endswith('B')}
        minor_keys = {k: v for k, v in key_data.items() if k.endswith('A')}

        return {
            "total_keys": len(key_data),
            "major_keys": len(major_keys),
            "minor_keys": len(minor_keys),
            "major_minor_ratio": len(major_keys) / len(minor_keys) if minor_keys else float('inf'),
            "most_common_key": max(key_data.keys(), key=lambda k: key_data[k]["count"]),
            "least_common_key": min(key_data.keys(), key=lambda k: key_data[k]["count"]),
            "average_tracks_per_key": sum(counts) / len(counts),
            "key_distribution_balance": min(counts) / max(counts) if max(counts) > 0 else 0
        }

    def _generate_key_recommendations(self, key_data: Dict[str, Any], most_compatible: List[Dict[str, Any]], least_compatible: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate recommendations for improving key distribution"""
        recommendations = []

        # Check for missing keys that would improve compatibility
        all_camelot_keys = [f"{i}{letter}" for i in range(1, 13) for letter in ['A', 'B']]
        missing_keys = [key for key in all_camelot_keys if key not in key_data]

        if len(missing_keys) > 12:  # More than half missing
            recommendations.append({
                "type": "key_expansion",
                "priority": "High",
                "description": f"Add tracks in {len(missing_keys)} missing keys to improve mixing flexibility",
                "missing_keys": missing_keys[:6]  # Show first 6
            })

        # Check for unbalanced distribution
        if key_data:
            counts = [data["count"] for data in key_data.values()]
            if max(counts) / min(counts) > 5:
                overrepresented = max(key_data.keys(), key=lambda k: key_data[k]["count"])
                underrepresented = min(key_data.keys(), key=lambda k: key_data[k]["count"])
                
                recommendations.append({
                    "type": "balance_improvement",
                    "priority": "Medium",
                    "description": f"Balance key distribution - key {overrepresented} is overrepresented, key {underrepresented} is underrepresented"
                })

        # Recommend focusing on highly compatible keys
        if most_compatible:
            top_key = most_compatible[0]
            recommendations.append({
                "type": "compatibility_focus",
                "priority": "Low",
                "description": f"Key {top_key['key']} has the highest compatibility - consider adding more tracks in this key",
                "key": top_key['key'],
                "compatibility_score": top_key['avg_compatibility']
            })

        return recommendations
