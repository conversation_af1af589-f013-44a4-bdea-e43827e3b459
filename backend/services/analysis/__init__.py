"""
Analysis Service Package

Modular analysis system with specialized components for track analysis,
collection health, BPM analysis, and key compatibility.
"""

# Export the main service
from .analysis_service import AnalysisService

# Export individual components for direct access if needed
from .track_analysis_jobs import TrackAnalysisJobManager
from .collection_analyzer import CollectionAnalyzer
from .bpm_analyzer import BPMAnalyzer
from .key_analyzer import KeyAnalyzer

__all__ = [
    'AnalysisService',
    'TrackAnalysisJobManager',
    'CollectionAnalyzer',
    'BPMAnalyzer',
    'KeyAnalyzer'
]
