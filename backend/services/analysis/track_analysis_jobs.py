"""
Track analysis job management.
Handles creation, updating, and monitoring of track analysis jobs.
"""

import logging
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import update
from fastapi import HTTPException

from ...models.track import Track
from ...models.track_analysis import TrackAnalysis, AnalysisStatus

logger = logging.getLogger(__name__)

class TrackAnalysisJobManager:
    """Manages track analysis jobs"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_analysis_job(self, track_id: int) -> TrackAnalysis:
        """Create a new track analysis job"""
        try:
            # Check if track exists
            track = self.db.query(Track).filter(Track.id == track_id).first()
            if not track:
                raise HTTPException(status_code=404, detail=f"Track {track_id} not found")

            # Check if analysis already exists
            existing_analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.track_id == track_id
            ).first()

            if existing_analysis:
                # Get raw status value from SQLAlchemy column
                status_value = str(existing_analysis.status.scalar() if hasattr(existing_analysis.status, 'scalar') else existing_analysis.status)
                if status_value == str(AnalysisStatus.PROCESSING):
                    return existing_analysis

                # Reset failed or canceled analysis
                if status_value in [str(AnalysisStatus.ERROR), str(AnalysisStatus.CANCELED)]:
                    self.db.execute(
                        update(TrackAnalysis)
                        .where(TrackAnalysis.id == existing_analysis.id)
                        .values(
                            status=str(AnalysisStatus.PENDING),
                            error_message=None
                        )
                    )
                    self.db.commit()
                    self.db.refresh(existing_analysis)
                    return existing_analysis
                return existing_analysis

            # Create new analysis job
            new_analysis = TrackAnalysis(
                track_id=track_id,
                status=str(AnalysisStatus.PENDING)
            )
            self.db.add(new_analysis)
            self.db.commit()
            self.db.refresh(new_analysis)
            return new_analysis

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating analysis job: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error creating analysis job: {str(e)}"
            )

    async def update_analysis_status(
        self,
        analysis_id: int,
        status: AnalysisStatus,
        progress: Optional[float] = None,
        error_message: Optional[str] = None,
        results: Optional[dict] = None
    ) -> TrackAnalysis:
        """Update the status of an analysis job"""
        try:
            analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.id == analysis_id
            ).first()
            
            if not analysis:
                raise HTTPException(status_code=404, detail=f"Analysis {analysis_id} not found")

            # Prepare update values
            update_values = {"status": str(status)}
            
            if progress is not None:
                update_values["progress"] = progress
            if error_message is not None:
                update_values["error_message"] = error_message
            if results is not None:
                update_values["results"] = results

            # Update the analysis
            self.db.execute(
                update(TrackAnalysis)
                .where(TrackAnalysis.id == analysis_id)
                .values(**update_values)
            )
            self.db.commit()
            self.db.refresh(analysis)
            return analysis

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating analysis status: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error updating analysis status: {str(e)}"
            )

    async def get_analysis_status(self, track_id: int) -> Optional[TrackAnalysis]:
        """Get the analysis status for a track"""
        try:
            return self.db.query(TrackAnalysis).filter(
                TrackAnalysis.track_id == track_id
            ).first()
        except Exception as e:
            logger.error(f"Error getting analysis status: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting analysis status: {str(e)}"
            )

    async def delete_analysis(self, track_id: int) -> bool:
        """Delete analysis data for a track"""
        try:
            analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.track_id == track_id
            ).first()
            
            if analysis:
                self.db.delete(analysis)
                self.db.commit()
                return True
            return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting analysis: {str(e)}"
            )

    async def get_pending_analyses(self, limit: int = 10) -> list[TrackAnalysis]:
        """Get pending analysis jobs"""
        try:
            return self.db.query(TrackAnalysis).filter(
                TrackAnalysis.status == str(AnalysisStatus.PENDING)
            ).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting pending analyses: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting pending analyses: {str(e)}"
            )

    async def get_processing_analyses(self) -> list[TrackAnalysis]:
        """Get currently processing analysis jobs"""
        try:
            return self.db.query(TrackAnalysis).filter(
                TrackAnalysis.status == str(AnalysisStatus.PROCESSING)
            ).all()
        except Exception as e:
            logger.error(f"Error getting processing analyses: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting processing analyses: {str(e)}"
            )

    async def get_failed_analyses(self, limit: int = 50) -> list[TrackAnalysis]:
        """Get failed analysis jobs"""
        try:
            return self.db.query(TrackAnalysis).filter(
                TrackAnalysis.status == str(AnalysisStatus.ERROR)
            ).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting failed analyses: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting failed analyses: {str(e)}"
            )

    async def retry_failed_analysis(self, analysis_id: int) -> TrackAnalysis:
        """Retry a failed analysis job"""
        try:
            analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.id == analysis_id
            ).first()
            
            if not analysis:
                raise HTTPException(status_code=404, detail=f"Analysis {analysis_id} not found")

            # Reset to pending status
            self.db.execute(
                update(TrackAnalysis)
                .where(TrackAnalysis.id == analysis_id)
                .values(
                    status=str(AnalysisStatus.PENDING),
                    progress=0.0,
                    error_message=None
                )
            )
            self.db.commit()
            self.db.refresh(analysis)
            return analysis

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error retrying failed analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error retrying failed analysis: {str(e)}"
            )

    async def cancel_analysis(self, analysis_id: int) -> TrackAnalysis:
        """Cancel a running analysis job"""
        try:
            analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.id == analysis_id
            ).first()
            
            if not analysis:
                raise HTTPException(status_code=404, detail=f"Analysis {analysis_id} not found")

            # Set to canceled status
            self.db.execute(
                update(TrackAnalysis)
                .where(TrackAnalysis.id == analysis_id)
                .values(status=str(AnalysisStatus.CANCELED))
            )
            self.db.commit()
            self.db.refresh(analysis)
            return analysis

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error canceling analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error canceling analysis: {str(e)}"
            )

    async def cleanup_old_analyses(self, days_old: int = 30) -> int:
        """Clean up old completed or failed analyses"""
        try:
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            # Delete old completed or failed analyses
            deleted_count = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.status.in_([
                    str(AnalysisStatus.COMPLETED),
                    str(AnalysisStatus.ERROR),
                    str(AnalysisStatus.CANCELED)
                ]),
                TrackAnalysis.updated_at < cutoff_date
            ).delete()
            
            self.db.commit()
            logger.info(f"Cleaned up {deleted_count} old analysis jobs")
            return deleted_count

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error cleaning up old analyses: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error cleaning up old analyses: {str(e)}"
            )

    async def get_analysis_statistics(self) -> dict:
        """Get statistics about analysis jobs"""
        try:
            stats = {}
            
            # Count by status
            for status in AnalysisStatus:
                count = self.db.query(TrackAnalysis).filter(
                    TrackAnalysis.status == str(status)
                ).count()
                stats[status.name.lower()] = count
            
            # Total count
            stats['total'] = self.db.query(TrackAnalysis).count()
            
            return stats

        except Exception as e:
            logger.error(f"Error getting analysis statistics: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting analysis statistics: {str(e)}"
            )
