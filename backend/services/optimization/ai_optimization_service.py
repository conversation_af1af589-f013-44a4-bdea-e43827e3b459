"""
AI Optimization Service

This service analyzes AI usage data and generates optimization recommendations
to improve AI performance and user satisfaction.
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import threading
import statistics
from pathlib import Path
import math
import random
from collections import defaultdict

from backend.db.session import get_db
from backend.services.monitoring.ai_data_collector import AIDataCollector
from backend.services.monitoring.ai_analytics_service import AIAnalyticsService
from backend.services.monitoring.ai_settings_impact_analyzer import AISettingsImpactAnalyzer
from backend.services.monitoring.ai_db_service import AIDBService

logger = logging.getLogger(__name__)

# Thread lock for file access
file_lock = threading.Lock()

# Flag to determine whether to use database or file storage
USE_DATABASE = True

class AIOptimizationService:
    """
    Service for generating optimization recommendations based on AI usage data.
    """

    # Parameter ranges for different AI models
    PARAMETER_RANGES = {
        "temperature": {
            "min": 0.0,
            "max": 1.0,
            "step": 0.1,
            "default": 0.7,
            "description": "Controls randomness: Lowering results in more deterministic outputs, higher produces more random outputs"
        },
        "top_p": {
            "min": 0.0,
            "max": 1.0,
            "step": 0.05,
            "default": 0.9,
            "description": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered"
        },
        "frequency_penalty": {
            "min": 0.0,
            "max": 2.0,
            "step": 0.1,
            "default": 0.0,
            "description": "How much to penalize new tokens based on their existing frequency in the text so far"
        },
        "presence_penalty": {
            "min": 0.0,
            "max": 2.0,
            "step": 0.1,
            "default": 0.0,
            "description": "How much to penalize new tokens based on whether they appear in the text so far"
        },
        "max_tokens": {
            "min": 50,
            "max": 4000,
            "step": 50,
            "default": 1000,
            "description": "The maximum number of tokens to generate in the completion"
        }
    }

    @staticmethod
    def get_optimization_recommendations(
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get optimization recommendations for AI settings.

        Args:
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by
            limit: Maximum number of recommendations to return

        Returns:
            List of optimization recommendations
        """
        try:
            # Get all features if not specified
            features = [feature_id] if feature_id else AIOptimizationService._get_active_features(time_range_hours)

            # Generate recommendations for each feature
            all_recommendations = []

            for feature in features:
                # Get recommendations for this feature
                feature_recommendations = AIOptimizationService._generate_feature_recommendations(
                    feature_id=feature,
                    time_range_hours=time_range_hours
                )

                all_recommendations.extend(feature_recommendations)

            # Sort recommendations by impact score (descending)
            all_recommendations.sort(key=lambda x: x.get("impact_score", 0), reverse=True)

            # Limit the number of recommendations
            return all_recommendations[:limit]
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {str(e)}")
            return []

    @staticmethod
    def apply_recommendation(
        recommendation_id: str,
        feature_id: str,
        parameter_name: str,
        new_value: str,
        applied_by: str = "user"
    ) -> Dict[str, Any]:
        """
        Apply an optimization recommendation.

        Args:
            recommendation_id: ID of the recommendation
            feature_id: Feature ID to optimize
            parameter_name: Parameter name to change
            new_value: New parameter value
            applied_by: Who applied the recommendation

        Returns:
            Result of applying the recommendation
        """
        try:
            # Get current parameter value
            current_value = AIOptimizationService._get_current_parameter_value(
                feature_id=feature_id,
                parameter_name=parameter_name
            )

            if current_value is None:
                return {
                    "success": False,
                    "message": f"Could not find current value for parameter {parameter_name} in feature {feature_id}"
                }

            # Update parameter value
            success = AIOptimizationService._update_parameter_value(
                feature_id=feature_id,
                parameter_name=parameter_name,
                new_value=new_value
            )

            if not success:
                return {
                    "success": False,
                    "message": f"Failed to update parameter {parameter_name} in feature {feature_id}"
                }

            # Log the optimization
            if USE_DATABASE:
                with get_db() as db:
                    AIDBService.log_optimization(
                        db=db,
                        feature_id=feature_id,
                        parameter_name=parameter_name,
                        old_value=str(current_value),
                        new_value=str(new_value),
                        reason=f"Applied recommendation {recommendation_id}",
                        impact_score=0.0,  # This would be the expected impact
                        applied_by=applied_by
                    )

            return {
                "success": True,
                "message": f"Successfully updated parameter {parameter_name} in feature {feature_id} from {current_value} to {new_value}",
                "feature_id": feature_id,
                "parameter_name": parameter_name,
                "old_value": current_value,
                "new_value": new_value
            }
        except Exception as e:
            logger.error(f"Error applying recommendation: {str(e)}")
            return {
                "success": False,
                "message": f"Error applying recommendation: {str(e)}"
            }

    @staticmethod
    def get_optimization_history(
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get optimization history.

        Args:
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by
            limit: Maximum number of history entries to return

        Returns:
            List of optimization history entries
        """
        try:
            if USE_DATABASE:
                with get_db() as db:
                    # Query optimization history from database
                    query = db.query(AIDBService.AIOptimizationHistory)

                    if feature_id:
                        query = query.filter(AIDBService.AIOptimizationHistory.feature_id == feature_id)

                    if time_range_hours:
                        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
                        query = query.filter(AIDBService.AIOptimizationHistory.timestamp >= cutoff_time)

                    query = query.order_by(AIDBService.AIOptimizationHistory.timestamp.desc())
                    query = query.limit(limit)

                    history = query.all()

                    return [entry.to_dict() for entry in history]
            else:
                # For now, return empty list for file storage
                # In a real implementation, you would read from a history file
                return []
        except Exception as e:
            logger.error(f"Error getting optimization history: {str(e)}")
            return []

    @staticmethod
    def _get_active_features(time_range_hours: Optional[int] = None) -> List[str]:
        """
        Get a list of active features.

        Args:
            time_range_hours: Optional time range in hours to filter by

        Returns:
            List of feature IDs
        """
        try:
            # Get performance metrics to find active features
            metrics = AIAnalyticsService.get_performance_metrics(time_range_hours=time_range_hours)

            if not metrics or "requests_per_feature" not in metrics:
                return []

            # Get features with at least one request
            features = [
                feature_id for feature_id, count in metrics["requests_per_feature"].items()
                if count > 0
            ]

            return features
        except Exception as e:
            logger.error(f"Error getting active features: {str(e)}")
            return []

    @staticmethod
    def _generate_feature_recommendations(
        feature_id: str,
        time_range_hours: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate optimization recommendations for a specific feature.

        Args:
            feature_id: Feature ID to generate recommendations for
            time_range_hours: Optional time range in hours to filter by

        Returns:
            List of optimization recommendations
        """
        try:
            recommendations = []

            # Get parameters to analyze
            parameters = AIOptimizationService._get_feature_parameters(feature_id)

            for param_name in parameters:
                # Skip parameters not in our known ranges
                if param_name not in AIOptimizationService.PARAMETER_RANGES:
                    continue

                # Analyze parameter impact on performance
                performance_impact = AISettingsImpactAnalyzer.analyze_parameter_performance_impact(
                    feature_id=feature_id,
                    param_name=param_name,
                    time_range_hours=time_range_hours
                )

                # Analyze parameter impact on satisfaction
                satisfaction_impact = AISettingsImpactAnalyzer.analyze_parameter_satisfaction_impact(
                    feature_id=feature_id,
                    param_name=param_name,
                    time_range_hours=time_range_hours
                )

                # Skip parameters with low impact
                if (
                    performance_impact.get("impact_score", 0) < 10 and
                    satisfaction_impact.get("impact_score", 0) < 10
                ):
                    continue

                # Generate recommendation
                recommendation = AIOptimizationService._generate_parameter_recommendation(
                    feature_id=feature_id,
                    param_name=param_name,
                    performance_impact=performance_impact,
                    satisfaction_impact=satisfaction_impact
                )

                if recommendation:
                    recommendations.append(recommendation)

            return recommendations
        except Exception as e:
            logger.error(f"Error generating feature recommendations: {str(e)}")
            return []

    @staticmethod
    def _get_feature_parameters(feature_id: str) -> List[str]:
        """
        Get parameters used by a feature.

        Args:
            feature_id: Feature ID to get parameters for

        Returns:
            List of parameter names
        """
        try:
            # Get settings usage stats
            stats = AIAnalyticsService.get_settings_usage_stats()

            if not stats or "settings_usage" not in stats or "parameters" not in stats["settings_usage"]:
                return []

            # Get parameters with usage
            parameters = list(stats["settings_usage"]["parameters"].keys())

            return parameters
        except Exception as e:
            logger.error(f"Error getting feature parameters: {str(e)}")
            return []

    @staticmethod
    def _generate_parameter_recommendation(
        feature_id: str,
        param_name: str,
        performance_impact: Dict[str, Any],
        satisfaction_impact: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Generate a recommendation for a parameter.

        Args:
            feature_id: Feature ID
            param_name: Parameter name
            performance_impact: Performance impact analysis
            satisfaction_impact: Satisfaction impact analysis

        Returns:
            Recommendation or None if no recommendation can be made
        """
        try:
            # Get current parameter value
            current_value = AIOptimizationService._get_current_parameter_value(
                feature_id=feature_id,
                param_name=param_name
            )

            if current_value is None:
                return None

            # Get parameter range
            param_range = AIOptimizationService.PARAMETER_RANGES.get(param_name)

            if not param_range:
                return None

            # Determine optimal value based on impact analysis
            optimal_value = AIOptimizationService._determine_optimal_value(
                param_name=param_name,
                current_value=current_value,
                param_range=param_range,
                performance_impact=performance_impact,
                satisfaction_impact=satisfaction_impact
            )

            if optimal_value is None or optimal_value == current_value:
                return None

            # Calculate impact score
            impact_score = AIOptimizationService._calculate_impact_score(
                performance_impact=performance_impact,
                satisfaction_impact=satisfaction_impact,
                current_value=current_value,
                optimal_value=optimal_value
            )

            # Generate recommendation
            recommendation = {
                "id": f"rec_{feature_id}_{param_name}_{int(datetime.now().timestamp())}",
                "feature_id": feature_id,
                "parameter_name": param_name,
                "current_value": current_value,
                "recommended_value": optimal_value,
                "impact_score": round(impact_score, 2),
                "description": AIOptimizationService._generate_recommendation_description(
                    feature_id=feature_id,
                    param_name=param_name,
                    current_value=current_value,
                    optimal_value=optimal_value,
                    param_range=param_range,
                    impact_score=impact_score
                ),
                "reason": AIOptimizationService._generate_recommendation_reason(
                    performance_impact=performance_impact,
                    satisfaction_impact=satisfaction_impact,
                    param_name=param_name,
                    current_value=current_value,
                    optimal_value=optimal_value
                ),
                "timestamp": datetime.now().isoformat()
            }

            return recommendation
        except Exception as e:
            logger.error(f"Error generating parameter recommendation: {str(e)}")
            return None

    @staticmethod
    def _get_current_parameter_value(
        feature_id: str,
        parameter_name: str
    ) -> Optional[Union[float, int, str]]:
        """
        Get the current value of a parameter for a feature.

        Args:
            feature_id: Feature ID
            parameter_name: Parameter name

        Returns:
            Current parameter value or None if not found
        """
        try:
            # In a real implementation, this would get the current value from a settings store
            # For now, we'll use a default value from our parameter ranges
            param_range = AIOptimizationService.PARAMETER_RANGES.get(parameter_name)

            if not param_range:
                return None

            # Get the default value
            default_value = param_range.get("default")

            # In a real implementation, you would check if the feature has a custom value
            # For now, we'll just return the default
            return default_value
        except Exception as e:
            logger.error(f"Error getting current parameter value: {str(e)}")
            return None

    @staticmethod
    def _update_parameter_value(
        feature_id: str,
        parameter_name: str,
        new_value: str
    ) -> bool:
        """
        Update the value of a parameter for a feature.

        Args:
            feature_id: Feature ID
            parameter_name: Parameter name
            new_value: New parameter value

        Returns:
            True if successful, False otherwise
        """
        try:
            # In a real implementation, this would update the value in a settings store
            # For now, we'll just return True to simulate success
            return True
        except Exception as e:
            logger.error(f"Error updating parameter value: {str(e)}")
            return False

    @staticmethod
    def _determine_optimal_value(
        param_name: str,
        current_value: Union[float, int, str],
        param_range: Dict[str, Any],
        performance_impact: Dict[str, Any],
        satisfaction_impact: Dict[str, Any]
    ) -> Optional[Union[float, int, str]]:
        """
        Determine the optimal value for a parameter based on impact analysis.

        Args:
            param_name: Parameter name
            current_value: Current parameter value
            param_range: Parameter range information
            performance_impact: Performance impact analysis
            satisfaction_impact: Satisfaction impact analysis

        Returns:
            Optimal parameter value or None if no optimization is needed
        """
        try:
            # Convert current value to float for numeric comparison
            try:
                current_value_float = float(current_value)
            except (ValueError, TypeError):
                return None

            # Get correlation coefficients
            performance_correlation = performance_impact.get("correlation", 0)
            satisfaction_correlation = satisfaction_impact.get("correlation", 0)

            # Calculate weighted correlation (satisfaction is more important)
            weighted_correlation = satisfaction_correlation * 0.7 + performance_correlation * 0.3

            # If correlation is very weak, no optimization is needed
            if abs(weighted_correlation) < 0.1:
                return None

            # Determine direction of optimization
            if weighted_correlation > 0:
                # Positive correlation: higher value is better
                optimal_value = min(
                    current_value_float + param_range.get("step", 0.1),
                    param_range.get("max", 1.0)
                )
            else:
                # Negative correlation: lower value is better
                optimal_value = max(
                    current_value_float - param_range.get("step", 0.1),
                    param_range.get("min", 0.0)
                )

            # If optimal value is the same as current value, no optimization is needed
            if abs(optimal_value - current_value_float) < 0.001:
                return None

            # Format the value based on parameter type
            if param_name == "max_tokens":
                return int(optimal_value)
            else:
                return round(optimal_value, 2)
        except Exception as e:
            logger.error(f"Error determining optimal value: {str(e)}")
            return None

    @staticmethod
    def _calculate_impact_score(
        performance_impact: Dict[str, Any],
        satisfaction_impact: Dict[str, Any],
        current_value: Union[float, int, str],
        optimal_value: Union[float, int, str]
    ) -> float:
        """
        Calculate the impact score of a recommendation.

        Args:
            performance_impact: Performance impact analysis
            satisfaction_impact: Satisfaction impact analysis
            current_value: Current parameter value
            optimal_value: Optimal parameter value

        Returns:
            Impact score (0-100)
        """
        try:
            # Convert values to float for numeric comparison
            try:
                current_value_float = float(current_value)
                optimal_value_float = float(optimal_value)
            except (ValueError, TypeError):
                return 0.0

            # Calculate relative change
            value_range = abs(optimal_value_float - current_value_float)

            # Get impact scores
            performance_score = performance_impact.get("impact_score", 0)
            satisfaction_score = satisfaction_impact.get("impact_score", 0)

            # Calculate weighted impact score (satisfaction is more important)
            weighted_score = satisfaction_score * 0.7 + performance_score * 0.3

            # Scale by relative change
            scaled_score = weighted_score * min(1.0, value_range * 10)

            return min(100.0, scaled_score)
        except Exception as e:
            logger.error(f"Error calculating impact score: {str(e)}")
            return 0.0

    @staticmethod
    def _generate_recommendation_description(
        feature_id: str,
        param_name: str,
        current_value: Union[float, int, str],
        optimal_value: Union[float, int, str],
        param_range: Dict[str, Any],
        impact_score: float
    ) -> str:
        """
        Generate a description for a recommendation.

        Args:
            feature_id: Feature ID
            param_name: Parameter name
            current_value: Current parameter value
            optimal_value: Optimal parameter value
            param_range: Parameter range information
            impact_score: Impact score

        Returns:
            Recommendation description
        """
        try:
            # Get parameter description
            param_description = param_range.get("description", "")

            # Format feature name
            feature_name = feature_id.replace("_", " ").title()

            # Format parameter name
            param_display_name = param_name.replace("_", " ").title()

            # Determine direction
            if float(optimal_value) > float(current_value):
                direction = "Increase"
            else:
                direction = "Decrease"

            # Generate description
            description = f"{direction} {param_display_name} for {feature_name} from {current_value} to {optimal_value}"

            return description
        except Exception as e:
            logger.error(f"Error generating recommendation description: {str(e)}")
            return f"Optimize {param_name} for {feature_id}"

    @staticmethod
    def _generate_recommendation_reason(
        performance_impact: Dict[str, Any],
        satisfaction_impact: Dict[str, Any],
        param_name: str,
        current_value: Union[float, int, str],
        optimal_value: Union[float, int, str]
    ) -> str:
        """
        Generate a reason for a recommendation.

        Args:
            performance_impact: Performance impact analysis
            satisfaction_impact: Satisfaction impact analysis
            param_name: Parameter name
            current_value: Current parameter value
            optimal_value: Optimal parameter value

        Returns:
            Recommendation reason
        """
        try:
            # Format parameter name
            param_display_name = param_name.replace("_", " ").title()

            # Determine direction
            if float(optimal_value) > float(current_value):
                direction = "higher"
            else:
                direction = "lower"

            # Get impact scores
            performance_score = performance_impact.get("impact_score", 0)
            satisfaction_score = satisfaction_impact.get("impact_score", 0)

            # Generate reason based on impact scores
            reasons = []

            if satisfaction_score > 10:
                reasons.append(f"Analysis shows that a {direction} {param_display_name} value is associated with higher user satisfaction ratings")

            if performance_score > 10:
                if direction == "lower":
                    reasons.append(f"A {direction} {param_display_name} value may improve response times and success rates")
                else:
                    reasons.append(f"A {direction} {param_display_name} value may improve success rates despite slightly longer response times")

            # Add correlation information
            performance_correlation = performance_impact.get("correlation", 0)
            satisfaction_correlation = satisfaction_impact.get("correlation", 0)

            if abs(satisfaction_correlation) > 0.3:
                corr_strength = "strong" if abs(satisfaction_correlation) > 0.6 else "moderate"
                corr_direction = "positive" if satisfaction_correlation > 0 else "negative"
                reasons.append(f"There is a {corr_strength} {corr_direction} correlation between {param_display_name} and user satisfaction")

            # Combine reasons
            if not reasons:
                return f"Optimizing {param_display_name} may improve overall performance and user satisfaction"

            return ". ".join(reasons) + "."
        except Exception as e:
            logger.error(f"Error generating recommendation reason: {str(e)}")
            return f"Optimizing {param_name} may improve performance and user satisfaction"
