"""
Quality control for beat grid extraction.
Validation, correction, and refinement of beat detection results.
"""

import logging
import numpy as np
import librosa
import scipy.signal
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)

class QualityController:
    """Handles quality control for beat grid extraction"""
    
    def __init__(self):
        pass
    
    def validate_beat_grid_quality(self, beat_times: np.ndarray, confidence: float, tempo: float) -> Dict[str, Any]:
        """
        Validate beat grid quality and determine if it should be accepted or rejected.
        
        Args:
            beat_times: Array of beat times
            confidence: Confidence score
            tempo: Detected tempo
            
        Returns:
            Validation results with quality assessment
        """
        try:
            validation_result = {
                "valid": True,
                "quality_score": 0.0,
                "issues": [],
                "warnings": [],
                "recommendations": []
            }
            
            # Check basic requirements
            if len(beat_times) == 0:
                validation_result["valid"] = False
                validation_result["issues"].append("No beats detected")
                return validation_result
            
            # Calculate quality metrics
            quality_metrics = self._calculate_quality_metrics(beat_times, confidence, tempo)
            validation_result["quality_score"] = quality_metrics["overall_score"]
            
            # Tempo validation
            if tempo < 60:
                validation_result["issues"].append(f"Tempo too slow: {tempo:.1f} BPM")
                validation_result["valid"] = False
            elif tempo > 200:
                validation_result["issues"].append(f"Tempo too fast: {tempo:.1f} BPM")
                validation_result["valid"] = False
            elif tempo < 80 or tempo > 180:
                validation_result["warnings"].append(f"Unusual tempo: {tempo:.1f} BPM")
            
            # Beat count validation
            if len(beat_times) < 4:
                validation_result["issues"].append("Too few beats detected")
                validation_result["valid"] = False
            elif len(beat_times) < 8:
                validation_result["warnings"].append("Low beat count may affect accuracy")
            
            # Confidence validation
            if confidence < 0.3:
                validation_result["issues"].append(f"Low confidence: {confidence:.2f}")
                validation_result["valid"] = False
            elif confidence < 0.5:
                validation_result["warnings"].append(f"Moderate confidence: {confidence:.2f}")
            
            # Beat consistency validation
            consistency_score = quality_metrics["consistency_score"]
            if consistency_score < 0.5:
                validation_result["issues"].append("Inconsistent beat intervals")
                validation_result["valid"] = False
            elif consistency_score < 0.7:
                validation_result["warnings"].append("Some beat interval inconsistency")
            
            # Add recommendations
            if validation_result["valid"]:
                if quality_metrics["overall_score"] < 0.7:
                    validation_result["recommendations"].append("Consider enhanced analysis for better accuracy")
                if len(beat_times) > 100:
                    validation_result["recommendations"].append("Good beat count for reliable analysis")
            else:
                validation_result["recommendations"].append("Try enhanced analysis or manual correction")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating beat grid quality: {e}")
            return {
                "valid": False,
                "quality_score": 0.0,
                "issues": [f"Validation error: {str(e)}"],
                "warnings": [],
                "recommendations": []
            }
    
    def _calculate_quality_metrics(self, beat_times: np.ndarray, confidence: float, tempo: float) -> Dict[str, Any]:
        """
        Calculate detailed quality metrics for beat grid.
        
        Args:
            beat_times: Array of beat times
            confidence: Confidence score
            tempo: Detected tempo
            
        Returns:
            Dictionary of quality metrics
        """
        try:
            metrics = {}
            
            # 1. Consistency score
            if len(beat_times) > 1:
                intervals = np.diff(beat_times)
                mean_interval = np.mean(intervals)
                std_interval = np.std(intervals)
                
                if mean_interval > 0:
                    consistency_score = 1.0 - min(1.0, std_interval / mean_interval)
                else:
                    consistency_score = 0.0
            else:
                consistency_score = 0.0
            
            metrics["consistency_score"] = consistency_score
            
            # 2. Tempo plausibility score
            if 80 <= tempo <= 160:
                tempo_score = 1.0
            elif 60 <= tempo <= 200:
                tempo_score = 0.7
            else:
                tempo_score = 0.3
            
            metrics["tempo_score"] = tempo_score
            
            # 3. Beat density score
            if len(beat_times) > 1:
                duration = beat_times[-1] - beat_times[0]
                if duration > 0:
                    beat_density = len(beat_times) / duration
                    # Expect roughly 1-4 beats per second
                    if 1.0 <= beat_density <= 4.0:
                        density_score = 1.0
                    elif 0.5 <= beat_density <= 6.0:
                        density_score = 0.7
                    else:
                        density_score = 0.3
                else:
                    density_score = 0.0
            else:
                density_score = 0.0
            
            metrics["density_score"] = density_score
            
            # 4. Overall score (weighted combination)
            overall_score = (
                0.4 * confidence +
                0.3 * consistency_score +
                0.2 * tempo_score +
                0.1 * density_score
            )
            
            metrics["overall_score"] = max(0.0, min(1.0, overall_score))
            
            return metrics
            
        except Exception as e:
            logger.warning(f"Error calculating quality metrics: {e}")
            return {
                "consistency_score": 0.0,
                "tempo_score": 0.0,
                "density_score": 0.0,
                "overall_score": 0.0
            }
    
    def detect_and_correct_tempo_doubling(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Detect and correct tempo doubling/halving issues.
        
        Args:
            result: Beat detection result
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Corrected result
        """
        try:
            tempo = result.get("tempo", 120)
            beat_times = np.array(result.get("beat_times", []))
            
            if len(beat_times) < 4:
                return result
            
            # Test different tempo hypotheses
            hypotheses = [
                {"tempo": tempo, "multiplier": 1.0, "description": "original"},
                {"tempo": tempo * 2, "multiplier": 2.0, "description": "doubled"},
                {"tempo": tempo / 2, "multiplier": 0.5, "description": "halved"}
            ]
            
            # Evaluate each hypothesis
            best_hypothesis = None
            best_score = -1
            
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onset_times = librosa.frames_to_time(
                librosa.onset.onset_detect(onset_envelope=onset_env, sr=sr),
                sr=sr
            )
            
            for hypothesis in hypotheses:
                score = self._evaluate_tempo_hypothesis(hypothesis, onset_env, onset_times, y, sr)
                
                if score > best_score:
                    best_score = score
                    best_hypothesis = hypothesis
            
            # Apply correction if needed
            if best_hypothesis and best_hypothesis["multiplier"] != 1.0:
                logger.info(f"Correcting tempo from {tempo:.1f} to {best_hypothesis['tempo']:.1f} BPM "
                           f"({best_hypothesis['description']})")
                
                result["tempo"] = best_hypothesis["tempo"]
                result["tempo_corrected"] = True
                result["original_tempo"] = tempo
                result["correction_type"] = best_hypothesis["description"]
                
                # Regenerate beats with corrected tempo
                if best_hypothesis["multiplier"] == 2.0:
                    # Double tempo: insert beats between existing ones
                    new_beats = []
                    for i in range(len(beat_times) - 1):
                        new_beats.append(beat_times[i])
                        new_beats.append((beat_times[i] + beat_times[i + 1]) / 2)
                    new_beats.append(beat_times[-1])
                    result["beat_times"] = new_beats
                    
                elif best_hypothesis["multiplier"] == 0.5:
                    # Half tempo: keep every other beat
                    result["beat_times"] = beat_times[::2].tolist()
            
            return result
            
        except Exception as e:
            logger.warning(f"Error in tempo doubling correction: {e}")
            return result
    
    def _evaluate_tempo_hypothesis(self, hypothesis: Dict[str, Any], onset_env: np.ndarray, 
                                  onset_times: np.ndarray, y: np.ndarray, sr: int) -> float:
        """
        Evaluate how well a tempo hypothesis matches the audio.
        
        Args:
            hypothesis: Tempo hypothesis to evaluate
            onset_env: Onset strength envelope
            onset_times: Onset times
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Score for the hypothesis
        """
        try:
            tempo = hypothesis["tempo"]
            
            # Generate expected beat times based on this tempo
            beat_interval = 60.0 / tempo
            duration = len(y) / sr
            expected_beats = np.arange(0, duration, beat_interval)
            
            if len(expected_beats) == 0 or len(onset_times) == 0:
                return 0.0
            
            # Calculate alignment score with onsets
            alignment_scores = []
            for beat_time in expected_beats:
                distances = np.abs(onset_times - beat_time)
                min_distance = np.min(distances) if len(distances) > 0 else 1.0
                alignment_score = np.exp(-min_distance * 10)  # Exponential decay
                alignment_scores.append(alignment_score)
            
            # Average alignment score
            avg_alignment = np.mean(alignment_scores) if alignment_scores else 0.0
            
            # Tempo plausibility score
            if 80 <= tempo <= 160:
                tempo_plausibility = 1.0
            elif 60 <= tempo <= 200:
                tempo_plausibility = 0.7
            else:
                tempo_plausibility = 0.3
            
            # Combined score
            score = 0.7 * avg_alignment + 0.3 * tempo_plausibility
            
            return score
            
        except Exception as e:
            logger.warning(f"Error evaluating tempo hypothesis: {e}")
            return 0.0
    
    def correct_beat_phase_alignment(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Correct beat phase alignment to fix systematic offset issues.
        
        Args:
            result: Beat detection result
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Phase-corrected result
        """
        try:
            beat_times = np.array(result.get("beat_times", []))
            
            if len(beat_times) < 4:
                return result
            
            # Calculate onset strength
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onset_frames = librosa.onset.onset_detect(onset_envelope=onset_env, sr=sr)
            onset_times = librosa.frames_to_time(onset_frames, sr=sr)
            
            if len(onset_times) == 0:
                return result
            
            # Find systematic offset between beats and onsets
            offsets = []
            for beat_time in beat_times:
                distances = np.abs(onset_times - beat_time)
                closest_onset_idx = np.argmin(distances)
                
                if distances[closest_onset_idx] < 0.2:  # Within 200ms
                    offset = onset_times[closest_onset_idx] - beat_time
                    offsets.append(offset)
            
            if len(offsets) < len(beat_times) * 0.3:  # Need at least 30% alignment
                return result
            
            # Calculate median offset
            median_offset = np.median(offsets)
            
            # Only apply correction if offset is significant
            if abs(median_offset) > 0.02:  # 20ms threshold
                logger.info(f"Correcting beat phase alignment by {median_offset:.3f}s")
                
                corrected_beats = beat_times + median_offset
                result["beat_times"] = corrected_beats.tolist()
                result["phase_corrected"] = True
                result["phase_offset"] = float(median_offset)
            
            return result
            
        except Exception as e:
            logger.warning(f"Error in phase alignment correction: {e}")
            return result
    
    def refine_beat_alignment_with_onsets(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Final refinement step that aligns each beat to the nearest strong onset.
        
        Args:
            result: Beat detection result
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Refined result
        """
        try:
            beat_times = np.array(result.get("beat_times", []))
            
            if len(beat_times) == 0:
                return result
            
            # Get onset information
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onset_frames = librosa.onset.onset_detect(
                onset_envelope=onset_env, 
                sr=sr,
                pre_max=3,
                post_max=3,
                pre_avg=3,
                post_avg=5,
                delta=0.5,
                wait=10
            )
            onset_times = librosa.frames_to_time(onset_frames, sr=sr)
            
            if len(onset_times) == 0:
                return result
            
            # Refine each beat position
            refined_beats = []
            refinement_tolerance = 0.1  # 100ms tolerance
            
            for beat_time in beat_times:
                # Find onsets within tolerance
                distances = np.abs(onset_times - beat_time)
                within_tolerance = distances <= refinement_tolerance
                
                if np.any(within_tolerance):
                    # Find the strongest onset within tolerance
                    candidate_onsets = onset_times[within_tolerance]
                    candidate_frames = onset_frames[within_tolerance]
                    
                    # Get onset strengths
                    onset_strengths = onset_env[candidate_frames]
                    
                    # Choose the strongest onset
                    strongest_idx = np.argmax(onset_strengths)
                    refined_beat = candidate_onsets[strongest_idx]
                    refined_beats.append(refined_beat)
                else:
                    # No nearby onset, keep original beat
                    refined_beats.append(beat_time)
            
            # Update result
            result["beat_times"] = refined_beats
            result["onset_refined"] = True
            
            # Calculate refinement statistics
            original_beats = beat_times
            refinement_distances = np.abs(np.array(refined_beats) - original_beats)
            avg_refinement = np.mean(refinement_distances)
            max_refinement = np.max(refinement_distances)
            
            result["refinement_stats"] = {
                "average_adjustment": float(avg_refinement),
                "max_adjustment": float(max_refinement),
                "beats_adjusted": int(np.sum(refinement_distances > 0.01))  # Count beats moved >10ms
            }
            
            logger.info(f"Beat refinement completed - Average adjustment: {avg_refinement:.3f}s, "
                       f"Beats adjusted: {result['refinement_stats']['beats_adjusted']}")
            
            return result
            
        except Exception as e:
            logger.warning(f"Error in beat refinement: {e}")
            return result
    
    def fix_missing_first_beat(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Detect and add missing first beat at the beginning of the track.
        
        Args:
            result: Beat detection result
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Result with first beat correction if needed
        """
        try:
            beat_times = np.array(result.get("beat_times", []))
            
            if len(beat_times) < 2:
                return result
            
            # Calculate expected beat interval
            intervals = np.diff(beat_times)
            median_interval = np.median(intervals)
            
            # Check if first beat is missing
            first_beat = beat_times[0]
            expected_first_beat = first_beat - median_interval
            
            # Only add if the expected first beat would be after audio start
            # and there's evidence of a strong onset there
            if expected_first_beat > 0.1:  # At least 100ms into the track
                # Check for onset near expected position
                onset_env = librosa.onset.onset_strength(y=y, sr=sr)
                expected_frame = librosa.time_to_frames(expected_first_beat, sr=sr)
                
                # Look in a small window around expected position
                window_size = int(0.05 * sr / 512)  # 50ms window
                start_frame = max(0, expected_frame - window_size)
                end_frame = min(len(onset_env), expected_frame + window_size)
                
                if start_frame < end_frame:
                    window_strength = onset_env[start_frame:end_frame]
                    max_strength = np.max(window_strength)
                    overall_mean = np.mean(onset_env)
                    
                    # Add first beat if there's a strong onset
                    if max_strength > 1.5 * overall_mean:
                        max_frame = start_frame + np.argmax(window_strength)
                        first_beat_time = librosa.frames_to_time(max_frame, sr=sr)
                        
                        # Insert at beginning
                        new_beats = np.concatenate([[first_beat_time], beat_times])
                        result["beat_times"] = new_beats.tolist()
                        result["first_beat_added"] = True
                        result["added_beat_time"] = float(first_beat_time)
                        
                        logger.info(f"Added missing first beat at {first_beat_time:.3f}s")
            
            return result
            
        except Exception as e:
            logger.warning(f"Error fixing missing first beat: {e}")
            return result
