"""
Audio segment analysis for beat grid extraction.
Analyzes audio in segments to detect tempo changes and structural elements.
"""

import logging
import numpy as np
import librosa
import scipy.signal
from typing import Dict, Any, List, Tuple

logger = logging.getLogger(__name__)

class SegmentAnalyzer:
    """Analyzes audio segments for tempo changes and structure"""
    
    def __init__(self):
        pass
    
    def analyze_segments(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Analyze audio in segments to detect tempo changes.
        
        Args:
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Segment analysis results
        """
        try:
            logger.info("Starting segment analysis")
            
            # Detect structural segments
            segments = self._detect_structural_segments(y, sr)
            
            # Analyze tempo in each segment
            segment_tempos = self._analyze_segment_tempos(y, sr, segments)
            
            # Detect tempo changes
            tempo_changes = self._detect_tempo_changes(segment_tempos)
            
            # Create segment boundaries
            segment_boundaries = self._create_segment_boundaries(segments, segment_tempos)
            
            result = {
                "segments": segments,
                "segment_tempos": segment_tempos,
                "tempo_changes": tempo_changes,
                "segment_boundaries": segment_boundaries,
                "has_tempo_changes": len(tempo_changes) > 0
            }
            
            logger.info(f"Segment analysis completed - {len(segments)} segments, "
                       f"{len(tempo_changes)} tempo changes")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in segment analysis: {e}")
            return {
                "segments": [],
                "segment_tempos": [],
                "tempo_changes": [],
                "segment_boundaries": [],
                "has_tempo_changes": False,
                "error": str(e)
            }
    
    def _detect_structural_segments(self, y: np.ndarray, sr: int) -> List[Dict[str, Any]]:
        """
        Detect structural segments in the audio using spectral features.
        
        Args:
            y: Audio time series
            sr: Sample rate
            
        Returns:
            List of segment dictionaries
        """
        try:
            # Use chroma features for structural analysis
            chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=512)
            
            # Compute recurrence matrix
            R = librosa.segment.recurrence_matrix(
                chroma, 
                width=43,  # ~2 seconds at 22050 Hz
                mode='affinity',
                sym=True
            )
            
            # Detect segment boundaries
            boundaries = librosa.segment.agglomerative(R, k=None)
            boundary_times = librosa.frames_to_time(boundaries, sr=sr, hop_length=512)
            
            # Create segment objects
            segments = []
            for i in range(len(boundary_times) - 1):
                start_time = boundary_times[i]
                end_time = boundary_times[i + 1]
                
                segments.append({
                    "start": float(start_time),
                    "end": float(end_time),
                    "duration": float(end_time - start_time),
                    "index": i
                })
            
            # Add final segment if needed
            if len(boundary_times) > 0:
                total_duration = len(y) / sr
                if boundary_times[-1] < total_duration - 1.0:  # At least 1 second remaining
                    segments.append({
                        "start": float(boundary_times[-1]),
                        "end": float(total_duration),
                        "duration": float(total_duration - boundary_times[-1]),
                        "index": len(segments)
                    })
            
            return segments
            
        except Exception as e:
            logger.warning(f"Error detecting structural segments: {e}")
            # Fallback: create simple time-based segments
            return self._create_time_based_segments(y, sr)
    
    def _create_time_based_segments(self, y: np.ndarray, sr: int, segment_length: float = 30.0) -> List[Dict[str, Any]]:
        """
        Create simple time-based segments as fallback.
        
        Args:
            y: Audio time series
            sr: Sample rate
            segment_length: Length of each segment in seconds
            
        Returns:
            List of time-based segments
        """
        total_duration = len(y) / sr
        segments = []
        
        start_time = 0.0
        index = 0
        
        while start_time < total_duration:
            end_time = min(start_time + segment_length, total_duration)
            
            segments.append({
                "start": start_time,
                "end": end_time,
                "duration": end_time - start_time,
                "index": index
            })
            
            start_time = end_time
            index += 1
        
        return segments
    
    def _analyze_segment_tempos(self, y: np.ndarray, sr: int, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze tempo in each segment.
        
        Args:
            y: Audio time series
            sr: Sample rate
            segments: List of segments to analyze
            
        Returns:
            List of segment tempo analysis results
        """
        segment_tempos = []
        
        for segment in segments:
            try:
                # Extract segment audio
                start_sample = int(segment["start"] * sr)
                end_sample = int(segment["end"] * sr)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) < sr * 2:  # Skip segments shorter than 2 seconds
                    continue
                
                # Analyze tempo in this segment
                tempo, beats = librosa.beat.beat_track(y=segment_audio, sr=sr)
                
                # Calculate confidence
                onset_env = librosa.onset.onset_strength(y=segment_audio, sr=sr)
                confidence = self._calculate_segment_confidence(beats, onset_env)
                
                segment_tempo = {
                    "segment_index": segment["index"],
                    "start": segment["start"],
                    "end": segment["end"],
                    "tempo": float(tempo),
                    "confidence": float(confidence),
                    "beat_count": len(beats)
                }
                
                segment_tempos.append(segment_tempo)
                
            except Exception as e:
                logger.warning(f"Error analyzing tempo in segment {segment['index']}: {e}")
                continue
        
        return segment_tempos
    
    def _calculate_segment_confidence(self, beats: np.ndarray, onset_env: np.ndarray) -> float:
        """
        Calculate confidence for segment tempo analysis.
        
        Args:
            beats: Beat positions in frames
            onset_env: Onset strength envelope
            
        Returns:
            Confidence score
        """
        try:
            if len(beats) == 0:
                return 0.0
            
            # Get onset strengths at beat positions
            beat_strengths = onset_env[beats]
            
            # Calculate confidence based on beat strength
            mean_beat_strength = np.mean(beat_strengths)
            mean_overall_strength = np.mean(onset_env)
            
            if mean_overall_strength == 0:
                return 0.0
            
            confidence = min(1.0, mean_beat_strength / mean_overall_strength)
            
            # Apply consistency bonus for regular beats
            if len(beats) > 1:
                intervals = np.diff(beats)
                consistency = 1.0 - (np.std(intervals) / (np.mean(intervals) + 1e-8))
                confidence *= (0.7 + 0.3 * max(0, consistency))
            
            return max(0.0, confidence)
            
        except Exception as e:
            logger.warning(f"Error calculating segment confidence: {e}")
            return 0.5
    
    def _detect_tempo_changes(self, segment_tempos: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Detect significant tempo changes between segments.
        
        Args:
            segment_tempos: List of segment tempo analyses
            
        Returns:
            List of detected tempo changes
        """
        tempo_changes = []
        
        if len(segment_tempos) < 2:
            return tempo_changes
        
        try:
            # Define threshold for significant tempo change
            tempo_threshold = 5.0  # BPM
            
            for i in range(1, len(segment_tempos)):
                prev_segment = segment_tempos[i - 1]
                curr_segment = segment_tempos[i]
                
                tempo_diff = abs(curr_segment["tempo"] - prev_segment["tempo"])
                
                if tempo_diff > tempo_threshold:
                    # Check if both segments have reasonable confidence
                    if (prev_segment["confidence"] > 0.3 and 
                        curr_segment["confidence"] > 0.3):
                        
                        tempo_change = {
                            "time": curr_segment["start"],
                            "from_tempo": prev_segment["tempo"],
                            "to_tempo": curr_segment["tempo"],
                            "tempo_diff": tempo_diff,
                            "from_segment": prev_segment["segment_index"],
                            "to_segment": curr_segment["segment_index"]
                        }
                        
                        tempo_changes.append(tempo_change)
            
            return tempo_changes
            
        except Exception as e:
            logger.warning(f"Error detecting tempo changes: {e}")
            return []
    
    def _create_segment_boundaries(self, segments: List[Dict[str, Any]], 
                                 segment_tempos: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create segment boundary markers for visualization.
        
        Args:
            segments: Original segments
            segment_tempos: Tempo analysis for segments
            
        Returns:
            List of segment boundaries
        """
        boundaries = []
        
        try:
            # Create tempo map for quick lookup
            tempo_map = {st["segment_index"]: st for st in segment_tempos}
            
            for segment in segments:
                tempo_info = tempo_map.get(segment["index"])
                
                boundary = {
                    "time": segment["start"],
                    "type": "segment_start",
                    "segment_index": segment["index"],
                    "duration": segment["duration"]
                }
                
                if tempo_info:
                    boundary.update({
                        "tempo": tempo_info["tempo"],
                        "confidence": tempo_info["confidence"]
                    })
                
                boundaries.append(boundary)
            
            # Add final boundary
            if segments:
                final_boundary = {
                    "time": segments[-1]["end"],
                    "type": "segment_end",
                    "segment_index": segments[-1]["index"]
                }
                boundaries.append(final_boundary)
            
            return boundaries
            
        except Exception as e:
            logger.warning(f"Error creating segment boundaries: {e}")
            return []
    
    def get_dominant_tempo(self, segment_tempos: List[Dict[str, Any]]) -> float:
        """
        Get the dominant tempo across all segments.
        
        Args:
            segment_tempos: List of segment tempo analyses
            
        Returns:
            Dominant tempo in BPM
        """
        if not segment_tempos:
            return 120.0
        
        try:
            # Weight tempos by confidence and segment duration
            weighted_tempos = []
            total_weight = 0
            
            for st in segment_tempos:
                weight = st["confidence"] * (st["end"] - st["start"])
                weighted_tempos.append(st["tempo"] * weight)
                total_weight += weight
            
            if total_weight > 0:
                dominant_tempo = sum(weighted_tempos) / total_weight
                return float(dominant_tempo)
            else:
                # Fallback to simple average
                tempos = [st["tempo"] for st in segment_tempos]
                return float(np.mean(tempos))
                
        except Exception as e:
            logger.warning(f"Error calculating dominant tempo: {e}")
            return 120.0
