"""
Enhanced beat grid analysis with multiple detection methods.
Advanced beat detection using ensemble methods and quality validation.
"""

import logging
import numpy as np
import librosa
import scipy.signal
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class EnhancedBeatAnalyzer:
    """Enhanced beat analysis with multiple methods and ensemble voting"""
    
    def __init__(self):
        pass
    
    def extract_enhanced_beat_grid(self, y: np.ndarray, sr: int, file_path: str) -> Dict[str, Any]:
        """
        Enhanced beat grid extraction with segment analysis, harmonic structure,
        and multiple onset detection methods.
        
        Args:
            y: Audio time series
            sr: Sample rate
            file_path: Path to audio file for logging
            
        Returns:
            Enhanced beat grid data
        """
        try:
            logger.info(f"Starting enhanced beat grid extraction for {file_path}")
            
            # Use multiple onset detection methods
            method_results = self._extract_with_multiple_methods(y, sr)
            
            # Use ensemble voting to get best result
            best_result = self._ensemble_vote_beat_detection(method_results)
            
            # Add enhanced features
            best_result["enhanced"] = True
            best_result["method"] = "enhanced_ensemble"
            
            logger.info(f"Enhanced extraction completed - Tempo: {best_result['tempo']:.2f} BPM, "
                       f"Confidence: {best_result['confidence']:.3f}")
            
            return best_result
            
        except Exception as e:
            logger.error(f"Error in enhanced beat extraction: {e}")
            # Fallback to basic method
            return self._basic_fallback(y, sr)
    
    def _extract_with_multiple_methods(self, y: np.ndarray, sr: int) -> List[Dict[str, Any]]:
        """
        Extract beat grid using multiple onset detection methods.
        
        Args:
            y: Audio time series
            sr: Sample rate
            
        Returns:
            List of results from different methods
        """
        results = []
        
        # Method 1: Standard librosa with default parameters
        try:
            tempo1, beats1 = librosa.beat.beat_track(y=y, sr=sr, units='frames')
            beat_times1 = librosa.frames_to_time(beats1, sr=sr)
            onset_env1 = librosa.onset.onset_strength(y=y, sr=sr)
            confidence1 = self._calculate_enhanced_confidence(beat_times1, beats1, onset_env1, sr)
            
            results.append({
                "tempo": float(tempo1),
                "beat_times": beat_times1,
                "beat_frames": beats1,
                "confidence": confidence1,
                "method": "standard"
            })
        except Exception as e:
            logger.warning(f"Standard method failed: {e}")
        
        # Method 2: Spectral centroid onset detection
        try:
            onset_env2 = librosa.onset.onset_strength(
                y=y, sr=sr, 
                feature=librosa.feature.spectral_centroid,
                aggregate=np.median
            )
            tempo2, beats2 = librosa.beat.beat_track(
                onset_envelope=onset_env2, sr=sr, units='frames'
            )
            beat_times2 = librosa.frames_to_time(beats2, sr=sr)
            confidence2 = self._calculate_enhanced_confidence(beat_times2, beats2, onset_env2, sr)
            
            results.append({
                "tempo": float(tempo2),
                "beat_times": beat_times2,
                "beat_frames": beats2,
                "confidence": confidence2,
                "method": "spectral_centroid"
            })
        except Exception as e:
            logger.warning(f"Spectral centroid method failed: {e}")
        
        # Method 3: Mel-frequency onset detection
        try:
            onset_env3 = librosa.onset.onset_strength(
                y=y, sr=sr,
                n_mels=256,
                fmax=8000,
                aggregate=np.median
            )
            tempo3, beats3 = librosa.beat.beat_track(
                onset_envelope=onset_env3, sr=sr, units='frames',
                start_bpm=120, tightness=200
            )
            beat_times3 = librosa.frames_to_time(beats3, sr=sr)
            confidence3 = self._calculate_enhanced_confidence(beat_times3, beats3, onset_env3, sr)
            
            results.append({
                "tempo": float(tempo3),
                "beat_times": beat_times3,
                "beat_frames": beats3,
                "confidence": confidence3,
                "method": "mel_frequency"
            })
        except Exception as e:
            logger.warning(f"Mel-frequency method failed: {e}")
        
        # Method 4: Harmonic-percussive separation
        try:
            y_harmonic, y_percussive = librosa.effects.hpss(y)
            onset_env4 = librosa.onset.onset_strength(y=y_percussive, sr=sr)
            tempo4, beats4 = librosa.beat.beat_track(
                onset_envelope=onset_env4, sr=sr, units='frames'
            )
            beat_times4 = librosa.frames_to_time(beats4, sr=sr)
            confidence4 = self._calculate_enhanced_confidence(beat_times4, beats4, onset_env4, sr)
            
            results.append({
                "tempo": float(tempo4),
                "beat_times": beat_times4,
                "beat_frames": beats4,
                "confidence": confidence4,
                "method": "harmonic_percussive"
            })
        except Exception as e:
            logger.warning(f"Harmonic-percussive method failed: {e}")
        
        return results
    
    def _calculate_enhanced_confidence(self, beat_times: np.ndarray, beat_frames: np.ndarray, 
                                     onset_env: np.ndarray, sr: int) -> float:
        """
        Calculate enhanced confidence score that better reflects actual beat alignment accuracy.
        
        Args:
            beat_times: Beat times in seconds
            beat_frames: Beat frame positions
            onset_env: Onset strength envelope
            sr: Sample rate
            
        Returns:
            Enhanced confidence score
        """
        try:
            if len(beat_times) == 0:
                return 0.0
            
            # 1. Beat strength score
            beat_strengths = onset_env[beat_frames]
            beat_strength_score = np.mean(beat_strengths) / (np.mean(onset_env) + 1e-8)
            
            # 2. Temporal consistency score
            if len(beat_times) > 1:
                intervals = np.diff(beat_times)
                mean_interval = np.mean(intervals)
                std_interval = np.std(intervals)
                consistency_score = 1.0 - min(1.0, std_interval / (mean_interval + 1e-8))
            else:
                consistency_score = 0.0
            
            # 3. Onset alignment score
            onset_peaks = librosa.onset.onset_detect(
                onset_envelope=onset_env, sr=sr, units='time'
            )
            
            if len(onset_peaks) > 0:
                # Find closest onset for each beat
                alignment_distances = []
                for beat_time in beat_times:
                    distances = np.abs(onset_peaks - beat_time)
                    min_distance = np.min(distances)
                    alignment_distances.append(min_distance)
                
                # Score based on how close beats are to onsets
                mean_distance = np.mean(alignment_distances)
                alignment_score = np.exp(-mean_distance * 10)  # Exponential decay
            else:
                alignment_score = 0.5
            
            # 4. Tempo plausibility score
            if len(beat_times) > 1:
                tempo = 60.0 / np.mean(np.diff(beat_times))
                if 60 <= tempo <= 200:
                    tempo_score = 1.0
                elif 30 <= tempo <= 300:
                    tempo_score = 0.7
                else:
                    tempo_score = 0.3
            else:
                tempo_score = 0.5
            
            # Combine scores with weights
            confidence = (
                0.3 * beat_strength_score +
                0.3 * consistency_score +
                0.3 * alignment_score +
                0.1 * tempo_score
            )
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            logger.warning(f"Error calculating enhanced confidence: {e}")
            return 0.5
    
    def _ensemble_vote_beat_detection(self, method_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Use ensemble voting to select the best beat detection result from multiple methods.
        
        Args:
            method_results: List of results from different detection methods
            
        Returns:
            Best result based on ensemble voting
        """
        if not method_results:
            return self._create_empty_result()
        
        if len(method_results) == 1:
            return method_results[0]
        
        try:
            # Score each result
            scored_results = []
            for result in method_results:
                score = self._score_beat_result(result, method_results)
                scored_results.append((score, result))
            
            # Sort by score (highest first)
            scored_results.sort(key=lambda x: x[0], reverse=True)
            
            best_result = scored_results[0][1]
            
            logger.info(f"Ensemble voting selected {best_result['method']} method "
                       f"with score {scored_results[0][0]:.3f}")
            
            return best_result
            
        except Exception as e:
            logger.error(f"Error in ensemble voting: {e}")
            # Return result with highest confidence
            return max(method_results, key=lambda x: x.get('confidence', 0))
    
    def _score_beat_result(self, result: Dict[str, Any], all_results: List[Dict[str, Any]]) -> float:
        """
        Score a beat detection result for ensemble voting.
        
        Args:
            result: Result to score
            all_results: All results for comparison
            
        Returns:
            Score for the result
        """
        try:
            # Base score from confidence
            score = result.get('confidence', 0.0)
            
            # Bonus for agreement with other methods
            beat_times = result.get('beat_times', np.array([]))
            if len(beat_times) > 0:
                agreement_scores = []
                for other_result in all_results:
                    if other_result is result:
                        continue
                    
                    other_beats = other_result.get('beat_times', np.array([]))
                    if len(other_beats) > 0:
                        similarity = self._calculate_beat_alignment_similarity(beat_times, other_beats)
                        agreement_scores.append(similarity)
                
                if agreement_scores:
                    agreement_bonus = np.mean(agreement_scores) * 0.3
                    score += agreement_bonus
            
            # Penalty for extreme tempos
            tempo = result.get('tempo', 120)
            if tempo < 60 or tempo > 200:
                score *= 0.7
            
            return score
            
        except Exception as e:
            logger.warning(f"Error scoring result: {e}")
            return result.get('confidence', 0.0)
    
    def _calculate_beat_alignment_similarity(self, beats1: np.ndarray, beats2: np.ndarray) -> float:
        """
        Calculate similarity between two sets of beat times.
        
        Args:
            beats1: First set of beat times
            beats2: Second set of beat times
            
        Returns:
            Similarity score between 0 and 1
        """
        try:
            if len(beats1) == 0 or len(beats2) == 0:
                return 0.0
            
            # Find matches within tolerance
            tolerance = 0.1  # 100ms tolerance
            matches = 0
            
            for beat1 in beats1:
                distances = np.abs(beats2 - beat1)
                if np.min(distances) <= tolerance:
                    matches += 1
            
            # Similarity based on proportion of matches
            similarity = matches / max(len(beats1), len(beats2))
            return similarity
            
        except Exception as e:
            logger.warning(f"Error calculating similarity: {e}")
            return 0.0
    
    def _basic_fallback(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Basic fallback method when enhanced analysis fails.
        
        Args:
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Basic beat grid result
        """
        try:
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr, units='frames')
            beat_times = librosa.frames_to_time(beats, sr=sr)
            
            return {
                "tempo": float(tempo),
                "beat_times": beat_times.tolist(),
                "confidence": 0.5,
                "enhanced": False,
                "method": "basic_fallback"
            }
        except Exception as e:
            logger.error(f"Even basic fallback failed: {e}")
            return self._create_empty_result()
    
    def _create_empty_result(self) -> Dict[str, Any]:
        """Create empty result for error cases"""
        return {
            "tempo": 120.0,
            "beat_times": [],
            "confidence": 0.0,
            "enhanced": False,
            "method": "empty_fallback",
            "error": "All detection methods failed"
        }
