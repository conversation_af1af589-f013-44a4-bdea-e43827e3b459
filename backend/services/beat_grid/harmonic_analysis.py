"""
Harmonic structure analysis for beat grid extraction.
Analyzes harmonic content to improve beat detection and identify musical structure.
"""

import logging
import numpy as np
import librosa
import scipy.signal
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class HarmonicAnalyzer:
    """Analyzes harmonic structure to improve beat grid detection"""
    
    def __init__(self):
        pass
    
    def analyze_harmonic_structure(self, y: np.n<PERSON><PERSON>, sr: int, beat_frames: np.n<PERSON><PERSON>) -> Dict[str, Any]:
        """
        Analyze harmonic structure to identify segments and improve beat grid.
        
        Args:
            y: Audio time series
            sr: Sample rate
            beat_frames: Detected beat frame positions
            
        Returns:
            Harmonic analysis results
        """
        try:
            logger.info("Starting harmonic structure analysis")
            
            # Extract harmonic and percussive components
            y_harmonic, y_percussive = librosa.effects.hpss(y)
            
            # Analyze chroma features
            chroma_analysis = self._analyze_chroma_features(y_harmonic, sr, beat_frames)
            
            # Detect key and mode
            key_analysis = self._detect_key_and_mode(y_harmonic, sr)
            
            # Analyze chord progressions
            chord_analysis = self._analyze_chord_progressions(y_harmonic, sr, beat_frames)
            
            # Detect harmonic rhythm
            harmonic_rhythm = self._detect_harmonic_rhythm(y_harmonic, sr, beat_frames)
            
            # Identify structural sections based on harmony
            harmonic_sections = self._identify_harmonic_sections(chroma_analysis, beat_frames, sr)
            
            result = {
                "chroma_analysis": chroma_analysis,
                "key_analysis": key_analysis,
                "chord_analysis": chord_analysis,
                "harmonic_rhythm": harmonic_rhythm,
                "harmonic_sections": harmonic_sections,
                "has_harmonic_content": self._has_significant_harmonic_content(y_harmonic, y_percussive)
            }
            
            logger.info(f"Harmonic analysis completed - Key: {key_analysis.get('key', 'Unknown')}, "
                       f"Sections: {len(harmonic_sections)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in harmonic analysis: {e}")
            return {
                "chroma_analysis": {},
                "key_analysis": {},
                "chord_analysis": {},
                "harmonic_rhythm": {},
                "harmonic_sections": [],
                "has_harmonic_content": False,
                "error": str(e)
            }
    
    def _analyze_chroma_features(self, y_harmonic: np.ndarray, sr: int, beat_frames: np.ndarray) -> Dict[str, Any]:
        """
        Analyze chroma features for harmonic content.
        
        Args:
            y_harmonic: Harmonic component of audio
            sr: Sample rate
            beat_frames: Beat frame positions
            
        Returns:
            Chroma analysis results
        """
        try:
            # Extract chroma features
            chroma = librosa.feature.chroma_stft(y=y_harmonic, sr=sr, hop_length=512)
            
            # Synchronize chroma to beats
            chroma_sync = librosa.util.sync(chroma, beat_frames)
            
            # Calculate chroma statistics
            chroma_mean = np.mean(chroma_sync, axis=1)
            chroma_std = np.std(chroma_sync, axis=1)
            
            # Find dominant pitch classes
            dominant_pitches = np.argsort(chroma_mean)[-3:]  # Top 3 pitch classes
            
            # Calculate chroma clarity (how distinct the pitch classes are)
            chroma_clarity = np.max(chroma_mean) - np.mean(chroma_mean)
            
            return {
                "chroma": chroma_sync.tolist(),
                "chroma_mean": chroma_mean.tolist(),
                "chroma_std": chroma_std.tolist(),
                "dominant_pitches": dominant_pitches.tolist(),
                "chroma_clarity": float(chroma_clarity),
                "pitch_class_names": ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            }
            
        except Exception as e:
            logger.warning(f"Error analyzing chroma features: {e}")
            return {}
    
    def _detect_key_and_mode(self, y_harmonic: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Detect the musical key and mode of the audio.
        
        Args:
            y_harmonic: Harmonic component of audio
            sr: Sample rate
            
        Returns:
            Key and mode analysis
        """
        try:
            # Extract chroma features
            chroma = librosa.feature.chroma_stft(y=y_harmonic, sr=sr)
            
            # Average chroma over time
            chroma_mean = np.mean(chroma, axis=1)
            
            # Key profiles (Krumhansl-Schmuckler)
            major_profile = np.array([6.35, 2.23, 3.48, 2.33, 4.38, 4.09, 2.52, 5.19, 2.39, 3.66, 2.29, 2.88])
            minor_profile = np.array([6.33, 2.68, 3.52, 5.38, 2.60, 3.53, 2.54, 4.75, 3.98, 2.69, 3.34, 3.17])
            
            # Normalize profiles
            major_profile = major_profile / np.sum(major_profile)
            minor_profile = minor_profile / np.sum(minor_profile)
            
            # Calculate correlations for all keys
            key_correlations = []
            
            for shift in range(12):
                # Major key correlation
                shifted_major = np.roll(major_profile, shift)
                major_corr = np.corrcoef(chroma_mean, shifted_major)[0, 1]
                key_correlations.append(('major', shift, major_corr))
                
                # Minor key correlation
                shifted_minor = np.roll(minor_profile, shift)
                minor_corr = np.corrcoef(chroma_mean, shifted_minor)[0, 1]
                key_correlations.append(('minor', shift, minor_corr))
            
            # Find best match
            best_match = max(key_correlations, key=lambda x: x[2] if not np.isnan(x[2]) else -1)
            
            key_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            
            return {
                "key": key_names[best_match[1]],
                "mode": best_match[0],
                "confidence": float(best_match[2]) if not np.isnan(best_match[2]) else 0.0,
                "key_correlations": [(mode, key_names[shift], float(corr) if not np.isnan(corr) else 0.0) 
                                   for mode, shift, corr in key_correlations]
            }
            
        except Exception as e:
            logger.warning(f"Error detecting key and mode: {e}")
            return {"key": "Unknown", "mode": "unknown", "confidence": 0.0}
    
    def _analyze_chord_progressions(self, y_harmonic: np.ndarray, sr: int, beat_frames: np.ndarray) -> Dict[str, Any]:
        """
        Analyze chord progressions in the audio.
        
        Args:
            y_harmonic: Harmonic component of audio
            sr: Sample rate
            beat_frames: Beat frame positions
            
        Returns:
            Chord progression analysis
        """
        try:
            # Extract chroma features synchronized to beats
            chroma = librosa.feature.chroma_stft(y=y_harmonic, sr=sr, hop_length=512)
            chroma_sync = librosa.util.sync(chroma, beat_frames)
            
            # Simple chord detection based on chroma peaks
            chords = []
            chord_changes = []
            
            for i, beat_chroma in enumerate(chroma_sync.T):
                # Find the most prominent pitch classes
                prominent_pitches = np.where(beat_chroma > 0.5 * np.max(beat_chroma))[0]
                
                # Simple chord classification
                chord_name = self._classify_chord(prominent_pitches)
                chords.append(chord_name)
                
                # Detect chord changes
                if i > 0 and chord_name != chords[i-1]:
                    beat_time = librosa.frames_to_time(beat_frames[i], sr=sr)
                    chord_changes.append({
                        "time": float(beat_time),
                        "beat_index": i,
                        "from_chord": chords[i-1],
                        "to_chord": chord_name
                    })
            
            # Calculate chord change frequency
            change_frequency = len(chord_changes) / len(chords) if chords else 0
            
            return {
                "chords": chords,
                "chord_changes": chord_changes,
                "change_frequency": float(change_frequency),
                "unique_chords": list(set(chords))
            }
            
        except Exception as e:
            logger.warning(f"Error analyzing chord progressions: {e}")
            return {"chords": [], "chord_changes": [], "change_frequency": 0.0}
    
    def _classify_chord(self, prominent_pitches: np.ndarray) -> str:
        """
        Simple chord classification based on prominent pitch classes.
        
        Args:
            prominent_pitches: Array of prominent pitch class indices
            
        Returns:
            Chord name
        """
        pitch_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        
        if len(prominent_pitches) == 0:
            return "N"  # No chord
        elif len(prominent_pitches) == 1:
            return pitch_names[prominent_pitches[0]]
        elif len(prominent_pitches) >= 3:
            # Sort pitches
            sorted_pitches = np.sort(prominent_pitches)
            root = sorted_pitches[0]
            
            # Check for major triad (root, major third, fifth)
            if len(sorted_pitches) >= 3:
                intervals = (sorted_pitches - root) % 12
                if 4 in intervals and 7 in intervals:
                    return f"{pitch_names[root]}maj"
                elif 3 in intervals and 7 in intervals:
                    return f"{pitch_names[root]}min"
            
            return f"{pitch_names[root]}?"
        else:
            return f"{pitch_names[prominent_pitches[0]]}?"
    
    def _detect_harmonic_rhythm(self, y_harmonic: np.ndarray, sr: int, beat_frames: np.ndarray) -> Dict[str, Any]:
        """
        Detect harmonic rhythm (rate of harmonic change).
        
        Args:
            y_harmonic: Harmonic component of audio
            sr: Sample rate
            beat_frames: Beat frame positions
            
        Returns:
            Harmonic rhythm analysis
        """
        try:
            # Extract chroma features
            chroma = librosa.feature.chroma_stft(y=y_harmonic, sr=sr, hop_length=512)
            chroma_sync = librosa.util.sync(chroma, beat_frames)
            
            # Calculate chroma change between beats
            chroma_diff = np.diff(chroma_sync, axis=1)
            chroma_change = np.sum(np.abs(chroma_diff), axis=0)
            
            # Smooth the change signal
            if len(chroma_change) > 5:
                chroma_change_smooth = scipy.signal.savgol_filter(chroma_change, 5, 2)
            else:
                chroma_change_smooth = chroma_change
            
            # Find peaks in harmonic change
            if len(chroma_change_smooth) > 0:
                peaks, _ = scipy.signal.find_peaks(
                    chroma_change_smooth, 
                    height=np.mean(chroma_change_smooth) + np.std(chroma_change_smooth)
                )
                
                peak_times = librosa.frames_to_time(beat_frames[peaks], sr=sr)
            else:
                peaks = np.array([])
                peak_times = np.array([])
            
            return {
                "chroma_change": chroma_change.tolist(),
                "chroma_change_smooth": chroma_change_smooth.tolist(),
                "harmonic_change_peaks": peaks.tolist(),
                "harmonic_change_times": peak_times.tolist(),
                "average_change_rate": float(np.mean(chroma_change)) if len(chroma_change) > 0 else 0.0
            }
            
        except Exception as e:
            logger.warning(f"Error detecting harmonic rhythm: {e}")
            return {"chroma_change": [], "harmonic_change_peaks": [], "average_change_rate": 0.0}
    
    def _identify_harmonic_sections(self, chroma_analysis: Dict[str, Any], 
                                  beat_frames: np.ndarray, sr: int) -> List[Dict[str, Any]]:
        """
        Identify structural sections based on harmonic content.
        
        Args:
            chroma_analysis: Results from chroma analysis
            beat_frames: Beat frame positions
            sr: Sample rate
            
        Returns:
            List of harmonic sections
        """
        try:
            if not chroma_analysis or "chroma" not in chroma_analysis:
                return []
            
            chroma = np.array(chroma_analysis["chroma"])
            
            if chroma.size == 0:
                return []
            
            # Use recurrence matrix to find similar harmonic sections
            R = librosa.segment.recurrence_matrix(
                chroma, 
                width=8,  # ~4 beats context
                mode='affinity',
                sym=True
            )
            
            # Detect boundaries
            boundaries = librosa.segment.agglomerative(R, k=None)
            
            # Convert to time
            boundary_times = librosa.frames_to_time(beat_frames[boundaries], sr=sr)
            
            # Create section objects
            sections = []
            for i in range(len(boundary_times) - 1):
                start_time = boundary_times[i]
                end_time = boundary_times[i + 1]
                
                # Analyze harmonic content of this section
                start_beat = boundaries[i]
                end_beat = boundaries[i + 1]
                section_chroma = chroma[:, start_beat:end_beat]
                
                section = {
                    "start": float(start_time),
                    "end": float(end_time),
                    "duration": float(end_time - start_time),
                    "start_beat": int(start_beat),
                    "end_beat": int(end_beat),
                    "harmonic_content": np.mean(section_chroma, axis=1).tolist(),
                    "section_index": i
                }
                
                sections.append(section)
            
            return sections
            
        except Exception as e:
            logger.warning(f"Error identifying harmonic sections: {e}")
            return []
    
    def _has_significant_harmonic_content(self, y_harmonic: np.ndarray, y_percussive: np.ndarray) -> bool:
        """
        Determine if the audio has significant harmonic content.
        
        Args:
            y_harmonic: Harmonic component
            y_percussive: Percussive component
            
        Returns:
            True if significant harmonic content is present
        """
        try:
            # Calculate energy ratio
            harmonic_energy = np.sum(y_harmonic ** 2)
            percussive_energy = np.sum(y_percussive ** 2)
            total_energy = harmonic_energy + percussive_energy
            
            if total_energy == 0:
                return False
            
            harmonic_ratio = harmonic_energy / total_energy
            
            # Consider significant if harmonic content is > 30%
            return harmonic_ratio > 0.3
            
        except Exception as e:
            logger.warning(f"Error checking harmonic content: {e}")
            return False
