"""
Core beat grid extraction functionality.
Basic beat detection using librosa.
"""

import os
import logging
import numpy as np
import librosa
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class CoreBeatExtractor:
    """Core beat grid extraction using librosa"""
    
    def __init__(self):
        self._cache = {}
    
    def extract_beat_grid(self, file_path: str) -> Dict[str, Any]:
        """
        Extract basic beat grid from an audio file using librosa
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Dictionary with beat grid information
        """
        try:
            logger.info(f"Loading audio file: {file_path}")
            
            # Load audio file
            y, sr = librosa.load(file_path, sr=None)
            
            # Extract tempo and beats
            tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr, units='frames')
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)
            
            # Calculate confidence based on beat strength
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            confidence = self._calculate_basic_confidence(beat_frames, onset_env)
            
            logger.info(f"Basic beat extraction completed - Tempo: {tempo:.2f} BPM, Beats: {len(beat_times)}, Confidence: {confidence:.3f}")
            
            return {
                "tempo": float(tempo),
                "beat_times": beat_times.tolist(),
                "confidence": float(confidence),
                "enhanced": False,
                "method": "basic_librosa"
            }
            
        except Exception as e:
            logger.error(f"Error extracting beat grid from {file_path}: {str(e)}")
            return {
                "error": str(e),
                "tempo": 120.0,  # Default fallback
                "beat_times": [],
                "confidence": 0.0,
                "enhanced": False
            }
    
    def _calculate_basic_confidence(self, beat_frames: np.ndarray, onset_env: np.ndarray) -> float:
        """
        Calculate basic confidence score for beat detection
        
        Args:
            beat_frames: Detected beat frame positions
            onset_env: Onset strength envelope
            
        Returns:
            Confidence score between 0 and 1
        """
        try:
            if len(beat_frames) == 0:
                return 0.0
            
            # Get onset strengths at beat positions
            beat_strengths = onset_env[beat_frames]
            
            # Calculate mean strength at beats vs overall mean
            beat_mean = np.mean(beat_strengths)
            overall_mean = np.mean(onset_env)
            
            if overall_mean == 0:
                return 0.0
            
            # Confidence is ratio of beat strength to overall strength
            confidence = min(beat_mean / overall_mean, 1.0)
            
            # Apply consistency bonus
            if len(beat_frames) > 1:
                intervals = np.diff(beat_frames)
                interval_consistency = 1.0 - (np.std(intervals) / np.mean(intervals))
                confidence *= (0.7 + 0.3 * max(0, interval_consistency))
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            logger.warning(f"Error calculating confidence: {e}")
            return 0.5  # Default moderate confidence
    
    def validate_basic_beat_grid(self, beat_times: np.ndarray, tempo: float) -> Dict[str, Any]:
        """
        Basic validation of beat grid quality
        
        Args:
            beat_times: Array of beat times
            tempo: Detected tempo
            
        Returns:
            Validation results
        """
        issues = []
        
        # Check if we have any beats
        if len(beat_times) == 0:
            issues.append("No beats detected")
            return {"valid": False, "issues": issues}
        
        # Check tempo range
        if tempo < 60 or tempo > 200:
            issues.append(f"Unusual tempo: {tempo:.1f} BPM")
        
        # Check beat consistency
        if len(beat_times) > 1:
            intervals = np.diff(beat_times)
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            
            if std_interval / mean_interval > 0.3:
                issues.append("Inconsistent beat intervals")
        
        # Check minimum number of beats
        if len(beat_times) < 4:
            issues.append("Too few beats detected")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "beat_count": len(beat_times),
            "tempo": tempo
        }
    
    def extract_with_fallback(self, file_path: str) -> Dict[str, Any]:
        """
        Extract beat grid with fallback parameters if initial attempt fails
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Beat grid data with fallback if needed
        """
        try:
            # Try standard extraction first
            result = self.extract_beat_grid(file_path)
            
            # Validate result
            if result.get("confidence", 0) > 0.3 and len(result.get("beat_times", [])) > 4:
                return result
            
            logger.info(f"Low confidence ({result.get('confidence', 0):.3f}), trying fallback parameters")
            
            # Try with different parameters
            y, sr = librosa.load(file_path, sr=None)
            
            # More aggressive onset detection
            onset_env = librosa.onset.onset_strength(
                y=y, sr=sr, 
                aggregate=np.median,
                fmax=8000,
                n_mels=256
            )
            
            tempo, beat_frames = librosa.beat.beat_track(
                onset_envelope=onset_env,
                sr=sr,
                units='frames',
                trim=False,
                start_bpm=120,
                tightness=200
            )
            
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)
            confidence = self._calculate_basic_confidence(beat_frames, onset_env)
            
            fallback_result = {
                "tempo": float(tempo),
                "beat_times": beat_times.tolist(),
                "confidence": float(confidence),
                "enhanced": False,
                "method": "fallback_librosa"
            }
            
            logger.info(f"Fallback extraction - Tempo: {tempo:.2f} BPM, Confidence: {confidence:.3f}")
            
            return fallback_result
            
        except Exception as e:
            logger.error(f"Error in fallback extraction: {e}")
            return {
                "error": str(e),
                "tempo": 120.0,
                "beat_times": [],
                "confidence": 0.0,
                "enhanced": False,
                "method": "error_fallback"
            }
    
    def get_tempo_from_beats(self, beat_times: np.ndarray) -> float:
        """
        Calculate tempo from beat times
        
        Args:
            beat_times: Array of beat times in seconds
            
        Returns:
            Tempo in BPM
        """
        if len(beat_times) < 2:
            return 120.0  # Default tempo
        
        intervals = np.diff(beat_times)
        mean_interval = np.mean(intervals)
        
        if mean_interval <= 0:
            return 120.0
        
        tempo = 60.0 / mean_interval
        return float(tempo)
    
    def clear_cache(self):
        """Clear the internal cache"""
        self._cache.clear()
        logger.info("Beat extraction cache cleared")
