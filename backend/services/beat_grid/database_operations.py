"""
Database operations for beat grid service.
CRUD operations for storing and retrieving beat grid data.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)

class BeatGridDatabaseManager:
    """Manages database operations for beat grids"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def save_beat_grid(self, track_id: int, beat_grid_data: Dict[str, Any]) -> bool:
        """
        Save beat grid data to database.
        
        Args:
            track_id: ID of the track
            beat_grid_data: Beat grid data to save
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Prepare data for storage
            storage_data = {
                "tempo": beat_grid_data.get("tempo", 120.0),
                "beat_times": beat_grid_data.get("beat_times", []),
                "confidence": beat_grid_data.get("confidence", 0.0),
                "enhanced": beat_grid_data.get("enhanced", False),
                "method": beat_grid_data.get("method", "unknown"),
                "metadata": {
                    "segments": beat_grid_data.get("segments", []),
                    "harmonic_analysis": beat_grid_data.get("harmonic_analysis", {}),
                    "loop_points": beat_grid_data.get("loop_points", []),
                    "quality_metrics": beat_grid_data.get("quality_metrics", {}),
                    "corrections_applied": beat_grid_data.get("corrections_applied", [])
                }
            }
            
            # Check if beat grid already exists
            existing = self.db.execute(
                text("SELECT id FROM beat_grids WHERE track_id = :track_id"),
                {"track_id": track_id}
            ).fetchone()
            
            if existing:
                # Update existing record
                self.db.execute(
                    text("""
                        UPDATE beat_grids 
                        SET tempo = :tempo,
                            beat_times = :beat_times,
                            confidence = :confidence,
                            enhanced = :enhanced,
                            method = :method,
                            metadata = :metadata,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE track_id = :track_id
                    """),
                    {
                        "track_id": track_id,
                        "tempo": storage_data["tempo"],
                        "beat_times": json.dumps(storage_data["beat_times"]),
                        "confidence": storage_data["confidence"],
                        "enhanced": storage_data["enhanced"],
                        "method": storage_data["method"],
                        "metadata": json.dumps(storage_data["metadata"])
                    }
                )
                logger.info(f"Updated beat grid for track {track_id}")
            else:
                # Insert new record
                self.db.execute(
                    text("""
                        INSERT INTO beat_grids 
                        (track_id, tempo, beat_times, confidence, enhanced, method, metadata, created_at, updated_at)
                        VALUES (:track_id, :tempo, :beat_times, :confidence, :enhanced, :method, :metadata, 
                                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """),
                    {
                        "track_id": track_id,
                        "tempo": storage_data["tempo"],
                        "beat_times": json.dumps(storage_data["beat_times"]),
                        "confidence": storage_data["confidence"],
                        "enhanced": storage_data["enhanced"],
                        "method": storage_data["method"],
                        "metadata": json.dumps(storage_data["metadata"])
                    }
                )
                logger.info(f"Saved new beat grid for track {track_id}")
            
            self.db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving beat grid for track {track_id}: {e}")
            self.db.rollback()
            return False
    
    def get_beat_grid(self, track_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve beat grid data from database.
        
        Args:
            track_id: ID of the track
            
        Returns:
            Beat grid data or None if not found
        """
        try:
            result = self.db.execute(
                text("""
                    SELECT tempo, beat_times, confidence, enhanced, method, metadata, 
                           created_at, updated_at
                    FROM beat_grids 
                    WHERE track_id = :track_id
                """),
                {"track_id": track_id}
            ).fetchone()
            
            if not result:
                return None
            
            # Parse JSON fields
            beat_times = json.loads(result.beat_times) if result.beat_times else []
            metadata = json.loads(result.metadata) if result.metadata else {}
            
            beat_grid_data = {
                "tempo": float(result.tempo),
                "beat_times": beat_times,
                "confidence": float(result.confidence),
                "enhanced": bool(result.enhanced),
                "method": result.method,
                "created_at": result.created_at,
                "updated_at": result.updated_at
            }
            
            # Add metadata fields
            beat_grid_data.update(metadata)
            
            return beat_grid_data
            
        except Exception as e:
            logger.error(f"Error retrieving beat grid for track {track_id}: {e}")
            return None
    
    def delete_beat_grid(self, track_id: int) -> bool:
        """
        Delete beat grid data from database.
        
        Args:
            track_id: ID of the track
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.db.execute(
                text("DELETE FROM beat_grids WHERE track_id = :track_id"),
                {"track_id": track_id}
            )
            
            self.db.commit()
            
            if result.rowcount > 0:
                logger.info(f"Deleted beat grid for track {track_id}")
                return True
            else:
                logger.warning(f"No beat grid found to delete for track {track_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting beat grid for track {track_id}: {e}")
            self.db.rollback()
            return False
    
    def get_beat_grids_by_tempo_range(self, min_tempo: float, max_tempo: float) -> List[Dict[str, Any]]:
        """
        Get beat grids within a tempo range.
        
        Args:
            min_tempo: Minimum tempo in BPM
            max_tempo: Maximum tempo in BPM
            
        Returns:
            List of beat grid data
        """
        try:
            results = self.db.execute(
                text("""
                    SELECT bg.track_id, bg.tempo, bg.confidence, bg.enhanced, bg.method,
                           t.title, t.artist, t.file_path
                    FROM beat_grids bg
                    JOIN tracks t ON bg.track_id = t.id
                    WHERE bg.tempo BETWEEN :min_tempo AND :max_tempo
                    ORDER BY bg.confidence DESC
                """),
                {"min_tempo": min_tempo, "max_tempo": max_tempo}
            ).fetchall()
            
            beat_grids = []
            for result in results:
                beat_grids.append({
                    "track_id": result.track_id,
                    "tempo": float(result.tempo),
                    "confidence": float(result.confidence),
                    "enhanced": bool(result.enhanced),
                    "method": result.method,
                    "track_title": result.title,
                    "track_artist": result.artist,
                    "file_path": result.file_path
                })
            
            return beat_grids
            
        except Exception as e:
            logger.error(f"Error getting beat grids by tempo range: {e}")
            return []
    
    def get_beat_grid_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about beat grids in the database.
        
        Returns:
            Dictionary with statistics
        """
        try:
            # Total count
            total_result = self.db.execute(
                text("SELECT COUNT(*) as count FROM beat_grids")
            ).fetchone()
            total_count = total_result.count if total_result else 0
            
            # Enhanced vs basic
            enhanced_result = self.db.execute(
                text("SELECT COUNT(*) as count FROM beat_grids WHERE enhanced = true")
            ).fetchone()
            enhanced_count = enhanced_result.count if enhanced_result else 0
            
            # Average confidence
            confidence_result = self.db.execute(
                text("SELECT AVG(confidence) as avg_confidence FROM beat_grids")
            ).fetchone()
            avg_confidence = float(confidence_result.avg_confidence) if confidence_result.avg_confidence else 0.0
            
            # Tempo distribution
            tempo_stats = self.db.execute(
                text("""
                    SELECT 
                        MIN(tempo) as min_tempo,
                        MAX(tempo) as max_tempo,
                        AVG(tempo) as avg_tempo,
                        COUNT(CASE WHEN tempo BETWEEN 60 AND 90 THEN 1 END) as slow_count,
                        COUNT(CASE WHEN tempo BETWEEN 90 AND 130 THEN 1 END) as medium_count,
                        COUNT(CASE WHEN tempo BETWEEN 130 AND 180 THEN 1 END) as fast_count
                    FROM beat_grids
                """)
            ).fetchone()
            
            # Method distribution
            method_stats = self.db.execute(
                text("""
                    SELECT method, COUNT(*) as count
                    FROM beat_grids
                    GROUP BY method
                    ORDER BY count DESC
                """)
            ).fetchall()
            
            method_distribution = {result.method: result.count for result in method_stats}
            
            statistics = {
                "total_beat_grids": total_count,
                "enhanced_count": enhanced_count,
                "basic_count": total_count - enhanced_count,
                "average_confidence": avg_confidence,
                "tempo_stats": {
                    "min_tempo": float(tempo_stats.min_tempo) if tempo_stats.min_tempo else 0.0,
                    "max_tempo": float(tempo_stats.max_tempo) if tempo_stats.max_tempo else 0.0,
                    "avg_tempo": float(tempo_stats.avg_tempo) if tempo_stats.avg_tempo else 0.0,
                    "slow_tracks": tempo_stats.slow_count,
                    "medium_tracks": tempo_stats.medium_count,
                    "fast_tracks": tempo_stats.fast_count
                },
                "method_distribution": method_distribution
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"Error getting beat grid statistics: {e}")
            return {}
    
    def cleanup_orphaned_beat_grids(self) -> int:
        """
        Clean up beat grids for tracks that no longer exist.
        
        Returns:
            Number of orphaned beat grids removed
        """
        try:
            result = self.db.execute(
                text("""
                    DELETE FROM beat_grids 
                    WHERE track_id NOT IN (SELECT id FROM tracks)
                """)
            )
            
            self.db.commit()
            
            removed_count = result.rowcount
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} orphaned beat grids")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Error cleaning up orphaned beat grids: {e}")
            self.db.rollback()
            return 0
    
    def update_beat_grid_confidence(self, track_id: int, new_confidence: float) -> bool:
        """
        Update just the confidence score for a beat grid.
        
        Args:
            track_id: ID of the track
            new_confidence: New confidence score
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.db.execute(
                text("""
                    UPDATE beat_grids 
                    SET confidence = :confidence, updated_at = CURRENT_TIMESTAMP
                    WHERE track_id = :track_id
                """),
                {"track_id": track_id, "confidence": new_confidence}
            )
            
            self.db.commit()
            
            if result.rowcount > 0:
                logger.info(f"Updated confidence for track {track_id} to {new_confidence:.3f}")
                return True
            else:
                logger.warning(f"No beat grid found to update for track {track_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating beat grid confidence for track {track_id}: {e}")
            self.db.rollback()
            return False
    
    def get_tracks_needing_beat_analysis(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get tracks that don't have beat grid analysis yet.
        
        Args:
            limit: Maximum number of tracks to return
            
        Returns:
            List of track information
        """
        try:
            results = self.db.execute(
                text("""
                    SELECT t.id, t.title, t.artist, t.file_path, t.duration
                    FROM tracks t
                    LEFT JOIN beat_grids bg ON t.id = bg.track_id
                    WHERE bg.track_id IS NULL
                    AND t.file_path IS NOT NULL
                    ORDER BY t.created_at DESC
                    LIMIT :limit
                """),
                {"limit": limit}
            ).fetchall()
            
            tracks = []
            for result in results:
                tracks.append({
                    "id": result.id,
                    "title": result.title,
                    "artist": result.artist,
                    "file_path": result.file_path,
                    "duration": result.duration
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting tracks needing beat analysis: {e}")
            return []
