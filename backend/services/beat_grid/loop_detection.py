"""
Loop point detection for DJ applications.
Detects potential seamless loop points based on harmonic and rhythmic similarity.
"""

import logging
import numpy as np
import librosa
import scipy.signal
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)

class LoopDetector:
    """Detects potential seamless loop points for DJ applications"""
    
    def __init__(self):
        pass
    
    def detect_loop_points(self, y: np.ndarray, sr: int, beat_times: np.ndarray, 
                          beat_chroma: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Detect potential seamless loop points for DJ applications.
        
        Args:
            y: Audio time series
            sr: Sample rate
            beat_times: Beat times in seconds
            beat_chroma: Optional pre-computed beat-synchronized chroma
            
        Returns:
            Loop point detection results
        """
        try:
            logger.info("Starting loop point detection")
            
            if len(beat_times) < 8:  # Need at least 8 beats for meaningful loops
                return {
                    "loop_points": [],
                    "loop_segments": [],
                    "best_loops": [],
                    "error": "Insufficient beats for loop detection"
                }
            
            # Extract features for loop analysis
            features = self._extract_loop_features(y, sr, beat_times, beat_chroma)
            
            # Find potential loop points
            loop_candidates = self._find_loop_candidates(features, beat_times)
            
            # Evaluate loop quality
            evaluated_loops = self._evaluate_loop_quality(loop_candidates, features, y, sr)
            
            # Select best loops
            best_loops = self._select_best_loops(evaluated_loops)
            
            # Create loop segments for visualization
            loop_segments = self._create_loop_segments(best_loops, beat_times)
            
            result = {
                "loop_points": [loop["start_time"] for loop in best_loops],
                "loop_segments": loop_segments,
                "best_loops": best_loops,
                "total_candidates": len(loop_candidates),
                "evaluated_loops": len(evaluated_loops)
            }
            
            logger.info(f"Loop detection completed - {len(best_loops)} quality loops found "
                       f"from {len(loop_candidates)} candidates")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in loop point detection: {e}")
            return {
                "loop_points": [],
                "loop_segments": [],
                "best_loops": [],
                "error": str(e)
            }
    
    def _extract_loop_features(self, y: np.ndarray, sr: int, beat_times: np.ndarray, 
                              beat_chroma: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Extract features needed for loop point analysis.
        
        Args:
            y: Audio time series
            sr: Sample rate
            beat_times: Beat times in seconds
            beat_chroma: Optional pre-computed chroma
            
        Returns:
            Dictionary of features
        """
        try:
            # Convert beat times to frames
            beat_frames = librosa.time_to_frames(beat_times, sr=sr)
            
            # Extract chroma features if not provided
            if beat_chroma is None:
                chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=512)
                beat_chroma = librosa.util.sync(chroma, beat_frames)
            
            # Extract MFCC features
            mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=512)
            beat_mfcc = librosa.util.sync(mfcc, beat_frames)
            
            # Extract spectral features
            spectral_centroid = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=512)
            beat_spectral_centroid = librosa.util.sync(spectral_centroid, beat_frames)
            
            # Extract onset strength
            onset_env = librosa.onset.onset_strength(y=y, sr=sr, hop_length=512)
            beat_onset_strength = librosa.util.sync(onset_env.reshape(1, -1), beat_frames)
            
            return {
                "chroma": beat_chroma,
                "mfcc": beat_mfcc,
                "spectral_centroid": beat_spectral_centroid,
                "onset_strength": beat_onset_strength,
                "beat_frames": beat_frames,
                "beat_times": beat_times
            }
            
        except Exception as e:
            logger.warning(f"Error extracting loop features: {e}")
            return {}
    
    def _find_loop_candidates(self, features: Dict[str, Any], beat_times: np.ndarray) -> List[Dict[str, Any]]:
        """
        Find potential loop start and end points.
        
        Args:
            features: Extracted audio features
            beat_times: Beat times in seconds
            
        Returns:
            List of loop candidates
        """
        candidates = []
        
        try:
            if "chroma" not in features:
                return candidates
            
            chroma = features["chroma"]
            n_beats = len(beat_times)
            
            # Common loop lengths in beats (4, 8, 16, 32 beats)
            loop_lengths = [4, 8, 16, 32]
            
            for loop_length in loop_lengths:
                if loop_length >= n_beats:
                    continue
                
                # Try different starting positions
                for start_beat in range(0, n_beats - loop_length, 2):  # Step by 2 beats
                    end_beat = start_beat + loop_length
                    
                    if end_beat >= n_beats:
                        break
                    
                    # Calculate harmonic similarity between start and end
                    start_chroma = chroma[:, start_beat]
                    end_chroma = chroma[:, end_beat]
                    
                    # Cosine similarity
                    similarity = np.dot(start_chroma, end_chroma) / (
                        np.linalg.norm(start_chroma) * np.linalg.norm(end_chroma) + 1e-8
                    )
                    
                    # Only consider loops with reasonable harmonic similarity
                    if similarity > 0.7:
                        candidate = {
                            "start_beat": start_beat,
                            "end_beat": end_beat,
                            "start_time": beat_times[start_beat],
                            "end_time": beat_times[end_beat],
                            "length_beats": loop_length,
                            "duration": beat_times[end_beat] - beat_times[start_beat],
                            "harmonic_similarity": float(similarity)
                        }
                        
                        candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.warning(f"Error finding loop candidates: {e}")
            return []
    
    def _evaluate_loop_quality(self, candidates: List[Dict[str, Any]], features: Dict[str, Any], 
                              y: np.ndarray, sr: int) -> List[Dict[str, Any]]:
        """
        Evaluate the quality of loop candidates.
        
        Args:
            candidates: List of loop candidates
            features: Audio features
            y: Audio time series
            sr: Sample rate
            
        Returns:
            List of evaluated loops with quality scores
        """
        evaluated = []
        
        try:
            for candidate in candidates:
                quality_score = self._calculate_loop_quality(candidate, features, y, sr)
                
                if quality_score > 0.5:  # Only keep decent quality loops
                    candidate["quality_score"] = quality_score
                    evaluated.append(candidate)
            
            # Sort by quality score
            evaluated.sort(key=lambda x: x["quality_score"], reverse=True)
            
            return evaluated
            
        except Exception as e:
            logger.warning(f"Error evaluating loop quality: {e}")
            return candidates  # Return original candidates if evaluation fails
    
    def _calculate_loop_quality(self, candidate: Dict[str, Any], features: Dict[str, Any], 
                               y: np.ndarray, sr: int) -> float:
        """
        Calculate quality score for a loop candidate.
        
        Args:
            candidate: Loop candidate
            features: Audio features
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Quality score between 0 and 1
        """
        try:
            start_beat = candidate["start_beat"]
            end_beat = candidate["end_beat"]
            
            scores = []
            
            # 1. Harmonic similarity (already calculated)
            harmonic_score = candidate["harmonic_similarity"]
            scores.append(harmonic_score)
            
            # 2. Spectral similarity
            if "mfcc" in features:
                mfcc = features["mfcc"]
                start_mfcc = mfcc[:, start_beat]
                end_mfcc = mfcc[:, end_beat]
                
                spectral_similarity = np.dot(start_mfcc, end_mfcc) / (
                    np.linalg.norm(start_mfcc) * np.linalg.norm(end_mfcc) + 1e-8
                )
                scores.append(max(0, spectral_similarity))
            
            # 3. Onset strength similarity
            if "onset_strength" in features:
                onset_strength = features["onset_strength"]
                start_onset = onset_strength[0, start_beat]
                end_onset = onset_strength[0, end_beat]
                
                # Normalize onset strengths
                max_onset = np.max(onset_strength)
                if max_onset > 0:
                    start_onset_norm = start_onset / max_onset
                    end_onset_norm = end_onset / max_onset
                    
                    onset_similarity = 1.0 - abs(start_onset_norm - end_onset_norm)
                    scores.append(onset_similarity)
            
            # 4. Audio waveform similarity at boundaries
            start_time = candidate["start_time"]
            end_time = candidate["end_time"]
            
            # Compare small windows around start and end points
            window_size = int(0.1 * sr)  # 100ms window
            
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            
            if (start_sample + window_size < len(y) and 
                end_sample + window_size < len(y)):
                
                start_window = y[start_sample:start_sample + window_size]
                end_window = y[end_sample:end_sample + window_size]
                
                # Cross-correlation
                correlation = np.corrcoef(start_window, end_window)[0, 1]
                if not np.isnan(correlation):
                    scores.append(max(0, correlation))
            
            # 5. Loop length preference (prefer common DJ loop lengths)
            length_beats = candidate["length_beats"]
            if length_beats in [8, 16, 32]:
                length_bonus = 1.0
            elif length_beats in [4, 64]:
                length_bonus = 0.8
            else:
                length_bonus = 0.6
            
            scores.append(length_bonus)
            
            # Calculate weighted average
            if scores:
                quality_score = np.mean(scores)
                return max(0.0, min(1.0, quality_score))
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"Error calculating loop quality: {e}")
            return 0.0
    
    def _select_best_loops(self, evaluated_loops: List[Dict[str, Any]], max_loops: int = 10) -> List[Dict[str, Any]]:
        """
        Select the best loops, avoiding overlaps.
        
        Args:
            evaluated_loops: List of evaluated loops
            max_loops: Maximum number of loops to return
            
        Returns:
            List of best non-overlapping loops
        """
        if not evaluated_loops:
            return []
        
        try:
            # Sort by quality score
            sorted_loops = sorted(evaluated_loops, key=lambda x: x["quality_score"], reverse=True)
            
            selected = []
            
            for loop in sorted_loops:
                if len(selected) >= max_loops:
                    break
                
                # Check for overlap with already selected loops
                overlaps = False
                for selected_loop in selected:
                    if self._loops_overlap(loop, selected_loop):
                        overlaps = True
                        break
                
                if not overlaps:
                    selected.append(loop)
            
            return selected
            
        except Exception as e:
            logger.warning(f"Error selecting best loops: {e}")
            return evaluated_loops[:max_loops]
    
    def _loops_overlap(self, loop1: Dict[str, Any], loop2: Dict[str, Any]) -> bool:
        """
        Check if two loops overlap in time.
        
        Args:
            loop1: First loop
            loop2: Second loop
            
        Returns:
            True if loops overlap
        """
        try:
            start1, end1 = loop1["start_time"], loop1["end_time"]
            start2, end2 = loop2["start_time"], loop2["end_time"]
            
            # Check for any overlap
            return not (end1 <= start2 or end2 <= start1)
            
        except Exception as e:
            logger.warning(f"Error checking loop overlap: {e}")
            return False
    
    def _create_loop_segments(self, best_loops: List[Dict[str, Any]], beat_times: np.ndarray) -> List[Dict[str, Any]]:
        """
        Create loop segments for visualization.
        
        Args:
            best_loops: List of best loops
            beat_times: Beat times in seconds
            
        Returns:
            List of loop segments
        """
        segments = []
        
        try:
            for i, loop in enumerate(best_loops):
                segment = {
                    "start": loop["start_time"],
                    "end": loop["end_time"],
                    "duration": loop["duration"],
                    "length_beats": loop["length_beats"],
                    "quality_score": loop["quality_score"],
                    "loop_index": i,
                    "type": "loop_segment",
                    "label": f"Loop {i+1} ({loop['length_beats']} beats)"
                }
                
                segments.append(segment)
            
            return segments
            
        except Exception as e:
            logger.warning(f"Error creating loop segments: {e}")
            return []
    
    def get_loop_recommendations(self, best_loops: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Get loop recommendations with DJ-specific information.
        
        Args:
            best_loops: List of best detected loops
            
        Returns:
            List of loop recommendations
        """
        recommendations = []
        
        try:
            for i, loop in enumerate(best_loops):
                recommendation = {
                    "loop_id": i,
                    "start_time": loop["start_time"],
                    "end_time": loop["end_time"],
                    "duration": loop["duration"],
                    "length_beats": loop["length_beats"],
                    "quality_score": loop["quality_score"],
                    "dj_rating": self._calculate_dj_rating(loop),
                    "use_case": self._suggest_use_case(loop),
                    "description": f"{loop['length_beats']}-beat loop with {loop['quality_score']:.1%} quality"
                }
                
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            logger.warning(f"Error creating loop recommendations: {e}")
            return []
    
    def _calculate_dj_rating(self, loop: Dict[str, Any]) -> str:
        """
        Calculate DJ-specific rating for a loop.
        
        Args:
            loop: Loop information
            
        Returns:
            DJ rating string
        """
        quality = loop["quality_score"]
        length = loop["length_beats"]
        
        # Prefer common DJ loop lengths
        if length in [8, 16, 32] and quality > 0.8:
            return "Excellent"
        elif length in [4, 8, 16, 32] and quality > 0.7:
            return "Good"
        elif quality > 0.6:
            return "Fair"
        else:
            return "Poor"
    
    def _suggest_use_case(self, loop: Dict[str, Any]) -> str:
        """
        Suggest use case for a loop based on its characteristics.
        
        Args:
            loop: Loop information
            
        Returns:
            Suggested use case
        """
        length = loop["length_beats"]
        quality = loop["quality_score"]
        
        if length == 4:
            return "Quick transitions, build-ups"
        elif length == 8:
            return "Standard mixing, phrase matching"
        elif length == 16:
            return "Extended mixing, creative loops"
        elif length == 32:
            return "Long blends, breakdown sections"
        else:
            return "Experimental use"
