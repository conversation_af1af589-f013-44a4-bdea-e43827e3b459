"""
Main beat grid service that orchestrates all beat grid functionality.
Integrates core extraction, enhanced analysis, quality control, and database operations.
"""

import os
import logging
import numpy as np
import librosa
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from .core_extraction import CoreBeatExtractor
from .enhanced_analysis import <PERSON>hancedBeatAnalyzer
from .segment_analysis import <PERSON>gmentAnaly<PERSON>
from .harmonic_analysis import HarmonicAnalyzer
from .loop_detection import LoopDetector
from .quality_control import QualityController
from .database_operations import BeatGridDatabaseManager

logger = logging.getLogger(__name__)

class BeatGridService:
    """Main beat grid service that orchestrates all functionality"""
    
    def __init__(self, db_session: Session):
        # Initialize all components
        self.core_extractor = CoreBeatExtractor()
        self.enhanced_analyzer = EnhancedBeatAnalyzer()
        self.segment_analyzer = SegmentAnalyzer()
        self.harmonic_analyzer = HarmonicAnalyzer()
        self.loop_detector = LoopDetector()
        self.quality_controller = QualityController()
        self.db_manager = BeatGridDatabaseManager(db_session)
        
        logger.info("BeatGridService initialized with all components")
    
    def extract_beat_grid(self, track_id: int, file_path: str, enhanced: bool = False) -> Dict[str, Any]:
        """
        Extract beat grid for a track with optional enhanced analysis.
        
        Args:
            track_id: Database ID of the track
            file_path: Path to the audio file
            enhanced: Whether to use enhanced analysis
            
        Returns:
            Complete beat grid analysis results
        """
        try:
            logger.info(f"Starting beat grid extraction for track {track_id} (enhanced={enhanced})")
            
            # Check if file exists
            if not os.path.exists(file_path):
                return {
                    "error": f"Audio file not found: {file_path}",
                    "track_id": track_id,
                    "success": False
                }
            
            # Load audio once for all analyses
            y, sr = librosa.load(file_path, sr=None)
            
            if enhanced:
                # Enhanced analysis pipeline
                result = self._extract_enhanced_beat_grid(y, sr, file_path)
            else:
                # Basic analysis pipeline
                result = self._extract_basic_beat_grid(file_path)
            
            # Add track information
            result["track_id"] = track_id
            result["file_path"] = file_path
            result["success"] = True
            
            # Apply quality control
            result = self._apply_quality_control(result, y, sr)
            
            # Save to database
            if result.get("success", False):
                saved = self.db_manager.save_beat_grid(track_id, result)
                result["saved_to_db"] = saved
            
            logger.info(f"Beat grid extraction completed for track {track_id} - "
                       f"Tempo: {result.get('tempo', 0):.1f} BPM, "
                       f"Confidence: {result.get('confidence', 0):.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting beat grid for track {track_id}: {e}")
            return {
                "error": str(e),
                "track_id": track_id,
                "success": False
            }
    
    def _extract_basic_beat_grid(self, file_path: str) -> Dict[str, Any]:
        """
        Extract basic beat grid using core extraction.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Basic beat grid result
        """
        try:
            # Use core extractor with fallback
            result = self.core_extractor.extract_with_fallback(file_path)
            
            # Validate result
            beat_times = np.array(result.get("beat_times", []))
            tempo = result.get("tempo", 120)
            confidence = result.get("confidence", 0)
            
            validation = self.core_extractor.validate_basic_beat_grid(beat_times, tempo)
            result["validation"] = validation
            
            return result
            
        except Exception as e:
            logger.error(f"Error in basic beat grid extraction: {e}")
            return {
                "error": str(e),
                "tempo": 120.0,
                "beat_times": [],
                "confidence": 0.0,
                "enhanced": False
            }
    
    def _extract_enhanced_beat_grid(self, y: np.ndarray, sr: int, file_path: str) -> Dict[str, Any]:
        """
        Extract enhanced beat grid with full analysis pipeline.
        
        Args:
            y: Audio time series
            sr: Sample rate
            file_path: Path to audio file
            
        Returns:
            Enhanced beat grid result
        """
        try:
            # Enhanced beat detection
            result = self.enhanced_analyzer.extract_enhanced_beat_grid(y, sr, file_path)
            
            beat_times = np.array(result.get("beat_times", []))
            beat_frames = librosa.time_to_frames(beat_times, sr=sr)
            
            # Segment analysis
            segment_analysis = self.segment_analyzer.analyze_segments(y, sr)
            result["segment_analysis"] = segment_analysis
            
            # Harmonic analysis
            harmonic_analysis = self.harmonic_analyzer.analyze_harmonic_structure(y, sr, beat_frames)
            result["harmonic_analysis"] = harmonic_analysis
            
            # Loop detection
            beat_chroma = harmonic_analysis.get("chroma_analysis", {}).get("chroma")
            if beat_chroma is not None:
                beat_chroma = np.array(beat_chroma)
            
            loop_analysis = self.loop_detector.detect_loop_points(y, sr, beat_times, beat_chroma)
            result["loop_analysis"] = loop_analysis
            
            # Enhanced quality metrics
            quality_metrics = self._calculate_enhanced_quality_metrics(result, y, sr)
            result["quality_metrics"] = quality_metrics
            
            return result
            
        except Exception as e:
            logger.error(f"Error in enhanced beat grid extraction: {e}")
            # Fallback to basic extraction
            return self._extract_basic_beat_grid(file_path)
    
    def _apply_quality_control(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Apply quality control and corrections to beat grid result.
        
        Args:
            result: Beat grid result
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Quality-controlled result
        """
        try:
            beat_times = np.array(result.get("beat_times", []))
            confidence = result.get("confidence", 0)
            tempo = result.get("tempo", 120)
            
            # Validate quality
            validation = self.quality_controller.validate_beat_grid_quality(beat_times, confidence, tempo)
            result["quality_validation"] = validation
            
            # Apply corrections if needed
            corrections_applied = []
            
            # Tempo doubling/halving correction
            original_tempo = tempo
            result = self.quality_controller.detect_and_correct_tempo_doubling(result, y, sr)
            if result.get("tempo_corrected", False):
                corrections_applied.append("tempo_correction")
            
            # Phase alignment correction
            result = self.quality_controller.correct_beat_phase_alignment(result, y, sr)
            if result.get("phase_corrected", False):
                corrections_applied.append("phase_alignment")
            
            # Beat refinement with onsets
            result = self.quality_controller.refine_beat_alignment_with_onsets(result, y, sr)
            if result.get("onset_refined", False):
                corrections_applied.append("onset_refinement")
            
            # Fix missing first beat
            result = self.quality_controller.fix_missing_first_beat(result, y, sr)
            if result.get("first_beat_added", False):
                corrections_applied.append("first_beat_correction")
            
            result["corrections_applied"] = corrections_applied
            
            # Recalculate confidence after corrections
            if corrections_applied:
                # Simple confidence boost for successful corrections
                original_confidence = result.get("confidence", 0)
                boost_factor = min(0.2, len(corrections_applied) * 0.05)
                result["confidence"] = min(1.0, original_confidence + boost_factor)
                result["original_confidence"] = original_confidence
            
            return result
            
        except Exception as e:
            logger.warning(f"Error in quality control: {e}")
            return result
    
    def _calculate_enhanced_quality_metrics(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Calculate enhanced quality metrics for the beat grid.
        
        Args:
            result: Beat grid result
            y: Audio time series
            sr: Sample rate
            
        Returns:
            Enhanced quality metrics
        """
        try:
            beat_times = np.array(result.get("beat_times", []))
            
            if len(beat_times) == 0:
                return {"overall_score": 0.0}
            
            metrics = {}
            
            # Basic metrics
            tempo = result.get("tempo", 120)
            confidence = result.get("confidence", 0)
            
            # Consistency metrics
            if len(beat_times) > 1:
                intervals = np.diff(beat_times)
                metrics["interval_consistency"] = 1.0 - (np.std(intervals) / (np.mean(intervals) + 1e-8))
                metrics["tempo_stability"] = float(np.std(intervals) < 0.1)
            else:
                metrics["interval_consistency"] = 0.0
                metrics["tempo_stability"] = 0.0
            
            # Onset alignment metrics
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onset_times = librosa.frames_to_time(
                librosa.onset.onset_detect(onset_envelope=onset_env, sr=sr),
                sr=sr
            )
            
            if len(onset_times) > 0:
                alignment_scores = []
                for beat_time in beat_times:
                    distances = np.abs(onset_times - beat_time)
                    min_distance = np.min(distances)
                    alignment_score = np.exp(-min_distance * 10)
                    alignment_scores.append(alignment_score)
                
                metrics["onset_alignment"] = float(np.mean(alignment_scores))
            else:
                metrics["onset_alignment"] = 0.5
            
            # Harmonic consistency (if available)
            harmonic_analysis = result.get("harmonic_analysis", {})
            if harmonic_analysis.get("has_harmonic_content", False):
                metrics["harmonic_consistency"] = 0.8  # Placeholder
            else:
                metrics["harmonic_consistency"] = 0.5
            
            # Segment consistency (if available)
            segment_analysis = result.get("segment_analysis", {})
            if segment_analysis.get("has_tempo_changes", False):
                metrics["segment_consistency"] = 0.6  # Lower for tempo changes
            else:
                metrics["segment_consistency"] = 0.9
            
            # Overall score
            weights = {
                "confidence": 0.3,
                "interval_consistency": 0.25,
                "onset_alignment": 0.25,
                "harmonic_consistency": 0.1,
                "segment_consistency": 0.1
            }
            
            overall_score = (
                weights["confidence"] * confidence +
                weights["interval_consistency"] * metrics["interval_consistency"] +
                weights["onset_alignment"] * metrics["onset_alignment"] +
                weights["harmonic_consistency"] * metrics["harmonic_consistency"] +
                weights["segment_consistency"] * metrics["segment_consistency"]
            )
            
            metrics["overall_score"] = max(0.0, min(1.0, overall_score))
            
            return metrics
            
        except Exception as e:
            logger.warning(f"Error calculating enhanced quality metrics: {e}")
            return {"overall_score": 0.5}
    
    def get_beat_grid(self, track_id: int) -> Optional[Dict[str, Any]]:
        """
        Get beat grid from database.
        
        Args:
            track_id: Database ID of the track
            
        Returns:
            Beat grid data or None if not found
        """
        return self.db_manager.get_beat_grid(track_id)
    
    def delete_beat_grid(self, track_id: int) -> bool:
        """
        Delete beat grid from database.
        
        Args:
            track_id: Database ID of the track
            
        Returns:
            True if successful
        """
        return self.db_manager.delete_beat_grid(track_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get beat grid statistics from database"""
        return self.db_manager.get_beat_grid_statistics()
    
    def cleanup_orphaned_beat_grids(self) -> int:
        """Clean up orphaned beat grids"""
        return self.db_manager.cleanup_orphaned_beat_grids()
    
    def get_tracks_needing_analysis(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get tracks that need beat grid analysis"""
        return self.db_manager.get_tracks_needing_beat_analysis(limit)
