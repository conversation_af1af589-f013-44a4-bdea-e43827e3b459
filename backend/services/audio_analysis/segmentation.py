"""
Segmentation and cue points module for audio analysis.
Contains advanced segmentation and cue point detection methods.
"""

import logging
import numpy as np
import librosa
from typing import Dict, Any, List

from .dependencies import SCIPY_AVAILABLE

logger = logging.getLogger(__name__)

if SCIPY_AVAILABLE:
    from scipy import signal, ndimage


class SegmentationAnalyzer:
    """Handles advanced segmentation and cue point detection."""
    
    def detect_advanced_segmentation(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Phase 5: Advanced segmentation and cue point detection
        """
        result = {
            "segments": [],
            "cue_points": [],
            "structure_analysis": {}
        }

        try:
            duration = len(y) / sr
            logger.info(f"🎵 Starting advanced segmentation for {duration:.1f}s track")

            # Enhanced segment detection
            segments = self._detect_enhanced_segments(y, sr)
            result["segments"] = segments

            # Enhanced cue point detection
            cue_points = self._detect_enhanced_cue_points(y, sr)
            result["cue_points"] = cue_points

            # Track structure analysis
            structure = self._analyze_track_structure_enhanced(y, sr, segments)
            result["structure_analysis"] = structure

            logger.info(f"✅ Advanced segmentation complete: {len(segments)} segments, {len(cue_points)} cue points")

        except Exception as e:
            logger.error(f"❌ Advanced segmentation failed: {e}")
            # Fallback to basic segmentation
            result["segments"] = self._create_fallback_segments(len(y) / sr)

        return result

    def _detect_enhanced_segments(self, y: np.ndarray, sr: int) -> List[Dict[str, Any]]:
        """
        Phase 5: Enhanced segment detection using multiple features
        """
        segments = []

        try:
            # Use librosa's segment detection
            # Compute chroma features for harmonic analysis
            chroma = librosa.feature.chroma_cqt(y=y, sr=sr)
            
            # Compute tempogram for rhythmic analysis
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            tempogram = librosa.feature.tempogram(onset_envelope=onset_env, sr=sr)
            
            # Combine features for segmentation
            features = np.vstack([chroma, tempogram])
            
            # Use recurrence matrix for structure detection
            R = librosa.segment.recurrence_matrix(features, mode='affinity')
            
            # Detect segment boundaries
            boundaries = librosa.segment.agglomerative(features, k=None)
            boundary_frames = librosa.util.fix_boundaries(boundaries, pad=False, fix_first=True, fix_last=True)
            
            # Convert to time
            boundary_times = librosa.frames_to_time(boundary_frames, sr=sr)
            
            # Create segment objects
            for i in range(len(boundary_times) - 1):
                start_time = float(boundary_times[i])
                end_time = float(boundary_times[i + 1])
                duration = end_time - start_time
                
                # Skip very short segments
                if duration < 5.0:
                    continue
                
                # Analyze segment characteristics
                start_frame = int(start_time * sr)
                end_frame = int(end_time * sr)
                segment_audio = y[start_frame:end_frame]
                
                # Calculate segment features
                rms = float(np.mean(librosa.feature.rms(y=segment_audio)[0]))
                spectral_centroid = float(np.mean(librosa.feature.spectral_centroid(y=segment_audio, sr=sr)[0]))
                
                segment = {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": duration,
                    "segment_type": self._classify_segment_type(i, len(boundary_times) - 1, rms, spectral_centroid),
                    "energy": rms,
                    "brightness": spectral_centroid,
                    "confidence": 0.8  # Default confidence
                }
                
                segments.append(segment)

        except Exception as e:
            logger.warning(f"Enhanced segment detection failed: {e}")
            # Fallback to simple time-based segmentation
            duration = len(y) / sr
            segments = self._create_fallback_segments(duration)

        return segments

    def _detect_enhanced_cue_points(self, y: np.ndarray, sr: int) -> List[Dict[str, Any]]:
        """
        Phase 5: Enhanced cue point detection with multiple methods
        """
        cue_points = []
        duration = len(y) / sr

        try:
            # Method 1: Onset-based cue points
            onset_cues = self._detect_onset_cue_points(y, sr, duration)
            cue_points.extend(onset_cues)

            # Method 2: Energy-based cue points
            energy_cues = self._detect_energy_cue_points(y, sr, duration)
            cue_points.extend(energy_cues)

            # Method 3: Harmonic change cue points
            harmonic_cues = self._detect_harmonic_cue_points(y, sr, duration)
            cue_points.extend(harmonic_cues)

            # Remove duplicates and sort
            cue_points = self._deduplicate_cue_points(cue_points)
            cue_points.sort(key=lambda x: x["time"])

        except Exception as e:
            logger.warning(f"Enhanced cue point detection failed: {e}")

        return cue_points

    def _detect_onset_cue_points(self, y: np.ndarray, sr: int, duration: float) -> List[Dict[str, Any]]:
        """
        Phase 5: Detect cue points based on onset strength
        """
        cue_points = []

        try:
            # Detect onsets
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onsets = librosa.onset.onset_detect(
                onset_envelope=onset_env, 
                sr=sr, 
                units='time',
                pre_max=0.03,
                post_max=0.03,
                pre_avg=0.1,
                post_avg=0.1,
                delta=0.07,
                wait=0.03
            )

            # Create cue points from strong onsets
            for onset_time in onsets:
                if 5.0 < onset_time < duration - 5.0:  # Avoid start/end
                    # Calculate onset strength at this point
                    frame = librosa.time_to_frames(onset_time, sr=sr)
                    if frame < len(onset_env):
                        strength = float(onset_env[frame])
                        
                        cue_point = {
                            "time": float(onset_time),
                            "type": "onset",
                            "strength": strength,
                            "confidence": min(1.0, strength / np.max(onset_env)),
                            "description": f"Strong onset at {onset_time:.1f}s"
                        }
                        cue_points.append(cue_point)

        except Exception as e:
            logger.warning(f"Onset cue point detection failed: {e}")

        return cue_points

    def _detect_energy_cue_points(self, y: np.ndarray, sr: int, duration: float) -> List[Dict[str, Any]]:
        """
        Phase 5: Detect cue points based on energy changes
        """
        cue_points = []

        try:
            # Calculate RMS energy
            rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
            times = librosa.frames_to_time(np.arange(len(rms)), sr=sr, hop_length=512)

            # Find significant energy changes
            rms_diff = np.diff(rms)
            
            # Find peaks in energy changes
            if SCIPY_AVAILABLE:
                peaks, properties = signal.find_peaks(
                    np.abs(rms_diff), 
                    height=np.std(rms_diff) * 2,
                    distance=int(sr / 512)  # Minimum 1 second apart
                )
            else:
                # Simple peak detection fallback
                threshold = np.std(rms_diff) * 2
                peaks = []
                for i in range(1, len(rms_diff) - 1):
                    if (abs(rms_diff[i]) > threshold and 
                        abs(rms_diff[i]) > abs(rms_diff[i-1]) and 
                        abs(rms_diff[i]) > abs(rms_diff[i+1])):
                        peaks.append(i)
                peaks = np.array(peaks)

            # Create cue points from energy changes
            for peak_idx in peaks:
                if peak_idx < len(times):
                    time = float(times[peak_idx])
                    if 5.0 < time < duration - 5.0:  # Avoid start/end
                        energy_change = float(abs(rms_diff[peak_idx]))
                        
                        cue_point = {
                            "time": time,
                            "type": "energy_change",
                            "strength": energy_change,
                            "confidence": min(1.0, energy_change / np.max(np.abs(rms_diff))),
                            "description": f"Energy change at {time:.1f}s"
                        }
                        cue_points.append(cue_point)

        except Exception as e:
            logger.warning(f"Energy cue point detection failed: {e}")

        return cue_points

    def _detect_harmonic_cue_points(self, y: np.ndarray, sr: int, duration: float) -> List[Dict[str, Any]]:
        """
        Phase 5: Detect cue points based on harmonic changes
        """
        cue_points = []

        try:
            # Chroma features for harmonic analysis
            chroma = librosa.feature.chroma_cqt(y=y, sr=sr)

            # Find harmonic changes
            chroma_diff = np.sum(np.abs(np.diff(chroma, axis=1)), axis=0)
            harmonic_peaks = librosa.util.peak_pick(
                chroma_diff,
                pre_max=15,
                post_max=15,
                pre_avg=30,
                post_avg=30,
                delta=0.2,
                wait=30
            )

            # Convert to times
            harmonic_times = librosa.frames_to_time(harmonic_peaks, sr=sr)

            for i, time in enumerate(harmonic_times):
                if time < 5.0 or time > duration - 5.0:
                    continue

                # Calculate harmonic change strength
                frame = librosa.time_to_frames(time, sr=sr)
                if frame < len(chroma_diff):
                    strength = float(chroma_diff[frame])

                    cue_point = {
                        "time": float(time),
                        "type": "harmonic_change",
                        "strength": strength,
                        "confidence": min(1.0, strength / np.max(chroma_diff)),
                        "description": f"Harmonic change at {time:.1f}s"
                    }
                    cue_points.append(cue_point)

        except Exception as e:
            logger.warning(f"Harmonic cue point detection failed: {e}")

        return cue_points

    def _deduplicate_cue_points(self, cue_points: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Phase 5: Remove duplicate cue points that are too close together
        """
        if not cue_points:
            return cue_points

        # Sort by time
        cue_points.sort(key=lambda x: x["time"])

        # Remove duplicates within 3 seconds
        deduplicated = [cue_points[0]]

        for cue in cue_points[1:]:
            if cue["time"] - deduplicated[-1]["time"] > 3.0:
                deduplicated.append(cue)

        return deduplicated

    def _create_fallback_segments(self, duration: float) -> List[Dict[str, Any]]:
        """
        Phase 5: Create fallback segments if detection fails
        """
        return [
            {
                "start_time": 0.0,
                "end_time": duration * 0.2,
                "duration": duration * 0.2,
                "segment_type": "intro",
                "energy": 0.5,
                "brightness": 1000.0,
                "confidence": 0.6
            },
            {
                "start_time": duration * 0.2,
                "end_time": duration * 0.8,
                "duration": duration * 0.6,
                "segment_type": "main",
                "energy": 0.8,
                "brightness": 2000.0,
                "confidence": 0.6
            },
            {
                "start_time": duration * 0.8,
                "end_time": duration,
                "duration": duration * 0.2,
                "segment_type": "outro",
                "energy": 0.4,
                "brightness": 1500.0,
                "confidence": 0.6
            }
        ]

    def _analyze_track_structure_enhanced(self, y: np.ndarray, sr: int, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Phase 5: Enhanced track structure analysis
        """
        structure = {
            "total_segments": len(segments),
            "intro_duration": 0.0,
            "outro_duration": 0.0,
            "main_duration": 0.0,
            "structure_type": "unknown"
        }

        try:
            duration = len(y) / sr

            # Analyze segment durations
            for segment in segments:
                seg_duration = segment["duration"]
                seg_type = segment["segment_type"]

                if seg_type == "intro":
                    structure["intro_duration"] += seg_duration
                elif seg_type == "outro":
                    structure["outro_duration"] += seg_duration
                else:
                    structure["main_duration"] += seg_duration

            # Determine structure type
            intro_ratio = structure["intro_duration"] / duration
            outro_ratio = structure["outro_duration"] / duration

            if intro_ratio > 0.15 and outro_ratio > 0.15:
                structure["structure_type"] = "full_structure"
            elif intro_ratio > 0.1:
                structure["structure_type"] = "intro_main"
            elif outro_ratio > 0.1:
                structure["structure_type"] = "main_outro"
            else:
                structure["structure_type"] = "continuous"

        except Exception as e:
            logger.warning(f"Track structure analysis failed: {e}")

        return structure

    def _classify_segment_type(self, index: int, total_segments: int, energy: float, brightness: float) -> str:
        """Classify segment type based on position and characteristics"""
        # Simple heuristic based on position
        if index == 0:
            return "intro"
        elif index == total_segments - 1:
            return "outro"
        elif energy > 0.7:
            return "chorus"
        elif energy < 0.4:
            return "breakdown"
        else:
            return "verse"
