"""
Energy and mood analysis module for audio analysis.
Contains energy profile, mood analysis, and perceptual features.
"""

import logging
import numpy as np
import librosa
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class EnergyMoodAnalyzer:
    """Handles energy profile, mood analysis, and perceptual features."""
    
    def analyze_energy_profile(self, y: np.n<PERSON><PERSON>, sr: int) -> Dict[str, Any]:
        """
        Extract multi-dimensional energy profile using enhanced librosa techniques
        """
        profile = {
            "arousal": 0.0,
            "valence": 0.0,
            "dominance": 0.0,
            "energy_legacy": 5,
            "danceability": 0.0,
            "texture": {},
            "mood_tags": [],
            "confidence": 0.0
        }

        try:
            logger.info("🎵 Starting enhanced energy profile analysis")

            # Calculate arousal (activation/energy level)
            arousal = self._calculate_arousal_enhanced(y, sr)
            profile["arousal"] = arousal

            # Calculate valence (positive/negative emotion)
            valence = self._calculate_valence_enhanced(y, sr)
            profile["valence"] = valence

            # Calculate dominance (submissive/aggressive)
            dominance = self._calculate_dominance(y, sr, arousal, valence)
            profile["dominance"] = dominance

            # Enhanced texture analysis
            texture = self._analyze_texture_advanced(y, sr)
            profile["texture"] = texture

            # Enhanced danceability
            danceability = self._calculate_danceability_enhanced(y, sr)
            profile["danceability"] = danceability

            # Generate mood tags
            mood_tags = self._generate_mood_tags(arousal, valence, dominance, texture)
            profile["mood_tags"] = mood_tags

            # Map to legacy energy scale for backward compatibility
            legacy_energy = self._map_to_legacy_energy(arousal, valence)
            profile["energy_legacy"] = legacy_energy

            # Calculate overall confidence
            confidence = self._calculate_profile_confidence(y, sr, arousal, valence, dominance)
            profile["confidence"] = confidence

            logger.info(f"✅ Energy profile complete: Arousal={arousal:.2f}, Valence={valence:.2f}, Energy={legacy_energy}")

        except Exception as e:
            logger.error(f"❌ Energy profile analysis failed: {e}")
            # Fallback to basic energy calculation
            try:
                rms = np.mean(librosa.feature.rms(y=y)[0])
                profile["energy_legacy"] = int(np.clip(rms * 10, 1, 10))
                profile["arousal"] = rms
                profile["valence"] = 0.5
                profile["confidence"] = 0.3
            except:
                pass

        return profile

    def analyze_energy_levels(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Analyze energy levels throughout the track
        """
        result = {
            "average_energy": 0.0,
            "peak_energy": 0.0,
            "energy_variance": 0.0,
            "energy_curve": [],
            "energy_segments": []
        }

        try:
            # Calculate RMS energy over time
            rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
            times = librosa.frames_to_time(np.arange(len(rms)), sr=sr, hop_length=512)

            # Basic statistics
            result["average_energy"] = float(np.mean(rms))
            result["peak_energy"] = float(np.max(rms))
            result["energy_variance"] = float(np.var(rms))

            # Energy curve (downsampled for efficiency)
            downsample_factor = max(1, len(rms) // 100)  # Max 100 points
            result["energy_curve"] = [
                {"time": float(times[i]), "energy": float(rms[i])}
                for i in range(0, len(rms), downsample_factor)
            ]

            # Identify energy segments
            energy_threshold = np.mean(rms)
            segments = []
            current_segment = None

            for i, energy in enumerate(rms):
                time = times[i]
                is_high_energy = energy > energy_threshold

                if current_segment is None:
                    current_segment = {
                        "start_time": float(time),
                        "high_energy": is_high_energy,
                        "peak_energy": float(energy)
                    }
                elif current_segment["high_energy"] != is_high_energy:
                    # End current segment
                    current_segment["end_time"] = float(time)
                    current_segment["duration"] = current_segment["end_time"] - current_segment["start_time"]
                    segments.append(current_segment)

                    # Start new segment
                    current_segment = {
                        "start_time": float(time),
                        "high_energy": is_high_energy,
                        "peak_energy": float(energy)
                    }
                else:
                    # Update peak energy for current segment
                    current_segment["peak_energy"] = max(current_segment["peak_energy"], float(energy))

            # Close final segment
            if current_segment is not None:
                current_segment["end_time"] = float(times[-1])
                current_segment["duration"] = current_segment["end_time"] - current_segment["start_time"]
                segments.append(current_segment)

            result["energy_segments"] = segments

        except Exception as e:
            logger.warning(f"Energy level analysis failed: {e}")

        return result

    def calculate_danceability(self, y: np.ndarray, sr: int) -> float:
        """
        Calculate danceability score based on rhythm regularity and energy
        """
        try:
            # Tempo stability
            tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr)
            
            if len(beat_frames) < 4:
                return 0.3  # Low danceability for tracks without clear beats

            # Beat regularity
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)
            beat_intervals = np.diff(beat_times)
            
            if len(beat_intervals) > 0:
                beat_regularity = 1.0 - (np.std(beat_intervals) / np.mean(beat_intervals))
                beat_regularity = max(0.0, min(1.0, beat_regularity))
            else:
                beat_regularity = 0.0

            # Energy consistency
            rms = librosa.feature.rms(y=y)[0]
            energy_consistency = 1.0 - (np.std(rms) / (np.mean(rms) + 1e-8))
            energy_consistency = max(0.0, min(1.0, energy_consistency))

            # Tempo range (optimal for dancing)
            tempo_score = 0.0
            if 120 <= tempo <= 140:
                tempo_score = 1.0
            elif 100 <= tempo <= 160:
                tempo_score = 0.7
            elif 80 <= tempo <= 180:
                tempo_score = 0.4
            else:
                tempo_score = 0.1

            # Combine factors
            danceability = (beat_regularity * 0.4 + 
                          energy_consistency * 0.3 + 
                          tempo_score * 0.3)

            return float(np.clip(danceability, 0.0, 1.0))

        except Exception as e:
            logger.warning(f"Danceability calculation failed: {e}")
            return 0.5

    def analyze_musical_characteristics(self, y: np.ndarray, sr: int) -> Dict[str, float]:
        """Analyze acousticness, instrumentalness, speechiness"""
        characteristics = {
            "acousticness": 0.5,
            "instrumentalness": 0.5,
            "speechiness": 0.1
        }

        try:
            # Harmonic vs percussive content
            y_harmonic, y_percussive = librosa.effects.hpss(y)
            
            harmonic_energy = np.mean(librosa.feature.rms(y=y_harmonic)[0])
            percussive_energy = np.mean(librosa.feature.rms(y=y_percussive)[0])
            total_energy = harmonic_energy + percussive_energy

            if total_energy > 0:
                # Acousticness: higher harmonic content suggests more acoustic
                characteristics["acousticness"] = float(harmonic_energy / total_energy)
                
                # Instrumentalness: lower spectral centroid suggests more instrumental
                spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=y, sr=sr))
                characteristics["instrumentalness"] = float(1.0 - min(1.0, spectral_centroid / 4000))

            # Speechiness: based on spectral features that correlate with speech
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            
            # Speech typically has specific MFCC patterns
            # This is a simplified heuristic
            mfcc_variance = np.var(mfccs[1:4], axis=1)  # Focus on 2nd-4th MFCCs
            speech_indicator = np.mean(mfcc_variance)
            characteristics["speechiness"] = float(min(1.0, speech_indicator / 100))

        except Exception as e:
            logger.warning(f"Musical characteristics analysis failed: {e}")

        return characteristics

    def _calculate_arousal_enhanced(self, y: np.ndarray, sr: int) -> float:
        """Enhanced arousal calculation using advanced librosa techniques"""

        # 1. Energy features with variance analysis
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        rms_mean = np.mean(rms)
        rms_variance = np.var(rms)  # Energy variation indicates excitement

        # 2. Multi-spectral analysis
        centroid = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        centroid_mean = np.mean(centroid)

        rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)[0]
        rolloff_mean = np.mean(rolloff)

        # 3. Enhanced rhythm analysis
        onset_env = librosa.onset.onset_strength(y=y, sr=sr)
        onset_rate = len(librosa.onset.onset_detect(onset_envelope=onset_env, sr=sr)) / (len(y) / sr)

        # 4. Multi-method tempo ensemble
        tempo_ensemble = self._get_tempo_ensemble(y, sr)

        # 5. Dynamic range analysis
        dynamic_range = np.max(rms) - np.min(rms)

        # Weighted combination with enhanced features
        arousal = (
            0.25 * self._normalize_feature(rms_mean, 0, 0.5) +
            0.20 * self._normalize_feature(centroid_mean, 1000, 4000) +
            0.15 * self._normalize_feature(rolloff_mean, 2000, 8000) +
            0.15 * self._normalize_feature(onset_rate, 0, 10) +
            0.10 * self._normalize_feature(tempo_ensemble, 60, 180) +
            0.10 * self._normalize_feature(dynamic_range, 0, 0.3) +
            0.05 * self._normalize_feature(rms_variance, 0, 0.1)
        )

        return float(np.clip(arousal * 2 - 1, -1, 1))  # Scale to -1 to +1

    def _calculate_valence_enhanced(self, y: np.ndarray, sr: int) -> float:
        """Enhanced valence calculation using advanced harmonic analysis"""

        # 1. Advanced chroma features for major/minor detection
        chroma = librosa.feature.chroma_stft(y=y, sr=sr)

        # Major/minor templates for better detection
        major_template = np.array([1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1])
        minor_template = np.array([1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0])

        # Calculate correlation with templates
        chroma_mean = np.mean(chroma, axis=1)
        major_corr = np.corrcoef(chroma_mean, major_template)[0, 1] if not np.isnan(np.corrcoef(chroma_mean, major_template)[0, 1]) else 0
        minor_corr = np.corrcoef(chroma_mean, minor_template)[0, 1] if not np.isnan(np.corrcoef(chroma_mean, minor_template)[0, 1]) else 0
        mode_score = major_corr - minor_corr  # Positive = major, negative = minor

        # 2. Harmonic-percussive separation analysis
        y_harmonic, y_percussive = librosa.effects.hpss(y)
        harmonic_ratio = np.mean(np.abs(y_harmonic)) / (np.mean(np.abs(y)) + 1e-8)

        # 3. Spectral contrast (consonance indicator)
        contrast = librosa.feature.spectral_contrast(y=y, sr=sr)
        contrast_mean = np.mean(contrast)

        # 4. Tonal centroid features (advanced harmonic analysis)
        try:
            tonnetz = librosa.feature.tonnetz(y=librosa.effects.harmonic(y), sr=sr)
            tonnetz_var = np.var(tonnetz)  # Harmonic stability
        except:
            tonnetz_var = 0.5  # Default if tonnetz fails

        # 5. Dissonance analysis using spectral features
        bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr)[0]
        bandwidth_mean = np.mean(bandwidth)

        # Enhanced combination
        valence = (
            0.40 * self._normalize_feature(mode_score, -1, 1) +
            0.25 * self._normalize_feature(harmonic_ratio, 0, 1) +
            0.20 * self._normalize_feature(contrast_mean, 10, 30) +
            0.10 * (1 - self._normalize_feature(tonnetz_var, 0, 1)) +  # Less variation = more positive
            0.05 * (1 - self._normalize_feature(bandwidth_mean, 1000, 4000))  # Narrower = more consonant
        )

        return float(np.clip(valence * 2 - 1, -1, 1))

    def _get_tempo_ensemble(self, y: np.ndarray, sr: int) -> float:
        """Multi-method tempo detection ensemble for higher accuracy"""
        try:
            tempos = []

            # Method 1: Standard beat tracking
            try:
                tempo1, _ = librosa.beat.beat_track(y=y, sr=sr)
                tempos.append(float(tempo1))
            except:
                pass

            # Method 2: Onset-based tempo
            try:
                onset_env = librosa.onset.onset_strength(y=y, sr=sr)
                tempo2 = librosa.feature.rhythm.tempo(onset_envelope=onset_env, sr=sr)[0]
                tempos.append(float(tempo2))
            except:
                pass

            # Method 3: Autocorrelation-based
            try:
                onset_env = librosa.onset.onset_strength(y=y, sr=sr)
                ac = librosa.autocorrelate(onset_env, max_size=2 * sr // 512)
                tempo3 = 60 * sr / (512 * (np.argmax(ac[sr//512:]) + sr//512))
                if 60 <= tempo3 <= 200:  # Reasonable tempo range
                    tempos.append(float(tempo3))
            except:
                pass

            if tempos:
                # Use median for robustness
                return float(np.median(tempos))
            else:
                return 120.0  # Default fallback

        except Exception as e:
            logger.warning(f"Tempo ensemble failed: {e}")
            return 120.0

    def _normalize_feature(self, value: float, min_val: float, max_val: float) -> float:
        """Normalize a feature value to 0-1 range"""
        if max_val == min_val:
            return 0.5
        return np.clip((value - min_val) / (max_val - min_val), 0, 1)

    def _analyze_texture_advanced(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """Advanced texture analysis using comprehensive librosa features"""

        texture = {
            "roughness": 0.0,
            "brightness": 0.0,
            "warmth": 0.0,
            "clarity": 0.0,
            "fullness": 0.0
        }

        try:
            # Roughness: spectral irregularity and dissonance
            bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr)[0]
            roughness = self._normalize_feature(np.mean(bandwidth), 500, 3000)
            texture["roughness"] = float(roughness)

            # Brightness: high-frequency content
            centroid = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            brightness = self._normalize_feature(np.mean(centroid), 1000, 4000)
            texture["brightness"] = float(brightness)

            # Warmth: low-frequency emphasis
            rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr, roll_percent=0.25)[0]
            warmth = 1.0 - self._normalize_feature(np.mean(rolloff), 500, 2000)
            texture["warmth"] = float(warmth)

            # Clarity: spectral contrast and definition
            contrast = librosa.feature.spectral_contrast(y=y, sr=sr)
            clarity = self._normalize_feature(np.mean(contrast), 15, 35)
            texture["clarity"] = float(clarity)

            # Fullness: spectral spread and energy distribution
            rms = librosa.feature.rms(y=y)[0]
            fullness = self._normalize_feature(np.mean(rms), 0.01, 0.3)
            texture["fullness"] = float(fullness)

        except Exception as e:
            logger.warning(f"Texture analysis failed: {e}")

        return texture

    def _calculate_danceability_enhanced(self, y: np.ndarray, sr: int) -> float:
        """Enhanced danceability calculation with rhythm complexity"""

        # Use existing danceability calculation as base
        base_danceability = self.calculate_danceability(y, sr)

        # Enhance with rhythm analysis
        try:
            # Beat strength analysis
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            beat_strength = np.mean(onset_env)

            # Rhythm regularity
            onsets = librosa.onset.onset_detect(onset_envelope=onset_env, sr=sr)
            if len(onsets) > 1:
                onset_intervals = np.diff(librosa.frames_to_time(onsets, sr=sr))
                rhythm_regularity = 1.0 - (np.std(onset_intervals) / (np.mean(onset_intervals) + 1e-8))
            else:
                rhythm_regularity = 0.5

            # Combine factors
            enhanced_danceability = (
                0.6 * base_danceability +
                0.25 * self._normalize_feature(beat_strength, 0, 2) +
                0.15 * rhythm_regularity
            )

            return float(np.clip(enhanced_danceability, 0, 1))

        except Exception as e:
            logger.warning(f"Enhanced danceability calculation failed: {e}")
            return base_danceability

    def _calculate_dominance(self, y: np.ndarray, sr: int, arousal: float, valence: float) -> float:
        """Calculate dominance (submissive/aggressive) for 3D emotion model"""

        # 1. Dynamic range analysis
        rms = librosa.feature.rms(y=y)[0]
        dynamic_range = np.max(rms) - np.min(rms)

        # 2. Spectral contrast (punchiness/definition)
        contrast = librosa.feature.spectral_contrast(y=y, sr=sr)
        contrast_mean = np.mean(contrast)

        # 3. Attack characteristics
        onset_env = librosa.onset.onset_strength(y=y, sr=sr)
        onset_strength = np.mean(onset_env)

        # 4. Harmonic-percussive ratio (percussive = more dominant)
        y_harmonic, y_percussive = librosa.effects.hpss(y)
        percussive_ratio = np.mean(np.abs(y_percussive)) / (np.mean(np.abs(y)) + 1e-8)

        # Combine features for dominance
        dominance = (
            0.3 * self._normalize_feature(dynamic_range, 0, 0.4) +
            0.3 * self._normalize_feature(contrast_mean, 15, 35) +
            0.2 * self._normalize_feature(onset_strength, 0, 2) +
            0.2 * self._normalize_feature(percussive_ratio, 0, 1)
        )

        return float(np.clip(dominance * 2 - 1, -1, 1))

    def _generate_mood_tags(self, arousal: float, valence: float, dominance: float, texture: Dict) -> List[str]:
        """Generate descriptive mood tags from multi-dimensional analysis"""
        tags = []

        # Arousal-based tags
        if arousal > 0.5:
            tags.extend(["energetic", "exciting", "intense"])
        elif arousal > 0:
            tags.extend(["moderate", "balanced"])
        else:
            tags.extend(["calm", "relaxed", "peaceful"])

        # Valence-based tags
        if valence > 0.5:
            tags.extend(["positive", "uplifting", "happy"])
        elif valence > 0:
            tags.extend(["neutral", "contemplative"])
        else:
            tags.extend(["melancholic", "dark", "moody"])

        # Dominance-based tags
        if dominance > 0.5:
            tags.extend(["powerful", "aggressive", "driving"])
        elif dominance > 0:
            tags.extend(["assertive", "confident"])
        else:
            tags.extend(["gentle", "subtle", "soft"])

        # Texture-based tags
        if texture.get("brightness", 0) > 0.7:
            tags.append("bright")
        if texture.get("warmth", 0) > 0.7:
            tags.append("warm")
        if texture.get("roughness", 0) > 0.7:
            tags.append("rough")

        return tags[:8]  # Limit to 8 most relevant tags

    def _map_to_legacy_energy(self, arousal: float, valence: float) -> int:
        """Map arousal-valence to legacy 1-10 energy scale for backward compatibility"""

        # Convert arousal (-1 to +1) to energy scale (1 to 10)
        # Higher arousal = higher energy
        base_energy = (arousal + 1) * 4.5 + 1  # Maps -1->1, +1->10

        # Adjust based on valence (positive valence slightly increases perceived energy)
        valence_adjustment = valence * 0.5  # Small adjustment

        legacy_energy = base_energy + valence_adjustment

        return int(np.clip(legacy_energy, 1, 10))

    def _calculate_profile_confidence(self, y: np.ndarray, sr: int, arousal: float, valence: float, dominance: float) -> float:
        """Calculate overall confidence in the energy profile analysis"""

        # Factors that affect confidence
        confidence_factors = []

        # 1. Audio quality (signal-to-noise ratio approximation)
        rms = librosa.feature.rms(y=y)[0]
        snr_approx = np.mean(rms) / (np.std(rms) + 1e-8)
        confidence_factors.append(min(1.0, snr_approx / 10))

        # 2. Feature consistency (how well features agree)
        feature_consistency = 1.0 - abs(arousal - valence) / 2  # Similar values = more consistent
        confidence_factors.append(feature_consistency)

        # 3. Audio length (longer audio = more reliable)
        duration = len(y) / sr
        duration_factor = min(1.0, duration / 30)  # Full confidence at 30+ seconds
        confidence_factors.append(duration_factor)

        # 4. Spectral stability
        centroid = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        spectral_stability = 1.0 - (np.std(centroid) / (np.mean(centroid) + 1e-8))
        confidence_factors.append(max(0.0, spectral_stability))

        # Overall confidence as weighted average
        overall_confidence = np.mean(confidence_factors)

        return float(np.clip(overall_confidence, 0.3, 1.0))  # Minimum 30% confidence
