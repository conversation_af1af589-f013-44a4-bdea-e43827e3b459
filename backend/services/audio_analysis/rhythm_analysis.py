"""
Rhythm analysis module for audio analysis.
Contains rhythm complexity, polyrhythm, and time signature detection methods.
"""

import logging
import numpy as np
import librosa
from typing import Dict, Any, List

from .dependencies import SCIPY_AVAILABLE

logger = logging.getLogger(__name__)

if SCIPY_AVAILABLE:
    from scipy import signal, ndimage
    from scipy.stats import mode


class RhythmAnalyzer:
    """Handles rhythm analysis including complexity, polyrhythms, and time signatures."""
    
    def detect_basic_rhythm_complexity(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Simplified rhythm analysis for DJ mixing - only essential complexity metrics
        Much faster than full complex rhythm detection
        """
        logger.info("🎵 Starting Basic Rhythm Detection")

        result = {
            "rhythm_complexity": 0.0,
            "rhythm_regularity": 0.0
        }

        try:
            # Basic rhythm complexity calculation (essential for DJ mixing)
            complexity = self._calculate_rhythm_complexity_simple(y, sr)
            result["rhythm_complexity"] = complexity

            # Basic rhythm regularity (useful for transition planning)
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            regularity = self._calculate_rhythm_regularity(onset_env)
            result["rhythm_regularity"] = regularity

            logger.info(f"✅ Basic rhythm analysis complete - Complexity: {complexity:.2f}")

        except Exception as e:
            logger.error(f"❌ Basic rhythm detection failed: {e}")

        return result

    def detect_complex_rhythms(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Phase 2: Advanced rhythm detection for complex patterns
        Handles polyrhythms, breakbeats, and irregular time signatures
        """
        logger.info("🎵 Starting Phase 2 Advanced Rhythm Detection")

        result = {
            "polyrhythm_detected": False,
            "rhythm_complexity": 0.0,
            "time_signature_confidence": 0.0,
            "breakbeat_probability": 0.0,
            "rhythm_patterns": []
        }

        try:
            # Multi-scale rhythm analysis
            rhythm_scales = self._analyze_rhythm_scales(y, sr)
            result["rhythm_scales"] = rhythm_scales

            # Detect polyrhythms
            polyrhythm_result = self._detect_polyrhythms(y, sr)
            result.update(polyrhythm_result)

            # Analyze rhythm complexity
            complexity = self._calculate_rhythm_complexity(y, sr)
            result["rhythm_complexity"] = complexity

            # Detect breakbeat patterns
            breakbeat_prob = self._detect_breakbeat_patterns(y, sr)
            result["breakbeat_probability"] = breakbeat_prob

            # Time signature detection
            time_sig_result = self._detect_time_signature(y, sr)
            result.update(time_sig_result)

            logger.info(f"✅ Advanced rhythm analysis complete - Complexity: {complexity:.2f}")

        except Exception as e:
            logger.error(f"❌ Advanced rhythm detection failed: {e}")

        return result

    def detect_time_signature(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Phase 2: Detect time signature (4/4, 3/4, 6/8, etc.)
        """
        result = {
            "time_signature": "4/4",
            "confidence": 0.0,
            "beat_pattern": []
        }

        try:
            # Get beat tracking
            tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr)
            
            if len(beat_frames) < 8:  # Need enough beats for analysis
                return result

            # Convert beat frames to time
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)
            beat_intervals = np.diff(beat_times)

            # Analyze beat patterns
            if len(beat_intervals) > 0:
                # Check for different time signatures
                signatures = {
                    "4/4": self._check_quadruple_meter(beat_intervals),
                    "3/4": self._check_triple_meter(beat_intervals),
                    "6/8": self._check_compound_meter(beat_intervals)
                }

                # Find most likely time signature
                best_sig = max(signatures.items(), key=lambda x: x[1])
                result["time_signature"] = best_sig[0]
                result["confidence"] = best_sig[1]

            logger.info(f"Time signature: {result['time_signature']} (confidence: {result['confidence']:.2f})")

        except Exception as e:
            logger.warning(f"Time signature detection failed: {e}")

        return result

    def _calculate_rhythm_complexity_simple(self, y: np.ndarray, sr: int) -> float:
        """
        Simplified rhythm complexity calculation - only essential factors for DJ mixing
        """
        try:
            complexity_factors = []

            # Factor 1: Onset density (most important for DJs)
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onset_density = len(librosa.util.peak_pick(onset_env, pre_max=3, post_max=3, pre_avg=3, post_avg=5, delta=0.1, wait=10)) / (len(y) / sr)
            complexity_factors.append(min(1.0, onset_density / 10.0))  # Normalize to 0-1

            # Factor 2: Rhythm regularity (inverse)
            regularity = self._calculate_rhythm_regularity(onset_env)
            complexity_factors.append(1.0 - regularity)

            # Simple average (faster than weighted)
            complexity = np.mean(complexity_factors)

            return float(complexity)

        except Exception as e:
            logger.warning(f"⚠️ Simple rhythm complexity calculation failed: {e}")
            return 0.5

    def _analyze_rhythm_scales(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Phase 2: Multi-scale rhythm analysis using different time windows
        """
        scales = {}

        try:
            # Analyze at different time scales
            time_scales = [0.5, 1.0, 2.0, 4.0]  # seconds

            for scale in time_scales:
                hop_length = int(scale * sr / 4)  # 4 hops per scale

                # Onset detection at this scale
                onset_env = librosa.onset.onset_strength(
                    y=y, sr=sr, hop_length=hop_length
                )

                # Tempo at this scale
                try:
                    tempo = librosa.feature.rhythm.tempo(onset_envelope=onset_env, sr=sr)[0]
                except AttributeError:
                    # Fallback for older librosa versions
                    tempo = librosa.beat.tempo(onset_envelope=onset_env, sr=sr)[0]

                # Rhythm strength at this scale
                rhythm_strength = np.mean(onset_env)

                scales[f"scale_{scale}s"] = {
                    "tempo": float(tempo),
                    "rhythm_strength": float(rhythm_strength),
                    "onset_density": len(librosa.util.peak_pick(onset_env)) / (len(y) / sr)
                }

        except Exception as e:
            logger.warning(f"Multi-scale rhythm analysis failed: {e}")

        return scales

    def _detect_polyrhythms(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Phase 2: Detect polyrhythmic patterns (multiple simultaneous rhythms)
        """
        result = {
            "polyrhythm_detected": False,
            "polyrhythm_confidence": 0.0,
            "rhythm_layers": []
        }

        try:
            # Analyze different frequency bands for separate rhythmic layers
            frequency_bands = [
                (20, 200),    # Bass/kick
                (200, 2000),  # Mid/snare
                (2000, 8000), # High/hi-hat
                (8000, 20000) # Very high/cymbals
            ]

            rhythm_layers = []

            for i, (low_freq, high_freq) in enumerate(frequency_bands):
                try:
                    # Filter to frequency band
                    y_filtered = self._filter_frequency_band(y, sr, low_freq, high_freq)

                    # Detect rhythm in this band
                    onset_env = librosa.onset.onset_strength(y=y_filtered, sr=sr)

                    # Get tempo for this layer
                    try:
                        tempo = librosa.feature.rhythm.tempo(onset_envelope=onset_env, sr=sr)[0]
                    except AttributeError:
                        tempo = librosa.beat.tempo(onset_envelope=onset_env, sr=sr)[0]

                    rhythm_layers.append({
                        "band": f"{low_freq}-{high_freq}Hz",
                        "tempo": float(tempo),
                        "strength": float(np.mean(onset_env)),
                        "regularity": self._calculate_rhythm_regularity(onset_env)
                    })

                except Exception as band_e:
                    logger.warning(f"Polyrhythm analysis failed for band {low_freq}-{high_freq}Hz: {band_e}")

            # Check for polyrhythm indicators
            if len(rhythm_layers) >= 2:
                tempos = [layer["tempo"] for layer in rhythm_layers]
                tempo_variance = np.var(tempos)

                # High tempo variance suggests polyrhythm
                if tempo_variance > 100:  # Threshold for polyrhythm detection
                    result["polyrhythm_detected"] = True
                    result["polyrhythm_confidence"] = min(1.0, tempo_variance / 500)

            result["rhythm_layers"] = rhythm_layers

        except Exception as e:
            logger.warning(f"Polyrhythm detection failed: {e}")

        return result

    def _filter_frequency_band(self, y: np.ndarray, sr: int, low_freq: float, high_freq: float) -> np.ndarray:
        """
        Phase 2: Filter audio to specific frequency band
        """
        try:
            if SCIPY_AVAILABLE:
                # Use scipy for better filtering
                nyquist = sr / 2
                low = low_freq / nyquist
                high = high_freq / nyquist

                # Ensure frequencies are in valid range
                low = max(0.01, min(low, 0.99))
                high = max(low + 0.01, min(high, 0.99))

                b, a = signal.butter(4, [low, high], btype='band')
                y_filtered = signal.filtfilt(b, a, y)
                return y_filtered
            else:
                # Simple fallback using librosa
                stft = librosa.stft(y)
                freqs = librosa.fft_frequencies(sr=sr)

                # Create frequency mask
                mask = (freqs >= low_freq) & (freqs <= high_freq)
                stft_filtered = stft.copy()
                stft_filtered[~mask] = 0

                y_filtered = librosa.istft(stft_filtered)
                return y_filtered

        except Exception as e:
            logger.warning(f"Frequency filtering failed: {e}")
            return y

    def _calculate_rhythm_regularity(self, onset_env: np.ndarray) -> float:
        """
        Phase 2: Calculate how regular/irregular the rhythm is
        """
        try:
            if len(onset_env) < 10:
                return 0.5

            # Find peaks (onsets)
            peaks = librosa.util.peak_pick(
                onset_env,
                pre_max=3, post_max=3,
                pre_avg=3, post_avg=5,
                delta=0.1, wait=10
            )

            if len(peaks) < 4:
                return 0.5

            # Calculate intervals between peaks
            intervals = np.diff(peaks)

            if len(intervals) < 2:
                return 0.5

            # Regularity is inverse of coefficient of variation
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)

            if mean_interval > 0:
                cv = std_interval / mean_interval
                regularity = max(0.0, 1.0 - cv)  # Higher regularity = lower variation
                return regularity
            else:
                return 0.5

        except Exception as e:
            logger.warning(f"Rhythm regularity calculation failed: {e}")
            return 0.5

    def _calculate_rhythm_complexity(self, y: np.ndarray, sr: int) -> float:
        """
        Phase 2: Calculate overall rhythm complexity score
        """
        try:
            complexity_factors = []

            # Factor 1: Onset density
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            onset_density = len(librosa.util.peak_pick(onset_env, pre_max=3, post_max=3, pre_avg=3, post_avg=5, delta=0.1, wait=10)) / (len(y) / sr)
            complexity_factors.append(min(1.0, onset_density / 10.0))  # Normalize to 0-1

            # Factor 2: Spectral irregularity
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            spectral_variation = np.std(spectral_centroids) / np.mean(spectral_centroids) if np.mean(spectral_centroids) > 0 else 0
            complexity_factors.append(min(1.0, spectral_variation))

            # Factor 3: Rhythm regularity (inverse)
            regularity = self._calculate_rhythm_regularity(onset_env)
            complexity_factors.append(1.0 - regularity)

            # Factor 4: Tempo variation
            tempo_variation = self._calculate_tempo_variation(y, sr)
            complexity_factors.append(tempo_variation)

            # Weighted average
            complexity = np.average(complexity_factors, weights=[0.3, 0.2, 0.3, 0.2])

            return float(complexity)

        except Exception as e:
            logger.warning(f"⚠️ Rhythm complexity calculation failed: {e}")
            return 0.5

    def _calculate_tempo_variation(self, y: np.ndarray, sr: int) -> float:
        """
        Phase 2: Calculate tempo variation throughout the track
        """
        try:
            # Split into segments and calculate tempo for each
            segment_duration = 10  # seconds
            hop_duration = 5  # seconds

            tempos = []
            for start_time in range(0, int(len(y) / sr), hop_duration):
                end_time = min(start_time + segment_duration, int(len(y) / sr))
                if end_time - start_time < 5:
                    continue

                start_sample = start_time * sr
                end_sample = end_time * sr
                segment = y[start_sample:end_sample]

                try:
                    try:
                        tempo = librosa.feature.rhythm.tempo(y=segment, sr=sr)[0]
                    except AttributeError:
                        tempo = librosa.beat.tempo(y=segment, sr=sr)[0]
                    tempos.append(tempo)
                except:
                    continue

            if len(tempos) < 2:
                return 0.0

            # Calculate coefficient of variation
            mean_tempo = np.mean(tempos)
            std_tempo = np.std(tempos)

            if mean_tempo > 0:
                cv = std_tempo / mean_tempo
                return min(1.0, cv * 2)  # Scale to 0-1 range
            else:
                return 0.0

        except Exception as e:
            logger.warning(f"⚠️ Tempo variation calculation failed: {e}")
            return 0.0

    def _detect_breakbeat_patterns(self, y: np.ndarray, sr: int) -> float:
        """
        Phase 2: Detect breakbeat/jungle/drum'n'bass patterns
        """
        try:
            # Breakbeats typically have:
            # 1. High onset density
            # 2. Irregular rhythm patterns
            # 3. Specific frequency characteristics

            # Extract percussive component
            _, y_percussive = librosa.effects.hpss(y)

            # High-frequency analysis (typical of breakbeats)
            y_high = self._filter_frequency_band(y_percussive, sr, 1000, 8000)

            # Onset detection
            onset_env = librosa.onset.onset_strength(y=y_high, sr=sr)
            onsets = librosa.util.peak_pick(onset_env, pre_max=3, post_max=3, pre_avg=3, post_avg=5, delta=0.1, wait=5)

            # Breakbeat indicators
            onset_density = len(onsets) / (len(y) / sr)
            irregularity = 1.0 - self._calculate_rhythm_regularity(onset_env)

            # Breakbeat probability
            breakbeat_prob = 0.0

            # High onset density (>8 onsets per second suggests breakbeat)
            if onset_density > 8:
                breakbeat_prob += 0.4
            elif onset_density > 5:
                breakbeat_prob += 0.2

            # High irregularity
            if irregularity > 0.6:
                breakbeat_prob += 0.3
            elif irregularity > 0.4:
                breakbeat_prob += 0.15

            # Tempo range (breakbeats often 160-180 BPM)
            try:
                tempo = librosa.feature.rhythm.tempo(y=y, sr=sr)[0]
            except AttributeError:
                tempo = librosa.beat.tempo(y=y, sr=sr)[0]
            if 160 <= tempo <= 180:
                breakbeat_prob += 0.3
            elif 140 <= tempo <= 200:
                breakbeat_prob += 0.15

            return min(1.0, breakbeat_prob)

        except Exception as e:
            logger.warning(f"⚠️ Breakbeat detection failed: {e}")
            return 0.0

    def _check_quadruple_meter(self, beat_intervals: np.ndarray) -> float:
        """Check if beat pattern suggests 4/4 time"""
        try:
            # Look for patterns of 4 beats
            if len(beat_intervals) < 8:
                return 0.0

            # Group beats into groups of 4 and check for regularity
            group_sums = []
            for i in range(0, len(beat_intervals) - 3, 4):
                group_sum = np.sum(beat_intervals[i:i+4])
                group_sums.append(group_sum)

            if len(group_sums) < 2:
                return 0.0

            # Check if group sums are regular
            cv = np.std(group_sums) / np.mean(group_sums) if np.mean(group_sums) > 0 else 1
            confidence = max(0.0, 1.0 - cv * 5)  # Convert CV to confidence
            return confidence

        except:
            return 0.0

    def _check_triple_meter(self, beat_intervals: np.ndarray) -> float:
        """Check if beat pattern suggests 3/4 time"""
        try:
            # Look for patterns of 3 beats
            if len(beat_intervals) < 6:
                return 0.0

            # Group beats into triplets and check for regularity
            triplet_sums = []
            for i in range(0, len(beat_intervals) - 2, 3):
                triplet_sum = np.sum(beat_intervals[i:i+3])
                triplet_sums.append(triplet_sum)

            if len(triplet_sums) < 2:
                return 0.0

            # Check if triplet sums are regular
            cv = np.std(triplet_sums) / np.mean(triplet_sums) if np.mean(triplet_sums) > 0 else 1
            confidence = max(0.0, 1.0 - cv * 5)  # Convert CV to confidence
            return confidence

        except:
            return 0.0

    def _check_compound_meter(self, beat_intervals: np.ndarray) -> float:
        """Check if beat pattern suggests compound meter (6/8, 9/8, 12/8)"""
        try:
            # Look for patterns of 6 beats (for 6/8)
            if len(beat_intervals) < 12:
                return 0.0

            # Group beats into groups of 6 and check for regularity
            group_sums = []
            for i in range(0, len(beat_intervals) - 5, 6):
                group_sum = np.sum(beat_intervals[i:i+6])
                group_sums.append(group_sum)

            if len(group_sums) < 2:
                return 0.0

            # Check if group sums are regular
            cv = np.std(group_sums) / np.mean(group_sums) if np.mean(group_sums) > 0 else 1
            confidence = max(0.0, 1.0 - cv * 5)  # Convert CV to confidence
            return confidence

        except:
            return 0.0
