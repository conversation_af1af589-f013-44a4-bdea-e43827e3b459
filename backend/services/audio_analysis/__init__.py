"""
Audio analysis package - modular audio analysis system.
"""

from .audio_analyzer import <PERSON><PERSON>naly<PERSON>
from .bpm_detection import BPMDetector
from .rhythm_analysis import RhythmAnalyzer
from .genre_classification import GenreClassifier
from .performance_optimization import PerformanceOptimizer
from .segmentation import SegmentationAnalyzer
from .energy_mood_analysis import Energy<PERSON>oodAnalyzer
from .dependencies import (
    RESAMPY_AVAILABLE,
    SKLEARN_AVAILABLE,
    SCIPY_AVAILABLE,
    SKLEARN_ML_AVAILABLE,
    NUMBA_AVAILABLE
)

__all__ = [
    'AudioAnalyzer',
    'BPMDetector',
    'RhythmAnalyzer',
    'GenreClassifier',
    'PerformanceOptimizer',
    'SegmentationAnalyzer',
    'EnergyMoodAnalyzer',
    'RESAMPY_AVAILABLE',
    'SK<PERSON>ARN_AVAILABLE',
    'SCIPY_AVAILABLE',
    '<PERSON><PERSON>ARN_ML_AVAILABLE',
    'NUMBA_AVAILABLE'
]
