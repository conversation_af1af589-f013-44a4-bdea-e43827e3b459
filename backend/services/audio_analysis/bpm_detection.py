"""
BPM and tempo detection module for audio analysis.
Contains all BPM/tempo detection methods and related utilities.
"""

import logging
import numpy as np
import librosa
from typing import Dict, Any, List

from .dependencies import RESAMPY_AVAILABLE, SKLEARN_AVAILABLE, DBSCAN

logger = logging.getLogger(__name__)


class BPMDetector:
    """Handles BPM and tempo detection with multiple methods and confidence scoring."""
    
    def detect_bpm_with_confidence(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        🚀 PHASE 1 ENHANCED BPM detection with confidence score and alternatives

        Improvements:
        - Enhanced preprocessing with resampy (if available)
        - Additional librosa methods for cross-validation
        - Improved clustering with scikit-learn (if available)
        - Better confidence scoring with multi-factor analysis
        - Outlier detection and removal

        Args:
            y: Audio time series
            sr: Sampling rate

        Returns:
            Dictionary with BPM analysis results
        """
        result = {}
        logger.info("🎵 Starting Phase 1 Enhanced BPM Detection")

        # Phase 1 Enhancement: High-quality preprocessing
        y_processed = self._preprocess_audio_for_analysis(y, sr)

        # Use multiple methods to detect BPM for higher accuracy
        tempo_estimates = []
        method_names = []

        # Method 1: Standard librosa beat tracking
        try:
            tempo1, beat_frames = librosa.beat.beat_track(y=y_processed, sr=sr)
            tempo1_float = float(tempo1)
            tempo_estimates.append(tempo1_float)
            method_names.append("librosa_beat_track")
            logger.info(f"Method 1 (beat_track): {tempo1_float:.2f} BPM")
        except Exception as e:
            logger.warning(f"Method 1 failed: {e}")

        # Method 2: Dynamic tempo estimation with onset detection
        try:
            onset_env = librosa.onset.onset_strength(y=y_processed, sr=sr)
            # Use updated librosa API
            try:
                tempo2 = librosa.feature.rhythm.tempo(onset_envelope=onset_env, sr=sr)[0]
            except AttributeError:
                # Fallback for older librosa versions
                tempo2 = librosa.beat.tempo(onset_envelope=onset_env, sr=sr)[0]
            tempo_estimates.append(float(tempo2))
            method_names.append("librosa_onset_tempo")
            logger.info(f"Method 2 (onset_tempo): {tempo2:.2f} BPM")
        except Exception as e:
            logger.warning(f"Method 2 failed: {e}")

        # Method 3: Tempogram-based estimation
        try:
            # Ensure we have onset_env for tempogram
            if 'onset_env' not in locals():
                onset_env = librosa.onset.onset_strength(y=y_processed, sr=sr)
            tempogram = librosa.feature.tempogram(onset_envelope=onset_env, sr=sr)
            # Use updated librosa API
            try:
                tempo3 = librosa.feature.rhythm.tempo(onset_envelope=onset_env, sr=sr, aggregate=None)[0]
            except AttributeError:
                # Fallback for older librosa versions
                tempo3 = librosa.beat.tempo(onset_envelope=onset_env, sr=sr, aggregate=None)[0]
            tempo_estimates.append(float(tempo3))
            method_names.append("librosa_tempogram")
            logger.info(f"Method 3 (tempogram): {tempo3:.2f} BPM")
        except Exception as e:
            logger.warning(f"Method 3 failed: {e}")

        # Method 4: Enhanced onset detection with different functions
        try:
            # Fix: Use valid onset function names - librosa uses different parameter name
            valid_onset_funcs = ['energy', 'spectral_centroid']  # Use correct librosa feature names
            for onset_func in valid_onset_funcs:
                try:
                    # Use correct parameter name for librosa.onset.onset_strength
                    if onset_func == 'energy':
                        onset_env_alt = librosa.onset.onset_strength(y=y_processed, sr=sr)
                    else:
                        onset_env_alt = librosa.onset.onset_strength(y=y_processed, sr=sr, feature=librosa.feature.spectral_centroid)

                    # Use updated librosa API
                    try:
                        tempo_alt = librosa.feature.rhythm.tempo(onset_envelope=onset_env_alt, sr=sr)[0]
                    except AttributeError:
                        # Fallback for older librosa versions
                        tempo_alt = librosa.beat.tempo(onset_envelope=onset_env_alt, sr=sr)[0]
                    tempo_estimates.append(float(tempo_alt))
                    method_names.append(f"librosa_onset_{onset_func}")
                    logger.info(f"Method 4 ({onset_func}): {tempo_alt:.2f} BPM")
                except Exception as func_e:
                    logger.warning(f"Method 4 ({onset_func}) failed: {func_e}")
        except Exception as e:
            logger.warning(f"Method 4 failed: {e}")

        # Phase 1 Enhancement: Remove outliers before clustering
        tempo_estimates_clean = self._remove_tempo_outliers(tempo_estimates)

        logger.info(f"Removed {len(tempo_estimates) - len(tempo_estimates_clean)} outliers")

        # Check for tempo variations (double/half tempo)
        tempo_variations = []
        for tempo in tempo_estimates_clean:
            tempo_variations.extend([tempo, tempo/2, tempo*2])

        # Phase 1 Enhancement: Enhanced clustering
        clustering_result = self._cluster_tempo_estimates(tempo_variations)
        primary_tempo = clustering_result['primary_tempo']
        alternative_tempos = clustering_result['alternative_tempos']

        logger.info(f"Primary tempo after clustering: {primary_tempo:.2f} BPM")
        logger.info(f"Alternative tempos: {[f'{t:.2f}' for t in alternative_tempos]}")

        # Phase 1 Enhancement: Multi-factor confidence calculation
        confidence_factors = {}

        # Factor 1: Method agreement
        method_agreement = self._calculate_method_agreement(tempo_estimates_clean, primary_tempo)
        confidence_factors['method_agreement'] = method_agreement

        # Factor 2: Temporal stability
        temporal_stability = self._calculate_temporal_stability(y_processed, sr, primary_tempo)
        confidence_factors['temporal_stability'] = temporal_stability

        # Factor 3: Cluster quality
        confidence_factors['cluster_quality'] = clustering_result['cluster_confidence']

        # Overall confidence (weighted average)
        weights = {'method_agreement': 0.4, 'temporal_stability': 0.4, 'cluster_quality': 0.2}
        overall_confidence = sum(confidence_factors[factor] * weights[factor] 
                               for factor in confidence_factors)

        result.update({
            'tempo': primary_tempo,
            'confidence': overall_confidence,
            'alternative_tempos': alternative_tempos,
            'method_estimates': dict(zip(method_names, tempo_estimates)),
            'confidence_factors': confidence_factors,
            'clustering_info': clustering_result
        })

        logger.info(f"✅ BPM Detection Complete: {primary_tempo:.2f} BPM (confidence: {overall_confidence:.2f})")
        return result

    def _preprocess_audio_for_analysis(self, y: np.ndarray, sr: int) -> np.ndarray:
        """
        Phase 1: Enhanced audio preprocessing with high-quality resampling
        """
        try:
            # Validate audio buffer first
            if not np.isfinite(y).all():
                logger.warning("⚠️ Audio buffer contains non-finite values, cleaning...")
                y = np.nan_to_num(y, nan=0.0, posinf=0.0, neginf=0.0)

            # Normalize audio to prevent clipping
            y_normalized = librosa.util.normalize(y)

            # High-quality resampling if resampy is available
            if RESAMPY_AVAILABLE and sr != 22050:
                import resampy
                logger.info(f"🔄 High-quality resampling from {sr}Hz to 22050Hz using resampy")
                y_resampled = resampy.resample(y_normalized, sr, 22050, filter='kaiser_best')

                # Final validation
                if not np.isfinite(y_resampled).all():
                    logger.warning("⚠️ Resampled audio buffer contains non-finite values, cleaning...")
                    y_resampled = np.nan_to_num(y_resampled, nan=0.0, posinf=0.0, neginf=0.0)

                return y_resampled
            else:
                # Use librosa default resampling
                if sr != 22050:
                    logger.info(f"🔄 Standard resampling from {sr}Hz to 22050Hz using librosa")
                    y_resampled = librosa.resample(y_normalized, orig_sr=sr, target_sr=22050)

                    # Final validation
                    if not np.isfinite(y_resampled).all():
                        logger.warning("⚠️ Resampled audio buffer contains non-finite values, cleaning...")
                        y_resampled = np.nan_to_num(y_resampled, nan=0.0, posinf=0.0, neginf=0.0)

                    return y_resampled
                return y_normalized
        except Exception as e:
            logger.warning(f"⚠️ Preprocessing failed, using original audio: {e}")
            return y

    def _remove_tempo_outliers(self, tempo_estimates: List[float]) -> List[float]:
        """
        Phase 1: Remove tempo outliers using statistical methods
        """
        if len(tempo_estimates) < 3:
            return tempo_estimates

        tempo_array = np.array(tempo_estimates)
        median = np.median(tempo_array)
        mad = np.median(np.abs(tempo_array - median))  # Median Absolute Deviation

        # Modified Z-score threshold (more robust than standard deviation)
        threshold = 2.5
        modified_z_scores = 0.6745 * (tempo_array - median) / mad if mad > 0 else np.zeros_like(tempo_array)

        # Keep values within threshold
        clean_estimates = tempo_array[np.abs(modified_z_scores) < threshold].tolist()

        logger.info(f"📊 Outlier removal: {len(tempo_estimates)} → {len(clean_estimates)} estimates")
        return clean_estimates if clean_estimates else tempo_estimates

    def _cluster_tempo_estimates(self, tempo_variations: List[float]) -> Dict[str, Any]:
        """
        Phase 1: Enhanced clustering with scikit-learn or fallback
        """
        if not tempo_variations:
            return {"primary_tempo": 120.0, "alternative_tempos": [], "cluster_confidence": 0.0}

        X = np.array(tempo_variations).reshape(-1, 1)

        if SKLEARN_AVAILABLE:
            # Use scikit-learn DBSCAN with optimized parameters
            clustering = DBSCAN(eps=3.0, min_samples=1).fit(X)
            labels = clustering.labels_
            logger.info("✅ Using scikit-learn DBSCAN clustering")
        else:
            # Use fallback clustering
            clustering = DBSCAN(eps=3.0, min_samples=1).fit(X)
            labels = clustering.labels_
            logger.info("⚠️ Using fallback clustering")

        # Process clusters
        unique_clusters = set(labels)
        clusters = []

        for cluster_id in unique_clusters:
            indices = np.where(labels == cluster_id)[0]
            cluster_tempos = X[indices].flatten()

            clusters.append({
                "tempo": float(np.mean(cluster_tempos)),
                "count": len(indices),
                "strength": len(indices) / len(tempo_variations),
                "std": float(np.std(cluster_tempos)),
                "indices": indices.tolist()
            })

        # Sort by strength (count normalized)
        clusters.sort(key=lambda x: x["strength"], reverse=True)

        # Extract primary and alternative tempos
        primary_tempo = clusters[0]["tempo"] if clusters else 120.0
        alternative_tempos = [c["tempo"] for c in clusters[1:3]]  # Top 2 alternatives
        cluster_confidence = clusters[0]["strength"] if clusters else 0.0

        return {
            "primary_tempo": primary_tempo,
            "alternative_tempos": alternative_tempos,
            "cluster_confidence": cluster_confidence,
            "clusters": clusters
        }

    def _calculate_method_agreement(self, estimates: List[float], primary_tempo: float) -> float:
        """
        Phase 1: Calculate agreement between different detection methods
        """
        if not estimates:
            return 0.0

        # Calculate how many estimates are close to the primary tempo
        tolerance = 3.0  # BPM tolerance
        agreements = sum(1 for est in estimates if abs(est - primary_tempo) <= tolerance)

        return agreements / len(estimates)

    def _calculate_temporal_stability(self, y: np.ndarray, sr: int, primary_tempo: float) -> float:
        """
        Phase 1: Calculate temporal stability of BPM throughout the track
        """
        try:
            # Split into segments and check tempo consistency
            segment_duration = 10  # seconds
            hop_duration = 5  # seconds

            segment_tempos = []
            for start_time in range(0, len(y) // sr, hop_duration):
                end_time = min(start_time + segment_duration, len(y) // sr)
                if end_time - start_time < 5:
                    continue

                start_sample = start_time * sr
                end_sample = end_time * sr
                segment = y[start_sample:end_sample]

                try:
                    tempo, _ = librosa.beat.beat_track(y=segment, sr=sr)
                    segment_tempos.append(float(tempo))
                except:
                    continue

            if not segment_tempos:
                return 0.5  # Default stability

            # Calculate stability as inverse of coefficient of variation
            mean_tempo = np.mean(segment_tempos)
            std_tempo = np.std(segment_tempos)

            if mean_tempo > 0:
                cv = std_tempo / mean_tempo
                stability = max(0.0, 1.0 - cv)  # Higher stability = lower variation
                return stability
            else:
                return 0.5

        except Exception as e:
            logger.warning(f"⚠️ Temporal stability calculation failed: {e}")
            return 0.5

    def get_tempo_ensemble(self, y: np.ndarray, sr: int) -> float:
        """Multi-method tempo detection ensemble for higher accuracy"""
        try:
            tempos = []

            # Method 1: Standard beat tracking
            try:
                tempo1, _ = librosa.beat.beat_track(y=y, sr=sr)
                tempos.append(float(tempo1))
            except:
                pass

            # Method 2: Onset-based tempo
            try:
                onset_env = librosa.onset.onset_strength(y=y, sr=sr)
                tempo2 = librosa.feature.rhythm.tempo(onset_envelope=onset_env, sr=sr)[0]
                tempos.append(float(tempo2))
            except:
                pass

            # Method 3: Autocorrelation-based
            try:
                onset_env = librosa.onset.onset_strength(y=y, sr=sr)
                ac = librosa.autocorrelate(onset_env, max_size=2 * sr // 512)
                tempo3 = 60 * sr / (512 * (np.argmax(ac[sr//512:]) + sr//512))
                if 60 <= tempo3 <= 200:  # Reasonable tempo range
                    tempos.append(float(tempo3))
            except:
                pass

            if tempos:
                # Use median for robustness
                return float(np.median(tempos))
            else:
                return 120.0  # Default fallback

        except Exception as e:
            logger.warning(f"Tempo ensemble failed: {e}")
            return 120.0
