"""
Main AudioAnalyzer class that orchestrates all audio analysis modules.
This is the refactored version of the original massive audio_analyzer.py file.
"""

import os
import logging
import asyncio
import signal
import threading
import concurrent.futures
from pathlib import Path
from typing import Dict, Any, List, Set, Optional
import numpy as np
import librosa
import mutagen
from mutagen.id3 import ID3
from mutagen.flac import FLAC
from mutagen.mp4 import MP4
from sqlalchemy.orm import Session

from backend.models.track import Track
from backend.models.track_analysis import TrackAnalysis, AnalysisStatus
from backend.utils.cover_extractor import extract_cover_art

from .bpm_detection import BPMDetector
from .rhythm_analysis import RhythmAnalyzer
from .genre_classification import GenreClassifier
from .performance_optimization import PerformanceOptimizer
from .segmentation import SegmentationAnalyzer
from .energy_mood_analysis import EnergyMoodAnalyzer

logger = logging.getLogger(__name__)


class AudioAnalyzer:
    """Service for analyzing audio files and extracting metadata"""

    def __init__(self, db):
        self.db = db
        self.shutdown_event = asyncio.Event()
        self.thread_pool = None
        self.background_tasks = set()
        
        # Initialize analysis modules
        self.bpm_detector = BPMDetector()
        self.rhythm_analyzer = RhythmAnalyzer()
        self.genre_classifier = GenreClassifier()
        self.performance_optimizer = PerformanceOptimizer()
        self.segmentation_analyzer = SegmentationAnalyzer()
        self.energy_mood_analyzer = EnergyMoodAnalyzer()
        
        self._setup_shutdown_handling()

    def _setup_shutdown_handling(self):
        """Setup shutdown handling for graceful termination"""
        try:
            def signal_handler(signum, frame):
                logger.info(f"Received signal {signum}, initiating graceful shutdown...")
                asyncio.create_task(self._shutdown())

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        except Exception as e:
            logger.warning(f"Could not setup signal handlers: {e}")

    async def _shutdown(self):
        """Graceful shutdown of the analyzer"""
        logger.info("Shutting down AudioAnalyzer...")
        self.shutdown_event.set()
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Shutdown thread pool
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
        
        logger.info("AudioAnalyzer shutdown complete")

    def _get_thread_pool(self) -> concurrent.futures.ThreadPoolExecutor:
        """Get or create a managed thread pool"""
        if self.thread_pool is None:
            self.thread_pool = concurrent.futures.ThreadPoolExecutor(
                max_workers=2, thread_name_prefix="audio_analysis"
            )
        return self.thread_pool

    def _add_background_task(self, task: asyncio.Task):
        """Add a background task to be managed"""
        self.background_tasks.add(task)
        task.add_done_callback(self.background_tasks.discard)

    def _is_valid_camelot_key(self, key_str: str) -> bool:
        """Check if a string is a valid Camelot key format (e.g., 1A, 2B, etc.)"""
        if not key_str:
            return False
        
        key_str = key_str.strip().upper()
        
        # Check format: number + letter
        if len(key_str) < 2:
            return False
        
        # Extract number and letter parts
        number_part = key_str[:-1]
        letter_part = key_str[-1]
        
        # Validate number (1-12)
        try:
            num = int(number_part)
            if not (1 <= num <= 12):
                return False
        except ValueError:
            return False
        
        # Validate letter (A or B)
        if letter_part not in ['A', 'B']:
            return False
        
        return True

    async def analyze_track_enhanced(self, track_id: int, progress_callback=None) -> Dict[str, Any]:
        """
        Enhanced track analysis using all available modules
        """
        db = self.db()
        results = {}
        
        try:
            # Get track from database
            track = db.query(Track).filter(Track.id == track_id).first()
            if not track:
                raise ValueError(f"Track with ID {track_id} not found")

            logger.info(f"🎵 Starting enhanced analysis for track {track_id}: {track.title}")

            # Check for existing analysis
            analysis = db.query(TrackAnalysis).filter(TrackAnalysis.track_id == track_id).first()
            if not analysis:
                analysis = TrackAnalysis(track_id=track_id, status=AnalysisStatus.PENDING.value)
                db.add(analysis)
                db.commit()

            # Update status
            analysis.status = AnalysisStatus.IN_PROGRESS.value
            db.commit()

            if progress_callback:
                await progress_callback({
                    "status": "starting",
                    "progress": 0,
                    "message": "🎵 Starting enhanced audio analysis...",
                    "phase": "initialization"
                })

            # Load audio file
            file_path = track.file_path
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Audio file not found: {file_path}")

            # Run librosa analysis in thread pool
            loop = asyncio.get_event_loop()
            thread_pool = self._get_thread_pool()
            
            if progress_callback:
                await progress_callback({
                    "status": "loading",
                    "progress": 10,
                    "message": "📁 Loading audio file...",
                    "phase": "loading"
                })

            librosa_results = await loop.run_in_executor(
                thread_pool, self._analyze_with_librosa, file_path
            )
            results.update(librosa_results)

            if progress_callback:
                await progress_callback({
                    "status": "analyzing",
                    "progress": 30,
                    "message": "🎯 Analyzing BPM and tempo...",
                    "phase": "bpm_detection"
                })

            # Enhanced BPM detection
            if "y" in results and "sr" in results:
                bpm_results = self.bpm_detector.detect_bpm_with_confidence(results["y"], results["sr"])
                results.update(bmp_results)

            if progress_callback:
                await progress_callback({
                    "status": "analyzing",
                    "progress": 50,
                    "message": "🎼 Analyzing rhythm and structure...",
                    "phase": "rhythm_analysis"
                })

            # Rhythm analysis
            if "y" in results and "sr" in results:
                rhythm_results = self.rhythm_analyzer.detect_basic_rhythm_complexity(results["y"], results["sr"])
                results["rhythm_analysis"] = rhythm_results

            if progress_callback:
                await progress_callback({
                    "status": "analyzing",
                    "progress": 70,
                    "message": "🎨 Analyzing energy and mood...",
                    "phase": "energy_analysis"
                })

            # Energy and mood analysis
            if "y" in results and "sr" in results:
                energy_results = self.energy_mood_analyzer.analyze_energy_profile(results["y"], results["sr"])
                results["energy_profile"] = energy_results

            if progress_callback:
                await progress_callback({
                    "status": "analyzing",
                    "progress": 90,
                    "message": "🏷️ Finalizing analysis...",
                    "phase": "finalization"
                })

            # Process and store results
            self._process_mixed_in_key_metadata(track, results)
            
            # Update analysis record
            analysis.status = AnalysisStatus.COMPLETED.value
            analysis.results = results
            db.commit()

            if progress_callback:
                await progress_callback({
                    "status": "completed",
                    "progress": 100,
                    "message": "✅ Enhanced analysis completed successfully!",
                    "phase": "completed"
                })

            logger.info(f"✅ COMPLETE: Enhanced analysis finished for track {track_id}")
            return results

        except Exception as e:
            logger.error(f"Error analyzing track {track_id}: {str(e)}", exc_info=True)
            if progress_callback:
                await progress_callback({
                    "status": "error",
                    "progress": 0,
                    "message": f"❌ Analysis failed: {str(e)}",
                    "phase": "error"
                })
            try:
                if 'analysis' in locals() and analysis:
                    analysis.status = AnalysisStatus.ERROR.value
                    analysis.error_message = str(e)
                    db.commit()
            except Exception as commit_error:
                logger.error(f"Error updating analysis status: {str(commit_error)}")
            return {"error": str(e)}
        finally:
            # Close the database session
            db.close()

    def _process_mixed_in_key_metadata(self, track, results):
        """Process Mixed in Key metadata and librosa analysis results"""

        # STEP 1: Store Mixed in Key data as backup/reference values
        if "mixed_in_key_key" in results and results["mixed_in_key_key"] is not None:
            track.mixed_in_key_key = results["mixed_in_key_key"]
            logger.info(f"Stored Mixed in Key metadata key: {track.mixed_in_key_key}")

            # Set as active value only if no current value exists
            if not track.key or track.key == "1A":
                track.key = results["mixed_in_key_key"]
                track.key_source = "Mixed in Key"
                logger.info(f"Setting track key from Mixed in Key metadata: {track.key}")

        if "mixed_in_key_energy" in results and results["mixed_in_key_energy"] is not None:
            track.mixed_in_key_energy = results["mixed_in_key_energy"]
            logger.info(f"Stored Mixed in Key metadata energy: {track.mixed_in_key_energy}")

            # Set as active value only if no current value exists
            if not track.energy or track.energy == 5:
                track.energy = results["mixed_in_key_energy"]
                track.energy_source = "Mixed in Key"
                logger.info(f"Setting track energy from Mixed in Key metadata: {track.energy}")

        if "mixed_in_key_bpm" in results and results["mixed_in_key_bpm"] is not None:
            # Store the Mixed in Key BPM value
            track.mixed_in_key_bpm = results["mixed_in_key_bpm"]
            logger.info(f"Stored Mixed in Key metadata BPM: {results['mixed_in_key_bpm']}")

            # Set as active value only if no current value exists
            if not track.bpm:
                track.bpm = results["mixed_in_key_bpm"]
                track.bpm_source = "Mixed in Key"
                logger.info(f"Setting track BPM from Mixed in Key metadata: {track.bpm}")

        # STEP 2: Use librosa analysis results only if no Mixed in Key data available
        if "tempo" in results and results["tempo"]:
            # Store librosa result separately
            track.librosa_bpm = results["tempo"]
            # Only use librosa BPM if no Mixed in Key BPM was found
            if not hasattr(track, 'bpm_source') or track.bpm_source != "TBPM Tag":
                track.bpm = results["tempo"]
                track.bpm_source = "Librosa"
                logger.info(f"🎯 Setting BPM from enhanced librosa analysis: {track.bpm}")
            else:
                logger.info(f"🎯 Keeping Mixed in Key BPM ({track.bpm}) over librosa BPM ({results['tempo']})")

        if "key" in results and results["key"]:
            # Store librosa result separately
            track.librosa_key = results["key"]
            # Only use librosa key if no Mixed in Key key was found
            if not track.mixed_in_key_key:
                track.key = results["key"]
                track.key_source = "Librosa"
                logger.info(f"🎯 Setting key from enhanced librosa analysis: {track.key}")
            else:
                logger.info(f"🎯 Keeping Mixed in Key key ({track.key}) over librosa key ({results['key']})")

        if "energy" in results and results["energy"]:
            # Store librosa result separately
            track.librosa_energy = results["energy"]
            # Only use librosa energy if no Mixed in Key energy was found
            if not track.mixed_in_key_energy:
                track.energy = results["energy"]
                track.energy_source = "Librosa"
                logger.info(f"🎯 Setting energy from enhanced librosa analysis: {track.energy}")
            else:
                logger.info(f"🎯 Keeping Mixed in Key energy ({track.energy}) over librosa energy ({results['energy']})")

        # STEP 3: Set defaults if nothing was found
        if not track.key:
            track.key = "1A"  # Default to 1A
            track.key_source = "Default"
            logger.info(f"Setting default key: {track.key}")

        if not track.energy:
            track.energy = 5  # Default to 5
            track.energy_source = "Default"
            logger.info(f"Setting default energy: {track.energy}")

        logger.info(f"✅ Final track values - BPM: {track.bpm}, Key: {track.key} ({track.key_source}), Energy: {track.energy} ({track.energy_source})")

    def _analyze_with_librosa(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze an audio file with librosa (runs in a separate thread)

        Args:
            file_path: Path to the audio file

        Returns:
            Dictionary with audio features
        """
        # Load the audio file - OPTIMIZED: Reduced from 120s to 90s for faster analysis
        # 90 seconds is sufficient for most DJ analysis while maintaining accuracy
        y, sr = librosa.load(file_path, sr=None, duration=90)  # Analyze first 90 seconds for good accuracy

        features = {"y": y, "sr": sr}

        # Enhanced BPM detection with confidence score
        bpm_results = self.bpm_detector.detect_bpm_with_confidence(y, sr)
        features.update(bpm_results)

        # Enhanced key detection with confidence score
        key_results = self._detect_key_with_confidence(y, sr)
        features.update(key_results)

        # Structure analysis (intro, verse, chorus, etc.)
        structure_results = self._analyze_track_structure(y, sr)
        features.update(structure_results)

        # Energy level mapping
        energy_results = self.energy_mood_analyzer.analyze_energy_levels(y, sr)
        features.update(energy_results)

        # Extract beat positions with enhanced accuracy
        beat_grid_results = self._extract_enhanced_beat_grid(y, sr, features.get("tempo", 120))
        features.update(beat_grid_results)

        # Generate simplified waveform data for visualization
        n_points = 1000  # Number of points in the waveform
        y_abs = np.abs(y)
        points_per_segment = len(y_abs) // n_points
        waveform = []
        for i in range(n_points):
            start = i * points_per_segment
            end = min((i + 1) * points_per_segment, len(y_abs))
            if start < end:
                segment = y_abs[start:end]
                waveform.append(float(np.mean(segment)))
            else:
                waveform.append(0.0)
        features["waveform_data"] = waveform

        # Calculate danceability
        features["danceability"] = self.energy_mood_analyzer.calculate_danceability(y, sr)

        # Multi-dimensional energy profile analysis
        energy_profile = self.energy_mood_analyzer.analyze_energy_profile(y, sr)
        features.update(energy_profile)

        return features

    def _detect_key_with_confidence(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """Enhanced key detection with confidence score and alternatives"""
        result = {
            "key": "1A",
            "key_confidence": 0.0,
            "alternative_keys": []
        }

        try:
            # Use librosa's chroma-based key detection
            chroma = librosa.feature.chroma_cqt(y=y, sr=sr)

            # Key profiles for major and minor keys
            key_profiles = {
                'C': [1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1],  # C major
                'C#': [1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0],  # C# major
                'D': [0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1],   # D major
                'D#': [1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0],  # D# major
                'E': [0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1],   # E major
                'F': [1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0],   # F major
                'F#': [0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1],  # F# major
                'G': [1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1],   # G major
                'G#': [1, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0],  # G# major
                'A': [0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1],   # A major
                'A#': [1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0],  # A# major
                'B': [0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1],   # B major
            }

            # Calculate average chroma
            chroma_mean = np.mean(chroma, axis=1)

            # Find best matching key
            correlations = {}
            for key_name, profile in key_profiles.items():
                correlation = np.corrcoef(chroma_mean, profile)[0, 1]
                if not np.isnan(correlation):
                    correlations[key_name] = correlation

            if correlations:
                # Get best match
                best_key = max(correlations.items(), key=lambda x: x[1])

                # Map to Camelot notation (simplified)
                camelot_map = {
                    'C': '8B', 'G': '9B', 'D': '10B', 'A': '11B', 'E': '12B', 'B': '1B',
                    'F#': '2B', 'C#': '3B', 'G#': '4B', 'D#': '5B', 'A#': '6B', 'F': '7B'
                }

                camelot_key = camelot_map.get(best_key[0], '1A')
                result["key"] = camelot_key
                result["key_confidence"] = float(max(0.0, best_key[1]))

        except Exception as e:
            logger.warning(f"Key detection failed: {e}")

        return result

    def _analyze_track_structure(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Analyze track structure to identify sections (intro, verse, chorus, etc.)
        """
        try:
            # Compute MFCC features - OPTIMIZED: Reduced from 13 to 8 coefficients
            mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=8)

            # Compute spectral contrast
            contrast = librosa.feature.spectral_contrast(y=y, sr=sr)

            # Compute onset strength
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)

            # Detect segment boundaries using spectral clustering
            bound_frames = librosa.segment.agglomerative(
                np.vstack([mfcc, contrast]),
                min(10, len(y) // sr // 10)  # Aim for ~10 segments or fewer
            )

            # Convert frames to time
            bound_times = librosa.frames_to_time(bound_frames, sr=sr)

            # Analyze each segment
            sections = []
            for i in range(len(bound_times) - 1):
                start_time = bound_times[i]
                end_time = bound_times[i+1]

                # Skip very short segments
                if end_time - start_time < 5:
                    continue

                # Extract segment
                start_sample = int(start_time * sr)
                end_sample = int(end_time * sr)
                segment = y[start_sample:end_sample]

                # Calculate segment features
                segment_rms = np.mean(librosa.feature.rms(y=segment)[0])
                segment_onset = np.mean(librosa.onset.onset_strength(y=segment, sr=sr))
                segment_spectral = np.mean(librosa.feature.spectral_centroid(y=segment, sr=sr)[0])

                # Determine section type based on features
                section_type = self._classify_section_type(
                    i, len(bound_times) - 2, segment_rms, segment_onset, segment_spectral
                )

                # Calculate energy level (0-10)
                energy = min(10, max(1, int(segment_rms * 50)))

                sections.append({
                    "start": float(start_time),
                    "end": float(end_time),
                    "type": section_type,
                    "energy": energy,
                    "confidence": 80.0  # Default confidence
                })

            return {"sections": sections}

        except Exception as e:
            logger.warning(f"Track structure analysis failed: {e}")
            return {"sections": []}

    def _classify_section_type(self, index: int, total_sections: int, rms: float, onset: float, spectral: float) -> str:
        """Classify section type based on audio features and position"""
        # Simple heuristic based on position and energy
        if index == 0:
            return "intro"
        elif index == total_sections - 1:
            return "outro"
        elif rms > 0.15:  # High energy
            return "chorus"
        elif rms < 0.05:  # Low energy
            return "breakdown"
        else:
            return "verse"

    def _extract_enhanced_beat_grid(self, y: np.ndarray, sr: int, bpm: float) -> Dict[str, Any]:
        """Extract enhanced beat grid with bar and beat positions"""
        try:
            # Get beat tracking
            tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr, bpm=bpm)

            # Convert to time
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)

            # Estimate bars (assuming 4/4 time)
            beats_per_bar = 4
            bar_times = beat_times[::beats_per_bar]

            return {
                "beat_positions": beat_times.tolist(),
                "bar_positions": bar_times.tolist(),
                "beats_per_bar": beats_per_bar,
                "total_beats": len(beat_times),
                "total_bars": len(bar_times)
            }

        except Exception as e:
            logger.warning(f"Beat grid extraction failed: {e}")
            return {
                "beat_positions": [],
                "bar_positions": [],
                "beats_per_bar": 4,
                "total_beats": 0,
                "total_bars": 0
            }
