"""
AI Settings Impact Analyzer

This service analyzes the impact of different AI settings on performance and user satisfaction.
It provides correlation analysis, impact visualization data, and A/B testing capabilities.
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
import threading
import statistics
from pathlib import Path
from collections import Counter, defaultdict
import math
import random

from backend.services.monitoring.ai_data_collector import AIDataCollector

logger = logging.getLogger(__name__)

# Thread lock for file access
file_lock = threading.Lock()

class AISettingsImpactAnalyzer:
    """
    Service for analyzing the impact of AI settings on performance and user satisfaction.
    """
    
    @staticmethod
    def analyze_parameter_performance_impact(
        feature_id: str,
        param_name: str,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Analyze the impact of a parameter on performance metrics.
        
        Args:
            feature_id: Feature ID to analyze
            param_name: Parameter name to analyze
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of performance impact analysis
        """
        try:
            # Load request data
            with file_lock:
                data = AIDataCollector._load_data(AIDataCollector.REQUESTS_FILE)
                
                if "requests" not in data or not data["requests"]:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "impact_score": 0,
                        "correlation": 0,
                        "values_analysis": {},
                        "sample_size": 0
                    }
                
                # Filter requests
                requests = data["requests"]
                requests = [r for r in requests if r.get("feature_id") == feature_id]
                
                if time_range_hours:
                    cutoff_time = (datetime.now() - timedelta(hours=time_range_hours)).isoformat()
                    requests = [r for r in requests if r.get("timestamp", "") >= cutoff_time]
                
                if not requests:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "impact_score": 0,
                        "correlation": 0,
                        "values_analysis": {},
                        "sample_size": 0
                    }
                
                # Group requests by parameter value
                param_groups = defaultdict(list)
                
                for r in requests:
                    parameters = r.get("parameters", {})
                    if param_name in parameters:
                        param_value = str(parameters[param_name])
                        param_groups[param_value].append(r)
                
                if not param_groups:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "impact_score": 0,
                        "correlation": 0,
                        "values_analysis": {},
                        "sample_size": 0
                    }
                
                # Calculate metrics for each parameter value
                values_analysis = {}
                
                for param_value, param_requests in param_groups.items():
                    total_requests = len(param_requests)
                    successful_requests = sum(1 for r in param_requests if r.get("success", False))
                    success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
                    
                    response_times = [r.get("response_time_ms", 0) for r in param_requests if "response_time_ms" in r]
                    avg_response_time = statistics.mean(response_times) if response_times else 0
                    
                    token_counts = [r.get("token_count", 0) for r in param_requests if "token_count" in r]
                    avg_token_count = statistics.mean(token_counts) if token_counts else 0
                    
                    values_analysis[param_value] = {
                        "total_requests": total_requests,
                        "success_rate": round(success_rate, 2),
                        "avg_response_time_ms": round(avg_response_time, 2),
                        "avg_token_count": round(avg_token_count, 2)
                    }
                
                # Calculate correlation between parameter values and performance metrics
                # This is a simplified correlation calculation
                correlation = AISettingsImpactAnalyzer._calculate_correlation(param_groups, param_name)
                
                # Calculate overall impact score (0-100)
                impact_score = min(100, abs(correlation * 100))
                
                return {
                    "feature_id": feature_id,
                    "param_name": param_name,
                    "impact_score": round(impact_score, 2),
                    "correlation": round(correlation, 4),
                    "values_analysis": values_analysis,
                    "sample_size": len(requests)
                }
        except Exception as e:
            logger.error(f"Error analyzing parameter performance impact: {str(e)}")
            return {
                "feature_id": feature_id,
                "param_name": param_name,
                "impact_score": 0,
                "correlation": 0,
                "values_analysis": {},
                "sample_size": 0,
                "error": str(e)
            }
    
    @staticmethod
    def analyze_parameter_satisfaction_impact(
        feature_id: str,
        param_name: str,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Analyze the impact of a parameter on user satisfaction.
        
        Args:
            feature_id: Feature ID to analyze
            param_name: Parameter name to analyze
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of satisfaction impact analysis
        """
        try:
            # Load request and feedback data
            with file_lock:
                request_data = AIDataCollector._load_data(AIDataCollector.REQUESTS_FILE)
                feedback_data = AIDataCollector._load_data(AIDataCollector.FEEDBACK_FILE)
                
                if "requests" not in request_data or not request_data["requests"] or "feedback" not in feedback_data or not feedback_data["feedback"]:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "impact_score": 0,
                        "correlation": 0,
                        "values_analysis": {},
                        "sample_size": 0
                    }
                
                # Filter requests and feedback
                requests = request_data["requests"]
                requests = [r for r in requests if r.get("feature_id") == feature_id]
                
                feedback = feedback_data["feedback"]
                feedback = [f for f in feedback if f.get("feature_id") == feature_id]
                
                if time_range_hours:
                    cutoff_time = (datetime.now() - timedelta(hours=time_range_hours)).isoformat()
                    requests = [r for r in requests if r.get("timestamp", "") >= cutoff_time]
                    feedback = [f for f in feedback if f.get("timestamp", "") >= cutoff_time]
                
                if not requests or not feedback:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "impact_score": 0,
                        "correlation": 0,
                        "values_analysis": {},
                        "sample_size": 0
                    }
                
                # Create a mapping of request_id to parameters
                request_params = {}
                for r in requests:
                    request_id = r.get("request_id")
                    if request_id and "parameters" in r and param_name in r["parameters"]:
                        request_params[request_id] = str(r["parameters"][param_name])
                
                # Group feedback by parameter value
                param_groups = defaultdict(list)
                
                for f in feedback:
                    request_id = f.get("request_id")
                    if request_id in request_params:
                        param_value = request_params[request_id]
                        param_groups[param_value].append(f)
                
                if not param_groups:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "impact_score": 0,
                        "correlation": 0,
                        "values_analysis": {},
                        "sample_size": 0
                    }
                
                # Calculate metrics for each parameter value
                values_analysis = {}
                
                for param_value, param_feedback in param_groups.items():
                    total_feedback = len(param_feedback)
                    ratings = [f.get("rating", 0) for f in param_feedback]
                    avg_rating = statistics.mean(ratings) if ratings else 0
                    
                    values_analysis[param_value] = {
                        "total_feedback": total_feedback,
                        "avg_rating": round(avg_rating, 2),
                        "rating_distribution": dict(Counter(ratings))
                    }
                
                # Calculate correlation between parameter values and satisfaction
                correlation = AISettingsImpactAnalyzer._calculate_satisfaction_correlation(param_groups, param_name)
                
                # Calculate overall impact score (0-100)
                impact_score = min(100, abs(correlation * 100))
                
                return {
                    "feature_id": feature_id,
                    "param_name": param_name,
                    "impact_score": round(impact_score, 2),
                    "correlation": round(correlation, 4),
                    "values_analysis": values_analysis,
                    "sample_size": len(feedback)
                }
        except Exception as e:
            logger.error(f"Error analyzing parameter satisfaction impact: {str(e)}")
            return {
                "feature_id": feature_id,
                "param_name": param_name,
                "impact_score": 0,
                "correlation": 0,
                "values_analysis": {},
                "sample_size": 0,
                "error": str(e)
            }
    
    @staticmethod
    def get_ab_test_results(
        feature_id: str,
        param_name: str,
        variant_a: str,
        variant_b: str,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get A/B test results for a parameter.
        
        Args:
            feature_id: Feature ID to analyze
            param_name: Parameter name to analyze
            variant_a: Value for variant A
            variant_b: Value for variant B
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of A/B test results
        """
        try:
            # Load request and feedback data
            with file_lock:
                request_data = AIDataCollector._load_data(AIDataCollector.REQUESTS_FILE)
                feedback_data = AIDataCollector._load_data(AIDataCollector.FEEDBACK_FILE)
                
                if "requests" not in request_data or not request_data["requests"]:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "variant_a": variant_a,
                        "variant_b": variant_b,
                        "performance_comparison": {},
                        "satisfaction_comparison": {},
                        "winner": "inconclusive",
                        "confidence": 0
                    }
                
                # Filter requests
                requests = request_data["requests"]
                requests = [r for r in requests if r.get("feature_id") == feature_id]
                
                if time_range_hours:
                    cutoff_time = (datetime.now() - timedelta(hours=time_range_hours)).isoformat()
                    requests = [r for r in requests if r.get("timestamp", "") >= cutoff_time]
                
                # Group requests by variant
                variant_a_requests = []
                variant_b_requests = []
                
                for r in requests:
                    parameters = r.get("parameters", {})
                    if param_name in parameters:
                        param_value = str(parameters[param_name])
                        if param_value == variant_a:
                            variant_a_requests.append(r)
                        elif param_value == variant_b:
                            variant_b_requests.append(r)
                
                # Calculate performance metrics for each variant
                performance_comparison = {}
                
                if variant_a_requests:
                    total_requests_a = len(variant_a_requests)
                    successful_requests_a = sum(1 for r in variant_a_requests if r.get("success", False))
                    success_rate_a = (successful_requests_a / total_requests_a) * 100 if total_requests_a > 0 else 0
                    
                    response_times_a = [r.get("response_time_ms", 0) for r in variant_a_requests if "response_time_ms" in r]
                    avg_response_time_a = statistics.mean(response_times_a) if response_times_a else 0
                    
                    performance_comparison["variant_a"] = {
                        "total_requests": total_requests_a,
                        "success_rate": round(success_rate_a, 2),
                        "avg_response_time_ms": round(avg_response_time_a, 2)
                    }
                
                if variant_b_requests:
                    total_requests_b = len(variant_b_requests)
                    successful_requests_b = sum(1 for r in variant_b_requests if r.get("success", False))
                    success_rate_b = (successful_requests_b / total_requests_b) * 100 if total_requests_b > 0 else 0
                    
                    response_times_b = [r.get("response_time_ms", 0) for r in variant_b_requests if "response_time_ms" in r]
                    avg_response_time_b = statistics.mean(response_times_b) if response_times_b else 0
                    
                    performance_comparison["variant_b"] = {
                        "total_requests": total_requests_b,
                        "success_rate": round(success_rate_b, 2),
                        "avg_response_time_ms": round(avg_response_time_b, 2)
                    }
                
                # Calculate satisfaction metrics for each variant
                satisfaction_comparison = {}
                
                if "feedback" in feedback_data and feedback_data["feedback"]:
                    feedback = feedback_data["feedback"]
                    feedback = [f for f in feedback if f.get("feature_id") == feature_id]
                    
                    if time_range_hours:
                        feedback = [f for f in feedback if f.get("timestamp", "") >= cutoff_time]
                    
                    # Create a mapping of request_id to variant
                    request_variants = {}
                    for r in requests:
                        request_id = r.get("request_id")
                        parameters = r.get("parameters", {})
                        if request_id and param_name in parameters:
                            param_value = str(parameters[param_name])
                            if param_value in [variant_a, variant_b]:
                                request_variants[request_id] = param_value
                    
                    # Group feedback by variant
                    variant_a_feedback = []
                    variant_b_feedback = []
                    
                    for f in feedback:
                        request_id = f.get("request_id")
                        if request_id in request_variants:
                            variant = request_variants[request_id]
                            if variant == variant_a:
                                variant_a_feedback.append(f)
                            elif variant == variant_b:
                                variant_b_feedback.append(f)
                    
                    if variant_a_feedback:
                        total_feedback_a = len(variant_a_feedback)
                        ratings_a = [f.get("rating", 0) for f in variant_a_feedback]
                        avg_rating_a = statistics.mean(ratings_a) if ratings_a else 0
                        
                        satisfaction_comparison["variant_a"] = {
                            "total_feedback": total_feedback_a,
                            "avg_rating": round(avg_rating_a, 2)
                        }
                    
                    if variant_b_feedback:
                        total_feedback_b = len(variant_b_feedback)
                        ratings_b = [f.get("rating", 0) for f in variant_b_feedback]
                        avg_rating_b = statistics.mean(ratings_b) if ratings_b else 0
                        
                        satisfaction_comparison["variant_b"] = {
                            "total_feedback": total_feedback_b,
                            "avg_rating": round(avg_rating_b, 2)
                        }
                
                # Determine the winner
                winner = "inconclusive"
                confidence = 0
                
                if (
                    "variant_a" in performance_comparison and 
                    "variant_b" in performance_comparison and 
                    "variant_a" in satisfaction_comparison and 
                    "variant_b" in satisfaction_comparison
                ):
                    # Calculate performance score (lower is better)
                    perf_score_a = (
                        (100 - performance_comparison["variant_a"]["success_rate"]) * 0.7 + 
                        performance_comparison["variant_a"]["avg_response_time_ms"] / 10 * 0.3
                    )
                    perf_score_b = (
                        (100 - performance_comparison["variant_b"]["success_rate"]) * 0.7 + 
                        performance_comparison["variant_b"]["avg_response_time_ms"] / 10 * 0.3
                    )
                    
                    # Calculate satisfaction score (higher is better)
                    sat_score_a = satisfaction_comparison["variant_a"]["avg_rating"] * 20  # Scale to 0-100
                    sat_score_b = satisfaction_comparison["variant_b"]["avg_rating"] * 20  # Scale to 0-100
                    
                    # Calculate overall score (higher is better)
                    overall_score_a = sat_score_a * 0.7 - perf_score_a * 0.3
                    overall_score_b = sat_score_b * 0.7 - perf_score_b * 0.3
                    
                    # Determine winner
                    if overall_score_a > overall_score_b:
                        winner = "variant_a"
                        # Calculate confidence based on sample size and score difference
                        score_diff = abs(overall_score_a - overall_score_b)
                        sample_size = min(
                            performance_comparison["variant_a"]["total_requests"],
                            performance_comparison["variant_b"]["total_requests"]
                        )
                        confidence = min(100, score_diff * math.log10(sample_size + 1) * 5)
                    elif overall_score_b > overall_score_a:
                        winner = "variant_b"
                        # Calculate confidence based on sample size and score difference
                        score_diff = abs(overall_score_b - overall_score_a)
                        sample_size = min(
                            performance_comparison["variant_a"]["total_requests"],
                            performance_comparison["variant_b"]["total_requests"]
                        )
                        confidence = min(100, score_diff * math.log10(sample_size + 1) * 5)
                
                return {
                    "feature_id": feature_id,
                    "param_name": param_name,
                    "variant_a": variant_a,
                    "variant_b": variant_b,
                    "performance_comparison": performance_comparison,
                    "satisfaction_comparison": satisfaction_comparison,
                    "winner": winner,
                    "confidence": round(confidence, 2)
                }
        except Exception as e:
            logger.error(f"Error getting A/B test results: {str(e)}")
            return {
                "feature_id": feature_id,
                "param_name": param_name,
                "variant_a": variant_a,
                "variant_b": variant_b,
                "performance_comparison": {},
                "satisfaction_comparison": {},
                "winner": "inconclusive",
                "confidence": 0,
                "error": str(e)
            }
    
    @staticmethod
    def _calculate_correlation(param_groups: Dict[str, List[Dict[str, Any]]], param_name: str) -> float:
        """
        Calculate correlation between parameter values and performance metrics.
        
        Args:
            param_groups: Dictionary of parameter values to requests
            param_name: Parameter name
            
        Returns:
            Correlation coefficient (-1 to 1)
        """
        try:
            # This is a simplified correlation calculation
            # In a real implementation, you would use a proper statistical method
            
            # Convert parameter values to numeric if possible
            numeric_values = []
            response_times = []
            
            for param_value, requests in param_groups.items():
                try:
                    numeric_value = float(param_value)
                    avg_response_time = statistics.mean([r.get("response_time_ms", 0) for r in requests if "response_time_ms" in r])
                    
                    numeric_values.append(numeric_value)
                    response_times.append(avg_response_time)
                except ValueError:
                    # Skip non-numeric values
                    pass
            
            if not numeric_values or not response_times:
                return 0
            
            # Calculate correlation coefficient
            n = len(numeric_values)
            sum_x = sum(numeric_values)
            sum_y = sum(response_times)
            sum_xy = sum(x * y for x, y in zip(numeric_values, response_times))
            sum_x2 = sum(x * x for x in numeric_values)
            sum_y2 = sum(y * y for y in response_times)
            
            numerator = n * sum_xy - sum_x * sum_y
            denominator = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))
            
            if denominator == 0:
                return 0
            
            return numerator / denominator
        except Exception as e:
            logger.error(f"Error calculating correlation: {str(e)}")
            return 0
    
    @staticmethod
    def _calculate_satisfaction_correlation(param_groups: Dict[str, List[Dict[str, Any]]], param_name: str) -> float:
        """
        Calculate correlation between parameter values and satisfaction ratings.
        
        Args:
            param_groups: Dictionary of parameter values to feedback
            param_name: Parameter name
            
        Returns:
            Correlation coefficient (-1 to 1)
        """
        try:
            # This is a simplified correlation calculation
            # In a real implementation, you would use a proper statistical method
            
            # Convert parameter values to numeric if possible
            numeric_values = []
            ratings = []
            
            for param_value, feedback_list in param_groups.items():
                try:
                    numeric_value = float(param_value)
                    avg_rating = statistics.mean([f.get("rating", 0) for f in feedback_list])
                    
                    numeric_values.append(numeric_value)
                    ratings.append(avg_rating)
                except ValueError:
                    # Skip non-numeric values
                    pass
            
            if not numeric_values or not ratings:
                return 0
            
            # Calculate correlation coefficient
            n = len(numeric_values)
            sum_x = sum(numeric_values)
            sum_y = sum(ratings)
            sum_xy = sum(x * y for x, y in zip(numeric_values, ratings))
            sum_x2 = sum(x * x for x in numeric_values)
            sum_y2 = sum(y * y for y in ratings)
            
            numerator = n * sum_xy - sum_x * sum_y
            denominator = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))
            
            if denominator == 0:
                return 0
            
            return numerator / denominator
        except Exception as e:
            logger.error(f"Error calculating satisfaction correlation: {str(e)}")
            return 0
