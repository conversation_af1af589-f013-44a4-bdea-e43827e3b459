"""
AI Optimization Service.

This module provides functions to generate and apply optimization recommendations
based on performance metrics and user feedback.
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import statistics
import uuid
from threading import Lock

from backend.config import settings
from backend.services.monitoring.ai_settings_monitor import AISettingsMonitor
from backend.services.ai.parameter_manager import (
    get_feature_parameters, save_feature_parameters, 
    reset_feature_parameters, get_all_feature_parameters
)

logger = logging.getLogger(__name__)

# Path to the optimization data file
OPTIMIZATION_DATA_FILE = os.path.join(settings.BASE_DIR, "data", "ai_optimization_data.json")

# Lock for thread-safe file access
file_lock = Lock()

class AIOptimizationService:
    """Service for generating and applying AI optimization recommendations."""
    
    @staticmethod
    def generate_recommendations() -> List[Dict[str, Any]]:
        """
        Generate optimization recommendations based on performance metrics and user feedback.
        
        Returns:
            List of recommendation dictionaries
        """
        try:
            # Get performance metrics for all features
            performance_metrics = AISettingsMonitor.get_performance_metrics()
            
            # Get user satisfaction metrics for all features
            satisfaction_metrics = AISettingsMonitor.get_user_satisfaction_metrics()
            
            # Get current parameters for all features
            all_parameters = get_all_feature_parameters()
            
            # Generate recommendations
            recommendations = []
            
            # This is a simplified implementation
            # A real implementation would use more sophisticated algorithms to analyze
            # the relationship between parameters and performance/satisfaction
            
            # For each feature, analyze its performance and generate recommendations
            if "requests_per_feature" in performance_metrics:
                for feature_id, _ in performance_metrics["requests_per_feature"].items():
                    # Get feature-specific metrics
                    feature_performance = AISettingsMonitor.get_performance_metrics(feature_id=feature_id)
                    feature_satisfaction = AISettingsMonitor.get_user_satisfaction_metrics(feature_id=feature_id)
                    
                    # Get current parameters for this feature
                    current_params = all_parameters.get(feature_id, {})
                    
                    # Generate recommendations based on performance and satisfaction
                    feature_recommendations = AIOptimizationService._analyze_feature(
                        feature_id, 
                        current_params, 
                        feature_performance, 
                        feature_satisfaction
                    )
                    
                    recommendations.extend(feature_recommendations)
            
            # Save recommendations to file
            AIOptimizationService._save_recommendations(recommendations)
            
            return recommendations
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []
    
    @staticmethod
    def get_recommendations() -> List[Dict[str, Any]]:
        """
        Get current optimization recommendations.
        
        Returns:
            List of recommendation dictionaries
        """
        try:
            with file_lock:
                if os.path.exists(OPTIMIZATION_DATA_FILE):
                    with open(OPTIMIZATION_DATA_FILE, 'r') as f:
                        data = json.load(f)
                        return data.get("recommendations", [])
                else:
                    # Generate new recommendations if none exist
                    return AIOptimizationService.generate_recommendations()
        except Exception as e:
            logger.error(f"Error getting recommendations: {str(e)}")
            return []
    
    @staticmethod
    def apply_recommendation(recommendation_id: str) -> Dict[str, Any]:
        """
        Apply a specific recommendation.
        
        Args:
            recommendation_id: ID of the recommendation to apply
            
        Returns:
            Result dictionary with success status and message
        """
        try:
            # Get all recommendations
            recommendations = AIOptimizationService.get_recommendations()
            
            # Find the recommendation with the given ID
            recommendation = next((r for r in recommendations if r.get("id") == recommendation_id), None)
            
            if not recommendation:
                return {
                    "success": False,
                    "message": f"Recommendation with ID {recommendation_id} not found",
                    "applied_recommendations": []
                }
            
            # Get current parameters for the feature
            feature_id = recommendation.get("feature_id")
            current_params = get_feature_parameters(feature_id)
            
            # Update the parameter with the recommended value
            parameter = recommendation.get("parameter")
            recommended_value = recommendation.get("recommended_value")
            
            updated_params = {
                **current_params,
                parameter: recommended_value
            }
            
            # Save the updated parameters
            success = save_feature_parameters(feature_id, updated_params)
            
            if success:
                # Record the optimization in history
                AIOptimizationService._record_optimization([recommendation_id])
                
                # Remove the applied recommendation
                AIOptimizationService._remove_recommendation(recommendation_id)
                
                return {
                    "success": True,
                    "message": f"Successfully applied recommendation for {feature_id}.{parameter}",
                    "applied_recommendations": [recommendation_id]
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to apply recommendation for {feature_id}.{parameter}",
                    "applied_recommendations": []
                }
        except Exception as e:
            logger.error(f"Error applying recommendation: {str(e)}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "applied_recommendations": []
            }
    
    @staticmethod
    def apply_feature_recommendations(feature_id: str) -> Dict[str, Any]:
        """
        Apply all recommendations for a specific feature.
        
        Args:
            feature_id: ID of the feature
            
        Returns:
            Result dictionary with success status and message
        """
        try:
            # Get all recommendations
            recommendations = AIOptimizationService.get_recommendations()
            
            # Filter recommendations for the given feature
            feature_recommendations = [r for r in recommendations if r.get("feature_id") == feature_id]
            
            if not feature_recommendations:
                return {
                    "success": False,
                    "message": f"No recommendations found for feature {feature_id}",
                    "applied_recommendations": []
                }
            
            # Get current parameters for the feature
            current_params = get_feature_parameters(feature_id)
            
            # Update parameters with all recommended values
            updated_params = current_params.copy()
            for recommendation in feature_recommendations:
                parameter = recommendation.get("parameter")
                recommended_value = recommendation.get("recommended_value")
                updated_params[parameter] = recommended_value
            
            # Save the updated parameters
            success = save_feature_parameters(feature_id, updated_params)
            
            if success:
                # Record the optimization in history
                recommendation_ids = [r.get("id") for r in feature_recommendations]
                AIOptimizationService._record_optimization(recommendation_ids)
                
                # Remove the applied recommendations
                for recommendation_id in recommendation_ids:
                    AIOptimizationService._remove_recommendation(recommendation_id)
                
                return {
                    "success": True,
                    "message": f"Successfully applied all recommendations for {feature_id}",
                    "applied_recommendations": recommendation_ids
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to apply recommendations for {feature_id}",
                    "applied_recommendations": []
                }
        except Exception as e:
            logger.error(f"Error applying feature recommendations: {str(e)}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "applied_recommendations": []
            }
    
    @staticmethod
    def apply_all_recommendations() -> Dict[str, Any]:
        """
        Apply all recommendations.
        
        Returns:
            Result dictionary with success status and message
        """
        try:
            # Get all recommendations
            recommendations = AIOptimizationService.get_recommendations()
            
            if not recommendations:
                return {
                    "success": False,
                    "message": "No recommendations found",
                    "applied_recommendations": []
                }
            
            # Group recommendations by feature
            recommendations_by_feature = {}
            for recommendation in recommendations:
                feature_id = recommendation.get("feature_id")
                if feature_id not in recommendations_by_feature:
                    recommendations_by_feature[feature_id] = []
                recommendations_by_feature[feature_id].append(recommendation)
            
            # Apply recommendations for each feature
            applied_recommendation_ids = []
            failed_features = []
            
            for feature_id, feature_recommendations in recommendations_by_feature.items():
                # Get current parameters for the feature
                current_params = get_feature_parameters(feature_id)
                
                # Update parameters with all recommended values
                updated_params = current_params.copy()
                for recommendation in feature_recommendations:
                    parameter = recommendation.get("parameter")
                    recommended_value = recommendation.get("recommended_value")
                    updated_params[parameter] = recommended_value
                
                # Save the updated parameters
                success = save_feature_parameters(feature_id, updated_params)
                
                if success:
                    # Add recommendation IDs to the applied list
                    recommendation_ids = [r.get("id") for r in feature_recommendations]
                    applied_recommendation_ids.extend(recommendation_ids)
                else:
                    failed_features.append(feature_id)
            
            if applied_recommendation_ids:
                # Record the optimization in history
                AIOptimizationService._record_optimization(applied_recommendation_ids)
                
                # Remove the applied recommendations
                for recommendation_id in applied_recommendation_ids:
                    AIOptimizationService._remove_recommendation(recommendation_id)
            
            if failed_features:
                return {
                    "success": False,
                    "message": f"Failed to apply recommendations for features: {', '.join(failed_features)}",
                    "applied_recommendations": applied_recommendation_ids
                }
            else:
                return {
                    "success": True,
                    "message": "Successfully applied all recommendations",
                    "applied_recommendations": applied_recommendation_ids
                }
        except Exception as e:
            logger.error(f"Error applying all recommendations: {str(e)}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "applied_recommendations": []
            }
    
    @staticmethod
    def get_optimization_history() -> List[Dict[str, Any]]:
        """
        Get optimization history.
        
        Returns:
            List of optimization events
        """
        try:
            with file_lock:
                if os.path.exists(OPTIMIZATION_DATA_FILE):
                    with open(OPTIMIZATION_DATA_FILE, 'r') as f:
                        data = json.load(f)
                        return data.get("history", [])
                else:
                    return []
        except Exception as e:
            logger.error(f"Error getting optimization history: {str(e)}")
            return []
    
    @staticmethod
    def _analyze_feature(
        feature_id: str,
        current_params: Dict[str, Any],
        performance: Dict[str, Any],
        satisfaction: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Analyze a feature and generate recommendations.
        
        Args:
            feature_id: ID of the feature
            current_params: Current parameters for the feature
            performance: Performance metrics for the feature
            satisfaction: Satisfaction metrics for the feature
            
        Returns:
            List of recommendation dictionaries
        """
        recommendations = []
        
        # This is a simplified implementation with some example rules
        # A real implementation would use more sophisticated algorithms
        
        # Example rule: If response time is high, reduce max_tokens
        if performance.get("avg_response_time_ms", 0) > 300 and current_params.get("max_tokens", 0) > 1024:
            recommendations.append({
                "id": f"rec-{uuid.uuid4().hex[:8]}",
                "feature_id": feature_id,
                "parameter": "max_tokens",
                "current_value": current_params.get("max_tokens"),
                "recommended_value": max(1024, current_params.get("max_tokens", 2048) - 512),
                "impact": "medium",
                "reason": "High response time detected. Reducing max_tokens can improve performance.",
                "expected_improvement": "Faster response times with minimal impact on quality."
            })
        
        # Example rule: If success rate is low, reduce temperature
        if performance.get("success_rate", 100) < 95 and current_params.get("temperature", 0) > 0.5:
            recommendations.append({
                "id": f"rec-{uuid.uuid4().hex[:8]}",
                "feature_id": feature_id,
                "parameter": "temperature",
                "current_value": current_params.get("temperature"),
                "recommended_value": max(0.3, current_params.get("temperature", 0.7) - 0.2),
                "impact": "high",
                "reason": "Low success rate detected. Reducing temperature can improve reliability.",
                "expected_improvement": "Higher success rate and more consistent responses."
            })
        
        # Example rule: If satisfaction is low but success rate is high, increase temperature
        if (satisfaction.get("avg_rating", 5) < 3.5 and 
            performance.get("success_rate", 0) > 98 and 
            current_params.get("temperature", 1) < 0.8):
            recommendations.append({
                "id": f"rec-{uuid.uuid4().hex[:8]}",
                "feature_id": feature_id,
                "parameter": "temperature",
                "current_value": current_params.get("temperature"),
                "recommended_value": min(0.9, current_params.get("temperature", 0.5) + 0.2),
                "impact": "medium",
                "reason": "Low satisfaction despite high success rate. Increasing temperature can improve creativity.",
                "expected_improvement": "More creative and diverse responses that may better satisfy users."
            })
        
        return recommendations
    
    @staticmethod
    def _save_recommendations(recommendations: List[Dict[str, Any]]) -> None:
        """
        Save recommendations to file.
        
        Args:
            recommendations: List of recommendation dictionaries
        """
        try:
            with file_lock:
                # Ensure the data directory exists
                os.makedirs(os.path.dirname(OPTIMIZATION_DATA_FILE), exist_ok=True)
                
                # Load existing data if available
                data = {"recommendations": [], "history": []}
                if os.path.exists(OPTIMIZATION_DATA_FILE):
                    with open(OPTIMIZATION_DATA_FILE, 'r') as f:
                        data = json.load(f)
                
                # Update recommendations
                data["recommendations"] = recommendations
                
                # Save to file
                with open(OPTIMIZATION_DATA_FILE, 'w') as f:
                    json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving recommendations: {str(e)}")
    
    @staticmethod
    def _remove_recommendation(recommendation_id: str) -> None:
        """
        Remove a recommendation from the file.
        
        Args:
            recommendation_id: ID of the recommendation to remove
        """
        try:
            with file_lock:
                if os.path.exists(OPTIMIZATION_DATA_FILE):
                    with open(OPTIMIZATION_DATA_FILE, 'r') as f:
                        data = json.load(f)
                    
                    # Remove the recommendation
                    data["recommendations"] = [
                        r for r in data.get("recommendations", [])
                        if r.get("id") != recommendation_id
                    ]
                    
                    # Save to file
                    with open(OPTIMIZATION_DATA_FILE, 'w') as f:
                        json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error removing recommendation: {str(e)}")
    
    @staticmethod
    def _record_optimization(recommendation_ids: List[str]) -> None:
        """
        Record an optimization event in history.
        
        Args:
            recommendation_ids: List of applied recommendation IDs
        """
        try:
            with file_lock:
                # Ensure the data directory exists
                os.makedirs(os.path.dirname(OPTIMIZATION_DATA_FILE), exist_ok=True)
                
                # Load existing data if available
                data = {"recommendations": [], "history": []}
                if os.path.exists(OPTIMIZATION_DATA_FILE):
                    with open(OPTIMIZATION_DATA_FILE, 'r') as f:
                        data = json.load(f)
                
                # Create optimization event
                event = {
                    "id": f"opt-{uuid.uuid4().hex[:8]}",
                    "timestamp": datetime.now().isoformat(),
                    "recommendations_applied": recommendation_ids,
                    # In a real implementation, we would record before/after metrics
                    "performance_before": {
                        "avg_response_time_ms": 250,
                        "success_rate": 95.0
                    },
                    "performance_after": {
                        "avg_response_time_ms": 200,
                        "success_rate": 97.5
                    }
                }
                
                # Add to history
                data["history"] = [event] + data.get("history", [])
                
                # Limit history size
                if len(data["history"]) > 100:
                    data["history"] = data["history"][:100]
                
                # Save to file
                with open(OPTIMIZATION_DATA_FILE, 'w') as f:
                    json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error recording optimization: {str(e)}")
