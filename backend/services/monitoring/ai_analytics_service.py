"""
AI Analytics Service

This service analyzes the data collected by the AI Data Collector
and provides metrics and insights for the AI Analytics Dashboard.
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import threading
import statistics
from pathlib import Path
from collections import Counter, defaultdict

from backend.db.session import get_db
from backend.services.monitoring.ai_data_collector import AIDataCollector
from backend.services.monitoring.ai_db_service import AIDBService

logger = logging.getLogger(__name__)

# Thread lock for file access
file_lock = threading.Lock()

# Flag to determine whether to use database or file storage
USE_DATABASE = True

class AIAnalyticsService:
    """
    Service for analyzing AI usage data and providing metrics.
    """

    @staticmethod
    def get_performance_metrics(
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get performance metrics for AI requests.

        Args:
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by

        Returns:
            Dictionary of performance metrics
        """
        try:
            if USE_DATABASE:
                # Use database storage
                with get_db() as db:
                    return AIDBService.get_performance_metrics(
                        db=db,
                        feature_id=feature_id,
                        time_range_hours=time_range_hours
                    )
            else:
                # Use file storage
                # Load request data
                with file_lock:
                    data = AIDataCollector._load_data(AIDataCollector.REQUESTS_FILE)

                    if "requests" not in data or not data["requests"]:
                        return {
                            "total_requests": 0,
                            "success_rate": 0,
                            "avg_response_time_ms": 0,
                            "avg_token_count": 0
                        }

                    # Filter requests
                    requests = data["requests"]

                    if feature_id:
                        requests = [r for r in requests if r.get("feature_id") == feature_id]

                    if time_range_hours:
                        cutoff_time = (datetime.now() - timedelta(hours=time_range_hours)).isoformat()
                        requests = [r for r in requests if r.get("timestamp", "") >= cutoff_time]

                    if not requests:
                        return {
                            "total_requests": 0,
                            "success_rate": 0,
                            "avg_response_time_ms": 0,
                            "avg_token_count": 0
                        }

                    # Calculate metrics
                    total_requests = len(requests)
                    successful_requests = sum(1 for r in requests if r.get("success", False))
                    success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0

                    response_times = [r.get("response_time_ms", 0) for r in requests if "response_time_ms" in r]
                    avg_response_time = statistics.mean(response_times) if response_times else 0

                    token_counts = [r.get("token_count", 0) for r in requests if "token_count" in r]
                    avg_token_count = statistics.mean(token_counts) if token_counts else 0

                    # Count requests by feature
                    requests_per_feature = Counter()
                    for r in requests:
                        feature = r.get("feature_id", "unknown")
                        requests_per_feature[feature] += 1

                    # Count requests by model
                    requests_per_model = Counter()
                    for r in requests:
                        model = r.get("model", "unknown")
                        requests_per_model[model] += 1

                    # Count error types
                    error_types = Counter()
                    for r in requests:
                        if not r.get("success", True) and "error_type" in r:
                            error_type = r.get("error_type", "unknown")
                            error_types[error_type] += 1

                    return {
                        "total_requests": total_requests,
                        "success_rate": round(success_rate, 2),
                        "avg_response_time_ms": round(avg_response_time, 2),
                        "avg_token_count": round(avg_token_count, 2),
                        "requests_per_feature": dict(requests_per_feature),
                        "requests_per_model": dict(requests_per_model),
                        "error_types": dict(error_types)
                    }
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {
                "total_requests": 0,
                "success_rate": 0,
                "avg_response_time_ms": 0,
                "avg_token_count": 0,
                "error": str(e)
            }

    @staticmethod
    def get_user_satisfaction_metrics(
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get user satisfaction metrics.

        Args:
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by

        Returns:
            Dictionary of user satisfaction metrics
        """
        try:
            if USE_DATABASE:
                # Use database storage
                with get_db() as db:
                    return AIDBService.get_user_satisfaction_metrics(
                        db=db,
                        feature_id=feature_id,
                        time_range_hours=time_range_hours
                    )
            else:
                # Use file storage
                # Load feedback data
                with file_lock:
                    data = AIDataCollector._load_data(AIDataCollector.FEEDBACK_FILE)

                    if "feedback" not in data or not data["feedback"]:
                        return {
                            "total_feedback": 0,
                            "avg_rating": 0
                        }

                    # Filter feedback
                    feedback = data["feedback"]

                    if feature_id:
                        feedback = [f for f in feedback if f.get("feature_id") == feature_id]

                    if time_range_hours:
                        cutoff_time = (datetime.now() - timedelta(hours=time_range_hours)).isoformat()
                        feedback = [f for f in feedback if f.get("timestamp", "") >= cutoff_time]

                    if not feedback:
                        return {
                            "total_feedback": 0,
                            "avg_rating": 0
                        }

                    # Calculate metrics
                    total_feedback = len(feedback)
                    ratings = [f.get("rating", 0) for f in feedback]
                    avg_rating = statistics.mean(ratings) if ratings else 0

                    # Count ratings
                    rating_distribution = Counter(ratings)

                    # Count feedback by feature
                    feedback_per_feature = Counter()
                    for f in feedback:
                        feature = f.get("feature_id", "unknown")
                        feedback_per_feature[feature] += 1

                    return {
                        "total_feedback": total_feedback,
                        "avg_rating": round(avg_rating, 2),
                        "rating_distribution": dict(rating_distribution),
                        "feedback_per_feature": dict(feedback_per_feature)
                    }
        except Exception as e:
            logger.error(f"Error getting user satisfaction metrics: {str(e)}")
            return {
                "total_feedback": 0,
                "avg_rating": 0,
                "error": str(e)
            }

    @staticmethod
    def get_settings_usage_stats(
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get settings usage statistics.

        Args:
            time_range_hours: Optional time range in hours to filter by

        Returns:
            Dictionary of settings usage statistics
        """
        try:
            if USE_DATABASE:
                # Use database storage
                with get_db() as db:
                    return AIDBService.get_settings_usage_stats(
                        db=db,
                        time_range_hours=time_range_hours
                    )
            else:
                # Use file storage
                # Load usage data
                with file_lock:
                    data = AIDataCollector._load_data(AIDataCollector.USAGE_FILE)

                    if "usage" not in data:
                        return {
                            "total_requests": 0,
                            "settings_usage": {}
                        }

                    # Get usage data
                    usage = data["usage"]

                    # Calculate total requests
                    total_requests = sum(
                        feature_data.get("total_requests", 0)
                        for feature_data in usage.get("features", {}).values()
                    )

                    # Prepare settings usage data
                    settings_usage = {
                        "features": usage.get("features", {}),
                        "models": usage.get("models", {}),
                        "providers": usage.get("providers", {})
                    }

                    # Process parameters
                    parameters = usage.get("parameters", {})
                    parameter_counts = defaultdict(int)

                    for param_key, param_data in parameters.items():
                        param_name = param_data.get("name", "unknown")
                        parameter_counts[param_name] += param_data.get("count", 0)

                    settings_usage["parameters"] = dict(parameter_counts)

                    return {
                        "total_requests": total_requests,
                        "settings_usage": settings_usage
                    }
        except Exception as e:
            logger.error(f"Error getting settings usage stats: {str(e)}")
            return {
                "total_requests": 0,
                "settings_usage": {},
                "error": str(e)
            }

    @staticmethod
    def get_settings_performance_comparison(
        feature_id: str,
        param_name: str,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get performance comparison for different settings.

        Args:
            feature_id: Feature ID to analyze
            param_name: Parameter name to compare
            time_range_hours: Optional time range in hours to filter by

        Returns:
            Performance comparison data
        """
        try:
            # Load request data
            with file_lock:
                data = AIDataCollector._load_data(AIDataCollector.REQUESTS_FILE)

                if "requests" not in data or not data["requests"]:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "comparison": {}
                    }

                # Filter requests
                requests = data["requests"]
                requests = [r for r in requests if r.get("feature_id") == feature_id]

                if time_range_hours:
                    cutoff_time = (datetime.now() - timedelta(hours=time_range_hours)).isoformat()
                    requests = [r for r in requests if r.get("timestamp", "") >= cutoff_time]

                if not requests:
                    return {
                        "feature_id": feature_id,
                        "param_name": param_name,
                        "comparison": {}
                    }

                # Group requests by parameter value
                param_groups = defaultdict(list)

                for r in requests:
                    parameters = r.get("parameters", {})
                    if param_name in parameters:
                        param_value = str(parameters[param_name])
                        param_groups[param_value].append(r)

                # Calculate metrics for each parameter value
                comparison = {}

                for param_value, param_requests in param_groups.items():
                    total_requests = len(param_requests)
                    successful_requests = sum(1 for r in param_requests if r.get("success", False))
                    success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0

                    response_times = [r.get("response_time_ms", 0) for r in param_requests if "response_time_ms" in r]
                    avg_response_time = statistics.mean(response_times) if response_times else 0

                    comparison[param_value] = {
                        "total_requests": total_requests,
                        "success_rate": round(success_rate, 2),
                        "avg_response_time_ms": round(avg_response_time, 2)
                    }

                return {
                    "feature_id": feature_id,
                    "param_name": param_name,
                    "comparison": comparison
                }
        except Exception as e:
            logger.error(f"Error getting settings performance comparison: {str(e)}")
            return {
                "feature_id": feature_id,
                "param_name": param_name,
                "comparison": {},
                "error": str(e)
            }
