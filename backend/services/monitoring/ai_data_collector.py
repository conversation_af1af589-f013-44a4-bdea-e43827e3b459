"""
AI Data Collector Service

This service collects and stores data about AI usage in the application.
It tracks requests, responses, errors, and user feedback to provide
analytics for the AI Analytics Dashboard.
"""

import logging
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import threading
import statistics
from pathlib import Path

from backend.db.session import get_db
from backend.services.monitoring.ai_db_service import AIDBService

logger = logging.getLogger(__name__)

# Thread lock for file access
file_lock = threading.Lock()

# Flag to determine whether to use database or file storage
USE_DATABASE = True

class AIDataCollector:
    """
    Service for collecting and storing AI usage data.
    """

    # Data storage paths
    DATA_DIR = Path("data/monitoring")
    REQUESTS_FILE = DATA_DIR / "ai_requests.json"
    FEEDBACK_FILE = DATA_DIR / "ai_feedback.json"
    USAGE_FILE = DATA_DIR / "ai_usage.json"

    @staticmethod
    def _ensure_data_dir():
        """Ensure the data directory exists."""
        os.makedirs(AIDataCollector.DATA_DIR, exist_ok=True)

    @staticmethod
    def _load_data(file_path: Path) -> Dict[str, Any]:
        """
        Load data from a JSON file.

        Args:
            file_path: Path to the JSON file

        Returns:
            Dictionary of data
        """
        AIDataCollector._ensure_data_dir()

        if not file_path.exists():
            return {}

        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}

    @staticmethod
    def _save_data(file_path: Path, data: Dict[str, Any]):
        """
        Save data to a JSON file.

        Args:
            file_path: Path to the JSON file
            data: Data to save
        """
        AIDataCollector._ensure_data_dir()

        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)

    @staticmethod
    def log_request(
        request_id: str,
        feature_id: str,
        provider: str,
        model: str,
        parameters: Dict[str, Any],
        prompt: str,
        response: Optional[str] = None,
        response_time_ms: Optional[float] = None,
        token_count: Optional[int] = None,
        success: bool = True,
        error_type: Optional[str] = None,
        error_message: Optional[str] = None,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log an AI request with its details.

        Args:
            request_id: Unique identifier for the request
            feature_id: The ID of the feature that made the request
            provider: The AI provider used
            model: The model used
            parameters: The parameters used for the request
            prompt: The prompt sent to the AI
            response: The response from the AI
            response_time_ms: The response time in milliseconds
            token_count: The number of tokens used
            success: Whether the request was successful
            error_type: Type of error if the request failed
            error_message: Error message if the request failed
            user_id: ID of the user who made the request
            metadata: Additional metadata about the request
        """
        try:
            if USE_DATABASE:
                # Use database storage
                with get_db() as db:
                    AIDBService.log_request(
                        db=db,
                        request_id=request_id,
                        feature_id=feature_id,
                        provider=provider,
                        model=model,
                        parameters=parameters,
                        prompt=prompt,
                        response=response,
                        response_time_ms=response_time_ms,
                        token_count=token_count,
                        success=success,
                        error_type=error_type,
                        error_message=error_message,
                        user_id=user_id,
                        metadata=metadata
                    )
            else:
                # Use file storage
                # Create the request entry
                entry = {
                    "request_id": request_id,
                    "timestamp": datetime.now().isoformat(),
                    "feature_id": feature_id,
                    "provider": provider,
                    "model": model,
                    "parameters": parameters,
                    "prompt_length": len(prompt) if prompt else 0,
                    "success": success,
                    "user_id": user_id
                }

                # Add optional fields if provided
                if response:
                    entry["response_length"] = len(response)

                if response_time_ms is not None:
                    entry["response_time_ms"] = response_time_ms

                if token_count is not None:
                    entry["token_count"] = token_count

                if not success:
                    entry["error_type"] = error_type
                    entry["error_message"] = error_message

                if metadata:
                    entry["metadata"] = metadata

                # Load existing data
                with file_lock:
                    data = AIDataCollector._load_data(AIDataCollector.REQUESTS_FILE)

                    # Initialize requests array if it doesn't exist
                    if "requests" not in data:
                        data["requests"] = []

                    # Add the new entry
                    data["requests"].append(entry)

                    # Limit the number of entries to prevent the file from growing too large
                    max_entries = 10000
                    if len(data["requests"]) > max_entries:
                        data["requests"] = data["requests"][-max_entries:]

                    # Save the updated data
                    AIDataCollector._save_data(AIDataCollector.REQUESTS_FILE, data)

                # Update usage statistics
                AIDataCollector._update_usage_stats(feature_id, provider, model, parameters, success)

        except Exception as e:
            logger.error(f"Error logging AI request: {str(e)}")

    @staticmethod
    def log_feedback(
        feedback_id: str,
        request_id: str,
        feature_id: str,
        rating: int,
        feedback_text: Optional[str] = None,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log user feedback on an AI response.

        Args:
            feedback_id: Unique identifier for the feedback
            request_id: ID of the request this feedback is for
            feature_id: The ID of the feature that received feedback
            rating: User rating (1-5)
            feedback_text: Optional text feedback
            user_id: ID of the user who provided feedback
            metadata: Additional metadata about the feedback
        """
        try:
            if USE_DATABASE:
                # Use database storage
                with get_db() as db:
                    AIDBService.log_feedback(
                        db=db,
                        feedback_id=feedback_id,
                        request_id=request_id,
                        feature_id=feature_id,
                        rating=rating,
                        feedback_text=feedback_text,
                        user_id=user_id,
                        metadata=metadata
                    )
            else:
                # Use file storage
                # Create the feedback entry
                entry = {
                    "feedback_id": feedback_id,
                    "request_id": request_id,
                    "timestamp": datetime.now().isoformat(),
                    "feature_id": feature_id,
                    "rating": rating,
                    "user_id": user_id
                }

                # Add optional fields if provided
                if feedback_text:
                    entry["feedback_text"] = feedback_text

                if metadata:
                    entry["metadata"] = metadata

                # Load existing data
                with file_lock:
                    data = AIDataCollector._load_data(AIDataCollector.FEEDBACK_FILE)

                    # Initialize feedback array if it doesn't exist
                    if "feedback" not in data:
                        data["feedback"] = []

                    # Add the new entry
                    data["feedback"].append(entry)

                    # Limit the number of entries
                    max_entries = 10000
                    if len(data["feedback"]) > max_entries:
                        data["feedback"] = data["feedback"][-max_entries:]

                    # Save the updated data
                    AIDataCollector._save_data(AIDataCollector.FEEDBACK_FILE, data)

        except Exception as e:
            logger.error(f"Error logging user feedback: {str(e)}")

    @staticmethod
    def _update_usage_stats(
        feature_id: str,
        provider: str,
        model: str,
        parameters: Dict[str, Any],
        success: bool
    ) -> None:
        """
        Update usage statistics for AI features, models, and parameters.

        Args:
            feature_id: The ID of the feature
            provider: The AI provider
            model: The model used
            parameters: The parameters used
            success: Whether the request was successful
        """
        try:
            # Load existing data
            with file_lock:
                data = AIDataCollector._load_data(AIDataCollector.USAGE_FILE)

                # Initialize usage data if it doesn't exist
                if "usage" not in data:
                    data["usage"] = {
                        "features": {},
                        "providers": {},
                        "models": {},
                        "parameters": {}
                    }

                # Update feature usage
                if feature_id not in data["usage"]["features"]:
                    data["usage"]["features"][feature_id] = {
                        "total_requests": 0,
                        "successful_requests": 0
                    }

                data["usage"]["features"][feature_id]["total_requests"] += 1
                if success:
                    data["usage"]["features"][feature_id]["successful_requests"] += 1

                # Update provider usage
                if provider not in data["usage"]["providers"]:
                    data["usage"]["providers"][provider] = {
                        "total_requests": 0,
                        "successful_requests": 0
                    }

                data["usage"]["providers"][provider]["total_requests"] += 1
                if success:
                    data["usage"]["providers"][provider]["successful_requests"] += 1

                # Update model usage
                if model not in data["usage"]["models"]:
                    data["usage"]["models"][model] = {
                        "total_requests": 0,
                        "successful_requests": 0
                    }

                data["usage"]["models"][model]["total_requests"] += 1
                if success:
                    data["usage"]["models"][model]["successful_requests"] += 1

                # Update parameter usage
                for param_name, param_value in parameters.items():
                    param_key = f"{param_name}:{param_value}"
                    if param_key not in data["usage"]["parameters"]:
                        data["usage"]["parameters"][param_key] = {
                            "name": param_name,
                            "value": param_value,
                            "count": 0
                        }

                    data["usage"]["parameters"][param_key]["count"] += 1

                # Save the updated data
                AIDataCollector._save_data(AIDataCollector.USAGE_FILE, data)

        except Exception as e:
            logger.error(f"Error updating usage stats: {str(e)}")
