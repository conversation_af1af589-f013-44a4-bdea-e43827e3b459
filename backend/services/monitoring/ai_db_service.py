"""
Database service for AI monitoring.

This service provides methods for storing and retrieving AI monitoring data from the database.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import uuid
import json
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_

from backend.db.session import get_db
from backend.db.models.ai_monitoring import AIRequest, AIFeedback, AIUsageStats, AIOptimizationHistory

logger = logging.getLogger(__name__)

class AIDBService:
    """
    Service for storing and retrieving AI monitoring data from the database.
    """
    
    @staticmethod
    def log_request(
        db: Session,
        request_id: str,
        feature_id: str,
        provider: str,
        model: str,
        parameters: Dict[str, Any],
        prompt: str,
        response: Optional[str] = None,
        response_time_ms: Optional[float] = None,
        token_count: Optional[int] = None,
        success: bool = True,
        error_type: Optional[str] = None,
        error_message: Optional[str] = None,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AIRequest:
        """
        Log an AI request to the database.
        
        Args:
            db: Database session
            request_id: Unique identifier for the request
            feature_id: The ID of the feature that made the request
            provider: The AI provider used
            model: The model used
            parameters: The parameters used for the request
            prompt: The prompt sent to the AI
            response: The response from the AI
            response_time_ms: The response time in milliseconds
            token_count: The number of tokens used
            success: Whether the request was successful
            error_type: Type of error if the request failed
            error_message: Error message if the request failed
            user_id: ID of the user who made the request
            metadata: Additional metadata about the request
            
        Returns:
            The created AIRequest object
        """
        try:
            # Create the request object
            ai_request = AIRequest(
                request_id=request_id,
                timestamp=datetime.now(),
                feature_id=feature_id,
                provider=provider,
                model=model,
                parameters=parameters,
                prompt_length=len(prompt) if prompt else 0,
                response_length=len(response) if response else None,
                response_time_ms=response_time_ms,
                token_count=token_count,
                success=success,
                error_type=error_type,
                error_message=error_message,
                user_id=user_id,
                metadata=metadata
            )
            
            # Add to database
            db.add(ai_request)
            db.commit()
            db.refresh(ai_request)
            
            # Update usage statistics
            AIDBService._update_usage_stats(
                db=db,
                feature_id=feature_id,
                provider=provider,
                model=model,
                parameters=parameters,
                response_time_ms=response_time_ms,
                token_count=token_count,
                success=success
            )
            
            return ai_request
        except Exception as e:
            db.rollback()
            logger.error(f"Error logging AI request to database: {str(e)}")
            raise
    
    @staticmethod
    def log_feedback(
        db: Session,
        feedback_id: str,
        request_id: str,
        feature_id: str,
        rating: int,
        feedback_text: Optional[str] = None,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AIFeedback:
        """
        Log user feedback on an AI response to the database.
        
        Args:
            db: Database session
            feedback_id: Unique identifier for the feedback
            request_id: ID of the request this feedback is for
            feature_id: The ID of the feature that received feedback
            rating: User rating (1-5)
            feedback_text: Optional text feedback
            user_id: ID of the user who provided feedback
            metadata: Additional metadata about the feedback
            
        Returns:
            The created AIFeedback object
        """
        try:
            # Create the feedback object
            ai_feedback = AIFeedback(
                feedback_id=feedback_id,
                request_id=request_id,
                timestamp=datetime.now(),
                feature_id=feature_id,
                rating=rating,
                feedback_text=feedback_text,
                user_id=user_id,
                metadata=metadata
            )
            
            # Add to database
            db.add(ai_feedback)
            db.commit()
            db.refresh(ai_feedback)
            
            # Update usage statistics with feedback
            AIDBService._update_feedback_stats(
                db=db,
                feature_id=feature_id,
                rating=rating
            )
            
            return ai_feedback
        except Exception as e:
            db.rollback()
            logger.error(f"Error logging AI feedback to database: {str(e)}")
            raise
    
    @staticmethod
    def get_performance_metrics(
        db: Session,
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get performance metrics for AI requests from the database.
        
        Args:
            db: Database session
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of performance metrics
        """
        try:
            # Build query
            query = db.query(AIRequest)
            
            # Apply filters
            if feature_id:
                query = query.filter(AIRequest.feature_id == feature_id)
            
            if time_range_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
                query = query.filter(AIRequest.timestamp >= cutoff_time)
            
            # Execute query
            requests = query.all()
            
            if not requests:
                return {
                    "total_requests": 0,
                    "success_rate": 0,
                    "avg_response_time_ms": 0,
                    "avg_token_count": 0
                }
            
            # Calculate metrics
            total_requests = len(requests)
            successful_requests = sum(1 for r in requests if r.success)
            success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
            
            response_times = [r.response_time_ms for r in requests if r.response_time_ms is not None]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            token_counts = [r.token_count for r in requests if r.token_count is not None]
            avg_token_count = sum(token_counts) / len(token_counts) if token_counts else 0
            
            # Count requests by feature
            feature_counts = {}
            for r in requests:
                feature = r.feature_id
                feature_counts[feature] = feature_counts.get(feature, 0) + 1
            
            # Count requests by model
            model_counts = {}
            for r in requests:
                model = r.model
                model_counts[model] = model_counts.get(model, 0) + 1
            
            # Count error types
            error_counts = {}
            for r in requests:
                if not r.success and r.error_type:
                    error_type = r.error_type
                    error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
            return {
                "total_requests": total_requests,
                "success_rate": round(success_rate, 2),
                "avg_response_time_ms": round(avg_response_time, 2),
                "avg_token_count": round(avg_token_count, 2),
                "requests_per_feature": feature_counts,
                "requests_per_model": model_counts,
                "error_types": error_counts
            }
        except Exception as e:
            logger.error(f"Error getting performance metrics from database: {str(e)}")
            return {
                "total_requests": 0,
                "success_rate": 0,
                "avg_response_time_ms": 0,
                "avg_token_count": 0,
                "error": str(e)
            }
    
    @staticmethod
    def get_user_satisfaction_metrics(
        db: Session,
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get user satisfaction metrics from the database.
        
        Args:
            db: Database session
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of user satisfaction metrics
        """
        try:
            # Build query
            query = db.query(AIFeedback)
            
            # Apply filters
            if feature_id:
                query = query.filter(AIFeedback.feature_id == feature_id)
            
            if time_range_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
                query = query.filter(AIFeedback.timestamp >= cutoff_time)
            
            # Execute query
            feedback = query.all()
            
            if not feedback:
                return {
                    "total_feedback": 0,
                    "avg_rating": 0
                }
            
            # Calculate metrics
            total_feedback = len(feedback)
            ratings = [f.rating for f in feedback]
            avg_rating = sum(ratings) / len(ratings) if ratings else 0
            
            # Count ratings
            rating_distribution = {}
            for f in feedback:
                rating = f.rating
                rating_distribution[str(rating)] = rating_distribution.get(str(rating), 0) + 1
            
            # Count feedback by feature
            feature_counts = {}
            for f in feedback:
                feature = f.feature_id
                feature_counts[feature] = feature_counts.get(feature, 0) + 1
            
            return {
                "total_feedback": total_feedback,
                "avg_rating": round(avg_rating, 2),
                "rating_distribution": rating_distribution,
                "feedback_per_feature": feature_counts
            }
        except Exception as e:
            logger.error(f"Error getting user satisfaction metrics from database: {str(e)}")
            return {
                "total_feedback": 0,
                "avg_rating": 0,
                "error": str(e)
            }
    
    @staticmethod
    def get_settings_usage_stats(
        db: Session,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get settings usage statistics from the database.
        
        Args:
            db: Database session
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of settings usage statistics
        """
        try:
            # Build query for feature and model stats
            query = db.query(AIUsageStats)
            
            # Apply time filter
            if time_range_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
                query = query.filter(AIUsageStats.timestamp >= cutoff_time)
            
            # Execute query
            stats = query.all()
            
            if not stats:
                return {
                    "total_requests": 0,
                    "settings_usage": {}
                }
            
            # Calculate total requests
            total_requests = sum(stat.total_requests for stat in stats if stat.feature_id is not None)
            
            # Group stats by type
            features = {}
            models = {}
            parameters = {}
            
            for stat in stats:
                if stat.feature_id is not None and stat.parameter_name is None and stat.parameter_value is None:
                    # Feature stat
                    features[stat.feature_id] = {
                        "total_requests": stat.total_requests,
                        "successful_requests": stat.successful_requests
                    }
                elif stat.model is not None and stat.parameter_name is None and stat.parameter_value is None:
                    # Model stat
                    models[stat.model] = {
                        "total_requests": stat.total_requests,
                        "successful_requests": stat.successful_requests
                    }
                elif stat.parameter_name is not None and stat.parameter_value is not None:
                    # Parameter stat
                    param_key = f"{stat.parameter_name}"
                    if param_key not in parameters:
                        parameters[param_key] = {
                            "name": stat.parameter_name,
                            "count": 0,
                            "values": {}
                        }
                    
                    parameters[param_key]["count"] += stat.total_requests
                    parameters[param_key]["values"][stat.parameter_value] = stat.total_requests
            
            return {
                "total_requests": total_requests,
                "settings_usage": {
                    "features": features,
                    "models": models,
                    "parameters": parameters
                }
            }
        except Exception as e:
            logger.error(f"Error getting settings usage stats from database: {str(e)}")
            return {
                "total_requests": 0,
                "settings_usage": {},
                "error": str(e)
            }
    
    @staticmethod
    def _update_usage_stats(
        db: Session,
        feature_id: str,
        provider: str,
        model: str,
        parameters: Dict[str, Any],
        response_time_ms: Optional[float],
        token_count: Optional[int],
        success: bool
    ) -> None:
        """
        Update usage statistics in the database.
        
        Args:
            db: Database session
            feature_id: The ID of the feature
            provider: The AI provider
            model: The model used
            parameters: The parameters used
            response_time_ms: The response time in milliseconds
            token_count: The number of tokens used
            success: Whether the request was successful
        """
        try:
            # Update feature stats
            feature_stat = db.query(AIUsageStats).filter(
                AIUsageStats.feature_id == feature_id,
                AIUsageStats.provider == None,
                AIUsageStats.model == None,
                AIUsageStats.parameter_name == None,
                AIUsageStats.parameter_value == None
            ).first()
            
            if feature_stat:
                # Update existing stat
                feature_stat.total_requests += 1
                if success:
                    feature_stat.successful_requests += 1
                
                # Update averages
                if response_time_ms is not None:
                    feature_stat.avg_response_time_ms = (
                        (feature_stat.avg_response_time_ms * (feature_stat.total_requests - 1) + response_time_ms) / 
                        feature_stat.total_requests
                    )
                
                if token_count is not None:
                    feature_stat.avg_token_count = (
                        (feature_stat.avg_token_count * (feature_stat.total_requests - 1) + token_count) / 
                        feature_stat.total_requests
                    )
            else:
                # Create new stat
                feature_stat = AIUsageStats(
                    feature_id=feature_id,
                    provider=None,
                    model=None,
                    parameter_name=None,
                    parameter_value=None,
                    total_requests=1,
                    successful_requests=1 if success else 0,
                    avg_response_time_ms=response_time_ms if response_time_ms is not None else 0,
                    avg_token_count=token_count if token_count is not None else 0
                )
                db.add(feature_stat)
            
            # Update model stats
            model_stat = db.query(AIUsageStats).filter(
                AIUsageStats.feature_id == None,
                AIUsageStats.provider == provider,
                AIUsageStats.model == model,
                AIUsageStats.parameter_name == None,
                AIUsageStats.parameter_value == None
            ).first()
            
            if model_stat:
                # Update existing stat
                model_stat.total_requests += 1
                if success:
                    model_stat.successful_requests += 1
            else:
                # Create new stat
                model_stat = AIUsageStats(
                    feature_id=None,
                    provider=provider,
                    model=model,
                    parameter_name=None,
                    parameter_value=None,
                    total_requests=1,
                    successful_requests=1 if success else 0
                )
                db.add(model_stat)
            
            # Update parameter stats
            for param_name, param_value in parameters.items():
                param_stat = db.query(AIUsageStats).filter(
                    AIUsageStats.feature_id == None,
                    AIUsageStats.provider == None,
                    AIUsageStats.model == None,
                    AIUsageStats.parameter_name == param_name,
                    AIUsageStats.parameter_value == str(param_value)
                ).first()
                
                if param_stat:
                    # Update existing stat
                    param_stat.total_requests += 1
                    if success:
                        param_stat.successful_requests += 1
                else:
                    # Create new stat
                    param_stat = AIUsageStats(
                        feature_id=None,
                        provider=None,
                        model=None,
                        parameter_name=param_name,
                        parameter_value=str(param_value),
                        total_requests=1,
                        successful_requests=1 if success else 0
                    )
                    db.add(param_stat)
            
            db.commit()
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating usage stats in database: {str(e)}")
    
    @staticmethod
    def _update_feedback_stats(
        db: Session,
        feature_id: str,
        rating: int
    ) -> None:
        """
        Update feedback statistics in the database.
        
        Args:
            db: Database session
            feature_id: The ID of the feature
            rating: User rating (1-5)
        """
        try:
            # Update feature stats
            feature_stat = db.query(AIUsageStats).filter(
                AIUsageStats.feature_id == feature_id,
                AIUsageStats.provider == None,
                AIUsageStats.model == None,
                AIUsageStats.parameter_name == None,
                AIUsageStats.parameter_value == None
            ).first()
            
            if feature_stat:
                # Update existing stat
                if feature_stat.total_feedback is None:
                    feature_stat.total_feedback = 0
                if feature_stat.avg_rating is None:
                    feature_stat.avg_rating = 0
                
                # Update averages
                feature_stat.avg_rating = (
                    (feature_stat.avg_rating * feature_stat.total_feedback + rating) / 
                    (feature_stat.total_feedback + 1)
                )
                feature_stat.total_feedback += 1
            
            db.commit()
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating feedback stats in database: {str(e)}")
    
    @staticmethod
    def log_optimization(
        db: Session,
        feature_id: str,
        parameter_name: str,
        old_value: str,
        new_value: str,
        reason: str,
        impact_score: float,
        applied_by: Optional[str] = "system"
    ) -> AIOptimizationHistory:
        """
        Log an optimization to the database.
        
        Args:
            db: Database session
            feature_id: The ID of the feature
            parameter_name: The name of the parameter
            old_value: The old value of the parameter
            new_value: The new value of the parameter
            reason: The reason for the optimization
            impact_score: The impact score of the optimization
            applied_by: Who applied the optimization
            
        Returns:
            The created AIOptimizationHistory object
        """
        try:
            # Create the optimization object
            optimization = AIOptimizationHistory(
                timestamp=datetime.now(),
                feature_id=feature_id,
                parameter_name=parameter_name,
                old_value=old_value,
                new_value=new_value,
                reason=reason,
                impact_score=impact_score,
                applied_by=applied_by
            )
            
            # Add to database
            db.add(optimization)
            db.commit()
            db.refresh(optimization)
            
            return optimization
        except Exception as e:
            db.rollback()
            logger.error(f"Error logging optimization to database: {str(e)}")
            raise
