"""
AI Settings Monitoring Service.

This module provides functions to track AI settings usage and performance.
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import statistics
from threading import Lock

from backend.config import settings

logger = logging.getLogger(__name__)

# Path to the monitoring data file
MONITORING_DATA_FILE = os.path.join(settings.BASE_DIR, "data", "ai_settings_monitoring.json")

# Lock for thread-safe file access
file_lock = Lock()

class AISettingsMonitor:
    """Monitor AI settings usage and performance."""
    
    @staticmethod
    def log_request(
        feature_id: str,
        provider: str,
        model: str,
        parameters: Dict[str, Any],
        advanced_settings: Dict[str, Any],
        response_time_ms: float,
        token_count: int,
        success: bool,
        error_message: Optional[str] = None
    ) -> None:
        """
        Log an AI request with its settings and performance metrics.
        
        Args:
            feature_id: The ID of the feature that made the request
            provider: The AI provider used
            model: The model used
            parameters: The parameters used for the request
            advanced_settings: The advanced settings used for the request
            response_time_ms: The response time in milliseconds
            token_count: The number of tokens used
            success: Whether the request was successful
            error_message: Optional error message if the request failed
        """
        try:
            # Create the monitoring entry
            entry = {
                "timestamp": datetime.now().isoformat(),
                "feature_id": feature_id,
                "provider": provider,
                "model": model,
                "parameters": parameters,
                "advanced_settings": advanced_settings,
                "response_time_ms": response_time_ms,
                "token_count": token_count,
                "success": success
            }
            
            if error_message:
                entry["error_message"] = error_message
            
            # Load existing data
            with file_lock:
                monitoring_data = AISettingsMonitor._load_monitoring_data()
                
                # Add the new entry
                if "requests" not in monitoring_data:
                    monitoring_data["requests"] = []
                
                monitoring_data["requests"].append(entry)
                
                # Limit the number of entries to prevent the file from growing too large
                max_entries = 1000
                if len(monitoring_data["requests"]) > max_entries:
                    monitoring_data["requests"] = monitoring_data["requests"][-max_entries:]
                
                # Save the updated data
                AISettingsMonitor._save_monitoring_data(monitoring_data)
        except Exception as e:
            logger.error(f"Error logging AI request: {str(e)}")
    
    @staticmethod
    def log_user_feedback(
        feature_id: str,
        rating: int,
        feedback_text: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        advanced_settings: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log user feedback on an AI response.
        
        Args:
            feature_id: The ID of the feature that received feedback
            rating: User rating (1-5)
            feedback_text: Optional feedback text
            parameters: Optional parameters used for the request
            advanced_settings: Optional advanced settings used for the request
        """
        try:
            # Create the feedback entry
            entry = {
                "timestamp": datetime.now().isoformat(),
                "feature_id": feature_id,
                "rating": rating
            }
            
            if feedback_text:
                entry["feedback_text"] = feedback_text
            
            if parameters:
                entry["parameters"] = parameters
            
            if advanced_settings:
                entry["advanced_settings"] = advanced_settings
            
            # Load existing data
            with file_lock:
                monitoring_data = AISettingsMonitor._load_monitoring_data()
                
                # Add the new entry
                if "feedback" not in monitoring_data:
                    monitoring_data["feedback"] = []
                
                monitoring_data["feedback"].append(entry)
                
                # Limit the number of entries
                max_entries = 1000
                if len(monitoring_data["feedback"]) > max_entries:
                    monitoring_data["feedback"] = monitoring_data["feedback"][-max_entries:]
                
                # Save the updated data
                AISettingsMonitor._save_monitoring_data(monitoring_data)
        except Exception as e:
            logger.error(f"Error logging user feedback: {str(e)}")
    
    @staticmethod
    def get_performance_metrics(
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get performance metrics for AI requests.
        
        Args:
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of performance metrics
        """
        try:
            with file_lock:
                monitoring_data = AISettingsMonitor._load_monitoring_data()
                
                if "requests" not in monitoring_data:
                    return {
                        "total_requests": 0,
                        "success_rate": 0,
                        "avg_response_time_ms": 0,
                        "avg_token_count": 0
                    }
                
                # Filter requests
                requests = monitoring_data["requests"]
                
                if feature_id:
                    requests = [r for r in requests if r.get("feature_id") == feature_id]
                
                if time_range_hours:
                    cutoff_time = datetime.now().timestamp() - (time_range_hours * 3600)
                    requests = [
                        r for r in requests 
                        if datetime.fromisoformat(r.get("timestamp")).timestamp() > cutoff_time
                    ]
                
                if not requests:
                    return {
                        "total_requests": 0,
                        "success_rate": 0,
                        "avg_response_time_ms": 0,
                        "avg_token_count": 0
                    }
                
                # Calculate metrics
                total_requests = len(requests)
                successful_requests = len([r for r in requests if r.get("success", False)])
                success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
                
                response_times = [r.get("response_time_ms", 0) for r in requests]
                avg_response_time = statistics.mean(response_times) if response_times else 0
                
                token_counts = [r.get("token_count", 0) for r in requests]
                avg_token_count = statistics.mean(token_counts) if token_counts else 0
                
                return {
                    "total_requests": total_requests,
                    "success_rate": success_rate,
                    "avg_response_time_ms": avg_response_time,
                    "avg_token_count": avg_token_count,
                    "requests_per_feature": AISettingsMonitor._count_by_field(requests, "feature_id"),
                    "requests_per_model": AISettingsMonitor._count_by_field(requests, "model"),
                    "error_types": AISettingsMonitor._count_error_types(requests)
                }
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {
                "total_requests": 0,
                "success_rate": 0,
                "avg_response_time_ms": 0,
                "avg_token_count": 0,
                "error": str(e)
            }
    
    @staticmethod
    def get_user_satisfaction_metrics(
        feature_id: Optional[str] = None,
        time_range_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get user satisfaction metrics.
        
        Args:
            feature_id: Optional feature ID to filter by
            time_range_hours: Optional time range in hours to filter by
            
        Returns:
            Dictionary of user satisfaction metrics
        """
        try:
            with file_lock:
                monitoring_data = AISettingsMonitor._load_monitoring_data()
                
                if "feedback" not in monitoring_data:
                    return {
                        "total_feedback": 0,
                        "avg_rating": 0
                    }
                
                # Filter feedback
                feedback = monitoring_data["feedback"]
                
                if feature_id:
                    feedback = [f for f in feedback if f.get("feature_id") == feature_id]
                
                if time_range_hours:
                    cutoff_time = datetime.now().timestamp() - (time_range_hours * 3600)
                    feedback = [
                        f for f in feedback 
                        if datetime.fromisoformat(f.get("timestamp")).timestamp() > cutoff_time
                    ]
                
                if not feedback:
                    return {
                        "total_feedback": 0,
                        "avg_rating": 0
                    }
                
                # Calculate metrics
                total_feedback = len(feedback)
                ratings = [f.get("rating", 0) for f in feedback]
                avg_rating = statistics.mean(ratings) if ratings else 0
                
                return {
                    "total_feedback": total_feedback,
                    "avg_rating": avg_rating,
                    "rating_distribution": AISettingsMonitor._count_ratings(feedback),
                    "feedback_per_feature": AISettingsMonitor._count_by_field(feedback, "feature_id")
                }
        except Exception as e:
            logger.error(f"Error getting user satisfaction metrics: {str(e)}")
            return {
                "total_feedback": 0,
                "avg_rating": 0,
                "error": str(e)
            }
    
    @staticmethod
    def _load_monitoring_data() -> Dict[str, Any]:
        """Load monitoring data from file."""
        if not os.path.exists(MONITORING_DATA_FILE):
            return {}
        
        try:
            with open(MONITORING_DATA_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading monitoring data: {str(e)}")
            return {}
    
    @staticmethod
    def _save_monitoring_data(data: Dict[str, Any]) -> None:
        """Save monitoring data to file."""
        # Ensure the data directory exists
        os.makedirs(os.path.dirname(MONITORING_DATA_FILE), exist_ok=True)
        
        try:
            with open(MONITORING_DATA_FILE, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving monitoring data: {str(e)}")
    
    @staticmethod
    def _count_by_field(items: List[Dict[str, Any]], field: str) -> Dict[str, int]:
        """Count items by a specific field."""
        counts = {}
        for item in items:
            value = item.get(field)
            if value:
                counts[value] = counts.get(value, 0) + 1
        return counts
    
    @staticmethod
    def _count_error_types(requests: List[Dict[str, Any]]) -> Dict[str, int]:
        """Count error types."""
        error_types = {}
        for request in requests:
            if not request.get("success", True) and "error_message" in request:
                error_message = request["error_message"]
                # Extract error type from message
                error_type = error_message.split(":")[0] if ":" in error_message else error_message
                error_types[error_type] = error_types.get(error_type, 0) + 1
        return error_types
    
    @staticmethod
    def _count_ratings(feedback: List[Dict[str, Any]]) -> Dict[str, int]:
        """Count ratings."""
        ratings = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
        for item in feedback:
            rating = item.get("rating")
            if rating and 1 <= rating <= 5:
                ratings[rating] = ratings.get(rating, 0) + 1
        return ratings
