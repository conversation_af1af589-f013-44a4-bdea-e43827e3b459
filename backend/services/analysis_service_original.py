from typing import Dict, List, Optional, Tuple, Any, cast
from sqlalchemy.orm import Session
from sqlalchemy import update, JSON, Column
from sqlalchemy.types import JSON as SQLAlchemyJSON
import pandas as pd
import numpy as np
from math import floor, ceil
from fastapi import HTTPException
import logging
import re
from ..models.track import Track
from ..models.track_analysis import TrackAnalysis, AnalysisStatus
from ..models.health_score import HealthScoreConfig
from ..models.analysis import (
    CollectionAnalysis,
    Track3DPosition,
    KeyNetworkNode,
    KeyNetworkEdge,
    KeyNetworkData,
    BPMCluster,
    BPMAnalysis
)
from ..utils.camelot_rules import CamelotRules
from ..config import settings
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import networkx as nx
from ..models.folder import Folder

class AnalysisService:
    def __init__(self, db: Session):
        self.db = db
        self.mix_styles = settings.mix_styles
        self.bpm_ranges = {}

        for style, config in self.mix_styles.items():
            if 'min_bpm' in config and 'max_bpm' in config:
                self.bpm_ranges[style] = (float(config['min_bpm']), float(config['max_bpm']))
            elif 'bpm_range' in config and isinstance(config['bpm_range'], list) and len(config['bpm_range']) >= 2:
                self.bpm_ranges[style] = (float(config['bpm_range'][0]), float(config['bpm_range'][1]))
            else:
                self.bpm_ranges[style] = (settings.DEFAULT_BPM_RANGE_MIN, settings.DEFAULT_BPM_RANGE_MAX)

    # New methods for track analysis job management
    async def create_analysis_job(self, track_id: int) -> TrackAnalysis:
        """Create a new track analysis job"""
        try:
            # Check if track exists
            track = self.db.query(Track).filter(Track.id == track_id).first()
            if not track:
                raise HTTPException(status_code=404, detail=f"Track {track_id} not found")

            # Check if analysis already exists
            existing_analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.track_id == track_id
            ).first()

            if existing_analysis:
                # Get raw status value from SQLAlchemy column
                status_value = str(existing_analysis.status.scalar() if hasattr(existing_analysis.status, 'scalar') else existing_analysis.status)
                if status_value == str(AnalysisStatus.PROCESSING):
                    return existing_analysis

                # Reset failed or canceled analysis
                if status_value in [str(AnalysisStatus.ERROR), str(AnalysisStatus.CANCELED)]:
                    self.db.execute(
                        update(TrackAnalysis)
                        .where(TrackAnalysis.id == existing_analysis.id)
                        .values(
                            status=str(AnalysisStatus.PENDING),
                            error_message=None
                        )
                    )
                    self.db.commit()
                    self.db.refresh(existing_analysis)
                    return existing_analysis
                return existing_analysis

            # Create new analysis job
            new_analysis = TrackAnalysis(
                track_id=track_id,
                status=str(AnalysisStatus.PENDING)
            )
            self.db.add(new_analysis)
            self.db.commit()
            self.db.refresh(new_analysis)
            return new_analysis

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logging.error(f"Error creating analysis job: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error creating analysis job: {str(e)}"
            )

    async def update_analysis_status(
        self,
        analysis_id: int,
        status: AnalysisStatus,
        error_message: Optional[str] = None,
        analysis_data: Optional[Dict[str, Any]] = None
    ) -> TrackAnalysis:
        """Update the status of an analysis job"""
        try:
            # Use SQLAlchemy update() instead of direct attribute assignment
            update_values: Dict[str, Any] = {'status': str(status)}
            if error_message is not None:
                update_values['error_message'] = error_message
            if analysis_data is not None:
                # Store JSON data properly
                update_values['analysis_data'] = cast(str, analysis_data)
            stmt = (
                update(TrackAnalysis)
                .where(TrackAnalysis.id == analysis_id)
                .values(**update_values)
            )
            result = self.db.execute(stmt)

            # In SQLAlchemy 2.0, use rowcount attribute from Result object
            if result.rowcount == 0:
                raise HTTPException(
                    status_code=404,
                    detail=f"Analysis job {analysis_id} not found"
                )
            self.db.commit()
            return self.db.query(TrackAnalysis).filter(TrackAnalysis.id == analysis_id).first()

        except HTTPException as he:
            raise he
        except Exception as e:
            self.db.rollback()
            logging.error(f"Error updating analysis status: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error updating analysis status: {str(e)}"
            )

    async def get_analysis_status(self, track_id: int) -> Optional[TrackAnalysis]:
        """Get the analysis status for a track"""
        try:
            analysis = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.track_id == track_id
            ).first()
            return analysis

        except Exception as e:
            logging.error(f"Error getting analysis status: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting analysis status: {str(e)}"
            )

    async def delete_analysis(self, track_id: int) -> bool:
        """Delete analysis data for a track"""
        try:
            result = self.db.query(TrackAnalysis).filter(
                TrackAnalysis.track_id == track_id
            ).delete()
            self.db.commit()
            return result > 0

        except Exception as e:
            self.db.rollback()
            logging.error(f"Error deleting analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting analysis: {str(e)}"
            )

    # Existing methods...
    async def analyze_collection(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive analysis of the collection

        Args:
            directory_id: Optional collection directory ID
            folder_id: Optional folder ID to filter tracks by
        """
        try:
            # Query all tracks from the collection
            query = self.db.query(Track)

            # Filter by directory if provided
            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            # Filter by folder if provided, using pattern matching that works
            if folder_id:
                logging.info(f"Filtering tracks by folder_id: {folder_id}")
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_name = folder.name.replace("'", "''")  # Escape single quotes for SQL

                    # Try patterns in order from most specific to most general
                    pattern_options = [
                        f"%/{folder_name}/%",    # Exact match with slashes
                        f"%{folder_name}/%",     # Folder at the start of path
                        f"%/{folder_name}%",     # Folder anywhere in path
                    ]

                    # Try each pattern until we find matches
                    matched = False
                    for pattern in pattern_options:
                        pattern_query = query.filter(Track.directory_path.like(pattern))
                        if pattern_query.count() > 0:
                            query = pattern_query
                            matched = True
                            break

                    # If no matches with specific patterns, try most general pattern
                    if not matched:
                        query = query.filter(Track.directory_path.like(f"%{folder_name}%"))

            tracks = query.all()
            logging.info(f"Found {len(tracks)} tracks for analysis")

            if not tracks:
                # Return empty analysis structure instead of raising an error
                return {
                    'total_tracks': 0,
                    'unique_artists': 0,
                    'bpm_range': (0.0, 0.0),
                    'energy_range': (0.0, 0.0),
                    'key_distribution': {},
                    'artist_distribution': {},
                    'bpm_distribution': {},
                    'energy_distribution': {},
                    'genre_coverage': {},
                    'mix_style_coverage': {},
                    'collection_gaps': ["No tracks found in specified directory"],
                    'recommendations': ["Check if the directory contains supported audio files"],
                    'health_score': {
                        'overall': 0.0,
                        'key_balance': 0.0,
                        'bpm_coverage': 0.0,
                        'energy_balance': 0.0,
                        'artist_diversity': 0.0,
                    }
                }

            # Convert to DataFrame for analysis with proper type handling
            track_data = []
            for track in tracks:
                try:
                    # Get values from SQLAlchemy ORM objects properly
                    track_dict = track.__dict__
                    bpm = float(track_dict.get('bpm', 0)) if track_dict.get('bpm') else None
                    energy = float(track_dict.get('energy', 0)) if track_dict.get('energy') else None
                    key = str(track_dict.get('key', '1A')) if track_dict.get('key') else '1A'

                    track_data.append({
                        'artist': str(track_dict.get('artist', '')),
                        'title': str(track_dict.get('title', '')),
                        'bpm': bpm,
                        'key': key,
                        'energy': energy
                    })
                except (ValueError, TypeError, AttributeError) as e:
                    logging.warning(f"Skipping track with invalid data: {e}")
                    continue

            if not track_data:
                raise ValueError("No valid tracks found after data validation")

            df = pd.DataFrame(track_data)

            # Calculate basic stats
            stats = {
                'total_tracks': len(df),
                'unique_artists': df['artist'].nunique(),
                'bpm_range': (
                    float(df['bpm'].min()) if not df['bpm'].isna().all() else 0.0,
                    float(df['bpm'].max()) if not df['bpm'].isna().all() else 0.0
                ),
                'energy_range': (
                    float(df['energy'].min()) if not df['energy'].isna().all() else 0.0,
                    float(df['energy'].max()) if not df['energy'].isna().all() else 0.0
                )
            }

            # Calculate distributions
            stats.update({
                'key_distribution': self._analyze_key_distribution(df),
                'artist_distribution': self._analyze_artist_distribution(df),
                'bpm_distribution': self._analyze_bpm_distribution(df),
                'energy_distribution': self._analyze_energy_distribution(df),
                'genre_coverage': self._analyze_genre_coverage(df),
                'mix_style_coverage': self._analyze_mix_style_coverage(df)
            })

            # Calculate health score
            health_score = self._calculate_collection_health(df)
            collection_gaps = self._identify_collection_gaps(df)
            recommendations = self._generate_recommendations(df)

            # Convert detailed gaps to simple list for backward compatibility
            simple_gaps = []
            for gap_type, gaps in collection_gaps.items():
                for gap in gaps:
                    simple_gaps.append(gap['description'])

            # Convert detailed recommendations to simple list for backward compatibility
            simple_recommendations = []
            for rec_type, recs in recommendations.items():
                for rec in recs:
                    simple_recommendations.append(rec['description'])

            # Add the rest of the analysis data
            stats.update({
                'health_score': health_score,
                'collection_gaps': simple_gaps,
                'recommendations': simple_recommendations,
                'detailed_gaps': collection_gaps,
                'detailed_recommendations': recommendations
            })

            return stats

        except Exception as e:
            logging.error(f"Error in collection analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error analyzing collection: {str(e)}"
            )

    def _analyze_key_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze key distribution and identify imbalances"""
        return df['key'].value_counts().to_dict()

    def _analyze_artist_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze artist distribution and identify potential overrepresentation"""
        return df['artist'].value_counts().to_dict()

    def _analyze_bpm_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze BPM distribution across common ranges"""
        try:
            if len(df) == 0 or 'bpm' not in df.columns:
                return {'No Data': 0}

            # Filter out any invalid BPM values
            valid_bpms = df[df['bpm'].notna() & (df['bpm'] > 0)]['bpm'].astype(float)

            if len(valid_bpms) == 0:
                return {'No Valid BPM Data': 0}

            # Get min and max BPM from configured styles or use defaults
            try:
                min_bpm = min(style_range[0] for style_range in self.bpm_ranges.values())
                max_bpm = max(style_range[1] for style_range in self.bpm_ranges.values())
            except (ValueError, AttributeError):
                min_bpm = 90  # Default minimum BPM
                max_bpm = 140  # Default maximum BPM

            # Create BPM ranges starting from min to max in 5 BPM increments
            range_start = floor(valid_bpms.min() / 5) * 5  # Round down to nearest 5
            range_end = ceil(valid_bpms.max() / 5) * 5     # Round up to nearest 5
            bpm_ranges = {}

            for start in range(range_start, range_end + 1, 5):
                range_name = f'{start}-{start+5}'
                count = len(valid_bpms[(valid_bpms >= start) & (valid_bpms < start + 5)])
                if count > 0:  # Only include ranges that have tracks
                    bpm_ranges[range_name] = count

            return bpm_ranges if bpm_ranges else {'No Tracks': 0}

        except Exception as e:
            logging.error(f"Error analyzing BPM distribution: {str(e)}")
            return {'Error': 0}

    def _analyze_energy_distribution(self, df: pd.DataFrame) -> Dict[str, float]:
        """Analyze energy level distribution"""
        energy_ranges = {
            'Low (0-0.3)': (0, 0.3),
            'Medium (0.3-0.6)': (0.3, 0.6),
            'High (0.6-1.0)': (0.6, 1.0)
        }

        distribution = {}
        for range_name, (min_e, max_e) in energy_ranges.items():
            count = len(df[(df['energy'] >= min_e) & (df['energy'] < max_e)])
            distribution[range_name] = count

        return distribution

    def _analyze_genre_coverage(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze genre coverage and identify missing genres"""
        if 'genre' not in df.columns:
            # Try to infer genres from track titles and artists
            genres = self._infer_genres_from_metadata(df)
            return genres

        # Clean up genre values
        df['genre'] = df['genre'].fillna('Unknown')
        df['genre'] = df['genre'].apply(lambda x: x if x and str(x).strip() else 'Unknown')

        # Get genre counts
        genre_counts = df['genre'].value_counts().to_dict()

        # Add common genres that are missing
        common_genres = [
            'House', 'Techno', 'Trance', 'Drum & Bass', 'Dubstep',
            'Hip Hop', 'R&B', 'Pop', 'Rock', 'Ambient', 'Downtempo'
        ]

        for genre in common_genres:
            if genre not in genre_counts:
                genre_counts[genre] = 0

        return genre_counts

    def _infer_genres_from_metadata(self, df: pd.DataFrame) -> Dict[str, int]:
        """Infer genres from track titles and artists when genre data is missing"""
        genre_keywords = {
            'House': ['house', 'deep house', 'tech house', 'progressive house'],
            'Techno': ['techno', 'minimal', 'industrial'],
            'Trance': ['trance', 'uplifting', 'psytrance', 'goa'],
            'Drum & Bass': ['drum and bass', 'drum & bass', 'dnb', 'jungle'],
            'Dubstep': ['dubstep', 'brostep', 'riddim'],
            'Hip Hop': ['hip hop', 'hip-hop', 'rap', 'trap'],
            'R&B': ['r&b', 'rnb', 'soul'],
            'Pop': ['pop', 'dance pop', 'synth pop'],
            'Rock': ['rock', 'indie', 'alternative'],
            'Ambient': ['ambient', 'chillout', 'downtempo'],
            'Downtempo': ['downtempo', 'trip hop', 'lofi']
        }

        inferred_genres = {genre: 0 for genre in genre_keywords.keys()}

        # Check each track's title and artist for genre keywords
        for _, row in df.iterrows():
            title = str(row.get('title', '')).lower()
            artist = str(row.get('artist', '')).lower()
            text = f"{title} {artist}"

            matched = False
            for genre, keywords in genre_keywords.items():
                if any(keyword in text for keyword in keywords):
                    inferred_genres[genre] += 1
                    matched = True

            if not matched:
                inferred_genres['Unknown'] = inferred_genres.get('Unknown', 0) + 1

        return inferred_genres

    def _analyze_mix_style_coverage(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Analyze coverage for different mix styles based on BPM, key, and energy"""
        if len(df) == 0:
            return {}

        coverage = {}
        for style_name, style_config in self.mix_styles.items():
            # Get BPM range for this style
            min_bpm = float(style_config.get('min_bpm', style_config.get('bpm_range', [90, 140])[0]))
            max_bpm = float(style_config.get('max_bpm', style_config.get('bpm_range', [90, 140])[1]))

            # Get energy range for this style (if defined)
            min_energy = float(style_config.get('min_energy', 0))
            max_energy = float(style_config.get('max_energy', 10))

            # Count tracks that match BPM range
            bpm_matches = df[(df['bpm'] >= min_bpm) & (df['bpm'] <= max_bpm)]
            bpm_match_count = len(bpm_matches)
            bpm_match_percentage = (bpm_match_count / len(df)) * 100

            # Count tracks that match energy range (if energy data is available)
            energy_match_count = 0
            energy_match_percentage = 0
            if 'energy' in df.columns and not df['energy'].isna().all():
                energy_matches = df[(df['energy'] >= min_energy) & (df['energy'] <= max_energy)]
                energy_match_count = len(energy_matches)
                energy_match_percentage = (energy_match_count / len(df)) * 100

            # Count tracks that match both BPM and energy
            full_match_count = 0
            full_match_percentage = 0
            if 'energy' in df.columns and not df['energy'].isna().all():
                full_matches = df[
                    (df['bpm'] >= min_bpm) &
                    (df['bpm'] <= max_bpm) &
                    (df['energy'] >= min_energy) &
                    (df['energy'] <= max_energy)
                ]
                full_match_count = len(full_matches)
                full_match_percentage = (full_match_count / len(df)) * 100
            else:
                # If no energy data, full match is same as BPM match
                full_match_count = bpm_match_count
                full_match_percentage = bpm_match_percentage

            # Get example tracks for this style
            example_tracks = []
            if full_match_count > 0:
                # Get up to 5 example tracks that match this style
                matches = df[
                    (df['bpm'] >= min_bpm) &
                    (df['bpm'] <= max_bpm)
                ]
                if 'energy' in df.columns and not df['energy'].isna().all():
                    matches = matches[
                        (matches['energy'] >= min_energy) &
                        (matches['energy'] <= max_energy)
                    ]

                # Get a sample of tracks
                sample_size = min(5, len(matches))
                if sample_size > 0:
                    sample = matches.sample(sample_size)
                    for _, track in sample.iterrows():
                        example_tracks.append({
                            'title': track.get('title', 'Unknown'),
                            'artist': track.get('artist', 'Unknown'),
                            'bpm': track.get('bpm', 0),
                            'key': track.get('key', 'Unknown'),
                            'energy': track.get('energy', 0)
                        })

            # Store coverage data for this style
            coverage[style_name] = {
                'bpm_range': f"{min_bpm}-{max_bpm}",
                'energy_range': f"{min_energy}-{max_energy}",
                'bpm_match_count': bpm_match_count,
                'bpm_match_percentage': bpm_match_percentage,
                'energy_match_count': energy_match_count,
                'energy_match_percentage': energy_match_percentage,
                'full_match_count': full_match_count,
                'full_match_percentage': full_match_percentage,
                'example_tracks': example_tracks
            }

        return coverage

    def _identify_collection_gaps(self, df: pd.DataFrame) -> Dict[str, List[Dict[str, Any]]]:
        """Identify gaps in the collection that could affect mix generation"""
        gaps = {
            'bpm_gaps': [],
            'key_gaps': [],
            'energy_gaps': [],
            'genre_gaps': [],
            'transition_gaps': []
        }

        # Check BPM coverage
        for style_name, style_config in self.mix_styles.items():
            # Get BPM range for this style
            min_bpm = float(style_config.get('min_bpm', style_config.get('bpm_range', [90, 140])[0]))
            max_bpm = float(style_config.get('max_bpm', style_config.get('bpm_range', [90, 140])[1]))

            suitable_tracks = len(df[(df['bpm'] >= min_bpm) & (df['bpm'] <= max_bpm)])
            min_threshold = 10  # Minimum threshold for good coverage

            if suitable_tracks < min_threshold:
                gaps['bpm_gaps'].append({
                    'type': 'style_coverage',
                    'style': style_name,
                    'bpm_range': f"{min_bpm}-{max_bpm}",
                    'track_count': suitable_tracks,
                    'min_threshold': min_threshold,
                    'description': f"Low coverage for {style_name} style (BPM {min_bpm}-{max_bpm})",
                    'severity': 'high' if suitable_tracks == 0 else 'medium'
                })

        # Check for BPM range gaps
        if not df['bpm'].isna().all():
            valid_bpms = df[df['bpm'].notna() & (df['bpm'] > 0)]['bpm'].astype(float)
            if len(valid_bpms) > 0:
                # Check for gaps in 5 BPM increments
                range_start = floor(valid_bpms.min() / 5) * 5  # Round down to nearest 5
                range_end = ceil(valid_bpms.max() / 5) * 5     # Round up to nearest 5

                for start in range(range_start, range_end + 1, 5):
                    count = len(valid_bpms[(valid_bpms >= start) & (valid_bpms < start + 5)])
                    if count < 3:  # Less than 3 tracks in this BPM range
                        gaps['bpm_gaps'].append({
                            'type': 'bpm_range',
                            'bpm_range': f"{start}-{start+5}",
                            'track_count': count,
                            'min_threshold': 3,
                            'description': f"Few or no tracks in BPM range {start}-{start+5}",
                            'severity': 'medium' if count == 0 else 'low'
                        })

        # Check key distribution
        key_counts = df['key'].value_counts()
        for key in CamelotRules.get_compatible_keys("1A"):  # Check all possible keys
            min_threshold = 5  # Minimum threshold for good coverage
            count = key_counts.get(key, 0)

            if count < min_threshold:
                gaps['key_gaps'].append({
                    'type': 'key_coverage',
                    'key': key,
                    'track_count': count,
                    'min_threshold': min_threshold,
                    'description': f"Low coverage for key {key}",
                    'severity': 'high' if count == 0 else 'medium'
                })

        # Check for transition gaps between keys
        for key in key_counts.index:
            compatible_keys = CamelotRules.get_compatible_keys(key)
            for compatible_key in compatible_keys:
                if compatible_key not in key_counts or key_counts[compatible_key] < 3:
                    gaps['transition_gaps'].append({
                        'type': 'key_transition',
                        'from_key': key,
                        'to_key': compatible_key,
                        'track_count': key_counts.get(compatible_key, 0),
                        'min_threshold': 3,
                        'description': f"Limited transition options from key {key} to {compatible_key}",
                        'severity': 'medium'
                    })

        # Check energy distribution
        if 'energy' in df.columns and not df['energy'].isna().all():
            energy_ranges = {
                'Low (0-0.3)': (0, 0.3),
                'Medium (0.3-0.6)': (0.3, 0.6),
                'High (0.6-1.0)': (0.6, 1.0)
            }

            for range_name, (min_e, max_e) in energy_ranges.items():
                count = len(df[(df['energy'] >= min_e) & (df['energy'] < max_e)])
                min_threshold = len(df) * 0.1  # At least 10% of collection

                if count < min_threshold:
                    gaps['energy_gaps'].append({
                        'type': 'energy_range',
                        'energy_range': range_name,
                        'track_count': count,
                        'min_threshold': min_threshold,
                        'description': f"Low coverage for energy range {range_name}",
                        'severity': 'medium' if count == 0 else 'low'
                    })

        # Check genre distribution
        if 'genre' in df.columns:
            genre_counts = df['genre'].value_counts()
            common_genres = [
                'House', 'Techno', 'Trance', 'Drum & Bass', 'Dubstep',
                'Hip Hop', 'R&B', 'Pop', 'Rock', 'Ambient', 'Downtempo'
            ]

            for genre in common_genres:
                count = genre_counts.get(genre, 0)
                if count < 5:  # Less than 5 tracks in this genre
                    gaps['genre_gaps'].append({
                        'type': 'genre_coverage',
                        'genre': genre,
                        'track_count': count,
                        'min_threshold': 5,
                        'description': f"Low coverage for {genre} genre",
                        'severity': 'low'
                    })

        return gaps

    def _generate_recommendations(self, df: pd.DataFrame) -> Dict[str, List[Dict[str, Any]]]:
        """Generate recommendations for improving the collection"""
        recommendations = {
            'track_additions': [],
            'organization_suggestions': [],
            'mix_suggestions': [],
            'transition_suggestions': []
        }

        # Get collection gaps
        gaps = self._identify_collection_gaps(df)

        # Generate recommendations based on BPM gaps
        for gap in gaps.get('bpm_gaps', []):
            if gap['type'] == 'style_coverage' and gap['track_count'] < gap['min_threshold']:
                style = gap['style']
                bpm_range = gap['bpm_range']

                recommendations['track_additions'].append({
                    'type': 'bpm_range',
                    'style': style,
                    'bpm_range': bpm_range,
                    'description': f"Add more tracks in the {bpm_range} BPM range for {style} style",
                    'priority': 'high' if gap['track_count'] == 0 else 'medium',
                    'example_artists': self._get_example_artists_for_style(style)
                })

        # Generate recommendations based on key gaps
        for gap in gaps.get('key_gaps', []):
            key = gap['key']
            recommendations['track_additions'].append({
                'type': 'key',
                'key': key,
                'description': f"Add more tracks in key {key}",
                'priority': 'high' if gap['track_count'] == 0 else 'medium',
                'example_tracks': self._get_example_tracks_for_key(key)
            })

        # Generate recommendations based on energy gaps
        for gap in gaps.get('energy_gaps', []):
            energy_range = gap['energy_range']
            recommendations['track_additions'].append({
                'type': 'energy',
                'energy_range': energy_range,
                'description': f"Add more tracks in energy range {energy_range}",
                'priority': 'medium',
                'example_genres': self._get_example_genres_for_energy(energy_range)
            })

        # Generate recommendations based on genre gaps
        for gap in gaps.get('genre_gaps', []):
            genre = gap['genre']
            recommendations['track_additions'].append({
                'type': 'genre',
                'genre': genre,
                'description': f"Add more tracks in {genre} genre",
                'priority': 'low',
                'example_artists': self._get_example_artists_for_genre(genre)
            })

        # Generate organization suggestions
        if len(df) > 20:  # Only suggest organization for larger collections
            # Suggest organizing by BPM ranges
            recommendations['organization_suggestions'].append({
                'type': 'bpm_organization',
                'description': "Organize tracks into BPM-based playlists for easier mix creation",
                'details': "Create playlists for BPM ranges like 90-100, 100-110, 110-120, etc.",
                'priority': 'high'
            })

            # Suggest organizing by key
            recommendations['organization_suggestions'].append({
                'type': 'key_organization',
                'description': "Organize tracks by key for harmonic mixing",
                'details': "Create playlists for each key or group compatible keys together",
                'priority': 'medium'
            })

            # Suggest organizing by energy level
            if 'energy' in df.columns and not df['energy'].isna().all():
                recommendations['organization_suggestions'].append({
                    'type': 'energy_organization',
                    'description': "Organize tracks by energy level for better flow control",
                    'details': "Create playlists for low, medium, and high energy tracks",
                    'priority': 'medium'
                })

        # Generate mix suggestions
        style_coverage = self._analyze_mix_style_coverage(df)
        for style, coverage_data in style_coverage.items():
            if coverage_data['full_match_count'] >= 10:  # Enough tracks for a mix
                recommendations['mix_suggestions'].append({
                    'type': 'style_mix',
                    'style': style,
                    'description': f"Create a {style} mix with your collection",
                    'track_count': coverage_data['full_match_count'],
                    'example_tracks': coverage_data.get('example_tracks', [])[:3],
                    'priority': 'high' if coverage_data['full_match_percentage'] > 50 else 'medium'
                })

        # Generate transition suggestions
        if len(gaps.get('transition_gaps', [])) > 0:
            recommendations['transition_suggestions'].append({
                'type': 'transition_techniques',
                'description': "Learn advanced transition techniques for challenging key changes",
                'details': "Use EQ, effects, and acapellas to smooth transitions between tracks with different keys",
                'priority': 'medium'
            })

        return recommendations

    def _get_example_artists_for_style(self, style: str) -> List[str]:
        """Get example artists for a specific style"""
        style_artists = {
            'House': ['Disclosure', 'Kerri Chandler', 'Franky Rizardo'],
            'Deep House': ['Lane 8', 'Yotto', 'Ben Böhmer'],
            'Tech House': ['Fisher', 'Chris Lake', 'Hot Since 82'],
            'Techno': ['Charlotte de Witte', 'Amelie Lens', 'Adam Beyer'],
            'Progressive House': ['Eric Prydz', 'Cristoph', 'Grum'],
            'Trance': ['Above & Beyond', 'Armin van Buuren', 'Ilan Bluestone'],
            'Drum & Bass': ['Netsky', 'Wilkinson', 'Sub Focus'],
            'Dubstep': ['Skrillex', 'Excision', 'Zeds Dead'],
            'Hip Hop': ['Kendrick Lamar', 'J. Cole', 'Drake'],
            'R&B': ['The Weeknd', 'SZA', 'H.E.R.'],
            'Pop': ['Dua Lipa', 'The Chainsmokers', 'Calvin Harris'],
            'Downtempo': ['Bonobo', 'Tycho', 'Four Tet']
        }
        return style_artists.get(style, ['Various Artists'])

    def _get_example_tracks_for_key(self, key: str) -> List[str]:
        """Get example track suggestions for a specific key"""
        # This would ideally come from a database of popular tracks by key
        # For now, return generic suggestions
        return [f"Search for popular tracks in {key} on Beatport or other music stores"]

    def _get_example_genres_for_energy(self, energy_range: str) -> List[str]:
        """Get example genres for a specific energy range"""
        energy_genres = {
            'Low (0-0.3)': ['Ambient', 'Downtempo', 'Chillout', 'Lo-fi'],
            'Medium (0.3-0.6)': ['Deep House', 'Progressive House', 'Melodic Techno', 'R&B'],
            'High (0.6-1.0)': ['Techno', 'Drum & Bass', 'Dubstep', 'Big Room House']
        }
        return energy_genres.get(energy_range, ['Various Genres'])

    def _get_example_artists_for_genre(self, genre: str) -> List[str]:
        """Get example artists for a specific genre"""
        genre_artists = {
            'House': ['Disclosure', 'Kerri Chandler', 'Franky Rizardo'],
            'Techno': ['Charlotte de Witte', 'Amelie Lens', 'Adam Beyer'],
            'Trance': ['Above & Beyond', 'Armin van Buuren', 'Ilan Bluestone'],
            'Drum & Bass': ['Netsky', 'Wilkinson', 'Sub Focus'],
            'Dubstep': ['Skrillex', 'Excision', 'Zeds Dead'],
            'Hip Hop': ['Kendrick Lamar', 'J. Cole', 'Drake'],
            'R&B': ['The Weeknd', 'SZA', 'H.E.R.'],
            'Pop': ['Dua Lipa', 'The Chainsmokers', 'Calvin Harris'],
            'Rock': ['Tame Impala', 'Arctic Monkeys', 'The Killers'],
            'Ambient': ['Brian Eno', 'Stars of the Lid', 'Aphex Twin'],
            'Downtempo': ['Bonobo', 'Tycho', 'Four Tet']
        }
        return genre_artists.get(genre, ['Various Artists'])

    def _calculate_collection_health(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate various health metrics for the collection"""
        if len(df) == 0:
            return {
                'overall': 0.0,
                'key_balance': 0.0,
                'bpm_coverage': 0.0,
                'energy_balance': 0.0,
                'artist_diversity': 0.0,
            }

        # Calculate key balance
        key_counts = df['key'].value_counts()
        key_balance = 1 - (key_counts.std() / key_counts.mean() if len(key_counts) > 0 else 1)
        key_balance = max(0, min(1, key_balance)) * 100

        # Calculate BPM coverage - check all configured BPM ranges
        bpm_coverage_scores = []
        for style, (min_bpm, max_bpm) in self.bpm_ranges.items():
            tracks_in_range = len(df[(df['bpm'] >= min_bpm) & (df['bpm'] <= max_bpm)])
            coverage = tracks_in_range / len(df) if len(df) > 0 else 0
            bpm_coverage_scores.append(coverage)
        bpm_coverage = sum(bpm_coverage_scores) / len(bpm_coverage_scores) if bpm_coverage_scores else 0
        bpm_coverage *= 100  # Convert to percentage

        # Calculate energy balance
        energy_dist = df['energy'].value_counts(bins=10)
        energy_balance = 1 - (energy_dist.std() / energy_dist.mean() if len(energy_dist) > 0 else 1)
        energy_balance = max(0, min(1, energy_balance)) * 100

        # Calculate artist diversity
        artist_counts = df['artist'].value_counts()
        artist_diversity = len(artist_counts) / len(df) * 100

        # Calculate overall health
        overall = np.mean([key_balance, bpm_coverage, energy_balance, artist_diversity])

        return {
            'overall': overall,
            'key_balance': key_balance,
            'bpm_coverage': bpm_coverage,
            'energy_balance': energy_balance,
            'artist_diversity': artist_diversity,
        }

    def get_collection_health(self, folder_id: Optional[str] = None) -> Dict[str, float]:
        """Get the current health metrics for the collection

        Args:
            folder_id: Optional folder ID to filter tracks by
        """
        try:
            # Query all tracks from the database - use the approach that works in the collection endpoint
            query = self.db.query(Track)

            # Filter by folder if provided, using pattern matching that works
            if folder_id:
                logging.info(f"Filtering health score by folder_id: {folder_id}")
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_name = folder.name.replace("'", "''")  # Escape single quotes for SQL

                    # Try patterns in order from most specific to most general
                    pattern_options = [
                        f"%/{folder_name}/%",    # Exact match with slashes
                        f"%{folder_name}/%",     # Folder at the start of path
                        f"%/{folder_name}%",     # Folder anywhere in path
                    ]

                    # Try each pattern until we find matches
                    matched = False
                    for pattern in pattern_options:
                        pattern_query = query.filter(Track.directory_path.like(pattern))
                        if pattern_query.count() > 0:
                            query = pattern_query
                            matched = True
                            break

                    # If no matches with specific patterns, try most general pattern
                    if not matched:
                        query = query.filter(Track.directory_path.like(f"%{folder_name}%"))

            tracks = query.all()
            logging.info(f"Found {len(tracks)} tracks for health analysis")

            if not tracks:
                return {
                    'overall': 0.0,
                    'key_balance': 0.0,
                    'bpm_coverage': 0.0,
                    'energy_balance': 0.0,
                    'artist_diversity': 0.0,
                }

            df = pd.DataFrame([
                {
                    'artist': t.artist,
                    'title': t.title,
                    'bpm': t.bpm,
                    'key': t.key,
                    'energy': t.energy
                } for t in tracks
            ])

            return self._calculate_collection_health(df)

        except Exception as e:
            logging.error(f"Error calculating collection health: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error calculating collection health: {str(e)}"
            )

    def _normalize_key(self, key: str) -> str:
        """Normalize key to standard format (e.g., '7A/2', '7Am', '7a' -> '7A')"""
        if not key:
            return '1A'

        # Remove everything after '/' if present
        key = key.split('/')[0]

        # Extract number and letter using regex
        match = re.match(r'(\d+)([AaBbmM]+)', key)
        if not match:
            return '1A'

        number, letter = match.groups()

        # Normalize the letter part
        if letter.upper().startswith('B') or letter.endswith('m'):
            letter = 'B'
        else:
            letter = 'A'

        return f"{number}{letter}"

    async def analyze_track_relationships(self, directory_id: Optional[str] = None) -> Dict:
        """Analyze track relationships in 3D space with optimized data processing"""
        try:
            # Execute query once and cache results
            query = self.db.query(Track.id, Track.key, Track.energy, Track.bpm)
            if directory_id:
                query = query.filter(
                    (Track.directory_id == directory_id) |
                    (Track.directory == directory_id)
                )
            tracks = query.all()

            if not tracks:
                return {'positions': [], 'isolated_tracks': [], 'clusters': []}

            # Create DataFrame with explicit type conversions
            df = pd.DataFrame([{
                'id': int(t.id),
                'key': self._normalize_key(str(t.key or '1A')),
                'energy': float(t.energy or 0.0),
                'bpm': float(t.bpm or 0.0)
            } for t in tracks])

            # Vectorized operations for key position calculation with explicit types
            key_numbers = pd.to_numeric(df['key'].str.extract(r'(\d+)')[0], errors='coerce').fillna(1).astype(int)
            key_types = df['key'].str.extract(r'([AB])')[0].fillna('A')
            key_positions = ((key_numbers - 1) * 2).astype(float).values
            key_positions[key_types == 'B'] += 1

            positions = []
            isolated_tracks = []

            # Convert to numpy arrays with explicit dtypes
            key_positions_np = np.array(key_positions, dtype=float)
            energy_np = df['energy'].to_numpy(dtype=float)
            bpm_np = df['bpm'].to_numpy(dtype=float)

            for i in range(len(df)):
                # Use numpy broadcasting for efficient distance calculations
                key_diffs = np.minimum(
                    np.abs(key_positions_np[i] - key_positions_np),
                    24 - np.abs(key_positions_np[i] - key_positions_np)
                )
                energy_diffs = np.abs(energy_np[i] - energy_np)
                bpm_diffs = np.abs(bpm_np[i] - bpm_np)

                # Count nearby tracks using vectorized operations with proper shapes
                mask = (key_diffs <= 2) & (energy_diffs <= 1) & (bpm_diffs <= 8)
                nearby = np.sum(mask & (np.arange(len(df)) != i))

                pos = Track3DPosition(
                    x=float(key_positions_np[i]),
                    y=float(energy_np[i]),
                    z=float(bpm_np[i]),
                    track_id=int(df['id'].iloc[i]),
                    key=str(df['key'].iloc[i]),
                    compatibility_score=float(nearby/len(df)),
                    is_isolated=nearby < 3,
                    nearby_tracks=int(nearby)
                )

                positions.append(pos)
                if nearby < 3:
                    isolated_tracks.append(pos)

            # Use numpy for cluster analysis
            clusters = self._identify_bpm_clusters(bpm_np)

            return {
                'positions': positions,
                'isolated_tracks': isolated_tracks,
                'clusters': clusters
            }

        except Exception as e:
            logging.error(f"Error in track relationships analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=str(e)
            )

    async def get_advanced_bpm_analysis(self, directory_id: Optional[str] = None) -> BPMAnalysis:
        """Get advanced BPM analysis including clusters and transition matrix"""
        try:
            query = self.db.query(Track)
            if directory_id:
                query = query.filter(
                    (Track.directory_id == directory_id) |
                    (Track.directory == directory_id)
                )
            tracks = query.all()

            if not tracks:
                return BPMAnalysis(
                    mean=0.0,
                    median=0.0,
                    std=0.0,
                    skew=0.0,
                    clusters=[],
                    transition_matrix={}
                )

            # Convert to numpy array with explicit dtype
            bpms = np.array([float(t.bpm or 0.0) for t in tracks], dtype=float)
            bpm_series = pd.Series(bpms)

            # Calculate basic stats with proper type conversions
            return BPMAnalysis(
                mean=float(np.mean(bpms)),
                median=float(np.median(bpms)),
                std=float(np.std(bpms)),
                skew=float(bpm_series.skew()),
                clusters=self._identify_bpm_clusters(bpms),
                transition_matrix=self._calculate_transition_matrix(bpms)
            )

        except Exception as e:
            logging.error(f"Error in advanced BPM analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error in BPM analysis: {str(e)}"
            )

    def _calculate_transition_matrix(self, bpm_values: np.ndarray) -> Dict[int, Dict[int, float]]:
        """Calculate BPM transition difficulty matrix"""
        if len(bpm_values) < 2:
            return {}

        # Filter out zero BPM values to prevent division by zero
        filtered_bpms = bpm_values[bpm_values > 0]
        if len(filtered_bpms) < 1:
            return {}

        bpm_ranges = list(range(int(min(filtered_bpms)), int(max(filtered_bpms)) + 5, 5))
        transition_matrix = {}

        for start_bpm in bpm_ranges:
            transition_matrix[start_bpm] = {}
            for end_bpm in bpm_ranges:
                # Avoid division by zero
                if start_bpm == 0:
                    transition_matrix[start_bpm][end_bpm] = 1.0  # Maximum difficulty for zero BPM
                else:
                    pitch_change = abs(1 - end_bpm/start_bpm)
                    transition_matrix[start_bpm][end_bpm] = min(1.0, pitch_change * 10)

        return transition_matrix

    def _identify_bpm_clusters(self, bpm_values: np.ndarray) -> List[Dict[str, float]]:
        """Identify natural BPM clusters in collection"""
        if len(bpm_values) < 2:
            return []

        try:
            # Convert and clean input to correct numpy dtype
            bpm_values = bpm_values.astype(float)
            valid_bpms = bpm_values[~np.isnan(bpm_values)]
            if len(valid_bpms) < 2:
                return []

            # Ensure 2D array for sklearn
            X = valid_bpms.reshape(-1, 1)

            # Try different numbers of clusters
            best_n_clusters = 2
            best_score = -1.0

            for n_clusters in range(2, min(9, len(valid_bpms))):
                kmeans = KMeans(n_clusters=n_clusters, n_init=5, random_state=42)
                cluster_labels = kmeans.fit_predict(X)

                try:
                    score = silhouette_score(X, cluster_labels)
                    if score > best_score:
                        best_score = score
                        best_n_clusters = n_clusters
                except Exception as cluster_error:
                    logging.debug(f"Silhouette score failed for {n_clusters} clusters: {str(cluster_error)}")
                    continue

            # Final clustering with optimal number of clusters
            final_kmeans = KMeans(n_clusters=best_n_clusters, n_init=10, random_state=42)
            labels = final_kmeans.fit_predict(X)

            clusters = []
            for i in range(best_n_clusters):
                cluster_bpms = valid_bpms[labels == i]
                if len(cluster_bpms) > 0:
                    clusters.append({
                        'center': float(np.mean(cluster_bpms)),
                        'min': float(np.min(cluster_bpms)),
                        'max': float(np.max(cluster_bpms)),
                        'size': int(np.sum(labels == i))
                    })

            return sorted(clusters, key=lambda x: x['center'])

        except Exception as e:
            logging.error(f"Error identifying BPM clusters: {str(e)}")
            return []

    def _calculate_advanced_bpm_stats(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate advanced BPM statistics"""
        if len(df) == 0 or df['bpm'].isna().all():
            return {
                'mean': 0.0,
                'median': 0.0,
                'std': 0.0,
                'skew': 0.0
            }

        valid_bpms = df['bpm'].dropna()
        if len(valid_bpms) == 0:
            return {
                'mean': 0.0,
                'median': 0.0,
                'std': 0.0,
                'skew': 0.0
            }

        return {
            'mean': float(valid_bpms.mean()),
            'median': float(valid_bpms.median()),
            'std': float(valid_bpms.std()),
            'skew': float(valid_bpms.skew())
        }

    async def get_track_relationships(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get track relationships for 3D visualization

        Args:
            directory_id: The collection directory ID to analyze
            folder_id: Optional folder ID to filter tracks by
        """
        try:
            # Query tracks with optional directory filter - use the approach that works in the collection endpoint
            query = self.db.query(Track)

            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            # Filter by folder if provided, using pattern matching that works
            if folder_id:
                logging.info(f"Filtering tracks by folder_id: {folder_id}")
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_name = folder.name.replace("'", "''")  # Escape single quotes for SQL

                    # Try patterns in order from most specific to most general
                    pattern_options = [
                        f"%/{folder_name}/%",    # Exact match with slashes
                        f"%{folder_name}/%",     # Folder at the start of path
                        f"%/{folder_name}%",     # Folder anywhere in path
                    ]

                    # Try each pattern until we find matches
                    matched = False
                    for pattern in pattern_options:
                        pattern_query = query.filter(Track.directory_path.like(pattern))
                        if pattern_query.count() > 0:
                            query = pattern_query
                            matched = True
                            break

                    # If no matches with specific patterns, try most general pattern
                    if not matched:
                        query = query.filter(Track.directory_path.like(f"%{folder_name}%"))

            tracks = query.all()
            logging.info(f"Found {len(tracks)} tracks for 3D visualization")

            if not tracks:
                return {
                    "positions": [],
                    "isolated_tracks": []
                }

            # Create DataFrame for analysis
            track_data = []
            for track in tracks:
                # Skip tracks with missing data
                if not track.key or not track.bpm:
                    continue

                track_data.append({
                    "id": track.id,
                    "key": self._normalize_key(track.key),
                    "energy": track.energy or 0.5,  # Default to mid energy if missing
                    "bpm": track.bpm or 120.0      # Default to mid BPM if missing
                })

            if not track_data:
                return {
                    "positions": [],
                    "isolated_tracks": []
                }

            df = pd.DataFrame(track_data)

            # Convert key to numeric position (on Camelot wheel)
            df['key_position'] = df['key'].apply(CamelotRules.get_key_position)

            # Create numpy arrays for vectorized operations
            key_positions_np = np.array(df['key_position'], dtype=float)
            energy_np = np.array(df['energy'], dtype=float)
            bpm_np = np.array(df['bpm'], dtype=float)

            # Set up 3D position arrays
            positions = []
            isolated_tracks = []

            # Calculate positions and find isolated tracks
            for i in range(len(df)):
                # Use numpy broadcasting for efficient distance calculations
                key_diffs = np.minimum(
                    np.abs(key_positions_np[i] - key_positions_np),
                    24 - np.abs(key_positions_np[i] - key_positions_np)
                )
                energy_diffs = np.abs(energy_np[i] - energy_np)
                bpm_diffs = np.abs(bpm_np[i] - bpm_np)

                # Count nearby tracks using vectorized operations with proper shapes
                mask = (key_diffs <= 2) & (energy_diffs <= 0.3) & (bpm_diffs <= 8)
                nearby = np.sum(mask & (np.arange(len(df)) != i))

                pos = {
                    "x": float(key_positions_np[i]),
                    "y": float(energy_np[i]),
                    "z": float(bpm_np[i]),
                    "track_id": int(df['id'].iloc[i]),
                    "key": str(df['key'].iloc[i]),
                    "compatibility_score": float(nearby/len(df)),
                    "is_isolated": nearby < 3,
                    "nearby_tracks": int(nearby)
                }

                positions.append(pos)
                if nearby < 3:
                    isolated_tracks.append(pos)

            return {
                "positions": positions,
                "isolated_tracks": isolated_tracks
            }
        except Exception as e:
            logging.error(f"Error in track relationships: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error analyzing track relationships: {str(e)}"
            )

    async def get_bpm_analysis(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """Get advanced BPM analysis with clustering

        Args:
            directory_id: The collection directory ID to analyze
            folder_id: Optional folder ID to filter tracks by
        """
        try:
            # Query tracks with optional directory filter - use the approach that works in the collection endpoint
            query = self.db.query(Track)

            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            # Filter by folder if provided, using pattern matching that works
            if folder_id:
                logging.info(f"Filtering tracks by folder_id: {folder_id}")
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()
                if folder:
                    folder_name = folder.name.replace("'", "''")  # Escape single quotes for SQL

                    # Try patterns in order from most specific to most general
                    pattern_options = [
                        f"%/{folder_name}/%",    # Exact match with slashes
                        f"%{folder_name}/%",     # Folder at the start of path
                        f"%/{folder_name}%",     # Folder anywhere in path
                    ]

                    # Try each pattern until we find matches
                    matched = False
                    for pattern in pattern_options:
                        pattern_query = query.filter(Track.directory_path.like(pattern))
                        if pattern_query.count() > 0:
                            query = pattern_query
                            matched = True
                            break

                    # If no matches with specific patterns, try most general pattern
                    if not matched:
                        query = query.filter(Track.directory_path.like(f"%{folder_name}%"))

            tracks = query.all()
            logging.info(f"Found {len(tracks)} tracks for BPM analysis")

            if not tracks:
                return {
                    "mean": 0,
                    "median": 0,
                    "std": 0,
                    "skew": 0,
                    "bpm_range": [0, 0],
                    "clusters": []
                }

            # Convert to numpy array with explicit dtype
            bpms = np.array([float(t.bpm or 0.0) for t in tracks if t.bpm], dtype=float)
            valid_bpms = bpms[~np.isnan(bpms) & (bpms > 0)]

            if len(valid_bpms) == 0:
                return {
                    "mean": 0,
                    "median": 0,
                    "std": 0,
                    "skew": 0,
                    "bpm_range": [0, 0],
                    "clusters": []
                }

            bpm_series = pd.Series(valid_bpms)

            # Calculate BPM clusters
            clusters = self._identify_bpm_clusters(valid_bpms)

            # Return actual analysis data
            return {
                "mean": float(np.mean(valid_bpms)),
                "median": float(np.median(valid_bpms)),
                "std": float(np.std(valid_bpms)),
                "skew": float(bpm_series.skew()),
                "bpm_range": [float(np.min(valid_bpms)), float(np.max(valid_bpms))],
                "clusters": clusters
            }
        except Exception as e:
            logging.error(f"Error in BPM analysis: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error analyzing BPM: {str(e)}"
            )

    async def get_key_compatibility_network(self, directory_id: Optional[str] = None, folder_id: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get key compatibility network for visualization

        Args:
            directory_id: The collection directory ID to analyze
            folder_id: Optional folder ID to filter tracks by
        """
        try:
            # Log the input parameters
            logging.info(f"Getting key compatibility network for directory_id: {directory_id}, folder_id: {folder_id}")

            # Query tracks with optional directory filter - use the approach that works in the collection endpoint
            query = self.db.query(Track)

            if directory_id:
                query = query.filter(Track.directory_id == directory_id)

            # Filter by folder if provided, using simpler approach
            if folder_id:
                logging.info(f"Filtering tracks by folder_id: {folder_id}")
                folder = self.db.query(Folder).filter(Folder.id == folder_id).first()

                if not folder:
                    logging.warning(f"Folder with ID {folder_id} not found")
                    return {"nodes": [], "edges": []}

                logging.info(f"Found folder: {folder.name}")

                # Just use a simple pattern match without getting too fancy
                query = query.filter(Track.directory_path.like(f"%{folder.name}%"))

            tracks = query.all()
            logging.info(f"Found {len(tracks)} tracks for key network analysis")

            # If no tracks found, return empty result early
            if not tracks:
                logging.warning("No tracks found for key network analysis")
                return {"nodes": [], "edges": []}

            # Define all possible Camelot keys
            all_keys = [f"{num}{letter}" for num in range(1, 13) for letter in ['A', 'B']]

            # Count occurrences of each key in tracks
            key_counts = {}
            for track in tracks:
                key = track.key
                if key and key in all_keys:
                    key_counts[key] = key_counts.get(key, 0) + 1

            # If no valid keys found, return empty result
            if not key_counts:
                return {"nodes": [], "edges": []}

            # Create nodes for the network
            nodes = []
            for key in all_keys:
                count = key_counts.get(key, 0)
                # Only include keys that have tracks
                if count > 0:
                    # Get position for visualization layout
                    position = CamelotRules.get_key_position(key)
                    nodes.append({
                        "id": key,
                        "count": count,
                        "position": position,
                        "label": key
                    })

            # Create edges for compatible connections
            edges = []
            included_keys = [node["id"] for node in nodes]

            # Connect each key to all compatible keys
            for source_key in included_keys:
                for target_key in included_keys:
                    if source_key == target_key:
                        # Self connection (same key)
                        weight = 1.0
                        edges.append({
                            "source": source_key,
                            "target": target_key,
                            "value": weight,
                            "weight": weight,
                            "type": "Perfect Match"  # Add the missing type field
                        })
                    else:
                        # Get compatibility score between keys
                        weight = CamelotRules.get_compatibility_score(source_key, target_key)
                        # Only include non-zero connections
                        if weight > 0:
                            transition_type = CamelotRules._get_basic_transition_type(source_key, target_key)
                            edges.append({
                                "source": source_key,
                                "target": target_key,
                                "value": weight,
                                "weight": weight,
                                "type": transition_type  # Add the missing type field
                            })

            return {"nodes": nodes, "edges": edges}

        except Exception as e:
            logging.error(f"Error generating key compatibility network: {str(e)}")
            # Return empty structure as fallback
            return {"nodes": [], "edges": []}

    async def update_health_score_config(self, config: Dict[str, float]) -> Dict[str, float]:
        """Update health score configuration weights"""
        try:
            # Validate weights sum to 1
            total = sum(config.values())
            if not np.isclose(total, 1.0):
                raise ValueError("Health score weights must sum to 1.0")

            # Transform weights into full configuration
            full_config = {
                "metrics": {
                    "key_balance": {
                        "name": "Key Balance",
                        "enabled": config.get("key_balance", 0) > 0,
                        "weight": config.get("key_balance", 0),
                        "description": "Balance of musical keys across collection"
                    },
                    "bpm_coverage": {
                        "name": "BPM Coverage",
                        "enabled": config.get("bpm_coverage", 0) > 0,
                        "weight": config.get("bpm_coverage", 0),
                        "description": "Coverage of different BPM ranges"
                    },
                    "energy_balance": {
                        "name": "Energy Balance",
                        "enabled": config.get("energy_balance", 0) > 0,
                        "weight": config.get("energy_balance", 0),
                        "description": "Distribution of energy levels"
                    },
                    "artist_diversity": {
                        "name": "Artist Diversity",
                        "enabled": config.get("artist_diversity", 0) > 0,
                        "weight": config.get("artist_diversity", 0),
                        "description": "Diversity of artists in collection"
                    }
                }
            }

            # Store in database
            HealthScoreConfig.update_config(self.db, full_config)
            return config

        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logging.error(f"Error updating health config: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error updating health score config: {str(e)}"
            )