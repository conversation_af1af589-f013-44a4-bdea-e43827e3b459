"""
Beat grid service for extracting beat grids from audio files.
This service uses librosa to analyze audio files and detect beats.
It provides both basic and enhanced beat grid extraction capabilities.
"""

import os
import logging
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List, Union
import numpy as np
import librosa
import uuid
from datetime import datetime
import scipy.signal
import scipy.ndimage
import scipy.stats
from sqlalchemy.orm import Session

from backend.models.track import Track
from backend.models.beat_grid import BeatGrid

logger = logging.getLogger(__name__)

class BeatGridService:
    """Service for analyzing audio files and detecting beat grids"""

    def __init__(self, db):
        self.db = db
        self._cache = {}  # Simple in-memory cache

    async def extract_track_beat_grid(self, track_id: int, db: Optional[Session] = None, enhanced: bool = False) -> Dict[str, Any]:
        """
        Extract beat grid for a track and store it in the database

        Args:
            track_id: ID of the track to analyze
            db: Database session (optional, will use self.db if not provided)
            enhanced: Whether to use enhanced beat grid extraction

        Returns:
            Dictionary with beat grid information
        """
        db = db or self.db
        try:
            # Get the track from the database
            track = db.query(Track).filter(Track.id == track_id).first()
            if not track:
                logger.error(f"Track {track_id} not found")
                return {"error": f"Track {track_id} not found"}

            # Get the file path
            file_path = track.file_path
            if not file_path or not os.path.exists(file_path):
                logger.error(f"File not found for track {track_id}: {file_path}")
                return {"error": f"File not found for track {track_id}"}

            # Perform the analysis
            logger.info(f"Starting {'enhanced ' if enhanced else ''}beat grid extraction for track {track_id}: {file_path}")
            print(f"DEBUG: Starting {'enhanced ' if enhanced else ''}beat grid extraction for track {track_id}: {file_path}")

            try:
                # Extract beat grid using librosa
                beat_grid_data = await asyncio.to_thread(self._extract_beat_grid, file_path, enhanced)

                print(f"DEBUG: Beat grid extraction result for track {track_id}:")
                print(f"DEBUG: Tempo: {beat_grid_data['tempo']}")
                print(f"DEBUG: Beat count: {len(beat_grid_data['beat_times'])}")
                print(f"DEBUG: First few beats: {beat_grid_data['beat_times'][:5]}")
                print(f"DEBUG: Confidence: {beat_grid_data['confidence']}")
                if enhanced:
                    print(f"DEBUG: Enhanced: {beat_grid_data.get('enhanced', False)}")
                    print(f"DEBUG: Meter: {beat_grid_data.get('meter', 4)}")
                    print(f"DEBUG: Segments: {len(beat_grid_data.get('segments', []))}")
                    print(f"DEBUG: Loop points: {len(beat_grid_data.get('loop_points', []))}")

                # Check if a beat grid already exists for this track
                existing_beat_grid = db.query(BeatGrid).filter(BeatGrid.track_id == track_id).first()

                if existing_beat_grid:
                    # Update existing beat grid
                    existing_beat_grid.tempo = beat_grid_data["tempo"]
                    existing_beat_grid.beat_times = json.dumps(beat_grid_data["beat_times"])
                    existing_beat_grid.confidence = beat_grid_data["confidence"]

                    # Update enhanced data if available
                    if enhanced and beat_grid_data.get("enhanced", False):
                        existing_beat_grid.enhanced = True
                        existing_beat_grid.bars = json.dumps(beat_grid_data.get("bars", []))
                        existing_beat_grid.segments = json.dumps(beat_grid_data.get("segments", []))
                        existing_beat_grid.downbeats = json.dumps(beat_grid_data.get("downbeats", []))
                        existing_beat_grid.loop_points = json.dumps(beat_grid_data.get("loop_points", []))
                        existing_beat_grid.meter = beat_grid_data.get("meter", 4)
                        existing_beat_grid.tempo_changes = json.dumps(beat_grid_data.get("tempo_changes", []))

                    db.commit()
                    beat_grid = existing_beat_grid
                    print(f"DEBUG: Updated existing beat grid for track {track_id}")
                else:
                    # Create new beat grid
                    beat_grid_args = {
                        "track_id": track_id,
                        "tempo": beat_grid_data["tempo"],
                        "beat_times": json.dumps(beat_grid_data["beat_times"]),
                        "confidence": beat_grid_data["confidence"]
                    }

                    # Add enhanced data if available
                    if enhanced and beat_grid_data.get("enhanced", False):
                        beat_grid_args.update({
                            "enhanced": True,
                            "bars": json.dumps(beat_grid_data.get("bars", [])),
                            "segments": json.dumps(beat_grid_data.get("segments", [])),
                            "downbeats": json.dumps(beat_grid_data.get("downbeats", [])),
                            "loop_points": json.dumps(beat_grid_data.get("loop_points", [])),
                            "meter": beat_grid_data.get("meter", 4),
                            "tempo_changes": json.dumps(beat_grid_data.get("tempo_changes", []))
                        })

                    beat_grid = BeatGrid(**beat_grid_args)
                    db.add(beat_grid)
                    db.commit()
                    print(f"DEBUG: Created new beat grid for track {track_id}")

                logger.info(f"Beat grid extraction completed for track {track_id}")
                print(f"DEBUG: Beat grid extraction completed for track {track_id}")

                result = {
                    "track_id": track_id,
                    "tempo": beat_grid_data["tempo"],
                    "beat_count": len(beat_grid_data["beat_times"]),
                    "confidence": beat_grid_data["confidence"]
                }

                # Add enhanced data to result if available
                if enhanced and beat_grid_data.get("enhanced", False):
                    result.update({
                        "enhanced": True,
                        "meter": beat_grid_data.get("meter", 4),
                        "segment_count": len(beat_grid_data.get("segments", [])),
                        "bar_count": len(beat_grid_data.get("bars", [])),
                        "loop_point_count": len(beat_grid_data.get("loop_points", [])),
                        "has_good_loops": beat_grid_data.get("has_good_loops", False),
                        "tempo_changes": len(beat_grid_data.get("tempo_changes", []))
                    })

                return result

            except Exception as e:
                logger.error(f"Error extracting beat grid for track {track_id}: {str(e)}", exc_info=True)
                return {"error": f"Error extracting beat grid: {str(e)}"}

        except Exception as e:
            logger.error(f"Error in extract_track_beat_grid for track {track_id}: {str(e)}", exc_info=True)
            return {"error": str(e)}
        finally:
            # Close the database session if we created it
            if db != self.db:
                db.close()

    def _extract_beat_grid(self, file_path: str, enhanced: bool = False) -> Dict[str, Any]:
        """
        Extract beat grid from an audio file using librosa

        Args:
            file_path: Path to the audio file
            enhanced: Whether to use enhanced beat grid extraction

        Returns:
            Dictionary containing:
            - tempo: Estimated BPM
            - beat_times: List of timestamps (in seconds) where beats occur
            - confidence: Confidence score of the beat tracking
            - Additional fields if enhanced=True
        """
        # Check cache first
        cache_key = f"beatgrid_{os.path.basename(file_path)}_{enhanced}"
        if cache_key in self._cache:
            return self._cache[cache_key]

        # Load the audio file
        # Load the full track to ensure beat grid covers the entire duration
        y, sr = librosa.load(file_path, sr=None)  # Load full track for complete beat grid coverage

        if enhanced:
            # Enhanced mode with all improvements
            result = self._enhanced_beat_grid_extraction(y, sr, file_path)
        else:
            # Basic mode with quality retry system
            result = self._extract_with_quality_retry(y, sr)

            if result is None:
                # If all attempts failed, return a minimal result
                result = {
                    "tempo": 0.0,
                    "beat_times": [],
                    "confidence": 0.0,
                    "quality_validation": {
                        "valid": False,
                        "quality_score": 0.0,
                        "issues": ["All beat detection attempts failed"],
                        "recommendations": ["Check audio quality and try manual beat grid correction"]
                    }
                }

        # Cache the result
        self._cache[cache_key] = result
        return result

    def _enhanced_beat_grid_extraction(self, y: np.ndarray, sr: int, file_path: str) -> Dict[str, Any]:
        """
        Enhanced beat grid extraction with segment analysis, harmonic structure,
        bar detection, and loop point identification.

        Args:
            y: Audio time series
            sr: Sample rate
            file_path: Path to the audio file (for caching)

        Returns:
            Dictionary containing comprehensive beat grid data
        """
        # Check cache first
        cache_key = f"enhanced_beatgrid_{os.path.basename(file_path)}"
        if cache_key in self._cache:
            return self._cache[cache_key]

        # 1. Core beat detection with multiple onset methods
        beat_result = self._extract_beat_grid_with_multiple_onset_methods(y, sr)
        tempo = beat_result["tempo"]
        beat_times = np.array(beat_result["beat_times"])
        beat_frames = librosa.time_to_frames(beat_times, sr=sr)
        beat_confidence = beat_result["confidence"]

        # 2. Segment analysis for tempo changes
        segment_result = self._analyze_segments(y, sr)

        # 3. Harmonic structure analysis
        structure_result = self._analyze_harmonic_structure(y, sr, beat_frames)

        # 4. Bar and meter detection
        bar_result = self._detect_bars_and_meter(y, sr, beat_times)

        # 5. Loop point detection
        # Check if beat_chroma is available in structure_result
        beat_chroma = structure_result.get("beat_chroma")
        if beat_chroma is not None:
            loop_result = self._detect_loop_points(y, sr, beat_times, np.array(beat_chroma))
        else:
            # If beat_chroma is not available, pass None to use the default calculation
            loop_result = self._detect_loop_points(y, sr, beat_times)

        # 6. Calculate overall confidence
        tempo_consistency = segment_result["tempo_consistency"]

        # Overall confidence combines beat strength and tempo consistency
        overall_confidence = 0.7 * beat_confidence + 0.3 * tempo_consistency

        # 7. Validate beat grid quality
        validation = self._validate_beat_grid_quality(beat_times, overall_confidence, float(tempo))

        # Combine all results
        result = {
            # Basic beat information
            "tempo": float(tempo),
            "beat_times": beat_times.tolist(),
            "confidence": min(1.0, max(0.0, overall_confidence)),

            # Segment analysis
            "segment_tempos": segment_result["segment_tempos"],
            "tempo_changes": segment_result["tempo_changes"],
            "tempo_variation": segment_result["tempo_std"],

            # Structure analysis
            "segments": structure_result["segments"],
            "transitions": structure_result["transitions"],

            # Bar and meter information
            "meter": bar_result["meter"],
            "bars": bar_result["bars"],
            "downbeats": bar_result["downbeats"],

            # Loop points
            "loop_points": loop_result["loop_points"],
            "has_good_loops": loop_result["has_good_loops"],

            # Metadata
            "enhanced": True,
            "analysis_version": "1.0",
            "file_path": file_path,
            "analysis_time": datetime.now().isoformat(),

            # Quality validation
            "quality_validation": validation
        }

        # Cache the result
        self._cache[cache_key] = result

        return result

    def _extract_beat_grid_with_multiple_onset_methods(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Enhanced beat detection with multiple onset detection methods.

        Args:
            y: Audio time series
            sr: Sample rate

        Returns:
            Dictionary with the best beat detection result
        """
        # Separate harmonic and percussive components for better beat detection
        y_harmonic, y_percussive = librosa.effects.hpss(y)

        # Try multiple onset detection methods and select the best result
        methods = [
            {'name': 'default', 'onset_env': librosa.onset.onset_strength(y=y, sr=sr)},
            {'name': 'percussive', 'onset_env': librosa.onset.onset_strength(y=y, sr=sr, feature=librosa.feature.melspectrogram)},
            {'name': 'hpss_percussive', 'onset_env': librosa.onset.onset_strength(y=y_percussive, sr=sr)},
            {'name': 'hpss_full', 'onset_env': librosa.onset.onset_strength(y=y_harmonic + y_percussive, sr=sr)}
        ]

        # Add scipy-enhanced onset detection methods
        try:
            # Method 1: Bandpass filtered signal for rhythm emphasis
            nyquist = sr / 2
            low_freq = 60 / nyquist   # 60 Hz high-pass (remove low rumble)
            high_freq = 8000 / nyquist  # 8 kHz low-pass (focus on rhythm)
            b, a = scipy.signal.butter(4, [low_freq, high_freq], btype='band')
            y_filtered = scipy.signal.filtfilt(b, a, y)
            filtered_onset = librosa.onset.onset_strength(y=y_filtered, sr=sr)
            methods.append({'name': 'scipy_bandpass', 'onset_env': filtered_onset})

            # Method 2: Gaussian smoothed onset envelope
            default_onset = librosa.onset.onset_strength(y=y, sr=sr)
            smoothed_onset = scipy.ndimage.gaussian_filter1d(default_onset, sigma=1.0)
            methods.append({'name': 'scipy_smoothed', 'onset_env': smoothed_onset})

            # Method 3: Enhanced percussive with filtering
            y_perc_filtered = scipy.signal.filtfilt(b, a, y_percussive)
            perc_filtered_onset = librosa.onset.onset_strength(y=y_perc_filtered, sr=sr)
            methods.append({'name': 'scipy_perc_filtered', 'onset_env': perc_filtered_onset})

        except Exception as e:
            print(f"Could not add scipy-enhanced methods: {str(e)}")

        # Add RMS method with correct parameters
        try:
            # Create a custom function that wraps rms to match the expected signature
            def rms_wrapper(y, sr, n_fft, hop_length, **kwargs):
                return librosa.feature.rms(y=y, frame_length=n_fft, hop_length=hop_length)

            rms_env = librosa.onset.onset_strength(y=y, sr=sr, feature=rms_wrapper)
            methods.append({'name': 'rms', 'onset_env': rms_env})
        except Exception as e:
            print(f"Could not add RMS method: {str(e)}")

        # Collect results from all methods for ensemble voting
        method_results = []

        for method in methods:
            try:
                # Extract tempo and beat frames using this onset method with optimized parameters
                tempo, beat_frames = librosa.beat.beat_track(
                    onset_envelope=method['onset_env'], sr=sr,
                    hop_length=512,        # Better time resolution
                    start_bpm=60,          # Wider BPM search range
                    tightness=100,         # Higher consistency requirement
                    trim=True              # Remove silence
                )

                # Convert frames to time (seconds)
                beat_times = librosa.frames_to_time(beat_frames, sr=sr)

                # Calculate enhanced confidence based on beat strength, tempo consistency, and beat regularity
                beat_strengths = method['onset_env'][beat_frames]
                confidence = self._calculate_enhanced_confidence(beat_times, beat_strengths, method['onset_env'])

                method_results.append({
                    "tempo": float(tempo),
                    "beat_times": beat_times.tolist(),
                    "confidence": confidence,
                    "method": method['name']
                })
            except Exception as e:
                print(f"Error with method {method['name']}: {str(e)}")
                continue

        # Use ensemble voting to select the best result
        best_result = self._ensemble_vote_beat_detection(method_results)

        # Apply quality validation to the best result
        if best_result:
            # Check for tempo doubling/halving issues and correct if needed
            corrected_result = self._detect_and_correct_tempo_doubling(best_result, y, sr)

            # Apply beat phase alignment correction to fix systematic offset
            phase_corrected_result = self._correct_beat_phase_alignment(corrected_result, y, sr)

            # Apply additional onset-based alignment refinement
            onset_refined_result = self._refine_beat_alignment_with_onsets(phase_corrected_result, y, sr)

            # Fix missing first beat issue
            final_result = self._fix_missing_first_beat(onset_refined_result, y, sr)

            validation = self._validate_beat_grid_quality(
                np.array(final_result["beat_times"]),
                final_result["confidence"],
                final_result["tempo"]
            )
            final_result["quality_validation"] = validation
            best_result = final_result

        # If all methods failed, fall back to the default method with optimized parameters
        if best_result is None:
            print("All onset detection methods failed, falling back to default")
            tempo, beat_frames = librosa.beat.beat_track(
                y=y, sr=sr,
                hop_length=512,        # Better time resolution
                start_bpm=60,          # Wider BPM search range
                tightness=100,         # Higher consistency requirement
                trim=True              # Remove silence
            )
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)

            # Calculate enhanced confidence
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            beat_strengths = onset_env[beat_frames]
            confidence = self._calculate_enhanced_confidence(beat_times, beat_strengths, onset_env)

            best_result = {
                "tempo": float(tempo),
                "beat_times": beat_times.tolist(),
                "confidence": confidence,
                "method": "fallback"
            }

        return best_result

    def _analyze_segments(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Analyze audio in segments to detect tempo changes.

        Args:
            y: Audio time series
            sr: Sample rate

        Returns:
            Dictionary with segment analysis results
        """
        # Split audio into segments (e.g., 30 seconds each)
        segment_length = 30 * sr
        segments = [y[i:i+segment_length] for i in range(0, len(y), segment_length) if i+segment_length <= len(y)]

        # Analyze each segment
        segment_tempos = []
        segment_beats = []
        segment_confidences = []

        for i, segment in enumerate(segments):
            # Get onset envelope for this segment
            onset_env = librosa.onset.onset_strength(y=segment, sr=sr)

            # Extract tempo and beat frames with optimized parameters
            tempo, beat_frames = librosa.beat.beat_track(
                onset_envelope=onset_env, sr=sr,
                hop_length=512,        # Better time resolution
                start_bpm=60,          # Wider BPM search range
                tightness=100,         # Higher consistency requirement
                trim=True              # Remove silence
            )
            segment_tempos.append(tempo)

            # Convert frames to time (seconds), adjusting for segment offset
            segment_offset = i * 30  # 30 seconds per segment
            beat_times = librosa.frames_to_time(beat_frames, sr=sr) + segment_offset
            segment_beats.append(beat_times)

            # Calculate enhanced confidence for this segment (with bounds checking)
            valid_frames = beat_frames[beat_frames < len(onset_env)]
            if len(valid_frames) > 0:
                beat_strengths = onset_env[valid_frames]
                # Adjust beat_times to match valid frames
                beat_times = librosa.frames_to_time(valid_frames, sr=sr) + segment_offset
            else:
                beat_strengths = np.array([])
            confidence = self._calculate_enhanced_confidence(beat_times, beat_strengths, onset_env)
            segment_confidences.append(confidence)

        # Calculate overall tempo and tempo variation
        try:
            # Ensure both arrays have the same length by truncating to the shorter one
            min_length = min(len(segment_tempos), len(segment_confidences))
            if min_length > 0:
                segment_tempos = np.array(segment_tempos[:min_length])
                segment_confidences = np.array(segment_confidences[:min_length])

                if np.sum(segment_confidences) > 0:
                    overall_tempo = np.average(segment_tempos, weights=segment_confidences)
                else:
                    overall_tempo = np.mean(segment_tempos)
            else:
                overall_tempo = 120.0  # Default tempo
        except Exception as e:
            print(f"Error calculating weighted average tempo: {str(e)}")
            print(f"segment_tempos length: {len(segment_tempos)}, segment_confidences length: {len(segment_confidences)}")
            overall_tempo = np.mean(segment_tempos) if len(segment_tempos) > 0 else 120.0

        tempo_std = np.std(segment_tempos)

        # Detect significant tempo changes (more than 10% change)
        tempo_changes = []
        for i in range(1, len(segment_tempos)):
            change_percent = abs(segment_tempos[i] - segment_tempos[i-1]) / segment_tempos[i-1]
            if change_percent > 0.1:  # 10% threshold
                tempo_changes.append({
                    "position": i * 30,  # 30 seconds per segment
                    "from_tempo": float(segment_tempos[i-1]),
                    "to_tempo": float(segment_tempos[i]),
                    "change_percent": float(change_percent)
                })

        # Calculate tempo consistency confidence
        tempo_consistency = 1.0 - min(1.0, tempo_std / (overall_tempo if overall_tempo > 0 else 1.0))

        return {
            "segment_tempos": [float(t) for t in segment_tempos],
            "segment_beats": [beats.tolist() for beats in segment_beats],
            "segment_confidences": segment_confidences,
            "overall_tempo": float(overall_tempo),
            "tempo_std": float(tempo_std),
            "tempo_changes": tempo_changes,
            "tempo_consistency": float(tempo_consistency)
        }

    def _analyze_harmonic_structure(self, y: np.ndarray, sr: int, beat_frames: np.ndarray) -> Dict[str, Any]:
        """
        Analyze harmonic structure to identify segments and improve beat grid.

        Args:
            y: Audio time series
            sr: Sample rate
            beat_frames: Beat frames from beat tracking

        Returns:
            Dictionary with harmonic analysis results
        """
        try:
            # Extract beat-synchronized chromagram
            chroma = librosa.feature.chroma_cqt(y=y, sr=sr)
            beat_chroma = librosa.util.sync(chroma, beat_frames)

            # Compute recurrence matrix (beat similarity)
            R = librosa.segment.recurrence_matrix(beat_chroma.T, mode='affinity')

            # Identify segments using spectral clustering
            # Make sure we don't request more segments than we have beats
            max_possible_segments = max(2, min(len(beat_frames) - 1, len(R)))

            # Adjust segment count based on track length for better accuracy
            # Longer tracks should have more segments
            duration = len(y) / sr
            if duration < 180:  # Less than 3 minutes
                segment_divisor = 20  # Fewer segments for short tracks
            elif duration < 300:  # 3-5 minutes
                segment_divisor = 16  # Medium number of segments
            else:  # More than 5 minutes
                segment_divisor = 12  # More segments for long tracks

            n_segments = max(2, min(max_possible_segments, int(len(beat_frames) / segment_divisor)))

            # Ensure we have enough data for clustering
            if n_segments < 2 or len(R) < 2:
                raise ValueError(f"Not enough data for clustering: n_segments={n_segments}, R.shape={R.shape}")

            labels = librosa.segment.agglomerative(R, n_segments)

            # Convert frames to time
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)

            # Group beats by segment
            segments = []
            for i in range(n_segments):
                # Ensure labels and beat_times have compatible dimensions
                if len(labels) == len(beat_times):
                    segment_beats = beat_times[labels == i]
                else:
                    # Fallback: use labels length to index beat_times safely
                    valid_indices = np.where(labels == i)[0]
                    valid_indices = valid_indices[valid_indices < len(beat_times)]
                    segment_beats = beat_times[valid_indices] if len(valid_indices) > 0 else np.array([])

                if len(segment_beats) > 0:
                    segments.append({
                        "start": float(segment_beats[0]),
                        "end": float(segment_beats[-1]),
                        "beats": segment_beats.tolist()
                    })

            # Sort segments by start time
            segments.sort(key=lambda x: x["start"])

            # Calculate segment transitions
            transitions = []
            for i in range(1, len(segments)):
                transitions.append({
                    "time": segments[i]["start"],
                    "from_segment": i-1,
                    "to_segment": i
                })

            return {
                "segments": segments,
                "transitions": transitions,
                "beat_chroma": beat_chroma.tolist(),
                "n_segments": n_segments
            }

        except Exception as e:
            print(f"Error in harmonic structure analysis: {str(e)}")
            # Fallback to simple time-based segmentation
            beat_times = librosa.frames_to_time(beat_frames, sr=sr)
            duration = len(y) / sr

            # Create simple segments based on time
            n_segments = max(2, min(8, int(duration / 30)))  # Aim for segments of ~30 seconds
            segment_duration = duration / n_segments

            segments = []
            for i in range(n_segments):
                start_time = i * segment_duration
                end_time = min((i + 1) * segment_duration, duration)

                # Find beats in this segment
                segment_beats = []
                for beat_time in beat_times:
                    if start_time <= beat_time < end_time:
                        segment_beats.append(float(beat_time))

                if segment_beats:
                    segments.append({
                        "start": float(start_time),
                        "end": float(end_time),
                        "beats": segment_beats
                    })

            return {
                "segments": segments,
                "transitions": [],
                "n_segments": len(segments)
            }

    def _detect_bars_and_meter(self, y: np.ndarray, sr: int, beat_times: np.ndarray) -> Dict[str, Any]:
        """
        Detect bars and meter (time signature) from beat times.

        Args:
            y: Audio time series
            sr: Sample rate
            beat_times: Beat times in seconds

        Returns:
            Dictionary with bar and meter information
        """
        # Calculate inter-beat intervals
        ibis = np.diff(beat_times)
        median_ibi = np.median(ibis)

        # Detect meter by analyzing beat strength patterns
        onset_env = librosa.onset.onset_strength(y=y, sr=sr)
        onset_times = librosa.times_like(onset_env, sr=sr)

        # Get beat strengths
        beat_strengths = np.array([
            onset_env[np.argmin(np.abs(onset_times - beat_time))]
            for beat_time in beat_times
        ])

        # Analyze patterns of 2, 3, 4 beats
        acf_2 = np.correlate(beat_strengths, beat_strengths[::2][:len(beat_strengths)//2], mode='valid') if len(beat_strengths) >= 4 else [0]
        acf_3 = np.correlate(beat_strengths, beat_strengths[::3][:len(beat_strengths)//3], mode='valid') if len(beat_strengths) >= 6 else [0]
        acf_4 = np.correlate(beat_strengths, beat_strengths[::4][:len(beat_strengths)//4], mode='valid') if len(beat_strengths) >= 8 else [0]

        # Determine most likely meter
        acf_values = [np.mean(acf_2), np.mean(acf_3), np.mean(acf_4)]

        # Add genre-based bias
        # Electronic music is usually 4/4
        # Check if the track has consistent tempo (electronic music indicator)
        ibis = np.diff(beat_times)
        tempo_consistency = np.std(ibis) / np.mean(ibis) if len(ibis) > 0 and np.mean(ibis) > 0 else 1.0

        # If tempo is very consistent, bias toward 4/4
        if tempo_consistency < 0.05:  # Very consistent tempo
            acf_values[2] *= 1.2  # Boost 4/4 time signature

        # If tempo is moderately consistent, bias slightly toward 4/4
        elif tempo_consistency < 0.1:
            acf_values[2] *= 1.1  # Slight boost for 4/4

        # Check for very fast tempos (drum & bass, etc.) which are often felt in half-time
        median_tempo = 60.0 / np.median(ibis) if len(ibis) > 0 and np.median(ibis) > 0 else 120.0
        if median_tempo > 160:
            acf_values[0] *= 1.2  # Boost 2/4 for fast tempos (half-time feel)

        meters = [2, 3, 4]
        meter = meters[np.argmax(acf_values)]

        # Group beats into bars based on detected meter
        bars = []
        for i in range(0, len(beat_times), meter):
            if i + meter <= len(beat_times):
                bars.append(beat_times[i:i+meter].tolist())
            elif i < len(beat_times):
                # Handle incomplete bar at the end
                bars.append(beat_times[i:].tolist())

        # Identify downbeats (first beat of each bar)
        downbeats = [bar[0] for bar in bars]

        return {
            "meter": meter,
            "bars": bars,
            "downbeats": downbeats,
            "median_ibi": float(median_ibi)
        }

    def _detect_loop_points(self, y: np.ndarray, sr: int, beat_times: np.ndarray, beat_chroma: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Detect potential seamless loop points for DJ applications.

        Args:
            y: Audio time series
            sr: Sample rate
            beat_times: Beat times in seconds
            beat_chroma: Beat-synchronized chromagram (optional)

        Returns:
            Dictionary with loop point information
        """
        # Compute beat-synchronized chromagram if not provided
        if beat_chroma is None:
            chroma = librosa.feature.chroma_cqt(y=y, sr=sr)
            beat_frames = librosa.time_to_frames(beat_times, sr=sr)
            beat_chroma = librosa.util.sync(chroma, beat_frames)

        # Compute self-similarity matrix
        S = librosa.segment.recurrence_matrix(beat_chroma.T, mode='affinity')

        # Find diagonal paths (potential loops)
        # Adjust loop length based on meter for better musical loops
        meter = 4  # Default to 4/4 time signature

        # Try to detect meter from beat pattern if we have enough beats
        if len(beat_times) >= 16:
            # Calculate inter-beat intervals
            ibis = np.diff(beat_times)

            # Look for patterns in beat strengths
            beat_frames = librosa.time_to_frames(beat_times, sr=sr)
            onset_env = librosa.onset.onset_strength(y=y, sr=sr)
            beat_strengths = onset_env[beat_frames] if len(beat_frames) <= len(onset_env) else np.zeros_like(beat_frames)

            # Check for patterns of 2, 3, and 4 beats
            if len(beat_strengths) >= 8:
                acf_2 = np.correlate(beat_strengths[:len(beat_strengths)//2*2], beat_strengths[::2][:len(beat_strengths)//2], mode='valid')
                acf_3 = np.correlate(beat_strengths[:len(beat_strengths)//3*3], beat_strengths[::3][:len(beat_strengths)//3], mode='valid')
                acf_4 = np.correlate(beat_strengths[:len(beat_strengths)//4*4], beat_strengths[::4][:len(beat_strengths)//4], mode='valid')

                # Determine most likely meter
                acf_values = [np.mean(acf_2), np.mean(acf_3), np.mean(acf_4)]
                meters = [2, 3, 4]
                meter = meters[np.argmax(acf_values)]

        # Set minimum loop length based on meter (4 bars)
        min_loop_length = meter * 4

        # For very short tracks, reduce minimum loop length
        if len(beat_times) < min_loop_length * 2:
            min_loop_length = max(8, len(beat_times) // 3)

        max_loops = 5  # Return top 5 loop candidates
        loop_candidates = []

        # Adjust similarity threshold based on genre characteristics
        # For electronic music (consistent tempo), use higher threshold
        tempo_consistency = np.std(np.diff(beat_times)) / np.mean(np.diff(beat_times))
        is_consistent_tempo = tempo_consistency < 0.1
        similarity_threshold = 0.75 if is_consistent_tempo else 0.65

        for i in range(len(S) - min_loop_length):
            for j in range(i + min_loop_length, len(S)):
                # Check for high similarity in a diagonal path
                diag_sim = np.mean(np.diag(S, j-i)[:min_loop_length])
                if diag_sim > similarity_threshold:
                    # Calculate loop quality based on similarity and length
                    loop_length = j - i

                    # Favor loops that are multiples of the meter (e.g., 4, 8, 16 bars)
                    musical_alignment = 1.0
                    if meter > 0:
                        bars = loop_length / meter
                        # Bonus for whole number of bars
                        musical_alignment += 0.2 * (1.0 - min(1.0, abs(bars - round(bars))))
                        # Extra bonus for powers of 2 (8, 16, 32 beats)
                        if abs(bars - 4) < 0.1 or abs(bars - 8) < 0.1 or abs(bars - 16) < 0.1:
                            musical_alignment += 0.3

                    loop_quality = diag_sim * musical_alignment * (1 + 0.1 * min(10, loop_length / meter))  # Favor longer loops

                    loop_candidates.append({
                        "start_beat": int(i),
                        "end_beat": int(j),
                        "start_time": float(beat_times[i]),
                        "end_time": float(beat_times[j]),
                        "length_beats": int(loop_length),
                        "length_seconds": float(beat_times[j] - beat_times[i]),
                        "quality": float(loop_quality)
                    })

        # Sort by quality and take top candidates
        loop_candidates.sort(key=lambda x: x["quality"], reverse=True)
        top_loops = loop_candidates[:max_loops]

        return {
            "loop_points": top_loops,
            "has_good_loops": len(top_loops) > 0 and top_loops[0]["quality"] > 0.8
        }

    async def get_track_beat_grid(self, track_id: int, db: Optional[Session] = None) -> Dict[str, Any]:
        """
        Get beat grid for a track

        Args:
            track_id: ID of the track
            db: Database session (optional, will use self.db if not provided)

        Returns:
            Dictionary with beat grid information
        """
        db = db or self.db
        try:
            # Get the beat grid from the database
            beat_grid = db.query(BeatGrid).filter(BeatGrid.track_id == track_id).first()
            if not beat_grid:
                logger.error(f"Beat grid not found for track {track_id}")
                return {"error": f"Beat grid not found for track {track_id}"}

            # Parse the beat times JSON
            beat_times = json.loads(beat_grid.beat_times)

            # Create the basic result
            result = {
                "track_id": track_id,
                "tempo": beat_grid.tempo,
                "beat_times": beat_times,
                "confidence": beat_grid.confidence,
                "enhanced": beat_grid.enhanced
            }

            # Add enhanced data if available
            if beat_grid.enhanced:
                if beat_grid.bars:
                    result["bars"] = json.loads(beat_grid.bars)
                if beat_grid.segments:
                    result["segments"] = json.loads(beat_grid.segments)
                if beat_grid.downbeats:
                    result["downbeats"] = json.loads(beat_grid.downbeats)
                if beat_grid.loop_points:
                    result["loop_points"] = json.loads(beat_grid.loop_points)
                if beat_grid.meter:
                    result["meter"] = beat_grid.meter
                if beat_grid.tempo_changes:
                    result["tempo_changes"] = json.loads(beat_grid.tempo_changes)

            return result

        except Exception as e:
            logger.error(f"Error in get_track_beat_grid for track {track_id}: {str(e)}", exc_info=True)
            return {"error": str(e)}
        finally:
            # Close the database session if we created it
            if db != self.db:
                db.close()

    async def extract_collection_beat_grids(self, collection_id: str, db: Optional[Session] = None, enhanced: bool = False) -> Dict[str, Any]:
        """
        Extract beat grids for all tracks in a collection

        Args:
            collection_id: ID of the collection
            db: Database session (optional, will use self.db if not provided)
            enhanced: Whether to use enhanced beat grid extraction

        Returns:
            Dictionary with extraction status
        """
        db = db or self.db
        try:
            # Get all tracks in the collection
            tracks = db.query(Track).filter(Track.directory_id == collection_id).all()
            if not tracks:
                logger.error(f"No tracks found for collection {collection_id}")
                return {"error": f"No tracks found for collection {collection_id}"}

            # Start the extraction for each track
            for track in tracks:
                asyncio.create_task(self.extract_track_beat_grid(track.id, db, enhanced))

            return {
                "message": f"{'Enhanced ' if enhanced else ''}Beat grid extraction started for {len(tracks)} tracks in collection {collection_id}",
                "collection_id": collection_id,
                "track_count": len(tracks),
                "enhanced": enhanced
            }

        except Exception as e:
            logger.error(f"Error in extract_collection_beat_grids for collection {collection_id}: {str(e)}", exc_info=True)
            return {"error": str(e)}
        finally:
            # Close the database session if we created it
            if db != self.db:
                db.close()

    async def enhance_existing_beat_grids(self, db: Optional[Session] = None) -> Dict[str, Any]:
        """
        Enhance all existing beat grids that don't already have enhanced data.
        This is useful for upgrading existing beat grids to the enhanced format.

        Args:
            db: Database session (optional, will use self.db if not provided)

        Returns:
            Dictionary with enhancement status
        """
        db = db or self.db
        try:
            # Get all beat grids that don't have enhanced data
            beat_grids = db.query(BeatGrid).filter(BeatGrid.enhanced == False).all()
            if not beat_grids:
                logger.info("No beat grids found that need enhancement")
                return {"message": "No beat grids found that need enhancement", "count": 0}

            # Start the enhancement for each beat grid
            enhanced_count = 0
            for beat_grid in beat_grids:
                try:
                    # Get the track
                    track = db.query(Track).filter(Track.id == beat_grid.track_id).first()
                    if not track or not track.file_path or not os.path.exists(track.file_path):
                        logger.warning(f"Skipping enhancement for track {beat_grid.track_id}: File not found")
                        continue

                    # Create a task to enhance the beat grid
                    asyncio.create_task(self.extract_track_beat_grid(beat_grid.track_id, db, enhanced=True))
                    enhanced_count += 1
                except Exception as e:
                    logger.error(f"Error enhancing beat grid for track {beat_grid.track_id}: {str(e)}", exc_info=True)
                    continue

            return {
                "message": f"Enhancement started for {enhanced_count} beat grids",
                "count": enhanced_count,
                "total": len(beat_grids)
            }

        except Exception as e:
            logger.error(f"Error in enhance_existing_beat_grids: {str(e)}", exc_info=True)
            return {"error": str(e)}
        finally:
            # Close the database session if we created it
            if db != self.db:
                db.close()

    def _calculate_enhanced_confidence(self, beat_times: np.ndarray, beat_strengths: np.ndarray, onset_env: np.ndarray) -> float:
        """
        Calculate enhanced confidence score that better reflects actual beat alignment accuracy.

        Combines:
        1. Beat strength (original metric)
        2. Tempo consistency (regularity of inter-beat intervals)
        3. Beat regularity (how well beats align with expected pattern)

        Args:
            beat_times: Array of beat times in seconds
            beat_strengths: Array of onset strengths at beat positions
            onset_env: Full onset strength envelope

        Returns:
            Enhanced confidence score between 0.0 and 1.0
        """
        if len(beat_times) < 2 or len(beat_strengths) == 0 or np.max(onset_env) == 0:
            return 0.0

        # 1. Beat strength confidence (original metric)
        beat_strength_conf = float(np.mean(beat_strengths) / np.max(onset_env))

        # 2. Tempo consistency confidence
        if len(beat_times) >= 3:
            ibis = np.diff(beat_times)  # Inter-beat intervals
            if len(ibis) > 0 and np.mean(ibis) > 0:
                # Lower coefficient of variation = higher consistency
                tempo_consistency = 1.0 - min(1.0, np.std(ibis) / np.mean(ibis))
            else:
                tempo_consistency = 0.0
        else:
            tempo_consistency = 0.0

        # 3. Beat regularity confidence
        if len(beat_times) >= 4:
            # Check how well beats follow expected regular pattern
            expected_interval = np.median(np.diff(beat_times))
            if expected_interval > 0:
                # Generate expected beat times
                expected_beats = np.arange(beat_times[0], beat_times[-1] + expected_interval, expected_interval)

                # Calculate alignment error between actual and expected beats
                alignment_errors = []
                for actual_beat in beat_times:
                    # Find closest expected beat
                    distances = np.abs(expected_beats - actual_beat)
                    min_error = np.min(distances)
                    alignment_errors.append(min_error)

                # Convert to confidence (lower error = higher confidence)
                mean_error = np.mean(alignment_errors)
                max_acceptable_error = expected_interval * 0.1  # 10% of beat interval
                beat_regularity = max(0.0, 1.0 - (mean_error / max_acceptable_error))
            else:
                beat_regularity = 0.0
        else:
            beat_regularity = 0.0

        # 4. Combine all confidence measures with weights
        # Beat strength: 50% (most important for onset detection)
        # Tempo consistency: 30% (important for DJ applications)
        # Beat regularity: 20% (helps detect misaligned beats)
        enhanced_confidence = (
            0.5 * beat_strength_conf +
            0.3 * tempo_consistency +
            0.2 * beat_regularity
        )

        # Ensure result is between 0 and 1
        return min(1.0, max(0.0, enhanced_confidence))

    def _ensemble_vote_beat_detection(self, method_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Use ensemble voting to select the best beat detection result from multiple methods.

        Combines results using:
        1. Confidence-weighted tempo averaging
        2. Beat time consensus analysis
        3. Quality validation voting

        Args:
            method_results: List of beat detection results from different methods

        Returns:
            Best beat detection result based on ensemble voting
        """
        if not method_results:
            return None

        if len(method_results) == 1:
            return method_results[0]

        # 1. Filter out very poor results (confidence < 0.3)
        valid_results = [r for r in method_results if r['confidence'] >= 0.3]
        if not valid_results:
            # If all results are poor, return the best of the poor ones
            return max(method_results, key=lambda x: x['confidence'])

        # 2. Calculate consensus tempo using confidence-weighted average
        tempos = [r['tempo'] for r in valid_results]
        confidences = [r['confidence'] for r in valid_results]

        if len(tempos) > 1:
            # Convert to numpy arrays and ensure same shape
            tempos_array = np.array(tempos)
            confidences_array = np.array(confidences)

            # Use weighted average to find consensus tempo
            if len(tempos_array) == len(confidences_array) and np.sum(confidences_array) > 0:
                consensus_tempo = np.average(tempos_array, weights=confidences_array)
            else:
                consensus_tempo = np.mean(tempos_array)
            tempo_std = np.std(tempos_array)

            # Prefer results with tempo close to consensus
            tempo_scores = []
            for result in valid_results:
                tempo_diff = abs(result['tempo'] - consensus_tempo)
                tempo_score = max(0.0, 1.0 - (tempo_diff / max(1.0, tempo_std)))
                tempo_scores.append(tempo_score)
        else:
            tempo_scores = [1.0] * len(valid_results)

        # 3. Analyze beat time consensus
        beat_consensus_scores = []
        for i, result in enumerate(valid_results):
            beat_times = np.array(result['beat_times'])
            if len(beat_times) == 0:
                beat_consensus_scores.append(0.0)
                continue

            # Compare with other results to find consensus
            consensus_score = 0.0
            comparisons = 0

            for j, other_result in enumerate(valid_results):
                if i == j:
                    continue

                other_beat_times = np.array(other_result['beat_times'])
                if len(other_beat_times) == 0:
                    continue

                # Calculate beat alignment similarity
                similarity = self._calculate_beat_alignment_similarity(beat_times, other_beat_times)
                consensus_score += similarity
                comparisons += 1

            if comparisons > 0:
                beat_consensus_scores.append(consensus_score / comparisons)
            else:
                beat_consensus_scores.append(0.5)  # Neutral score if no comparisons

        # 4. Combine all scores with weights
        final_scores = []
        for i, result in enumerate(valid_results):
            # Weighted combination:
            # - Confidence: 40% (individual quality)
            # - Tempo consensus: 30% (agreement with others)
            # - Beat consensus: 30% (beat alignment agreement)
            final_score = (
                0.4 * result['confidence'] +
                0.3 * tempo_scores[i] +
                0.3 * beat_consensus_scores[i]
            )
            final_scores.append(final_score)

        # 5. Select the result with highest ensemble score
        best_index = np.argmax(final_scores)
        best_result = valid_results[best_index].copy()

        # Add ensemble metadata
        best_result['ensemble_score'] = final_scores[best_index]
        best_result['ensemble_methods'] = [r['method'] for r in valid_results]
        best_result['ensemble_consensus_tempo'] = consensus_tempo if len(tempos) > 1 else best_result['tempo']

        return best_result

    def _calculate_beat_alignment_similarity(self, beats1: np.ndarray, beats2: np.ndarray) -> float:
        """
        Calculate similarity between two sets of beat times.

        Args:
            beats1: First set of beat times
            beats2: Second set of beat times

        Returns:
            Similarity score between 0.0 and 1.0
        """
        if len(beats1) == 0 or len(beats2) == 0:
            return 0.0

        # Find overlapping time range
        start_time = max(beats1[0], beats2[0])
        end_time = min(beats1[-1], beats2[-1])

        if start_time >= end_time:
            return 0.0

        # Filter beats to overlapping range
        beats1_overlap = beats1[(beats1 >= start_time) & (beats1 <= end_time)]
        beats2_overlap = beats2[(beats2 >= start_time) & (beats2 <= end_time)]

        if len(beats1_overlap) == 0 or len(beats2_overlap) == 0:
            return 0.0

        # Calculate alignment tolerance (10% of median beat interval)
        all_intervals = np.concatenate([np.diff(beats1_overlap), np.diff(beats2_overlap)])
        if len(all_intervals) > 0:
            tolerance = np.median(all_intervals) * 0.1
        else:
            tolerance = 0.1  # Default 100ms tolerance

        # Count aligned beats
        aligned_count = 0
        for beat1 in beats1_overlap:
            distances = np.abs(beats2_overlap - beat1)
            if np.min(distances) <= tolerance:
                aligned_count += 1

        # Similarity based on proportion of aligned beats
        similarity = aligned_count / max(len(beats1_overlap), len(beats2_overlap))
        return min(1.0, similarity)

    def _extract_with_quality_retry(self, y: np.ndarray, sr: int) -> Optional[Dict[str, Any]]:
        """
        Extract beat grid with automatic quality validation and retry with different parameters.

        Tries multiple parameter sets and returns the best result that passes quality validation.

        Args:
            y: Audio time series
            sr: Sample rate

        Returns:
            Best beat grid result that passes quality validation, or None if all attempts fail
        """
        # Define different parameter sets to try (in order of preference)
        parameter_sets = [
            # Set 1: Optimized parameters (default)
            {
                "name": "optimized",
                "hop_length": 512,
                "start_bpm": 60,
                "tightness": 100,
                "trim": True
            },
            # Set 2: Higher tightness for very regular music
            {
                "name": "high_tightness",
                "hop_length": 512,
                "start_bpm": 80,
                "tightness": 200,
                "trim": True
            },
            # Set 3: Lower tightness for irregular music
            {
                "name": "low_tightness",
                "hop_length": 512,
                "start_bpm": 60,
                "tightness": 50,
                "trim": True
            },
            # Set 4: Different hop length for better time resolution
            {
                "name": "fine_resolution",
                "hop_length": 256,
                "start_bpm": 60,
                "tightness": 100,
                "trim": True
            },
            # Set 5: Conservative parameters for difficult tracks
            {
                "name": "conservative",
                "hop_length": 1024,
                "start_bpm": 120,
                "tightness": 150,
                "trim": False
            }
        ]

        best_result = None
        best_quality_score = 0.0

        for param_set in parameter_sets:
            try:
                # Extract tempo and beat frames with current parameter set
                tempo, beat_frames = librosa.beat.beat_track(
                    y=y, sr=sr,
                    hop_length=param_set["hop_length"],
                    start_bpm=param_set["start_bpm"],
                    tightness=param_set["tightness"],
                    trim=param_set["trim"]
                )

                # Convert frames to time (seconds)
                beat_times = librosa.frames_to_time(beat_frames, sr=sr)

                # Calculate enhanced confidence (with bounds checking)
                onset_env = librosa.onset.onset_strength(y=y, sr=sr, hop_length=param_set["hop_length"])
                # Ensure beat_frames are within bounds
                valid_frames = beat_frames[beat_frames < len(onset_env)]
                if len(valid_frames) > 0:
                    beat_strengths = onset_env[valid_frames]
                    # Adjust beat_times to match valid frames
                    beat_times = librosa.frames_to_time(valid_frames, sr=sr, hop_length=param_set["hop_length"])
                else:
                    beat_strengths = np.array([])
                confidence = self._calculate_enhanced_confidence(beat_times, beat_strengths, onset_env)

                # Validate beat grid quality
                validation = self._validate_beat_grid_quality(beat_times, confidence, float(tempo))

                # Create result
                result = {
                    "tempo": float(tempo),
                    "beat_times": beat_times.tolist(),
                    "confidence": confidence,
                    "quality_validation": validation,
                    "parameter_set": param_set["name"]
                }

                # Check if this result passes quality validation
                if validation["valid"] and validation["quality_score"] > best_quality_score:
                    best_result = result
                    best_quality_score = validation["quality_score"]

                    # If we found a very good result, stop trying
                    if validation["quality_score"] > 0.8:
                        break

                # Keep track of best result even if it doesn't pass validation
                elif best_result is None or validation["quality_score"] > best_quality_score:
                    best_result = result
                    best_quality_score = validation["quality_score"]

            except Exception as e:
                print(f"Error with parameter set {param_set['name']}: {str(e)}")
                continue

        return best_result

    def _detect_and_correct_tempo_doubling(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Detect and correct tempo doubling/halving issues.

        Common issues:
        - Half-time detection: Algorithm finds every other beat (tempo too slow)
        - Double-time detection: Algorithm finds sub-beats (tempo too fast)

        Args:
            result: Beat detection result to check
            y: Audio time series
            sr: Sample rate

        Returns:
            Corrected beat detection result
        """
        beat_times = np.array(result["beat_times"])
        tempo = result["tempo"]

        if len(beat_times) < 4:
            return result  # Not enough beats to analyze

        # Calculate current beat interval
        current_interval = np.median(np.diff(beat_times))
        current_bpm = 60.0 / current_interval if current_interval > 0 else tempo

        # Generate onset strength for analysis
        onset_env = librosa.onset.onset_strength(y=y, sr=sr)
        onset_times = librosa.times_like(onset_env, sr=sr)

        # Test different tempo hypotheses
        hypotheses = []

        # Hypothesis 1: Current tempo is correct
        hypotheses.append({
            "name": "original",
            "tempo": current_bpm,
            "beat_times": beat_times,
            "multiplier": 1.0
        })

        # Hypothesis 2: Double the tempo (current detection is half-time)
        if current_bpm < 100:  # Only consider doubling for slow tempos
            double_tempo = current_bpm * 2
            # Insert beats between existing beats
            doubled_beats = []
            for i in range(len(beat_times) - 1):
                doubled_beats.append(beat_times[i])
                # Add beat halfway between current beats
                mid_beat = (beat_times[i] + beat_times[i + 1]) / 2
                doubled_beats.append(mid_beat)
            doubled_beats.append(beat_times[-1])  # Add last beat

            hypotheses.append({
                "name": "doubled",
                "tempo": double_tempo,
                "beat_times": np.array(doubled_beats),
                "multiplier": 2.0
            })

        # Hypothesis 3: Half the tempo (current detection is double-time)
        if current_bpm > 140:  # Only consider halving for fast tempos
            half_tempo = current_bpm / 2
            # Take every other beat
            halved_beats = beat_times[::2]

            hypotheses.append({
                "name": "halved",
                "tempo": half_tempo,
                "beat_times": halved_beats,
                "multiplier": 0.5
            })

        # Evaluate each hypothesis
        best_hypothesis = hypotheses[0]
        best_score = 0.0

        for hypothesis in hypotheses:
            score = self._evaluate_tempo_hypothesis(hypothesis, onset_env, onset_times, y, sr)

            if score > best_score:
                best_score = score
                best_hypothesis = hypothesis

        # Apply the best hypothesis
        if best_hypothesis["name"] != "original":
            print(f"Tempo correction applied: {best_hypothesis['name']} (x{best_hypothesis['multiplier']:.1f})")
            print(f"Tempo changed: {current_bpm:.1f} -> {best_hypothesis['tempo']:.1f} BPM")

            # Update the result
            corrected_result = result.copy()
            corrected_result["tempo"] = float(best_hypothesis["tempo"])
            corrected_result["beat_times"] = best_hypothesis["beat_times"].tolist()
            corrected_result["tempo_correction"] = {
                "applied": True,
                "method": best_hypothesis["name"],
                "original_tempo": float(current_bpm),
                "corrected_tempo": float(best_hypothesis["tempo"]),
                "multiplier": best_hypothesis["multiplier"]
            }

            return corrected_result

        # No correction needed
        result["tempo_correction"] = {"applied": False}
        return result

    def _evaluate_tempo_hypothesis(self, hypothesis: Dict[str, Any], onset_env: np.ndarray, onset_times: np.ndarray, y: np.ndarray, sr: int) -> float:
        """
        Evaluate how well a tempo hypothesis matches the audio.

        Args:
            hypothesis: Tempo hypothesis to evaluate
            onset_env: Onset strength envelope
            onset_times: Time points for onset envelope
            y: Audio time series
            sr: Sample rate

        Returns:
            Score between 0.0 and 1.0 (higher is better)
        """
        beat_times = hypothesis["beat_times"]
        tempo = hypothesis["tempo"]

        if len(beat_times) < 2:
            return 0.0

        # Factor 1: Beat-onset alignment (40% weight)
        onset_alignment_score = 0.0
        for beat_time in beat_times:
            # Find closest onset time
            closest_idx = np.argmin(np.abs(onset_times - beat_time))
            if closest_idx < len(onset_env):
                # Normalize by maximum onset strength
                onset_alignment_score += onset_env[closest_idx] / np.max(onset_env)
        onset_alignment_score /= len(beat_times)

        # Factor 2: Tempo consistency (30% weight)
        intervals = np.diff(beat_times)
        if len(intervals) > 0:
            tempo_consistency = 1.0 - min(1.0, np.std(intervals) / np.mean(intervals))
        else:
            tempo_consistency = 0.0

        # Factor 3: Tempo plausibility (20% weight)
        # Prefer tempos in common ranges for different genres
        if 80 <= tempo <= 140:  # Most common range
            tempo_plausibility = 1.0
        elif 60 <= tempo <= 180:  # Extended common range
            tempo_plausibility = 0.8
        elif 40 <= tempo <= 200:  # Acceptable range
            tempo_plausibility = 0.6
        else:  # Unusual tempo
            tempo_plausibility = 0.3

        # Factor 4: Beat density appropriateness (10% weight)
        track_duration = len(y) / sr
        beats_per_minute = len(beat_times) / (track_duration / 60)
        expected_beats_per_minute = tempo

        if expected_beats_per_minute > 0:
            density_ratio = min(beats_per_minute / expected_beats_per_minute, expected_beats_per_minute / beats_per_minute)
            beat_density_score = density_ratio
        else:
            beat_density_score = 0.0

        # Combine all factors
        total_score = (
            0.4 * onset_alignment_score +
            0.3 * tempo_consistency +
            0.2 * tempo_plausibility +
            0.1 * beat_density_score
        )

        return min(1.0, max(0.0, total_score))

    def _correct_beat_phase_alignment(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Correct beat phase alignment to fix systematic offset issues.

        Based on research showing that systematic offset between beats and actual onsets
        is a common issue in beat tracking algorithms. This implements a more aggressive
        correction approach that finds the optimal phase alignment.

        Args:
            result: Beat detection result to correct
            y: Audio time series
            sr: Sample rate

        Returns:
            Phase-corrected beat detection result
        """
        beat_times = np.array(result["beat_times"])
        tempo = result["tempo"]

        if len(beat_times) < 4:
            return result  # Not enough beats to analyze phase

        # Calculate beat interval
        beat_interval = 60.0 / tempo if tempo > 0 else np.median(np.diff(beat_times))

        # Generate high-resolution onset strength envelope for precise phase analysis
        hop_length = 256  # Higher resolution for better phase detection
        onset_env = librosa.onset.onset_strength(y=y, sr=sr, hop_length=hop_length)
        onset_times = librosa.times_like(onset_env, sr=sr, hop_length=hop_length)

        # Test a wider range of phase offsets with higher resolution
        # Test offsets from -50% to +50% of beat interval (covers full beat cycle)
        max_offset = beat_interval * 0.5
        test_offsets = np.linspace(-max_offset, max_offset, 101)  # 101 test points for precision

        best_offset = 0.0
        best_alignment_score = 0.0
        original_score = 0.0

        # Calculate original alignment score
        for beat_time in beat_times:
            if 0 <= beat_time < onset_times[-1]:
                closest_idx = np.argmin(np.abs(onset_times - beat_time))
                if closest_idx < len(onset_env):
                    original_score += onset_env[closest_idx]

        if len(beat_times) > 0 and np.max(onset_env) > 0:
            original_score = original_score / (len(beat_times) * np.max(onset_env))

        # Test all phase offsets
        for offset in test_offsets:
            # Apply offset to beat times
            offset_beats = beat_times + offset

            # Calculate alignment score with onset envelope
            alignment_score = 0.0
            valid_beats = 0

            for beat_time in offset_beats:
                if 0 <= beat_time < onset_times[-1]:  # Beat is within track bounds
                    # Find closest onset time
                    closest_idx = np.argmin(np.abs(onset_times - beat_time))
                    if closest_idx < len(onset_env):
                        # Weight by onset strength
                        alignment_score += onset_env[closest_idx]
                        valid_beats += 1

            # Normalize by number of valid beats and max onset strength
            if valid_beats > 0 and np.max(onset_env) > 0:
                normalized_score = alignment_score / (valid_beats * np.max(onset_env))

                if normalized_score > best_alignment_score:
                    best_alignment_score = normalized_score
                    best_offset = offset

        # Apply correction if there's ANY improvement (remove conservative threshold)
        improvement = best_alignment_score - original_score
        if improvement > 0.01:  # Apply if there's at least 1% improvement
            corrected_beats = beat_times + best_offset

            # Ensure beats stay within track bounds
            track_duration = len(y) / sr
            corrected_beats = corrected_beats[corrected_beats >= 0]
            corrected_beats = corrected_beats[corrected_beats <= track_duration]

            print(f"Beat phase correction applied: {best_offset:.3f}s offset ({best_offset/beat_interval*100:.1f}% of beat interval)")
            print(f"Alignment improvement: {original_score:.3f} -> {best_alignment_score:.3f} (+{improvement:.3f})")

            # Update the result
            corrected_result = result.copy()
            corrected_result["beat_times"] = corrected_beats.tolist()
            corrected_result["phase_correction"] = {
                "applied": True,
                "offset_seconds": float(best_offset),
                "offset_percentage": float(best_offset / beat_interval * 100),
                "original_score": float(original_score),
                "corrected_score": float(best_alignment_score),
                "improvement": float(improvement)
            }

            return corrected_result

        # No improvement found
        print(f"Beat phase correction: No improvement found (original score: {original_score:.3f})")
        result["phase_correction"] = {
            "applied": False,
            "original_score": float(original_score),
            "best_possible_score": float(best_alignment_score)
        }
        return result

    def _refine_beat_alignment_with_onsets(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Final refinement step that aligns each beat to the nearest strong onset.

        This addresses micro-timing issues by snapping each beat to the closest
        significant onset peak within a small window.

        Args:
            result: Beat detection result to refine
            y: Audio time series
            sr: Sample rate

        Returns:
            Onset-refined beat detection result
        """
        beat_times = np.array(result["beat_times"])
        tempo = result["tempo"]

        if len(beat_times) < 2:
            return result

        # Calculate beat interval for window sizing
        beat_interval = 60.0 / tempo if tempo > 0 else np.median(np.diff(beat_times))

        # Generate high-resolution onset detection
        hop_length = 128  # Very high resolution
        onset_env = librosa.onset.onset_strength(y=y, sr=sr, hop_length=hop_length)
        onset_times = librosa.times_like(onset_env, sr=sr, hop_length=hop_length)

        # Find onset peaks (local maxima)
        from scipy.signal import find_peaks
        peaks, _ = find_peaks(onset_env, height=np.mean(onset_env) + np.std(onset_env))
        peak_times = onset_times[peaks]
        peak_strengths = onset_env[peaks]

        # Refine each beat to nearest strong onset
        refined_beats = []
        refinement_window = beat_interval * 0.15  # 15% of beat interval window

        beats_refined = 0
        total_adjustment = 0.0

        for beat_time in beat_times:
            # Find onset peaks within the refinement window
            window_mask = np.abs(peak_times - beat_time) <= refinement_window
            nearby_peaks = peak_times[window_mask]
            nearby_strengths = peak_strengths[window_mask]

            if len(nearby_peaks) > 0:
                # Choose the strongest onset peak within the window
                strongest_idx = np.argmax(nearby_strengths)
                refined_beat = nearby_peaks[strongest_idx]

                # Only apply refinement if it's a significant improvement
                adjustment = abs(refined_beat - beat_time)
                if adjustment < refinement_window * 0.8:  # Don't move too far
                    refined_beats.append(refined_beat)
                    beats_refined += 1
                    total_adjustment += adjustment
                else:
                    refined_beats.append(beat_time)  # Keep original
            else:
                refined_beats.append(beat_time)  # Keep original if no nearby onsets

        refined_beats = np.array(refined_beats)

        # Apply refinement if it improved alignment
        if beats_refined > len(beat_times) * 0.3:  # At least 30% of beats were refined
            avg_adjustment = total_adjustment / beats_refined if beats_refined > 0 else 0

            print(f"Onset-based refinement applied: {beats_refined}/{len(beat_times)} beats refined")
            print(f"Average adjustment: {avg_adjustment*1000:.1f}ms")

            # Update the result
            refined_result = result.copy()
            refined_result["beat_times"] = refined_beats.tolist()
            refined_result["onset_refinement"] = {
                "applied": True,
                "beats_refined": int(beats_refined),
                "total_beats": int(len(beat_times)),
                "refinement_percentage": float(beats_refined / len(beat_times) * 100),
                "average_adjustment_ms": float(avg_adjustment * 1000)
            }

            return refined_result

        # No significant refinement applied
        print(f"Onset-based refinement: Only {beats_refined}/{len(beat_times)} beats could be refined")
        result["onset_refinement"] = {
            "applied": False,
            "beats_refined": int(beats_refined),
            "total_beats": int(len(beat_times))
        }
        return result

    def _fix_missing_first_beat(self, result: Dict[str, Any], y: np.ndarray, sr: int) -> Dict[str, Any]:
        """
        Detect and add missing first beat at the beginning of the track.

        Many beat tracking algorithms miss the very first beat, especially when
        it starts immediately. This method detects strong onsets at the beginning
        and adds the missing first beat if needed.

        Args:
            result: Beat detection result to check
            y: Audio time series
            sr: Sample rate

        Returns:
            Beat detection result with corrected first beat
        """
        beat_times = np.array(result["beat_times"])
        tempo = result["tempo"]

        if len(beat_times) < 2:
            return result

        # Calculate expected beat interval
        beat_interval = 60.0 / tempo if tempo > 0 else np.median(np.diff(beat_times))

        # Check if first beat is missing (first detected beat is too far from start)
        first_beat_time = beat_times[0]
        expected_first_beat_threshold = beat_interval * 0.8  # 80% of beat interval

        if first_beat_time > expected_first_beat_threshold:
            print(f"Potential missing first beat detected: first beat at {first_beat_time:.3f}s (threshold: {expected_first_beat_threshold:.3f}s)")

            # Analyze the beginning of the track for strong onsets
            analysis_window = min(beat_interval * 2, 3.0)  # Analyze first 2 beats or 3 seconds
            window_samples = int(analysis_window * sr)
            y_start = y[:window_samples]

            # High-resolution onset detection at the beginning
            hop_length = 64  # Very high resolution for precise timing
            onset_env = librosa.onset.onset_strength(y=y_start, sr=sr, hop_length=hop_length)
            onset_times = librosa.times_like(onset_env, sr=sr, hop_length=hop_length)

            # Find strong onset peaks in the first part
            from scipy.signal import find_peaks
            # Use a lower threshold for the beginning to catch the first beat
            threshold = np.mean(onset_env) + 0.5 * np.std(onset_env)
            peaks, properties = find_peaks(onset_env, height=threshold, distance=int(0.1 * sr / hop_length))

            if len(peaks) > 0:
                peak_times = onset_times[peaks]
                peak_strengths = onset_env[peaks]

                # Look for a strong onset very early in the track
                early_peaks = peak_times[peak_times < beat_interval * 0.5]  # Within first half beat interval

                if len(early_peaks) > 0:
                    # Find the strongest early peak
                    early_strengths = peak_strengths[peak_times < beat_interval * 0.5]
                    strongest_early_idx = np.argmax(early_strengths)
                    candidate_first_beat = early_peaks[strongest_early_idx]

                    # Validate that this would create a reasonable beat grid
                    # Check if adding this beat creates consistent intervals
                    extended_beats = np.concatenate([[candidate_first_beat], beat_times])
                    intervals = np.diff(extended_beats)

                    # Check if the new first interval is reasonable
                    median_interval = np.median(intervals[1:])  # Median of existing intervals
                    first_interval = intervals[0]

                    interval_ratio = first_interval / median_interval
                    if 0.7 <= interval_ratio <= 1.3:  # Within 30% of expected interval
                        print(f"Adding missing first beat at {candidate_first_beat:.3f}s")
                        print(f"First interval: {first_interval:.3f}s, Median interval: {median_interval:.3f}s (ratio: {interval_ratio:.2f})")

                        # Add the first beat
                        corrected_beats = np.concatenate([[candidate_first_beat], beat_times])

                        # Update the result
                        corrected_result = result.copy()
                        corrected_result["beat_times"] = corrected_beats.tolist()
                        corrected_result["first_beat_correction"] = {
                            "applied": True,
                            "original_first_beat": float(first_beat_time),
                            "corrected_first_beat": float(candidate_first_beat),
                            "improvement_seconds": float(first_beat_time - candidate_first_beat),
                            "interval_ratio": float(interval_ratio)
                        }

                        return corrected_result
                    else:
                        print(f"Candidate first beat rejected: interval ratio {interval_ratio:.2f} outside acceptable range")
                else:
                    print("No strong early onsets found for first beat correction")
            else:
                print("No onset peaks found in track beginning")
        else:
            print(f"First beat timing looks good: {first_beat_time:.3f}s (within threshold)")

        # No correction needed or possible
        result["first_beat_correction"] = {"applied": False}
        return result

    def _validate_beat_grid_quality(self, beat_times: np.ndarray, confidence: float, tempo: float) -> Dict[str, Any]:
        """
        Validate beat grid quality and determine if it should be accepted or rejected.

        Args:
            beat_times: Array of beat times in seconds
            confidence: Confidence score from beat detection
            tempo: Detected tempo in BPM

        Returns:
            Dictionary with validation results:
            - valid: bool - Whether the beat grid passes quality checks
            - quality_score: float - Overall quality score (0-1)
            - issues: list - List of quality issues found
            - recommendations: list - Recommendations for improvement
        """
        issues = []
        recommendations = []
        valid = True

        # Quality thresholds
        MIN_CONFIDENCE = 0.7
        MIN_BEATS = 4
        MIN_TEMPO = 60
        MAX_TEMPO = 200
        MAX_TEMPO_VARIATION = 0.15  # 15% coefficient of variation

        # 1. Check minimum confidence
        if confidence < MIN_CONFIDENCE:
            issues.append(f"Low confidence score: {confidence:.2f} < {MIN_CONFIDENCE}")
            if confidence < 0.5:
                valid = False
                recommendations.append("Consider re-extracting with different parameters")
            else:
                recommendations.append("Beat grid may be inaccurate - verify manually")

        # 2. Check minimum number of beats
        if len(beat_times) < MIN_BEATS:
            issues.append(f"Too few beats detected: {len(beat_times)} < {MIN_BEATS}")
            valid = False
            recommendations.append("Track may be too short or have unclear rhythm")

        # 3. Check tempo range
        if tempo < MIN_TEMPO or tempo > MAX_TEMPO:
            issues.append(f"Tempo outside normal range: {tempo:.1f} BPM (expected {MIN_TEMPO}-{MAX_TEMPO})")
            if tempo < 30 or tempo > 300:
                valid = False
                recommendations.append("Tempo detection may have failed - check audio quality")
            else:
                recommendations.append("Unusual tempo detected - verify accuracy")

        # 4. Check tempo consistency (if enough beats)
        if len(beat_times) >= 3:
            ibis = np.diff(beat_times)
            if len(ibis) > 0 and np.mean(ibis) > 0:
                tempo_cv = np.std(ibis) / np.mean(ibis)  # Coefficient of variation
                if tempo_cv > MAX_TEMPO_VARIATION:
                    issues.append(f"High tempo variation: {tempo_cv:.2f} > {MAX_TEMPO_VARIATION}")
                    if tempo_cv > 0.3:
                        valid = False
                        recommendations.append("Track has irregular tempo - may not be suitable for DJ mixing")
                    else:
                        recommendations.append("Moderate tempo variation detected - check beat alignment")

        # 5. Check for beat clustering (beats too close together)
        if len(beat_times) >= 2:
            min_interval = np.min(np.diff(beat_times))
            expected_interval = 60.0 / tempo if tempo > 0 else 0.5
            if min_interval < expected_interval * 0.3:  # Beats closer than 30% of expected interval
                issues.append(f"Beats too close together: {min_interval:.3f}s < {expected_interval * 0.3:.3f}s")
                recommendations.append("Beat detection may have detected sub-beats or noise")

        # 6. Calculate overall quality score
        quality_factors = []

        # Confidence factor (0-1)
        quality_factors.append(min(1.0, confidence / MIN_CONFIDENCE))

        # Tempo factor (1.0 if in normal range, lower if outside)
        if MIN_TEMPO <= tempo <= MAX_TEMPO:
            tempo_factor = 1.0
        else:
            tempo_factor = max(0.0, 1.0 - abs(tempo - 120) / 120)  # Penalty based on distance from 120 BPM
        quality_factors.append(tempo_factor)

        # Tempo consistency factor
        if len(beat_times) >= 3:
            ibis = np.diff(beat_times)
            if len(ibis) > 0 and np.mean(ibis) > 0:
                tempo_cv = np.std(ibis) / np.mean(ibis)
                consistency_factor = max(0.0, 1.0 - (tempo_cv / MAX_TEMPO_VARIATION))
            else:
                consistency_factor = 0.0
        else:
            consistency_factor = 0.5  # Neutral for short tracks
        quality_factors.append(consistency_factor)

        # Beat count factor
        beat_count_factor = min(1.0, len(beat_times) / 20)  # Optimal around 20+ beats
        quality_factors.append(beat_count_factor)

        # Overall quality score (weighted average)
        quality_score = np.average(quality_factors, weights=[0.4, 0.2, 0.3, 0.1])

        return {
            "valid": valid,
            "quality_score": float(quality_score),
            "issues": issues,
            "recommendations": recommendations,
            "confidence": confidence,
            "tempo": tempo,
            "beat_count": len(beat_times),
            "tempo_variation": tempo_cv if len(beat_times) >= 3 and len(ibis) > 0 and np.mean(ibis) > 0 else None
        }
