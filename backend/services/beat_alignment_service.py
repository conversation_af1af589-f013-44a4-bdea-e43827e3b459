"""
Beat Alignment Service for Phase 3 Beat Grid Integration.

This service provides the core integration layer between beat grid detection
and beatmatching systems, enabling beat-perfect track alignment and synchronization.
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from sqlalchemy.orm import Session

from .beat_grid_service import BeatGridService
from ..models.track import Track

logger = logging.getLogger(__name__)

@dataclass
class BeatAlignment:
    """Beat alignment data structure for track synchronization."""
    track_id: int
    original_beat_times: List[float]
    aligned_beat_times: List[float]
    beat_offset: float
    sync_quality: str  # 'perfect', 'good', 'poor'
    alignment_confidence: float
    stretch_ratio: float
    master_bpm: float
    original_bpm: float

@dataclass
class BeatSyncResult:
    """Result of beat synchronization operation."""
    success: bool
    beat_alignment: Optional[BeatAlignment]
    optimal_position: float
    sync_quality_score: float
    warnings: List[str]
    error: Optional[str]

class BeatAlignmentService:
    """
    Service for integrating beat grid detection with beatmatching systems.
    
    Provides beat-perfect track alignment, beat grid preservation during
    time stretching, and professional DJ workflow capabilities.
    """
    
    def __init__(self, db: Session = None):
        self.db = db
        self.beat_grid_service = BeatGridService(db)
        # Audio processor will be initialized when needed with proper dependencies
        self.audio_processor = None
        
        # Beat alignment thresholds
        self.perfect_alignment_threshold = 0.02  # 20ms
        self.good_alignment_threshold = 0.05     # 50ms
        self.minimum_confidence_threshold = 0.7
        
        logger.info("BeatAlignmentService initialized")
    
    async def calculate_beat_alignment(
        self,
        track: Track,
        master_bpm: float,
        master_beat_grid: Optional[List[float]] = None,
        target_position: float = 0.0
    ) -> BeatSyncResult:
        """
        Calculate beat-perfect alignment for a track against master BPM/beat grid.
        
        Args:
            track: Track to align
            master_bpm: Target BPM for alignment
            master_beat_grid: Optional master beat grid for alignment
            target_position: Desired position in timeline (seconds)
            
        Returns:
            BeatSyncResult with alignment data and quality metrics
        """
        try:
            logger.info(f"Calculating beat alignment for track {track.id} to {master_bpm} BPM")
            
            # 1. Extract track's beat grid
            beat_grid_data = await self.beat_grid_service.extract_track_beat_grid(
                track.id, self.db, enhanced=True
            )
            
            if not beat_grid_data or beat_grid_data.get('error'):
                return BeatSyncResult(
                    success=False,
                    beat_alignment=None,
                    optimal_position=target_position,
                    sync_quality_score=0.0,
                    warnings=[],
                    error="Failed to extract beat grid"
                )
            
            original_bpm = beat_grid_data.get('tempo', track.bpm or 120)
            beat_times = beat_grid_data.get('beat_times', [])
            confidence = beat_grid_data.get('confidence', 0.0)
            
            if confidence < self.minimum_confidence_threshold:
                return BeatSyncResult(
                    success=False,
                    beat_alignment=None,
                    optimal_position=target_position,
                    sync_quality_score=confidence,
                    warnings=[f"Low beat grid confidence: {confidence:.2f}"],
                    error="Beat grid confidence too low for reliable alignment"
                )
            
            # 2. Calculate stretch ratio and validate compatibility
            stretch_ratio = master_bpm / original_bpm
            stretch_percentage = abs((stretch_ratio - 1.0) * 100)
            
            # 3. Calculate beat-aligned position
            optimal_position = self._calculate_optimal_beat_position(
                beat_times, target_position, stretch_ratio
            )
            
            # 4. Generate aligned beat times (after time stretching)
            aligned_beat_times = self._calculate_aligned_beat_times(
                beat_times, stretch_ratio
            )
            
            # 5. Calculate beat offset and sync quality
            beat_offset = self._calculate_beat_offset(
                optimal_position, aligned_beat_times
            )
            
            sync_quality, quality_score = self._assess_sync_quality(
                beat_offset, confidence, stretch_percentage
            )
            
            # 6. Create beat alignment result
            beat_alignment = BeatAlignment(
                track_id=track.id,
                original_beat_times=beat_times,
                aligned_beat_times=aligned_beat_times,
                beat_offset=beat_offset,
                sync_quality=sync_quality,
                alignment_confidence=confidence,
                stretch_ratio=stretch_ratio,
                master_bpm=master_bpm,
                original_bpm=original_bpm
            )
            
            warnings = []
            if stretch_percentage > 15:
                warnings.append(f"Large tempo change: {stretch_percentage:.1f}%")
            if beat_offset > self.good_alignment_threshold:
                warnings.append(f"Beat alignment offset: {beat_offset*1000:.1f}ms")
            
            return BeatSyncResult(
                success=True,
                beat_alignment=beat_alignment,
                optimal_position=optimal_position,
                sync_quality_score=quality_score,
                warnings=warnings,
                error=None
            )
            
        except Exception as e:
            logger.error(f"Error calculating beat alignment for track {track.id}: {e}")
            return BeatSyncResult(
                success=False,
                beat_alignment=None,
                optimal_position=target_position,
                sync_quality_score=0.0,
                warnings=[],
                error=str(e)
            )
    
    def snap_to_nearest_beat(
        self,
        position: float,
        beat_times: List[float],
        tolerance: float = 0.1
    ) -> Tuple[float, float]:
        """
        Snap a position to the nearest beat boundary.
        
        Args:
            position: Target position in seconds
            beat_times: List of beat positions in seconds
            tolerance: Maximum distance to snap (seconds)
            
        Returns:
            Tuple of (snapped_position, distance_to_beat)
        """
        if not beat_times:
            return position, float('inf')
        
        beat_array = np.array(beat_times)
        distances = np.abs(beat_array - position)
        nearest_idx = np.argmin(distances)
        nearest_beat = beat_times[nearest_idx]
        distance = distances[nearest_idx]
        
        if distance <= tolerance:
            return nearest_beat, distance
        else:
            return position, distance
    
    def _calculate_optimal_beat_position(
        self,
        beat_times: List[float],
        target_position: float,
        stretch_ratio: float
    ) -> float:
        """Calculate optimal beat-aligned position for track placement."""
        if not beat_times:
            return target_position
        
        # Apply stretch ratio to beat times
        stretched_beats = [t / stretch_ratio for t in beat_times]
        
        # Find nearest beat to target position
        snapped_position, _ = self.snap_to_nearest_beat(
            target_position, stretched_beats
        )
        
        return snapped_position
    
    def _calculate_aligned_beat_times(
        self,
        original_beat_times: List[float],
        stretch_ratio: float
    ) -> List[float]:
        """Calculate beat times after time stretching."""
        return [t / stretch_ratio for t in original_beat_times]
    
    def _calculate_beat_offset(
        self,
        position: float,
        beat_times: List[float]
    ) -> float:
        """Calculate offset from nearest beat."""
        if not beat_times:
            return float('inf')
        
        _, offset = self.snap_to_nearest_beat(position, beat_times)
        return offset
    
    def _assess_sync_quality(
        self,
        beat_offset: float,
        confidence: float,
        stretch_percentage: float
    ) -> Tuple[str, float]:
        """Assess synchronization quality based on multiple factors."""
        # Base score from beat grid confidence
        quality_score = confidence
        
        # Penalize large beat offsets
        if beat_offset <= self.perfect_alignment_threshold:
            alignment_factor = 1.0
            quality_level = 'perfect'
        elif beat_offset <= self.good_alignment_threshold:
            alignment_factor = 0.8
            quality_level = 'good'
        else:
            alignment_factor = 0.5
            quality_level = 'poor'
        
        # Penalize large stretch ratios
        stretch_factor = max(0.5, 1.0 - (stretch_percentage / 100))
        
        # Calculate final quality score
        final_score = quality_score * alignment_factor * stretch_factor
        
        # Determine quality level based on final score
        if final_score >= 0.9 and quality_level == 'perfect':
            return 'perfect', final_score
        elif final_score >= 0.7:
            return 'good', final_score
        else:
            return 'poor', final_score
    
    async def preserve_beat_grid_during_stretching(
        self,
        track: Track,
        stretch_ratio: float,
        original_beat_grid: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Preserve and update beat grid accuracy during time stretching.
        
        Args:
            track: Track being processed
            stretch_ratio: Time stretching ratio applied
            original_beat_grid: Original beat grid data
            
        Returns:
            Updated beat grid data with preserved accuracy
        """
        try:
            logger.info(f"Preserving beat grid during stretching for track {track.id}")
            
            # Update beat times with stretch ratio
            original_beats = original_beat_grid.get('beat_times', [])
            stretched_beats = [t / stretch_ratio for t in original_beats]
            
            # Update tempo
            original_tempo = original_beat_grid.get('tempo', 120)
            new_tempo = original_tempo * stretch_ratio
            
            # Preserve other beat grid data
            updated_beat_grid = {
                **original_beat_grid,
                'beat_times': stretched_beats,
                'tempo': new_tempo,
                'stretch_ratio': stretch_ratio,
                'original_tempo': original_tempo,
                'time_stretched': True,
                'stretch_timestamp': np.datetime64('now').astype(str)
            }
            
            # Update enhanced data if present
            if 'downbeats' in original_beat_grid:
                updated_beat_grid['downbeats'] = [
                    t / stretch_ratio for t in original_beat_grid['downbeats']
                ]
            
            if 'bars' in original_beat_grid:
                updated_beat_grid['bars'] = [
                    [start / stretch_ratio, end / stretch_ratio]
                    for start, end in original_beat_grid['bars']
                ]
            
            logger.info(f"Beat grid preserved: {original_tempo:.1f} → {new_tempo:.1f} BPM")
            return updated_beat_grid
            
        except Exception as e:
            logger.error(f"Error preserving beat grid during stretching: {e}")
            return original_beat_grid
