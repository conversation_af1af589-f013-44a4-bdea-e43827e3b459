"""
Coqui TTS Service for fast, lightweight text-to-speech synthesis.
"""

import os
import logging
from pathlib import Path
from typing import Optional
import soundfile as sf

from TTS.api import TTS

logger = logging.getLogger(__name__)

class CoquiTTSService:
    """Service for text-to-speech using Coqui TTS."""

    def __init__(self, model_name: str = "tts_models/en/ljspeech/tacotron2-DDC", out_dir: str = "cache/tts"):
        self.model_name = model_name
        self.out_dir = Path(out_dir)
        self.out_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Loading Coqui TTS model: {model_name}")
        self.tts = TTS(model_name)
        logger.info("Coqui TTS model loaded.")

    def synthesize(self, text: str, file_name: Optional[str] = None) -> str:
        if not file_name:
            import time
            file_name = f"tts_{int(time.time())}.wav"
        out_path = self.out_dir / file_name
        wav = self.tts.tts(text)
        sf.write(str(out_path), wav, self.tts.synthesizer.output_sample_rate)
        return str(out_path)
