[{"server_id": "default", "name": "Default MCP Server", "description": "Default MCP server for DJ Mix Constructor", "host": "127.0.0.1", "port": 8000, "transport": "stdio", "enabled": true, "server_command": null, "tool_prefixes": [], "metadata": {}, "created_at": 1746743879.838901, "updated_at": 1746743879.838901, "last_connected_at": null, "status": "disconnected", "error": null}, {"server_id": "audio-analysis", "name": "Audio Analysis", "description": "Advanced audio analysis for DJ mixing", "host": "127.0.0.1", "port": 0, "transport": "stdio", "enabled": true, "server_command": ["python", "-m", "backend.services.ai.mcp_audio_analysis"], "tool_prefixes": ["analyze_", "extract_", "detect_", "download_"], "metadata": {"category": "audio", "version": "1.0.0", "features": ["BPM detection", "Key detection", "Beat grid extraction", "Segment detection", "Chroma analysis", "MFCC analysis", "YouTube downloading", "URL downloading"]}, "created_at": 1746744145.49841, "updated_at": 1753897683.641233, "last_connected_at": null, "status": "disconnected", "error": null}]