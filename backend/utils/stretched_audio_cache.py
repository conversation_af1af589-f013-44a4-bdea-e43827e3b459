"""
Stretched Audio Cache Management Utilities.

This module provides utilities for managing the stretched audio cache,
including cleanup, monitoring, and optimization functions.
"""

import os
import json
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

class StretchedAudioCache:
    """
    Manages the stretched audio cache with intelligent cleanup and monitoring.
    """
    
    def __init__(self, cache_dir: Optional[Path] = None):
        """
        Initialize the cache manager.
        
        Args:
            cache_dir: Directory for cached files (defaults to backend/static/stretched_audio)
        """
        if cache_dir is None:
            self.cache_dir = Path(__file__).parent.parent / "static" / "stretched_audio"
        else:
            self.cache_dir = cache_dir
        
        # Create cache directory if it doesn't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache settings
        self.max_cache_size_gb = 5.0
        self.max_file_age_days = 30
        self.cleanup_threshold = 0.8  # Clean up when 80% full
        
        logger.info(f"StretchedAudioCache initialized: {self.cache_dir}")
    
    def generate_cache_key(self, track_id: int, target_bpm: float, quality_mode: str = "high") -> str:
        """Generate a unique cache key for stretched audio."""
        key_string = f"track_{track_id}_bpm_{target_bpm:.1f}_quality_{quality_mode}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get_cache_file_paths(self, cache_key: str) -> Dict[str, Path]:
        """Get the file paths for a cache entry."""
        return {
            "audio": self.cache_dir / f"{cache_key}.wav",
            "metadata": self.cache_dir / f"{cache_key}.json"
        }
    
    def cache_exists(self, cache_key: str) -> bool:
        """Check if a cache entry exists."""
        paths = self.get_cache_file_paths(cache_key)
        return paths["audio"].exists() and paths["metadata"].exists()
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """Get comprehensive cache information."""
        try:
            # Get all cache files
            audio_files = list(self.cache_dir.glob("*.wav"))
            metadata_files = list(self.cache_dir.glob("*.json"))
            
            # Calculate sizes and ages
            total_size = 0
            file_info = []
            
            for audio_file in audio_files:
                try:
                    stat = audio_file.stat()
                    size = stat.st_size
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    age_days = (datetime.now() - mtime).days
                    
                    # Get metadata if available
                    metadata_file = audio_file.with_suffix(".json")
                    metadata = {}
                    if metadata_file.exists():
                        try:
                            with open(metadata_file, 'r') as f:
                                metadata = json.load(f)
                        except Exception as e:
                            logger.warning(f"Failed to read metadata for {audio_file}: {e}")
                    
                    file_info.append({
                        "cache_key": audio_file.stem,
                        "audio_file": str(audio_file),
                        "metadata_file": str(metadata_file),
                        "size_bytes": size,
                        "size_mb": round(size / (1024**2), 2),
                        "age_days": age_days,
                        "last_modified": mtime.isoformat(),
                        "track_id": metadata.get("track_id"),
                        "original_bpm": metadata.get("original_bpm"),
                        "target_bpm": metadata.get("target_bpm"),
                        "quality_mode": metadata.get("quality_mode"),
                        "quality_score": metadata.get("quality_metrics", {}).get("quality_score")
                    })
                    
                    total_size += size
                    
                except Exception as e:
                    logger.warning(f"Error processing cache file {audio_file}: {e}")
            
            # Sort by last modified (newest first)
            file_info.sort(key=lambda x: x["last_modified"], reverse=True)
            
            total_size_gb = total_size / (1024**3)
            usage_percentage = (total_size_gb / self.max_cache_size_gb) * 100
            
            return {
                "cache_directory": str(self.cache_dir),
                "total_files": len(audio_files),
                "metadata_files": len(metadata_files),
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024**2), 2),
                "total_size_gb": round(total_size_gb, 3),
                "max_size_gb": self.max_cache_size_gb,
                "usage_percentage": round(usage_percentage, 1),
                "cleanup_needed": usage_percentage > (self.cleanup_threshold * 100),
                "files": file_info
            }
            
        except Exception as e:
            logger.error(f"Failed to get cache info: {e}")
            return {"error": str(e)}
    
    async def cleanup_cache(self, force: bool = False, max_age_days: Optional[int] = None) -> Dict[str, Any]:
        """
        Clean up old or excess cached files.
        
        Args:
            force: Force cleanup even if not needed
            max_age_days: Maximum age for files (defaults to self.max_file_age_days)
            
        Returns:
            Dictionary with cleanup results
        """
        try:
            cache_info = await self.get_cache_info()
            
            if "error" in cache_info:
                return cache_info
            
            cleanup_needed = cache_info.get("cleanup_needed", False)
            
            if not force and not cleanup_needed:
                return {
                    "cleaned": False,
                    "reason": "Cleanup not needed",
                    "cache_info": cache_info
                }
            
            max_age = max_age_days or self.max_file_age_days
            files_to_remove = []
            
            # Find files to remove based on age and size constraints
            for file_info in cache_info["files"]:
                should_remove = False
                reason = []
                
                # Remove old files
                if file_info["age_days"] > max_age:
                    should_remove = True
                    reason.append(f"age > {max_age} days")
                
                # Remove files if cache is too full (remove oldest first)
                if cleanup_needed and len(files_to_remove) < len(cache_info["files"]) // 3:
                    should_remove = True
                    reason.append("cache size cleanup")
                
                if should_remove:
                    files_to_remove.append({
                        **file_info,
                        "removal_reason": ", ".join(reason)
                    })
            
            # Remove files
            removed_count = 0
            removed_size = 0
            errors = []
            
            for file_info in files_to_remove:
                try:
                    audio_path = Path(file_info["audio_file"])
                    metadata_path = Path(file_info["metadata_file"])
                    
                    # Remove audio file
                    if audio_path.exists():
                        audio_path.unlink()
                        removed_size += file_info["size_bytes"]
                        removed_count += 1
                    
                    # Remove metadata file
                    if metadata_path.exists():
                        metadata_path.unlink()
                    
                    logger.info(f"Removed cached file: {file_info['cache_key']} ({file_info['removal_reason']})")
                    
                except Exception as e:
                    error_msg = f"Failed to remove {file_info['cache_key']}: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            # Get updated cache info
            updated_cache_info = await self.get_cache_info()
            
            result = {
                "cleaned": True,
                "removed_files": removed_count,
                "removed_size_mb": round(removed_size / (1024**2), 2),
                "removed_size_gb": round(removed_size / (1024**3), 3),
                "errors": errors,
                "cache_info_before": cache_info,
                "cache_info_after": updated_cache_info
            }
            
            logger.info(f"Cache cleanup completed: removed {removed_count} files ({result['removed_size_mb']} MB)")
            return result
            
        except Exception as e:
            logger.error(f"Cache cleanup failed: {e}")
            return {"error": str(e)}
    
    async def get_cache_statistics(self) -> Dict[str, Any]:
        """Get detailed cache statistics."""
        try:
            cache_info = await self.get_cache_info()
            
            if "error" in cache_info:
                return cache_info
            
            files = cache_info.get("files", [])
            
            # Calculate statistics
            stats = {
                "total_files": len(files),
                "total_size_gb": cache_info.get("total_size_gb", 0),
                "average_file_size_mb": 0,
                "oldest_file_days": 0,
                "newest_file_days": 0,
                "quality_distribution": {},
                "bpm_range_distribution": {},
                "track_count": 0
            }
            
            if files:
                # Average file size
                total_size_mb = sum(f["size_mb"] for f in files)
                stats["average_file_size_mb"] = round(total_size_mb / len(files), 2)
                
                # Age statistics
                ages = [f["age_days"] for f in files]
                stats["oldest_file_days"] = max(ages)
                stats["newest_file_days"] = min(ages)
                
                # Quality distribution
                quality_counts = {}
                for f in files:
                    quality = f.get("quality_mode", "unknown")
                    quality_counts[quality] = quality_counts.get(quality, 0) + 1
                stats["quality_distribution"] = quality_counts
                
                # BPM range distribution
                bpm_ranges = {"<100": 0, "100-120": 0, "120-140": 0, "140-160": 0, ">160": 0}
                track_ids = set()
                
                for f in files:
                    target_bpm = f.get("target_bpm")
                    if target_bpm:
                        if target_bpm < 100:
                            bpm_ranges["<100"] += 1
                        elif target_bpm < 120:
                            bpm_ranges["100-120"] += 1
                        elif target_bpm < 140:
                            bpm_ranges["120-140"] += 1
                        elif target_bpm < 160:
                            bpm_ranges["140-160"] += 1
                        else:
                            bpm_ranges[">160"] += 1
                    
                    track_id = f.get("track_id")
                    if track_id:
                        track_ids.add(track_id)
                
                stats["bpm_range_distribution"] = bpm_ranges
                stats["track_count"] = len(track_ids)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get cache statistics: {e}")
            return {"error": str(e)}
    
    async def optimize_cache(self) -> Dict[str, Any]:
        """Optimize the cache by removing duplicates and low-quality entries."""
        try:
            cache_info = await self.get_cache_info()
            
            if "error" in cache_info:
                return cache_info
            
            files = cache_info.get("files", [])
            optimizations = []
            
            # Group files by track_id and target_bpm to find duplicates
            track_bpm_groups = {}
            for file_info in files:
                track_id = file_info.get("track_id")
                target_bpm = file_info.get("target_bpm")
                
                if track_id and target_bpm:
                    key = f"{track_id}_{target_bpm}"
                    if key not in track_bpm_groups:
                        track_bpm_groups[key] = []
                    track_bmp_groups[key].append(file_info)
            
            # For each group, keep only the highest quality version
            files_to_remove = []
            for group_files in track_bpm_groups.values():
                if len(group_files) > 1:
                    # Sort by quality score (highest first)
                    group_files.sort(key=lambda x: x.get("quality_score", 0), reverse=True)
                    
                    # Keep the best one, remove the rest
                    for file_info in group_files[1:]:
                        files_to_remove.append(file_info)
                        optimizations.append(f"Removed duplicate: {file_info['cache_key']} (lower quality)")
            
            # Remove low-quality files (quality score < 0.5)
            for file_info in files:
                quality_score = file_info.get("quality_score", 1.0)
                if quality_score and quality_score < 0.5:
                    files_to_remove.append(file_info)
                    optimizations.append(f"Removed low quality: {file_info['cache_key']} (score: {quality_score:.2f})")
            
            # Remove the files
            removed_count = 0
            removed_size = 0
            
            for file_info in files_to_remove:
                try:
                    audio_path = Path(file_info["audio_file"])
                    metadata_path = Path(file_info["metadata_file"])
                    
                    if audio_path.exists():
                        audio_path.unlink()
                        removed_size += file_info["size_bytes"]
                        removed_count += 1
                    
                    if metadata_path.exists():
                        metadata_path.unlink()
                        
                except Exception as e:
                    logger.error(f"Failed to remove file during optimization: {e}")
            
            return {
                "optimized": True,
                "removed_files": removed_count,
                "removed_size_mb": round(removed_size / (1024**2), 2),
                "optimizations": optimizations,
                "cache_info_after": await self.get_cache_info()
            }
            
        except Exception as e:
            logger.error(f"Cache optimization failed: {e}")
            return {"error": str(e)}
