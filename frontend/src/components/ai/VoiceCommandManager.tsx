import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';

// Define command categories
export enum CommandCategory {
  NAVIGATION = 'navigation',
  PLAYBACK = 'playback',
  INTERFACE = 'interface',
  SETTINGS = 'settings',
  GENERAL = 'general'
}

// Define a voice command structure
export interface VoiceCommand {
  id: string;
  name: string;
  category: CommandCategory;
  phrases: string[];
  description: string;
  action: (params?: any) => void;
  requiresConfirmation?: boolean;
  enabled?: boolean;
}

// Props for the VoiceCommandManager
interface VoiceCommandManagerProps {
  onCommandRecognized?: (command: VoiceCommand, matchedPhrase: string, params?: any) => void;
  onCommandExecuted?: (command: VoiceCommand, success: boolean) => void;
  children?: React.ReactNode;
}

const VoiceCommandManager: React.FC<VoiceCommandManagerProps> = ({
  onCommandRecognized,
  onCommandExecuted,
  children
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { addRecentAction } = useApplicationContext();
  
  // Store command settings in local storage
  const [commandSettings, setCommandSettings] = useLocalStorage('voice-command-settings', {
    enabled: true,
    commandsEnabled: {} as Record<string, boolean>,
    sensitivity: 0.7, // How closely the phrase needs to match
    requireConfirmation: true,
  });
  
  // State for commands
  const [commands, setCommands] = useState<VoiceCommand[]>([]);
  const [isProcessingCommand, setIsProcessingCommand] = useState(false);
  const [lastRecognizedCommand, setLastRecognizedCommand] = useState<{
    command: VoiceCommand | null;
    phrase: string;
    params: any;
    timestamp: number;
  }>({
    command: null,
    phrase: '',
    params: null,
    timestamp: 0
  });

  // Initialize commands
  useEffect(() => {
    initializeCommands();
  }, [navigate]);

  // Initialize the available commands
  const initializeCommands = () => {
    const availableCommands: VoiceCommand[] = [
      // Navigation commands
      {
        id: 'nav-home',
        name: 'Go to Home',
        category: CommandCategory.NAVIGATION,
        phrases: ['go to home', 'navigate to home', 'open home page', 'show home'],
        description: 'Navigate to the home page',
        action: () => navigate('/'),
        enabled: true
      },
      {
        id: 'nav-mix-timeline',
        name: 'Go to Mix Timeline',
        category: CommandCategory.NAVIGATION,
        phrases: ['go to mix timeline', 'open mix timeline', 'show mix timeline', 'navigate to timeline'],
        description: 'Navigate to the mix timeline page',
        action: () => navigate('/mix-timeline-restructured'),
        enabled: true
      },
      {
        id: 'nav-settings',
        name: 'Go to Settings',
        category: CommandCategory.NAVIGATION,
        phrases: ['go to settings', 'open settings', 'show settings', 'navigate to settings'],
        description: 'Navigate to the settings page',
        action: () => navigate('/settings'),
        enabled: true
      },
      {
        id: 'nav-ai-settings',
        name: 'Go to AI Settings',
        category: CommandCategory.NAVIGATION,
        phrases: ['go to ai settings', 'open ai settings', 'show ai settings', 'navigate to ai settings'],
        description: 'Navigate to the AI settings page',
        action: () => navigate('/ai-settings'),
        enabled: true
      },
      
      // Interface commands
      {
        id: 'ui-toggle-sidebar',
        name: 'Toggle Sidebar',
        category: CommandCategory.INTERFACE,
        phrases: ['toggle sidebar', 'show sidebar', 'hide sidebar', 'open sidebar', 'close sidebar'],
        description: 'Toggle the sidebar visibility',
        action: () => {
          // This is a placeholder - will be implemented when we connect to the actual app context
          toast({
            title: 'Command recognized',
            description: 'Toggle sidebar command would be executed here',
          });
        },
        enabled: true
      },
      
      // Playback commands - these will need to be connected to the actual audio playback system
      {
        id: 'playback-play',
        name: 'Play',
        category: CommandCategory.PLAYBACK,
        phrases: ['play', 'start playback', 'resume playback', 'play music'],
        description: 'Start or resume playback',
        action: () => {
          // Placeholder
          toast({
            title: 'Command recognized',
            description: 'Play command would be executed here',
          });
        },
        enabled: true
      },
      {
        id: 'playback-pause',
        name: 'Pause',
        category: CommandCategory.PLAYBACK,
        phrases: ['pause', 'pause playback', 'pause music', 'stop playback'],
        description: 'Pause playback',
        action: () => {
          // Placeholder
          toast({
            title: 'Command recognized',
            description: 'Pause command would be executed here',
          });
        },
        enabled: true
      },
      
      // General commands
      {
        id: 'general-help',
        name: 'Help',
        category: CommandCategory.GENERAL,
        phrases: ['help', 'show help', 'voice commands help', 'what can I say', 'command list'],
        description: 'Show voice command help',
        action: () => {
          // Placeholder - will show a help dialog
          toast({
            title: 'Voice Commands Help',
            description: 'A list of available voice commands would be shown here',
          });
        },
        enabled: true
      }
    ];
    
    // Apply stored enabled/disabled state
    const commandsWithSettings = availableCommands.map(command => ({
      ...command,
      enabled: commandSettings.commandsEnabled[command.id] !== undefined 
        ? commandSettings.commandsEnabled[command.id] 
        : command.enabled
    }));
    
    setCommands(commandsWithSettings);
  };

  // Process speech input to detect commands
  const processSpeechInput = (text: string): boolean => {
    if (!commandSettings.enabled || isProcessingCommand) return false;
    
    const normalizedText = text.toLowerCase().trim();
    
    // Check each command for matching phrases
    for (const command of commands) {
      if (!command.enabled) continue;
      
      for (const phrase of command.phrases) {
        if (normalizedText.includes(phrase)) {
          // Extract parameters if any (text after the command phrase)
          const params = normalizedText.replace(phrase, '').trim();
          
          // Set as recognized command
          setLastRecognizedCommand({
            command,
            phrase,
            params: params || null,
            timestamp: Date.now()
          });
          
          // Notify parent component
          if (onCommandRecognized) {
            onCommandRecognized(command, phrase, params || null);
          }
          
          // Execute the command if no confirmation required
          if (!command.requiresConfirmation && !commandSettings.requireConfirmation) {
            executeCommand(command, params);
          } else {
            // Show confirmation toast
            toast({
              title: 'Command Recognized',
              description: `Say "yes" to execute: ${command.name}`,
            });
            
            // Listen for confirmation
            // This would need to be implemented with the speech recognition system
          }
          
          return true;
        }
      }
    }
    
    return false;
  };
  
  // Execute a command
  const executeCommand = (command: VoiceCommand, params?: any) => {
    setIsProcessingCommand(true);
    
    try {
      // Execute the command action
      command.action(params);
      
      // Log the action
      addRecentAction({
        type: 'voice_command_executed',
        description: `Voice command executed: ${command.name}`
      });
      
      // Notify parent component
      if (onCommandExecuted) {
        onCommandExecuted(command, true);
      }
      
      // Show success toast
      toast({
        title: 'Command Executed',
        description: `Successfully executed: ${command.name}`,
      });
    } catch (error) {
      console.error('Error executing voice command:', error);
      
      // Notify parent component
      if (onCommandExecuted) {
        onCommandExecuted(command, false);
      }
      
      // Show error toast
      toast({
        title: 'Command Failed',
        description: `Failed to execute: ${command.name}`,
        variant: 'destructive'
      });
    } finally {
      setIsProcessingCommand(false);
    }
  };
  
  // Get all available commands
  const getAvailableCommands = () => {
    return commands.filter(command => command.enabled);
  };
  
  // Enable or disable a command
  const toggleCommand = (commandId: string, enabled: boolean) => {
    // Update commands state
    setCommands(prev => 
      prev.map(cmd => 
        cmd.id === commandId ? { ...cmd, enabled } : cmd
      )
    );
    
    // Update local storage settings
    setCommandSettings(prev => ({
      ...prev,
      commandsEnabled: {
        ...prev.commandsEnabled,
        [commandId]: enabled
      }
    }));
  };
  
  // Enable or disable all commands
  const setCommandsEnabled = (enabled: boolean) => {
    setCommandSettings(prev => ({
      ...prev,
      enabled
    }));
  };
  
  // The context value to provide
  const contextValue = {
    commands,
    processSpeechInput,
    executeCommand,
    getAvailableCommands,
    toggleCommand,
    setCommandsEnabled,
    isProcessingCommand,
    lastRecognizedCommand,
    settings: commandSettings
  };
  
  return (
    <div>
      {children}
    </div>
  );
};

export default VoiceCommandManager;
