// TypeScript: Extend window for SpeechRecognition APIs (fix for type errors)
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Mic, MicOff, Volume2, VolumeX, Loader2, Play, Square, Settings, Command } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Slider } from '@/components/ui/slider';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { useVoiceCommands } from '@/providers/VoiceCommandProvider';

interface VoiceInteractionProps {
  onSpeechResult?: (text: string) => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
  textToSpeak?: string;
  autoSpeak?: boolean;
  className?: string;
  showSpeakButton?: boolean;
  onSpeakStart?: () => void;
  onSpeakEnd?: () => void;
  mode?: 'input' | 'output' | 'both';
  size?: 'sm' | 'md' | 'lg';
  enableCommands?: boolean;
}

const VoiceInteraction: React.FC<VoiceInteractionProps> = ({
  onSpeechResult,
  onSpeechStart,
  onSpeechEnd,
  textToSpeak,
  autoSpeak = false,
  className = '',
  showSpeakButton = false,
  onSpeakStart,
  onSpeakEnd,
  mode = 'both',
  size = 'sm',
  enableCommands = true
}) => {
  // Get voice commands context if available
  let voiceCommands;
  try {
    voiceCommands = useVoiceCommands();
  } catch (error) {
    // Voice commands provider not available, will work without it
    voiceCommands = null;
  }
  // Get voice settings from local storage with defaults
  const [voiceSettings, setVoiceSettings] = useLocalStorage('voice-settings', {
    volume: 80,
    speechRate: 1,
    preferredVoiceName: '',
  });

  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(false);
  const [synthesisSupported, setSynthesisSupported] = useState(false);
  const [volume, setVolume] = useState(voiceSettings.volume);
  const [speechRate, setSpeechRate] = useState(voiceSettings.speechRate);
  const [showVoiceControls, setShowVoiceControls] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>(voiceSettings.preferredVoiceName);

  const recognitionRef = useRef<any>(null);
  const { toast } = useToast();

  // Initialize speech recognition and synthesis
  useEffect(() => {
    // Check for speech recognition support
    if (mode === 'both' || mode === 'input') {
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = false;
        recognitionRef.current.interimResults = false;
        recognitionRef.current.lang = 'en-US';

        recognitionRef.current.onresult = (event: any) => {
          const transcript = event.results[0][0].transcript;

          // Check if this is a voice command
          let isCommand = false;
          if (enableCommands && voiceCommands && voiceCommands.settings.enabled) {
            isCommand = voiceCommands.processSpeechInput(transcript);
          }

          // If not a command or commands are disabled, pass to normal speech handler
          if (!isCommand && onSpeechResult) {
            onSpeechResult(transcript);
          }
        };

        recognitionRef.current.onend = () => {
          setIsListening(false);
          if (onSpeechEnd) {
            onSpeechEnd();
          }
        };

        recognitionRef.current.onerror = (event: any) => {
          console.error('Speech recognition error', event.error);
          setIsListening(false);

          let description = `Error: ${event.error}`;
          if (event.error === 'network') {
            description = `Network error: Speech recognition requires an internet connection and may require HTTPS.\nPlease check your connection and ensure you are running the app over HTTPS or localhost.`;
          } else if (event.error === 'not-allowed') {
            description = `Microphone access denied. Please allow microphone access in your browser settings.`;
          }

          toast({
            title: 'Speech Recognition Error',
            description,
            variant: 'destructive',
          });
        };

        setSpeechSupported(true);
      } else {
        setSpeechSupported(false);
      }
    }

    // Check for speech synthesis support
    if (mode === 'both' || mode === 'output') {
      if ('speechSynthesis' in window) {
        setSynthesisSupported(true);

        // Load available voices
        const loadVoices = () => {
          const voices = window.speechSynthesis.getVoices();
          if (voices.length > 0) {
            setAvailableVoices(voices);
          }
        };

        // Load voices immediately if available
        loadVoices();

        // Chrome loads voices asynchronously, so we need to listen for the voiceschanged event
        window.speechSynthesis.onvoiceschanged = loadVoices;

        return () => {
          // Cleanup
          window.speechSynthesis.onvoiceschanged = null;
        };
      } else {
        setSynthesisSupported(false);
      }
    }
  }, [toast, onSpeechEnd, onSpeechResult, mode]);

  // Auto-speak text when it changes
  useEffect(() => {
    if (autoSpeak && textToSpeak && !isSpeaking) {
      handleSpeak();
    }
  }, [textToSpeak, autoSpeak]);

  // Toggle speech recognition
  const toggleListening = () => {
    if (!speechSupported) {
      toast({
        title: 'Speech Recognition Not Supported',
        description: 'Your browser does not support speech recognition.',
        variant: 'destructive',
      });
      return;
    }

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
      if (onSpeechEnd) {
        onSpeechEnd();
      }
    } else {
      try {
        recognitionRef.current.start();
        setIsListening(true);
        if (onSpeechStart) {
          onSpeechStart();
        }
      } catch (error) {
        console.error('Speech recognition error', error);
        toast({
          title: 'Speech Recognition Error',
          description: 'Could not start speech recognition.',
          variant: 'destructive',
        });
      }
    }
  };

  // Save voice settings to local storage
  const saveVoiceSettings = () => {
    setVoiceSettings({
      volume,
      speechRate,
      preferredVoiceName: selectedVoice
    });
  };

  // Effect to save settings when they change
  useEffect(() => {
    saveVoiceSettings();
  }, [volume, speechRate, selectedVoice]);

  // Speak text using Spark-TTS
  const handleSpeak = async () => {
    if (!textToSpeak) return;

    try {
      setIsSpeaking(true);
      if (onSpeakStart) {
        onSpeakStart();
      }

      // Use Spark-TTS API instead of browser's Web Speech API
      const response = await fetch('/api/v1/tts/synthesize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: textToSpeak,
          preset_id: selectedVoice || 'default',
          voice_params: {
            // Only include these if no preset is selected
            ...((!selectedVoice || selectedVoice === 'default') && {
              gender: 'male', // Default gender
              pitch: 3, // Default pitch (1-5)
              speed: speechRate * 2, // Map speechRate (0.5-2) to speed (1-5)
            }),
          },
          use_cache: true,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to synthesize speech: ${response.statusText}`);
      }

      // Get audio blob from response
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      // Create and play audio element
      const audio = new Audio(audioUrl);
      audio.volume = volume / 100;

      audio.onended = () => {
        setIsSpeaking(false);
        if (onSpeakEnd) {
          onSpeakEnd();
        }
        URL.revokeObjectURL(audioUrl); // Clean up
      };

      audio.onerror = (event) => {
        console.error('Audio playback error', event);
        setIsSpeaking(false);
        URL.revokeObjectURL(audioUrl); // Clean up

        toast({
          title: 'Speech Playback Error',
          description: 'Could not play the synthesized speech.',
          variant: 'destructive',
        });
      };

      // Play the audio
      await audio.play();
    } catch (error) {
      console.error('Speech synthesis error', error);
      setIsSpeaking(false);

      // Fallback to browser's Web Speech API if Spark-TTS fails
      if (synthesisSupported) {
        toast({
          title: 'Using Fallback TTS',
          description: 'Spark-TTS failed, using browser TTS instead.',
          variant: 'warning',
        });

        // Cancel any ongoing speech
        window.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(textToSpeak);
        utterance.volume = volume / 100;
        utterance.rate = speechRate;
        utterance.lang = 'en-US';

        // Get available voices
        const voices = window.speechSynthesis.getVoices();
        const voiceToUse = voices.find(voice =>
          voice.name.includes('Google') ||
          voice.name.includes('Natural') ||
          voice.name.includes('Premium')
        );

        if (voiceToUse) {
          utterance.voice = voiceToUse;
        }

        utterance.onstart = () => {
          setIsSpeaking(true);
          if (onSpeakStart) {
            onSpeakStart();
          }
        };

        utterance.onend = () => {
          setIsSpeaking(false);
          if (onSpeakEnd) {
            onSpeakEnd();
          }
        };

        window.speechSynthesis.speak(utterance);
      } else {
        toast({
          title: 'Speech Synthesis Error',
          description: 'Could not synthesize speech.',
          variant: 'destructive',
        });
      }
    }
  };

  // Stop speaking
  const handleStopSpeaking = () => {
    // For browser's Web Speech API
    if (synthesisSupported) {
      window.speechSynthesis.cancel();
    }

    // For our custom audio elements
    // This will stop all audio elements on the page
    document.querySelectorAll('audio').forEach(audio => {
      audio.pause();
      audio.currentTime = 0;
    });

    setIsSpeaking(false);
    if (onSpeakEnd) {
      onSpeakEnd();
    }
  };

  // Get button size based on size prop
  const getButtonSize = () => {
    switch (size) {
      case 'sm': return 'h-8 w-8';
      case 'md': return 'h-9 w-9';
      case 'lg': return 'h-10 w-10';
      default: return 'h-8 w-8';
    }
  };

  // Get icon size based on size prop
  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'h-4 w-4';
      case 'md': return 'h-5 w-5';
      case 'lg': return 'h-6 w-6';
      default: return 'h-4 w-4';
    }
  };

  // Check if command mode is active
  const isCommandModeActive = enableCommands && voiceCommands && voiceCommands.settings.enabled;

  return (
    <div className={`flex items-center gap-1.5 ${className}`}>
      {/* Speech recognition button - only show if mode includes input */}
      {(mode === 'both' || mode === 'input') && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event bubbling
                  toggleListening();
                }}
                disabled={!speechSupported}
                className={`${getButtonSize()} p-0 rounded-full ${
                  isListening
                    ? isCommandModeActive
                      ? 'bg-purple-500 hover:bg-purple-600 text-white'
                      : 'bg-red-500 hover:bg-red-600 text-white'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {isListening ? (
                  isCommandModeActive ? (
                    <Command className={getIconSize()} />
                  ) : (
                    <MicOff className={getIconSize()} />
                  )
                ) : (
                  <Mic className={getIconSize()} />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>
                {isListening
                  ? isCommandModeActive
                    ? 'Command mode active - say a command'
                    : 'Stop listening'
                  : 'Voice input'}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Listening indicator */}
      {isListening && (
        <span className={`ml-1 text-xs ${isCommandModeActive ? 'text-purple-500' : 'text-red-500'} animate-pulse`}>
          {isCommandModeActive ? 'Command mode...' : 'Listening...'}
        </span>
      )}

      {/* Text-to-speech buttons - only show if mode includes output */}
      {(mode === 'both' || mode === 'output') && showSpeakButton && (
        <>
          {/* Speak button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent event bubbling
                    isSpeaking ? handleStopSpeaking() : handleSpeak();
                  }}
                  disabled={!synthesisSupported || !textToSpeak}
                  className={`${getButtonSize()} p-0 rounded-full ${isSpeaking ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}`}
                >
                  {isSpeaking ? (
                    <Square className={getIconSize()} />
                  ) : (
                    <Play className={getIconSize()} />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>{isSpeaking ? 'Stop speaking' : 'Speak text'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Voice settings */}
          <Popover>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`${getButtonSize()} p-0 rounded-full text-muted-foreground hover:text-foreground`}
                    >
                      <Settings className={getIconSize()} />
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Voice settings</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">Voice Settings</h4>

                {/* Volume control */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="voice-volume">Volume</Label>
                    <span className="text-xs text-muted-foreground">{volume}%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <VolumeX className="h-4 w-4 text-muted-foreground" />
                    <Slider
                      id="voice-volume"
                      min={0}
                      max={100}
                      step={5}
                      value={[volume]}
                      onValueChange={(value) => setVolume(value[0])}
                      className="flex-1"
                    />
                    <Volume2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>

                {/* Speech rate control */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="speech-rate">Speech Rate</Label>
                    <span className="text-xs text-muted-foreground">{speechRate.toFixed(1)}x</span>
                  </div>
                  <Slider
                    id="speech-rate"
                    min={0.5}
                    max={2}
                    step={0.1}
                    value={[speechRate]}
                    onValueChange={(value) => setSpeechRate(value[0])}
                  />
                </div>

                {/* Voice selection */}
                <div className="space-y-2">
                  <Label htmlFor="voice-select">Voice</Label>
                  <div className="flex items-center gap-2">
                    <select
                      id="voice-select"
                      value={selectedVoice}
                      onChange={(e) => setSelectedVoice(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="default">Default Voice</option>
                      <option value="male-1">Male Voice</option>
                      <option value="female-1">Female Voice</option>
                      {/* We'll fetch custom voices from the API in a future update */}
                    </select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Open the TTS Settings page in a new window/tab
                        window.open('/tts-settings', '_blank');
                      }}
                    >
                      More
                    </Button>
                  </div>
                </div>

                {/* Test voice button */}
                <Button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/v1/tts/test-voice/' + (selectedVoice || 'default'), {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          text: "This is a test of the selected voice and settings."
                        }),
                      });

                      if (!response.ok) {
                        throw new Error(`Failed to test voice: ${response.statusText}`);
                      }

                      const audioBlob = await response.blob();
                      const audioUrl = URL.createObjectURL(audioBlob);
                      const audio = new Audio(audioUrl);
                      audio.volume = volume / 100;

                      audio.onended = () => {
                        URL.revokeObjectURL(audioUrl);
                      };

                      await audio.play();
                    } catch (error) {
                      console.error('Voice test error', error);
                      toast({
                        title: 'Voice Test Failed',
                        description: 'Could not test the selected voice.',
                        variant: 'destructive',
                      });

                      // Fallback to browser's Web Speech API
                      if (synthesisSupported) {
                        const testUtterance = new SpeechSynthesisUtterance("This is a test of the selected voice and settings.");
                        testUtterance.volume = volume / 100;
                        testUtterance.rate = speechRate;
                        window.speechSynthesis.speak(testUtterance);
                      }
                    }
                  }}
                  className="w-full"
                >
                  Test Voice
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </>
      )}

      {/* Speaking indicator */}
      {isSpeaking && (
        <span className="ml-1 text-xs text-primary animate-pulse">
          Speaking...
        </span>
      )}
    </div>
  );
};

export default VoiceInteraction;
