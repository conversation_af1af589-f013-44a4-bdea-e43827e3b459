import React from 'react';
import { useMCP } from '@/providers/MCPProvider';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Command,
  ShieldAlert,
  Info,
  CheckCircle2,
  XCircle
} from 'lucide-react';

interface MCPToolPermissionRequestProps {
  toolName: string;
  parameters: any;
  isOpen: boolean;
  onAllow: () => void;
  onDeny: () => void;
}

const MCPToolPermissionRequest: React.FC<MCPToolPermissionRequestProps> = ({
  toolName,
  parameters,
  isOpen,
  onAllow,
  onDeny
}) => {
  const { availableTools } = useMCP();
  
  // Find the tool in the available tools
  const tool = availableTools.find(t => t.name === toolName);
  
  // Get tool description
  const description = tool?.description || 'No description available';
  
  // Get tool category
  const category = tool?.category || 'Unknown';
  
  // Get parameter descriptions
  const parameterDescriptions = tool?.parameters?.properties || {};
  
  // Format parameters for display
  const formatParameterValue = (value: any): string => {
    if (value === null || value === undefined) {
      return 'null';
    } else if (typeof value === 'object') {
      return JSON.stringify(value);
    } else {
      return String(value);
    }
  };
  
  // Determine if the tool is potentially sensitive
  const isSensitiveTool = () => {
    const sensitiveKeywords = ['delete', 'remove', 'modify', 'update', 'create', 'write', 'save'];
    return sensitiveKeywords.some(keyword => toolName.toLowerCase().includes(keyword));
  };
  
  return (
    <AlertDialog open={isOpen}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            {isSensitiveTool() ? (
              <ShieldAlert className="h-5 w-5 text-destructive" />
            ) : (
              <Command className="h-5 w-5 text-primary" />
            )}
            Tool Permission Request
          </AlertDialogTitle>
          <AlertDialogDescription>
            The AI assistant wants to use the following tool:
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <div className="py-2">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium">{toolName}</h3>
            <Badge variant="outline" className="text-xs">
              {category}
            </Badge>
          </div>
          
          <p className="text-sm text-muted-foreground mb-3">
            {description}
          </p>
          
          <Separator className="my-2" />
          
          <div className="space-y-2">
            <h4 className="text-xs font-medium flex items-center gap-1.5">
              <Info className="h-3.5 w-3.5 text-muted-foreground" />
              Parameters
            </h4>
            
            <ScrollArea className="h-32 w-full rounded-md border">
              <div className="p-3 space-y-2">
                {Object.entries(parameters).map(([key, value]) => (
                  <div key={key} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium">{key}</span>
                      <Badge variant="secondary" className="text-[10px]">
                        {typeof value}
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {parameterDescriptions[key]?.description || 'No description available'}
                    </div>
                    <div className="text-xs bg-muted p-1 rounded overflow-x-auto">
                      {formatParameterValue(value)}
                    </div>
                  </div>
                ))}
                
                {Object.keys(parameters).length === 0 && (
                  <div className="text-xs text-muted-foreground">
                    No parameters provided
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
          
          {isSensitiveTool() && (
            <div className="mt-3 flex items-start gap-2 p-2 bg-destructive/10 rounded-md text-xs text-destructive">
              <ShieldAlert className="h-4 w-4 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Caution: This tool can modify your data</p>
                <p>This tool has permissions to make changes to your data. Only allow if you trust the AI's intent.</p>
              </div>
            </div>
          )}
        </div>
        
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onDeny} className="flex items-center gap-1.5">
            <XCircle className="h-4 w-4" />
            Deny
          </AlertDialogCancel>
          <AlertDialogAction onClick={onAllow} className="flex items-center gap-1.5">
            <CheckCircle2 className="h-4 w-4" />
            Allow
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default MCPToolPermissionRequest;
