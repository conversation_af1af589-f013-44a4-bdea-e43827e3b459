import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  EnhancedTrackAnalysis, 
  TrackSection, 
  BPMConfidence, 
  KeyConfidence,
  EnergySegment
} from '@/types/audioAnalysis';

interface EnhancedAudioAnalysisProps {
  analysis: EnhancedTrackAnalysis;
  className?: string;
}

const EnhancedAudioAnalysis: React.FC<EnhancedAudioAnalysisProps> = ({
  analysis,
  className = ''
}) => {
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Enhanced Audio Analysis</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <Tabs defaultValue="bpm">
          <TabsList className="grid grid-cols-4 h-8">
            <TabsTrigger value="bpm" className="text-xs">BPM</TabsTrigger>
            <TabsTrigger value="key" className="text-xs">Key</TabsTrigger>
            <TabsTrigger value="structure" className="text-xs">Structure</TabsTrigger>
            <TabsTrigger value="energy" className="text-xs">Energy</TabsTrigger>
          </TabsList>
          
          <TabsContent value="bpm" className="mt-2 space-y-4">
            <BPMAnalysis 
              bpm={analysis.bpm} 
              confidence={analysis.bpmConfidence} 
              alternatives={analysis.bpmAlternatives}
              variations={analysis.bpmRange}
            />
          </TabsContent>
          
          <TabsContent value="key" className="mt-2 space-y-4">
            <KeyAnalysis 
              key={analysis.key} 
              confidence={analysis.keyConfidence} 
              alternatives={analysis.keyAlternatives}
              keyChanges={analysis.keyChanges}
            />
          </TabsContent>
          
          <TabsContent value="structure" className="mt-2 space-y-4">
            <StructureAnalysis sections={analysis.sections} />
          </TabsContent>
          
          <TabsContent value="energy" className="mt-2 space-y-4">
            <EnergyAnalysis 
              overallEnergy={analysis.energyLevel} 
              segments={analysis.energySegments} 
              danceability={analysis.danceability}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

interface BPMAnalysisProps {
  bpm: number;
  confidence: number;
  alternatives?: BPMConfidence[];
  variations?: [number, number];
}

const BPMAnalysis: React.FC<BPMAnalysisProps> = ({
  bpm,
  confidence,
  alternatives,
  variations
}) => {
  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Primary BPM</h3>
          <Badge variant="secondary">{bpm.toFixed(1)}</Badge>
        </div>
        
        <Progress value={confidence} className="h-2" />
        <p className="text-xs text-right">Confidence: {confidence.toFixed(1)}%</p>
      </div>
      
      {variations && (
        <div className="space-y-1">
          <h3 className="text-sm font-medium">BPM Range</h3>
          <div className="flex justify-between text-xs">
            <span>{variations[0].toFixed(1)}</span>
            <span>{variations[1].toFixed(1)}</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary" 
              style={{ 
                marginLeft: `${(variations[0] / (variations[1] * 1.5)) * 100}%`,
                width: `${((variations[1] - variations[0]) / variations[1]) * 100}%`
              }}
            ></div>
          </div>
        </div>
      )}
      
      {alternatives && alternatives.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Alternative BPMs</h3>
          <div className="space-y-1">
            {alternatives.map((alt, index) => (
              <div key={index} className="flex justify-between items-center">
                <Badge variant="outline">{alt.bpm.toFixed(1)}</Badge>
                <span className="text-xs text-muted-foreground">{alt.confidence.toFixed(1)}% confidence</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface KeyAnalysisProps {
  key: string;
  confidence: number;
  alternatives?: KeyConfidence[];
  keyChanges?: { time: number, key: string, confidence: number }[];
}

const KeyAnalysis: React.FC<KeyAnalysisProps> = ({
  key,
  confidence,
  alternatives,
  keyChanges
}) => {
  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Primary Key</h3>
          <Badge variant="secondary">{key}</Badge>
        </div>
        
        <Progress value={confidence} className="h-2" />
        <p className="text-xs text-right">Confidence: {confidence.toFixed(1)}%</p>
      </div>
      
      {alternatives && alternatives.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Alternative Keys</h3>
          <div className="space-y-1">
            {alternatives.map((alt, index) => (
              <div key={index} className="flex justify-between items-center">
                <Badge variant="outline">{alt.key}</Badge>
                <span className="text-xs text-muted-foreground">{alt.confidence.toFixed(1)}% confidence</span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {keyChanges && keyChanges.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Key Changes</h3>
          <div className="space-y-1">
            {keyChanges.map((change, index) => (
              <div key={index} className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <span className="text-xs">{formatTime(change.time)}</span>
                  <Badge variant="outline">{change.key}</Badge>
                </div>
                <span className="text-xs text-muted-foreground">{change.confidence.toFixed(1)}%</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface StructureAnalysisProps {
  sections: TrackSection[];
}

const StructureAnalysis: React.FC<StructureAnalysisProps> = ({
  sections
}) => {
  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">Track Structure</h3>
      
      <div className="relative h-12 bg-muted rounded-md overflow-hidden">
        {sections.map((section, index) => {
          // Calculate section position and width based on start/end times
          const totalDuration = sections[sections.length - 1].end;
          const left = (section.start / totalDuration) * 100;
          const width = ((section.end - section.start) / totalDuration) * 100;
          
          // Get color based on section type
          const color = getSectionColor(section.type);
          
          return (
            <div
              key={index}
              className={`absolute h-full ${color} border-r border-background`}
              style={{ left: `${left}%`, width: `${width}%` }}
              title={`${section.type} (${formatTime(section.start)} - ${formatTime(section.end)})`}
            >
              <div className="h-full flex items-center justify-center">
                <span className="text-[8px] text-white font-bold uppercase truncate px-1">
                  {section.type}
                </span>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
        {sections.map((section, index) => (
          <div key={index} className="flex justify-between items-center text-xs">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getSectionTextColor(section.type)}>
                {section.type}
              </Badge>
              <span>{formatTime(section.start)} - {formatTime(section.end)}</span>
            </div>
            <div className="flex items-center gap-2">
              <span>Energy: {section.energy}/10</span>
              {section.bpm && <span>BPM: {section.bpm}</span>}
              {section.key && <span>Key: {section.key}</span>}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface EnergyAnalysisProps {
  overallEnergy: number;
  segments: EnergySegment[];
  danceability: number;
}

const EnergyAnalysis: React.FC<EnergyAnalysisProps> = ({
  overallEnergy,
  segments,
  danceability
}) => {
  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Overall Energy</h3>
          <Badge variant="secondary">{overallEnergy}/10</Badge>
        </div>
        
        <Progress value={overallEnergy * 10} className="h-2 bg-muted" />
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Danceability</h3>
          <Badge variant="secondary">{(danceability * 100).toFixed(0)}%</Badge>
        </div>
        
        <Progress value={danceability * 100} className="h-2 bg-muted" />
      </div>
      
      <div className="space-y-2">
        <h3 className="text-sm font-medium">Energy Flow</h3>
        
        <div className="relative h-12 bg-muted rounded-md overflow-hidden">
          {segments.map((segment, index) => {
            // Calculate segment position and width
            const totalDuration = segments[segments.length - 1].end;
            const left = (segment.start / totalDuration) * 100;
            const width = ((segment.end - segment.start) / totalDuration) * 100;
            
            // Get color based on energy level
            const color = getEnergyColor(segment.energy);
            
            return (
              <div
                key={index}
                className={`absolute h-full ${color} border-r border-background`}
                style={{ left: `${left}%`, width: `${width}%` }}
                title={`Energy: ${segment.energy}/10 (${formatTime(segment.start)} - ${formatTime(segment.end)})`}
              >
                <div className="h-full flex items-center justify-center">
                  <span className="text-[10px] text-white font-bold">
                    {segment.energy}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// Helper functions
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const getSectionColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'intro': 'bg-blue-500',
    'verse': 'bg-green-500',
    'chorus': 'bg-purple-500',
    'drop': 'bg-red-500',
    'breakdown': 'bg-yellow-500',
    'bridge': 'bg-indigo-500',
    'outro': 'bg-gray-500',
    'section': 'bg-slate-500'
  };
  
  return colorMap[type.toLowerCase()] || 'bg-slate-500';
};

const getSectionTextColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'intro': 'text-blue-500',
    'verse': 'text-green-500',
    'chorus': 'text-purple-500',
    'drop': 'text-red-500',
    'breakdown': 'text-yellow-500',
    'bridge': 'text-indigo-500',
    'outro': 'text-gray-500',
    'section': 'text-slate-500'
  };
  
  return colorMap[type.toLowerCase()] || 'text-slate-500';
};

const getEnergyColor = (energy: number): string => {
  const colors = [
    'bg-blue-900',   // 1
    'bg-blue-700',   // 2
    'bg-blue-500',   // 3
    'bg-green-500',  // 4
    'bg-green-400',  // 5
    'bg-yellow-400', // 6
    'bg-yellow-500', // 7
    'bg-orange-500', // 8
    'bg-red-500',    // 9
    'bg-red-600'     // 10
  ];
  
  return colors[Math.min(9, Math.max(0, energy - 1))];
};

export default EnhancedAudioAnalysis;
