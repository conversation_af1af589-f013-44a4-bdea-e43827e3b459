import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  ChevronDown,
  ChevronUp,
  Download,
  Command,
  Copy,
  ExternalLink,
  Music,
  AudioWaveform,
  BarChart3,
  Clock,
  Music2,
  Disc3,
  Shuffle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface MCPToolResultVisualizerProps {
  toolName: string;
  result: any;
  className?: string;
}

const MCPToolResultVisualizer: React.FC<MCPToolResultVisualizerProps> = ({
  toolName,
  result,
  className = ''
}) => {
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = React.useState(false);

  // Handle copy button click
  const handleCopy = () => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2));
    toast({
      title: 'Copied to clipboard',
      description: 'The tool result has been copied to your clipboard.',
      duration: 3000
    });
  };

  // Handle download button click
  const handleDownload = () => {
    const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${toolName.replace(/\s+/g, '_').toLowerCase()}_result.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Determine the visualization type based on the tool name and result
  const getVisualizationType = () => {
    if (toolName.includes('track') || toolName.includes('Track')) {
      return 'track';
    } else if (toolName.includes('collection') || toolName.includes('Collection')) {
      return 'collection';
    } else if (toolName.includes('transition') || toolName.includes('Transition')) {
      return 'transition';
    } else if (toolName.includes('bpm') || toolName.includes('BPM')) {
      return 'bpm';
    } else if (toolName.includes('key') || toolName.includes('Key')) {
      return 'key';
    } else if (toolName.includes('energy') || toolName.includes('Energy')) {
      return 'energy';
    } else {
      return 'generic';
    }
  };

  // Render track visualization
  const renderTrackVisualization = () => {
    if (!result || typeof result !== 'object') {
      return <div className="text-sm text-muted-foreground">No track data available</div>;
    }

    const track = result.track_info || result.track || result;

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Music className="h-5 w-5 text-primary" />
          <div>
            <h3 className="text-sm font-medium">{track.title || 'Unknown Track'}</h3>
            <p className="text-xs text-muted-foreground">{track.artist || 'Unknown Artist'}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1.5">
            <AudioWaveform className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">BPM:</span>
            <span className="font-medium">{track.bpm || 'N/A'}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <Music2 className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Key:</span>
            <span className="font-medium">{track.key || 'N/A'}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Energy:</span>
            <span className="font-medium">{track.energy || 'N/A'}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Length:</span>
            <span className="font-medium">
              {track.length ? `${Math.floor(track.length / 60)}:${(track.length % 60).toString().padStart(2, '0')}` : 'N/A'}
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Render transition visualization
  const renderTransitionVisualization = () => {
    if (!result || typeof result !== 'object') {
      return <div className="text-sm text-muted-foreground">No transition data available</div>;
    }

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Shuffle className="h-5 w-5 text-primary" />
            <div>
              <h3 className="text-sm font-medium">{result.transition_type || 'Unknown'} Transition</h3>
              <p className="text-xs text-muted-foreground">Duration: {result.duration || 'N/A'} beats</p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            {result.transition_id || 'N/A'}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div className="border rounded-md p-2">
            <h4 className="text-xs font-medium mb-1">From Track</h4>
            {result.from_track && (
              <div className="text-xs">
                <p className="font-medium">{result.from_track.title || 'Unknown'}</p>
                <p className="text-muted-foreground">{result.from_track.artist || 'Unknown'}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-[10px]">
                    {result.from_track.bpm || 'N/A'} BPM
                  </Badge>
                  <Badge variant="secondary" className="text-[10px]">
                    {result.from_track.key || 'N/A'}
                  </Badge>
                </div>
              </div>
            )}
          </div>

          <div className="border rounded-md p-2">
            <h4 className="text-xs font-medium mb-1">To Track</h4>
            {result.to_track && (
              <div className="text-xs">
                <p className="font-medium">{result.to_track.title || 'Unknown'}</p>
                <p className="text-muted-foreground">{result.to_track.artist || 'Unknown'}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-[10px]">
                    {result.to_track.bpm || 'N/A'} BPM
                  </Badge>
                  <Badge variant="secondary" className="text-[10px]">
                    {result.to_track.key || 'N/A'}
                  </Badge>
                </div>
              </div>
            )}
          </div>
        </div>

        {result.message && (
          <div className="text-xs text-muted-foreground mt-2">
            {result.message}
          </div>
        )}
      </div>
    );
  };

  // Render collection visualization
  const renderCollectionVisualization = () => {
    if (!result || typeof result !== 'object') {
      return <div className="text-sm text-muted-foreground">No collection data available</div>;
    }

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Disc3 className="h-5 w-5 text-primary" />
          <div>
            <h3 className="text-sm font-medium">{result.collection_name || 'Unknown Collection'}</h3>
            <p className="text-xs text-muted-foreground">
              {result.track_count || result.total_matches || 0} tracks
            </p>
          </div>
        </div>

        {result.tracks && result.tracks.length > 0 && (
          <div className="mt-2">
            <h4 className="text-xs font-medium mb-1">Tracks</h4>
            <ScrollArea className="h-24 w-full rounded-md border">
              <div className="p-2 space-y-1">
                {result.tracks.slice(0, 5).map((track: any, index: number) => (
                  <div key={index} className="text-xs flex items-center justify-between">
                    <div>
                      <span className="font-medium">{track.title || 'Unknown'}</span>
                      <span className="text-muted-foreground"> by </span>
                      <span>{track.artist || 'Unknown'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge variant="outline" className="text-[10px]">
                        {track.bpm || 'N/A'} BPM
                      </Badge>
                      <Badge variant="outline" className="text-[10px]">
                        {track.key || 'N/A'}
                      </Badge>
                    </div>
                  </div>
                ))}
                {result.tracks.length > 5 && (
                  <div className="text-xs text-muted-foreground text-center pt-1">
                    + {result.tracks.length - 5} more tracks
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    );
  };

  // Render generic visualization
  const renderGenericVisualization = () => {
    return (
      <div className="space-y-2">
        <pre className="text-xs overflow-auto p-2 bg-muted rounded-md max-h-32">
          {JSON.stringify(result, null, 2)}
        </pre>
      </div>
    );
  };

  // Render visualization based on type
  const renderVisualization = () => {
    const type = getVisualizationType();

    switch (type) {
      case 'track':
        return renderTrackVisualization();
      case 'transition':
        return renderTransitionVisualization();
      case 'collection':
        return renderCollectionVisualization();
      default:
        return renderGenericVisualization();
    }
  };

  return (
    <Card className={`border-dashed ${className}`}>
      <CardHeader className="py-3 px-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-1.5">
            <Command className="h-4 w-4 text-primary" />
            {toolName}
          </CardTitle>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleCopy}
            >
              <Copy className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleDownload}
            >
              <Download className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="h-3.5 w-3.5" />
              ) : (
                <ChevronDown className="h-3.5 w-3.5" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <>
          <Separator />
          <CardContent className="py-3 px-4">
            {renderVisualization()}
          </CardContent>
        </>
      )}
    </Card>
  );
};

export default MCPToolResultVisualizer;
