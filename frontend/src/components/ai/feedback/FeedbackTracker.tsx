import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, BarChart2, MessageSquare, ThumbsUp, ThumbsDown, Star, AlertTriangle, CheckCircle2, Info, Filter } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { aiSettingsMonitoringService, UserSatisfactionMetrics } from '@/services/monitoring/aiSettingsMonitoring';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface FeedbackTrackerProps {
  className?: string;
}

interface FeedbackEntry {
  id: string;
  timestamp: string;
  featureId: string;
  featureType: string;
  contentId?: string;
  contentType?: string;
  rating: 'positive' | 'negative';
  stars?: number;
  comment?: string;
  userId?: string;
}

const FeedbackTracker: React.FC<FeedbackTrackerProps> = ({
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('24');
  const [selectedFeature, setSelectedFeature] = useState<string>('all');
  const [selectedContentType, setSelectedContentType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [ratingFilter, setRatingFilter] = useState<string>('all');
  
  const [feedbackEntries, setFeedbackEntries] = useState<FeedbackEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<FeedbackEntry[]>([]);
  const [showComments, setShowComments] = useState<boolean>(true);
  
  const { toast } = useToast();
  
  // Available features
  const features = [
    { id: 'all', name: 'All Features' },
    { id: 'ai_assistant', name: 'AI Assistant' },
    { id: 'style_creator', name: 'Style Creator' },
    { id: 'transition_suggester', name: 'Transition Suggester' },
    { id: 'collection_analyzer', name: 'Collection Analyzer' }
  ];
  
  // Available content types
  const contentTypes = [
    { id: 'all', name: 'All Content Types' },
    { id: 'assistant_message', name: 'Assistant Message' },
    { id: 'style_parameters', name: 'Style Parameters' },
    { id: 'transition_suggestion', name: 'Transition Suggestion' },
    { id: 'collection_analysis', name: 'Collection Analysis' }
  ];
  
  // Load feedback data
  const loadFeedbackData = async () => {
    setIsLoading(true);
    
    try {
      // This would normally come from the backend API, but we'll simulate it for the demo
      // In a real implementation, we would call an API endpoint to get the feedback entries
      
      // Generate sample feedback entries
      const sampleEntries: FeedbackEntry[] = [];
      
      // Generate random timestamps within the selected time range
      const now = new Date();
      const timeRangeHours = parseInt(timeRange);
      const startTime = new Date(now.getTime() - timeRangeHours * 60 * 60 * 1000);
      
      // Generate sample entries for each feature
      features.filter(f => f.id !== 'all').forEach(feature => {
        // Generate 5-15 entries per feature
        const entryCount = Math.floor(Math.random() * 10) + 5;
        
        for (let i = 0; i < entryCount; i++) {
          // Generate random timestamp
          const timestamp = new Date(
            startTime.getTime() + Math.random() * (now.getTime() - startTime.getTime())
          ).toISOString();
          
          // Determine content type based on feature
          let contentType = '';
          switch (feature.id) {
            case 'ai_assistant':
              contentType = 'assistant_message';
              break;
            case 'style_creator':
              contentType = 'style_parameters';
              break;
            case 'transition_suggester':
              contentType = 'transition_suggestion';
              break;
            case 'collection_analyzer':
              contentType = 'collection_analysis';
              break;
          }
          
          // Generate random rating (70% positive, 30% negative)
          const rating = Math.random() < 0.7 ? 'positive' : 'negative';
          
          // Generate random stars (1-5)
          const stars = rating === 'positive' 
            ? Math.floor(Math.random() * 2) + 4 // 4-5 for positive
            : Math.floor(Math.random() * 2) + 1; // 1-2 for negative
          
          // Generate random comment (50% chance)
          let comment = undefined;
          if (Math.random() < 0.5) {
            const comments = [
              'This was very helpful!',
              'Great suggestion, thanks!',
              'Not what I was looking for.',
              'Could be more detailed.',
              'Perfect recommendation!',
              'Too generic, needs improvement.',
              'Exactly what I needed!',
              'Confusing response.',
              'Very intuitive suggestion.',
              'Doesn\'t match my style.'
            ];
            comment = comments[Math.floor(Math.random() * comments.length)];
          }
          
          // Add entry
          sampleEntries.push({
            id: `feedback_${feature.id}_${i}`,
            timestamp,
            featureId: feature.id,
            featureType: feature.id,
            contentType,
            contentId: `content_${Math.floor(Math.random() * 1000)}`,
            rating,
            stars,
            comment,
            userId: `user_${Math.floor(Math.random() * 10)}`
          });
        }
      });
      
      // Sort by timestamp (newest first)
      sampleEntries.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      setFeedbackEntries(sampleEntries);
      
      toast({
        title: 'Feedback data loaded',
        description: `Loaded ${sampleEntries.length} feedback entries for the last ${timeRange} hours.`,
      });
    } catch (error) {
      console.error('Error loading feedback data:', error);
      
      toast({
        title: 'Error loading feedback data',
        description: 'Failed to load feedback data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Apply filters to feedback entries
  useEffect(() => {
    if (feedbackEntries.length === 0) return;
    
    let filtered = [...feedbackEntries];
    
    // Filter by feature
    if (selectedFeature !== 'all') {
      filtered = filtered.filter(entry => entry.featureId === selectedFeature);
    }
    
    // Filter by content type
    if (selectedContentType !== 'all') {
      filtered = filtered.filter(entry => entry.contentType === selectedContentType);
    }
    
    // Filter by rating
    if (ratingFilter !== 'all') {
      filtered = filtered.filter(entry => entry.rating === ratingFilter);
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry => 
        (entry.comment && entry.comment.toLowerCase().includes(query)) ||
        entry.featureId.toLowerCase().includes(query) ||
        (entry.contentType && entry.contentType.toLowerCase().includes(query))
      );
    }
    
    setFilteredEntries(filtered);
  }, [feedbackEntries, selectedFeature, selectedContentType, ratingFilter, searchQuery]);
  
  // Load data on mount
  useEffect(() => {
    loadFeedbackData();
  }, [timeRange]);
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };
  
  // Get feature name
  const getFeatureName = (featureId: string) => {
    const feature = features.find(f => f.id === featureId);
    return feature ? feature.name : featureId;
  };
  
  // Get content type name
  const getContentTypeName = (contentTypeId: string) => {
    const contentType = contentTypes.find(c => c.id === contentTypeId);
    return contentType ? contentType.name : contentTypeId;
  };
  
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle>Feedback Tracker</CardTitle>
        <CardDescription>
          Track and analyze user feedback by feature and content type
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Time Range</label>
                <Select
                  value={timeRange}
                  onValueChange={setTimeRange}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select time range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="24">Last 24 Hours</SelectItem>
                    <SelectItem value="72">Last 3 Days</SelectItem>
                    <SelectItem value="168">Last 7 Days</SelectItem>
                    <SelectItem value="720">Last 30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="show-comments" 
                  checked={showComments} 
                  onCheckedChange={(checked) => setShowComments(!!checked)} 
                />
                <Label htmlFor="show-comments">Show Comments</Label>
              </div>
            </div>
            
            <Button
              variant="outline"
              onClick={loadFeedbackData}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </>
              )}
            </Button>
          </div>
          
          <Separator />
          
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search feedback..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            
            <div className="flex flex-col md:flex-row gap-4">
              <Select
                value={selectedFeature}
                onValueChange={setSelectedFeature}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by feature" />
                </SelectTrigger>
                <SelectContent>
                  {features.map(feature => (
                    <SelectItem key={feature.id} value={feature.id}>
                      {feature.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select
                value={selectedContentType}
                onValueChange={setSelectedContentType}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by content type" />
                </SelectTrigger>
                <SelectContent>
                  {contentTypes.map(contentType => (
                    <SelectItem key={contentType.id} value={contentType.id}>
                      {contentType.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select
                value={ratingFilter}
                onValueChange={setRatingFilter}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by rating" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Ratings</SelectItem>
                  <SelectItem value="positive">Positive</SelectItem>
                  <SelectItem value="negative">Negative</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>Feature</TableHead>
                  <TableHead>Content Type</TableHead>
                  <TableHead>Rating</TableHead>
                  {showComments && <TableHead>Comment</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={showComments ? 5 : 4} className="text-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading feedback data...</p>
                    </TableCell>
                  </TableRow>
                ) : filteredEntries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={showComments ? 5 : 4} className="text-center py-8">
                      <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-muted-foreground">No feedback entries found matching the current filters.</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEntries.map(entry => (
                    <TableRow key={entry.id}>
                      <TableCell className="whitespace-nowrap">
                        {formatDate(entry.timestamp)}
                      </TableCell>
                      <TableCell>
                        {getFeatureName(entry.featureId)}
                      </TableCell>
                      <TableCell>
                        {entry.contentType ? getContentTypeName(entry.contentType) : '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {entry.rating === 'positive' ? (
                            <ThumbsUp className="h-4 w-4 text-green-500 mr-1" />
                          ) : (
                            <ThumbsDown className="h-4 w-4 text-red-500 mr-1" />
                          )}
                          {entry.stars && (
                            <div className="flex ml-2">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <Star 
                                  key={i} 
                                  className={`h-3 w-3 ${i < entry.stars! ? 'fill-yellow-500 text-yellow-500' : 'text-muted-foreground'}`} 
                                />
                              ))}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      {showComments && (
                        <TableCell className="max-w-[300px] truncate">
                          {entry.comment || '-'}
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {filteredEntries.length} of {feedbackEntries.length} feedback entries
            </div>
            
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Filters: {selectedFeature !== 'all' ? getFeatureName(selectedFeature) : 'All Features'} • 
                {selectedContentType !== 'all' ? getContentTypeName(selectedContentType) : 'All Content Types'} • 
                {ratingFilter !== 'all' ? (ratingFilter === 'positive' ? 'Positive' : 'Negative') : 'All Ratings'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>About Feedback Tracking</AlertTitle>
          <AlertDescription>
            This system tracks user feedback across different features and content types.
            Use the filters to analyze specific feedback patterns and identify areas for improvement.
          </AlertDescription>
        </Alert>
      </CardFooter>
    </Card>
  );
};

export default FeedbackTracker;
