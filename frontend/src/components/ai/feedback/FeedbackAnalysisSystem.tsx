import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, BarChart2, MessageSquare, ThumbsUp, ThumbsDown, Star, AlertTriangle, CheckCircle2, Info } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { aiSettingsMonitoringService, UserSatisfactionMetrics } from '@/services/monitoring/aiSettingsMonitoring';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useAI } from '@/providers/AIProvider';

interface FeedbackAnalysisSystemProps {
  className?: string;
}

interface FeedbackPattern {
  id: string;
  pattern: string;
  frequency: number;
  impact: number;
  examples: string[];
  affectedFeatures: string[];
}

interface ImprovementSuggestion {
  id: string;
  suggestion: string;
  priority: 'high' | 'medium' | 'low';
  relatedPatterns: string[];
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  potentialImpact: number;
}

const FeedbackAnalysisSystem: React.FC<FeedbackAnalysisSystemProps> = ({
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('24');
  const [selectedFeature, setSelectedFeature] = useState<string>('all');
  
  const [satisfactionMetrics, setSatisfactionMetrics] = useState<UserSatisfactionMetrics | null>(null);
  const [feedbackPatterns, setFeedbackPatterns] = useState<FeedbackPattern[]>([]);
  const [improvementSuggestions, setImprovementSuggestions] = useState<ImprovementSuggestion[]>([]);
  
  const { toast } = useToast();
  const { submitFeedback } = useAI();
  
  // Available features
  const features = [
    { id: 'all', name: 'All Features' },
    { id: 'ai_assistant', name: 'AI Assistant' },
    { id: 'style_creator', name: 'Style Creator' },
    { id: 'transition_suggester', name: 'Transition Suggester' },
    { id: 'collection_analyzer', name: 'Collection Analyzer' }
  ];
  
  // Load feedback data
  const loadFeedbackData = async () => {
    setIsLoading(true);
    
    try {
      // Get user satisfaction metrics
      const featureId = selectedFeature === 'all' ? undefined : selectedFeature;
      const timeRangeHours = parseInt(timeRange);
      
      const metrics = await aiSettingsMonitoringService.getUserSatisfactionMetrics(
        featureId,
        timeRangeHours
      );
      
      setSatisfactionMetrics(metrics);
      
      // Generate feedback patterns (simulated for demo)
      generateFeedbackPatterns(metrics);
      
      // Generate improvement suggestions based on patterns
      generateImprovementSuggestions();
      
      toast({
        title: 'Feedback data loaded',
        description: `Loaded feedback data for the last ${timeRange} hours.`,
      });
    } catch (error) {
      console.error('Error loading feedback data:', error);
      
      toast({
        title: 'Error loading feedback data',
        description: 'Failed to load feedback data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Generate feedback patterns based on metrics
  const generateFeedbackPatterns = (metrics: UserSatisfactionMetrics) => {
    // This would normally come from the backend, but we'll simulate it for the demo
    const patterns: FeedbackPattern[] = [
      {
        id: 'pattern_1',
        pattern: 'Unclear or confusing AI responses',
        frequency: Math.round(metrics.total_feedback * 0.15),
        impact: 75,
        examples: [
          'I didn\'t understand the suggestion',
          'The response was too technical',
          'Could you explain this in simpler terms?'
        ],
        affectedFeatures: ['ai_assistant', 'style_creator']
      },
      {
        id: 'pattern_2',
        pattern: 'Slow response times',
        frequency: Math.round(metrics.total_feedback * 0.12),
        impact: 80,
        examples: [
          'The AI took too long to respond',
          'Generation is very slow',
          'Can this be faster?'
        ],
        affectedFeatures: ['transition_suggester', 'collection_analyzer']
      },
      {
        id: 'pattern_3',
        pattern: 'Irrelevant suggestions',
        frequency: Math.round(metrics.total_feedback * 0.18),
        impact: 85,
        examples: [
          'This suggestion doesn\'t match my style',
          'These transitions don\'t work for my genre',
          'The analysis doesn\'t seem relevant to my collection'
        ],
        affectedFeatures: ['transition_suggester', 'style_creator']
      },
      {
        id: 'pattern_4',
        pattern: 'Positive feedback on personalization',
        frequency: Math.round(metrics.total_feedback * 0.25),
        impact: 90,
        examples: [
          'Great suggestions that match my style!',
          'The AI seems to understand my preferences',
          'These recommendations are spot on'
        ],
        affectedFeatures: ['ai_assistant', 'style_creator', 'transition_suggester']
      }
    ];
    
    setFeedbackPatterns(patterns);
  };
  
  // Generate improvement suggestions based on patterns
  const generateImprovementSuggestions = () => {
    // This would normally come from the backend, but we'll simulate it for the demo
    const suggestions: ImprovementSuggestion[] = [
      {
        id: 'suggestion_1',
        suggestion: 'Simplify AI responses for beginner users',
        priority: 'high',
        relatedPatterns: ['pattern_1'],
        implementationDifficulty: 'medium',
        potentialImpact: 85
      },
      {
        id: 'suggestion_2',
        suggestion: 'Optimize response time for transition suggestions',
        priority: 'medium',
        relatedPatterns: ['pattern_2'],
        implementationDifficulty: 'hard',
        potentialImpact: 75
      },
      {
        id: 'suggestion_3',
        suggestion: 'Improve genre-specific transition recommendations',
        priority: 'high',
        relatedPatterns: ['pattern_3'],
        implementationDifficulty: 'medium',
        potentialImpact: 90
      },
      {
        id: 'suggestion_4',
        suggestion: 'Enhance personalization by collecting more user preferences',
        priority: 'medium',
        relatedPatterns: ['pattern_4'],
        implementationDifficulty: 'easy',
        potentialImpact: 80
      }
    ];
    
    setImprovementSuggestions(suggestions);
  };
  
  // Load data on mount and when filters change
  useEffect(() => {
    loadFeedbackData();
  }, [selectedFeature, timeRange]);
  
  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };
  
  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };
  
  // Render rating distribution
  const renderRatingDistribution = () => {
    if (!satisfactionMetrics || !satisfactionMetrics.rating_distribution) {
      return (
        <div className="text-center py-6 text-muted-foreground">
          No rating data available
        </div>
      );
    }
    
    const distribution = satisfactionMetrics.rating_distribution;
    const total = Object.values(distribution).reduce((sum, count) => sum + count, 0);
    
    return (
      <div className="space-y-4">
        {[5, 4, 3, 2, 1].map(rating => {
          const count = distribution[rating.toString()] || 0;
          const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
          
          return (
            <div key={rating} className="space-y-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {Array.from({ length: rating }).map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                  ))}
                  <span className="ml-2 text-sm">{rating} stars</span>
                </div>
                <span className="text-sm font-medium">{count} ({percentage}%)</span>
              </div>
              <Progress value={percentage} className="h-2" />
            </div>
          );
        })}
      </div>
    );
  };
  
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle>Feedback Analysis System</CardTitle>
        <CardDescription>
          Analyze user feedback patterns and generate improvement suggestions
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Feature</label>
              <Select
                value={selectedFeature}
                onValueChange={setSelectedFeature}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select feature" />
                </SelectTrigger>
                <SelectContent>
                  {features.map(feature => (
                    <SelectItem key={feature.id} value={feature.id}>
                      {feature.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">Time Range</label>
              <Select
                value={timeRange}
                onValueChange={setTimeRange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24">Last 24 Hours</SelectItem>
                  <SelectItem value="72">Last 3 Days</SelectItem>
                  <SelectItem value="168">Last 7 Days</SelectItem>
                  <SelectItem value="720">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <Button
            variant="outline"
            onClick={loadFeedbackData}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="patterns">Feedback Patterns</TabsTrigger>
            <TabsTrigger value="suggestions">Improvement Suggestions</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6">
            {satisfactionMetrics ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Total Feedback</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold">{satisfactionMetrics.total_feedback}</div>
                      <p className="text-sm text-muted-foreground">
                        Feedback submissions in selected time period
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Average Rating</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <div className="text-3xl font-bold mr-2">
                          {satisfactionMetrics.avg_rating.toFixed(1)}
                        </div>
                        <div className="flex">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star 
                              key={i} 
                              className={`h-5 w-5 ${i < Math.round(satisfactionMetrics.avg_rating) ? 'fill-yellow-500 text-yellow-500' : 'text-muted-foreground'}`} 
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Average star rating (1-5)
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Sentiment</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <ThumbsUp className="h-5 w-5 text-green-500 mr-1" />
                          <span className="text-lg font-medium">
                            {Math.round((satisfactionMetrics.avg_rating / 5) * 100)}%
                          </span>
                        </div>
                        <div className="flex items-center">
                          <ThumbsDown className="h-5 w-5 text-red-500 mr-1" />
                          <span className="text-lg font-medium">
                            {100 - Math.round((satisfactionMetrics.avg_rating / 5) * 100)}%
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Positive vs. negative sentiment
                      </p>
                    </CardContent>
                  </Card>
                </div>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Rating Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderRatingDistribution()}
                  </CardContent>
                </Card>
                
                {satisfactionMetrics.feedback_per_feature && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Feedback by Feature</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {Object.entries(satisfactionMetrics.feedback_per_feature).map(([feature, count]) => {
                          const featureName = features.find(f => f.id === feature)?.name || feature;
                          const percentage = Math.round((count / satisfactionMetrics.total_feedback) * 100);
                          
                          return (
                            <div key={feature} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <span className="text-sm">{featureName}</span>
                                <span className="text-sm font-medium">{count} ({percentage}%)</span>
                              </div>
                              <Progress value={percentage} className="h-2" />
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                {isLoading ? (
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                ) : (
                  <BarChart2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                )}
                <h3 className="text-lg font-medium mb-2">
                  {isLoading ? 'Loading feedback data...' : 'No feedback data available'}
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  {isLoading 
                    ? 'Please wait while we load the feedback data.'
                    : 'There is no feedback data available for the selected filters.'}
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="patterns" className="space-y-6">
            {feedbackPatterns.length > 0 ? (
              <div className="space-y-4">
                {feedbackPatterns.map(pattern => (
                  <Card key={pattern.id}>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{pattern.pattern}</CardTitle>
                        <Badge>Impact: {pattern.impact}%</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-muted-foreground">Frequency</div>
                          <div className="font-medium">{pattern.frequency} occurrences</div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Affected Features</div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {pattern.affectedFeatures.map(featureId => {
                              const feature = features.find(f => f.id === featureId);
                              return (
                                <Badge key={featureId} variant="outline">
                                  {feature?.name || featureId}
                                </Badge>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm text-muted-foreground mb-2">Example Feedback</div>
                        <div className="space-y-2">
                          {pattern.examples.map((example, index) => (
                            <div key={index} className="bg-muted p-2 rounded-md text-sm">
                              <MessageSquare className="h-4 w-4 inline-block mr-2 text-muted-foreground" />
                              {example}
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                {isLoading ? (
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                ) : (
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                )}
                <h3 className="text-lg font-medium mb-2">
                  {isLoading ? 'Analyzing feedback patterns...' : 'No feedback patterns found'}
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  {isLoading 
                    ? 'Please wait while we analyze the feedback patterns.'
                    : 'There are no feedback patterns available for the selected filters.'}
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="suggestions" className="space-y-6">
            {improvementSuggestions.length > 0 ? (
              <div className="space-y-4">
                {improvementSuggestions.map(suggestion => (
                  <Card key={suggestion.id}>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{suggestion.suggestion}</CardTitle>
                        <div className="flex items-center space-x-2">
                          <Badge className={getPriorityColor(suggestion.priority)}>
                            {suggestion.priority.charAt(0).toUpperCase() + suggestion.priority.slice(1)} Priority
                          </Badge>
                          <Badge className={getDifficultyColor(suggestion.implementationDifficulty)}>
                            {suggestion.implementationDifficulty.charAt(0).toUpperCase() + suggestion.implementationDifficulty.slice(1)} Implementation
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-muted-foreground">Potential Impact</div>
                          <div className="font-medium">{suggestion.potentialImpact}%</div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Related Patterns</div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {suggestion.relatedPatterns.map(patternId => {
                              const pattern = feedbackPatterns.find(p => p.id === patternId);
                              return (
                                <Badge key={patternId} variant="outline">
                                  {pattern?.pattern || patternId}
                                </Badge>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                {isLoading ? (
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                ) : (
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                )}
                <h3 className="text-lg font-medium mb-2">
                  {isLoading ? 'Generating improvement suggestions...' : 'No improvement suggestions found'}
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  {isLoading 
                    ? 'Please wait while we generate improvement suggestions.'
                    : 'There are no improvement suggestions available for the selected filters.'}
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>About Feedback Analysis</AlertTitle>
          <AlertDescription>
            This system analyzes user feedback to identify patterns and generate improvement suggestions.
            The analysis is based on user ratings, comments, and usage patterns.
          </AlertDescription>
        </Alert>
      </CardFooter>
    </Card>
  );
};

export default FeedbackAnalysisSystem;
