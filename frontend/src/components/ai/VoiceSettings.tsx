import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Volume2, Mic, Play, Square, RefreshCw } from 'lucide-react';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface VoiceSettingsProps {
  className?: string;
}

const VoiceSettings: React.FC<VoiceSettingsProps> = ({ className = '' }) => {
  const { toast } = useToast();
  
  // Voice settings from local storage
  const [voiceSettings, setVoiceSettings] = useLocalStorage('voice-settings', {
    volume: 80,
    speechRate: 1,
    preferredVoiceName: '',
    autoSpeak: false,
    enabled: true,
  });
  
  // Local state
  const [volume, setVolume] = useState(voiceSettings.volume);
  const [speechRate, setSpeechRate] = useState(voiceSettings.speechRate);
  const [selectedVoice, setSelectedVoice] = useState(voiceSettings.preferredVoiceName);
  const [autoSpeak, setAutoSpeak] = useState(voiceSettings.autoSpeak);
  const [enabled, setEnabled] = useState(voiceSettings.enabled);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(false);
  
  // Check for speech synthesis support
  useEffect(() => {
    if ('speechSynthesis' in window) {
      setSpeechSupported(true);
      
      // Load available voices
      const loadVoices = () => {
        const voices = window.speechSynthesis.getVoices();
        if (voices.length > 0) {
          setAvailableVoices(voices);
        }
      };
      
      // Load voices immediately if available
      loadVoices();
      
      // Chrome loads voices asynchronously, so we need to listen for the voiceschanged event
      window.speechSynthesis.onvoiceschanged = loadVoices;
      
      return () => {
        // Cleanup
        window.speechSynthesis.onvoiceschanged = null;
      };
    } else {
      setSpeechSupported(false);
    }
  }, []);
  
  // Save settings when they change
  useEffect(() => {
    setVoiceSettings({
      volume,
      speechRate,
      preferredVoiceName: selectedVoice,
      autoSpeak,
      enabled,
    });
  }, [volume, speechRate, selectedVoice, autoSpeak, enabled, setVoiceSettings]);
  
  // Test voice
  const handleTestVoice = () => {
    if (!speechSupported) return;
    
    // Cancel any ongoing speech
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance("This is a test of the selected voice and settings.");
    utterance.volume = volume / 100;
    utterance.rate = speechRate;
    utterance.lang = 'en-US';
    
    // Try to find the selected voice
    if (selectedVoice) {
      const voice = availableVoices.find(v => v.name === selectedVoice);
      if (voice) {
        utterance.voice = voice;
      }
    }
    
    utterance.onstart = () => {
      setIsSpeaking(true);
    };
    
    utterance.onend = () => {
      setIsSpeaking(false);
    };
    
    utterance.onerror = (event) => {
      console.error('Speech synthesis error', event);
      setIsSpeaking(false);
      
      toast({
        title: 'Speech Synthesis Error',
        description: 'Could not speak the text.',
        variant: 'destructive',
      });
    };
    
    window.speechSynthesis.speak(utterance);
  };
  
  // Stop speaking
  const handleStopSpeaking = () => {
    if (!speechSupported) return;
    
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
  };
  
  // Reset to defaults
  const handleResetDefaults = () => {
    setVolume(80);
    setSpeechRate(1);
    setSelectedVoice('');
    setAutoSpeak(false);
    setEnabled(true);
    
    toast({
      title: 'Settings Reset',
      description: 'Voice settings have been reset to defaults.',
    });
  };
  
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Volume2 className="h-5 w-5 mr-2" />
          Voice Settings
        </CardTitle>
        <CardDescription>
          Configure text-to-speech and speech recognition settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!speechSupported && (
          <Alert variant="destructive">
            <AlertTitle>Speech Synthesis Not Supported</AlertTitle>
            <AlertDescription>
              Your browser does not support speech synthesis. Voice features will not be available.
            </AlertDescription>
          </Alert>
        )}
        
        {/* Enable/Disable Voice Features */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base">Enable Voice Features</Label>
            <p className="text-sm text-muted-foreground">
              Enable or disable all voice interaction features
            </p>
          </div>
          <Switch 
            checked={enabled}
            onCheckedChange={setEnabled}
            disabled={!speechSupported}
          />
        </div>
        
        {/* Auto-speak setting */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base">Auto-speak Responses</Label>
            <p className="text-sm text-muted-foreground">
              Automatically speak AI responses when received
            </p>
          </div>
          <Switch 
            checked={autoSpeak}
            onCheckedChange={setAutoSpeak}
            disabled={!speechSupported || !enabled}
          />
        </div>
        
        {/* Volume control */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="voice-volume">Volume</Label>
            <span className="text-sm text-muted-foreground">{volume}%</span>
          </div>
          <Slider
            id="voice-volume"
            min={0}
            max={100}
            step={5}
            value={[volume]}
            onValueChange={(value) => setVolume(value[0])}
            disabled={!speechSupported || !enabled}
          />
        </div>
        
        {/* Speech rate control */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="speech-rate">Speech Rate</Label>
            <span className="text-sm text-muted-foreground">{speechRate.toFixed(1)}x</span>
          </div>
          <Slider
            id="speech-rate"
            min={0.5}
            max={2}
            step={0.1}
            value={[speechRate]}
            onValueChange={(value) => setSpeechRate(value[0])}
            disabled={!speechSupported || !enabled}
          />
        </div>
        
        {/* Voice selection */}
        <div className="space-y-3">
          <Label htmlFor="voice-select">Voice</Label>
          <select
            id="voice-select"
            value={selectedVoice}
            onChange={(e) => setSelectedVoice(e.target.value)}
            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            disabled={!speechSupported || !enabled}
          >
            <option value="">Default Voice</option>
            {availableVoices.map((voice) => (
              <option key={voice.name} value={voice.name}>
                {voice.name} ({voice.lang})
              </option>
            ))}
          </select>
        </div>
        
        {/* Test voice button */}
        <div className="flex gap-3">
          <Button 
            onClick={isSpeaking ? handleStopSpeaking : handleTestVoice}
            disabled={!speechSupported || !enabled}
            className="flex-1"
          >
            {isSpeaking ? (
              <>
                <Square className="h-4 w-4 mr-2" />
                Stop Test
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Test Voice
              </>
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleResetDefaults}
            className="flex-1"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset Defaults
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default VoiceSettings;
