import React, { useState, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import { useMCP } from '@/providers/MCPProvider';
import { useAI } from '@/providers/AIProvider';
import { useMultiModalAI } from '@/providers/MultiModalAIProvider';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import {
  Music,
  Upload,
  Link,
  Youtube,
  AudioWaveform,
  FileMusic,
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Loader2,
  RefreshCw,
  Download,
  ChevronRight,
  ChevronDown,
  Info,
  AlertCircle,
  CheckCircle,
  XCircle,
  BarChart,
  PieChart,
  LineChart,
  Sparkles,
} from 'lucide-react';

interface MCPAudioAnalysisProps {
  onAnalysisComplete?: (analysis: any) => void;
  onClose?: () => void;
  className?: string;
}

interface AnalysisOptions {
  extractBPM: boolean;
  extractKey: boolean;
  extractBeats: boolean;
  extractSegments: boolean;
  extractChroma: boolean;
  extractMFCC: boolean;
  enhancedAnalysis: boolean;
}

const MCPAudioAnalysisPanel: React.FC<MCPAudioAnalysisProps> = ({
  onAnalysisComplete,
  onClose,
  className = '',
}) => {
  const { toast } = useToast();
  const { callTool, isConnected } = useMCP();
  const { answerQuestion } = useAI();
  const { analyzeAudioFile } = useMultiModalAI();
  const { addRecentAction } = useApplicationContext();

  // State for file upload
  const [file, setFile] = useState<File | null>(null);
  const [fileUrl, setFileUrl] = useState<string>('');
  const [youtubeUrl, setYoutubeUrl] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFilePath, setUploadedFilePath] = useState<string>('');

  // State for analysis
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // State for analysis options
  const [analysisOptions, setAnalysisOptions] = useState<AnalysisOptions>({
    extractBPM: true,
    extractKey: true,
    extractBeats: true,
    extractSegments: true,
    extractChroma: false,
    extractMFCC: false,
    enhancedAnalysis: true,
  });

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setFileUrl('');
      setYoutubeUrl('');
      setAnalysisResult(null);
      setAnalysisError(null);
      setUploadedFilePath('');
    }
  };

  // Handle file URL input
  const handleFileUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFileUrl(e.target.value);
    setFile(null);
    setYoutubeUrl('');
    setAnalysisResult(null);
    setAnalysisError(null);
    setUploadedFilePath('');
  };

  // Handle YouTube URL input
  const handleYoutubeUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setYoutubeUrl(e.target.value);
    setFile(null);
    setFileUrl('');
    setAnalysisResult(null);
    setAnalysisError(null);
    setUploadedFilePath('');
  };

  // Handle option change
  const handleOptionChange = (option: keyof AnalysisOptions, value: boolean) => {
    setAnalysisOptions((prev) => ({
      ...prev,
      [option]: value,
    }));
  };

  // Handle file upload
  const handleUpload = async () => {
    try {
      setIsUploading(true);
      setUploadProgress(0);
      setAnalysisError(null);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return prev + 5;
        });
      }, 100);

      let filePath = '';

      if (file) {
        // Upload file using FormData
        const formData = new FormData();
        formData.append('file', file);

        // Use the existing analyzeAudioFile function from MultiModalAI provider
        const result = await analyzeAudioFile(file);
        filePath = result.filePath || '';
      } else if (fileUrl) {
        // Download from URL using MCP tool
        if (!isConnected) {
          throw new Error('MCP is not connected. Please connect to MCP first.');
        }

        filePath = await callTool('download_from_url', { url: fileUrl });
      } else if (youtubeUrl) {
        // Download from YouTube using MCP tool
        if (!isConnected) {
          throw new Error('MCP is not connected. Please connect to MCP first.');
        }

        filePath = await callTool('download_from_youtube', { youtube_url: youtubeUrl });
      } else {
        throw new Error('Please select a file, enter a URL, or enter a YouTube URL.');
      }

      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadedFilePath(filePath);

      // Log the action
      addRecentAction({
        type: 'audio_file_uploaded',
        description: `Uploaded audio file: ${file?.name || fileUrl || youtubeUrl}`,
      });

      // Automatically start analysis after upload
      setTimeout(() => {
        setIsUploading(false);
        handleAnalyze(filePath);
      }, 500);
    } catch (error: any) {
      setIsUploading(false);
      setUploadProgress(0);
      setAnalysisError(error.message || 'Failed to upload file');
      toast({
        title: 'Upload Error',
        description: error.message || 'Failed to upload file',
        variant: 'destructive',
      });
    }
  };

  // Handle analysis
  const handleAnalyze = async (filePath: string) => {
    try {
      setIsAnalyzing(true);
      setAnalysisProgress(0);
      setAnalysisError(null);
      setAnalysisResult(null);

      // Simulate analysis progress
      const progressInterval = setInterval(() => {
        setAnalysisProgress((prev) => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return prev + 5;
        });
      }, 200);

      if (!isConnected) {
        throw new Error('MCP is not connected. Please connect to MCP first.');
      }

      // Call the appropriate MCP tools based on selected options
      const results: any = {};

      if (analysisOptions.extractBPM || analysisOptions.extractKey || analysisOptions.enhancedAnalysis) {
        // Use analyze_track for comprehensive analysis
        const trackAnalysis = await callTool('analyze_track', { file_path: filePath });
        results.trackAnalysis = trackAnalysis;
      }

      if (analysisOptions.extractBeats) {
        // Extract beat grid
        const beatGrid = await callTool('extract_beat_grid', {
          file_path: filePath,
          enhanced: analysisOptions.enhancedAnalysis
        });
        results.beatGrid = beatGrid;
      }

      if (analysisOptions.extractSegments) {
        // Detect segments
        const segments = await callTool('detect_segments', { file_path: filePath });
        results.segments = segments;
      }

      if (analysisOptions.extractChroma) {
        // Extract chroma features
        const chromaCsv = await callTool('extract_chroma', { file_path: filePath });
        results.chromaCsv = chromaCsv;
      }

      if (analysisOptions.extractMFCC) {
        // Extract MFCC features
        const mfccCsv = await callTool('extract_mfcc', { file_path: filePath });
        results.mfccCsv = mfccCsv;
      }

      clearInterval(progressInterval);
      setAnalysisProgress(100);
      setAnalysisResult(results);

      // Call the onAnalysisComplete callback if provided
      if (onAnalysisComplete) {
        onAnalysisComplete(results);
      }

      // Log the action
      addRecentAction({
        type: 'audio_analysis_completed',
        description: `Analyzed audio file: ${file?.name || fileUrl || youtubeUrl}`,
      });

      // Get AI interpretation of the analysis
      if (analysisOptions.enhancedAnalysis) {
        const prompt = `
          I've analyzed an audio track with the following results:
          ${JSON.stringify(results, null, 2)}

          Please provide a brief interpretation of these results, focusing on:
          1. The musical characteristics (BPM, key, energy)
          2. The track structure and how it might be used in a DJ mix
          3. Any notable features that stand out

          Keep your response concise and focused on the most important insights.
        `;

        answerQuestion(prompt, 'Analyzing audio results...');
      }
    } catch (error: any) {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
      setAnalysisError(error.message || 'Failed to analyze file');
      toast({
        title: 'Analysis Error',
        description: error.message || 'Failed to analyze file',
        variant: 'destructive',
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Render analysis results
  const renderAnalysisResults = () => {
    if (!analysisResult) return null;

    return (
      <Card className="mt-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <BarChart className="h-5 w-5 mr-2" />
            Analysis Results
          </CardTitle>
          <CardDescription>
            {file?.name || fileUrl || youtubeUrl}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview">
            <TabsList className="grid grid-cols-4 h-8">
              <TabsTrigger value="overview" className="text-xs">Overview</TabsTrigger>
              <TabsTrigger value="beats" className="text-xs">Beats</TabsTrigger>
              <TabsTrigger value="segments" className="text-xs">Segments</TabsTrigger>
              <TabsTrigger value="features" className="text-xs">Features</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="pt-4">
              {analysisResult.trackAnalysis && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>BPM</Label>
                      <div className="flex items-center">
                        <Badge className="text-lg font-bold px-3 py-1">
                          {analysisResult.trackAnalysis.tempo?.toFixed(1) || 'N/A'}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Key</Label>
                      <div className="flex items-center">
                        <Badge className="text-lg font-bold px-3 py-1">
                          {analysisResult.trackAnalysis.key || 'N/A'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Energy</Label>
                    <Progress value={analysisResult.trackAnalysis.energy * 100 || 0} />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Low</span>
                      <span>Medium</span>
                      <span>High</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Duration</Label>
                    <div className="text-sm">
                      {formatTime(analysisResult.trackAnalysis.duration || 0)}
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="beats" className="pt-4">
              {analysisResult.beatGrid && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Beat Confidence</Label>
                    <Progress value={analysisResult.beatGrid.confidence * 100 || 0} />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Low</span>
                      <span>Medium</span>
                      <span>High</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Beat Times</Label>
                    <ScrollArea className="h-[200px] border rounded-md p-2">
                      <div className="space-y-1">
                        {analysisResult.beatGrid.beat_times?.slice(0, 50).map((time: number, index: number) => (
                          <div key={index} className="text-xs">
                            Beat {index + 1}: {time.toFixed(3)}s
                          </div>
                        ))}
                        {(analysisResult.beatGrid.beat_times?.length || 0) > 50 && (
                          <div className="text-xs text-muted-foreground pt-2">
                            + {(analysisResult.beatGrid.beat_times?.length || 0) - 50} more beats
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="segments" className="pt-4">
              {analysisResult.segments && (
                <div className="space-y-4">
                  <ScrollArea className="h-[250px] border rounded-md p-2">
                    <div className="space-y-2">
                      {analysisResult.segments.map((segment: any, index: number) => (
                        <div key={index} className="border rounded-md p-2">
                          <div className="flex justify-between items-center">
                            <Badge variant="outline">{segment.type}</Badge>
                            <span className="text-xs text-muted-foreground">
                              {formatTime(segment.start_time)} - {formatTime(segment.end_time)}
                            </span>
                          </div>
                          <div className="text-xs mt-1">
                            Confidence: {(segment.confidence * 100).toFixed(0)}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </TabsContent>

            <TabsContent value="features" className="pt-4">
              <div className="space-y-4">
                {analysisResult.chromaCsv && (
                  <div className="space-y-2">
                    <Label>Chroma Features</Label>
                    <div className="flex items-center">
                      <Badge variant="outline" className="text-xs">
                        CSV file available
                      </Badge>
                      <Button variant="ghost" size="sm" className="ml-2 h-7">
                        <Download className="h-3.5 w-3.5 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                )}

                {analysisResult.mfccCsv && (
                  <div className="space-y-2">
                    <Label>MFCC Features</Label>
                    <div className="flex items-center">
                      <Badge variant="outline" className="text-xs">
                        CSV file available
                      </Badge>
                      <Button variant="ghost" size="sm" className="ml-2 h-7">
                        <Download className="h-3.5 w-3.5 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  };

  // Helper function to format time
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <AudioWaveform className="h-5 w-5 mr-2" />
          Audio Analysis
        </CardTitle>
        <CardDescription>
          Analyze audio files using MCP and librosa
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="file">
          <TabsList className="grid grid-cols-3 h-9 mb-4">
            <TabsTrigger value="file" className="text-xs">
              <FileMusic className="h-3.5 w-3.5 mr-1.5" />
              File Upload
            </TabsTrigger>
            <TabsTrigger value="url" className="text-xs">
              <Link className="h-3.5 w-3.5 mr-1.5" />
              URL
            </TabsTrigger>
            <TabsTrigger value="youtube" className="text-xs">
              <Youtube className="h-3.5 w-3.5 mr-1.5" />
              YouTube
            </TabsTrigger>
          </TabsList>

          <TabsContent value="file">
            <div className="space-y-4">
              <div className="flex items-center justify-center w-full">
                <Label
                  htmlFor="audio-file"
                  className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/30 hover:bg-muted/50"
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                    <p className="mb-1 text-sm text-muted-foreground">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground">
                      MP3, WAV, or FLAC (max. 10MB)
                    </p>
                  </div>
                  <Input
                    id="audio-file"
                    type="file"
                    accept=".mp3,.wav,.flac"
                    className="hidden"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                  />
                </Label>
              </div>

              {file && (
                <div className="flex items-center justify-between p-2 border rounded-md bg-muted/30">
                  <div className="flex items-center">
                    <Music className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFile(null)}
                    className="h-7 w-7 p-0"
                  >
                    <XCircle className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="url">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="audio-url">Audio File URL</Label>
                <Input
                  id="audio-url"
                  placeholder="https://example.com/audio.mp3"
                  value={fileUrl}
                  onChange={handleFileUrlChange}
                />
                <p className="text-xs text-muted-foreground">
                  Enter a direct URL to an MP3, WAV, or FLAC file
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="youtube">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="youtube-url">YouTube URL</Label>
                <Input
                  id="youtube-url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  value={youtubeUrl}
                  onChange={handleYoutubeUrlChange}
                />
                <p className="text-xs text-muted-foreground">
                  Enter a YouTube video URL to extract audio
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <Separator className="my-4" />

        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">Analysis Options</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="extract-bpm"
                  checked={analysisOptions.extractBPM}
                  onCheckedChange={(checked) =>
                    handleOptionChange('extractBPM', checked === true)
                  }
                />
                <Label htmlFor="extract-bpm" className="text-sm">BPM</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="extract-key"
                  checked={analysisOptions.extractKey}
                  onCheckedChange={(checked) =>
                    handleOptionChange('extractKey', checked === true)
                  }
                />
                <Label htmlFor="extract-key" className="text-sm">Key</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="extract-beats"
                  checked={analysisOptions.extractBeats}
                  onCheckedChange={(checked) =>
                    handleOptionChange('extractBeats', checked === true)
                  }
                />
                <Label htmlFor="extract-beats" className="text-sm">Beat Grid</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="extract-segments"
                  checked={analysisOptions.extractSegments}
                  onCheckedChange={(checked) =>
                    handleOptionChange('extractSegments', checked === true)
                  }
                />
                <Label htmlFor="extract-segments" className="text-sm">Segments</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="extract-chroma"
                  checked={analysisOptions.extractChroma}
                  onCheckedChange={(checked) =>
                    handleOptionChange('extractChroma', checked === true)
                  }
                />
                <Label htmlFor="extract-chroma" className="text-sm">Chroma</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="extract-mfcc"
                  checked={analysisOptions.extractMFCC}
                  onCheckedChange={(checked) =>
                    handleOptionChange('extractMFCC', checked === true)
                  }
                />
                <Label htmlFor="extract-mfcc" className="text-sm">MFCC</Label>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="enhanced-analysis"
              checked={analysisOptions.enhancedAnalysis}
              onCheckedChange={(checked) =>
                handleOptionChange('enhancedAnalysis', checked)
              }
            />
            <Label htmlFor="enhanced-analysis" className="text-sm">Enhanced Analysis with AI Interpretation</Label>
          </div>
        </div>

        {(isUploading || uploadProgress > 0) && (
          <div className="mt-4 space-y-2">
            <div className="flex justify-between items-center">
              <Label className="text-sm">Uploading</Label>
              <span className="text-xs text-muted-foreground">{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} />
          </div>
        )}

        {(isAnalyzing || analysisProgress > 0) && (
          <div className="mt-4 space-y-2">
            <div className="flex justify-between items-center">
              <Label className="text-sm">Analyzing</Label>
              <span className="text-xs text-muted-foreground">{analysisProgress}%</span>
            </div>
            <Progress value={analysisProgress} />
          </div>
        )}

        {analysisError && (
          <div className="mt-4 p-3 border border-red-200 bg-red-50 rounded-md flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-700">{analysisError}</p>
          </div>
        )}

        {renderAnalysisResults()}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>

        <Button
          onClick={handleUpload}
          disabled={
            (!file && !fileUrl && !youtubeUrl) ||
            isUploading ||
            isAnalyzing ||
            !isConnected
          }
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : isAnalyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analyzing...
            </>
          ) : uploadedFilePath ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Re-analyze
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              Analyze
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default MCPAudioAnalysisPanel;
