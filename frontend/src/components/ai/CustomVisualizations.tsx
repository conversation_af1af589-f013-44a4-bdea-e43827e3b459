import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

// Types for visualizations
interface TrackData {
  id: string;
  title: string;
  artist: string;
  bpm: number;
  key: string;
  energy: number;
  genre?: string;
}

interface TransitionVisualizationProps {
  fromTrack: TrackData;
  toTrack: TrackData;
  transitionLengthBeats?: number;
  transitionLengthSeconds?: number;
  className?: string;
}

interface KeyCompatibilityVisualizationProps {
  fromKey: string;
  toKey: string;
  className?: string;
}

interface EnergyFlowVisualizationProps {
  tracks: TrackData[];
  className?: string;
}

interface WaveformDisplayProps {
  trackId: string;
  waveformData?: number[];
  markers?: {
    position: number;
    type: 'cue' | 'loop' | 'beatgrid';
    label?: string;
  }[];
  className?: string;
}

// Helper function to calculate key compatibility (0-100%)
const calculateKeyCompatibility = (fromKey: string, toKey: string): number => {
  // Camelot wheel key mapping
  const camelotWheel: Record<string, number> = {
    '1A': 1, '2A': 2, '3A': 3, '4A': 4, '5A': 5, '6A': 6, '7A': 7, '8A': 8, '9A': 9, '10A': 10, '11A': 11, '12A': 12,
    '1B': 13, '2B': 14, '3B': 15, '4B': 16, '5B': 17, '6B': 18, '7B': 19, '8B': 20, '9B': 21, '10B': 22, '11B': 23, '12B': 24
  };
  
  // If keys not found in mapping, return 0
  if (!camelotWheel[fromKey] || !camelotWheel[toKey]) return 0;
  
  const fromNum = camelotWheel[fromKey];
  const toNum = camelotWheel[toKey];
  
  // Same key = 100% compatible
  if (fromNum === toNum) return 100;
  
  // Check if keys are adjacent on the wheel (perfect mix)
  const fromLetter = fromKey.slice(-1);
  const toLetter = toKey.slice(-1);
  
  if (fromLetter === toLetter) {
    // Same letter (A or B)
    const fromNumber = parseInt(fromKey);
    const toNumber = parseInt(toKey);
    
    // Adjacent numbers or 12 to 1 transition
    if (Math.abs(fromNumber - toNumber) === 1 || Math.abs(fromNumber - toNumber) === 11) {
      return 90;
    }
    
    // Two steps away
    if (Math.abs(fromNumber - toNumber) === 2 || Math.abs(fromNumber - toNumber) === 10) {
      return 70;
    }
  } else {
    // Different letter (A to B or B to A)
    const fromNumber = parseInt(fromKey);
    const toNumber = parseInt(toKey);
    
    // Relative minor/major (same number)
    if (fromNumber === toNumber) {
      return 80;
    }
    
    // Adjacent relative
    if (Math.abs(fromNumber - toNumber) === 1 || Math.abs(fromNumber - toNumber) === 11) {
      return 60;
    }
  }
  
  // Other combinations
  return 30;
};

// Transition Visualization Component
const TransitionVisualization: React.FC<TransitionVisualizationProps> = ({
  fromTrack,
  toTrack,
  transitionLengthBeats = 32,
  transitionLengthSeconds,
  className = ''
}) => {
  // Calculate key compatibility
  const keyCompatibility = calculateKeyCompatibility(fromTrack.key, toTrack.key);
  
  // Calculate BPM difference
  const bpmDifference = Math.abs(fromTrack.bpm - toTrack.bpm);
  const bpmCompatibility = bpmDifference <= 5 ? 'High' : bpmDifference <= 10 ? 'Medium' : 'Low';
  
  // Calculate energy flow
  const energyDifference = Math.abs(fromTrack.energy - toTrack.energy);
  const energyFlow = energyDifference <= 0.2 ? 'Smooth' : energyDifference <= 0.4 ? 'Moderate' : 'Dramatic';
  
  // Calculate transition time in seconds if not provided
  const transitionSeconds = transitionLengthSeconds || (transitionLengthBeats / fromTrack.bpm) * 60;
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Transition Analysis</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <div className="flex justify-between items-center mb-4">
          <div className="text-center">
            <p className="text-xs text-muted-foreground">From</p>
            <p className="text-sm font-medium">{fromTrack.title}</p>
            <p className="text-xs text-muted-foreground">{fromTrack.artist}</p>
          </div>
          <div className="h-0.5 flex-1 bg-muted mx-2 relative">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground whitespace-nowrap">
              {transitionSeconds.toFixed(1)}s
            </div>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">To</p>
            <p className="text-sm font-medium">{toTrack.title}</p>
            <p className="text-xs text-muted-foreground">{toTrack.artist}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-2 mb-2">
          <div className="border rounded-md p-2 text-center">
            <p className="text-xs text-muted-foreground">Key Compatibility</p>
            <div className="mt-1 flex justify-center">
              <div 
                className="h-2 w-full max-w-[80px] bg-muted rounded-full overflow-hidden"
                title={`${keyCompatibility}%`}
              >
                <div 
                  className="h-full bg-primary" 
                  style={{ width: `${keyCompatibility}%` }}
                />
              </div>
            </div>
            <p className="text-xs mt-1">
              {fromTrack.key} → {toTrack.key}
            </p>
          </div>
          
          <div className="border rounded-md p-2 text-center">
            <p className="text-xs text-muted-foreground">BPM Compatibility</p>
            <p className="text-sm font-medium mt-1">{bpmCompatibility}</p>
            <p className="text-xs">
              {fromTrack.bpm} → {toTrack.bpm}
            </p>
          </div>
          
          <div className="border rounded-md p-2 text-center">
            <p className="text-xs text-muted-foreground">Energy Flow</p>
            <p className="text-sm font-medium mt-1">{energyFlow}</p>
            <p className="text-xs">
              {(fromTrack.energy * 10).toFixed(1)} → {(toTrack.energy * 10).toFixed(1)}
            </p>
          </div>
        </div>
        
        <div className="text-xs text-muted-foreground mt-2">
          <p>Recommended transition: {transitionLengthBeats} beats ({transitionSeconds.toFixed(1)} seconds)</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Key Compatibility Visualization Component
const KeyCompatibilityVisualization: React.FC<KeyCompatibilityVisualizationProps> = ({
  fromKey,
  toKey,
  className = ''
}) => {
  // Calculate key compatibility
  const compatibility = calculateKeyCompatibility(fromKey, toKey);
  
  // Determine compatibility level
  let compatibilityLevel = '';
  let compatibilityColor = '';
  
  if (compatibility >= 90) {
    compatibilityLevel = 'Perfect Match';
    compatibilityColor = 'bg-green-500';
  } else if (compatibility >= 70) {
    compatibilityLevel = 'Good Match';
    compatibilityColor = 'bg-green-300';
  } else if (compatibility >= 50) {
    compatibilityLevel = 'Acceptable';
    compatibilityColor = 'bg-yellow-400';
  } else if (compatibility >= 30) {
    compatibilityLevel = 'Challenging';
    compatibilityColor = 'bg-orange-400';
  } else {
    compatibilityLevel = 'Dissonant';
    compatibilityColor = 'bg-red-500';
  }
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Key Compatibility</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <div className="flex justify-between items-center mb-4">
          <Badge variant="outline" className="text-lg px-3 py-1">{fromKey}</Badge>
          <div className="h-0.5 flex-1 bg-muted mx-2 relative">
            <div 
              className={`absolute top-0 left-0 h-0.5 ${compatibilityColor}`} 
              style={{ width: `${compatibility}%` }}
            />
          </div>
          <Badge variant="outline" className="text-lg px-3 py-1">{toKey}</Badge>
        </div>
        
        <div className="text-center">
          <Badge className={`${compatibilityColor.replace('bg-', 'bg-opacity-20 text-')} border-none`}>
            {compatibilityLevel}
          </Badge>
          <p className="text-sm mt-2">{compatibility}% Compatible</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Energy Flow Visualization Component
const EnergyFlowVisualization: React.FC<EnergyFlowVisualizationProps> = ({
  tracks,
  className = ''
}) => {
  // If no tracks, return null
  if (!tracks || tracks.length === 0) return null;
  
  // Calculate max height for visualization
  const maxHeight = 100;
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Energy Flow</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <div className="h-[150px] flex items-end justify-between gap-1">
          {tracks.map((track, index) => {
            const height = Math.max(20, track.energy * maxHeight);
            
            return (
              <div key={track.id} className="flex flex-col items-center">
                <div 
                  className="w-8 bg-primary rounded-t-sm"
                  style={{ height: `${height}px` }}
                  title={`${track.title} - Energy: ${(track.energy * 10).toFixed(1)}/10`}
                />
                <p className="text-xs mt-1 truncate max-w-[60px] text-center">
                  {index + 1}
                </p>
              </div>
            );
          })}
        </div>
        
        <div className="mt-4">
          <Tabs defaultValue="tracks">
            <TabsList className="grid grid-cols-2 h-8">
              <TabsTrigger value="tracks" className="text-xs">Tracks</TabsTrigger>
              <TabsTrigger value="analysis" className="text-xs">Analysis</TabsTrigger>
            </TabsList>
            
            <TabsContent value="tracks" className="mt-2">
              <div className="text-xs space-y-1">
                {tracks.map((track, index) => (
                  <div key={track.id} className="flex justify-between items-center">
                    <span>{index + 1}. {track.title}</span>
                    <Badge variant="outline">
                      Energy: {(track.energy * 10).toFixed(1)}
                    </Badge>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="analysis" className="mt-2">
              <div className="text-xs">
                <p>Average Energy: {(tracks.reduce((sum, track) => sum + track.energy, 0) / tracks.length * 10).toFixed(1)}/10</p>
                <p className="mt-1">Peak: Track {tracks.indexOf(tracks.reduce((max, track) => max.energy > track.energy ? max : track, tracks[0])) + 1}</p>
                <p className="mt-1">Valley: Track {tracks.indexOf(tracks.reduce((min, track) => min.energy < track.energy ? min : track, tracks[0])) + 1}</p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
};

export { 
  TransitionVisualization,
  KeyCompatibilityVisualization,
  EnergyFlowVisualization
};
