import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, ExternalLink, Co<PERSON>, Check, X } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';

interface CodeBlockProps {
  language: string;
  value: string;
}

interface CardBlockProps {
  title: string;
  content: string;
}

interface TabsBlockProps {
  tabs: {
    title: string;
    content: string;
  }[];
}

interface ChartBlockProps {
  type: 'bar' | 'line' | 'pie';
  data: any;
  options?: any;
}

interface RichResponseRendererProps {
  content: string;
  className?: string;
}

// Code block component with syntax highlighting and copy button
const CodeBlock: React.FC<CodeBlockProps> = ({ language, value }) => {
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleCopy = () => {
    navigator.clipboard.writeText(value);
    setCopied(true);

    toast({
      title: 'Copied to clipboard',
      description: 'Code has been copied to your clipboard',
    });

    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative">
      <div className="absolute right-2 top-2 z-10">
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 bg-background/80 backdrop-blur-sm"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="h-3 w-3" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      </div>
      <pre className="bg-muted p-4 rounded-md overflow-x-auto">
        <code className={`language-${language}`}>
          {value}
        </code>
      </pre>
    </div>
  );
};

// Card block component for structured information
const CardBlock: React.FC<CardBlockProps> = ({ title, content }) => {
  return (
    <Card className="my-4">
      <CardHeader className="py-2">
        <CardTitle className="text-sm">{title}</CardTitle>
      </CardHeader>
      <CardContent className="py-2">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {content}
        </ReactMarkdown>
      </CardContent>
    </Card>
  );
};

// Tabs block component for organizing content
const TabsBlock: React.FC<TabsBlockProps> = ({ tabs }) => {
  return (
    <Tabs defaultValue={tabs[0]?.title} className="my-4">
      <TabsList className="grid" style={{ gridTemplateColumns: `repeat(${tabs.length}, 1fr)` }}>
        {tabs.map(tab => (
          <TabsTrigger key={tab.title} value={tab.title}>
            {tab.title}
          </TabsTrigger>
        ))}
      </TabsList>
      {tabs.map(tab => (
        <TabsContent key={tab.title} value={tab.title} className="pt-2">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {tab.content}
          </ReactMarkdown>
        </TabsContent>
      ))}
    </Tabs>
  );
};

// Collapsible section component
const CollapsibleSection: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border rounded-md my-4">
      <button
        className="flex justify-between items-center w-full p-2 text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium">{title}</span>
        {isOpen ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </button>
      {isOpen && (
        <div className="p-2 pt-0 border-t">
          {children}
        </div>
      )}
    </div>
  );
};

// Custom renderer for markdown components
const customRenderers = {
  // Override code block rendering
  code({ node, inline, className, children, ...props }: any) {
    const match = /language-(\w+)/.exec(className || '');
    const language = match ? match[1] : '';

    // Handle special code blocks
    if (language === 'card') {
      try {
        const cardData = JSON.parse(String(children).replace(/\n$/, ''));
        return <CardBlock title={cardData.title} content={cardData.content} />;
      } catch (e) {
        return <p>Invalid card data</p>;
      }
    }

    if (language === 'tabs') {
      try {
        const tabsData = JSON.parse(String(children).replace(/\n$/, ''));
        return <TabsBlock tabs={tabsData} />;
      } catch (e) {
        return <p>Invalid tabs data</p>;
      }
    }

    if (language === 'collapsible') {
      try {
        const collapsibleData = JSON.parse(String(children).replace(/\n$/, ''));
        return (
          <CollapsibleSection title={collapsibleData.title}>
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {collapsibleData.content}
            </ReactMarkdown>
          </CollapsibleSection>
        );
      } catch (e) {
        return <p>Invalid collapsible data</p>;
      }
    }

    // Regular code blocks
    return !inline ? (
      <CodeBlock language={language} value={String(children).replace(/\n$/, '')} />
    ) : (
      <code className="bg-muted px-1 py-0.5 rounded text-sm" {...props}>
        {children}
      </code>
    );
  },

  // Override link rendering to open in new tab
  a({ node, children, href, ...props }: any) {
    return (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-primary hover:underline inline-flex items-center"
        {...props}
      >
        {children}
        <ExternalLink className="h-3 w-3 ml-1" />
      </a>
    );
  },

  // Override image rendering to add better styling and full-size view
  img({ node, src, alt, ...props }: any) {
    const [isFullSize, setIsFullSize] = useState(false);

    if (isFullSize) {
      return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80" onClick={() => setIsFullSize(false)}>
          <div className="relative max-w-[90vw] max-h-[90vh]">
            <img
              src={src}
              alt={alt || 'Generated image'}
              className="max-w-full max-h-[90vh] object-contain"
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 bg-background/20 backdrop-blur-sm hover:bg-background/40"
              onClick={(e) => {
                e.stopPropagation();
                setIsFullSize(false);
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="my-4 relative">
        <img
          src={src}
          alt={alt || 'Generated image'}
          className="rounded-md max-w-full cursor-pointer hover:opacity-90 transition-opacity"
          onClick={() => setIsFullSize(true)}
          {...props}
        />
        <div className="text-xs text-muted-foreground mt-1 text-center">
          {alt || 'Generated image'} (Click to enlarge)
        </div>
      </div>
    );
  }
};

// Main component for rendering rich responses
const RichResponseRenderer: React.FC<RichResponseRendererProps> = ({
  content,
  className = ''
}) => {
  return (
    <div className={`prose dark:prose-invert prose-sm max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={customRenderers}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default RichResponseRenderer;
