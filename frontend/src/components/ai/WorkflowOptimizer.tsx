import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Lightbulb, Zap, Clock, Workflow, Sparkles } from 'lucide-react';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import { useGlobalAIAssistant } from '@/providers/GlobalAIAssistantProvider';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface OptimizationTip {
  id: string;
  title: string;
  description: string;
  category: 'speed' | 'quality' | 'workflow' | 'creative';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  context?: string;
  action?: string;
}

interface WorkflowOptimizerProps {
  className?: string;
}

const WorkflowOptimizer: React.FC<WorkflowOptimizerProps> = ({
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<string>('speed');
  const { appState, addRecentAction } = useApplicationContext();
  const { openAssistant } = useGlobalAIAssistant();
  const { toast } = useToast();

  // Generate optimization tips based on application state and user behavior
  const generateOptimizationTips = (): Record<string, OptimizationTip[]> => {
    const { currentPage, recentActions } = appState;
    
    // Speed optimization tips
    const speedTips: OptimizationTip[] = [
      {
        id: 'speed-1',
        title: 'Use Keyboard Shortcuts',
        description: 'Learn the most common keyboard shortcuts to speed up your workflow.',
        category: 'speed',
        difficulty: 'beginner',
        context: 'Here are the most useful keyboard shortcuts for DJ Mix Constructor:',
        action: 'View Shortcuts'
      },
      {
        id: 'speed-2',
        title: 'Batch Process Tracks',
        description: 'Process multiple tracks at once for faster preparation.',
        category: 'speed',
        difficulty: 'intermediate',
        context: 'Would you like to learn how to batch process tracks for faster preparation?',
        action: 'Learn Batch Processing'
      }
    ];
    
    // Quality optimization tips
    const qualityTips: OptimizationTip[] = [
      {
        id: 'quality-1',
        title: 'Set Precise Beat Grids',
        description: 'Ensure your beat grids are accurately set for better mixing.',
        category: 'quality',
        difficulty: 'beginner',
        context: 'Would you like to learn how to set precise beat grids for better mixing?',
        action: 'Learn Beat Gridding'
      },
      {
        id: 'quality-2',
        title: 'Use EQ Mixing Techniques',
        description: 'Apply proper EQ techniques for cleaner transitions.',
        category: 'quality',
        difficulty: 'intermediate',
        context: 'Would you like to learn about EQ mixing techniques for cleaner transitions?',
        action: 'Learn EQ Techniques'
      }
    ];
    
    // Workflow optimization tips
    const workflowTips: OptimizationTip[] = [
      {
        id: 'workflow-1',
        title: 'Organize Tracks by Energy',
        description: 'Group tracks by energy level for easier mix planning.',
        category: 'workflow',
        difficulty: 'beginner',
        context: 'Would you like to learn how to organize tracks by energy level for easier mix planning?',
        action: 'Learn Organization'
      },
      {
        id: 'workflow-2',
        title: 'Create Template Mixes',
        description: 'Save template mixes for common scenarios to save time.',
        category: 'workflow',
        difficulty: 'intermediate',
        context: 'Would you like to learn how to create template mixes for common scenarios?',
        action: 'Learn Templates'
      }
    ];
    
    // Creative optimization tips
    const creativeTips: OptimizationTip[] = [
      {
        id: 'creative-1',
        title: 'Try AI-Generated Transitions',
        description: 'Let the AI suggest creative transition techniques between tracks.',
        category: 'creative',
        difficulty: 'beginner',
        context: 'Would you like me to suggest some creative transition techniques for your mix?',
        action: 'Generate Ideas'
      },
      {
        id: 'creative-2',
        title: 'Experiment with Energy Flow',
        description: 'Create dynamic mixes by planning energy progression.',
        category: 'creative',
        difficulty: 'intermediate',
        context: 'Would you like to learn about creating dynamic mixes through energy progression?',
        action: 'Learn Energy Flow'
      }
    ];
    
    return {
      speed: speedTips,
      quality: qualityTips,
      workflow: workflowTips,
      creative: creativeTips
    };
  };
  
  const optimizationTips = generateOptimizationTips();
  
  // Handle tip action
  const handleTipAction = (tip: OptimizationTip) => {
    // Open assistant with context if available
    if (tip.context) {
      openAssistant(tip.context);
    }
    
    // Log the action
    addRecentAction({
      type: 'optimization_tip_used',
      description: `Used optimization tip: ${tip.title}`
    });
    
    // Show toast
    toast({
      title: 'Tip Applied',
      description: tip.title
    });
  };
  
  // Render icon based on category
  const renderCategoryIcon = (category: string) => {
    switch (category) {
      case 'speed':
        return <Zap className="h-4 w-4" />;
      case 'quality':
        return <Sparkles className="h-4 w-4" />;
      case 'workflow':
        return <Workflow className="h-4 w-4" />;
      case 'creative':
        return <Lightbulb className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <Clock className="h-4 w-4 mr-2" />
          Workflow Optimization
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <Tabs defaultValue="speed" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 h-8">
            <TabsTrigger value="speed" className="text-xs">Speed</TabsTrigger>
            <TabsTrigger value="quality" className="text-xs">Quality</TabsTrigger>
            <TabsTrigger value="workflow" className="text-xs">Workflow</TabsTrigger>
            <TabsTrigger value="creative" className="text-xs">Creative</TabsTrigger>
          </TabsList>
          
          {Object.entries(optimizationTips).map(([category, tips]) => (
            <TabsContent key={category} value={category} className="mt-2 space-y-2">
              {tips.map(tip => (
                <div key={tip.id} className="border rounded-md p-2">
                  <div className="flex items-start">
                    <div className="mt-0.5 mr-2">
                      {renderCategoryIcon(category)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{tip.title}</p>
                      <p className="text-xs text-muted-foreground">{tip.description}</p>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-xs text-muted-foreground capitalize">
                          {tip.difficulty}
                        </span>
                        {tip.action && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="h-6 text-xs"
                            onClick={() => handleTipAction(tip)}
                          >
                            {tip.action}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default WorkflowOptimizer;
