import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useUserPreferences } from '@/providers/UserPreferencesProvider';
import { SkillLevel } from '@/types/userPreferences';
import { trackFeatureUsage } from './UserPreferenceCollector';
import { Loader2, Save, RefreshCw } from 'lucide-react';

interface UserPreferenceProfileProps {
  className?: string;
}

const UserPreferenceProfile: React.FC<UserPreferenceProfileProps> = ({
  className = ''
}) => {
  const { preferences, updatePreferences, isLoading } = useUserPreferences();
  const [activeTab, setActiveTab] = useState('general');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Form state
  const [formState, setFormState] = useState({
    skillLevel: preferences.skillLevel,
    experienceYears: preferences.experienceYears,
    preferredGenres: [...(preferences.preferredGenres || [])],
    preferredBpmRanges: [...(preferences.preferredBpmRanges || [])],
    preferredKeys: [...(preferences.preferredKeys || [])],
    preferredTransitions: [...(preferences.preferredTransitions || [])],
    preferExploration: preferences.preferExploration,
    complexitySetting: preferences.complexitySetting
  });
  
  // Format date string
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };
  
  // Handle form changes
  const handleFormChange = (field: string, value: any) => {
    setFormState({
      ...formState,
      [field]: value
    });
  };
  
  // Handle save
  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      await updatePreferences(formState);
      setIsEditing(false);
      trackFeatureUsage('preference_profile', 'complete', { action: 'save' });
    } catch (error) {
      console.error('Failed to update preferences:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    // Reset form state to current preferences
    setFormState({
      skillLevel: preferences.skillLevel,
      experienceYears: preferences.experienceYears,
      preferredGenres: [...(preferences.preferredGenres || [])],
      preferredBpmRanges: [...(preferences.preferredBpmRanges || [])],
      preferredKeys: [...(preferences.preferredKeys || [])],
      preferredTransitions: [...(preferences.preferredTransitions || [])],
      preferExploration: preferences.preferExploration,
      complexitySetting: preferences.complexitySetting
    });
    
    setIsEditing(false);
  };
  
  // Handle reset
  const handleReset = async () => {
    if (confirm('Are you sure you want to reset your preferences? This cannot be undone.')) {
      setIsSaving(true);
      
      try {
        await updatePreferences({
          hasCompletedSurvey: false
        });
        trackFeatureUsage('preference_profile', 'complete', { action: 'reset' });
      } catch (error) {
        console.error('Failed to reset preferences:', error);
      } finally {
        setIsSaving(false);
      }
    }
  };
  
  if (isLoading) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>AI Personalization Profile</CardTitle>
          <CardDescription>
            Your preferences help the AI provide better suggestions
          </CardDescription>
        </div>
        
        <div className="flex items-center space-x-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              Edit Preferences
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="music">Music Preferences</TabsTrigger>
            <TabsTrigger value="ai">AI Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>DJ Skill Level</Label>
                {isEditing ? (
                  <RadioGroup 
                    value={formState.skillLevel} 
                    onValueChange={(value) => handleFormChange('skillLevel', value)}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={SkillLevel.BEGINNER} id="edit-beginner" />
                      <Label htmlFor="edit-beginner">Beginner</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={SkillLevel.INTERMEDIATE} id="edit-intermediate" />
                      <Label htmlFor="edit-intermediate">Intermediate</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={SkillLevel.ADVANCED} id="edit-advanced" />
                      <Label htmlFor="edit-advanced">Advanced</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={SkillLevel.PROFESSIONAL} id="edit-professional" />
                      <Label htmlFor="edit-professional">Professional</Label>
                    </div>
                  </RadioGroup>
                ) : (
                  <div className="p-2 bg-muted rounded-md">
                    {preferences.skillLevel || 'Not specified'}
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <Label>Experience</Label>
                {isEditing ? (
                  <Select 
                    value={formState.experienceYears?.toString()} 
                    onValueChange={(value) => handleFormChange('experienceYears', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select years of experience" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Less than 1 year</SelectItem>
                      <SelectItem value="1">1 year</SelectItem>
                      <SelectItem value="2">2 years</SelectItem>
                      <SelectItem value="3">3 years</SelectItem>
                      <SelectItem value="5">5+ years</SelectItem>
                      <SelectItem value="10">10+ years</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 bg-muted rounded-md">
                    {preferences.experienceYears === 0 
                      ? 'Less than 1 year' 
                      : `${preferences.experienceYears} year${preferences.experienceYears > 1 ? 's' : ''}`}
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Last Updated</Label>
              <div className="p-2 bg-muted rounded-md">
                {formatDate(preferences.lastUpdated)}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="music" className="space-y-4">
            <div className="space-y-2">
              <Label>Preferred Genres</Label>
              <div className="flex flex-wrap gap-2">
                {preferences.preferredGenres && preferences.preferredGenres.length > 0 ? (
                  preferences.preferredGenres.map((genre) => (
                    <Badge key={genre.genre} variant="secondary">
                      {genre.genre} ({genre.weight}%)
                    </Badge>
                  ))
                ) : (
                  <div className="text-muted-foreground">No preferred genres specified</div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Preferred BPM Ranges</Label>
              <div className="flex flex-wrap gap-2">
                {preferences.preferredBpmRanges && preferences.preferredBpmRanges.length > 0 ? (
                  preferences.preferredBpmRanges.map((range, index) => (
                    <Badge key={index} variant="secondary">
                      {range.min}-{range.max} BPM ({range.weight}%)
                    </Badge>
                  ))
                ) : (
                  <div className="text-muted-foreground">No preferred BPM ranges specified</div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Preferred Keys</Label>
              <div className="flex flex-wrap gap-2">
                {preferences.preferredKeys && preferences.preferredKeys.length > 0 ? (
                  preferences.preferredKeys.map((key) => (
                    <Badge key={key.key} variant="secondary">
                      {key.key} ({key.weight}%)
                    </Badge>
                  ))
                ) : (
                  <div className="text-muted-foreground">No preferred keys specified</div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Preferred Transitions</Label>
              <div className="flex flex-wrap gap-2">
                {preferences.preferredTransitions && preferences.preferredTransitions.length > 0 ? (
                  preferences.preferredTransitions.map((transition) => (
                    <Badge key={transition.type} variant="secondary">
                      {transition.type} ({transition.weight}%)
                    </Badge>
                  ))
                ) : (
                  <div className="text-muted-foreground">No preferred transitions specified</div>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="ai" className="space-y-4">
            <div className="space-y-2">
              <Label>Suggestion Complexity</Label>
              {isEditing ? (
                <div className="space-y-2">
                  <Slider
                    value={[formState.complexitySetting || 50]}
                    min={0}
                    max={100}
                    step={10}
                    onValueChange={(value) => handleFormChange('complexitySetting', value[0])}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Simple</span>
                    <span>Balanced</span>
                    <span>Complex</span>
                  </div>
                </div>
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {preferences.complexitySetting <= 30 ? 'Simple' : 
                   preferences.complexitySetting <= 70 ? 'Balanced' : 'Complex'} 
                  ({preferences.complexitySetting}%)
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                {isEditing ? (
                  <>
                    <Switch
                      id="exploration"
                      checked={formState.preferExploration}
                      onCheckedChange={(checked) => handleFormChange('preferExploration', checked)}
                    />
                    <Label htmlFor="exploration">Prefer exploration over familiar suggestions</Label>
                  </>
                ) : (
                  <>
                    <div className={`h-4 w-4 rounded-full ${preferences.preferExploration ? 'bg-primary' : 'bg-muted-foreground'}`}></div>
                    <span>Prefer exploration over familiar suggestions: {preferences.preferExploration ? 'Yes' : 'No'}</span>
                  </>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleReset} disabled={isSaving}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Reset Preferences
        </Button>
      </CardFooter>
    </Card>
  );
};

export default UserPreferenceProfile;
