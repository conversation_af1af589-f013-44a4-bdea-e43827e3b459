import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useUserPreferences } from '@/providers/UserPreferencesProvider';
import { SkillLevel } from '@/types/userPreferences';
import { trackFeatureUsage } from './UserPreferenceCollector';

interface UserPreferenceSurveyProps {
  onComplete?: () => void;
  className?: string;
}

const UserPreferenceSurvey: React.FC<UserPreferenceSurveyProps> = ({
  onComplete,
  className = ''
}) => {
  const { preferences, updatePreferences } = useUserPreferences();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form state
  const [skillLevel, setSkillLevel] = useState<SkillLevel>(preferences.skillLevel || SkillLevel.BEGINNER);
  const [experienceYears, setExperienceYears] = useState<number>(preferences.experienceYears || 0);
  const [selectedGenres, setSelectedGenres] = useState<string[]>(
    preferences.preferredGenres?.map(g => g.genre) || []
  );
  const [complexitySetting, setComplexitySetting] = useState<number>(
    preferences.complexitySetting || 50
  );
  const [preferExploration, setPreferExploration] = useState<boolean>(
    preferences.preferExploration !== undefined ? preferences.preferExploration : true
  );
  
  // Available genres
  const availableGenres = [
    'House', 'Deep House', 'Tech House', 'Progressive House', 'Techno',
    'Minimal Techno', 'Trance', 'Progressive Trance', 'Drum & Bass',
    'Dubstep', 'Trap', 'Hip Hop', 'R&B', 'Pop', 'Rock', 'Indie',
    'Ambient', 'Chill', 'Lounge', 'Disco', 'Funk', 'Soul'
  ];
  
  // Steps configuration
  const steps = [
    {
      title: 'DJ Experience',
      description: 'Tell us about your DJ experience level',
      content: (
        <div className="space-y-6">
          <div className="space-y-3">
            <Label>How would you describe your DJ skill level?</Label>
            <RadioGroup value={skillLevel} onValueChange={(value) => setSkillLevel(value as SkillLevel)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={SkillLevel.BEGINNER} id="beginner" />
                <Label htmlFor="beginner">Beginner</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={SkillLevel.INTERMEDIATE} id="intermediate" />
                <Label htmlFor="intermediate">Intermediate</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={SkillLevel.ADVANCED} id="advanced" />
                <Label htmlFor="advanced">Advanced</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={SkillLevel.PROFESSIONAL} id="professional" />
                <Label htmlFor="professional">Professional</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-3">
            <Label>How many years have you been DJing?</Label>
            <Select 
              value={experienceYears.toString()} 
              onValueChange={(value) => setExperienceYears(parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select years of experience" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Less than 1 year</SelectItem>
                <SelectItem value="1">1 year</SelectItem>
                <SelectItem value="2">2 years</SelectItem>
                <SelectItem value="3">3 years</SelectItem>
                <SelectItem value="5">5+ years</SelectItem>
                <SelectItem value="10">10+ years</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )
    },
    {
      title: 'Music Preferences',
      description: 'Tell us about your musical preferences',
      content: (
        <div className="space-y-6">
          <div className="space-y-3">
            <Label>Which genres do you typically play? (Select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {availableGenres.map((genre) => (
                <div key={genre} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`genre-${genre}`} 
                    checked={selectedGenres.includes(genre)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedGenres([...selectedGenres, genre]);
                      } else {
                        setSelectedGenres(selectedGenres.filter(g => g !== genre));
                      }
                    }}
                  />
                  <Label htmlFor={`genre-${genre}`}>{genre}</Label>
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    },
    {
      title: 'AI Preferences',
      description: 'Customize how the AI assistant works for you',
      content: (
        <div className="space-y-6">
          <div className="space-y-3">
            <Label>Suggestion Complexity</Label>
            <div className="space-y-2">
              <Slider
                value={[complexitySetting]}
                min={0}
                max={100}
                step={10}
                onValueChange={(value) => setComplexitySetting(value[0])}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Simple</span>
                <span>Balanced</span>
                <span>Complex</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              This setting controls how complex the AI suggestions will be. Higher values provide more detailed and advanced suggestions.
            </p>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="exploration" 
                checked={preferExploration}
                onCheckedChange={(checked) => setPreferExploration(!!checked)}
              />
              <Label htmlFor="exploration">Prefer exploration over familiar suggestions</Label>
            </div>
            <p className="text-xs text-muted-foreground">
              When enabled, the AI will suggest more diverse and exploratory options rather than sticking closely to your established preferences.
            </p>
          </div>
        </div>
      )
    }
  ];
  
  // Handle next step
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      trackFeatureUsage('preference_survey', 'complete', { step: currentStep });
    } else {
      handleSubmit();
    }
  };
  
  // Handle previous step
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // Convert selected genres to genre preferences
      const genrePreferences = selectedGenres.map(genre => ({
        genre,
        weight: 80 // Default weight for selected genres
      }));
      
      // Update user preferences
      await updatePreferences({
        skillLevel,
        experienceYears,
        preferredGenres: genrePreferences,
        complexitySetting,
        preferExploration,
        hasCompletedSurvey: true
      });
      
      // Track completion
      trackFeatureUsage('preference_survey', 'complete', { completed: true });
      
      // Call onComplete callback
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Failed to update user preferences:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Skip survey
  const handleSkip = () => {
    updatePreferences({
      hasCompletedSurvey: true
    });
    
    trackFeatureUsage('preference_survey', 'complete', { skipped: true });
    
    if (onComplete) {
      onComplete();
    }
  };
  
  return (
    <Card className={`w-full max-w-lg mx-auto ${className}`}>
      <CardHeader>
        <CardTitle>{steps[currentStep].title}</CardTitle>
        <CardDescription>{steps[currentStep].description}</CardDescription>
      </CardHeader>
      
      <CardContent>
        {steps[currentStep].content}
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <div>
          {currentStep === 0 ? (
            <Button variant="ghost" onClick={handleSkip}>
              Skip for now
            </Button>
          ) : (
            <Button variant="outline" onClick={handlePrevious}>
              Previous
            </Button>
          )}
        </div>
        
        <Button onClick={handleNext} disabled={isSubmitting}>
          {currentStep < steps.length - 1 ? 'Next' : 'Complete'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default UserPreferenceSurvey;
