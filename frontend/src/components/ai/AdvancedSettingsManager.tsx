import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { <PERSON>lider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, AlertCircle, Info, Settings2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { aiSettingsService, CategoryMetadata, SettingMetadata } from '@/services/api/aiSettings';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface AdvancedSettingsManagerProps {
  className?: string;
}

const AdvancedSettingsManager: React.FC<AdvancedSettingsManagerProps> = ({ className = '' }) => {
  const [categories, setCategories] = useState<string[]>([]);
  const [settings, setSettings] = useState<Record<string, Record<string, any>>>({});
  const [metadata, setMetadata] = useState<Record<string, CategoryMetadata>>({});
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [editedSettings, setEditedSettings] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load categories, settings, and metadata on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Load categories, settings, and metadata in parallel
      const [categoriesResponse, settingsResponse, metadataResponse] = await Promise.all([
        aiSettingsService.getAdvancedSettingsCategories(),
        aiSettingsService.getAdvancedSettings(),
        aiSettingsService.getSettingMetadata()
      ]);
      
      setCategories(categoriesResponse);
      setSettings(settingsResponse);
      setMetadata(metadataResponse.metadata);
      
      // Select the first category by default
      if (categoriesResponse.length > 0) {
        const firstCategory = categoriesResponse[0];
        setSelectedCategory(firstCategory);
        setEditedSettings(settingsResponse[firstCategory]);
      }
    } catch (error: any) {
      setError('Failed to load advanced settings: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to load advanced settings',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setEditedSettings(settings[category]);
  };

  const handleSettingChange = (settingName: string, value: any) => {
    setEditedSettings(prev => ({
      ...prev,
      [settingName]: value
    }));
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    setError(null);
    try {
      await aiSettingsService.updateAdvancedSettings(selectedCategory, editedSettings);
      
      // Update local state
      setSettings(prev => ({
        ...prev,
        [selectedCategory]: editedSettings
      }));
      
      toast({
        title: 'Success',
        description: 'Advanced settings updated successfully'
      });
    } catch (error: any) {
      setError('Failed to update advanced settings: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to update advanced settings',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSettings = async () => {
    setIsResetting(true);
    setError(null);
    try {
      await aiSettingsService.resetAdvancedSettings(selectedCategory);
      
      // Reload data to get the default values
      await loadData();
      
      toast({
        title: 'Success',
        description: 'Advanced settings reset to default'
      });
    } catch (error: any) {
      setError('Failed to reset advanced settings: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to reset advanced settings',
        variant: 'destructive'
      });
    } finally {
      setIsResetting(false);
    }
  };

  const renderSetting = (settingName: string, settingValue: any, settingMeta: SettingMetadata) => {
    switch (settingMeta.type) {
      case 'boolean':
        return (
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor={`setting-${settingName}`}>{settingMeta.name}</Label>
              <div className="text-xs text-muted-foreground">{settingMeta.description}</div>
            </div>
            <Switch
              id={`setting-${settingName}`}
              checked={settingValue}
              onCheckedChange={(checked) => handleSettingChange(settingName, checked)}
              disabled={isSaving || isResetting}
            />
          </div>
        );
      
      case 'integer':
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor={`setting-${settingName}`}>{settingMeta.name}</Label>
              <span className="text-sm font-medium">{settingValue}</span>
            </div>
            <Slider
              id={`setting-${settingName}`}
              value={[settingValue]}
              min={settingMeta.min || 0}
              max={settingMeta.max || 100}
              step={1}
              onValueChange={(value) => handleSettingChange(settingName, value[0])}
              disabled={isSaving || isResetting}
            />
            <div className="text-xs text-muted-foreground">{settingMeta.description}</div>
          </div>
        );
      
      case 'select':
        return (
          <div className="space-y-2">
            <Label htmlFor={`setting-${settingName}`}>{settingMeta.name}</Label>
            <Select
              value={settingValue}
              onValueChange={(value) => handleSettingChange(settingName, value)}
              disabled={isSaving || isResetting}
            >
              <SelectTrigger id={`setting-${settingName}`}>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                {settingMeta.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option.charAt(0).toUpperCase() + option.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">{settingMeta.description}</div>
          </div>
        );
      
      default:
        return (
          <div className="space-y-0.5">
            <Label>{settingMeta.name}</Label>
            <div className="text-sm">{String(settingValue)}</div>
            <div className="text-xs text-muted-foreground">{settingMeta.description}</div>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading advanced settings...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings2 className="h-5 w-5 mr-2" />
          Advanced Settings
        </CardTitle>
        <CardDescription>
          Configure advanced AI behavior and performance settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        {categories.length === 0 ? (
          <div className="text-center p-4">
            <p>No advanced settings found.</p>
          </div>
        ) : (
          <Tabs value={selectedCategory} onValueChange={handleCategorySelect}>
            <TabsList className="grid grid-cols-2 md:grid-cols-3 mb-4">
              {categories.map(category => (
                <TabsTrigger key={category} value={category}>
                  {metadata[category]?.name || category}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {categories.map(category => (
              <TabsContent key={category} value={category}>
                <div className="space-y-6">
                  {metadata[category] && (
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>{metadata[category].name}</AlertTitle>
                      <AlertDescription>{metadata[category].description}</AlertDescription>
                    </Alert>
                  )}
                  
                  {metadata[category] && settings[category] && Object.entries(settings[category]).map(([settingName, settingValue]) => {
                    const settingMeta = metadata[category].settings[settingName];
                    if (!settingMeta) return null;
                    
                    return (
                      <div key={settingName} className="py-2">
                        {renderSetting(
                          settingName,
                          category === selectedCategory ? editedSettings[settingName] : settingValue,
                          settingMeta
                        )}
                      </div>
                    );
                  })}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleResetSettings}
          disabled={isLoading || isSaving || isResetting}
        >
          {isResetting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Reset to Default
        </Button>
        <Button
          onClick={handleSaveSettings}
          disabled={isLoading || isSaving || isResetting}
        >
          {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AdvancedSettingsManager;
