import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, X, ChevronRight, Check, Sparkles, RefreshCw } from 'lucide-react';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import { useGlobalAIAssistant } from '@/providers/GlobalAIAssistantProvider';
import { useUserPreferences } from '@/providers/UserPreferencesProvider';
import { useToast } from '@/components/ui/use-toast';
import { 
  getAISuggestions, 
  getRuleBasedSuggestions, 
  trackSuggestionInteraction,
  Suggestion
} from '@/services/api/smartSuggestions';

interface EnhancedSmartSuggestionsProps {
  className?: string;
  maxSuggestions?: number;
  refreshInterval?: number; // Refresh interval in milliseconds
  preferAI?: boolean; // Whether to prefer AI suggestions over rule-based ones
}

const EnhancedSmartSuggestions: React.FC<EnhancedSmartSuggestionsProps> = ({
  className = '',
  maxSuggestions = 3,
  refreshInterval = 300000, // Default: refresh every 5 minutes
  preferAI = true
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());
  
  const { appState, addRecentAction, trackFeatureInteraction } = useApplicationContext();
  const { preferences } = useUserPreferences();
  const { openAssistant } = useGlobalAIAssistant();
  const { toast } = useToast();

  // Function to fetch suggestions
  const fetchSuggestions = useCallback(async () => {
    setIsLoading(true);
    
    try {
      let newSuggestions: Suggestion[] = [];
      
      // Get rule-based suggestions first
      const ruleBasedSuggestions = getRuleBasedSuggestions(appState, maxSuggestions);
      
      // If AI is preferred and enabled, try to get AI suggestions
      if (preferAI && preferences.aiFeatures.smartSuggestions.enabled) {
        try {
          const aiSuggestions = await getAISuggestions({
            appState,
            userPreferences: preferences,
            maxSuggestions
          });
          
          if (aiSuggestions.length > 0) {
            // Combine AI and rule-based suggestions, prioritizing AI ones
            newSuggestions = [
              ...aiSuggestions,
              ...ruleBasedSuggestions.filter(rbs => 
                !aiSuggestions.some(ais => 
                  ais.title === rbs.title || 
                  ais.description === rbs.description
                )
              )
            ].slice(0, maxSuggestions);
          } else {
            newSuggestions = ruleBasedSuggestions;
          }
          
          // Track successful AI suggestion generation
          trackFeatureInteraction('ai_smart_suggestions', true);
        } catch (error) {
          console.error('Error fetching AI suggestions:', error);
          // Fall back to rule-based suggestions
          newSuggestions = ruleBasedSuggestions;
          // Track error
          trackFeatureInteraction('ai_smart_suggestions', false, { error: String(error) });
        }
      } else {
        // Use only rule-based suggestions
        newSuggestions = ruleBasedSuggestions;
      }
      
      // Update suggestions
      setSuggestions(newSuggestions);
      setLastRefreshed(new Date());
      
      // Reset active suggestion index
      setActiveSuggestionIndex(0);
    } catch (error) {
      console.error('Error generating suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [appState, preferences, maxSuggestions, preferAI, trackFeatureInteraction]);

  // Fetch suggestions when component mounts or when dependencies change
  useEffect(() => {
    fetchSuggestions();
  }, [fetchSuggestions]);
  
  // Set up refresh interval
  useEffect(() => {
    if (refreshInterval > 0) {
      const intervalId = setInterval(() => {
        fetchSuggestions();
      }, refreshInterval);
      
      return () => clearInterval(intervalId);
    }
  }, [fetchSuggestions, refreshInterval]);

  // Handle dismissing a suggestion
  const handleDismissSuggestion = async (id: string) => {
    const suggestion = suggestions.find(s => s.id === id);
    if (!suggestion) return;
    
    setSuggestions(prev => prev.filter(s => s.id !== id));
    
    // Log the action
    addRecentAction({
      type: 'suggestion_dismissed',
      description: `Dismissed suggestion: ${suggestion.title}`,
      data: { suggestionId: id, suggestionType: suggestion.type }
    });
    
    // Track interaction with the suggestion
    if (suggestion.source === 'ai') {
      await trackSuggestionInteraction(id, 'dismissed');
    }
  };

  // Handle suggestion action
  const handleSuggestionAction = async (suggestion: Suggestion) => {
    // Open assistant with context if available
    if (suggestion.context) {
      openAssistant(suggestion.context);
    }

    // Execute custom action handler if available
    if (suggestion.actionHandler) {
      suggestion.actionHandler();
    }

    // Log the action
    addRecentAction({
      type: 'suggestion_accepted',
      description: `Accepted suggestion: ${suggestion.title}`,
      data: { suggestionId: suggestion.id, suggestionType: suggestion.type }
    });
    
    // Track interaction with the suggestion
    if (suggestion.source === 'ai') {
      await trackSuggestionInteraction(suggestion.id, 'accepted');
    }

    // Dismiss the suggestion
    handleDismissSuggestion(suggestion.id);

    // Show toast
    toast({
      title: 'Suggestion Applied',
      description: suggestion.title
    });
  };

  // Handle next suggestion
  const handleNextSuggestion = () => {
    setActiveSuggestionIndex(prev => (prev + 1) % suggestions.length);
  };
  
  // Handle manual refresh
  const handleRefresh = () => {
    fetchSuggestions();
    
    toast({
      title: 'Refreshing Suggestions',
      description: 'Finding new suggestions based on your current context...'
    });
  };

  // If no suggestions, show a minimal card
  if (suggestions.length === 0) {
    return (
      <Card className={`shadow-md ${className}`}>
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center">
            <Lightbulb className="h-4 w-4 mr-2" />
            Smart Suggestions
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh suggestions"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </CardHeader>
        <CardContent className="pt-0 pb-3">
          <p className="text-xs text-muted-foreground">
            No suggestions available right now. Continue working and check back later.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Get the active suggestion
  const activeSuggestion = suggestions[activeSuggestionIndex];

  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium flex items-center">
          {activeSuggestion.source === 'ai' ? (
            <Sparkles className="h-4 w-4 mr-2 text-primary" />
          ) : (
            <Lightbulb className="h-4 w-4 mr-2" />
          )}
          Smart Suggestions
          {suggestions.length > 1 && (
            <Badge variant="outline" className="ml-2 text-xs">
              {activeSuggestionIndex + 1}/{suggestions.length}
            </Badge>
          )}
        </CardTitle>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh suggestions"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          {suggestions.length > 1 && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleNextSuggestion}
              title="Next suggestion"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => handleDismissSuggestion(activeSuggestion.id)}
            title="Dismiss"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-sm font-medium">{activeSuggestion.title}</h3>
              <p className="text-xs text-muted-foreground">{activeSuggestion.description}</p>
              {activeSuggestion.source === 'ai' && (
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">
                    AI-powered
                  </Badge>
                  {activeSuggestion.confidence && (
                    <span className="text-[10px] text-muted-foreground ml-1">
                      {Math.round(activeSuggestion.confidence * 100)}% confidence
                    </span>
                  )}
                </div>
              )}
            </div>
            <Badge variant={getBadgeVariant(activeSuggestion.type)} className="ml-2">
              {activeSuggestion.type}
            </Badge>
          </div>
          {activeSuggestion.action && (
            <Button
              variant="outline"
              size="sm"
              className="w-full mt-2 text-xs h-8"
              onClick={() => handleSuggestionAction(activeSuggestion)}
            >
              <Check className="h-3 w-3 mr-1" />
              {activeSuggestion.action}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Helper function to get badge variant based on suggestion type
const getBadgeVariant = (type: string) => {
  switch (type) {
    case 'tip':
      return 'default';
    case 'optimization':
      return 'secondary';
    case 'feature':
      return 'outline';
    case 'workflow':
      return 'destructive';
    default:
      return 'default';
  }
};

export default EnhancedSmartSuggestions;
