import React from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Command, HelpCircle } from 'lucide-react';
import { useVoiceCommands, CommandCategory } from '@/providers/VoiceCommandProvider';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

interface VoiceCommandHelpProps {
  trigger?: React.ReactNode;
  className?: string;
}

const VoiceCommandHelp: React.FC<VoiceCommandHelpProps> = ({ 
  trigger,
  className = '' 
}) => {
  const voiceCommands = useVoiceCommands();
  
  // Get commands grouped by category
  const commandsByCategory = voiceCommands.commands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = [];
    }
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, typeof voiceCommands.commands>);
  
  // Get category name for display
  const getCategoryDisplayName = (category: string): string => {
    switch (category) {
      case CommandCategory.NAVIGATION:
        return 'Navigation';
      case CommandCategory.PLAYBACK:
        return 'Playback';
      case CommandCategory.INTERFACE:
        return 'Interface';
      case CommandCategory.SETTINGS:
        return 'Settings';
      case CommandCategory.GENERAL:
        return 'General';
      default:
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };
  
  // Get category description
  const getCategoryDescription = (category: string): string => {
    switch (category) {
      case CommandCategory.NAVIGATION:
        return 'Commands for navigating between different pages and views';
      case CommandCategory.PLAYBACK:
        return 'Commands for controlling audio playback';
      case CommandCategory.INTERFACE:
        return 'Commands for controlling the user interface';
      case CommandCategory.SETTINGS:
        return 'Commands for adjusting application settings';
      case CommandCategory.GENERAL:
        return 'General purpose commands';
      default:
        return '';
    }
  };
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className={className}>
            <Command className="h-4 w-4 mr-2" />
            Voice Commands
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Command className="h-5 w-5 mr-2" />
            Voice Commands Help
          </DialogTitle>
          <DialogDescription>
            Available voice commands for controlling the application
          </DialogDescription>
        </DialogHeader>
        
        <div className="mt-4">
          {!voiceCommands.settings.enabled ? (
            <div className="p-4 border rounded-md bg-muted/50 text-center">
              <HelpCircle className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <h3 className="font-medium mb-1">Voice Commands are Disabled</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Voice commands are currently disabled. Enable them in AI Settings to use these commands.
              </p>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => voiceCommands.setCommandsEnabled(true)}
              >
                Enable Voice Commands
              </Button>
            </div>
          ) : (
            <Tabs defaultValue="general">
              <TabsList className="grid grid-cols-5 mb-4">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="navigation">Navigation</TabsTrigger>
                <TabsTrigger value="playback">Playback</TabsTrigger>
                <TabsTrigger value="interface">Interface</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>
              
              {Object.entries(commandsByCategory).map(([category, commands]) => (
                <TabsContent key={category} value={category} className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">{getCategoryDisplayName(category)} Commands</h3>
                    <p className="text-sm text-muted-foreground">{getCategoryDescription(category)}</p>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    {commands.filter(cmd => cmd.enabled).map(command => (
                      <div key={command.id} className="p-3 border rounded-md">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{command.name}</h4>
                          <Badge variant="outline">{getCategoryDisplayName(command.category)}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">{command.description}</p>
                        <div>
                          <h5 className="text-xs font-medium mb-1">Say any of these phrases:</h5>
                          <div className="flex flex-wrap gap-2">
                            {command.phrases.map((phrase, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">"{phrase}"</Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {commands.filter(cmd => cmd.enabled).length === 0 && (
                      <div className="p-4 border rounded-md bg-muted/50 text-center">
                        <p className="text-sm text-muted-foreground">
                          No enabled commands in this category. Enable commands in AI Settings.
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          )}
          
          <div className="mt-6 p-4 border rounded-md bg-muted/30">
            <h3 className="font-medium mb-2">How to use voice commands</h3>
            <ol className="text-sm space-y-2 list-decimal pl-5">
              <li>Click the microphone button or press the voice shortcut key</li>
              <li>When the microphone turns purple, it's in command mode</li>
              <li>Speak one of the command phrases clearly</li>
              <li>The command will be executed automatically</li>
            </ol>
            <p className="text-xs text-muted-foreground mt-3">
              Note: You can customize voice commands and settings in the AI Settings page.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VoiceCommandHelp;
