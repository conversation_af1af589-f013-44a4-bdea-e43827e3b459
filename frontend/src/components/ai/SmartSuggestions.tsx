import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, X, ChevronRight, Check } from 'lucide-react';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import { useGlobalAIAssistant } from '@/providers/GlobalAIAssistantProvider';
import { useToast } from '@/components/ui/use-toast';

interface Suggestion {
  id: string;
  title: string;
  description: string;
  type: 'tip' | 'optimization' | 'feature' | 'workflow';
  context?: string;
  action?: string;
  actionHandler?: () => void;
  dismissed?: boolean;
  timestamp: Date;
}

interface SmartSuggestionsProps {
  className?: string;
  maxSuggestions?: number;
}

const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  className = '',
  maxSuggestions = 3
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(0);
  const { appState, addRecentAction } = useApplicationContext();
  const { openAssistant } = useGlobalAIAssistant();
  const { toast } = useToast();

  useEffect(() => {
    const generateAndSetSuggestions = () => {
      const newGeneratedSuggestions: Suggestion[] = [];
      const { currentPage, currentView, activeTracks, recentActions } = appState;

      // Suggest using keyboard shortcuts if user has been doing repetitive actions
      if (recentActions.length >= 3) {
        const actionTypes = recentActions.slice(0, 3).map(a => a.type);
        const hasDuplicates = new Set(actionTypes).size !== actionTypes.length;
        if (hasDuplicates) {
          newGeneratedSuggestions.push({
            id: `suggestion-shortcuts-${Date.now()}`,
            title: 'Use Keyboard Shortcuts',
            description: 'You seem to be performing repetitive actions. Try using keyboard shortcuts to speed up your workflow.',
            type: 'optimization',
            context: 'I notice you\'re performing repetitive actions. Here are some keyboard shortcuts that might help you work more efficiently.',
            action: 'View Shortcuts',
            timestamp: new Date()
          });
        }
      }

      // Suggest features based on current page
      if (currentPage === 'mix-timeline' || currentPage === 'mix-timeline-restructured') {
        if (activeTracks.length >= 2) {
          newGeneratedSuggestions.push({
            id: `suggestion-transitions-${Date.now()}`,
            title: 'Try Automatic Transitions',
            description: 'You have multiple tracks in your mix. Try using the AI to suggest transitions between them.',
            type: 'feature',
            context: 'I see you have multiple tracks in your mix. Would you like me to suggest some transition techniques between them?',
            action: 'Generate Transitions',
            timestamp: new Date()
          });
        }
        if (activeTracks.length >= 4) {
          newGeneratedSuggestions.push({
            id: `suggestion-flow-${Date.now()}`,
            title: 'Analyze Mix Flow',
            description: 'Your mix is taking shape! Analyze the energy flow to ensure a smooth progression.',
            type: 'workflow',
            context: 'I notice your mix has several tracks now. Would you like me to analyze the energy flow and suggest improvements?',
            action: 'Analyze Flow',
            timestamp: new Date()
          });
        }
      }

      // Suggest tips for new users
      const userHasUsedFeature = (featureType: string) => {
        return recentActions.some(a => a.type.includes(featureType));
      };
      if (!userHasUsedFeature('beatgrid') && (currentPage === 'mix-timeline' || currentPage.includes('demo'))) {
        newGeneratedSuggestions.push({
          id: `suggestion-beatgrid-${Date.now()}`,
          title: 'Set Beat Grids',
          description: 'Setting beat grids helps with precise mixing. Double-click on a waveform to add beat markers.',
          type: 'tip',
          context: 'I notice you haven\'t set up beat grids yet. Beat grids are essential for precise mixing. Would you like to learn how to set them up?',
          timestamp: new Date()
        });
      }

      setSuggestions(prevSuggestions => {
        const allConsideredSuggestions = [...prevSuggestions];
        newGeneratedSuggestions.forEach(newSugg => {
          // Add if ID is not already present (Date.now() makes IDs unique, so they are always "new")
          if (!allConsideredSuggestions.some(existing => existing.id === newSugg.id)) {
            allConsideredSuggestions.push(newSugg);
          }
        });

        const finalSuggestions = allConsideredSuggestions
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()) // Newest first for slicing
          .slice(0, maxSuggestions)
          .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime()); // Restore chronological order for display

        // Compare with previous suggestions to prevent unnecessary re-renders
        if (prevSuggestions.length === finalSuggestions.length) {
          const prevSortedIds = [...prevSuggestions].sort((a, b) => a.id.localeCompare(b.id)).map(s => s.id).join(',');
          const finalSortedIds = [...finalSuggestions].sort((a, b) => a.id.localeCompare(b.id)).map(s => s.id).join(',');
          if (prevSortedIds === finalSortedIds) {
            return prevSuggestions; // No change, return the same object reference
          }
        }
        // If suggestions changed, reset active index to avoid out of bounds
        if (finalSuggestions.length > 0 && activeSuggestionIndex >= finalSuggestions.length) {
          setActiveSuggestionIndex(0);
        } else if (finalSuggestions.length === 0) {
          setActiveSuggestionIndex(0);
        }
        return finalSuggestions;
      });
    };

    generateAndSetSuggestions(); // Initial run and on dependency change
    const interval = setInterval(generateAndSetSuggestions, 60000); // Periodic check

    return () => clearInterval(interval);
  }, [appState, maxSuggestions, addRecentAction, openAssistant, toast]); // Dependencies for useEffect

  // Handle suggestion action
  const handleSuggestionAction = (suggestion: Suggestion) => {
    // Open assistant with context if available
    if (suggestion.context) {
      openAssistant(suggestion.context);
    }

    // Execute custom action handler if available
    if (suggestion.actionHandler) {
      suggestion.actionHandler();
    }

    // Log the action
    addRecentAction({
      type: 'suggestion_accepted',
      description: `Accepted suggestion: ${suggestion.title}`
    });

    // Dismiss the suggestion
    handleDismissSuggestion(suggestion.id);

    // Show toast
    toast({
      title: 'Suggestion Applied',
      description: suggestion.title
    });
  };

  // Handle dismissing a suggestion
  const handleDismissSuggestion = (id: string) => {
    setSuggestions(prev => {
      const updatedSuggestions = prev.filter(s => s.id !== id);
      if (updatedSuggestions.length === 0) {
        setActiveSuggestionIndex(0);
      } else if (activeSuggestionIndex >= updatedSuggestions.length) {
        setActiveSuggestionIndex(Math.max(0, updatedSuggestions.length - 1));
      }
      return updatedSuggestions;
    });

    // Log the action
    addRecentAction({
      type: 'suggestion_dismissed',
      description: `Dismissed suggestion`
    });
  };

  // Rotate through suggestions
  const handleNextSuggestion = () => {
    setActiveSuggestionIndex(prev => {
      if (suggestions.length === 0) return 0;
      return (prev + 1) % suggestions.length;
    });
  };

  // If no suggestions, don't render anything
  if (suggestions.length === 0) {
    return null;
  }

  // Ensure activeSuggestion is valid if suggestions array is populated
  const activeSuggestion = suggestions[activeSuggestionIndex % suggestions.length];
  if (!activeSuggestion) { // Should not happen if suggestions.length > 0 and index is managed
    return null;
  }

  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2 pt-3 flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium flex items-center">
          <Lightbulb className="h-4 w-4 mr-2 text-yellow-500" />
          Smart Suggestion
          {suggestions.length > 1 && (
            <Badge variant="outline" className="ml-2 text-xs">
              {activeSuggestionIndex + 1}/{suggestions.length}
            </Badge>
          )}
        </CardTitle>
        <div className="flex gap-1">
          {suggestions.length > 1 && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-6 w-6" 
              onClick={handleNextSuggestion}
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          )}
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6" 
            onClick={() => handleDismissSuggestion(activeSuggestion.id)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <div className="space-y-2">
          <p className="text-sm font-medium">{activeSuggestion.title}</p>
          <p className="text-xs text-muted-foreground">{activeSuggestion.description}</p>
          {activeSuggestion.action && (
            <Button 
              variant="default" 
              size="sm" 
              className="mt-2 h-7 text-xs w-full"
              onClick={() => handleSuggestionAction(activeSuggestion)}
            >
              <Check className="h-3 w-3 mr-1" />
              {activeSuggestion.action}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SmartSuggestions;
