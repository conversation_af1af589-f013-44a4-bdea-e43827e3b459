import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings } from 'lucide-react';

interface ImageGenerationOptionsProps {
  width: number;
  height: number;
  style: string;
  onOptionsChange: (options: { width: number; height: number; style: string }) => void;
}

const ImageGenerationOptions: React.FC<ImageGenerationOptionsProps> = ({
  width,
  height,
  style,
  onOptionsChange
}) => {
  const [localWidth, setLocalWidth] = useState(width);
  const [localHeight, setLocalHeight] = useState(height);
  const [localStyle, setLocalStyle] = useState(style);
  const [isOpen, setIsOpen] = useState(false);

  const handleApply = () => {
    onOptionsChange({
      width: localWidth,
      height: localHeight,
      style: localStyle
    });
    setIsOpen(false);
  };

  const predefinedSizes = [
    { name: 'Square (1:1)', width: 1024, height: 1024 },
    { name: 'Portrait (2:3)', width: 768, height: 1152 },
    { name: 'Landscape (3:2)', width: 1152, height: 768 },
    { name: 'Widescreen (16:9)', width: 1280, height: 720 },
    { name: 'Mobile (9:16)', width: 720, height: 1280 }
  ];

  const styleOptions = [
    { value: '', label: 'Default' },
    { value: 'photorealistic', label: 'Photorealistic' },
    { value: 'anime', label: 'Anime' },
    { value: 'digital-art', label: 'Digital Art' },
    { value: 'oil-painting', label: 'Oil Painting' },
    { value: 'watercolor', label: 'Watercolor' },
    { value: 'sketch', label: 'Sketch' },
    { value: 'pixel-art', label: 'Pixel Art' },
    { value: 'comic-book', label: 'Comic Book' },
    { value: 'abstract', label: 'Abstract' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Image Generation Options</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="size-preset">Preset Sizes</Label>
            <Select
              onValueChange={(value) => {
                const size = predefinedSizes.find(s => s.name === value);
                if (size) {
                  setLocalWidth(size.width);
                  setLocalHeight(size.height);
                }
              }}
            >
              <SelectTrigger id="size-preset">
                <SelectValue placeholder="Select a size" />
              </SelectTrigger>
              <SelectContent>
                {predefinedSizes.map((size) => (
                  <SelectItem key={size.name} value={size.name}>
                    {size.name} ({size.width}x{size.height})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="width">Width</Label>
              <Input
                id="width"
                type="number"
                value={localWidth}
                onChange={(e) => setLocalWidth(parseInt(e.target.value) || 512)}
                min={256}
                max={2048}
                step={64}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="height">Height</Label>
              <Input
                id="height"
                type="number"
                value={localHeight}
                onChange={(e) => setLocalHeight(parseInt(e.target.value) || 512)}
                min={256}
                max={2048}
                step={64}
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="style">Style</Label>
            <Select
              value={localStyle}
              onValueChange={setLocalStyle}
            >
              <SelectTrigger id="style">
                <SelectValue placeholder="Select a style" />
              </SelectTrigger>
              <SelectContent>
                {styleOptions.map((style) => (
                  <SelectItem key={style.value} value={style.value}>
                    {style.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={handleApply}>Apply Options</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageGenerationOptions;
