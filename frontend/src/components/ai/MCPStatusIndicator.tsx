import React, { useState } from 'react';
import { useMCP } from '@/providers/MCPProvider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CircleCheck,
  CircleX,
  CircleDashed,
  Loader2,
  Info,
  RefreshCw,
  Database,
  Command,
  Clock,
  Server,
  HardDrive,
  BarChart
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface MCPStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

const MCPStatusIndicator: React.FC<MCPStatusIndicatorProps> = ({
  className = '',
  showDetails = false
}) => {
  const {
    isConnected,
    isConnecting,
    connectionError,
    availableTools,
    connect,
    disconnect,
    serverStatus,
    getServerStatus,
    toolUsageStats,
    getToolUsageStats
  } = useMCP();

  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle refresh button click
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Add a timeout to prevent hanging if the server is down
      const statusPromise = getServerStatus();
      const statsPromise = getToolUsageStats();

      // Use Promise.allSettled to continue even if one fails
      await Promise.allSettled([statusPromise, statsPromise]);
    } catch (error) {
      console.error("Error refreshing MCP status:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle connect button click
  const handleConnect = async () => {
    await connect();
  };

  // Handle disconnect button click
  const handleDisconnect = async () => {
    await disconnect();
  };

  // Render status icon
  const renderStatusIcon = () => {
    if (isConnecting) {
      return <Loader2 className="h-4 w-4 animate-spin text-yellow-500" />;
    } else if (isConnected) {
      return <CircleCheck className="h-4 w-4 text-green-500" />;
    } else if (connectionError) {
      return <CircleX className="h-4 w-4 text-red-500" />;
    } else {
      return <CircleDashed className="h-4 w-4 text-gray-500" />;
    }
  };

  // Render status text
  const renderStatusText = () => {
    if (isConnecting) {
      return 'Connecting...';
    } else if (isConnected) {
      return 'Connected';
    } else if (connectionError) {
      return 'Connection Error';
    } else {
      return 'Disconnected';
    }
  };

  // Render status color
  const getStatusColor = () => {
    if (isConnecting) {
      return 'bg-yellow-500';
    } else if (isConnected) {
      return 'bg-green-500';
    } else if (connectionError) {
      return 'bg-red-500';
    } else {
      return 'bg-gray-500';
    }
  };

  // Format uptime
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  // Format memory usage
  const formatMemoryUsage = (bytes: number) => {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else if (bytes < 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    } else {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    }
  };

  // Format last used time
  const formatLastUsed = (timestamp: string | null) => {
    if (!timestamp) {
      return 'Never';
    }

    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (err) {
      return 'Unknown';
    }
  };

  // Render simple status indicator
  if (!showDetails) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={`flex items-center gap-1.5 ${className}`}>
              {renderStatusIcon()}
              <span className="text-xs font-medium">MCP</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              <p className="font-semibold">Model Context Protocol</p>
              <p>Status: {renderStatusText()}</p>
              {isConnected && (
                <p>Tools: {availableTools.length} available</p>
              )}
              {connectionError && (
                <p className="text-red-500">{connectionError}</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Render detailed status indicator
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`h-8 gap-1.5 ${className}`}
        >
          <div className={`h-2 w-2 rounded-full ${getStatusColor()}`} />
          <span className="text-xs font-medium">MCP</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="p-4 pb-2">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold flex items-center gap-1.5">
              <Command className="h-4 w-4" />
              Model Context Protocol
            </h3>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleRefresh}
              disabled={isRefreshing || isConnecting}
            >
              <RefreshCw className={`h-3.5 w-3.5 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          <div className="mt-2 flex items-center gap-2">
            <Badge variant={isConnected ? 'default' : 'outline'} className="h-6 px-2 text-xs">
              {renderStatusIcon()}
              <span className="ml-1">{renderStatusText()}</span>
            </Badge>

            {isConnected ? (
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs"
                onClick={handleDisconnect}
                disabled={isConnecting}
              >
                Disconnect
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs"
                onClick={handleConnect}
                disabled={isConnecting}
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  'Connect'
                )}
              </Button>
            )}
          </div>

          {connectionError && (
            <div className="mt-2 text-xs text-red-500 flex items-start gap-1.5">
              <Info className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
              <span>{connectionError}</span>
            </div>
          )}
        </div>

        <Separator />

        <div className="p-4 pt-2 grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1.5">
            <Database className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Tools:</span>
            <span className="font-medium">{availableTools.length}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <BarChart className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Calls:</span>
            <span className="font-medium">{toolUsageStats.totalCalls}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <Server className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Version:</span>
            <span className="font-medium">{serverStatus.version || 'Unknown'}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <HardDrive className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Memory:</span>
            <span className="font-medium">{formatMemoryUsage(serverStatus.memoryUsage)}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Uptime:</span>
            <span className="font-medium">{formatUptime(serverStatus.uptime)}</span>
          </div>

          <div className="flex items-center gap-1.5">
            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
            <span className="text-muted-foreground">Last Used:</span>
            <span className="font-medium">{formatLastUsed(toolUsageStats.lastUsed)}</span>
          </div>
        </div>

        {isConnected && availableTools.length > 0 && (
          <>
            <Separator />
            <div className="p-4 pt-2">
              <h4 className="text-xs font-semibold mb-2">Tool Categories</h4>
              <div className="flex flex-wrap gap-1">
                {Array.from(new Set(availableTools.map(tool => tool.category))).map(category => (
                  <Badge key={category} variant="secondary" className="text-xs">
                    {category}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default MCPStatusIndicator;
