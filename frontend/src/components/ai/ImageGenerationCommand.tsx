import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Image as ImageIcon, Send } from 'lucide-react';
import { useMultiModalAI } from '@/providers/MultiModalAIProvider';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import ImageGenerationOptions from './ImageGenerationOptions';

interface ImageGenerationCommandProps {
  onGenerate: (imageMarkdown: string) => void;
  onCancel: () => void;
  initialPrompt?: string;
}

const ImageGenerationCommand: React.FC<ImageGenerationCommandProps> = ({
  onGenerate,
  onCancel,
  initialPrompt = ''
}) => {
  const [prompt, setPrompt] = useState(initialPrompt);
  const [isGenerating, setIsGenerating] = useState(false);
  const [width, setWidth] = useState(1024);
  const [height, setHeight] = useState(1024);
  const [style, setStyle] = useState('');

  const { generateImage } = useMultiModalAI();
  const { addRecentAction } = useApplicationContext();

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);

    try {
      // Generate the image
      const imageUrl = await generateImage(prompt, {
        width,
        height,
        style: style || undefined
      });

      // Create options text for display
      let optionsText = '';
      if (width !== 1024 || height !== 1024) {
        optionsText += `\n- Size: ${width}x${height}`;
      }
      if (style) {
        optionsText += `\n- Style: ${style}`;
      }

      // Create markdown with the image
      const imageMarkdown = `## Generated Image\n\n![${prompt}](${imageUrl})\n\n*Generated from prompt: "${prompt}"*${optionsText ? `\n\nOptions:${optionsText}` : ''}`;

      // Pass the markdown back to the parent
      onGenerate(imageMarkdown);

      // Log the action
      addRecentAction({
        type: 'image_generated',
        description: `Generated image: "${prompt.substring(0, 30)}${prompt.length > 30 ? '...' : ''}"`
      });
    } catch (error) {
      // Create error markdown
      const errorMarkdown = `Error generating image: ${error instanceof Error ? error.message : 'Unknown error'}`;
      onGenerate(errorMarkdown);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOptionsChange = (options: { width: number; height: number; style: string }) => {
    setWidth(options.width);
    setHeight(options.height);
    setStyle(options.style);
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          <ImageIcon className="mr-2 h-5 w-5" />
          Generate Image
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {width}x{height} {style ? `• Style: ${style}` : ''}
            </div>
            <ImageGenerationOptions
              width={width}
              height={height}
              style={style}
              onOptionsChange={handleOptionsChange}
            />
          </div>
          <Textarea
            placeholder="Describe the image you want to generate..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="min-h-[100px] resize-none"
            disabled={isGenerating}
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel} disabled={isGenerating}>
          Cancel
        </Button>
        <Button onClick={handleGenerate} disabled={isGenerating || !prompt.trim()}>
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              Generate
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ImageGenerationCommand;
