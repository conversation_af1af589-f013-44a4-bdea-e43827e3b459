import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { MoodAnalysis } from '@/types/multimodal';

interface EnhancedMoodAnalysisProps {
  mood: MoodAnalysis;
  className?: string;
}

const EnhancedMoodAnalysis: React.FC<EnhancedMoodAnalysisProps> = ({
  mood,
  className = ''
}) => {
  // Get color based on mood
  const getMoodColor = (mood: string) => {
    const moodColorMap: Record<string, string> = {
      'happy': 'bg-yellow-500',
      'sad': 'bg-blue-500',
      'energetic': 'bg-red-500',
      'calm': 'bg-green-500',
      'aggressive': 'bg-purple-500',
      'romantic': 'bg-pink-500',
      'melancholic': 'bg-indigo-500',
      'nostalgic': 'bg-amber-500',
      'dark': 'bg-slate-700',
      'uplifting': 'bg-sky-500',
      'mysterious': 'bg-violet-500',
      'dreamy': 'bg-cyan-400',
      'tense': 'bg-rose-600',
      'playful': 'bg-emerald-400',
      'ethereal': 'bg-teal-300',
      'dramatic': 'bg-red-700',
      'peaceful': 'bg-blue-300',
      'intense': 'bg-orange-600',
      'joyful': 'bg-yellow-400',
      'somber': 'bg-gray-600'
    };
    
    // Find the closest mood match
    const moodLower = mood.toLowerCase();
    const exactMatch = moodColorMap[moodLower];
    
    if (exactMatch) return exactMatch;
    
    // Try to find a partial match
    for (const [key, value] of Object.entries(moodColorMap)) {
      if (moodLower.includes(key) || key.includes(moodLower)) {
        return value;
      }
    }
    
    return 'bg-gray-500'; // Default color
  };
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Mood Analysis</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3 space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Primary Mood</h3>
            <Badge className={`${getMoodColor(mood.primary_mood).replace('bg-', 'bg-opacity-20 text-')}`}>
              {mood.primary_mood}
            </Badge>
          </div>
          
          <Progress 
            value={mood.intensity * 10} 
            className={`h-2 ${getMoodColor(mood.primary_mood)}`} 
          />
          <p className="text-xs text-right">Intensity: {mood.intensity}/10</p>
        </div>
        
        {mood.secondary_moods && mood.secondary_moods.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Secondary Moods</h3>
            <div className="flex flex-wrap gap-1">
              {mood.secondary_moods.map((secondaryMood, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className={getMoodColor(secondaryMood).replace('bg-', 'text-')}
                >
                  {secondaryMood}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        <div className="text-xs text-muted-foreground mt-2">
          <p>Mood intensity affects energy levels, transitions, and overall mix flow.</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedMoodAnalysis;
