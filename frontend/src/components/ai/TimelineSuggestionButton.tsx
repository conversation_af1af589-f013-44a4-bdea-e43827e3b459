import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import TransitionSuggesterDialog from './TransitionSuggesterDialog';

interface Track {
  id: string;
  title: string;
  artist: string;
  duration: number;
  bpm?: number;
  key?: string;
  energy?: number;
  genre?: string;
  [key: string]: any;
}

interface Transition {
  fromTrackId: string;
  toTrackId: string;
  duration: number;
  startPoint: number;
  type?: string;
  notes?: string;
  [key: string]: any;
}

interface TimelineSuggestionButtonProps {
  fromTrack: Track;
  toTrack: Track;
  currentTransition?: Transition;
  onSuggestionApply?: (suggestion: any) => void;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
}

/**
 * TimelineSuggestionButton - Replacement for the deleted timeline component
 * Uses the existing TransitionSuggesterDialog for AI suggestions
 */
const TimelineSuggestionButton: React.FC<TimelineSuggestionButtonProps> = ({
  fromTrack,
  toTrack,
  currentTransition,
  onSuggestionApply,
  className = '',
  size = 'sm',
  variant = 'outline'
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSuggestionApply = async (suggestion: any) => {
    if (!onSuggestionApply) return;

    setIsLoading(true);
    try {
      await onSuggestionApply(suggestion);
    } catch (error) {
      console.error('Error applying suggestion:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const trigger = (
    <Button
      variant={variant}
      size={size}
      className={`flex items-center gap-1 ${className}`}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-3 w-3 animate-spin" />
      ) : (
        <Sparkles className="h-3 w-3" />
      )}
      <span className="hidden sm:inline">AI</span>
    </Button>
  );

  return (
    <TransitionSuggesterDialog
      fromTrack={fromTrack}
      toTrack={toTrack}
      currentTransition={currentTransition}
      onApplySuggestion={handleSuggestionApply}
      trigger={trigger}
    />
  );
};

export default TimelineSuggestionButton;
