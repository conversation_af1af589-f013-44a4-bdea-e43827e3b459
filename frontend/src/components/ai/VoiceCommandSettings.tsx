import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Command, Mic, RefreshCw, HelpCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useVoiceCommands, CommandCategory } from '@/providers/VoiceCommandProvider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface VoiceCommandSettingsProps {
  className?: string;
}

const VoiceCommandSettings: React.FC<VoiceCommandSettingsProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const voiceCommands = useVoiceCommands();
  const [activeTab, setActiveTab] = useState<string>('general');
  
  // Get commands grouped by category
  const commandsByCategory = voiceCommands.commands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = [];
    }
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, typeof voiceCommands.commands>);
  
  // Handle toggling all commands
  const handleToggleAllCommands = (enabled: boolean) => {
    voiceCommands.setCommandsEnabled(enabled);
    
    toast({
      title: enabled ? 'Voice Commands Enabled' : 'Voice Commands Disabled',
      description: enabled 
        ? 'You can now use voice commands to control the application' 
        : 'Voice commands have been disabled',
    });
  };
  
  // Handle toggling a specific command
  const handleToggleCommand = (commandId: string, enabled: boolean) => {
    voiceCommands.toggleCommand(commandId, enabled);
  };
  
  // Get category name for display
  const getCategoryDisplayName = (category: string): string => {
    switch (category) {
      case CommandCategory.NAVIGATION:
        return 'Navigation';
      case CommandCategory.PLAYBACK:
        return 'Playback';
      case CommandCategory.INTERFACE:
        return 'Interface';
      case CommandCategory.SETTINGS:
        return 'Settings';
      case CommandCategory.GENERAL:
        return 'General';
      default:
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };
  
  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case CommandCategory.NAVIGATION:
        return <Command className="h-4 w-4 mr-2" />;
      case CommandCategory.PLAYBACK:
        return <Command className="h-4 w-4 mr-2" />;
      case CommandCategory.INTERFACE:
        return <Command className="h-4 w-4 mr-2" />;
      case CommandCategory.SETTINGS:
        return <Command className="h-4 w-4 mr-2" />;
      case CommandCategory.GENERAL:
        return <Command className="h-4 w-4 mr-2" />;
      default:
        return <Command className="h-4 w-4 mr-2" />;
    }
  };
  
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Command className="h-5 w-5 mr-2" />
          Voice Command Settings
        </CardTitle>
        <CardDescription>
          Configure voice commands for controlling the application
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enable/Disable Voice Commands */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base">Enable Voice Commands</Label>
            <p className="text-sm text-muted-foreground">
              Enable or disable all voice commands
            </p>
          </div>
          <Switch 
            checked={voiceCommands.settings.enabled}
            onCheckedChange={handleToggleAllCommands}
          />
        </div>
        
        {/* Confirmation setting */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base">Require Confirmation</Label>
            <p className="text-sm text-muted-foreground">
              Require confirmation before executing commands
            </p>
          </div>
          <Switch 
            checked={voiceCommands.settings.requireConfirmation}
            onCheckedChange={(checked) => {
              voiceCommands.settings.requireConfirmation = checked;
              // This would need to be implemented in the provider
            }}
            disabled={!voiceCommands.settings.enabled}
          />
        </div>
        
        {/* Sensitivity slider */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="command-sensitivity">Recognition Sensitivity</Label>
            <span className="text-sm text-muted-foreground">{(voiceCommands.settings.sensitivity * 100).toFixed(0)}%</span>
          </div>
          <Slider
            id="command-sensitivity"
            min={0.1}
            max={1}
            step={0.1}
            value={[voiceCommands.settings.sensitivity]}
            onValueChange={(value) => {
              // This would need to be implemented in the provider
              voiceCommands.settings.sensitivity = value[0];
            }}
            disabled={!voiceCommands.settings.enabled}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>More Precise</span>
            <span>More Flexible</span>
          </div>
        </div>
        
        <Separator />
        
        {/* Command categories */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="navigation">Navigation</TabsTrigger>
            <TabsTrigger value="playback">Playback</TabsTrigger>
            <TabsTrigger value="interface">Interface</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          
          {Object.entries(commandsByCategory).map(([category, commands]) => (
            <TabsContent key={category} value={category} className="space-y-4">
              <Alert>
                <HelpCircle className="h-4 w-4" />
                <AlertTitle>{getCategoryDisplayName(category)} Commands</AlertTitle>
                <AlertDescription>
                  These commands allow you to control {category.toLowerCase()} features with your voice
                </AlertDescription>
              </Alert>
              
              <Accordion type="multiple" className="w-full">
                {commands.map(command => (
                  <AccordionItem key={command.id} value={command.id}>
                    <div className="flex items-center justify-between">
                      <AccordionTrigger className="flex-1">
                        {command.name}
                      </AccordionTrigger>
                      <Switch 
                        checked={command.enabled}
                        onCheckedChange={(checked) => handleToggleCommand(command.id, checked)}
                        disabled={!voiceCommands.settings.enabled}
                        className="mr-4"
                      />
                    </div>
                    <AccordionContent>
                      <div className="space-y-2 p-2">
                        <p className="text-sm text-muted-foreground">{command.description}</p>
                        <div className="mt-2">
                          <Label className="text-xs">Trigger phrases:</Label>
                          <ul className="mt-1 space-y-1">
                            {command.phrases.map((phrase, index) => (
                              <li key={index} className="text-xs bg-muted p-1 rounded">"{phrase}"</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
      <CardFooter>
        <Button 
          variant="outline" 
          onClick={() => {
            // Reset to defaults
            toast({
              title: 'Settings Reset',
              description: 'Voice command settings have been reset to defaults',
            });
          }}
          className="w-full"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </CardFooter>
    </Card>
  );
};

export default VoiceCommandSettings;
