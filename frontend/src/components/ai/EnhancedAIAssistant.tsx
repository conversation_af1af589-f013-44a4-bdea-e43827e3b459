import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Loader2,
  Send,
  Bot,
  User,
  Sparkles,
  RefreshCw,
  Paperclip,
  Image as ImageIcon,
  Music,
  X,
  Maximize2,
  MessageSquare,
  Settings,
  ChevronRight,
  Command
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAI } from '@/providers/AIProvider';
import { useMultiModalAI } from '@/providers/MultiModalAIProvider';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import RichResponseRenderer from './RichResponseRenderer';
import VoiceInteraction from './VoiceInteraction';
import VoiceCommandHelp from './VoiceCommandHelp';
import SlashCommands from './SlashCommands';
import SmartSuggestions from './SmartSuggestions';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import AIFeedback from './AIFeedback';
import MCPStatusIndicator from './MCPStatusIndicator';

// Define types for messages and media content
interface MediaContent {
  type: 'image' | 'audio';
  url?: string;
  file?: File;
  data?: string; // Base64 data
  analysis?: any;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  media?: MediaContent;
  isRichResponse?: boolean;
  isSpeaking?: boolean;
}

interface EnhancedAIAssistantProps {
  initialContext?: string;
  placeholder?: string;
  title?: string;
  description?: string;
  showSuggestions?: boolean;
  className?: string;
}

const EnhancedAIAssistant: React.FC<EnhancedAIAssistantProps> = ({
  initialContext,
  placeholder = "Ask a question about DJing, music production, or the application...",
  title = "AI Assistant",
  description = "Ask questions about DJing, music production, or the application",
  showSuggestions = true,
  className = ''
}) => {
  const { toast } = useToast();
  const { answerQuestion, isGenerating } = useAI();
  const {
    analyzeImageFile,
    analyzeAudioFile,
    analyzeAlbumArtworkFile,
    analyzeTrackFeaturesFile,
    isProcessing
  } = useMultiModalAI();
  const { getContextForAssistant, addRecentAction } = useApplicationContext();

  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [fileType, setFileType] = useState<'image' | 'audio' | null>(null);
  const [showFileUploadOptions, setShowFileUploadOptions] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('chat');
  const [textToSpeak, setTextToSpeak] = useState<string | undefined>(undefined);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize with welcome message
  useEffect(() => {
    setMessages([
      {
        id: 'welcome',
        role: 'assistant',
        content: "Hi! I'm your DJ Mix Constructor AI assistant. I can help with questions about DJing, music production, or how to use the application. What would you like to know?",
        timestamp: new Date()
      }
    ]);
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (isGenerating || !input.trim()) return;

    // Create user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    // Add user message to chat
    setMessages(prev => [...prev, userMessage]);
    setInput('');

    try {
      // Get recent conversation context (last 5 messages)
      const recentMessages = messages.slice(-5).map(msg =>
        `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`
      ).join('\n\n');

      // Get application context
      const appContext = getContextForAssistant();

      // Combine all context information
      let context = '';

      if (initialContext) {
        context += `${initialContext}\n\n`;
      }

      context += `Application Context:\n${appContext}\n\n`;
      context += `Recent conversation:\n${recentMessages}`;

      // Get AI response
      const response = await answerQuestion(input, context);

      // Create assistant message
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: response,
        timestamp: new Date(),
        isRichResponse: true
      };

      // Add assistant message to chat
      setMessages(prev => [...prev, assistantMessage]);

      // Set text to speak
      setTextToSpeak(response);

      // Log the action
      addRecentAction({
        type: 'ai_assistant_used',
        description: `Asked AI Assistant: "${input.substring(0, 30)}${input.length > 30 ? '...' : ''}"`
      });
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to get response',
        variant: 'destructive',
      });
    }
  };

  // Handle key press in textarea
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Clear conversation
  const handleClearConversation = () => {
    setMessages([
      {
        id: 'welcome',
        role: 'assistant',
        content: "Hi! I'm your DJ Mix Constructor AI assistant. I can help with questions about DJing, music production, or how to use the application. What would you like to know?",
        timestamp: new Date()
      }
    ]);
    clearFileUpload();
    setTextToSpeak(undefined);
  };

  // File upload functions
  const handleFileUploadClick = () => {
    setShowFileUploadOptions(!showFileUploadOptions);
  };

  const handleFileTypeSelect = (type: 'image' | 'audio') => {
    setFileType(type);
    if (fileInputRef.current) {
      fileInputRef.current.accept = type === 'image' ? 'image/*' : 'audio/*';
      fileInputRef.current.click();
    }
    setShowFileUploadOptions(false);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size
    const maxSize = fileType === 'image' ? 10 * 1024 * 1024 : 20 * 1024 * 1024; // 10MB for images, 20MB for audio
    if (file.size > maxSize) {
      const sizeInMB = Math.round(maxSize / (1024 * 1024));
      toast({
        title: 'File too large',
        description: `File size exceeds ${sizeInMB}MB limit. Please select a smaller file.`,
        variant: 'destructive',
      });
      return;
    }

    // Validate file type
    if (fileType === 'image' && !file.type.startsWith('image/')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select an image file (JPEG, PNG, etc.)',
        variant: 'destructive',
      });
      return;
    } else if (fileType === 'audio' && !file.type.startsWith('audio/')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select an audio file (MP3, WAV, etc.)',
        variant: 'destructive',
      });
      return;
    }

    setUploadedFile(file);

    // Create preview for the file
    if (fileType === 'image') {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFilePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else if (fileType === 'audio') {
      // For audio, we'll just show the file name
      setFilePreview(URL.createObjectURL(file));
    }
  };

  const clearFileUpload = () => {
    setUploadedFile(null);
    setFilePreview(null);
    setFileType(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle sending a file message
  const handleSendFileMessage = async () => {
    if (!uploadedFile || !fileType) return;

    // Create a message with the file
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input || `Analyzing ${fileType}: ${uploadedFile.name}`,
      timestamp: new Date(),
      media: {
        type: fileType,
        file: uploadedFile,
        data: filePreview || undefined
      }
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    try {
      let analysis: Record<string, any> | null = null;

      // Process the file based on its type
      if (fileType === 'image') {
        try {
          if (input.toLowerCase().includes('album') || input.toLowerCase().includes('artwork')) {
            analysis = await analyzeAlbumArtworkFile(uploadedFile);
          } else {
            analysis = await analyzeImageFile(uploadedFile, input || undefined);
          }
        } catch (imageError: any) {
          // If server returns 500 error, provide a fallback analysis
          if (imageError.status === 500) {
            analysis = {
              message: "I couldn't process this image through our AI service, but I can see it's an image file.",
              file_info: {
                name: uploadedFile.name,
                size: `${(uploadedFile.size / 1024).toFixed(1)} KB`,
                type: uploadedFile.type
              },
              suggestion: "You can try a different image or ask me a question about it directly."
            };
          } else {
            // Re-throw other errors to be caught by the outer catch block
            throw imageError;
          }
        }
      } else if (fileType === 'audio') {
        try {
          if (input.toLowerCase().includes('track') || input.toLowerCase().includes('features')) {
            analysis = await analyzeTrackFeaturesFile(uploadedFile);
          } else {
            analysis = await analyzeAudioFile(uploadedFile, input || undefined);
          }
        } catch (audioError: any) {
          // If server returns 500 error, provide a fallback analysis
          if (audioError.status === 500) {
            analysis = {
              message: "I couldn't process this audio file through our AI service, but I can see it's an audio file.",
              file_info: {
                name: uploadedFile.name,
                size: `${(uploadedFile.size / 1024).toFixed(1)} KB`,
                type: uploadedFile.type
              },
              suggestion: "You can try a different audio file or ask me a question about it directly."
            };
          } else {
            // Re-throw other errors to be caught by the outer catch block
            throw audioError;
          }
        }
      }

      // Format the analysis as markdown
      let responseContent = '';

      if (analysis) {
        responseContent = `## Analysis of ${uploadedFile.name}\n\n`;

        if (typeof analysis === 'object') {
          // Format each key in the analysis
          Object.entries(analysis).forEach(([key, value]) => {
            const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            if (typeof value === 'object' && value !== null) {
              responseContent += `### ${formattedKey}\n\n`;
              responseContent += '```json\n' + JSON.stringify(value, null, 2) + '\n```\n\n';
            } else {
              responseContent += `**${formattedKey}**: ${value}\n\n`;
            }
          });
        } else if (typeof analysis === 'string') {
          responseContent += analysis;
        } else if (analysis !== null) {
          responseContent += String(analysis);
        }
      } else {
        responseContent = `I've analyzed the ${fileType}, but couldn't extract meaningful information. Would you like me to try a different approach?`;
      }

      // Add assistant message with the analysis
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: responseContent,
        timestamp: new Date(),
        media: {
          type: fileType,
          data: filePreview || undefined,
          analysis: analysis
        },
        isRichResponse: true
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Log the action
      addRecentAction({
        type: 'ai_assistant_file_analysis',
        description: `Analyzed ${fileType} file: ${uploadedFile.name}`
      });
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || `Failed to analyze ${fileType}`,
        variant: 'destructive',
      });

      // Add error message
      const errorMessage: Message = {
        id: `assistant-error-${Date.now()}`,
        role: 'assistant',
        content: `I'm sorry, I encountered an error while analyzing your ${fileType}. Please try again or try a different file.`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      clearFileUpload();
    }
  };

  // Handle slash command selection
  const handleCommandSelect = (command: string, replace: boolean) => {
    if (replace) {
      setInput(command);
    } else {
      setInput(prev => prev + command);
    }

    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  // Handle slash command execution
  const handleCommandExecute = (command: any, input?: string) => {
    if (command.id === 'clear') {
      handleClearConversation();
      return;
    }

    if (input) {
      // Check if this is a loading message for image generation
      if (command.id === 'generate-loading') {
        // Create assistant message with the loading state
        const loadingMessage: Message = {
          id: `assistant-command-loading-${Date.now()}`,
          role: 'assistant',
          content: input,
          timestamp: new Date(),
          isRichResponse: true
        };

        setMessages(prev => [...prev, loadingMessage]);
        return;
      }

      // Check if this is a result message for image generation
      if (command.id === 'generate-result') {
        // Replace the loading message if it exists
        setMessages(prev => {
          const loadingIndex = prev.findIndex(msg =>
            msg.role === 'assistant' && msg.id.includes('assistant-command-loading'));

          if (loadingIndex !== -1) {
            // Replace the loading message
            const newMessages = [...prev];
            newMessages[loadingIndex] = {
              id: `assistant-command-${Date.now()}`,
              role: 'assistant',
              content: input,
              timestamp: new Date(),
              isRichResponse: true
            };
            return newMessages;
          } else {
            // Add a new message
            return [...prev, {
              id: `assistant-command-${Date.now()}`,
              role: 'assistant',
              content: input,
              timestamp: new Date(),
              isRichResponse: true
            }];
          }
        });
        return;
      }

      // For other commands, create a new assistant message
      const assistantMessage: Message = {
        id: `assistant-command-${Date.now()}`,
        role: 'assistant',
        content: input,
        timestamp: new Date(),
        isRichResponse: true
      };

      setMessages(prev => [...prev, assistantMessage]);
    }
  };

  // Handle speech result
  const handleSpeechResult = (text: string) => {
    setInput(text);

    // Auto-send if the text is a complete question
    if (text.trim().endsWith('?')) {
      setTimeout(() => {
        handleSendMessage();
      }, 500);
    }
  };

  // Render a message
  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user';

    return (
      <div
        key={message.id}
        className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-5`}
      >
        <div className={`flex ${isUser ? 'flex-row-reverse' : 'flex-row'} max-w-[85%] items-end`}>
          <Avatar className={`h-8 w-8 ${isUser ? 'ml-2.5' : 'mr-2.5'} flex-shrink-0`}>
            {isUser ? (
              <>
                <AvatarFallback>U</AvatarFallback>
                <AvatarImage src="/user-avatar.png" />
              </>
            ) : (
              <>
                <AvatarFallback>AI</AvatarFallback>
                <AvatarImage src="/ai-avatar.png" />
              </>
            )}
          </Avatar>

          <div
            className={`p-4 ${
              isUser
                ? 'bg-primary text-primary-foreground rounded-2xl rounded-tr-sm'
                : 'bg-muted rounded-2xl rounded-tl-sm'
            }`}
          >
            {/* Render media content if present */}
            {message.media && (
              <div className="mb-3">
                {message.media.type === 'image' && message.media.data && (
                  <div className="relative">
                    <img
                      src={message.media.data}
                      alt="Uploaded image"
                      className="rounded-md max-h-[200px] w-auto object-contain mb-2"
                    />
                    <Button
                      variant="secondary"
                      size="icon"
                      className="absolute top-2 right-2 h-7 w-7 bg-background/80 backdrop-blur-sm"
                      onClick={() => window.open(message.media?.data, '_blank')}
                    >
                      <Maximize2 className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                )}
                {message.media.type === 'audio' && message.media.data && (
                  <div className="mb-2">
                    <audio
                      controls
                      src={message.media.data}
                      className="w-full max-w-[300px]"
                    />
                  </div>
                )}
                <div className="text-xs flex items-center mb-1">
                  {message.media.type === 'image' ? (
                    <ImageIcon className="h-3.5 w-3.5 mr-1.5" />
                  ) : (
                    <Music className="h-3.5 w-3.5 mr-1.5" />
                  )}
                  <span>
                    {message.media.file?.name || 'Media file'}
                  </span>
                </div>
              </div>
            )}

            {/* Render text content */}
            {isUser ? (
              <p className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</p>
            ) : (
              <div className="prose dark:prose-invert prose-sm max-w-none">
                {message.isRichResponse ? (
                  <RichResponseRenderer content={message.content} />
                ) : (
                  <p className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</p>
                )}
              </div>
            )}

            <div className="flex justify-between items-center mt-2.5 pt-1.5 border-t border-primary-foreground/10">
              <div
                className={`text-xs ${
                  isUser ? 'text-primary-foreground/70' : 'text-muted-foreground'
                }`}
              >
                {message.timestamp.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>

              <div className="flex items-center gap-2">
                {/* Add speak button for assistant messages */}
                {!isUser && (
                  <VoiceInteraction
                    textToSpeak={message.content}
                    showSpeakButton={true}
                    mode="output"
                    size="sm"
                    onSpeakStart={() => {
                      // Mark this message as speaking
                      setMessages(prev =>
                        prev.map(msg =>
                          msg.id === message.id
                            ? { ...msg, isSpeaking: true }
                            : { ...msg, isSpeaking: false }
                        )
                      );
                    }}
                    onSpeakEnd={() => {
                      // Mark this message as not speaking
                      setMessages(prev =>
                        prev.map(msg =>
                          msg.id === message.id
                            ? { ...msg, isSpeaking: false }
                            : msg
                        )
                      );
                    }}
                  />
                )}

                {!isUser && (
                  <AIFeedback
                    featureId="ai_assistant"
                    featureType="ai_assistant"
                    contentId={message.id}
                    contentType="assistant_message"
                    compact={true}
                    className={`${
                      isUser ? 'text-primary-foreground/70' : 'text-muted-foreground'
                    }`}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={`w-full h-full flex flex-col overflow-hidden ${className}`}>
      <CardHeader className="pb-3 pt-4 px-4">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center text-lg">
              <Bot className="mr-2 h-5 w-5 text-primary" />
              {title}
            </CardTitle>
            <CardDescription className="text-xs mt-1">{description}</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <MCPStatusIndicator />
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 overflow-visible">
        <div className="h-full flex flex-col">
          <div className="px-4 pb-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-3">
                <TabsTrigger value="chat" className="text-xs py-1.5">
                  <MessageSquare className="h-3 w-3 mr-1.5" />
                  Chat
                </TabsTrigger>
                <TabsTrigger value="suggestions" className="text-xs py-1.5">
                  <Sparkles className="h-3 w-3 mr-1.5" />
                  Suggestions
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden px-0">
                <TabsContent value="chat" className="m-0 h-full">
                  <ScrollArea className="h-[400px] pr-2 overflow-y-auto">
                    <div className="py-2 space-y-4">
                      {messages.map(renderMessage)}
                      <div ref={messagesEndRef} />
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="suggestions" className="m-0 h-full">
                  <ScrollArea className="h-[400px] pr-2 overflow-y-auto">
                    <div className="py-2 space-y-6">
                      <SmartSuggestions />
                      <Separator className="my-4" />
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <Button variant="outline" className="flex justify-start h-auto py-2" onClick={() => {
                          setInput("What are the best transition techniques for house music?");
                          setActiveTab("chat");
                        }}>
                          <span className="mr-2 text-sm">House Transitions</span>
                          <ChevronRight className="h-4 w-4 ml-auto" />
                        </Button>
                        <Button variant="outline" className="flex justify-start h-auto py-2" onClick={() => {
                          setInput("How do I use EQ mixing for smoother transitions?");
                          setActiveTab("chat");
                        }}>
                          <span className="mr-2 text-sm">EQ Mixing</span>
                          <ChevronRight className="h-4 w-4 ml-auto" />
                        </Button>
                        <Button variant="outline" className="flex justify-start h-auto py-2" onClick={() => {
                          setInput("What's the best way to organize my music library?");
                          setActiveTab("chat");
                        }}>
                          <span className="mr-2 text-sm">Music Organization</span>
                          <ChevronRight className="h-4 w-4 ml-auto" />
                        </Button>
                        <Button variant="outline" className="flex justify-start h-auto py-2" onClick={() => {
                          setInput("How do I create a good energy flow in my mix?");
                          setActiveTab("chat");
                        }}>
                          <span className="mr-2 text-sm">Energy Flow</span>
                          <ChevronRight className="h-4 w-4 ml-auto" />
                        </Button>
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex flex-col space-y-3 p-4 pt-3 border-t bg-card sticky bottom-0">
        {/* File preview */}
        {uploadedFile && filePreview && (
          <div className="w-full p-3 border rounded-md mb-2 bg-background">
            <div className="flex items-center">
              {fileType === 'image' && (
                <img
                  src={filePreview}
                  alt="Preview"
                  className="h-16 w-auto object-contain mr-3"
                />
              )}
              {fileType === 'audio' && (
                <div className="mr-3">
                  <Music className="h-10 w-10 text-primary" />
                </div>
              )}
              <div className="flex-1">
                <p className="text-sm font-medium truncate">{uploadedFile.name}</p>
                <p className="text-xs text-muted-foreground">
                  {(uploadedFile.size / 1024).toFixed(1)} KB
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={clearFileUpload}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="mt-3 flex justify-end">
              <Button
                size="sm"
                onClick={handleSendFileMessage}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                ) : (
                  <Send className="h-3 w-3 mr-2" />
                )}
                Analyze {fileType}
              </Button>
            </div>
          </div>
        )}

        {/* File upload options */}
        {showFileUploadOptions && !uploadedFile && (
          <div className="w-full p-3 border rounded-md mb-2 flex justify-center space-x-3 bg-background">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleFileTypeSelect('image')}
              className="h-9"
            >
              <ImageIcon className="h-4 w-4 mr-2" />
              Upload Image
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleFileTypeSelect('audio')}
              className="h-9"
            >
              <Music className="h-4 w-4 mr-2" />
              Upload Audio
            </Button>
          </div>
        )}

        {/* Slash commands */}
        <SlashCommands
          inputValue={input}
          onCommandSelect={handleCommandSelect}
          onCommandExecute={handleCommandExecute}
        />

        {/* Input area - completely redesigned for better cohesion */}
        <div className="w-full mt-4">
          {/* Hidden file input */}
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileChange}
          />

          {/* Main input container */}
          <div className="flex flex-col gap-2">
            {/* Input field with integrated buttons */}
            <div className="flex items-center gap-2 w-full">
              {/* Left side - attachment button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleFileUploadClick}
                className="h-10 w-10 rounded-full bg-muted/30 text-muted-foreground hover:text-foreground hover:bg-muted/50"
                disabled={isGenerating || isProcessing}
              >
                <Paperclip className="h-5 w-5" />
              </Button>

              {/* Center - input field */}
              <div className="flex-1 relative">
                <Textarea
                  placeholder={placeholder}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="w-full min-h-[50px] max-h-[120px] resize-none py-2.5 px-4 rounded-full bg-muted/30 border-0 focus-visible:ring-1 focus-visible:ring-primary/30"
                  disabled={isGenerating || isProcessing}
                  ref={textareaRef}
                />

                {/* Voice interaction positioned inside input field */}
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  <VoiceInteraction
                    onSpeechResult={handleSpeechResult}
                    textToSpeak={textToSpeak}
                    className="flex-shrink-0"
                    mode="input"
                    size="sm"
                  />
                </div>
              </div>

              {/* Right side - send button */}
              <Button
                onClick={handleSendMessage}
                disabled={(isGenerating || isProcessing || !input.trim()) && !uploadedFile}
                size="icon"
                className="h-10 w-10 rounded-full bg-primary hover:bg-primary/90"
              >
                {isGenerating || isProcessing ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
              </Button>
            </div>

            {/* Clear conversation button and voice commands help - visually connected */}
            <div className="flex justify-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearConversation}
                className="text-xs text-muted-foreground hover:text-foreground transition-colors"
              >
                <RefreshCw className="h-3 w-3 mr-1.5" />
                Clear conversation
              </Button>

              <VoiceCommandHelp
                trigger={
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <Command className="h-3 w-3 mr-1.5" />
                    Voice commands
                  </Button>
                }
              />
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
};

export default EnhancedAIAssistant;
