import React from 'react';
import { useMCP } from '@/providers/MCPProvider';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  Music,
  AudioWaveform,
  Disc3,
  Bar<PERSON>hart,
  Shuffle,
  Layers,
  Database
} from 'lucide-react';

interface MCPToolUsageIndicatorProps {
  toolName: string;
  className?: string;
}

const MCPToolUsageIndicator: React.FC<MCPToolUsageIndicatorProps> = ({
  toolName,
  className = ''
}) => {
  const { availableTools } = useMCP();

  // Find the tool in the available tools
  const tool = availableTools.find(t => t.name === toolName);

  if (!tool) {
    return null;
  }

  // Get tool category
  const category = tool.category || 'Unknown';

  // Get icon based on category
  const getIcon = () => {
    switch (category.toLowerCase()) {
      case 'music library':
        return <Database className="h-3 w-3" />;
      case 'audio analysis':
        return <AudioWaveform className="h-3 w-3" />;
      case 'mix creation':
        return <Disc3 className="h-3 w-3" />;
      case 'transition':
        return <Shuffle className="h-3 w-3" />;
      case 'analytics':
        return <BarChart className="h-3 w-3" />;
      case 'collection':
        return <Layers className="h-3 w-3" />;
      case 'music':
        return <Music className="h-3 w-3" />;
      default:
        return <Command className="h-3 w-3" />;
    }
  };

  // Get badge variant based on category
  const getBadgeVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    switch (category.toLowerCase()) {
      case 'music library':
        return 'default';
      case 'audio analysis':
        return 'secondary';
      case 'mix creation':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant={getBadgeVariant()}
            className={`h-5 px-1.5 text-[10px] gap-1 ${className}`}
          >
            {getIcon()}
            <span>{toolName}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="top" align="center">
          <div className="text-xs">
            <p className="font-semibold">{toolName}</p>
            <p className="text-muted-foreground">{tool.description}</p>
            <p className="mt-1 text-[10px] text-muted-foreground">Category: {category}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default MCPToolUsageIndicator;
