import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Upload, Image as ImageIcon } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useMultiModalAI } from '@/providers/MultiModalAIProvider';
import { EnhancedAlbumArtworkAnalysis } from '@/types/multimodal';
import EnhancedAlbumArtworkAnalysisComponent from './EnhancedAlbumArtworkAnalysis';

interface AlbumArtworkAnalysisDemoProps {
  className?: string;
}

const AlbumArtworkAnalysisDemo: React.FC<AlbumArtworkAnalysisDemoProps> = ({
  className = ''
}) => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [analysis, setAnalysis] = useState<EnhancedAlbumArtworkAnalysis | null>(null);
  const [activeTab, setActiveTab] = useState<string>('url');
  
  const { analyzeAlbumArtwork, analyzeAlbumArtworkFile, isProcessing } = useMultiModalAI();
  const { toast } = useToast();
  
  // Handle URL input change
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageUrl(e.target.value);
  };
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid file type',
          description: 'Please select an image file.',
          variant: 'destructive',
        });
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: 'File too large',
          description: 'Please select an image smaller than 5MB.',
          variant: 'destructive',
        });
        return;
      }
      
      setSelectedFile(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle analysis
  const handleAnalyze = async () => {
    try {
      if (activeTab === 'url' && imageUrl) {
        // Analyze image from URL
        const result = await analyzeAlbumArtwork(imageUrl);
        setAnalysis(result as EnhancedAlbumArtworkAnalysis);
        
        toast({
          title: 'Analysis complete',
          description: 'Album artwork has been analyzed successfully.',
        });
      } else if (activeTab === 'upload' && selectedFile) {
        // Analyze uploaded file
        const result = await analyzeAlbumArtworkFile(selectedFile);
        setAnalysis(result as EnhancedAlbumArtworkAnalysis);
        
        toast({
          title: 'Analysis complete',
          description: 'Album artwork has been analyzed successfully.',
        });
      } else {
        toast({
          title: 'No image provided',
          description: 'Please enter an image URL or upload an image file.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Analysis failed',
        description: error.message || 'Failed to analyze album artwork.',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>Album Artwork Analysis</CardTitle>
          <CardDescription>
            Analyze album artwork to extract musical insights and genre suggestions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="url">Image URL</TabsTrigger>
              <TabsTrigger value="upload">Upload Image</TabsTrigger>
            </TabsList>
            
            <TabsContent value="url" className="space-y-4">
              <Input
                placeholder="Enter image URL"
                value={imageUrl}
                onChange={handleUrlChange}
              />
              
              {imageUrl && (
                <div className="rounded-md overflow-hidden border border-border">
                  <img 
                    src={imageUrl} 
                    alt="Album artwork" 
                    className="w-full h-auto max-h-[300px] object-contain"
                    onError={() => {
                      toast({
                        title: 'Image error',
                        description: 'Failed to load image from URL.',
                        variant: 'destructive',
                      });
                    }}
                  />
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="upload" className="space-y-4">
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="flex-1"
                />
              </div>
              
              {previewUrl && (
                <div className="rounded-md overflow-hidden border border-border">
                  <img 
                    src={previewUrl} 
                    alt="Album artwork" 
                    className="w-full h-auto max-h-[300px] object-contain"
                  />
                </div>
              )}
            </TabsContent>
          </Tabs>
          
          <Button 
            onClick={handleAnalyze} 
            disabled={isProcessing || (activeTab === 'url' && !imageUrl) || (activeTab === 'upload' && !selectedFile)}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <ImageIcon className="mr-2 h-4 w-4" />
                Analyze Artwork
              </>
            )}
          </Button>
        </CardContent>
      </Card>
      
      {analysis && (
        <EnhancedAlbumArtworkAnalysisComponent analysis={analysis} />
      )}
    </div>
  );
};

export default AlbumArtworkAnalysisDemo;
