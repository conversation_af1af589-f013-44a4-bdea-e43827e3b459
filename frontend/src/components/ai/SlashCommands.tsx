import React, { useState, useEffect, useRef } from 'react';
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from '@/components/ui/command';
import { useAI } from '@/providers/AIProvider';
import { useMultiModalAI } from '@/providers/MultiModalAIProvider';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import { useMCP } from '@/providers/MCPProvider';
import ImageGenerationCommand from './ImageGenerationCommand';
import MCPAudioAnalysisPanel from './MCPAudioAnalysisPanel';

interface SlashCommand {
  id: string;
  name: string;
  description: string;
  icon?: React.ReactNode;
  action: (input?: string) => void;
  showInList: boolean;
}

interface SlashCommandsProps {
  inputValue: string;
  onCommandSelect: (command: string, replace: boolean) => void;
  onCommandExecute: (command: SlashCommand, input?: string) => void;
}

const SlashCommands: React.FC<SlashCommandsProps> = ({
  inputValue,
  onCommandSelect,
  onCommandExecute
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filteredCommands, setFilteredCommands] = useState<SlashCommand[]>([]);
  const [commandInput, setCommandInput] = useState('');
  const [showImageGenerator, setShowImageGenerator] = useState(false);
  const [showAudioAnalysis, setShowAudioAnalysis] = useState(false);

  const { answerQuestion } = useAI();
  const { analyzeImageFile, analyzeAudioFile, generateImage } = useMultiModalAI();
  const { appState, addRecentAction } = useApplicationContext();
  const { isConnected } = useMCP();

  const inputRef = useRef<HTMLInputElement>(null);

  // Define available slash commands
  const slashCommands: SlashCommand[] = [
    {
      id: 'help',
      name: '/help',
      description: 'Show available commands',
      action: () => {
        const helpText = 'Available commands:\n\n' +
          slashCommands
            .filter(cmd => cmd.showInList)
            .map(cmd => `${cmd.name} - ${cmd.description}`)
            .join('\n');

        onCommandExecute({
          id: 'help-result',
          name: '/help',
          description: 'Help result',
          action: () => {},
          showInList: false
        }, helpText);
      },
      showInList: true
    },
    {
      id: 'analyze',
      name: '/analyze',
      description: 'Analyze the current mix or track',
      action: () => {
        const { activeTracks, activeMix } = appState;

        let analysisPrompt = '';

        if (activeMix) {
          analysisPrompt = `Please analyze my current mix "${activeMix.title || 'Untitled'}" with ${activeTracks.length} tracks.`;
        } else if (activeTracks.length > 0) {
          analysisPrompt = `Please analyze my current track "${activeTracks[0].title || 'Untitled'}" by ${activeTracks[0].artist || 'Unknown'}.`;
        } else {
          analysisPrompt = 'Please analyze my current project.';
        }

        onCommandExecute({
          id: 'analyze-result',
          name: '/analyze',
          description: 'Analysis result',
          action: () => {},
          showInList: false
        }, analysisPrompt);
      },
      showInList: true
    },
    {
      id: 'generate',
      name: '/generate',
      description: 'Generate an image based on description',
      action: (input) => {
        // Store the input as initial prompt if provided
        if (input) {
          localStorage.setItem('imageGenerationInitialPrompt', input);
        } else {
          localStorage.removeItem('imageGenerationInitialPrompt');
        }

        // Show the image generator UI
        setShowImageGenerator(true);
      },
      showInList: true
    },
    {
      id: 'transition',
      name: '/transition',
      description: 'Suggest transition between tracks',
      action: () => {
        const { activeTracks } = appState;

        if (activeTracks.length < 2) {
          onCommandExecute({
            id: 'transition-error',
            name: '/transition',
            description: 'Transition error',
            action: () => {},
            showInList: false
          }, 'You need at least two tracks to suggest a transition.');
          return;
        }

        const fromTrack = activeTracks[0];
        const toTrack = activeTracks[1];

        onCommandExecute({
          id: 'transition-result',
          name: '/transition',
          description: 'Transition suggestion',
          action: () => {},
          showInList: false
        }, `Please suggest a transition from "${fromTrack.title || 'Track 1'}" by ${fromTrack.artist || 'Unknown'} to "${toTrack.title || 'Track 2'}" by ${toTrack.artist || 'Unknown'}.`);
      },
      showInList: true
    },
    {
      id: 'shortcuts',
      name: '/shortcuts',
      description: 'Show keyboard shortcuts',
      action: () => {
        onCommandExecute({
          id: 'shortcuts-result',
          name: '/shortcuts',
          description: 'Keyboard shortcuts',
          action: () => {},
          showInList: false
        }, 'Please show me the keyboard shortcuts for this application.');
      },
      showInList: true
    },
    {
      id: 'clear',
      name: '/clear',
      description: 'Clear the conversation',
      action: () => {
        onCommandExecute({
          id: 'clear',
          name: '/clear',
          description: 'Clear the conversation',
          action: () => {},
          showInList: false
        });
      },
      showInList: true
    },
    {
      id: 'feedback',
      name: '/feedback',
      description: 'Provide feedback on the assistant',
      action: (input) => {
        if (!input) {
          onCommandExecute({
            id: 'feedback-error',
            name: '/feedback',
            description: 'Feedback error',
            action: () => {},
            showInList: false
          }, 'Please provide your feedback.');
          return;
        }

        onCommandExecute({
          id: 'feedback-result',
          name: '/feedback',
          description: 'Feedback submitted',
          action: () => {},
          showInList: false
        }, `Thank you for your feedback: "${input}"`);
      },
      showInList: true
    },
    {
      id: 'audio-analysis',
      name: '/audio-analysis',
      description: 'Analyze audio using MCP and librosa',
      action: () => {
        if (!isConnected) {
          onCommandExecute({
            id: 'audio-analysis-error',
            name: '/audio-analysis',
            description: 'Audio analysis error',
            action: () => {},
            showInList: false
          }, 'MCP is not connected. Please connect to MCP first to use audio analysis.');
          return;
        }

        // Show the audio analysis UI
        setShowAudioAnalysis(true);
      },
      showInList: true
    }
  ];

  // Check if input starts with a slash
  useEffect(() => {
    if (inputValue.startsWith('/')) {
      setIsOpen(true);
      setCommandInput(inputValue.slice(1));

      // Filter commands based on input
      const filtered = slashCommands.filter(cmd =>
        cmd.showInList && cmd.name.toLowerCase().includes(inputValue.toLowerCase())
      );

      setFilteredCommands(filtered);
    } else {
      setIsOpen(false);
    }
  }, [inputValue]);



  // Handle command selection
  const handleCommandSelect = (commandName: string) => {
    const command = slashCommands.find(cmd => cmd.name === commandName);

    if (command) {
      // If command requires additional input, replace the slash command with the command name
      if (['feedback'].includes(command.id)) {
        onCommandSelect(`${command.name} `, true);
      } else if (command.id === 'generate') {
        // For generate command, show the UI directly
        command.action('');
        onCommandSelect('', true);
      } else if (command.id === 'audio-analysis') {
        // For audio analysis command, show the UI directly
        command.action();
        onCommandSelect('', true);
      } else {
        // Execute the command directly
        command.action();
        onCommandSelect('', true);
      }

      // Log the action
      addRecentAction({
        type: 'slash_command_used',
        description: `Used slash command: ${command.name}`
      });
    }

    setIsOpen(false);
  };

  // Handle command execution with input
  const handleCommandExecute = (commandId: string, input: string) => {
    const command = slashCommands.find(cmd => cmd.id === commandId);

    if (command) {
      command.action(input);

      // Log the action
      addRecentAction({
        type: 'slash_command_executed',
        description: `Executed slash command: ${command.name}`
      });
    }
  };

  // Handle image generation result
  const handleImageGenerated = (imageMarkdown: string) => {
    setShowImageGenerator(false);

    // Clean up localStorage
    localStorage.removeItem('imageGenerationInitialPrompt');

    // Show the result
    onCommandExecute({
      id: 'generate-result',
      name: '/generate',
      description: 'Generate image result',
      action: () => {},
      showInList: false
    }, imageMarkdown);
  };

  // Handle image generation cancel
  const handleImageGenerationCancel = () => {
    setShowImageGenerator(false);

    // Clean up localStorage
    localStorage.removeItem('imageGenerationInitialPrompt');
  };

  // Handle audio analysis result
  const handleAudioAnalysisComplete = (analysisResult: any) => {
    setShowAudioAnalysis(false);

    // Show a message that the analysis is complete
    onCommandExecute({
      id: 'audio-analysis-result',
      name: '/audio-analysis',
      description: 'Audio analysis result',
      action: () => {},
      showInList: false
    }, 'Audio analysis complete. The AI will now interpret the results.');
  };

  // Handle audio analysis cancel
  const handleAudioAnalysisCancel = () => {
    setShowAudioAnalysis(false);
  };

  // If image generator is shown, render it
  if (showImageGenerator) {
    // Get the initial prompt from localStorage if available
    const initialPrompt = localStorage.getItem('imageGenerationInitialPrompt') || '';

    return (
      <div className="absolute bottom-full left-0 w-full mb-1 z-10">
        <ImageGenerationCommand
          onGenerate={handleImageGenerated}
          onCancel={handleImageGenerationCancel}
          initialPrompt={initialPrompt}
        />
      </div>
    );
  }

  // If audio analysis panel is shown, render it
  if (showAudioAnalysis) {
    return (
      <div className="absolute bottom-full left-0 w-full mb-1 z-10">
        <MCPAudioAnalysisPanel
          onAnalysisComplete={handleAudioAnalysisComplete}
          onClose={handleAudioAnalysisCancel}
        />
      </div>
    );
  }

  // If not open, don't render
  if (!isOpen) {
    return null;
  }

  return (
    <div className="absolute bottom-full left-0 w-full mb-1 z-10">
      <Command className="rounded-lg border shadow-md">
        <CommandInput
          placeholder="Type a command..."
          value={commandInput}
          onValueChange={setCommandInput}
          ref={inputRef}
        />
        <CommandList>
          <CommandEmpty>No commands found.</CommandEmpty>
          <CommandGroup heading="Commands">
            {filteredCommands.map(command => (
              <CommandItem
                key={command.id}
                onSelect={() => handleCommandSelect(command.name)}
              >
                <span className="font-medium">{command.name}</span>
                <span className="text-muted-foreground ml-2 text-xs">
                  {command.description}
                </span>
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </Command>
    </div>
  );
};

export default SlashCommands;
