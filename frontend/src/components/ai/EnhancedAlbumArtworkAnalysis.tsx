import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { EnhancedAlbumArtworkAnalysis } from '@/types/multimodal';
import EnhancedMoodAnalysis from './EnhancedMoodAnalysis';
import VisualToMusicStyleMapping from './VisualToMusicStyleMapping';

interface EnhancedAlbumArtworkAnalysisComponentProps {
  analysis: EnhancedAlbumArtworkAnalysis;
  className?: string;
}

const EnhancedAlbumArtworkAnalysisComponent: React.FC<EnhancedAlbumArtworkAnalysisComponentProps> = ({
  analysis,
  className = ''
}) => {
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Album Artwork Analysis</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <Tabs defaultValue="genres">
          <TabsList className="grid grid-cols-4 h-8">
            <TabsTrigger value="genres" className="text-xs">Genres</TabsTrigger>
            <TabsTrigger value="mood" className="text-xs">Mood</TabsTrigger>
            <TabsTrigger value="style" className="text-xs">Style</TabsTrigger>
            <TabsTrigger value="mapping" className="text-xs">Music Mapping</TabsTrigger>
          </TabsList>
          
          <TabsContent value="genres" className="mt-2 space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Genre Suggestions</h3>
              <div className="space-y-2">
                {analysis.genre_suggestions.map((genre, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{genre.genre}</Badge>
                      <span className="text-xs text-muted-foreground">{genre.confidence}% confidence</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {analysis.sub_genres && analysis.sub_genres.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Sub-Genres</h3>
                <div className="flex flex-wrap gap-1">
                  {analysis.sub_genres.map((subGenre, index) => (
                    <Badge key={index} variant="outline">
                      {subGenre.sub_genre} ({subGenre.confidence}%)
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Era</h3>
              <Badge>{analysis.era}</Badge>
            </div>
          </TabsContent>
          
          <TabsContent value="mood" className="mt-2">
            <EnhancedMoodAnalysis mood={analysis.mood} />
          </TabsContent>
          
          <TabsContent value="style" className="mt-2 space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Visual Style</h3>
              <Badge variant="secondary">{analysis.visual_style.primary_style}</Badge>
              
              {analysis.visual_style.elements && analysis.visual_style.elements.length > 0 && (
                <div className="mt-2">
                  <h4 className="text-xs font-medium">Elements</h4>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {analysis.visual_style.elements.map((element, index) => (
                      <Badge key={index} variant="outline">{element}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Color Palette</h3>
              <div className="flex flex-wrap gap-2">
                {analysis.color_palette.map((color, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div 
                      className="w-6 h-6 rounded-full border border-border"
                      style={{ backgroundColor: color.color }}
                      title={`${color.color}: ${color.emotion} (${color.intensity}/10)`}
                    />
                    <span className="text-xs mt-1">{color.emotion}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {analysis.symbols && analysis.symbols.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Symbols</h3>
                <div className="space-y-1">
                  {analysis.symbols.map((symbol, index) => (
                    <div key={index} className="flex justify-between text-xs">
                      <span>{symbol.symbol}</span>
                      <span className="text-muted-foreground">{symbol.meaning}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="mapping" className="mt-2">
            <VisualToMusicStyleMapping analysis={analysis} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default EnhancedAlbumArtworkAnalysisComponent;
