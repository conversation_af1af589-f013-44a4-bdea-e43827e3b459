import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, AlertCircle, Info, Settings } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { aiSettingsService, ParameterMetadata } from '@/services/api/aiSettings';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface FeatureParameterManagerProps {
  className?: string;
}

// Human-readable names for feature IDs
const FEATURE_NAMES: Record<string, string> = {
  question_answering: 'Question Answering',
  collection_analysis: 'Collection Analysis',
  style_generation: 'Style Generation',
  style_documentation: 'Style Documentation',
  transition_suggestions: 'Transition Suggestions',
  image_analysis: 'Image Analysis',
  audio_analysis: 'Audio Analysis',
  mix_flow_analysis: 'Mix Flow Analysis'
};

const FeatureParameterManager: React.FC<FeatureParameterManagerProps> = ({ className = '' }) => {
  const [features, setFeatures] = useState<Record<string, Record<string, any>>>({});
  const [selectedFeature, setSelectedFeature] = useState<string>('');
  const [editedParameters, setEditedParameters] = useState<Record<string, any>>({});
  const [metadata, setMetadata] = useState<Record<string, ParameterMetadata>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load features and metadata on component mount
  useEffect(() => {
    loadFeaturesAndMetadata();
  }, []);

  const loadFeaturesAndMetadata = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Load features and metadata in parallel
      const [featuresResponse, metadataResponse] = await Promise.all([
        aiSettingsService.getAllFeatureParameters(),
        aiSettingsService.getParameterMetadata()
      ]);
      
      setFeatures(featuresResponse);
      setMetadata(metadataResponse.metadata);
      
      // Select the first feature by default
      const featureIds = Object.keys(featuresResponse);
      if (featureIds.length > 0) {
        const firstFeatureId = featureIds[0];
        setSelectedFeature(firstFeatureId);
        setEditedParameters(featuresResponse[firstFeatureId]);
      }
    } catch (error: any) {
      setError('Failed to load feature parameters: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to load feature parameters',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFeatureSelect = (featureId: string) => {
    setSelectedFeature(featureId);
    setEditedParameters(features[featureId]);
  };

  const handleParameterChange = (paramName: string, value: any) => {
    setEditedParameters(prev => ({
      ...prev,
      [paramName]: value
    }));
  };

  const handleSaveParameters = async () => {
    setIsSaving(true);
    setError(null);
    try {
      await aiSettingsService.updateFeatureParameters(selectedFeature, editedParameters);
      
      // Update local state
      setFeatures(prev => ({
        ...prev,
        [selectedFeature]: editedParameters
      }));
      
      toast({
        title: 'Success',
        description: 'Feature parameters updated successfully'
      });
    } catch (error: any) {
      setError('Failed to update feature parameters: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to update feature parameters',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetParameters = async () => {
    setIsResetting(true);
    setError(null);
    try {
      await aiSettingsService.resetFeatureParameters(selectedFeature);
      
      // Reload features to get the default values
      await loadFeaturesAndMetadata();
      
      toast({
        title: 'Success',
        description: 'Feature parameters reset to default'
      });
    } catch (error: any) {
      setError('Failed to reset feature parameters: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to reset feature parameters',
        variant: 'destructive'
      });
    } finally {
      setIsResetting(false);
    }
  };

  // Get a human-readable name for the feature ID
  const getFeatureName = (featureId: string) => {
    return FEATURE_NAMES[featureId] || featureId
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading feature parameters...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          Feature Parameters
        </CardTitle>
        <CardDescription>
          Customize AI parameters for specific features
        </CardDescription>
      </CardHeader>
      <CardContent>
        {Object.keys(features).length === 0 ? (
          <div className="text-center p-4">
            <p>No feature parameters found.</p>
          </div>
        ) : (
          <Tabs value={selectedFeature} onValueChange={handleFeatureSelect}>
            <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-4">
              {Object.keys(features).map(featureId => (
                <TabsTrigger key={featureId} value={featureId}>
                  {getFeatureName(featureId)}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {Object.keys(features).map(featureId => (
              <TabsContent key={featureId} value={featureId}>
                <div className="space-y-6">
                  {Object.entries(features[featureId]).map(([paramName, paramValue]) => {
                    const paramMeta = metadata[paramName];
                    if (!paramMeta) return null;
                    
                    return (
                      <div key={paramName} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Label className="flex items-center cursor-help">
                                  {paramMeta.name}
                                  <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                                </Label>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{paramMeta.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <span className="text-sm font-medium">
                            {featureId === selectedFeature 
                              ? editedParameters[paramName]?.toFixed(2) 
                              : paramValue?.toFixed(2)}
                          </span>
                        </div>
                        
                        {paramMeta.min !== undefined && paramMeta.max !== undefined && (
                          <Slider
                            value={[featureId === selectedFeature 
                              ? editedParameters[paramName] 
                              : paramValue]}
                            min={paramMeta.min}
                            max={paramMeta.max}
                            step={paramMeta.step || 0.1}
                            onValueChange={(value) => handleParameterChange(paramName, value[0])}
                            disabled={featureId !== selectedFeature || isSaving || isResetting}
                          />
                        )}
                        
                        <div className="flex justify-between text-xs text-muted-foreground">
                          {paramName === 'temperature' ? (
                            <>
                              <span>More Precise</span>
                              <span>More Creative</span>
                            </>
                          ) : paramName === 'max_tokens' ? (
                            <>
                              <span>Shorter</span>
                              <span>Longer</span>
                            </>
                          ) : (
                            <>
                              <span>Min: {paramMeta.min}</span>
                              <span>Max: {paramMeta.max}</span>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleResetParameters}
          disabled={isLoading || isSaving || isResetting}
        >
          {isResetting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Reset to Default
        </Button>
        <Button
          onClick={handleSaveParameters}
          disabled={isLoading || isSaving || isResetting}
        >
          {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  );
};

export default FeatureParameterManager;
