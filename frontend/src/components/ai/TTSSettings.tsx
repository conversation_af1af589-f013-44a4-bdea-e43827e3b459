import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Play, Square, RefreshCw, Upload, Mic, Save, Volume2 } from 'lucide-react';
import axios from 'axios';

// Define types for voice settings
export interface VoicePreset {
  id: string;
  name: string;
  type: 'system' | 'cloned' | 'custom';
  gender?: 'male' | 'female';
  pitch?: number;
  speed?: number;
  samplePath?: string;
}

export interface TTSSettingsProps {
  className?: string;
  onVoiceChange?: (preset: VoicePreset) => void;
}

const TTSSettings: React.FC<TTSSettingsProps> = ({ className = '', onVoiceChange }) => {
  const { toast } = useToast();
  
  // Voice settings from local storage
  const [ttsSettings, setTTSSettings] = useLocalStorage('tts-settings', {
    enabled: true,
    autoSpeak: false,
    volume: 80,
    selectedPresetId: 'default',
  });
  
  // Local state
  const [volume, setVolume] = useState(ttsSettings.volume);
  const [enabled, setEnabled] = useState(ttsSettings.enabled);
  const [autoSpeak, setAutoSpeak] = useState(ttsSettings.autoSpeak);
  const [selectedPresetId, setSelectedPresetId] = useState(ttsSettings.selectedPresetId);
  const [presets, setPresets] = useState<VoicePreset[]>([
    { id: 'default', name: 'Default Voice', type: 'system' },
    { id: 'male-1', name: 'Male Voice', type: 'system', gender: 'male' },
    { id: 'female-1', name: 'Female Voice', type: 'system', gender: 'female' },
  ]);
  
  // Voice customization state
  const [customName, setCustomName] = useState('');
  const [customGender, setCustomGender] = useState<'male' | 'female'>('male');
  const [customPitch, setCustomPitch] = useState(3);
  const [customSpeed, setCustomSpeed] = useState(3);
  
  // Voice cloning state
  const [cloneName, setCloneName] = useState('');
  const [promptText, setPromptText] = useState('');
  const [promptAudio, setPromptAudio] = useState<File | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recorder, setRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Save settings when they change
  useEffect(() => {
    setTTSSettings({
      enabled,
      autoSpeak,
      volume,
      selectedPresetId,
    });
    
    // Notify parent component if callback provided
    if (onVoiceChange) {
      const selectedPreset = presets.find(preset => preset.id === selectedPresetId);
      if (selectedPreset) {
        onVoiceChange(selectedPreset);
      }
    }
  }, [enabled, autoSpeak, volume, selectedPresetId]);
  
  // Initialize microphone for recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      setRecorder(mediaRecorder);
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setAudioChunks(prev => [...prev, event.data]);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        const file = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });
        setPromptAudio(file);
        setAudioChunks([]);
      };
      
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      toast({
        title: 'Microphone Error',
        description: 'Could not access microphone. Please check permissions.',
        variant: 'destructive',
      });
    }
  };
  
  const stopRecording = () => {
    if (recorder && recorder.state !== 'inactive') {
      recorder.stop();
      setIsRecording(false);
    }
  };
  
  // Handle file upload for voice cloning
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPromptAudio(file);
    }
  };
  
  // Create a custom voice
  const createCustomVoice = async () => {
    if (!customName) {
      toast({
        title: 'Name Required',
        description: 'Please provide a name for your custom voice.',
        variant: 'destructive',
      });
      return;
    }
    
    const newPreset: VoicePreset = {
      id: `custom-${Date.now()}`,
      name: customName,
      type: 'custom',
      gender: customGender,
      pitch: customPitch,
      speed: customSpeed,
    };
    
    setPresets(prev => [...prev, newPreset]);
    setSelectedPresetId(newPreset.id);
    
    toast({
      title: 'Voice Created',
      description: `Custom voice "${customName}" has been created.`,
    });
    
    // Reset form
    setCustomName('');
  };
  
  // Clone a voice from audio sample
  const cloneVoice = async () => {
    if (!cloneName) {
      toast({
        title: 'Name Required',
        description: 'Please provide a name for your cloned voice.',
        variant: 'destructive',
      });
      return;
    }
    
    if (!promptAudio) {
      toast({
        title: 'Audio Required',
        description: 'Please upload or record an audio sample.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Create form data for API request
      const formData = new FormData();
      formData.append('name', cloneName);
      formData.append('prompt_audio', promptAudio);
      if (promptText) {
        formData.append('prompt_text', promptText);
      }
      
      // Send request to backend
      const response = await axios.post('/api/v1/tts/clone-voice', formData);
      
      if (response.data.success) {
        const newPreset: VoicePreset = {
          id: response.data.preset_id,
          name: cloneName,
          type: 'cloned',
          samplePath: response.data.sample_path,
        };
        
        setPresets(prev => [...prev, newPreset]);
        setSelectedPresetId(newPreset.id);
        
        toast({
          title: 'Voice Cloned',
          description: `Voice "${cloneName}" has been successfully cloned.`,
        });
        
        // Reset form
        setCloneName('');
        setPromptText('');
        setPromptAudio(null);
      }
    } catch (error) {
      console.error('Error cloning voice:', error);
      toast({
        title: 'Cloning Failed',
        description: 'Failed to clone voice. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Test the selected voice
  const testVoice = async () => {
    const selectedPreset = presets.find(preset => preset.id === selectedPresetId);
    if (!selectedPreset) return;
    
    setIsSpeaking(true);
    
    try {
      const response = await axios.post('/api/v1/tts/synthesize', {
        text: 'This is a test of the selected voice and settings.',
        preset_id: selectedPreset.id,
        volume: volume / 100,
      }, {
        responseType: 'blob'
      });
      
      // Play the audio
      const audioBlob = response.data;
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);
      
      audio.onended = () => {
        setIsSpeaking(false);
        URL.revokeObjectURL(audioUrl);
      };
      
      audio.play();
    } catch (error) {
      console.error('Error testing voice:', error);
      setIsSpeaking(false);
      
      toast({
        title: 'Test Failed',
        description: 'Failed to test voice. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Volume2 className="h-5 w-5 mr-2" />
          Text-to-Speech Settings
        </CardTitle>
        <CardDescription>
          Configure high-quality text-to-speech settings with Spark-TTS
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs defaultValue="voices">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="voices">Voice Selection</TabsTrigger>
            <TabsTrigger value="custom">Create Voice</TabsTrigger>
            <TabsTrigger value="clone">Clone Voice</TabsTrigger>
          </TabsList>
          
          {/* Voice Selection Tab */}
          <TabsContent value="voices" className="space-y-4">
            <div className="space-y-4">
              <Label>Select Voice</Label>
              <RadioGroup 
                value={selectedPresetId} 
                onValueChange={setSelectedPresetId}
                className="space-y-2"
              >
                {presets.map(preset => (
                  <div key={preset.id} className="flex items-center justify-between space-x-2 border p-3 rounded-md">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={preset.id} id={preset.id} />
                      <Label htmlFor={preset.id} className="font-medium">
                        {preset.name}
                        <span className="ml-2 text-xs text-muted-foreground">
                          {preset.type === 'system' ? 'System' : 
                           preset.type === 'cloned' ? 'Cloned' : 'Custom'}
                        </span>
                      </Label>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => testVoice()}
                      disabled={isSpeaking}
                    >
                      {isSpeaking && selectedPresetId === preset.id ? <Square className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                  </div>
                ))}
              </RadioGroup>
            </div>
            
            {/* Volume slider */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Volume</Label>
                <span className="text-sm">{volume}%</span>
              </div>
              <Slider
                value={[volume]}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) => setVolume(value[0])}
              />
            </div>
            
            {/* Enable/Disable TTS */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Enable Text-to-Speech</Label>
                <p className="text-sm text-muted-foreground">
                  Enable or disable all text-to-speech features
                </p>
              </div>
              <Switch 
                checked={enabled}
                onCheckedChange={setEnabled}
              />
            </div>
            
            {/* Auto-speak setting */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Auto-speak Responses</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically speak AI responses when received
                </p>
              </div>
              <Switch 
                checked={autoSpeak}
                onCheckedChange={setAutoSpeak}
                disabled={!enabled}
              />
            </div>
          </TabsContent>
          
          {/* Create Custom Voice Tab */}
          <TabsContent value="custom" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="custom-name">Voice Name</Label>
                <Input 
                  id="custom-name" 
                  value={customName} 
                  onChange={(e) => setCustomName(e.target.value)} 
                  placeholder="Enter a name for your custom voice"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Gender</Label>
                <RadioGroup 
                  value={customGender} 
                  onValueChange={(value: 'male' | 'female') => setCustomGender(value)}
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="male" id="male" />
                    <Label htmlFor="male">Male</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="female" id="female" />
                    <Label htmlFor="female">Female</Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Pitch</Label>
                  <span className="text-sm">
                    {customPitch === 1 ? 'Very Low' : 
                     customPitch === 2 ? 'Low' : 
                     customPitch === 3 ? 'Medium' : 
                     customPitch === 4 ? 'High' : 'Very High'}
                  </span>
                </div>
                <Slider
                  value={[customPitch]}
                  min={1}
                  max={5}
                  step={1}
                  onValueChange={(value) => setCustomPitch(value[0])}
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Speed</Label>
                  <span className="text-sm">
                    {customSpeed === 1 ? 'Very Slow' : 
                     customSpeed === 2 ? 'Slow' : 
                     customSpeed === 3 ? 'Medium' : 
                     customSpeed === 4 ? 'Fast' : 'Very Fast'}
                  </span>
                </div>
                <Slider
                  value={[customSpeed]}
                  min={1}
                  max={5}
                  step={1}
                  onValueChange={(value) => setCustomSpeed(value[0])}
                />
              </div>
              
              <Button 
                className="w-full" 
                onClick={createCustomVoice}
              >
                <Save className="h-4 w-4 mr-2" />
                Create Voice
              </Button>
            </div>
          </TabsContent>
          
          {/* Clone Voice Tab */}
          <TabsContent value="clone" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clone-name">Voice Name</Label>
                <Input 
                  id="clone-name" 
                  value={cloneName} 
                  onChange={(e) => setCloneName(e.target.value)} 
                  placeholder="Enter a name for your cloned voice"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="prompt-text">Reference Audio Text (Optional)</Label>
                <Input 
                  id="prompt-text" 
                  value={promptText} 
                  onChange={(e) => setPromptText(e.target.value)} 
                  placeholder="Enter the text spoken in your reference audio"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Reference Audio</Label>
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Input
                      type="file"
                      accept="audio/*"
                      onChange={handleFileChange}
                      className="hidden"
                      id="audio-upload"
                    />
                    <Label 
                      htmlFor="audio-upload" 
                      className="flex items-center justify-center w-full p-2 border border-dashed rounded-md cursor-pointer hover:bg-secondary"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {promptAudio ? promptAudio.name : 'Upload Audio'}
                    </Label>
                  </div>
                  <Button 
                    variant={isRecording ? "destructive" : "outline"}
                    onClick={isRecording ? stopRecording : startRecording}
                  >
                    {isRecording ? (
                      <Square className="h-4 w-4 mr-2" />
                    ) : (
                      <Mic className="h-4 w-4 mr-2" />
                    )}
                    {isRecording ? 'Stop' : 'Record'}
                  </Button>
                </div>
              </div>
              
              <Button 
                className="w-full" 
                onClick={cloneVoice}
                disabled={isLoading}
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Clone Voice
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default TTSSettings;
