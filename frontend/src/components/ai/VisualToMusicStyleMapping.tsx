import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { EnhancedAlbumArtworkAnalysis } from '@/types/multimodal';
import { generateMusicStyleSuggestions } from '@/services/visualToMusicMapping';

interface VisualToMusicStyleMappingProps {
  analysis: EnhancedAlbumArtworkAnalysis;
  className?: string;
}

const VisualToMusicStyleMapping: React.FC<VisualToMusicStyleMappingProps> = ({
  analysis,
  className = ''
}) => {
  const suggestions = generateMusicStyleSuggestions(analysis);
  
  if (!suggestions.length) return null;
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Visual to Music Style Mapping</CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3 space-y-4">
        {suggestions.map((suggestion, index) => (
          <div key={index} className="space-y-2">
            <h3 className="text-sm font-medium">{suggestion.title}</h3>
            
            {suggestion.genres && (
              <div className="flex flex-wrap gap-1">
                {suggestion.genres.map((genre, i) => (
                  <Badge key={i} variant="secondary">{genre}</Badge>
                ))}
              </div>
            )}
            
            {suggestion.moods && (
              <div className="flex flex-wrap gap-1">
                {suggestion.moods.map((mood, i) => (
                  <Badge key={i} variant="outline">{mood}</Badge>
                ))}
              </div>
            )}
            
            {suggestion.characteristics && (
              <p className="text-xs text-muted-foreground">
                Characteristics: {suggestion.characteristics.join(', ')}
              </p>
            )}
            
            {suggestion.description && (
              <p className="text-xs text-muted-foreground">{suggestion.description}</p>
            )}
          </div>
        ))}
        
        <div className="text-xs text-muted-foreground mt-2 pt-2 border-t">
          <p>These suggestions are based on visual elements in the album artwork and can help guide track selection and mix style.</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default VisualToMusicStyleMapping;
