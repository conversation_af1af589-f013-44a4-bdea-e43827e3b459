import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, AlertCircle, Info } from 'lucide-react';
import { aiSettingsService } from '@/services/api/aiSettings';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface SystemPromptManagerProps {
  className?: string;
}

interface PromptVariable {
  name: string;
  description: string;
}

// Known variables for each prompt type
const PROMPT_VARIABLES: Record<string, PromptVariable[]> = {
  question_answering: [
    { name: 'question', description: 'The user\'s question' },
    { name: 'context_text', description: 'Application context information' }
  ],
  collection_analysis: [
    { name: 'tracks_summary', description: 'Summary of tracks in the collection' }
  ],
  album_artwork_analysis: [],
  audio_analysis: [],
  mix_flow_analysis: [
    { name: 'track_sequence_json', description: 'JSON representation of the track sequence' }
  ],
  style_generation: [
    { name: 'description', description: 'User\'s description of the desired style' },
    { name: 'constraints', description: 'Optional constraints for the style parameters' }
  ],
  style_documentation: [
    { name: 'style_name', description: 'Name of the style' },
    { name: 'style_description', description: 'Description of the style' },
    { name: 'bpm_range', description: 'BPM range of the style' },
    { name: 'energy_pattern', description: 'Energy pattern of the style' },
    { name: 'key_rules', description: 'Key rules of the style' }
  ],
  transition_suggestions: [
    { name: 'current_track_details', description: 'Details of the current track' },
    { name: 'next_track_details', description: 'Details of the next track' }
  ]
};

// Human-readable names for prompt IDs
const PROMPT_NAMES: Record<string, string> = {
  question_answering: 'Question Answering',
  collection_analysis: 'Collection Analysis',
  album_artwork_analysis: 'Album Artwork Analysis',
  audio_analysis: 'Audio Analysis',
  mix_flow_analysis: 'Mix Flow Analysis',
  style_generation: 'Style Generation',
  style_documentation: 'Style Documentation',
  transition_suggestions: 'Transition Suggestions'
};

const SystemPromptManager: React.FC<SystemPromptManagerProps> = ({ className = '' }) => {
  const [prompts, setPrompts] = useState<Record<string, string>>({});
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [editedPrompt, setEditedPrompt] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load prompts on component mount
  useEffect(() => {
    loadPrompts();
  }, []);

  const loadPrompts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const prompts = await aiSettingsService.getSystemPrompts();
      setPrompts(prompts);

      // Select the first prompt by default
      const promptIds = Object.keys(prompts);
      if (promptIds.length > 0) {
        const firstPromptId = promptIds[0];
        setSelectedPrompt(firstPromptId);
        setEditedPrompt(prompts[firstPromptId]);
      }
    } catch (error: any) {
      setError('Failed to load system prompts: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to load system prompts',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePromptSelect = (promptId: string) => {
    setSelectedPrompt(promptId);
    setEditedPrompt(prompts[promptId]);
  };

  const handleSavePrompt = async () => {
    setIsSaving(true);
    setError(null);
    try {
      await aiSettingsService.updateSystemPrompt(selectedPrompt, editedPrompt);

      // Update local state
      setPrompts(prev => ({
        ...prev,
        [selectedPrompt]: editedPrompt
      }));

      toast({
        title: 'Success',
        description: 'System prompt updated successfully'
      });
    } catch (error: any) {
      setError('Failed to update system prompt: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to update system prompt',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetPrompt = async () => {
    setIsResetting(true);
    setError(null);
    try {
      await aiSettingsService.resetSystemPrompt(selectedPrompt);

      // Reload prompts to get the default value
      await loadPrompts();

      toast({
        title: 'Success',
        description: 'System prompt reset to default'
      });
    } catch (error: any) {
      setError('Failed to reset system prompt: ' + (error.response?.data?.detail || error.message));
      toast({
        title: 'Error',
        description: 'Failed to reset system prompt',
        variant: 'destructive'
      });
    } finally {
      setIsResetting(false);
    }
  };

  // Get a human-readable name for the prompt ID
  const getPromptName = (promptId: string) => {
    return PROMPT_NAMES[promptId] || promptId
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get variables for the current prompt
  const getPromptVariables = (promptId: string) => {
    return PROMPT_VARIABLES[promptId] || [];
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading system prompts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle>System Prompts</CardTitle>
        <CardDescription>
          Customize the system prompts used by the AI assistant
        </CardDescription>
      </CardHeader>
      <CardContent>
        {Object.keys(prompts).length === 0 ? (
          <div className="text-center p-4">
            <p>No system prompts found.</p>
          </div>
        ) : (
          <Tabs value={selectedPrompt} onValueChange={handlePromptSelect}>
            <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-4">
              {Object.keys(prompts).map(promptId => (
                <TabsTrigger key={promptId} value={promptId}>
                  {getPromptName(promptId)}
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.keys(prompts).map(promptId => (
              <TabsContent key={promptId} value={promptId}>
                <div className="space-y-4">
                  <Textarea
                    value={promptId === selectedPrompt ? editedPrompt : prompts[promptId]}
                    onChange={e => setEditedPrompt(e.target.value)}
                    rows={12}
                    className="font-mono text-sm"
                    disabled={promptId !== selectedPrompt || isSaving || isResetting}
                  />

                  {getPromptVariables(promptId).length > 0 && (
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Available Variables</AlertTitle>
                      <AlertDescription>
                        <ul className="list-disc pl-5 mt-2">
                          {getPromptVariables(promptId).map(variable => (
                            <li key={variable.name} className="mb-1">
                              <code>{`{${variable.name}}`}</code> - {variable.description}
                            </li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleResetPrompt}
          disabled={isLoading || isSaving || isResetting}
        >
          {isResetting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Reset to Default
        </Button>
        <Button
          onClick={handleSavePrompt}
          disabled={isLoading || isSaving || isResetting}
        >
          {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SystemPromptManager;
