import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Music, Disc, BookOpen, ArrowRight, Plus } from 'lucide-react';
import { useApplicationContext } from '@/providers/ApplicationContextProvider';
import { useGlobalAIAssistant } from '@/providers/GlobalAIAssistantProvider';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface TrackRecommendation {
  id: string;
  title: string;
  artist: string;
  genre?: string;
  bpm?: number;
  key?: string;
  reason: string;
}

interface TransitionRecommendation {
  id: string;
  name: string;
  description: string;
  fromTrack: string;
  toTrack: string;
  technique: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface LearningResource {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'tutorial';
  url?: string;
  context?: string;
}

interface ContentRecommendationsProps {
  className?: string;
}

const ContentRecommendations: React.FC<ContentRecommendationsProps> = ({
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<string>('tracks');
  const { appState, addRecentAction } = useApplicationContext();
  const { openAssistant } = useGlobalAIAssistant();
  const { toast } = useToast();

  // Generate track recommendations based on current mix
  const generateTrackRecommendations = (): TrackRecommendation[] => {
    const { activeTracks } = appState;
    
    // If no active tracks, return empty array
    if (!activeTracks || activeTracks.length === 0) {
      return [];
    }
    
    // Mock recommendations based on active tracks
    // In a real implementation, this would call an API or use AI to generate recommendations
    return [
      {
        id: 'track-rec-1',
        title: 'Summer Vibes',
        artist: 'DJ Sunshine',
        genre: 'House',
        bpm: 128,
        key: '6A',
        reason: 'Similar energy to your current tracks'
      },
      {
        id: 'track-rec-2',
        title: 'Midnight Drive',
        artist: 'Night Cruiser',
        genre: 'Deep House',
        bpm: 124,
        key: '8A',
        reason: 'Harmonically compatible with your mix'
      },
      {
        id: 'track-rec-3',
        title: 'Urban Jungle',
        artist: 'City Beats',
        genre: 'Tech House',
        bpm: 126,
        key: '11B',
        reason: 'Would create a nice progression in your mix'
      }
    ];
  };
  
  // Generate transition recommendations
  const generateTransitionRecommendations = (): TransitionRecommendation[] => {
    const { activeTracks } = appState;
    
    // If less than 2 active tracks, return empty array
    if (!activeTracks || activeTracks.length < 2) {
      return [];
    }
    
    // Mock transition recommendations
    // In a real implementation, this would call an API or use AI to generate recommendations
    return [
      {
        id: 'transition-rec-1',
        name: 'Smooth EQ Blend',
        description: 'Gradually swap bass frequencies while maintaining mid and high elements',
        fromTrack: activeTracks[0].title || 'Track 1',
        toTrack: activeTracks[1].title || 'Track 2',
        technique: 'EQ Mixing',
        difficulty: 'easy'
      },
      {
        id: 'transition-rec-2',
        name: 'Echo Out Transition',
        description: 'Apply echo effect to outgoing track while bringing in the new track',
        fromTrack: activeTracks[0].title || 'Track 1',
        toTrack: activeTracks[1].title || 'Track 2',
        technique: 'Effect Transition',
        difficulty: 'medium'
      },
      {
        id: 'transition-rec-3',
        name: 'Double Drop',
        description: 'Align the drops of both tracks for a high-energy transition',
        fromTrack: activeTracks[0].title || 'Track 1',
        toTrack: activeTracks[1].title || 'Track 2',
        technique: 'Energy Transition',
        difficulty: 'hard'
      }
    ];
  };
  
  // Generate learning resources
  const generateLearningResources = (): LearningResource[] => {
    // Mock learning resources
    // In a real implementation, this would be based on user skill level and interests
    return [
      {
        id: 'resource-1',
        title: 'Mastering EQ Mixing',
        description: 'Learn how to create seamless transitions using EQ techniques',
        type: 'article',
        context: 'Would you like to learn about EQ mixing techniques for smoother transitions?',
      },
      {
        id: 'resource-2',
        title: 'Beat Matching Fundamentals',
        description: 'A comprehensive guide to perfect beat matching every time',
        type: 'tutorial',
        context: 'Would you like to improve your beat matching skills?',
      },
      {
        id: 'resource-3',
        title: 'Energy Flow in DJ Sets',
        description: 'How to create the perfect energy progression in your mixes',
        type: 'video',
        context: 'Would you like to learn about creating dynamic energy flow in your mixes?',
      }
    ];
  };
  
  const trackRecommendations = generateTrackRecommendations();
  const transitionRecommendations = generateTransitionRecommendations();
  const learningResources = generateLearningResources();
  
  // Handle track recommendation action
  const handleTrackAction = (track: TrackRecommendation) => {
    // In a real implementation, this would add the track to the mix
    toast({
      title: 'Track Added',
      description: `${track.title} by ${track.artist} added to your collection`
    });
    
    // Log the action
    addRecentAction({
      type: 'track_recommendation_used',
      description: `Added recommended track: ${track.title}`
    });
  };
  
  // Handle transition recommendation action
  const handleTransitionAction = (transition: TransitionRecommendation) => {
    // Open assistant with transition details
    openAssistant(`Here's how to perform the "${transition.name}" transition from "${transition.fromTrack}" to "${transition.toTrack}":\n\n${transition.description}\n\nThis is a ${transition.difficulty} difficulty ${transition.technique} technique. Would you like more details?`);
    
    // Log the action
    addRecentAction({
      type: 'transition_recommendation_used',
      description: `Used transition recommendation: ${transition.name}`
    });
  };
  
  // Handle learning resource action
  const handleResourceAction = (resource: LearningResource) => {
    // Open assistant with resource context
    if (resource.context) {
      openAssistant(resource.context);
    }
    
    // Log the action
    addRecentAction({
      type: 'learning_resource_used',
      description: `Accessed learning resource: ${resource.title}`
    });
  };
  
  // Render difficulty badge
  const renderDifficultyBadge = (difficulty: string) => {
    const colors = {
      easy: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      hard: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };
    
    return (
      <span className={`text-xs px-2 py-0.5 rounded-full ${colors[difficulty as keyof typeof colors]}`}>
        {difficulty}
      </span>
    );
  };
  
  return (
    <Card className={`shadow-md ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <Music className="h-4 w-4 mr-2" />
          Content Recommendations
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        <Tabs defaultValue="tracks" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 h-8">
            <TabsTrigger value="tracks" className="text-xs">Tracks</TabsTrigger>
            <TabsTrigger value="transitions" className="text-xs">Transitions</TabsTrigger>
            <TabsTrigger value="learning" className="text-xs">Learning</TabsTrigger>
          </TabsList>
          
          <TabsContent value="tracks" className="mt-2 space-y-2">
            {trackRecommendations.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                Add tracks to your mix to get recommendations
              </p>
            ) : (
              trackRecommendations.map(track => (
                <div key={track.id} className="border rounded-md p-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium">{track.title}</p>
                      <p className="text-xs text-muted-foreground">{track.artist}</p>
                      <div className="flex gap-2 mt-1">
                        {track.genre && <Badge variant="outline" className="text-xs">{track.genre}</Badge>}
                        {track.bpm && <Badge variant="outline" className="text-xs">{track.bpm} BPM</Badge>}
                        {track.key && <Badge variant="outline" className="text-xs">Key: {track.key}</Badge>}
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">{track.reason}</p>
                    </div>
                    <Button 
                      variant="outline" 
                      size="icon" 
                      className="h-6 w-6 flex-shrink-0"
                      onClick={() => handleTrackAction(track)}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </TabsContent>
          
          <TabsContent value="transitions" className="mt-2 space-y-2">
            {transitionRecommendations.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                Add at least two tracks to get transition recommendations
              </p>
            ) : (
              transitionRecommendations.map(transition => (
                <div key={transition.id} className="border rounded-md p-2">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="text-sm font-medium">{transition.name}</p>
                        {renderDifficultyBadge(transition.difficulty)}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">{transition.description}</p>
                      <div className="flex items-center text-xs text-muted-foreground mt-2">
                        <span className="truncate max-w-[100px]">{transition.fromTrack}</span>
                        <ArrowRight className="h-3 w-3 mx-1" />
                        <span className="truncate max-w-[100px]">{transition.toTrack}</span>
                      </div>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-6 text-xs ml-2 flex-shrink-0"
                      onClick={() => handleTransitionAction(transition)}
                    >
                      Learn
                    </Button>
                  </div>
                </div>
              ))
            )}
          </TabsContent>
          
          <TabsContent value="learning" className="mt-2 space-y-2">
            {learningResources.map(resource => (
              <div key={resource.id} className="border rounded-md p-2">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium">{resource.title}</p>
                    <p className="text-xs text-muted-foreground">{resource.description}</p>
                    <Badge variant="outline" className="text-xs mt-2 capitalize">{resource.type}</Badge>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-6 text-xs ml-2 flex-shrink-0"
                    onClick={() => handleResourceAction(resource)}
                  >
                    View
                  </Button>
                </div>
              </div>
            ))}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ContentRecommendations;
