import { useState, useEffect, useRef, useCallback } from 'react';
import { getAudioStreamUrl } from '@/services/api/audio';
import waveSurferVisualization from '@/components/mixes/timeline/services/WaveSurferVisualization';

// NOTE: This hook is deprecated as part of the single WaveSurfer architecture migration
// Use the enhanced timeline components instead

interface Track {
  id: string;
}

interface UseAudioWaveformsReturn {
  waveformInstances: Record<string, any>;
  waveformLoading: Record<string, boolean>;
  waveformError: Record<string, string | null>;
  setContainerRef: (trackId: string, element: HTMLElement | null) => void;
}

/**
 * Hook for managing audio waveforms in AudioPreview components
 * This is a simplified replacement for the deleted useTimelineWaveforms hook
 */
export function useAudioWaveforms(tracks: Track[]): UseAudioWaveformsReturn {
  const [waveformInstances, setWaveformInstances] = useState<Record<string, any>>({});
  const [waveformLoading, setWaveformLoading] = useState<Record<string, boolean>>({});
  const [waveformError, setWaveformError] = useState<Record<string, string | null>>({});
  const containerRefs = useRef<Record<string, HTMLElement>>({});
  const isCleaningUp = useRef(false);

  const setContainerRef = useCallback((trackId: string, element: HTMLElement | null) => {
    if (element) {
      containerRefs.current[trackId] = element;
      // Initialize waveform when container is set
      initializeWaveform(trackId, element);
    } else {
      // Clean up when container is removed
      delete containerRefs.current[trackId];
      cleanupWaveform(trackId);
    }
  }, []);

  const initializeWaveform = async (trackId: string, container: HTMLElement) => {
    if (waveformLoading[trackId] || waveformInstances[trackId]) {
      return; // Already loading or loaded
    }

    setWaveformLoading(prev => ({ ...prev, [trackId]: true }));
    setWaveformError(prev => ({ ...prev, [trackId]: null }));

    try {
      // Get audio URL
      const audioUrl = getAudioStreamUrl(trackId);
      
      // Create waveform using the timeline-new service
      const wavesurfer = await waveSurferVisualization.createWaveform(trackId, container, {
        url: audioUrl,
        waveColor: '#ddd',
        progressColor: '#999',
        cursorColor: '#999',
        barWidth: 2,
        barGap: 1,
        height: 60,
        interact: true,
        hideScrollbar: true,
      });

      setWaveformInstances(prev => ({ ...prev, [trackId]: wavesurfer }));
      setWaveformLoading(prev => ({ ...prev, [trackId]: false }));
    } catch (error) {
      console.error(`Failed to create waveform for track ${trackId}:`, error);
      setWaveformError(prev => ({ 
        ...prev, 
        [trackId]: error instanceof Error ? error.message : 'Failed to load audio'
      }));
      setWaveformLoading(prev => ({ ...prev, [trackId]: false }));
    }
  };

  const cleanupWaveform = (trackId: string) => {
    // Prevent cleanup during unmount to avoid setState calls
    if (isCleaningUp.current) {
      return;
    }

    // Clean up waveform instance
    if (waveformInstances[trackId]) {
      try {
        waveSurferVisualization.destroyWaveform(trackId);
      } catch (error) {
        console.warn(`Error cleaning up waveform for track ${trackId}:`, error);
      }
    }

    // Batch state updates to prevent multiple re-renders
    setWaveformInstances(prev => {
      const newInstances = { ...prev };
      delete newInstances[trackId];
      return newInstances;
    });
    setWaveformLoading(prev => {
      const newLoading = { ...prev };
      delete newLoading[trackId];
      return newLoading;
    });
    setWaveformError(prev => {
      const newError = { ...prev };
      delete newError[trackId];
      return newError;
    });
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isCleaningUp.current = true;
      Object.keys(containerRefs.current).forEach(trackId => {
        // Clean up waveform instances without setState calls
        if (waveformInstances[trackId]) {
          try {
            waveSurferVisualization.destroyWaveform(trackId);
          } catch (error) {
            console.warn(`Error cleaning up waveform for track ${trackId}:`, error);
          }
        }
      });
    };
  }, [waveformInstances]);

  return {
    waveformInstances,
    waveformLoading,
    waveformError,
    setContainerRef,
  };
}
