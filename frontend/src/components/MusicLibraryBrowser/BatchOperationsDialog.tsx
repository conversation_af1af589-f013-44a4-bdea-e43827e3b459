import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Track } from '@/types/api/tracks';
import { analyzeTrackEnhanced } from '@/services/api/cuePoints';
import { getTrack } from '@/services/api/tracks';
import { 
  Music, 
  BarChart3, 
  Grid, 
  BookMarked, 
  Tag, 
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface BatchOperationsDialogProps {
  tracks: Track[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: () => void;
}

type OperationType = 'analyze' | 'extract_beatgrid' | 'detect_segments' | 'tag' | 'export';

interface OperationOption {
  id: OperationType;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const BatchOperationsDialog: React.FC<BatchOperationsDialogProps> = ({
  tracks,
  open,
  onOpenChange,
  onComplete,
}) => {
  const [selectedOperation, setSelectedOperation] = useState<OperationType>('analyze');
  const [selectedTracks, setSelectedTracks] = useState<Record<string, boolean>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processedCount, setProcessedCount] = useState(0);
  const [failedCount, setFailedCount] = useState(0);
  const [tagValue, setTagValue] = useState('');
  const [exportFormat, setExportFormat] = useState('csv');
  const { toast } = useToast();

  // Reset state when dialog opens
  React.useEffect(() => {
    if (open) {
      setSelectedOperation('analyze');
      setSelectedTracks({});
      setIsProcessing(false);
      setProgress(0);
      setProcessedCount(0);
      setFailedCount(0);
      setTagValue('');
      setExportFormat('csv');
    }
  }, [open]);

  // Available operations
  const operations: OperationOption[] = [
    {
      id: 'analyze',
      label: 'Analyze Tracks',
      icon: <BarChart3 className="h-4 w-4" />,
      description: '🚀 Enhanced analysis: BPM, key, genre, rhythm, segmentation & cue points'
    },
    {
      id: 'extract_beatgrid',
      label: 'Extract Beat Grid',
      icon: <Grid className="h-4 w-4" />,
      description: 'Extract beat grid data for selected tracks'
    },
    {
      id: 'detect_segments',
      label: 'Detect Segments',
      icon: <BookMarked className="h-4 w-4" />,
      description: 'Detect segments (intro, verse, chorus, etc.) in selected tracks'
    },
    {
      id: 'tag',
      label: 'Add Tag',
      icon: <Tag className="h-4 w-4" />,
      description: 'Add a tag to selected tracks'
    },
    {
      id: 'export',
      label: 'Export Metadata',
      icon: <Music className="h-4 w-4" />,
      description: 'Export track metadata to CSV or JSON'
    }
  ];

  // Toggle all tracks
  const toggleAllTracks = (checked: boolean) => {
    const newSelectedTracks: Record<string, boolean> = {};
    tracks.forEach(track => {
      newSelectedTracks[track.id.toString()] = checked;
    });
    setSelectedTracks(newSelectedTracks);
  };

  // Toggle a single track
  const toggleTrack = (trackId: string, checked: boolean) => {
    setSelectedTracks(prev => ({
      ...prev,
      [trackId]: checked
    }));
  };

  // Get selected track IDs
  const getSelectedTrackIds = (): string[] => {
    return Object.entries(selectedTracks)
      .filter(([_, selected]) => selected)
      .map(([id]) => id);
  };

  // Process tracks
  const processSelectedTracks = async () => {
    const selectedIds = getSelectedTrackIds();
    
    if (selectedIds.length === 0) {
      toast({
        title: 'No tracks selected',
        description: 'Please select at least one track to process.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProcessedCount(0);
    setFailedCount(0);

    try {
      // Process tracks based on selected operation
      for (let i = 0; i < selectedIds.length; i++) {
        const trackId = selectedIds[i];
        try {
          switch (selectedOperation) {
            case 'analyze':
              await analyzeTrackEnhanced(trackId);
              break;
            case 'extract_beatgrid':
              // Call your beat grid extraction API here
              await analyzeTrackEnhanced(trackId); // Enhanced analysis includes beat grid
              break;
            case 'detect_segments':
              // Call your segment detection API here
              await analyzeTrackEnhanced(trackId); // Enhanced analysis includes Phase 5 segmentation
              break;
            case 'tag':
              // Call your tag API here
              // This is a placeholder - implement actual tag API call
              await new Promise(resolve => setTimeout(resolve, 500));
              break;
            case 'export':
              // This would typically be handled differently (all at once)
              // For now, we'll just simulate progress
              await new Promise(resolve => setTimeout(resolve, 200));
              break;
          }
          setProcessedCount(prev => prev + 1);
        } catch (error) {
          console.error(`Error processing track ${trackId}:`, error);
          setFailedCount(prev => prev + 1);
        }

        // Update progress
        setProgress(((i + 1) / selectedIds.length) * 100);
      }

      // If export operation, trigger download
      if (selectedOperation === 'export') {
        exportTrackData(selectedIds, exportFormat);
      }

      toast({
        title: 'Operation complete',
        description: `Processed ${processedCount} tracks with ${failedCount} failures.`,
        variant: failedCount > 0 ? 'destructive' : 'default',
      });

      // Wait a bit before closing to show completion
      setTimeout(() => {
        onComplete();
        onOpenChange(false);
      }, 1500);
    } catch (error) {
      console.error('Error processing tracks:', error);
      toast({
        title: 'Operation failed',
        description: 'There was an error processing the tracks.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Export track data
  const exportTrackData = async (trackIds: string[], format: string) => {
    try {
      // Fetch full track data for selected tracks
      const trackData = await Promise.all(
        trackIds.map(async id => {
          const response = await getTrack(id);
          return response.data;
        })
      );

      // Convert to the selected format
      let content: string;
      let mimeType: string;
      let extension: string;

      if (format === 'csv') {
        // Create CSV content
        const headers = ['id', 'title', 'artist', 'album', 'key', 'bpm', 'energy', 'duration', 'genre'];
        const rows = trackData.map(track => 
          headers.map(header => 
            track[header as keyof Track] !== undefined ? 
              `"${String(track[header as keyof Track]).replace(/"/g, '""')}"` : 
              '""'
          ).join(',')
        );
        content = [headers.join(','), ...rows].join('\n');
        mimeType = 'text/csv';
        extension = 'csv';
      } else {
        // JSON format
        content = JSON.stringify(trackData, null, 2);
        mimeType = 'application/json';
        extension = 'json';
      }

      // Create and trigger download
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `track_export_${new Date().toISOString().slice(0, 10)}.${extension}`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('Error exporting track data:', error);
      toast({
        title: 'Export failed',
        description: 'There was an error exporting the track data.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Batch Operations</DialogTitle>
          <DialogDescription>
            Apply operations to multiple tracks at once.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Operation selection */}
          <div className="grid grid-cols-1 gap-2">
            <Label>Select Operation</Label>
            <Select
              value={selectedOperation}
              onValueChange={(value) => setSelectedOperation(value as OperationType)}
              disabled={isProcessing}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an operation" />
              </SelectTrigger>
              <SelectContent>
                {operations.map(op => (
                  <SelectItem key={op.id} value={op.id}>
                    <div className="flex items-center gap-2">
                      {op.icon}
                      <span>{op.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              {operations.find(op => op.id === selectedOperation)?.description}
            </p>
          </div>

          {/* Operation-specific options */}
          {selectedOperation === 'tag' && (
            <div className="grid grid-cols-1 gap-2">
              <Label htmlFor="tag-value">Tag Value</Label>
              <Input
                id="tag-value"
                value={tagValue}
                onChange={(e) => setTagValue(e.target.value)}
                placeholder="Enter tag value"
                disabled={isProcessing}
              />
            </div>
          )}

          {selectedOperation === 'export' && (
            <div className="grid grid-cols-1 gap-2">
              <Label htmlFor="export-format">Export Format</Label>
              <Select
                value={exportFormat}
                onValueChange={setExportFormat}
                disabled={isProcessing}
              >
                <SelectTrigger id="export-format">
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="json">JSON</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Track selection */}
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center justify-between">
              <Label>Select Tracks</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={tracks.length > 0 && tracks.every(track => selectedTracks[track.id.toString()])}
                  onCheckedChange={toggleAllTracks}
                  disabled={isProcessing}
                />
                <Label htmlFor="select-all" className="text-sm cursor-pointer">Select All</Label>
              </div>
            </div>
            
            <div className="border rounded-md p-2 max-h-[200px] overflow-y-auto">
              {tracks.length === 0 ? (
                <p className="text-sm text-muted-foreground p-2">No tracks available</p>
              ) : (
                tracks.map(track => (
                  <div key={track.id} className="flex items-center space-x-2 py-1">
                    <Checkbox
                      id={`track-${track.id}`}
                      checked={selectedTracks[track.id.toString()] || false}
                      onCheckedChange={(checked) => toggleTrack(track.id.toString(), !!checked)}
                      disabled={isProcessing}
                    />
                    <Label htmlFor={`track-${track.id}`} className="text-sm cursor-pointer">
                      {track.artist} - {track.title}
                    </Label>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Progress */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing tracks...</span>
                <span>{processedCount + failedCount} / {getSelectedTrackIds().length}</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isProcessing}>
            Cancel
          </Button>
          <Button onClick={processSelectedTracks} disabled={isProcessing || getSelectedTrackIds().length === 0}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Process Selected Tracks'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BatchOperationsDialog;
