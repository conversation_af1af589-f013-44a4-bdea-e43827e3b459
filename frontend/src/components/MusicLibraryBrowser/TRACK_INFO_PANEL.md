# Track Info Panel Implementation

This document describes the implementation of the Track Info Panel in the Music Library Browser component.

## Overview

The Track Info Panel is a comprehensive display of track metadata, analysis, and tools that appears when a track is selected in the Music Library Browser. It provides a detailed view of all information associated with a track, including:

- Basic metadata (title, artist, album, genre, etc.)
- Audio characteristics (key, BPM, energy, duration)
- Analysis data (Mixed in Key, Librosa)
- Waveform visualization
- Cue points, segments, and loops
- Track tools and actions

## Components

### TrackInfoPanel.tsx

The main component that displays all track information. It includes:

- A tabbed interface for organizing different types of information
- Responsive design that works in both desktop and mobile layouts
- Integration with the track's audio playback
- Tools for track analysis and editing

### Integration with MusicLibraryBrowser

The Track Info Panel is integrated into the MusicLibraryBrowser component and appears in the right sidebar when a track is selected. It replaces the CollectionDetails component when a track is selected.

## State Management

The MusicLibraryBrowser component manages the selected track state:

```typescript
const [selectedTrackId, setSelectedTrackId] = useState<number | null>(null);
```

When a track is clicked in the ResizableTrackTable, it triggers a 'select' action that sets the selectedTrackId.

## Track Actions

The following track actions are supported:

- `select`: Select a track and show its details in the Track Info Panel
- `closeInfo`: Close the Track Info Panel
- `play`: Play the selected track
- `analyze`: Analyze the track using Librosa or Mixed in Key
- `editMetadata`: Edit the track's metadata
- `editBeatGrid`: Edit the track's beat grid
- `editCuePoints`: Edit the track's cue points
- `share`: Share the track

## Data Flow

1. User clicks on a track in the ResizableTrackTable
2. The handleTrackClick function in ResizableTrackTable calls onTrackAction with 'select' action
3. The handleTrackAction function in MusicLibraryBrowser sets the selectedTrackId
4. The TrackInfoPanel component is rendered with the selectedTrackId
5. TrackInfoPanel fetches the track data and displays it

## UI Structure

The Track Info Panel is organized into tabs:

1. **Overview**: Basic track information and metadata
2. **Waveform**: Waveform visualization with playback controls
3. **Analysis**: Detailed analysis data from Mixed in Key and Librosa
4. **Segments**: Cue points, segments, and loops
5. **Similar**: Similar tracks based on key, energy, and BPM
6. **Tools**: Track tools and actions

## Styling

The Track Info Panel uses the following UI components:

- Card, CardHeader, CardContent from @/components/ui/card
- Tabs, TabsList, TabsTrigger, TabsContent from @/components/ui/tabs
- Badge from @/components/ui/badge
- Button from @/components/ui/button
- ScrollArea from @/components/ui/scroll-area

## Integrated Features

The Track Info Panel integrates with several existing components and services:

1. **Audio Playback**: Uses SmartAudioPlayer for high-quality audio playback
2. **Track Analysis**: Integrates with TrackAnalysisButton for analyzing tracks with Librosa
3. **Waveform Visualization**: Uses OptimizedTrackWaveform for visualizing audio waveforms
4. **Similar Tracks**: Uses the compatibility API to find similar tracks based on key, energy, and BPM
5. **Cue Points and Segments**: Displays and allows editing of cue points and segments
6. **Batch Operations**: Provides a dialog for applying operations to multiple tracks at once

## Batch Operations

The Track Info Panel includes a Batch Operations feature that allows users to apply operations to multiple tracks at once:

1. **Track Selection**: Select multiple tracks from the current view
2. **Operation Types**:
   - Analyze Tracks: Extract BPM, key, energy, and other metadata
   - Extract Beat Grid: Extract beat grid data for selected tracks
   - Detect Segments: Detect segments (intro, verse, chorus, etc.) in selected tracks
   - Add Tag: Add a tag to selected tracks
   - Export Metadata: Export track metadata to CSV or JSON
3. **Progress Tracking**: Visual progress indicator for batch operations
4. **Completion Handling**: Automatic refresh of track data after operations complete

## Future Enhancements

Potential future enhancements for the Track Info Panel:

1. **Direct Editing**: Add inline editing of metadata, cue points, and segments
2. **Enhanced Visualization**: Add more detailed visualizations for beat grids and segments
3. **AI Integration**: Enhance AI-powered track analysis and recommendations
4. **Advanced Export Options**: Add more export formats and customization options

## Usage

The Track Info Panel is automatically shown when a track is selected in the Music Library Browser. To close it, click the close button or select another track.

## Implementation Details

### Track Selection

Track selection is handled by the ResizableTrackTable component:

```typescript
const handleTrackClick = (index: number) => {
  setSelectedIndex(index);
  // Trigger the 'select' action to show the track info panel
  if (index >= 0 && index < tracks.length) {
    onTrackAction(tracks[index].id, 'select');
  }
};
```

### Track Info Panel Rendering

The Track Info Panel is conditionally rendered in the MusicLibraryBrowser component:

```tsx
{/* Show track info panel when a track is selected */}
{selectedTrackId ? (
  <div className="w-80 border-l overflow-y-auto">
    <TrackInfoPanel
      trackId={selectedTrackId}
      onClose={() => setSelectedTrackId(null)}
    />
  </div>
) : (
  /* Only show collection details for collections (not playlists) when no track is selected */
  selectedPlaylist?.isFolder && selectedPlaylistId !== 1 && (
    <div className="w-80 p-4 border-l overflow-y-auto">
      <CollectionDetails
        collectionId={selectedPlaylistId?.toString() || ''}
        trackCount={filteredTracks.length}
      />
    </div>
  )
)}
```

### Data Fetching

The TrackInfoPanel component fetches track data when the trackId changes:

```typescript
useEffect(() => {
  if (!trackId) {
    setTrack(null);
    return;
  }

  const fetchTrackData = async () => {
    setIsLoading(true);
    try {
      const response = await getTrack(trackId.toString());
      setTrack(response.data);
    } catch (error) {
      console.error('Error fetching track data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load track details',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  fetchTrackData();
}, [trackId]);
```
