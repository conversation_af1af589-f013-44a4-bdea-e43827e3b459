import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Music,
  Info,
  AudioWaveform,
  BarChart3,
  Grid,
  BookMarked,
  Repeat,
  Tag,
  Play,
  Pause,
  Download,
  Edit,
  Trash,
  Share,
  Plus,
  ListFilter,
  Settings,
  FolderOpen,
  ExternalLink
} from 'lucide-react';
import { Track } from '@/types/api/tracks';
import { getTrack } from '@/services/api/tracks';
import { formatDuration } from '@/utils/formatters';
import { getKeyBadgeColor, getBpmBadgeColor, getEnergyBadgeColor, getSourceBadge, getSourceBadgeColor } from '@/components/MusicLibraryBrowser/ResizableTrackTable';
import OptimizedTrackWaveform from '@/components/tracks/waveform/OptimizedTrackWaveform';
import { toast } from '@/components/ui/use-toast';
import BasicAudioPlayer from '@/components/audio/BasicAudioPlayer';
import TrackAnalysisButton from '@/components/tracks/ui/TrackAnalysisButton';
import TrackAnalysisStatusBadge from '@/components/tracks/ui/TrackAnalysisStatusBadge';
import TrackAnalysisManager from '@/components/tracks/ui/TrackAnalysisManager';
import { getCompatibleTracks } from '@/services/api/compatibility';
import BatchOperationsDialog from './BatchOperationsDialog';
import { formatCoverArtUrl } from '@/utils/apiUrlHelper';
import { getTrackAnalysisStatus, getFullAnalysisData } from '@/services/api/trackAnalysisManagement';
import { Activity } from 'lucide-react';

// Add global type declaration for toneAudioEngine
declare global {
  interface Window {
    toneAudioEngine?: {
      playTrack: (trackId: string) => void;
      pauseTrack: (trackId: string) => void;
    };
  }
}

interface TrackInfoPanelProps {
  trackId: number | null;
  onClose?: () => void;
  allTracks?: Track[]; // All tracks in the current view for batch operations
}

const TrackInfoPanel: React.FC<TrackInfoPanelProps> = ({ trackId, onClose, allTracks = [] }) => {
  const [track, setTrack] = useState<Track | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [similarTracks, setSimilarTracks] = useState<Track[]>([]);
  const [isFetchingSimilar, setIsFetchingSimilar] = useState(false);
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);

  // 🚀 NEW: Enhanced energy profile data
  const [fullAnalysisData, setFullAnalysisData] = useState<any>(null);
  const [loadingAnalysis, setLoadingAnalysis] = useState(false);

  // Debug logging
  console.log('TrackInfoPanel rendered with trackId:', trackId);

  // Fetch track data when trackId changes
  useEffect(() => {
    console.log('TrackInfoPanel useEffect triggered with trackId:', trackId);

    if (!trackId) {
      console.log('No trackId provided, clearing track data');
      setTrack(null);
      setSimilarTracks([]);
      setFullAnalysisData(null);
      return;
    }

    const fetchTrackData = async () => {
      console.log('Fetching track data for trackId:', trackId);
      setIsLoading(true);
      setLoadingAnalysis(true);
      try {
        console.log('Calling getTrack API with trackId:', trackId.toString());

        // Fetch both track data and full analysis data
        const [response, fullAnalysis] = await Promise.all([
          getTrack(trackId.toString()),
          getFullAnalysisData(trackId).catch(() => null) // Don't fail if analysis data is not available
        ]);

        console.log('getTrack API response:', response);
        console.log('Full analysis data:', fullAnalysis);

        if (response.data) {
          console.log('Track data received:', response.data);
          setTrack(response.data);
          setFullAnalysisData(fullAnalysis);
          // Fetch similar tracks after getting track data
          fetchSimilarTracks(response.data);
        } else {
          // Handle case when track is not found (null data)
          console.warn(`Track with ID ${trackId} not found in the database.`);
          setTrack(null);
          setSimilarTracks([]);

          // Show a user-friendly toast notification
          toast({
            title: 'Track Not Found',
            description: `The track with ID ${trackId} could not be found.`,
            variant: 'default',
          });
        }
      } catch (error) {
        console.error('Error fetching track data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load track details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
        setLoadingAnalysis(false);
      }
    };

    fetchTrackData();
  }, [trackId]);

  // Fetch similar tracks based on the current track
  const fetchSimilarTracks = async (currentTrack: Track) => {
    if (!currentTrack || !currentTrack.key) return;

    setIsFetchingSimilar(true);
    try {
      // Use the compatibility API to find similar tracks
      const params = {
        key: currentTrack.key,
        energy: currentTrack.energy || 0.5,
        limit: 5
      };

      const similar = await getCompatibleTracks(params);

      // Filter out the current track
      const filteredTracks = similar.filter(t => t.id !== currentTrack.id);
      setSimilarTracks(filteredTracks);
    } catch (error) {
      console.error('Error fetching similar tracks:', error);
    } finally {
      setIsFetchingSimilar(false);
    }
  };

  // Handle play/pause
  const togglePlayback = () => {
    if (track) {
      setIsPlaying(!isPlaying);
      // Use the existing audio playback functionality
      if (isPlaying) {
        // If currently playing, pause
        if (window.toneAudioEngine) {
          window.toneAudioEngine.pauseTrack(track.id.toString());
        } else {
          console.log('Pausing track via standard audio API');
          // Fallback to standard audio API
          const audioElement = document.querySelector(`audio[data-track-id="${track.id}"]`) as HTMLAudioElement;
          if (audioElement) {
            audioElement.pause();
          }
        }
      } else {
        // If currently paused, play
        if (window.toneAudioEngine) {
          window.toneAudioEngine.playTrack(track.id.toString());
        } else {
          console.log('Playing track via standard audio API');
          // Fallback to standard audio API
          const audioElement = document.querySelector(`audio[data-track-id="${track.id}"]`) as HTMLAudioElement;
          if (audioElement) {
            audioElement.play().catch(err => console.error('Error playing audio:', err));
          }
        }
      }
    }
  };

  // If no track is selected
  if (!trackId || !track) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>Track Information</CardTitle>
          <CardDescription>Select a track to view details</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[calc(100%-5rem)]">
          <div className="text-center text-muted-foreground">
            <Music className="mx-auto h-12 w-12 opacity-50 mb-4" />
            <p>No track selected</p>
            <p className="text-sm mt-2">Click on a track in the list to view its details</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start gap-2">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-xl truncate">{track.title}</CardTitle>
            <CardDescription className="text-base truncate">{track.artist}</CardDescription>
          </div>
          {onClose && (
            <Button variant="ghost" size="icon" className="flex-shrink-0" onClick={onClose}>
              <Trash className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-300px)] min-h-[400px]">
          <div className="p-6">
            <Accordion type="multiple" defaultValue={["info"]} className="w-full">
              {/* Track Info Section */}
              <AccordionItem value="info">
                <AccordionTrigger className="py-3">
                  <span className="flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    Track Info
                  </span>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    {/* Cover Art - Centered */}
                    <div className="flex justify-center">
                      {track.coverArtUrl || track.albumArt || track.imageUrl || track.file_path ? (
                        <img
                          src={formatCoverArtUrl(track.coverArtUrl || track.albumArt || track.imageUrl || track.file_path)}
                          alt={`${track.title} cover`}
                          className="w-40 h-40 object-cover rounded-lg shadow-lg"
                          onError={(e) => {
                            // Fallback to placeholder if image fails to load
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-40 h-40 bg-gradient-to-br from-muted to-muted/50 rounded-lg flex items-center justify-center ${track.coverArtUrl || track.albumArt || track.imageUrl || track.file_path ? 'hidden' : ''}`}>
                        <Music className="h-16 w-16 text-muted-foreground opacity-50" />
                      </div>
                    </div>

                    {/* Essential Metadata - Clean Grid */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 bg-muted/20 rounded-md">
                        <h4 className="text-xs font-medium text-muted-foreground mb-1">Key</h4>
                        <div className="flex items-center justify-center gap-1">
                          <Badge variant="secondary" className={`${getKeyBadgeColor(track.key)} text-sm`}>
                            {track.key || 'Unknown'}
                          </Badge>
                          <span className={getSourceBadgeColor(getSourceBadge(track, 'key').source)} title={`Source: ${getSourceBadge(track, 'key').source === 'M' ? 'Mixed in Key' : getSourceBadge(track, 'key').source === 'L' ? 'Librosa' : 'Default'}`}>
                            {getSourceBadge(track, 'key').source}
                          </span>
                        </div>
                        {track.key_source && (
                          <p className="text-xs text-muted-foreground mt-1">
                            via {track.key_source}
                          </p>
                        )}
                      </div>
                      <div className="text-center p-3 bg-muted/20 rounded-md">
                        <h4 className="text-xs font-medium text-muted-foreground mb-1">BPM</h4>
                        <div className="flex items-center justify-center gap-1">
                          <Badge variant="secondary" className={`${getBpmBadgeColor(track.bpm)} text-sm`}>
                            {Math.round(track.bpm) || 'Unknown'}
                          </Badge>
                          <span className={getSourceBadgeColor(getSourceBadge(track, 'bpm').source)} title={`Source: ${getSourceBadge(track, 'bpm').source === 'M' ? 'Mixed in Key' : getSourceBadge(track, 'bpm').source === 'L' ? 'Librosa' : 'Default'}`}>
                            {getSourceBadge(track, 'bpm').source}
                          </span>
                        </div>
                      </div>
                      <div className="text-center p-3 bg-muted/20 rounded-md">
                        <h4 className="text-xs font-medium text-muted-foreground mb-1">Energy</h4>
                        <div className="flex items-center justify-center gap-1">
                          <Badge variant="secondary" className={`${getEnergyBadgeColor(track.energy || 0)} text-sm`}>
                            {track.energy ? (track.energy * 10).toFixed(1) : (track.mixed_in_key_energy ? (track.mixed_in_key_energy * 10).toFixed(1) : 'Unknown')}
                          </Badge>
                          <span className={getSourceBadgeColor(getSourceBadge(track, 'energy').source)} title={`Source: ${getSourceBadge(track, 'energy').source === 'M' ? 'Mixed in Key' : getSourceBadge(track, 'energy').source === 'L' ? 'Librosa' : 'Default'}`}>
                            {getSourceBadge(track, 'energy').source}
                          </span>
                        </div>
                        {(track.energy_source || track.mixed_in_key_energy) && (
                          <p className="text-xs text-muted-foreground mt-1">
                            via {track.energy_source || 'Mixed in Key'}
                          </p>
                        )}
                      </div>
                      <div className="text-center p-3 bg-muted/20 rounded-md">
                        <h4 className="text-xs font-medium text-muted-foreground mb-1">Duration</h4>
                        <Badge variant="secondary" className="text-sm">
                          {formatDuration(track.duration)}
                        </Badge>
                      </div>
                    </div>

                    {/* 🚀 NEW: Multi-Dimensional Energy Profile */}
                    {fullAnalysisData?.analysis_data?.energy_profile && (
                      <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg border">
                        <div className="flex items-center gap-2 mb-3">
                          <Activity className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-semibold text-blue-900 dark:text-blue-100">Enhanced Energy Profile</span>
                          <Badge variant="outline" className="text-xs">
                            {(fullAnalysisData.analysis_data.energy_profile.confidence * 100).toFixed(0)}% confidence
                          </Badge>
                        </div>

                        {/* 3D Emotion Model */}
                        <div className="grid grid-cols-3 gap-3 mb-3">
                          <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
                            <div className="text-xs text-muted-foreground">Arousal</div>
                            <div className="text-sm font-bold text-red-600">
                              {((fullAnalysisData.analysis_data.energy_profile.arousal + 1) * 50).toFixed(0)}%
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {fullAnalysisData.analysis_data.energy_profile.arousal > 0 ? 'Energetic' : 'Calm'}
                            </div>
                          </div>
                          <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
                            <div className="text-xs text-muted-foreground">Valence</div>
                            <div className="text-sm font-bold text-green-600">
                              {((fullAnalysisData.analysis_data.energy_profile.valence + 1) * 50).toFixed(0)}%
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {fullAnalysisData.analysis_data.energy_profile.valence > 0 ? 'Positive' : 'Negative'}
                            </div>
                          </div>
                          <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
                            <div className="text-xs text-muted-foreground">Dominance</div>
                            <div className="text-sm font-bold text-purple-600">
                              {((fullAnalysisData.analysis_data.energy_profile.dominance + 1) * 50).toFixed(0)}%
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {fullAnalysisData.analysis_data.energy_profile.dominance > 0 ? 'Assertive' : 'Submissive'}
                            </div>
                          </div>
                        </div>

                        {/* Mood Tags */}
                        <div className="flex flex-wrap gap-1 mb-3">
                          {fullAnalysisData.analysis_data.energy_profile.mood_tags?.map((tag, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs capitalize bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        {/* Musical Characteristics */}
                        {fullAnalysisData.analysis_data.energy_profile.musical_characteristics && (
                          <div className="grid grid-cols-3 gap-2 text-xs">
                            <div className="text-center">
                              <div className="text-muted-foreground">Acousticness</div>
                              <div className="font-medium">
                                {(fullAnalysisData.analysis_data.energy_profile.musical_characteristics.acousticness * 100).toFixed(0)}%
                              </div>
                            </div>
                            <div className="text-center">
                              <div className="text-muted-foreground">Vocal Content</div>
                              <div className="font-medium">
                                {fullAnalysisData.analysis_data.energy_profile.musical_characteristics.instrumentalness > 0.7 ? 'Instrumental' : 'Vocal'}
                              </div>
                            </div>
                            <div className="text-center">
                              <div className="text-muted-foreground">Danceability</div>
                              <div className="font-medium">
                                {(fullAnalysisData.analysis_data.energy_profile.danceability * 100).toFixed(0)}%
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Audio Player */}
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <BasicAudioPlayer
                        trackId={track.id}
                        trackTitle={`${track.artist} - ${track.title}`}
                        compact={true}
                      />
                    </div>

                    {/* Show in Finder Button */}
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        // Show in Finder functionality
                        const filePath = track.filepath || track.file_path || track.filePath;
                        if (filePath) {
                          // Create a proper API endpoint for showing files
                          fetch(`/api/v1/files/show-in-finder`, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ path: filePath }),
                          })
                          .then(response => {
                            if (response.ok) {
                              toast({
                                title: 'File location opened',
                                description: 'The file location has been opened in Finder.',
                              });
                            } else {
                              toast({
                                title: 'Error',
                                description: 'Could not open file location.',
                                variant: 'destructive',
                              });
                            }
                          })
                          .catch(error => {
                            console.error('Error opening file location:', error);
                            toast({
                              title: 'Error',
                              description: 'Could not open file location.',
                              variant: 'destructive',
                            });
                          });
                        }
                      }}
                    >
                      <FolderOpen className="h-4 w-4 mr-2" />
                      Show in Finder
                    </Button>

                    <Separator />

                    {/* Additional Metadata */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {track.album && (
                        <div>
                          <h4 className="text-xs font-medium text-muted-foreground mb-1">Album</h4>
                          <p className="text-sm">{track.album}</p>
                        </div>
                      )}
                      {track.genre && (
                        <div>
                          <h4 className="text-xs font-medium text-muted-foreground mb-1">Genre</h4>
                          <p className="text-sm">{track.genre}</p>
                        </div>
                      )}
                      {track.year && (
                        <div>
                          <h4 className="text-xs font-medium text-muted-foreground mb-1">Year</h4>
                          <p className="text-sm">{track.year}</p>
                        </div>
                      )}
                      {track.label && (
                        <div>
                          <h4 className="text-xs font-medium text-muted-foreground mb-1">Label</h4>
                          <p className="text-sm">{track.label}</p>
                        </div>
                      )}
                    </div>

                    {track.tags && track.tags.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium text-muted-foreground mb-2">Tags</h4>
                        <div className="flex flex-wrap gap-2">
                          {track.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {track.comment && (
                      <div>
                        <h4 className="text-xs font-medium text-muted-foreground mb-1">Comment</h4>
                        <p className="text-sm text-muted-foreground">{track.comment}</p>
                      </div>
                    )}

                    <Separator />

                    {/* Similar Tracks */}
                    <div>
                      <h4 className="text-sm font-medium mb-3">Similar Tracks</h4>
                      {isFetchingSimilar ? (
                        <div className="flex justify-center items-center py-6">
                          <div className="animate-spin mr-2">
                            <AudioWaveform className="h-4 w-4" />
                          </div>
                          <span className="text-sm text-muted-foreground">Finding similar tracks...</span>
                        </div>
                      ) : similarTracks.length > 0 ? (
                        <div className="space-y-2">
                          {similarTracks.slice(0, 3).map((similarTrack) => (
                            <div
                              key={similarTrack.id}
                              className="flex items-center justify-between p-3 bg-muted/30 rounded-md hover:bg-muted/50 transition-colors"
                            >
                              <div className="flex items-center gap-3">
                                {similarTrack.coverArtUrl || similarTrack.albumArt || similarTrack.imageUrl ? (
                                  <img
                                    src={formatCoverArtUrl(similarTrack.coverArtUrl || similarTrack.albumArt || similarTrack.imageUrl)}
                                    alt={`${similarTrack.title} cover`}
                                    className="w-8 h-8 object-cover rounded"
                                  />
                                ) : (
                                  <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
                                    <Music className="h-4 w-4 text-muted-foreground opacity-50" />
                                  </div>
                                )}
                                <div>
                                  <div className="font-medium text-sm truncate max-w-[140px]">{similarTrack.title}</div>
                                  <div className="text-xs text-muted-foreground truncate max-w-[140px]">{similarTrack.artist}</div>
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                <Badge variant="outline" className="text-xs px-1 py-0">
                                  {similarTrack.key || '?'}
                                </Badge>
                                <Badge variant="outline" className="text-xs px-1 py-0">
                                  {Math.round(similarTrack.bpm) || '?'}
                                </Badge>
                              </div>
                            </div>
                          ))}
                          {similarTracks.length > 3 && (
                            <Button variant="ghost" size="sm" className="w-full">
                              <ExternalLink className="h-4 w-4 mr-2" />
                              View all {similarTracks.length} similar tracks
                            </Button>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-6 text-muted-foreground bg-muted/20 rounded-md">
                          <Music className="mx-auto h-6 w-6 opacity-50 mb-2" />
                          <p className="text-sm">No similar tracks found</p>
                          <p className="text-xs mt-1">Try analyzing this track first</p>
                        </div>
                      )}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>



              {/* Analysis & Tools Section */}
              <AccordionItem value="analysis">
                <AccordionTrigger className="py-3">
                  <span className="flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analysis & Tools
                  </span>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-6">
                {/* Analysis Status */}
                <div>
                  <h3 className="text-base font-medium mb-3">Analysis Status</h3>
                  <TrackAnalysisStatusBadge track={track} variant="detailed" />
                </div>

                {/* Quick Actions */}
                <div>
                  <h3 className="text-base font-medium mb-3">Quick Actions</h3>
                  <div className="grid grid-cols-1 gap-3">
                    <TrackAnalysisButton
                      trackId={track.id}
                      variant="outline"
                      className="w-full justify-start"
                      onAnalysisComplete={() => {
                        // Refresh track data after analysis
                        getTrack(track.id.toString()).then(response => {
                          setTrack(response.data);
                          toast({
                            title: 'Analysis complete',
                            description: 'Track analysis has been completed successfully.',
                          });
                        });
                      }}
                    />
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => setIsBatchDialogOpen(true)}
                    >
                      <ListFilter className="h-4 w-4 mr-2" />
                      Batch Operations
                    </Button>
                  </div>
                </div>

                {/* Analysis Data Comparison */}
                <div>
                  <h3 className="text-base font-medium mb-3">Analysis Data</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Mixed in Key Analysis */}
                    <div>
                      <h4 className="text-sm font-medium mb-3">Mixed in Key</h4>
                      <div className="space-y-3">
                        <div>
                          <span className="text-xs text-muted-foreground">Key</span>
                          <div>
                            <Badge variant="secondary" className={getKeyBadgeColor(track.mixed_in_key_key || '')}>
                              {track.mixed_in_key_key || 'Not analyzed'}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground">Energy</span>
                          <div>
                            <Badge variant="secondary" className={getEnergyBadgeColor(track.mixed_in_key_energy || 0)}>
                              {track.mixed_in_key_energy ? (track.mixed_in_key_energy * 10).toFixed(1) : 'Not analyzed'}
                            </Badge>
                          </div>
                        </div>
                        {track.mixed_in_key_version && (
                          <div>
                            <span className="text-xs text-muted-foreground">Version</span>
                            <p className="text-sm">{track.mixed_in_key_version}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Librosa Analysis */}
                    <div>
                      <h4 className="text-sm font-medium mb-3">Librosa</h4>
                      <div className="space-y-3">
                        <div>
                          <span className="text-xs text-muted-foreground">Key</span>
                          <div>
                            <Badge variant="secondary" className={getKeyBadgeColor(track.librosa_key || '')}>
                              {track.librosa_key || 'Not analyzed'}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground">Energy</span>
                          <div>
                            <Badge variant="secondary" className={getEnergyBadgeColor(track.librosa_energy || 0)}>
                              {track.librosa_energy ? (track.librosa_energy * 10).toFixed(1) : 'Not analyzed'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Current Active Source */}
                  <div className="mt-4 p-3 bg-muted/20 rounded-md">
                    <h4 className="text-sm font-medium mb-2">Currently Active</h4>
                    <div className="flex gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Key Source: </span>
                        <Badge variant="outline" className="text-xs">
                          {track.key_source || 'Default'}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Energy Source: </span>
                        <Badge variant="outline" className="text-xs">
                          {track.energy_source || 'Default'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Audio Metrics */}
                {(track.peakDb !== undefined || track.averageDb !== undefined) && (
                  <div>
                    <h3 className="text-base font-medium mb-3">Audio Metrics</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {track.peakDb !== undefined && (
                        <div>
                          <span className="text-xs text-muted-foreground">Peak dB</span>
                          <p className="text-sm font-medium">{track.peakDb.toFixed(1)} dB</p>
                        </div>
                      )}
                      {track.averageDb !== undefined && (
                        <div>
                          <span className="text-xs text-muted-foreground">Average dB</span>
                          <p className="text-sm font-medium">{track.averageDb.toFixed(1)} dB</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Data Management */}
                <div>
                  <TrackAnalysisManager
                    track={track}
                    onTrackUpdate={(updatedTrack) => {
                      setTrack(updatedTrack);
                      toast({
                        title: 'Track Updated',
                        description: 'Track analysis data has been updated.',
                      });
                    }}
                    onAnalysisComplete={() => {
                      // Refresh track data after analysis
                      getTrack(track.id.toString()).then(response => {
                        setTrack(response.data);
                        toast({
                          title: 'Analysis Complete',
                          description: 'Track analysis has been completed successfully.',
                        });
                      });
                    }}
                  />
                </div>



                {/* Batch Operations Dialog */}
                <BatchOperationsDialog
                  tracks={allTracks.length > 0 ? allTracks : (track ? [track] : [])}
                  open={isBatchDialogOpen}
                  onOpenChange={setIsBatchDialogOpen}
                  onComplete={() => {
                    // Refresh track data after batch operations
                    if (track) {
                      getTrack(track.id.toString()).then(response => {
                        setTrack(response.data);
                        toast({
                          title: 'Batch operations complete',
                          description: 'Track data has been refreshed.',
                        });
                      });
                    }
                  }}
                />
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default TrackInfoPanel;
