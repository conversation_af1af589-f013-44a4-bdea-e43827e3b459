import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { cn } from "../lib/utils";

interface MixingRulesPanelProps {
  enabledRules: string[];
  onRulesChange: (rules: string[]) => void;
  isMinimal?: boolean;
}

// Defining the rule values as a type for better type checking
type RuleValue =
  | "perfect"
  | "adjacent"
  | "relative"
  | "same_number"
  | "energy_boost"
  | "energy_drop"
  | "diagonal_mix"
  | "harmonic_third"
  | "major_third"
  | "jaws_mix"
  | "pay_attention";

// Defining the structure of transition groups
type TransitionGroups = {
  [key: string]: {
    name: string;
    description: string;
    rules: ReadonlyArray<RuleValue>;
  };
};

const TRANSITION_GROUPS: TransitionGroups = {
  basic: {
    name: "Basic Transitions",
    description: "Essential mixing transitions",
    rules: ["perfect", "adjacent", "relative", "same_number"],
  },
  energy: {
    name: "Energy Control",
    description: "Transitions that affect energy levels",
    rules: ["energy_boost", "energy_drop"],
  },
  advanced: {
    name: "Advanced Transitions",
    description: "Complex harmonic mixing patterns",
    rules: ["diagonal_mix", "harmonic_third", "major_third"],
  },
  experimental: {
    name: "Experimental",
    description: "Unconventional mixing patterns",
    rules: ["jaws_mix", "pay_attention"],
  },
};

const TRANSITION_TYPES = [
  { value: "perfect", label: "Perfect Match", description: "Same key (e.g., 8A to 8A)" },
  { value: "adjacent", label: "Adjacent Match", description: "One step clockwise/counter-clockwise" },
  { value: "relative", label: "Relative Key", description: "Switch between Major/Minor" },
  { value: "same_number", label: "Same Number", description: "Same number, different letter" },
  { value: "energy_boost", label: "Energy Boost", description: "+1 number, same letter" },
  { value: "energy_drop", label: "Energy Drop", description: "-1 number, same letter" },
  { value: "diagonal_mix", label: "Diagonal Mix", description: "Diagonal on the wheel" },
  { value: "harmonic_third", label: "Harmonic Third", description: "Jump 3 steps" },
  { value: "jaws_mix", label: "Jaws Mix", description: "Jump 6 steps" },
  { value: "pay_attention", label: "Pay Attention", description: "Jump 2 steps" },
  { value: "major_third", label: "Major Third", description: "Jump 4 steps" },
] as const;

export const MixingRulesPanel: React.FC<MixingRulesPanelProps> = ({
  enabledRules,
  onRulesChange,
  isMinimal = false,
}) => {
  const handlePresetClick = (rules: string[]) => {
    onRulesChange(rules);
  };

  const handleRuleToggle = (rule: string, checked: boolean) => {
    if (checked) {
      onRulesChange([...enabledRules, rule]);
    } else {
      onRulesChange(enabledRules.filter((r) => r !== rule));
    }
  };

  const handleGroupToggle = (groupRules: ReadonlyArray<RuleValue>) => {
    const allEnabled = groupRules.every((rule) => enabledRules.includes(rule));
    if (allEnabled) {
      onRulesChange(enabledRules.filter((rule) => !groupRules.includes(rule as RuleValue)));
    } else {
      // Convert the readonly array to a regular array for the spread operator
      const rulesArray = [...groupRules] as string[];
      const newRules = new Set([...enabledRules, ...rulesArray]);
      onRulesChange(Array.from(newRules));
    }
  };

  return (
    <div className={cn("w-full", isMinimal ? "text-xs" : "text-sm")}>
      <div className="flex flex-wrap gap-2 mb-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size={isMinimal ? "sm" : "default"} className="h-7">
              Quick Select
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>Preset Combinations</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handlePresetClick([...TRANSITION_GROUPS.basic.rules] as string[])}>
              Basic Transitions Only
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handlePresetClick([
                ...TRANSITION_GROUPS.basic.rules,
                ...TRANSITION_GROUPS.energy.rules,
              ] as string[])}
            >
              Basic + Energy Control
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePresetClick(TRANSITION_TYPES.map(t => t.value))}>
              Enable All
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePresetClick([])}>
              Clear All
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Collapsible defaultOpen={!isMinimal}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size={isMinimal ? "sm" : "default"}
            className="flex items-center justify-between w-full h-7"
          >
            <span>Mixing Rules</span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2 space-y-4">
          {Object.entries(TRANSITION_GROUPS).map(([key, group]) => (
            <div key={key} className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className={cn(
                    "font-medium",
                    isMinimal ? "text-xs" : "text-sm"
                  )}>
                    {group.name}
                  </h4>
                  {!isMinimal && (
                    <p className="text-xs text-muted-foreground">{group.description}</p>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6"
                  onClick={() => handleGroupToggle(group.rules)}
                >
                  {group.rules.every((rule) => enabledRules.includes(rule))
                    ? "Disable All"
                    : "Enable All"}
                </Button>
              </div>
              <div className="grid grid-cols-1 gap-2 pl-2">
                {TRANSITION_TYPES
                  .filter((type) => group.rules.includes(type.value as RuleValue))
                  .map((rule) => (
                    <div key={rule.value} className="flex items-start space-x-2">
                      <Checkbox
                        id={`rule-${rule.value}`}
                        checked={enabledRules.includes(rule.value)}
                        onCheckedChange={(checked) =>
                          handleRuleToggle(rule.value, checked === true)
                        }
                        className={isMinimal ? "h-3 w-3 mt-0.5" : ""}
                      />
                      <div className="grid gap-1.5 leading-none">
                        <label
                          htmlFor={`rule-${rule.value}`}
                          className={cn(
                            "font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                            isMinimal ? "text-xs" : "text-sm"
                          )}
                        >
                          {rule.label}
                        </label>
                        {!isMinimal && (
                          <p className="text-xs text-muted-foreground">
                            {rule.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default MixingRulesPanel;
