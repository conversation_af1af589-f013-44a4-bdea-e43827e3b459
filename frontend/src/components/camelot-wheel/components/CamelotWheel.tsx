import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { HelpCircle, Maximize2, Minimize2, Disc } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { CamelotWheelRules, TransitionType } from "../lib/camelot-wheel-rules";
import { getWheelColor, getTransitionColor, cn } from "../lib/utils";
import { convertKey } from "../lib/key-conversion";
import MixingRulesPanel from "./MixingRulesPanel";

const MINOR_KEYS = Array.from({ length: 12 }, (_, i) => `${i + 1}A`);
const MAJOR_KEYS = Array.from({ length: 12 }, (_, i) => `${i + 1}B`);

type ViewMode = "minimal" | "full";

interface CamelotWheelProps {
  onKeySelect?: (key: string) => void;
  initialKey?: string;
  className?: string;
  defaultViewMode?: ViewMode;
  isSidebar?: boolean;
  defaultDisplayMode?: "camelot" | "standard";
  // Added for compatibility with the sidebar
  selectedKey?: string;
  compact?: boolean;
  showRules?: boolean;
}

const CamelotWheel: React.FC<CamelotWheelProps> = ({
  onKeySelect,
  initialKey = "8A",
  className = "",
  defaultViewMode = "full",
  isSidebar = false,
  defaultDisplayMode = "camelot",
  // Added for compatibility with the sidebar
  selectedKey: propSelectedKey,
  compact = false,
  showRules = true,
}) => {
  // State
  const [selectedKey, setSelectedKey] = useState<string>(propSelectedKey || initialKey);
  const [secondKey, setSecondKey] = useState<string | null>(null);
  const [bpm1, setBpm1] = useState<number | null>(null);
  const [bpm2, setBpm2] = useState<number | null>(null);
  const [enabledRules, setEnabledRules] = useState<string[]>([
    "perfect",
    "adjacent",
    "relative",
    "same_number",
  ]);
  const [compatibleKeys, setCompatibleKeys] = useState<string[]>([]);
  const [transitionInfo, setTransitionInfo] = useState<{
    score: number;
    type: string;
    description: string;
  } | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>(compact ? "minimal" : defaultViewMode);
  const [displayMode, setDisplayMode] = useState<"camelot" | "standard">(defaultDisplayMode);
  const [wheelSize, setWheelSize] = useState<number>(compact ? 120 : 240);
  const [hoveredKey, setHoveredKey] = useState<string | null>(null);

  // Refs
  const wheelRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Rules
  const camelotRules = new CamelotWheelRules();

  // Update selectedKey when propSelectedKey changes
  useEffect(() => {
    if (propSelectedKey !== undefined) {
      setSelectedKey(propSelectedKey);
    }
  }, [propSelectedKey]);

  // Update compatible keys when selected key or enabled rules change
  useEffect(() => {
    if (selectedKey) {
      const compatible = Array.from(
        camelotRules.getCompatibleKeys(selectedKey, enabledRules)
      );
      setCompatibleKeys(compatible);
    }
  }, [selectedKey, enabledRules]);

  // Update transition info when selected key or second key changes
  useEffect(() => {
    if (selectedKey && secondKey) {
      const [score, type] = camelotRules.getTransitionScore(
        selectedKey,
        secondKey,
        bpm1 || undefined,
        bpm2 || undefined,
        enabledRules
      );

      setTransitionInfo({
        score,
        type,
        description: camelotRules.getTransitionName(selectedKey, secondKey),
      });
    } else {
      setTransitionInfo(null);
    }
  }, [selectedKey, secondKey, bpm1, bpm2, enabledRules]);

  // Handle click outside to reset selection
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wheelRef.current && !wheelRef.current.contains(event.target as Node)) {
        resetSelection();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update wheel size based on container width
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const newSize = compact ? Math.min(containerWidth, 120) : Math.min(containerWidth, 240);
        setWheelSize(newSize);
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [compact]);

  // Handlers
  const handleKeyClick = (key: string) => {
    if (key === selectedKey) {
      setSelectedKey("");
      setSecondKey(null);
      if (onKeySelect) onKeySelect("");
    } else if (!selectedKey || selectedKey === "") {
      setSelectedKey(key);
      if (onKeySelect) onKeySelect(key);
    } else {
      setSecondKey(key);
    }
  };

  const resetSelection = () => {
    setSelectedKey("");
    setSecondKey(null);
    setBpm1(null);
    setBpm2(null);
    if (onKeySelect) onKeySelect("");
  };

  const copyResultToClipboard = () => {
    if (!transitionInfo) return;

    const text = `
Camelot Mix: ${selectedKey} → ${secondKey}
Transition: ${transitionInfo.description}
Type: ${transitionInfo.type}
Score: ${transitionInfo.score.toFixed(1)}
${bpm1 && bpm2 ? `BPM: ${bpm1} → ${bpm2} (diff: ${Math.abs(bpm1 - bpm2).toFixed(1)})` : ''}
    `.trim();

    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "Copied to clipboard",
        description: "Transition details copied to clipboard",
      });
    });
  };

  const getKeyColor = (key: string): string => {
    const [num, letter] = [parseInt(key.slice(0, -1)), key.slice(-1)];

    const hourColors: { [key: number]: { a: string, b: string } } = {
      1: { a: 'fill-cyan-500', b: 'fill-cyan-600' },
      2: { a: 'fill-green-500', b: 'fill-green-600' },
      3: { a: 'fill-lime-500', b: 'fill-lime-600' },
      4: { a: 'fill-yellow-500', b: 'fill-amber-600' },
      5: { a: 'fill-orange-500', b: 'fill-orange-600' },
      6: { a: 'fill-red-500', b: 'fill-red-600' },
      7: { a: 'fill-rose-500', b: 'fill-rose-600' },
      8: { a: 'fill-pink-500', b: 'fill-pink-600' },
      9: { a: 'fill-fuchsia-500', b: 'fill-fuchsia-600' },
      10: { a: 'fill-violet-500', b: 'fill-violet-600' },
      11: { a: 'fill-indigo-500', b: 'fill-indigo-600' },
      12: { a: 'fill-blue-500', b: 'fill-blue-600' },
    };

    return hourColors[num][letter.toLowerCase() as 'a' | 'b'];
  };

  const cycleViewMode = () => {
    setViewMode(prev => {
      if (prev === "full") return "minimal";
      return "full";
    });
  };

  const getDisplayKey = (key: string) => {
    return displayMode === "camelot" ? key : convertKey(key, false);
  };

  // Render the wheel for all view modes
  const renderWheel = () => {
    // Determine the center and radius based on the container
    const center = 200;
    const viewBox = `0 0 ${center * 2} ${center * 2}`;

    return (
      <svg className="w-full h-full" viewBox={viewBox}>
        {MINOR_KEYS.map((key, i) => {
          const startAngle = (i * 30) * (Math.PI / 180);
          const endAngle = ((i + 1) * 30) * (Math.PI / 180);

          const innerRadius = 80;
          const outerRadius = 130;

          const x1 = center + innerRadius * Math.cos(startAngle);
          const y1 = center + innerRadius * Math.sin(startAngle);
          const x2 = center + outerRadius * Math.cos(startAngle);
          const y2 = center + outerRadius * Math.sin(startAngle);
          const x3 = center + outerRadius * Math.cos(endAngle);
          const y3 = center + outerRadius * Math.sin(endAngle);
          const x4 = center + innerRadius * Math.cos(endAngle);
          const y4 = center + innerRadius * Math.sin(endAngle);

          const largeArcFlag = 0;

          const path = [
            `M ${x1} ${y1}`,
            `L ${x2} ${y2}`,
            `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x3} ${y3}`,
            `L ${x4} ${y4}`,
            `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x1} ${y1}`,
            'Z'
          ].join(' ');

          return (
            <path
              key={`minor-${key}`}
              d={path}
              className={cn(
                getKeyColor(key),
                key === selectedKey || key === secondKey ? "opacity-100" :
                  (selectedKey && compatibleKeys.includes(key)) ? "opacity-90" : "opacity-70",
                "transition-opacity duration-200"
              )}
              onClick={() => handleKeyClick(key)}
              style={{ cursor: "pointer" }}
            />
          );
        })}

        {MAJOR_KEYS.map((key, i) => {
          const startAngle = (i * 30) * (Math.PI / 180);
          const endAngle = ((i + 1) * 30) * (Math.PI / 180);

          const innerRadius = 130;
          const outerRadius = 180;

          const x1 = center + innerRadius * Math.cos(startAngle);
          const y1 = center + innerRadius * Math.sin(startAngle);
          const x2 = center + outerRadius * Math.cos(startAngle);
          const y2 = center + outerRadius * Math.sin(startAngle);
          const x3 = center + outerRadius * Math.cos(endAngle);
          const y3 = center + outerRadius * Math.sin(endAngle);
          const x4 = center + innerRadius * Math.cos(endAngle);
          const y4 = center + innerRadius * Math.sin(endAngle);

          const largeArcFlag = 0;

          const path = [
            `M ${x1} ${y1}`,
            `L ${x2} ${y2}`,
            `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x3} ${y3}`,
            `L ${x4} ${y4}`,
            `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x1} ${y1}`,
            'Z'
          ].join(' ');

          return (
            <path
              key={`major-${key}`}
              d={path}
              className={cn(
                getKeyColor(key),
                key === selectedKey || key === secondKey ? "opacity-100" :
                  (selectedKey && compatibleKeys.includes(key)) ? "opacity-90" : "opacity-70",
                "transition-opacity duration-200"
              )}
              onClick={() => handleKeyClick(key)}
              style={{ cursor: "pointer" }}
            />
          );
        })}

        <circle cx={center} cy={center} r="80" fill="white" />
      </svg>
    );
  };



  // Minimal view mode
  if (viewMode === "minimal") {
    return (
      <div ref={containerRef} className={cn("flex flex-col", className)}>
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium">Camelot Wheel</h3>
          <Button variant="ghost" size="sm" className="h-7 w-7 p-0" onClick={cycleViewMode}>
            {viewMode === "minimal" ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
        </div>

        <div className="relative w-full aspect-square max-w-[200px] mx-auto mb-3" ref={wheelRef}>
          {renderWheel()}

          {MINOR_KEYS.map((key, i) => {
            const angle = (i * 30) * (Math.PI / 180);
            const radius = 52.5;
            const x = 100 + radius * Math.cos(angle);
            const y = 100 + radius * Math.sin(angle);

            return (
              <div
                key={`label-${key}`}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center font-bold cursor-pointer text-white text-xs w-4 h-4"
                style={{
                  left: `${x}px`,
                  top: `${y}px`,
                  zIndex: key === selectedKey || key === secondKey ? 20 : 10,
                }}
                onClick={() => handleKeyClick(key)}
              >
                {getDisplayKey(key)}
              </div>
            );
          })}

          {MAJOR_KEYS.map((key, i) => {
            const angle = (i * 30) * (Math.PI / 180);
            const radius = 77.5;
            const x = 100 + radius * Math.cos(angle);
            const y = 100 + radius * Math.sin(angle);

            return (
              <div
                key={`label-${key}`}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center font-bold cursor-pointer text-white text-xs w-4 h-4"
                style={{
                  left: `${x}px`,
                  top: `${y}px`,
                  zIndex: key === selectedKey || key === secondKey ? 20 : 10,
                }}
                onClick={() => handleKeyClick(key)}
              >
                {getDisplayKey(key)}
              </div>
            );
          })}
        </div>

        {selectedKey && (
          <div className="flex flex-col space-y-2 p-2 border rounded-md bg-background/50 text-xs">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex flex-col">
                  <span className="text-muted-foreground">From</span>
                  <span className="text-base font-bold">{selectedKey}</span>
                </div>

                {secondKey && (
                  <>
                    <div className="text-base">→</div>
                    <div className="flex flex-col">
                      <span className="text-muted-foreground">To</span>
                      <span className="text-base font-bold">{secondKey}</span>
                    </div>
                  </>
                )}
              </div>

              <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={resetSelection}>
                <HelpCircle className="h-3 w-3" />
              </Button>
            </div>

            {transitionInfo && (
              <div className="flex flex-col space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs">{transitionInfo.description}</span>
                  <span
                    className={cn(
                      "px-1.5 py-0.5 rounded text-xs font-medium",
                      transitionInfo.score >= 80 ? "bg-green-100 text-green-800" :
                      transitionInfo.score >= 60 ? "bg-yellow-100 text-yellow-800" :
                      transitionInfo.score > 0 ? "bg-orange-100 text-orange-800" :
                      "bg-red-100 text-red-800"
                    )}
                  >
                    {transitionInfo.score.toFixed(1)}
                  </span>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="mt-2">
          <MixingRulesPanel
            enabledRules={enabledRules}
            onRulesChange={setEnabledRules}
            isMinimal={true}
          />
        </div>
      </div>
    );
  }

  // Full view mode (default)
  return (
    <div ref={containerRef} className={cn("flex flex-col items-center", className)}>
      <div className="flex flex-col space-y-6 w-full max-w-3xl">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <CardTitle className="flex items-center space-x-2">
                  <span>Camelot Wheel</span>
                  <HelpCircle
                    className="h-5 w-5 cursor-help text-muted-foreground"
                    onClick={() => {
                      toast({
                        title: "Camelot Wheel",
                        description: "The Camelot Wheel is a tool for harmonic mixing. Click on keys to select and see compatible transitions.",
                      });
                    }}
                  />
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="key-display"
                    checked={displayMode === "standard"}
                    onCheckedChange={(checked) =>
                      setDisplayMode(checked ? "standard" : "camelot")
                    }
                  />
                  <label htmlFor="key-display" className="text-sm">
                    {displayMode === "standard" ? "Standard" : "Camelot"} Keys
                  </label>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={cycleViewMode}
                className="flex items-center gap-1"
              >
                <Minimize2 className="h-4 w-4" />
                <span>Minimal</span>
              </Button>
            </div>
            <CardDescription>
              Interactive tool for harmonic mixing. {displayMode === "standard" ? "Showing musical keys" : "Showing Camelot notation"}.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative w-[400px] h-[400px] mx-auto mb-6" ref={wheelRef}>
              {renderWheel()}

              {MINOR_KEYS.map((key, i) => {
                const angle = (i * 30) * (Math.PI / 180);
                const radius = 105;
                const x = 200 + radius * Math.cos(angle);
                const y = 200 + radius * Math.sin(angle);

                return (
                  <div
                    key={`label-${key}`}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center font-bold cursor-pointer text-white text-sm w-9 h-9"
                    style={{
                      left: `${x}px`,
                      top: `${y}px`,
                      zIndex: key === selectedKey || key === secondKey ? 20 : 10,
                    }}
                    onClick={() => handleKeyClick(key)}
                  >
                    {getDisplayKey(key)}
                  </div>
                );
              })}

              {MAJOR_KEYS.map((key, i) => {
                const angle = (i * 30) * (Math.PI / 180);
                const radius = 155;
                const x = 200 + radius * Math.cos(angle);
                const y = 200 + radius * Math.sin(angle);

                return (
                  <div
                    key={`label-${key}`}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center font-bold cursor-pointer text-white text-sm w-9 h-9"
                    style={{
                      left: `${x}px`,
                      top: `${y}px`,
                      zIndex: key === selectedKey || key === secondKey ? 20 : 10,
                    }}
                    onClick={() => handleKeyClick(key)}
                  >
                    {getDisplayKey(key)}
                  </div>
                );
              })}

              {!selectedKey && (
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center p-4 pointer-events-none">
                  <p className="text-gray-500 font-medium">Click on a key to start</p>
                </div>
              )}
              {selectedKey && !secondKey && (
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center p-4 pointer-events-none">
                  <p className="text-gray-500 font-medium">Select a second key</p>
                </div>
              )}
            </div>

            {selectedKey && (
              <div className="flex flex-col space-y-4 mt-6 p-4 border rounded-md bg-background/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex flex-col">
                      <span className="text-sm text-muted-foreground">From Key</span>
                      <span className="text-2xl font-bold">{selectedKey}</span>
                      <div className="flex items-center mt-1">
                        <input
                          type="number"
                          placeholder="BPM"
                          className="w-20 h-8 px-2 text-sm border rounded"
                          value={bpm1 !== null ? bpm1 : ""}
                          onChange={(e) => setBpm1(parseFloat(e.target.value) || null)}
                        />
                        <span className="text-xs ml-1">BPM</span>
                      </div>
                    </div>

                    {secondKey && (
                      <>
                        <div className="text-2xl">→</div>
                        <div className="flex flex-col">
                          <span className="text-sm text-muted-foreground">To Key</span>
                          <span className="text-2xl font-bold">{secondKey}</span>
                          <div className="flex items-center mt-1">
                            <input
                              type="number"
                              placeholder="BPM"
                              className="w-20 h-8 px-2 text-sm border rounded"
                              value={bpm2 !== null ? bpm2 : ""}
                              onChange={(e) => setBpm2(parseFloat(e.target.value) || null)}
                            />
                            <span className="text-xs ml-1">BPM</span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>

                  <Button variant="outline" size="sm" onClick={resetSelection}>
                    Reset
                  </Button>
                </div>

                {transitionInfo && (
                  <div className="flex flex-col space-y-2 mt-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Transition: {transitionInfo.description}</span>
                      <span
                        className={cn(
                          "px-2 py-0.5 rounded text-xs font-medium",
                          transitionInfo.score >= 80 ? "bg-green-100 text-green-800" :
                          transitionInfo.score >= 60 ? "bg-yellow-100 text-yellow-800" :
                          transitionInfo.score > 0 ? "bg-orange-100 text-orange-800" :
                          "bg-red-100 text-red-800"
                        )}
                      >
                        Score: {transitionInfo.score.toFixed(1)}
                      </span>
                    </div>

                    {bpm1 && bpm2 && (
                      <div className="text-sm">
                        BPM diff: <span className={Math.abs(bpm1 - bpm2) > 8 ? "text-amber-600 font-medium" : "text-green-600 font-medium"}>
                          {Math.abs(bpm1 - bpm2).toFixed(1)}
                        </span>
                      </div>
                    )}

                    <Button
                      variant="secondary"
                      size="sm"
                      className="self-end"
                      onClick={copyResultToClipboard}
                    >
                      Copy Details
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex-col items-start">
            <MixingRulesPanel
              enabledRules={enabledRules}
              onRulesChange={setEnabledRules}
              isMinimal={false}
            />
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default CamelotWheel;