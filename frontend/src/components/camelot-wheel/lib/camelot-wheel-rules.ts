// Define transition types
export enum TransitionType {
  PERFECT = "perfect",
  ADJACENT = "adjacent",
  RELATIVE = "relative",
  SAME_NUMBER = "same_number",
  ENERGY_BOOST = "energy_boost",
  ENERGY_DROP = "energy_drop",
  DIAGONAL_MIX = "diagonal_mix",
  HARMONIC_THIRD = "harmonic_third",
  JAWS_MIX = "jaws_mix",
  PAY_ATTENTION = "pay_attention",
  MAJOR_THIRD = "major_third",
  DOMINANT_KEY = "dominant_key",
  SUBDOMINANT_KEY = "subdominant_key",
  DISTANT = "distant",
  INVALID = "invalid",
  UNKNOWN = "unknown"
}

// Define transition scores
const TRANSITION_SCORES: Record<TransitionType, number> = {
  [TransitionType.PERFECT]: 100,
  [TransitionType.ADJACENT]: 85,
  [TransitionType.RELATIVE]: 80,
  [TransitionType.SAME_NUMBER]: 75,
  [TransitionType.ENERGY_BOOST]: 90,
  [TransitionType.ENERGY_DROP]: 80,
  [TransitionType.DIAGONAL_MIX]: 70,
  [TransitionType.HARMONIC_THIRD]: 60,
  [TransitionType.JAWS_MIX]: 50,
  [TransitionType.PAY_ATTENTION]: 50,
  [TransitionType.MAJOR_THIRD]: 50,
  [TransitionType.DOMINANT_KEY]: 65,
  [TransitionType.SUBDOMINANT_KEY]: 65,
  [TransitionType.DISTANT]: 30,
  [TransitionType.INVALID]: 0,
  [TransitionType.UNKNOWN]: 0,
};

// Define rules class
interface CamelotRule {
  name: string;
  description: string;
  maxBpmDiff?: number;
}

export class CamelotWheelRules {
  private wheel: Record<string, Set<string>>;
  private rules: Record<TransitionType, CamelotRule>;

  constructor() {
    this.wheel = this.initializeWheel();
    this.rules = this.initializeRules();
  }

  private initializeWheel(): Record<string, Set<string>> {
    const wheel: Record<string, Set<string>> = {};
    const majorKeys = Array.from({ length: 12 }, (_, i) => `${i + 1}B`);
    const minorKeys = Array.from({ length: 12 }, (_, i) => `${i + 1}A`);

    for (let i = 0; i < 12; i++) {
      const currentMajor = majorKeys[i];
      const currentMinor = minorKeys[i];

      // Adjacent keys
      const prevMajor = majorKeys[(i - 1 + 12) % 12];
      const nextMajor = majorKeys[(i + 1) % 12];
      const prevMinor = minorKeys[(i - 1 + 12) % 12];
      const nextMinor = minorKeys[(i + 1) % 12];

      // Compatible keys based on standard rules (perfect, adjacent, relative)
      wheel[currentMajor] = new Set([currentMajor, prevMajor, nextMajor, currentMinor]);
      wheel[currentMinor] = new Set([currentMinor, prevMinor, nextMinor, currentMajor]);
    }

    return wheel;
  }

  private initializeRules(): Record<TransitionType, CamelotRule> {
    return {
      [TransitionType.PERFECT]: { name: "Perfect Match", description: "Same key (e.g., 8A to 8A)", maxBpmDiff: 8.0 },
      [TransitionType.ADJACENT]: { name: "Adjacent Match", description: "One step clockwise/counter-clockwise (e.g., 8A to 7A or 9A)", maxBpmDiff: 8.0 },
      [TransitionType.RELATIVE]: { name: "Relative Key", description: "Switch between Major/Minor (e.g., 8A to 8B)", maxBpmDiff: 8.0 },
      [TransitionType.SAME_NUMBER]: { name: "Same Number", description: "Same number, different letter (e.g., 8A to 8B)", maxBpmDiff: 8.0 },
      [TransitionType.ENERGY_BOOST]: { name: "Energy Boost", description: "+1 number, same letter (e.g., 8A to 9A)", maxBpmDiff: 5.0 },
      [TransitionType.DIAGONAL_MIX]: { name: "Diagonal Mix", description: "Diagonal on the wheel (e.g., 8A to 7B or 9B)", maxBpmDiff: 8.0 },
      [TransitionType.ENERGY_DROP]: { name: "Energy Drop", description: "-1 number, same letter (e.g., 8A to 7A)", maxBpmDiff: 5.0 },
      [TransitionType.HARMONIC_THIRD]: { name: "Harmonic Third", description: "Jump 3 steps (e.g. 8A to 11A)", maxBpmDiff: 10.0 },
      [TransitionType.JAWS_MIX]: { name: "Jaws Mix", description: "Jump 6 steps (e.g. 8A to 2A)", maxBpmDiff: 10.0 },
      [TransitionType.PAY_ATTENTION]: { name: "Pay Attention", description: "Jump 2 steps (e.g. 8A to 10A)", maxBpmDiff: 10.0 },
      [TransitionType.MAJOR_THIRD]: { name: "Major Third", description: "Jump 4 steps (e.g. 8A to 12A)", maxBpmDiff: 10.0 },
      [TransitionType.DOMINANT_KEY]: { name: "Dominant Key", description: "Dominant key relationship", maxBpmDiff: 8.0 },
      [TransitionType.SUBDOMINANT_KEY]: { name: "Subdominant Key", description: "Subdominant key relationship", maxBpmDiff: 8.0 },
      [TransitionType.DISTANT]: { name: "Distant", description: "Distant key transition", maxBpmDiff: 12.0 },
      [TransitionType.INVALID]: { name: "Invalid", description: "Invalid transition" },
      [TransitionType.UNKNOWN]: { name: "Unknown", description: "Unknown transition" },
    };
  }

  private parseKey(key: string): [number, string] {
    if (!key || typeof key !== "string" || !key.slice(0, -1).match(/^\d+$/) || !["A", "B"].includes(key.slice(-1))) {
      throw new Error(`Invalid Camelot key format: ${key}`);
    }
    const number = parseInt(key.slice(0, -1), 10);
    const letter = key.slice(-1);
    return [number, letter];
  }

  private formatKey(number: number, letter: string): string {
    return `${((number - 1 + 12) % 12) + 1}${letter}`;
  }

  private getBasicTransitionType(key1: string, key2: string): TransitionType {
    try {
      const [num1, let1] = this.parseKey(key1);
      const [num2, let2] = this.parseKey(key2);

      let numDiff = Math.abs(num1 - num2);
      // Handle wrap-around difference (e.g., 1 to 12 is diff 1)
      if (numDiff > 6) {
        numDiff = 12 - numDiff;
      }

      if (key1 === key2) {
        return TransitionType.PERFECT;
      }

      if (let1 === let2) {  // Same letter (A-A or B-B)
        if (numDiff === 1) {
          // Determine energy boost/drop based on direction
          if (((num1 % 12) + 1) % 12 === num2 % 12) {
            return TransitionType.ENERGY_BOOST; // e.g. 8A -> 9A
          } else {
            return TransitionType.ENERGY_DROP; // e.g. 8A -> 7A
          }
        } else if (numDiff === 2) {
          return TransitionType.PAY_ATTENTION;
        } else if (numDiff === 3) {
          return TransitionType.HARMONIC_THIRD;
        } else if (numDiff === 4) {
          return TransitionType.MAJOR_THIRD;
        } else if (numDiff === 6) {
          return TransitionType.JAWS_MIX;
        } else {
          return TransitionType.INVALID; // Other differences on same letter
        }
      } else if (num1 === num2) {  // Same number (A-B or B-A)
        return TransitionType.RELATIVE; // or TransitionType.SAME_NUMBER
      } else { // Different number and letter
        if (numDiff === 1) {
          return TransitionType.DIAGONAL_MIX;
        } else {
          return TransitionType.INVALID; // Other diagonal/distant transitions
        }
      }
    } catch (error) {
      return TransitionType.INVALID;
    }
  }

  public getTransitionType(key1: string, key2: string, allowedRules?: string[]): TransitionType {
    const basicType = this.getBasicTransitionType(key1, key2);

    if (basicType === TransitionType.INVALID) {
      return TransitionType.INVALID;
    }

    if (!allowedRules) {
      return basicType; // If no rules specified, return the basic type found
    }

    // Check if the found basic type is in the list of allowed rules
    if (allowedRules.includes(basicType)) {
      // Special handling for adjacent vs energy boost/drop if needed
      if (basicType === TransitionType.ENERGY_BOOST &&
          allowedRules.includes(TransitionType.ADJACENT) &&
          !allowedRules.includes(TransitionType.ENERGY_BOOST)) {
        return TransitionType.ADJACENT; // Treat boost as adjacent if only adjacent is allowed
      }
      if (basicType === TransitionType.ENERGY_DROP &&
          allowedRules.includes(TransitionType.ADJACENT) &&
          !allowedRules.includes(TransitionType.ENERGY_DROP)) {
        return TransitionType.ADJACENT; // Treat drop as adjacent if only adjacent is allowed
      }
      return basicType;
    } else if ((basicType === TransitionType.ENERGY_BOOST || basicType === TransitionType.ENERGY_DROP) &&
                allowedRules.includes(TransitionType.ADJACENT)) {
      // If boost/drop isn't explicitly allowed but adjacent is, consider it adjacent
      return TransitionType.ADJACENT;
    } else if (basicType === TransitionType.RELATIVE && allowedRules.includes(TransitionType.SAME_NUMBER)) {
      // Allow alias
      return TransitionType.SAME_NUMBER;
    } else if (basicType === TransitionType.SAME_NUMBER && allowedRules.includes(TransitionType.RELATIVE)) {
      // Allow alias
      return TransitionType.RELATIVE;
    } else {
      return TransitionType.INVALID; // Type found is not in the allowed list
    }
  }

  public checkCompatibility(key1: string, key2: string, allowedRules?: string[]): {
    compatible: boolean;
    transitionType: TransitionType;
    score: number;
    description: string;
  } {
    const transitionType = this.getTransitionType(key1, key2, allowedRules);

    return {
      compatible: transitionType !== TransitionType.INVALID,
      transitionType,
      score: TRANSITION_SCORES[transitionType] || 0,
      description: this.getTransitionName(key1, key2)
    };
  }

  public getCompatibleKeys(key: string, allowedRules: string[]): Set<string> {
    const compatible = new Set<string>();

    try {
      const [num, let_] = this.parseKey(key);
      const otherLet = let_ === 'A' ? 'B' : 'A';

      for (const ruleName of allowedRules) {
        if (ruleName === TransitionType.PERFECT) {
          compatible.add(key);
        } else if (ruleName === TransitionType.ADJACENT) {
          compatible.add(this.formatKey(num + 1, let_));
          compatible.add(this.formatKey(num - 1, let_));
        } else if (ruleName === TransitionType.RELATIVE || ruleName === TransitionType.SAME_NUMBER) {
          compatible.add(this.formatKey(num, otherLet));
        } else if (ruleName === TransitionType.ENERGY_BOOST) {
          compatible.add(this.formatKey(num + 1, let_));
        } else if (ruleName === TransitionType.ENERGY_DROP) {
          compatible.add(this.formatKey(num - 1, let_));
        } else if (ruleName === TransitionType.DIAGONAL_MIX) {
          compatible.add(this.formatKey(num + 1, otherLet));
          compatible.add(this.formatKey(num - 1, otherLet));
        } else if (ruleName === TransitionType.HARMONIC_THIRD) {
          compatible.add(this.formatKey(num + 3, let_));
          compatible.add(this.formatKey(num - 3, let_));
        } else if (ruleName === TransitionType.JAWS_MIX) {
          compatible.add(this.formatKey(num + 6, let_)); // num - 6 is the same
        } else if (ruleName === TransitionType.PAY_ATTENTION) {
          compatible.add(this.formatKey(num + 2, let_));
          compatible.add(this.formatKey(num - 2, let_));
        } else if (ruleName === TransitionType.MAJOR_THIRD) {
          compatible.add(this.formatKey(num + 4, let_));
          compatible.add(this.formatKey(num - 4, let_));
        }
      }
    } catch (error) {
      // Return empty set for invalid input key
    }

    return compatible;
  }

  public getTransitionScore(
    key1: string,
    key2: string,
    bpm1?: number,
    bpm2?: number,
    allowedRules?: string[]
  ): [number, TransitionType] {
    try {
      // Add default values for invalid inputs
      if (!key1 || !key2 || typeof key1 !== "string" || typeof key2 !== "string") {
        console.log(`Invalid key format - key1: ${key1}, key2: ${key2}`);
        return [0.0, TransitionType.INVALID];
      }

      const transitionType = this.getTransitionType(key1, key2, allowedRules);
      let baseScore = TRANSITION_SCORES[transitionType] || 0;

      // Apply BPM penalty if BPM values are provided
      if (typeof bpm1 === 'number' && typeof bpm2 === 'number' && baseScore > 0) {
        const bpmDiff = Math.abs(bpm1 - bpm2);
        const maxDiff = this.getMaxBpmDiff(transitionType) || 10.0;  // Default to 10 BPM

        // Apply penalty based on how close we are to max diff
        if (bpmDiff > maxDiff) {
          const penalty = Math.min(0.5, (bpmDiff - maxDiff) / 10.0);  // Up to 50% penalty
          baseScore *= (1.0 - penalty);
        }
      }

      return [baseScore, transitionType];
    } catch (error) {
      // Log and return a safe default
      console.error("Error calculating transition score:", error);
      console.log(`key1=${key1}, key2=${key2}, bpm1=${bpm1}, bpm2=${bpm2}`);
      return [0.0, TransitionType.UNKNOWN];
    }
  }

  public getTransitionName(key1: string, key2: string): string {
    const basicType = this.getBasicTransitionType(key1, key2);
    const rule = this.rules[basicType];
    return rule ? rule.description : "Invalid Transition";
  }

  public getMaxBpmDiff(transitionType: TransitionType): number | undefined {
    const rule = this.rules[transitionType];
    return rule ? rule.maxBpmDiff : undefined;
  }
}