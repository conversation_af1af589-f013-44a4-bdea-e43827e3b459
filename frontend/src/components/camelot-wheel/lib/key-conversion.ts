type KeyPair = {
  camelot: string;
  standard: string;
};

const keyMap: KeyPair[] = [
  { camelot: "1A", standard: "Abm" },
  { camelot: "1B", standard: "B" },
  { camelot: "2A", standard: "Ebm" },
  { camelot: "2B", standard: "Gb" },
  { camelot: "3A", standard: "Bbm" },
  { camelot: "3B", standard: "Db" },
  { camelot: "4A", standard: "Fm" },
  { camelot: "4B", standard: "Ab" },
  { camelot: "5A", standard: "Cm" },
  { camelot: "5B", standard: "Eb" },
  { camelot: "6A", standard: "Gm" },
  { camelot: "6B", standard: "Bb" },
  { camelot: "7A", standard: "Dm" },
  { camelot: "7B", standard: "F" },
  { camelot: "8A", standard: "Am" },
  { camelot: "8B", standard: "C" },
  { camelot: "9A", standard: "Em" },
  { camelot: "9B", standard: "G" },
  { camelot: "10A", standard: "Bm" },
  { camelot: "10B", standard: "D" },
  { camelot: "11A", standard: "F#m" },
  { camelot: "11B", standard: "A" },
  { camelot: "12A", standard: "C#m" },
  { camelot: "12B", standard: "E" },
];

export const convertKey = (key: string, toCamelot: boolean): string => {
  const pair = keyMap.find(pair => 
    toCamelot ? pair.standard === key : pair.camelot === key
  );
  return pair ? (toCamelot ? pair.camelot : pair.standard) : key;
};

export const STANDARD_KEYS = keyMap.map(pair => pair.standard);
export const CAMELOT_KEYS = keyMap.map(pair => pair.camelot);

