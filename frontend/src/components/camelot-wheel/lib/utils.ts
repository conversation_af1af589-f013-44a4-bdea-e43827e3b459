import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { TransitionType } from "./camelot-wheel-rules";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const WHEEL_COLORS = [
  "#FF0000", // 1 - Red
  "#FF8000", // 2 - Orange
  "#FFFF00", // 3 - Yellow
  "#80FF00", // 4 - Lime
  "#00FF00", // 5 - Green
  "#00FF80", // 6 - Spring Green
  "#00FFFF", // 7 - <PERSON>an
  "#0080FF", // 8 - Azure
  "#0000FF", // 9 - <PERSON>
  "#8000FF", // 10 - Purple
  "#FF00FF", // 11 - <PERSON>genta
  "#FF0080", // 12 - Rose
];

export const TRANSITION_COLORS: Record<TransitionType, string> = {
  [TransitionType.PERFECT]: "#00FF00", // Green
  [TransitionType.ADJACENT]: "#FFFF00", // Yellow
  [TransitionType.RELATIVE]: "#00FFFF", // <PERSON>an
  [TransitionType.SAME_NUMBER]: "#00FFFF", // <PERSON>an (same as RELATIVE)
  [TransitionType.ENERGY_BOOST]: "#FF8000", // Orange
  [TransitionType.ENERGY_DROP]: "#0080FF", // Azure
  [TransitionType.DIAGONAL_MIX]: "#8000FF", // Purple
  [TransitionType.HARMONIC_THIRD]: "#FF00FF", // Magenta
  [TransitionType.JAWS_MIX]: "#FF0080", // Rose
  [TransitionType.PAY_ATTENTION]: "#FF0000", // Red
  [TransitionType.MAJOR_THIRD]: "#80FF00", // Lime
  [TransitionType.DOMINANT_KEY]: "#FFA500", // Orange
  [TransitionType.SUBDOMINANT_KEY]: "#FF6347", // Tomato
  [TransitionType.DISTANT]: "#A0A0A0", // Light Gray
  [TransitionType.INVALID]: "#808080", // Gray
  [TransitionType.UNKNOWN]: "#808080", // Gray
};

export const TRANSITION_DESCRIPTIONS: Record<TransitionType, string> = {
  [TransitionType.PERFECT]: "Perfect match - same key",
  [TransitionType.ADJACENT]: "Adjacent keys - one step clockwise/counter-clockwise",
  [TransitionType.RELATIVE]: "Relative key - switch between Major/Minor",
  [TransitionType.SAME_NUMBER]: "Same number - different letter",
  [TransitionType.ENERGY_BOOST]: "Energy boost - +1 number, same letter",
  [TransitionType.ENERGY_DROP]: "Energy drop - -1 number, same letter",
  [TransitionType.DIAGONAL_MIX]: "Diagonal mix - adjacent number, different letter",
  [TransitionType.HARMONIC_THIRD]: "Harmonic third - jump 3 steps",
  [TransitionType.JAWS_MIX]: "Jaws mix - jump 6 steps (opposite)",
  [TransitionType.PAY_ATTENTION]: "Pay attention - jump 2 steps",
  [TransitionType.MAJOR_THIRD]: "Major third - jump 4 steps",
  [TransitionType.DOMINANT_KEY]: "Dominant key relationship",
  [TransitionType.SUBDOMINANT_KEY]: "Subdominant key relationship",
  [TransitionType.DISTANT]: "Distant key transition",
  [TransitionType.INVALID]: "Invalid transition",
  [TransitionType.UNKNOWN]: "Unknown transition",
};

export const DEFAULT_ENABLED_RULES = [
  TransitionType.PERFECT,
  TransitionType.ADJACENT,
  TransitionType.RELATIVE,
  TransitionType.ENERGY_BOOST,
  TransitionType.ENERGY_DROP,
];

export const ALL_RULES = [
  TransitionType.PERFECT,
  TransitionType.ADJACENT,
  TransitionType.RELATIVE,
  TransitionType.SAME_NUMBER,
  TransitionType.ENERGY_BOOST,
  TransitionType.ENERGY_DROP,
  TransitionType.DIAGONAL_MIX,
  TransitionType.HARMONIC_THIRD,
  TransitionType.JAWS_MIX,
  TransitionType.PAY_ATTENTION,
  TransitionType.MAJOR_THIRD,
];

export const getWheelColor = (position: number): string => {
  return WHEEL_COLORS[(position - 1) % 12];
};

export const getTransitionColor = (transitionType: TransitionType): string => {
  return TRANSITION_COLORS[transitionType] || TRANSITION_COLORS[TransitionType.INVALID];
};

export const getTransitionDescription = (transitionType: TransitionType): string => {
  return TRANSITION_DESCRIPTIONS[transitionType] || TRANSITION_DESCRIPTIONS[TransitionType.INVALID];
};
