import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Music } from 'lucide-react';
import CamelotWheel from './components/CamelotWheel';

interface CamelotWheelModalProps {
  selectedKey?: string;
  onKeySelect?: (key: string) => void;
  className?: string;
}

/**
 * CamelotWheelModal provides a popup modal for the Camelot wheel
 * with a toggle between minimal and full view modes
 */
const CamelotWheelModal: React.FC<CamelotWheelModalProps> = ({
  selectedKey = "8A",
  onKeySelect,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"minimal" | "full">("minimal");

  const handleKeySelect = (key: string) => {
    if (onKeySelect) {
      onKeySelect(key);
    }
  };

  const toggleViewMode = () => {
    setViewMode(prev => prev === "minimal" ? "full" : "minimal");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`gap-1 px-2 ${className}`}
          title="Harmonic Wheel"
        >
          <Music className="h-4 w-4" />
          <span className="hidden xl:inline">Harmonic</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] lg:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Harmonic Mixing Wheel</DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center space-y-4">
          {/* View mode toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "minimal" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("minimal")}
            >
              Minimal
            </Button>
            <Button
              variant={viewMode === "full" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("full")}
            >
              Full
            </Button>
          </div>

          {/* Camelot Wheel */}
          <div className="w-full flex justify-center">
            <CamelotWheel
              selectedKey={selectedKey}
              onKeySelect={handleKeySelect}
              defaultViewMode={viewMode}
              isSidebar={false}
              compact={false}
              className="w-full max-w-none"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CamelotWheelModal;
