import { colorPalettes } from './colorPalettes';
import type { CoverConfig, LayerConfig } from '../types';

/**
 * Generate a random number within a range
 */
export const random = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

/**
 * Get a random item from an array
 */
export const randomFromArray = <T>(arr: T[]): T => {
  return arr[Math.floor(Math.random() * arr.length)];
};

/**
 * Generate a random shape layer
 */
export const generateLayer = (type: string, palette: string[]): LayerConfig => {
  // Base parameters
  const layer: LayerConfig = {
    type,
    translateX: random(-80, 80),
    translateY: random(-80, 80),
    scaleX: random(0.2, 1.5),
    scaleY: random(0.2, 1.5),
    rotate: random(0, 360),
    skewX: random(-30, 30),
    skewY: random(-30, 30),
    blur: random(3, 50),
    opacity: random(0.6, 1),
    
    // Border radius for organic shapes
    borderRadius: [
      random(10, 90),
      random(10, 90),
      random(10, 90),
      random(10, 90),
      random(10, 90),
      random(10, 90),
      random(10, 90),
      random(10, 90)
    ],
    
    // Color configuration
    color: randomFromArray(palette),
    gradient: Math.random() < 0.35, // 35% chance of gradient
    secondaryColor: randomFromArray(palette),
    gradientType: Math.random() < 0.5 ? 'radial-gradient' : 'linear-gradient',
    gradientAngle: Math.floor(random(0, 360)),
    gradientPosition: `${Math.floor(random(30, 70))}% ${Math.floor(random(30, 70))}%`,
    
    // Blend mode
    blendMode: randomFromArray([
      'hard-light', 'overlay', 'screen', 'color-dodge', 'soft-light'
    ]),
  };
  
  // Customize based on layer type
  switch(type) {
    case 'base-layer':
      // Base layer is larger and less blurred
      layer.scaleX = random(1.0, 1.8);
      layer.scaleY = random(1.0, 1.8);
      layer.blur = random(5, 20);
      layer.opacity = random(0.8, 1.0);
      break;
      
    case 'accent-layer':
      // Accent layers have medium scale and blur
      layer.scaleX = random(0.4, 1.0);
      layer.scaleY = random(0.4, 1.0);
      layer.blur = random(10, 30);
      break;
      
    case 'highlight-layer':
      // Highlight layers are smaller with high blur
      layer.scaleX = random(0.1, 0.5);
      layer.scaleY = random(0.1, 0.5);
      layer.blur = random(20, 60);
      layer.opacity = random(0.7, 1.0);
      break;
      
    case 'feature-layer':
      // Feature layer is distinctive with less blur
      layer.scaleX = random(0.3, 0.8);
      layer.scaleY = random(0.3, 0.8);
      layer.blur = random(3, 15);
      layer.opacity = random(0.75, 0.95);
      break;
  }
  
  return layer;
};

/**
 * Generate a completely new random cover configuration
 */
export const generateRandomCover = (): CoverConfig => {
  // Choose a random palette
  const paletteIndex = Math.floor(random(0, colorPalettes.length));
  const palette = colorPalettes[paletteIndex].colors;
  
  // Configure noise
  const baseFrequencyX = random(0.001, 0.03);
  const baseFrequencyY = random(0.001, 0.03);
  const noiseScale = random(30, 120);
  const noiseOctaves = Math.floor(random(2, 5));
  
  // Configure texture
  const textureTypes = ['dots', 'lines', 'circles', 'scattered', 'none'];
  const textureType = randomFromArray(textureTypes);
  const textureOpacity = random(0.3, 0.8);
  const textureBlendModes = ['overlay', 'soft-light', 'color-burn', 'multiply'];
  const textureBlendMode = randomFromArray(textureBlendModes);
  
  // Generate layers
  const layers: LayerConfig[] = [];
  
  // Base layer
  layers.push(generateLayer('base-layer', palette));
  
  // Accent layers
  for (let i = 0; i < 3; i++) {
    layers.push(generateLayer('accent-layer', palette));
  }
  
  // Highlight layers
  for (let i = 0; i < 2; i++) {
    layers.push(generateLayer('highlight-layer', palette));
  }
  
  // Feature layer
  layers.push(generateLayer('feature-layer', palette));
  
  // Generate grain settings
  const grainOpacity = random(0.1, 0.3);
  const grainSize = random(0.8, 1.5);
  const grainAnimated = Math.random() < 0.2; // 20% chance of animated grain
  
  return {
    palette: paletteIndex,
    noiseBaseFrequencyX: baseFrequencyX,
    noiseBaseFrequencyY: baseFrequencyY,
    noiseScale,
    noiseOctaves,
    textureType,
    textureOpacity,
    textureBlendMode,
    grainOpacity,
    grainSize,
    grainAnimated,
    layers
  };
};

/**
 * Refine an existing cover with subtle changes
 */
export const refineCover = (config: CoverConfig): CoverConfig => {
  const currentPalette = colorPalettes[config.palette].colors;
  
  // Make small adjustments to noise filter
  const newConfig: CoverConfig = {
    ...config,
    noiseBaseFrequencyX: Math.max(0.001, config.noiseBaseFrequencyX + random(-0.005, 0.005)),
    noiseBaseFrequencyY: Math.max(0.001, config.noiseBaseFrequencyY + random(-0.005, 0.005)),
  };
  
  // Refine each layer
  newConfig.layers = config.layers.map(layer => {
    // Clone the layer to avoid mutating the original
    const refinedLayer = { ...layer };
    
    // Make small adjustments to the current values
    const translateAdjustX = random(-10, 10);
    const translateAdjustY = random(-10, 10);
    const rotateAdjust = random(-15, 15);
    const blurAdjust = random(-5, 5);
    
    // Apply adjustments
    refinedLayer.translateX += translateAdjustX;
    refinedLayer.translateY += translateAdjustY;
    refinedLayer.rotate += rotateAdjust;
    refinedLayer.blur = Math.max(0, refinedLayer.blur + blurAdjust);
    
    // 20% chance to change the color to a different one from the same palette
    if (Math.random() < 0.2) {
      const newColor = randomFromArray(currentPalette);
      refinedLayer.color = newColor;
      
      if (refinedLayer.gradient && Math.random() < 0.5) {
        refinedLayer.secondaryColor = randomFromArray(currentPalette);
      }
    }
    
    return refinedLayer;
  });
  
  return newConfig;
};
