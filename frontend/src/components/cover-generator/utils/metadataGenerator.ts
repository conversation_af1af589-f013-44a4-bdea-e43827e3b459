import type { Metadata } from '../types';

// Arrays for generating album titles and artist names
const adjectives = [
  'Electric', 'Cosmic', 'Ethereal', 'Infinite', 'Analog', 'Digital', 'Quantum', 
  'Velvet', '<PERSON>', 'Neon', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Distant',
  'Haunted', '<PERSON>', 'Vibrant', 'Lost', 'Hidden', 'Dreaming', 'Radian<PERSON>'
];

const nouns = [
  'Dreams', 'Echoes', 'Waves', 'Shadows', 'Signals', 'Horizon', 'Symphony', 
  'Journey', 'Reality', 'Void', 'Dimension', 'Cascade', 'Pulse', 'Whisper',
  'Empire', 'Vision', 'Mirage', 'Reflection', 'Fractal', 'Paradise', 'Dystopia'
];

const artistPrefixes = [
  'The', 'DJ', 'King', 'Queen', 'Dr.', 'MC', '<PERSON>', 'Lady', 'Sir', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const artistNames = [
  'Nebula', '<PERSON><PERSON>', '<PERSON>ert<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
  '<PERSON>ynth', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Flux', 'Atlas', 'Nova', 'Spark', 'Horizon',
  'Chrome', 'Neon', 'Midnight', 'Analog', 'Digital', 'Wave', 'Echo'
];

const artistSuffixes = [
  'Collective', 'Experience', 'Orchestra', 'Project', 'Syndrome', 'Theory',
  'Engine', 'System', 'Continuum', 'Machine', 'Alliance', 'Experiment'
];

/**
 * Generate random metadata for the album
 */
export const generateMetadata = (): Metadata => {
  // Different methods to generate album titles
  const titleGenerators = [
    // Adjective + Noun
    () => `${getRandomItem(adjectives)} ${getRandomItem(nouns)}`,
    
    // The + Noun + of + Adjective + Noun
    () => `The ${getRandomItem(nouns)} of ${getRandomItem(adjectives)} ${getRandomItem(nouns)}`,
    
    // Adjective + Noun + Preposition + Noun
    () => {
      const prepositions = ['in', 'under', 'beyond', 'through', 'within', 'against'];
      return `${getRandomItem(adjectives)} ${getRandomItem(nouns)} ${getRandomItem(prepositions)} ${getRandomItem(nouns)}`;
    },
    
    // Single word (all caps for emphasis)
    () => getRandomItem(nouns).toUpperCase(),
    
    // Year-based title
    () => {
      const years = ['1984', '2000', '2077', '3000', 'MMXXV'];
      return getRandomItem(years);
    }
  ];
  
  // Different methods to generate artist names
  const artistGenerators = [
    // Prefix + Name
    () => `${getRandomItem(artistPrefixes)} ${getRandomItem(artistNames)}`,
    
    // Name + Suffix
    () => `${getRandomItem(artistNames)} ${getRandomItem(artistSuffixes)}`,
    
    // Just the name
    () => getRandomItem(artistNames),
    
    // Compound name with symbol
    () => {
      const symbols = ['&', '+', 'x', '/', '.', '_'];
      return `${getRandomItem(artistNames)}${getRandomItem(symbols)}${getRandomItem(artistNames)}`;
    },
    
    // Stylized name with different case
    () => getRandomItem(artistNames).toLowerCase().replace(/[aeiou]/g, function(match) {
      return match.toUpperCase();
    })
  ];
  
  // Randomly select a generator for both title and artist
  const titleGen = getRandomItem(titleGenerators);
  const artistGen = getRandomItem(artistGenerators);
  
  return {
    title: titleGen(),
    artist: artistGen()
  };
};

/**
 * Get a random item from an array
 */
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}
