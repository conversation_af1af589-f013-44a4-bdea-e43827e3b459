import type { Metadata } from '../types';

/**
 * Export the current vinyl cover as a PNG image
 * @param elementId The ID of the element to export
 * @param metadata The metadata to include in the filename
 * @param returnBlob If true, returns the blob instead of triggering a download
 * @returns A Promise that resolves to a Blob if returnBlob is true, otherwise void
 */
export const exportCoverAsImage = async (
  elementId: string,
  metadata: Metadata,
  returnBlob: boolean = false
): Promise<Blob | void> => {
  const element = document.getElementById(elementId);
  if (!element) return;

  // Create a canvas element
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  // Set canvas dimensions to match the element
  canvas.width = element.offsetWidth * 2; // Double for higher quality
  canvas.height = element.offsetHeight * 2;

  if (!context) return;

  // Scale for better quality
  context.scale(2, 2);

  try {
    // Use html2canvas directly
    const html2canvas = (await import('html2canvas')).default;
    const canvas = await html2canvas(element, {
      backgroundColor: null,
      scale: 2,
      logging: false,
      useCORS: true
    });

    // Create a blob from the canvas
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        }
      }, 'image/png', 0.95);
    });

    if (returnBlob) {
      return blob;
    }

    // Create a download link
    const link = document.createElement('a');
    link.download = `${metadata.title || 'vinyl-cover'}.png`;
    link.href = URL.createObjectURL(blob);
    link.click();

    // Clean up
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Error exporting cover image:', error);

    // Fallback method using SVG and foreignObject
    try {
      const elementRect = element.getBoundingClientRect();

      // Use foreignObject approach
      const data = `
        <svg xmlns="http://www.w3.org/2000/svg" width="${elementRect.width}" height="${elementRect.height}">
          <foreignObject width="100%" height="100%">
            <div xmlns="http://www.w3.org/1999/xhtml">
              ${element.outerHTML}
            </div>
          </foreignObject>
        </svg>
      `;

      // Create a Blob from the SVG data
      const blob = new Blob([data], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);

      // Load the image
      const image = new Image();

      const imageLoaded = new Promise<void>((resolve, reject) => {
        image.onload = function() {
          context.drawImage(image, 0, 0, elementRect.width, elementRect.height);
          URL.revokeObjectURL(url);
          resolve();
        };
        image.onerror = reject;
        image.src = url;
      });

      await imageLoaded;

      // Convert canvas to blob
      const pngBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          }
        }, 'image/png', 0.95);
      });

      if (returnBlob) {
        return pngBlob;
      }

      // Create a download link
      const link = document.createElement('a');
      link.download = `${metadata.title || 'vinyl-cover'}.png`;
      link.href = URL.createObjectURL(pngBlob);
      link.click();

      // Clean up
      URL.revokeObjectURL(link.href);
    } catch (fallbackError) {
      console.error('Fallback export method failed:', fallbackError);
    }
  }
};
