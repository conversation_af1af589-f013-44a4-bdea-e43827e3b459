/* Main container */
.vinyl-generator {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  color: var(--foreground);
}

/* Header section */
.vinyl-generator__header {
  text-align: center;
  margin-bottom: 2rem;
  width: 100%;
}

.vinyl-generator__header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, var(--primary), var(--primary-foreground));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metadata {
  transition: all 0.3s ease;
}

.metadata h2 {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.metadata h3 {
  font-size: 1.2rem;
  font-weight: 400;
  opacity: 0.8;
}

/* Main content layout */
.vinyl-generator__main {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  width: 100%;
  flex-wrap: wrap;
  justify-content: center;
}

.cover-container {
  flex: 0 0 auto;
}

.control-container {
  flex: 1 1 400px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 500px;
}

/* Action buttons */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Cover display */
.cover-display {
  width: 400px;
  height: 400px;
  overflow: hidden;
  box-shadow: 0 1em 3em rgba(0, 0, 0, 0.4);
  background: var(--background);
  position: relative;
  border-radius: 0.25rem;
  transition: transform 0.3s ease;
}

.cover-display:hover {
  transform: scale(1.01);
}

.cover-display__layers {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.cover-display__layer {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  color: transparent;
  background: currentcolor;
  box-shadow: 0 0 2em 1.5em currentcolor;
  mix-blend-mode: hard-light;
  will-change: transform, filter, border-radius, background, color, box-shadow;
  transition: all 0.7s ease-out;
}

.cover-display__texture {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.cover-display__grain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 11;
}

.cover-display__metadata {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  z-index: 20;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.cover-display:hover .cover-display__metadata {
  opacity: 1;
  transform: translateY(0);
}

.cover-display__title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.cover-display__artist {
  font-size: 1.2rem;
  font-weight: 400;
}

/* Color palette */
.color-palette {
  margin-bottom: 1.5rem;
}

.color-palette__title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-palette__selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.color-palette__option {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid var(--border);
  position: relative;
}

.color-palette__option:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0.5rem rgba(255, 255, 255, 0.2);
}

.color-palette__option.active {
  border: 2px solid var(--primary);
  transform: scale(1.1);
}

.color-palette__option.active::after {
  content: '';
  position: absolute;
  top: -4px;
  right: -4px;
  bottom: -4px;
  left: -4px;
  border: 1px solid var(--primary-foreground);
  border-radius: 50%;
  pointer-events: none;
}

/* Control panel */
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.control-panel__section {
  background: var(--card);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid var(--border);
}

.control-panel__title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-panel__row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.control-panel__row label {
  width: 100px;
  font-size: 0.9rem;
}

.control-panel__row .value {
  width: 50px;
  text-align: right;
  font-size: 0.9rem;
  font-variant-numeric: tabular-nums;
}

.control-panel__row.checkbox {
  justify-content: space-between;
}

/* History panel */
.history-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100vh;
  background: var(--background);
  border-left: 1px solid var(--border);
  z-index: 100;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
}

.history-panel__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
}

.history-panel__header h2 {
  font-size: 1.2rem;
  font-weight: 600;
}

.history-panel__covers {
  overflow-y: auto;
  flex: 1;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0.5rem;
}

.history-item:hover {
  background: var(--accent);
}

.history-item__thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 0.25rem;
  overflow: hidden;
}

.thumbnail-preview {
  width: 100%;
  height: 100%;
}

.history-item__details {
  flex: 1;
}

.history-item__details h3 {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.history-item__details p {
  font-size: 0.8rem;
  opacity: 0.7;
}

.history-panel__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  opacity: 0.7;
}

.history-panel__tip {
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .vinyl-generator__main {
    flex-direction: column;
    align-items: center;
  }
  
  .control-container {
    max-width: 100%;
  }
  
  .history-panel {
    width: 100%;
  }
}
