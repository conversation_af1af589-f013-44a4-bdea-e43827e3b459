import React, { useRef, useEffect } from 'react';
import { cn } from '@/utils/ui/classNames';
import type { CoverConfig, Metadata } from './types';

interface CoverDisplayProps {
  config: CoverConfig;
  metadata: Metadata;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * CoverDisplay component renders the visual cover art based on the provided configuration
 */
const CoverDisplay: React.FC<CoverDisplayProps> = ({ 
  config, 
  metadata,
  size = 'md',
  className
}) => {
  const coverRef = useRef<HTMLDivElement>(null);
  const grainCanvasRef = useRef<HTMLCanvasElement>(null);
  
  // Set up animated grain if enabled
  useEffect(() => {
    if (!config.grainAnimated || !grainCanvasRef.current) return;
    
    const canvas = grainCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Setup canvas
    canvas.width = 400;
    canvas.height = 400;
    
    let animationFrameId: number;
    
    // Animation function
    const animate = () => {
      const imageData = ctx.createImageData(canvas.width, canvas.height);
      const data = imageData.data;
      
      // Generate noise
      for (let i = 0; i < data.length; i += 4) {
        const value = Math.floor(Math.random() * 255 * config.grainSize);
        data[i] = value;     // R
        data[i + 1] = value; // G
        data[i + 2] = value; // B
        data[i + 3] = Math.random() * 255 * config.grainOpacity; // A
      }
      
      ctx.putImageData(imageData, 0, 0);
      animationFrameId = requestAnimationFrame(animate);
    };
    
    // Start animation
    animate();
    
    // Cleanup
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [config.grainAnimated, config.grainOpacity, config.grainSize]);
  
  // Generate static grain if not animated
  useEffect(() => {
    if (config.grainAnimated || !grainCanvasRef.current) return;
    
    const canvas = grainCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Setup canvas
    canvas.width = 400;
    canvas.height = 400;
    
    const imageData = ctx.createImageData(canvas.width, canvas.height);
    const data = imageData.data;
    
    // Generate noise
    for (let i = 0; i < data.length; i += 4) {
      const value = Math.floor(Math.random() * 255 * config.grainSize);
      data[i] = value;     // R
      data[i + 1] = value; // G
      data[i + 2] = value; // B
      data[i + 3] = Math.random() * 255 * config.grainOpacity; // A
    }
    
    ctx.putImageData(imageData, 0, 0);
  }, [config.grainAnimated, config.grainOpacity, config.grainSize]);
  
  // Helper function to get texture URL
  const getTextureUrl = (type: string): string => {
    switch (type) {
      case 'dots':
        return "data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.2' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3Ccircle cx='13' cy='13' r='1'/%3E%3C/g%3E%3C/svg%3E";
      case 'lines':
        return "data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.2' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E";
      case 'circles':
        return "data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E";
      case 'scattered':
        return "data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E";
      default:
        return '';
    }
  };
  
  // Size classes
  const sizeClasses = {
    sm: 'w-32 h-32',
    md: 'w-64 h-64',
    lg: 'w-96 h-96',
  };
  
  return (
    <div 
      className={cn(
        "cover-display", 
        sizeClasses[size],
        className
      )} 
      id="vinyl-cover" 
      ref={coverRef}
    >
      <div className="cover-display__layers" style={{
        filter: `url(#noise) saturate(120%) contrast(130%) brightness(110%)`,
      }}>
        {config.layers.map((layer, index) => (
          <div
            key={index}
            className={`cover-display__layer ${layer.type}`}
            style={{
              transform: `translate(${layer.translateX}%, ${layer.translateY}%) scale(${layer.scaleX}, ${layer.scaleY}) rotate(${layer.rotate}deg) skew(${layer.skewX}deg, ${layer.skewY}deg)`,
              filter: `blur(${layer.blur}px)`,
              opacity: layer.opacity,
              borderRadius: `${layer.borderRadius.join('% ')}%`,
              background: layer.gradient 
                ? `${layer.gradientType}(${layer.gradientType === 'linear-gradient' ? `${layer.gradientAngle}deg` : 'circle at ' + layer.gradientPosition}, ${layer.color}, ${layer.secondaryColor})`
                : layer.color,
              boxShadow: `0 0 2em 1.5em ${layer.color}`,
              mixBlendMode: layer.blendMode as any,
            }}
          />
        ))}
      </div>
      
      {/* Texture overlay */}
      <div className="cover-display__texture" style={{
        backgroundImage: config.textureType !== 'none' ? `url(${getTextureUrl(config.textureType)})` : 'none',
        opacity: config.textureOpacity,
        mixBlendMode: config.textureBlendMode as any,
      }} />
      
      {/* Grain overlay */}
      <canvas 
        ref={grainCanvasRef} 
        className="cover-display__grain"
        style={{
          opacity: config.grainOpacity * 2, // Double for better visibility
          mixBlendMode: 'overlay',
        }}
      />
      
      {/* Metadata overlay */}
      <div className="cover-display__metadata">
        <h2 className="cover-display__title">{metadata.title || 'Untitled'}</h2>
        <p className="cover-display__artist">{metadata.artist || 'Unknown Artist'}</p>
      </div>
    </div>
  );
};

export default CoverDisplay;
