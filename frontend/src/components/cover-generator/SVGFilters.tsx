import React, { useEffect } from 'react';

interface SVGFiltersProps {
  noiseBaseFrequencyX?: number;
  noiseBaseFrequencyY?: number;
  noiseScale?: number;
  noiseOctaves?: number;
}

/**
 * SVGFilters component provides the SVG filters used for cover effects
 */
const SVGFilters: React.FC<SVGFiltersProps> = ({
  noiseBaseFrequencyX = 0.01,
  noiseBaseFrequencyY = 0.01,
  noiseScale = 80,
  noiseOctaves = 4
}) => {
  // Update filter parameters when props change
  useEffect(() => {
    const feTurb = document.getElementById('feTurb');
    const feDisp = document.getElementById('feDisp');
    
    if (feTurb) {
      feTurb.setAttribute('baseFrequency', `${noiseBaseFrequencyX} ${noiseBaseFrequencyY}`);
      feTurb.setAttribute('numOctaves', noiseOctaves.toString());
    }
    
    if (feDisp) {
      feDisp.setAttribute('scale', noiseScale.toString());
    }
  }, [noiseBaseFrequencyX, noiseBaseFrequencyY, noiseScale, noiseOctaves]);
  
  return (
    <svg width="0" height="0" style={{ position: 'absolute' }}>
      <filter id="noise" x="0%" y="0%" width="100%" height="100%">
        <feTurbulence
          id="feTurb"
          type="fractalNoise" 
          baseFrequency={`${noiseBaseFrequencyX} ${noiseBaseFrequencyY}`}
          numOctaves={noiseOctaves}
          result="NOISE"
          stitchTiles="stitch"
        />
        <feDisplacementMap
          id="feDisp"
          in="SourceGraphic"
          in2="NOISE"
          scale={noiseScale}
          xChannelSelector="R"
          yChannelSelector="R"
        />
      </filter>
      
      {/* Additional filter for more artistic grain effect */}
      <filter id="grain">
        <feTurbulence type="fractalNoise" baseFrequency="0.5" numOctaves="2" result="turb" />
        <feColorMatrix type="saturate" values="0" in="turb" result="mono" />
        <feComposite operator="in" in="mono" in2="SourceGraphic" result="grain" />
        <feBlend mode="overlay" in="SourceGraphic" in2="grain" />
      </filter>
    </svg>
  );
};

export default SVGFilters;
