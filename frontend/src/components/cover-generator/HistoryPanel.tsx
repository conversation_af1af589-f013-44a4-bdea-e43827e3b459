import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/utils/ui/classNames';
import type { SavedCover } from './types';

interface HistoryPanelProps {
  savedCovers: SavedCover[];
  onSelectCover: (cover: SavedCover) => void;
  onClose: () => void;
  className?: string;
}

/**
 * HistoryPanel component for displaying saved covers
 */
const HistoryPanel: React.FC<HistoryPanelProps> = ({ 
  savedCovers, 
  onSelectCover, 
  onClose,
  className
}) => {
  if (savedCovers.length === 0) {
    return (
      <div className={cn("history-panel", className)}>
        <div className="history-panel__header">
          <h2>Design History</h2>
          <Button 
            variant="ghost" 
            size="icon" 
            className="close-button" 
            onClick={onClose}
          >
            <X size={20} />
          </Button>
        </div>
        <div className="history-panel__empty">
          <p>No saved designs yet</p>
          <p className="history-panel__tip">Click the Save button to add designs to your history</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn("history-panel", className)}>
      <div className="history-panel__header">
        <h2>Design History</h2>
        <Button 
          variant="ghost" 
          size="icon" 
          className="close-button" 
          onClick={onClose}
        >
          <X size={20} />
        </Button>
      </div>
      <div className="history-panel__covers">
        {savedCovers.map((cover) => (
          <div key={cover.id} className="history-item" onClick={() => onSelectCover(cover)}>
            <div className="history-item__thumbnail">
              <div 
                className="thumbnail-preview"
                style={{ 
                  background: `linear-gradient(45deg, ${
                    cover.config.layers.slice(0, 3).map(layer => layer.color).join(', ')
                  })` 
                }}
              />
            </div>
            <div className="history-item__details">
              <h3>{cover.metadata.title || 'Untitled'}</h3>
              <p>{new Date(cover.timestamp).toLocaleTimeString()}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HistoryPanel;
