import React from 'react';
import { colorPalettes } from './utils/colorPalettes';
import { cn } from '@/utils/ui/classNames';

interface ColorPaletteProps {
  currentPalette: number;
  onSelectPalette: (index: number) => void;
  className?: string;
}

/**
 * ColorPalette component for selecting color schemes
 */
const ColorPalette: React.FC<ColorPaletteProps> = ({ 
  currentPalette, 
  onSelectPalette,
  className
}) => {
  return (
    <div className={cn("color-palette", className)}>
      <h3 className="color-palette__title">Color Palette</h3>
      <div className="color-palette__selector">
        {colorPalettes.map((palette, index) => (
          <div
            key={index}
            className={`color-palette__option ${index === currentPalette ? 'active' : ''}`}
            style={{
              background: `linear-gradient(45deg, ${palette.colors.join(', ')})`,
            }}
            onClick={() => onSelectPalette(index)}
            title={palette.name}
          />
        ))}
      </div>
    </div>
  );
};

export default ColorPalette;
