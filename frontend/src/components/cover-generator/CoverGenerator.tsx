import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, Sliders, Save, Star, History, Copy, X, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { cn } from '@/utils/ui/classNames';
import CoverDisplay from './CoverDisplay';
import ControlPanel from './ControlPanel';
import ColorPalette from './ColorPalette';
import SVGFilters from './SVGFilters';
import HistoryPanel from './HistoryPanel';
import { generateRandomCover, refineCover } from './utils/coverGenerator';
import { exportCoverAsImage } from './utils/exportUtils';
import { generateMetadata } from './utils/metadataGenerator';
import type { CoverConfig, SavedCover, Metadata } from './types';

interface CoverGeneratorProps {
  initialMetadata?: Metadata;
  onSave?: (coverConfig: CoverConfig, metadata: Metadata, imageBlob?: Blob) => void;
  className?: string;
  compact?: boolean;
  showControls?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * CoverGenerator component that can be used in different contexts
 * - As a standalone component
 * - As a dialog
 * - In compact mode for embedding in other components
 */
const CoverGenerator: React.FC<CoverGeneratorProps> = ({
  initialMetadata,
  onSave,
  className,
  compact = false,
  showControls = true,
  size = 'md',
}) => {
  const [coverConfig, setCoverConfig] = useState<CoverConfig>({
    palette: 0,
    noiseBaseFrequencyX: 0.01,
    noiseBaseFrequencyY: 0.01,
    noiseScale: 80,
    noiseOctaves: 4,
    grainOpacity: 0.2,
    grainSize: 1,
    grainAnimated: false,
    textureType: 'dots',
    textureOpacity: 0.5,
    textureBlendMode: 'overlay',
    layers: []
  });
  
  const [savedCovers, setSavedCovers] = useState<SavedCover[]>([]);
  const [metadata, setMetadata] = useState<Metadata>(initialMetadata || { title: '', artist: '' });
  const [showHistory, setShowHistory] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Generate initial cover on first render
  useEffect(() => {
    generateNewCover();
  }, []);

  // Update metadata if initialMetadata changes
  useEffect(() => {
    if (initialMetadata) {
      setMetadata(initialMetadata);
    }
  }, [initialMetadata]);

  // Generate new random cover
  const generateNewCover = useCallback(() => {
    const newConfig = generateRandomCover();
    setCoverConfig(newConfig);
    
    // Generate new metadata if none was provided
    if (!initialMetadata) {
      const newMetadata = generateMetadata();
      setMetadata(newMetadata);
    }
  }, [initialMetadata]);

  // Refine current cover
  const handleRefineCover = useCallback(() => {
    setCoverConfig(prevConfig => refineCover(prevConfig));
  }, []);

  // Save current cover to history
  const saveCover = useCallback(() => {
    const newSavedCover = {
      id: Date.now().toString(),
      config: { ...coverConfig },
      metadata: { ...metadata },
      timestamp: new Date().toISOString()
    };
    
    setSavedCovers(prev => [newSavedCover, ...prev]);
    
    // Call onSave callback if provided
    if (onSave) {
      // Create a blob from the cover for external use
      exportCoverAsImage('vinyl-cover', metadata, true)
        .then(blob => {
          if (blob) {
            onSave(coverConfig, metadata, blob);
          } else {
            onSave(coverConfig, metadata);
          }
        })
        .catch(err => {
          console.error('Error exporting cover image:', err);
          onSave(coverConfig, metadata);
        });
    }
  }, [coverConfig, metadata, onSave]);

  // Load a saved cover
  const loadSavedCover = useCallback((savedCover: SavedCover) => {
    setCoverConfig(savedCover.config);
    setMetadata(savedCover.metadata);
    setShowHistory(false);
  }, []);

  // Export cover as image
  const handleExport = useCallback(() => {
    exportCoverAsImage('vinyl-cover', metadata);
  }, [metadata]);

  // Copy current cover
  const duplicateCover = useCallback(() => {
    saveCover();
  }, [saveCover]);

  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Size classes for the cover display
  const sizeClasses = {
    sm: 'w-32 h-32',
    md: 'w-64 h-64',
    lg: 'w-96 h-96',
  };

  // Render compact version (just the cover with minimal controls)
  if (compact) {
    return (
      <div className={cn("relative group", className)}>
        <div className={cn("cover-generator-compact", sizeClasses[size])}>
          <CoverDisplay 
            config={coverConfig} 
            metadata={metadata}
            size={size}
          />
        </div>
        
        {showControls && (
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
            <Button 
              variant="ghost" 
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={generateNewCover}
              title="Generate New Cover"
            >
              <RefreshCw size={16} />
            </Button>
            
            <Button 
              variant="ghost" 
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={saveCover}
              title="Save Cover"
            >
              <Star size={16} />
            </Button>
          </div>
        )}
        
        <SVGFilters />
      </div>
    );
  }

  // Render full version
  return (
    <div className={cn(
      "vinyl-generator",
      isFullscreen ? "fixed inset-0 z-50 bg-background p-6" : "",
      className
    )}>
      <div className="vinyl-generator__header">
        <div className="flex items-center justify-between">
          <h1>Vinyl Cover Generator</h1>
          
          {isFullscreen && (
            <Button 
              variant="ghost" 
              size="icon"
              onClick={toggleFullscreen}
              title="Exit Fullscreen"
            >
              <X size={20} />
            </Button>
          )}
        </div>
        
        <div className="metadata">
          <h2>{metadata.title || 'Untitled Album'}</h2>
          <h3>{metadata.artist || 'Unknown Artist'}</h3>
        </div>
      </div>
      
      <div className="vinyl-generator__main">
        <div className="cover-container">
          <CoverDisplay 
            config={coverConfig} 
            metadata={metadata}
          />
        </div>
        
        <div className="control-container">
          <ColorPalette 
            currentPalette={coverConfig.palette} 
            onSelectPalette={(index) => setCoverConfig(prev => ({ ...prev, palette: index }))} 
          />
          
          <div className="action-buttons">
            <Button 
              variant="default" 
              className="action-button primary" 
              onClick={generateNewCover}
              title="Generate New Cover"
            >
              <RefreshCw size={18} />
              <span>Generate</span>
            </Button>
            
            <Button 
              variant="secondary"
              className="action-button" 
              onClick={handleRefineCover}
              title="Refine Current Cover"
            >
              <Sliders size={18} />
              <span>Refine</span>
            </Button>
            
            <Button 
              variant="secondary"
              className="action-button" 
              onClick={saveCover}
              title="Save to History"
            >
              <Star size={18} />
              <span>Save</span>
            </Button>
            
            <Button 
              variant="secondary"
              className="action-button" 
              onClick={handleExport}
              title="Export as Image"
            >
              <Save size={18} />
              <span>Export</span>
            </Button>
            
            <Button 
              variant="secondary"
              className="action-button" 
              onClick={() => setShowHistory(!showHistory)}
              title="View History"
            >
              <History size={18} />
              <span>History</span>
            </Button>
            
            <Button 
              variant="secondary"
              className="action-button" 
              onClick={duplicateCover}
              title="Duplicate Cover"
            >
              <Copy size={18} />
              <span>Duplicate</span>
            </Button>
            
            {!isFullscreen && (
              <Button 
                variant="secondary"
                className="action-button" 
                onClick={toggleFullscreen}
                title="Fullscreen"
              >
                <Maximize2 size={18} />
                <span>Fullscreen</span>
              </Button>
            )}
          </div>
          
          <ControlPanel 
            config={coverConfig} 
            onConfigChange={(newConfig) => setCoverConfig(prev => ({ ...prev, ...newConfig }))} 
          />
        </div>
      </div>
      
      {showHistory && (
        <HistoryPanel 
          savedCovers={savedCovers} 
          onSelectCover={loadSavedCover} 
          onClose={() => setShowHistory(false)} 
        />
      )}
      
      <SVGFilters />
    </div>
  );
};

/**
 * CoverGeneratorDialog component that wraps the CoverGenerator in a dialog
 */
export const CoverGeneratorDialog: React.FC<{
  trigger: React.ReactNode;
  initialMetadata?: Metadata;
  onSave?: (coverConfig: CoverConfig, metadata: Metadata, imageBlob?: Blob) => void;
}> = ({ trigger, initialMetadata, onSave }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Cover Generator</DialogTitle>
        </DialogHeader>
        <CoverGenerator 
          initialMetadata={initialMetadata} 
          onSave={onSave} 
        />
      </DialogContent>
    </Dialog>
  );
};

export default CoverGenerator;
