import React from 'react';
import { Droplet, Grid, Film, Wind } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/utils/ui/classNames';
import type { CoverConfig } from './types';

interface ControlPanelProps {
  config: CoverConfig;
  onConfigChange: (config: Partial<CoverConfig>) => void;
  className?: string;
}

/**
 * ControlPanel component for adjusting cover generation parameters
 */
const ControlPanel: React.FC<ControlPanelProps> = ({ 
  config, 
  onConfigChange,
  className
}) => {
  const handleSliderChange = (value: number[], property: keyof CoverConfig) => {
    onConfigChange({ [property]: value[0] });
  };
  
  const handleCheckboxChange = (checked: boolean, property: keyof CoverConfig) => {
    onConfigChange({ [property]: checked });
  };
  
  const handleSelectChange = (value: string, property: keyof CoverConfig) => {
    onConfigChange({ [property]: value });
  };
  
  return (
    <div className={cn("control-panel", className)}>
      <div className="control-panel__section">
        <h3 className="control-panel__title">
          <Film className="h-4 w-4" />
          <span>Grain Effect</span>
        </h3>
        <div className="control-panel__row">
          <Label>Opacity</Label>
          <Slider
            min={0}
            max={0.5}
            step={0.01}
            value={[config.grainOpacity]}
            onValueChange={(value) => handleSliderChange(value, 'grainOpacity')}
            className="flex-1"
          />
          <span className="value">{(config.grainOpacity * 100).toFixed(0)}%</span>
        </div>
        <div className="control-panel__row">
          <Label>Size</Label>
          <Slider
            min={0.5}
            max={2}
            step={0.1}
            value={[config.grainSize]}
            onValueChange={(value) => handleSliderChange(value, 'grainSize')}
            className="flex-1"
          />
          <span className="value">{config.grainSize.toFixed(1)}</span>
        </div>
        <div className="control-panel__row checkbox">
          <Label>Animated</Label>
          <Switch
            checked={config.grainAnimated}
            onCheckedChange={(checked) => handleCheckboxChange(checked, 'grainAnimated')}
          />
        </div>
      </div>
      
      <div className="control-panel__section">
        <h3 className="control-panel__title">
          <Grid className="h-4 w-4" />
          <span>Texture</span>
        </h3>
        <div className="control-panel__row">
          <Label>Type</Label>
          <Select
            value={config.textureType}
            onValueChange={(value) => handleSelectChange(value, 'textureType')}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select texture type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="dots">Dots</SelectItem>
              <SelectItem value="lines">Lines</SelectItem>
              <SelectItem value="circles">Circles</SelectItem>
              <SelectItem value="scattered">Scattered</SelectItem>
              <SelectItem value="none">None</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="control-panel__row">
          <Label>Opacity</Label>
          <Slider
            min={0}
            max={1}
            step={0.05}
            value={[config.textureOpacity]}
            onValueChange={(value) => handleSliderChange(value, 'textureOpacity')}
            className="flex-1"
          />
          <span className="value">{(config.textureOpacity * 100).toFixed(0)}%</span>
        </div>
        <div className="control-panel__row">
          <Label>Blend Mode</Label>
          <Select
            value={config.textureBlendMode}
            onValueChange={(value) => handleSelectChange(value, 'textureBlendMode')}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select blend mode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="overlay">Overlay</SelectItem>
              <SelectItem value="soft-light">Soft Light</SelectItem>
              <SelectItem value="color-burn">Color Burn</SelectItem>
              <SelectItem value="multiply">Multiply</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="control-panel__section">
        <h3 className="control-panel__title">
          <Wind className="h-4 w-4" />
          <span>Noise Effect</span>
        </h3>
        <div className="control-panel__row">
          <Label>Frequency X</Label>
          <Slider
            min={0.001}
            max={0.05}
            step={0.001}
            value={[config.noiseBaseFrequencyX]}
            onValueChange={(value) => handleSliderChange(value, 'noiseBaseFrequencyX')}
            className="flex-1"
          />
          <span className="value">{config.noiseBaseFrequencyX.toFixed(3)}</span>
        </div>
        <div className="control-panel__row">
          <Label>Frequency Y</Label>
          <Slider
            min={0.001}
            max={0.05}
            step={0.001}
            value={[config.noiseBaseFrequencyY]}
            onValueChange={(value) => handleSliderChange(value, 'noiseBaseFrequencyY')}
            className="flex-1"
          />
          <span className="value">{config.noiseBaseFrequencyY.toFixed(3)}</span>
        </div>
        <div className="control-panel__row">
          <Label>Scale</Label>
          <Slider
            min={30}
            max={120}
            step={1}
            value={[config.noiseScale]}
            onValueChange={(value) => handleSliderChange(value, 'noiseScale')}
            className="flex-1"
          />
          <span className="value">{config.noiseScale}</span>
        </div>
        <div className="control-panel__row">
          <Label>Octaves</Label>
          <Slider
            min={1}
            max={5}
            step={1}
            value={[config.noiseOctaves]}
            onValueChange={(value) => handleSliderChange(value, 'noiseOctaves')}
            className="flex-1"
          />
          <span className="value">{config.noiseOctaves}</span>
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
