// Color palette definition
export interface ColorPalette {
  name: string;
  colors: string[];
  mood: string;
}

// Layer configuration
export interface LayerConfig {
  type: string;
  translateX: number;
  translateY: number;
  scaleX: number;
  scaleY: number;
  rotate: number;
  skewX: number;
  skewY: number;
  blur: number;
  opacity: number;
  borderRadius: number[];
  color: string;
  gradient: boolean;
  secondaryColor: string;
  gradientType: string;
  gradientAngle: number;
  gradientPosition: string;
  blendMode: string;
}

// Cover configuration
export interface CoverConfig {
  palette: number;
  noiseBaseFrequencyX: number;
  noiseBaseFrequencyY: number;
  noiseScale: number;
  noiseOctaves: number;
  grainOpacity: number;
  grainSize: number;
  grainAnimated: boolean;
  textureType: string;
  textureOpacity: number;
  textureBlendMode: string;
  layers: LayerConfig[];
}

// Album metadata
export interface Metadata {
  title: string;
  artist: string;
}

// Saved cover history item
export interface SavedCover {
  id: string;
  config: CoverConfig;
  metadata: Metadata;
  timestamp: string;
}
