/**
 * WaveformManager
 * 
 * Manages waveform creation, destruction, and basic management
 * for the WaveSurfer visualization system.
 */

import WaveSurfer from 'wavesurfer.js';
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.esm.js';
import HoverPlugin from 'wavesurfer.js/dist/plugins/hover.esm.js';
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline.esm.js';
import EnvelopePlugin from 'wavesurfer.js/dist/plugins/envelope.esm.js';

import enhancedToneAudioEngine from '../audio/EnhancedToneAudioEngine';
import { ErrorHandler, ErrorType, ErrorSeverity } from '../../utils/ErrorHandler';
import { AudioFallback } from '../../utils/AudioFallback';
import { cleanupWaveSurferInstanceForTrack } from '@/utils/wavesurferCleanup';
import { batchDOMOperations, throttle } from '../../utils/performance';

export class WaveformManager {
  private instances: Map<string, WaveSurfer> = new Map();
  private containers: Map<string, HTMLElement> = new Map();
  private loadingPromises: Map<string, Promise<void>> = new Map();
  private envelopes: Map<string, EnvelopePlugin> = new Map();

  constructor() {
    console.log('[WaveformManager] Initialized');
  }

  /**
   * Create a WaveSurfer instance for a track
   */
  async createWaveform(trackId: string, container: HTMLElement, options: any = {}): Promise<WaveSurfer> {
    // First, check if the container already has waveform elements
    const existingWaveformElements = container.querySelectorAll('wave, canvas, .wavesurfer-wrapper');
    if (existingWaveformElements.length > 0) {
      console.log(`[WaveformManager] Container already has ${existingWaveformElements.length} waveform elements for track ${trackId}, cleaning up first`);

      // Remove all existing waveform elements from the container
      existingWaveformElements.forEach(element => {
        element.remove();
      });
    }

    // Check if we already have an instance for this track
    if (this.instances.has(trackId)) {
      console.log(`[WaveformManager] Destroying existing waveform instance for track ${trackId}`);
      await this.destroyWaveform(trackId);
    }

    // Check if we're already loading this track
    if (this.loadingPromises.has(trackId)) {
      console.log(`[WaveformManager] Waiting for existing loading promise for track ${trackId}`);
      try {
        await this.loadingPromises.get(trackId);
      } catch (error) {
        console.warn(`[WaveformManager] Previous loading attempt for track ${trackId} failed, will try again`);
      }

      // If we have an instance after waiting, return it
      if (this.instances.has(trackId)) {
        return this.instances.get(trackId)!;
      }
    }

    // If options.url is provided, ensure it's a streaming URL
    if (options.url) {
      try {
        // Import the tracks API dynamically to avoid circular dependencies
        const { getAudioStreamUrl } = await import('@/services/api/tracks');

        // Convert local file path to streaming URL if needed
        options.url = getAudioStreamUrl(trackId);
        console.log(`[WaveformManager] Using streaming URL for track ${trackId}: ${options.url}`);
      } catch (error) {
        console.error(`[WaveformManager] Error creating streaming URL for track ${trackId}:`, error);
      }
    }

    // Create a loading promise
    const loadingPromise = new Promise<void>(async (resolve, reject) => {
      try {
        console.log(`[WaveformManager] Creating waveform for track ${trackId}`);

        // Store the container
        this.containers.set(trackId, container);

        // Create regions plugin
        const regionsPlugin = RegionsPlugin.create();

        // Create hover plugin
        const hoverPlugin = HoverPlugin.create({
          lineColor: '#ff0000',
          lineWidth: 2,
          labelBackground: '#555',
          labelColor: '#fff',
          labelSize: '11px'
        });

        // Create timeline plugin
        const timelinePlugin = TimelinePlugin.create({
          height: 20,
          insertPosition: 'beforebegin',
          timeInterval: 0.2,
          primaryLabelInterval: 5,
          secondaryLabelInterval: 1,
          style: {
            fontSize: '10px',
            color: '#2D3748',
          },
        });

        // Create envelope plugin for fade in/out
        const envelopePlugin = EnvelopePlugin.create({
          lineColor: 'rgba(255, 0, 0, 0.7)',
          lineWidth: 4,
          dragPointSize: 8,
          dragPointFill: 'rgba(255, 255, 255, 0.8)',
          dragPointStroke: 'rgba(255, 0, 0, 0.8)',
        });

        // Store envelope plugin
        this.envelopes.set(trackId, envelopePlugin);

        // Default WaveSurfer options
        const defaultOptions = {
          container: container,
          waveColor: '#4F46E5',
          progressColor: '#7C3AED',
          cursorColor: '#EF4444',
          barWidth: 2,
          barRadius: 1,
          responsive: true,
          height: 80,
          normalize: true,
          backend: 'WebAudio',
          mediaControls: false,
          interact: true,
          hideScrollbar: true,
          plugins: [
            regionsPlugin,
            hoverPlugin,
            timelinePlugin,
            envelopePlugin
          ]
        };

        // Merge with provided options
        const finalOptions = { ...defaultOptions, ...options };

        // Create WaveSurfer instance
        const wavesurfer = WaveSurfer.create(finalOptions);

        // Store the instance
        this.instances.set(trackId, wavesurfer);

        // Set up event listeners
        this.setupWaveformEventListeners(trackId, wavesurfer);

        // Load the audio if URL is provided
        if (finalOptions.url) {
          console.log(`[WaveformManager] Loading audio for track ${trackId} from ${finalOptions.url}`);
          await wavesurfer.load(finalOptions.url);
        }

        console.log(`[WaveformManager] ✅ Waveform created successfully for track ${trackId}`);
        resolve();

      } catch (error) {
        console.error(`[WaveformManager] ❌ Error creating waveform for track ${trackId}:`, error);
        
        // Clean up on error
        this.instances.delete(trackId);
        this.containers.delete(trackId);
        this.envelopes.delete(trackId);
        
        reject(error);
      }
    });

    // Store the loading promise
    this.loadingPromises.set(trackId, loadingPromise);

    try {
      await loadingPromise;
      return this.instances.get(trackId)!;
    } finally {
      // Clean up the loading promise
      this.loadingPromises.delete(trackId);
    }
  }

  /**
   * Set up event listeners for a waveform
   */
  private setupWaveformEventListeners(trackId: string, wavesurfer: WaveSurfer): void {
    // Ready event
    wavesurfer.on('ready', () => {
      console.log(`[WaveformManager] Waveform ready for track ${trackId}`);
      
      // Connect to enhanced audio engine
      try {
        enhancedToneAudioEngine.connectWaveSurfer(trackId, wavesurfer);
      } catch (error) {
        console.warn(`[WaveformManager] Failed to connect to audio engine for track ${trackId}:`, error);
      }
    });

    // Error event
    wavesurfer.on('error', (error) => {
      console.error(`[WaveformManager] Waveform error for track ${trackId}:`, error);
      ErrorHandler.handleError(error, ErrorType.AUDIO_PLAYBACK, ErrorSeverity.HIGH, {
        trackId,
        context: 'waveform_error'
      });
    });

    // Loading events
    wavesurfer.on('loading', (percent) => {
      console.log(`[WaveformManager] Loading track ${trackId}: ${percent}%`);
    });

    // Interaction events
    wavesurfer.on('click', (relativeX) => {
      console.log(`[WaveformManager] Waveform clicked for track ${trackId} at ${relativeX}`);
    });

    // Seek event
    wavesurfer.on('seeking', (currentTime) => {
      console.log(`[WaveformManager] Seeking track ${trackId} to ${currentTime}s`);
    });
  }

  /**
   * Destroy a waveform instance
   */
  async destroyWaveform(trackId: string): Promise<void> {
    try {
      console.log(`[WaveformManager] Destroying waveform for track ${trackId}`);

      // Cancel any pending loading
      if (this.loadingPromises.has(trackId)) {
        this.loadingPromises.delete(trackId);
      }

      // Get the instance
      const wavesurfer = this.instances.get(trackId);
      if (wavesurfer) {
        try {
          // Disconnect from audio engine
          enhancedToneAudioEngine.disconnectWaveSurfer(trackId);
        } catch (error) {
          console.warn(`[WaveformManager] Error disconnecting from audio engine for track ${trackId}:`, error);
        }

        try {
          // Destroy the WaveSurfer instance
          wavesurfer.destroy();
        } catch (error) {
          console.warn(`[WaveformManager] Error destroying WaveSurfer instance for track ${trackId}:`, error);
        }

        // Remove from instances map
        this.instances.delete(trackId);
      }

      // Clean up envelope
      this.envelopes.delete(trackId);

      // Clean up container reference
      this.containers.delete(trackId);

      // Use utility function for additional cleanup
      try {
        cleanupWaveSurferInstanceForTrack(trackId);
      } catch (error) {
        console.warn(`[WaveformManager] Error in cleanup utility for track ${trackId}:`, error);
      }

      console.log(`[WaveformManager] ✅ Waveform destroyed for track ${trackId}`);

    } catch (error) {
      console.error(`[WaveformManager] ❌ Error destroying waveform for track ${trackId}:`, error);
      ErrorHandler.handleError(error, ErrorType.CLEANUP, ErrorSeverity.MEDIUM, {
        trackId,
        context: 'waveform_destruction'
      });
    }
  }

  /**
   * Get a waveform instance
   */
  getWaveform(trackId: string): WaveSurfer | undefined {
    return this.instances.get(trackId);
  }

  /**
   * Get the wrapper element for a waveform
   */
  getWrapper(trackId: string): HTMLElement | undefined {
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) return undefined;

    try {
      return wavesurfer.getWrapper();
    } catch (error) {
      console.warn(`[WaveformManager] Error getting wrapper for track ${trackId}:`, error);
      return undefined;
    }
  }

  /**
   * Check if a waveform exists
   */
  hasWaveform(trackId: string): boolean {
    try {
      // Check if the instance exists
      const hasInstance = this.instances.has(trackId);
      
      // Additional check: verify the instance is still valid
      if (hasInstance) {
        const wavesurfer = this.instances.get(trackId);
        if (!wavesurfer) {
          // Instance exists in map but is null/undefined - clean up
          this.instances.delete(trackId);
          return false;
        }
      }
      
      return hasInstance;
    } catch (error) {
      console.warn(`[WaveformManager] Error checking waveform existence for track ${trackId}:`, error);
      return false;
    }
  }

  /**
   * Get all waveform IDs
   */
  getAllWaveformIds(): string[] {
    return Array.from(this.instances.keys());
  }

  /**
   * Check if a waveform is ready
   */
  isWaveformReady(trackId: string): boolean {
    try {
      // First check if the waveform exists
      if (!this.hasWaveform(trackId)) {
        return false;
      }

      const wavesurfer = this.instances.get(trackId);
      if (!wavesurfer) {
        return false;
      }

      // Check if the waveform is ready (has been loaded)
      // WaveSurfer doesn't have a direct isReady method, so we check if it has a duration
      try {
        const duration = wavesurfer.getDuration();
        return duration > 0;
      } catch (error) {
        // If getDuration throws an error, the waveform is not ready
        return false;
      }
    } catch (error) {
      console.warn(`[WaveformManager] Error checking waveform readiness for track ${trackId}:`, error);
      return false;
    }
  }

  /**
   * Get envelope plugin for a track
   */
  getEnvelope(trackId: string): EnvelopePlugin | undefined {
    return this.envelopes.get(trackId);
  }

  /**
   * Get container for a track
   */
  getContainer(trackId: string): HTMLElement | undefined {
    return this.containers.get(trackId);
  }

  /**
   * Get instance count
   */
  getInstanceCount(): number {
    return this.instances.size;
  }

  /**
   * Dispose all waveforms
   */
  dispose(): void {
    // Destroy all instances
    this.instances.forEach((wavesurfer, trackId) => {
      this.destroyWaveform(trackId);
    });

    // Clear all maps
    this.instances.clear();
    this.containers.clear();
    this.loadingPromises.clear();
    this.envelopes.clear();

    console.log('[WaveformManager] Disposed all waveforms');
  }
}
