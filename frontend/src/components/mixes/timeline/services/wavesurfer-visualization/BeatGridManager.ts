/**
 * BeatGridManager
 * 
 * Manages beat grid visualization and management for WaveSurfer.
 */

import WaveSurfer from 'wavesurfer.js';
import { batchDOMOperations } from '../../utils/performance';

export class BeatGridManager {
  private beatGrids: Map<string, any> = new Map();
  private beatGridVisible: Map<string, boolean> = new Map();

  constructor() {
    console.log('[BeatGridManager] Initialized');
  }

  /**
   * Add beat grid regions to a waveform
   */
  addBeatGridRegions(trackId: string, beatGrid: any, getWaveform: (trackId: string) => WaveSurfer | undefined, addRegion: (trackId: string, regionId: string, options: any) => void): void {
    console.log(`[BeatGridManager] Adding beat grid regions for track ${trackId}:`, beatGrid);

    if (!beatGrid || !beatGrid.beats || beatGrid.beats.length === 0) {
      console.warn(`[BeatGridManager] No beat grid data for track ${trackId}`);
      return;
    }

    // Store beat grid
    this.beatGrids.set(trackId, beatGrid);

    // Check if beat grid should be visible
    if (!this.beatGridVisible.get(trackId)) {
      console.log(`[BeatGridManager] Beat grid not visible for track ${trackId}, skipping region creation`);
      return;
    }

    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[BeatGridManager] Cannot add beat grid regions: no waveform for track ${trackId}`);
        return;
      }

      // Get track duration
      const duration = wavesurfer.getDuration();
      if (duration <= 0) {
        console.warn(`[BeatGridManager] Cannot add beat grid regions: invalid duration for track ${trackId}`);
        return;
      }

      // Add beat markers
      beatGrid.beats.forEach((beat: any, index: number) => {
        const beatTime = beat.time || beat.position || 0;
        
        // Skip beats outside the track duration
        if (beatTime < 0 || beatTime > duration) {
          return;
        }

        // Determine beat type and color
        const isDownbeat = beat.beat === 1 || (index % 4 === 0);
        const color = isDownbeat ? 'rgba(255, 0, 0, 0.6)' : 'rgba(255, 255, 0, 0.4)';
        
        // Create beat marker region
        const regionId = `beat_${trackId}_${index}`;
        addRegion(regionId, {
          start: beatTime,
          end: beatTime + 0.01, // Very small region for beat marker
          color: color,
          drag: false,
          resize: false,
          content: isDownbeat ? '1' : beat.beat?.toString() || '',
          className: isDownbeat ? 'beat-marker downbeat' : 'beat-marker'
        });
      });

      console.log(`[BeatGridManager] ✅ Added ${beatGrid.beats.length} beat grid regions for track ${trackId}`);

    } catch (error) {
      console.error(`[BeatGridManager] ❌ Error adding beat grid regions for track ${trackId}:`, error);
    }
  }

  /**
   * Remove beat grid regions from a waveform
   */
  removeBeatGridRegions(trackId: string, getAllRegions: (trackId: string) => any[], removeRegion: (trackId: string, regionId: string) => void): void {
    try {
      console.log(`[BeatGridManager] Removing beat grid regions for track ${trackId}`);

      // Get all regions
      const regions = getAllRegions(trackId);
      
      // Remove beat grid regions
      regions.forEach((region: any) => {
        if (region.id && region.id.startsWith(`beat_${trackId}_`)) {
          removeRegion(trackId, region.id);
        }
      });

      console.log(`[BeatGridManager] ✅ Removed beat grid regions for track ${trackId}`);

    } catch (error) {
      console.error(`[BeatGridManager] ❌ Error removing beat grid regions for track ${trackId}:`, error);
    }
  }

  /**
   * Set beat grid visibility
   */
  setBeatGridVisible(trackId: string, visible: boolean, getWaveform: (trackId: string) => WaveSurfer | undefined, addRegion: (trackId: string, regionId: string, options: any) => void, getAllRegions: (trackId: string) => any[], removeRegion: (trackId: string, regionId: string) => void): void {
    console.log(`[BeatGridManager] Setting beat grid visibility for track ${trackId}: ${visible}`);

    this.beatGridVisible.set(trackId, visible);

    if (visible) {
      // Show beat grid - add regions if we have beat grid data
      const beatGrid = this.beatGrids.get(trackId);
      if (beatGrid) {
        this.addBeatGridRegions(trackId, beatGrid, getWaveform, addRegion);
      }
    } else {
      // Hide beat grid - remove regions
      this.removeBeatGridRegions(trackId, getAllRegions, removeRegion);
    }
  }

  /**
   * Check if beat grid is visible
   */
  isBeatGridVisible(trackId: string): boolean {
    return this.beatGridVisible.get(trackId) || false;
  }

  /**
   * Get beat grid data
   */
  getBeatGrid(trackId: string): any {
    return this.beatGrids.get(trackId);
  }

  /**
   * Update beat grid
   */
  updateBeatGrid(trackId: string, beatGrid: any, getWaveform: (trackId: string) => WaveSurfer | undefined, addRegion: (trackId: string, regionId: string, options: any) => void, getAllRegions: (trackId: string) => any[], removeRegion: (trackId: string, regionId: string) => void): void {
    console.log(`[BeatGridManager] Updating beat grid for track ${trackId}`);

    // Store the new beat grid
    this.beatGrids.set(trackId, beatGrid);

    // If beat grid is visible, refresh the regions
    if (this.beatGridVisible.get(trackId)) {
      // Remove existing beat grid regions
      this.removeBeatGridRegions(trackId, getAllRegions, removeRegion);
      
      // Add new beat grid regions
      this.addBeatGridRegions(trackId, beatGrid, getWaveform, addRegion);
    }
  }

  /**
   * Add beat marker at specific time
   */
  addBeatMarker(trackId: string, beatNumber: number, time: number, getWaveform: (trackId: string) => WaveSurfer | undefined, addRegion: (trackId: string, regionId: string, options: any) => void): void {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[BeatGridManager] Cannot add beat marker: no waveform for track ${trackId}`);
        return;
      }

      // Check if beat grid is visible
      if (!this.beatGridVisible.get(trackId)) {
        console.log(`[BeatGridManager] Beat grid not visible for track ${trackId}, skipping beat marker`);
        return;
      }

      // Determine if this is a downbeat
      const isDownbeat = beatNumber === 1 || (beatNumber % 4 === 1);
      const color = isDownbeat ? 'rgba(255, 0, 0, 0.6)' : 'rgba(255, 255, 0, 0.4)';
      
      // Create beat marker region
      const regionId = `beat_${trackId}_${beatNumber}`;
      addRegion(regionId, {
        start: time,
        end: time + 0.01, // Very small region for beat marker
        color: color,
        drag: false,
        resize: false,
        content: isDownbeat ? '1' : beatNumber.toString(),
        className: isDownbeat ? 'beat-marker downbeat' : 'beat-marker'
      });

      console.log(`[BeatGridManager] ✅ Added beat marker ${beatNumber} at ${time}s for track ${trackId}`);

    } catch (error) {
      console.error(`[BeatGridManager] ❌ Error adding beat marker for track ${trackId}:`, error);
    }
  }

  /**
   * Remove beat marker
   */
  removeBeatMarker(trackId: string, beatNumber: number, removeRegion: (trackId: string, regionId: string) => void): void {
    try {
      const regionId = `beat_${trackId}_${beatNumber}`;
      removeRegion(trackId, regionId);
      console.log(`[BeatGridManager] ✅ Removed beat marker ${beatNumber} for track ${trackId}`);
    } catch (error) {
      console.error(`[BeatGridManager] ❌ Error removing beat marker for track ${trackId}:`, error);
    }
  }

  /**
   * Get beat at time
   */
  getBeatAtTime(trackId: string, time: number): any {
    const beatGrid = this.beatGrids.get(trackId);
    if (!beatGrid || !beatGrid.beats) {
      return null;
    }

    // Find the closest beat
    let closestBeat = null;
    let minDistance = Infinity;

    beatGrid.beats.forEach((beat: any) => {
      const beatTime = beat.time || beat.position || 0;
      const distance = Math.abs(beatTime - time);
      
      if (distance < minDistance) {
        minDistance = distance;
        closestBeat = beat;
      }
    });

    return closestBeat;
  }

  /**
   * Snap time to nearest beat
   */
  snapToBeat(trackId: string, time: number, tolerance: number = 0.1): number {
    const beatGrid = this.beatGrids.get(trackId);
    if (!beatGrid || !beatGrid.beats) {
      return time;
    }

    // Find the closest beat within tolerance
    let closestBeatTime = time;
    let minDistance = tolerance;

    beatGrid.beats.forEach((beat: any) => {
      const beatTime = beat.time || beat.position || 0;
      const distance = Math.abs(beatTime - time);
      
      if (distance < minDistance) {
        minDistance = distance;
        closestBeatTime = beatTime;
      }
    });

    return closestBeatTime;
  }

  /**
   * Get beat grid statistics
   */
  getBeatGridStats(trackId: string): any {
    const beatGrid = this.beatGrids.get(trackId);
    if (!beatGrid || !beatGrid.beats) {
      return null;
    }

    const beats = beatGrid.beats;
    const beatCount = beats.length;
    
    if (beatCount < 2) {
      return { beatCount, averageInterval: 0, bpm: 0 };
    }

    // Calculate average interval between beats
    let totalInterval = 0;
    for (let i = 1; i < beatCount; i++) {
      const prevTime = beats[i - 1].time || beats[i - 1].position || 0;
      const currTime = beats[i].time || beats[i].position || 0;
      totalInterval += currTime - prevTime;
    }

    const averageInterval = totalInterval / (beatCount - 1);
    const bpm = averageInterval > 0 ? 60 / averageInterval : 0;

    return {
      beatCount,
      averageInterval,
      bpm,
      firstBeat: beats[0].time || beats[0].position || 0,
      lastBeat: beats[beatCount - 1].time || beats[beatCount - 1].position || 0
    };
  }

  /**
   * Generate beat grid from BPM
   */
  generateBeatGridFromBPM(trackId: string, bpm: number, offset: number = 0, getWaveform: (trackId: string) => WaveSurfer | undefined): any {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[BeatGridManager] Cannot generate beat grid: no waveform for track ${trackId}`);
        return null;
      }

      const duration = wavesurfer.getDuration();
      if (duration <= 0) {
        console.warn(`[BeatGridManager] Cannot generate beat grid: invalid duration for track ${trackId}`);
        return null;
      }

      // Calculate beat interval
      const beatInterval = 60 / bpm; // seconds per beat

      // Generate beats
      const beats = [];
      let beatNumber = 1;
      
      for (let time = offset; time < duration; time += beatInterval) {
        beats.push({
          beat: beatNumber,
          time: time,
          position: time
        });
        
        beatNumber++;
        if (beatNumber > 4) beatNumber = 1; // Reset to 1 after 4 (4/4 time)
      }

      const beatGrid = {
        bpm: bpm,
        offset: offset,
        beats: beats,
        generated: true
      };

      console.log(`[BeatGridManager] ✅ Generated beat grid for track ${trackId}: ${beats.length} beats at ${bpm} BPM`);
      return beatGrid;

    } catch (error) {
      console.error(`[BeatGridManager] ❌ Error generating beat grid for track ${trackId}:`, error);
      return null;
    }
  }

  /**
   * Clean up beat grid for a track
   */
  cleanupBeatGrid(trackId: string): void {
    this.beatGrids.delete(trackId);
    this.beatGridVisible.delete(trackId);
  }

  /**
   * Get memory stats
   */
  getMemoryStats(): { beatGrids: number } {
    return { beatGrids: this.beatGrids.size };
  }

  /**
   * Dispose all beat grids
   */
  dispose(): void {
    this.beatGrids.clear();
    this.beatGridVisible.clear();
    console.log('[BeatGridManager] Disposed all beat grids');
  }
}
