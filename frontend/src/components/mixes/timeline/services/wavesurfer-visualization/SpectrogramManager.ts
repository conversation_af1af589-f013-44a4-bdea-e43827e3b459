/**
 * SpectrogramManager
 * 
 * Manages spectrogram visualization functionality for WaveSurfer.
 */

import WaveSurfer from 'wavesurfer.js';
import SpectrogramPlugin from 'wavesurfer.js/dist/plugins/spectrogram.esm.js';

export class SpectrogramManager {
  private spectrograms: Map<string, SpectrogramPlugin> = new Map();
  private spectrogramVisible: Map<string, boolean> = new Map();
  private spectrogramContainers: Map<string, HTMLElement> = new Map();

  constructor() {
    console.log('[SpectrogramManager] Initialized');
  }

  /**
   * Create and add spectrogram to a waveform
   */
  addSpectrogram(trackId: string, container: HTMLElement, options: any = {}, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      console.log(`[SpectrogramManager] Adding spectrogram for track ${trackId}`);

      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[SpectrogramManager] Cannot add spectrogram: no waveform for track ${trackId}`);
        return;
      }

      // Check if spectrogram already exists
      if (this.spectrograms.has(trackId)) {
        console.log(`[SpectrogramManager] Spectrogram already exists for track ${trackId}, removing first`);
        this.removeSpectrogram(trackId, getWaveform);
      }

      // Default spectrogram options
      const defaultOptions = {
        height: 200,
        fftSamples: 512,
        noverlap: 256,
        windowFunc: 'hann',
        alpha: 0.8,
        colorMap: [
          [0, 0, 0, 0],
          [0, 0, 255, 1],
          [0, 255, 255, 1],
          [255, 255, 0, 1],
          [255, 0, 0, 1]
        ]
      };

      // Merge with provided options
      const finalOptions = { ...defaultOptions, ...options };

      // Create spectrogram plugin
      const spectrogramPlugin = SpectrogramPlugin.create({
        container: container,
        ...finalOptions
      });

      // Register the plugin with WaveSurfer
      wavesurfer.registerPlugin(spectrogramPlugin);

      // Store references
      this.spectrograms.set(trackId, spectrogramPlugin);
      this.spectrogramContainers.set(trackId, container);
      this.spectrogramVisible.set(trackId, true);

      console.log(`[SpectrogramManager] ✅ Spectrogram added for track ${trackId}`);

    } catch (error) {
      console.error(`[SpectrogramManager] ❌ Error adding spectrogram for track ${trackId}:`, error);
    }
  }

  /**
   * Remove spectrogram from a waveform
   */
  removeSpectrogram(trackId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      console.log(`[SpectrogramManager] Removing spectrogram for track ${trackId}`);

      // Get the spectrogram plugin
      const spectrogramPlugin = this.spectrograms.get(trackId);
      if (!spectrogramPlugin) {
        console.warn(`[SpectrogramManager] No spectrogram found for track ${trackId}`);
        return;
      }

      // Get the waveform instance
      const wavesurfer = getWaveform(trackId);
      if (wavesurfer) {
        try {
          // Unregister the plugin
          wavesurfer.unregisterPlugin(spectrogramPlugin);
        } catch (error) {
          console.warn(`[SpectrogramManager] Error unregistering spectrogram plugin for track ${trackId}:`, error);
        }
      }

      // Clean up references
      this.spectrograms.delete(trackId);
      this.spectrogramContainers.delete(trackId);
      this.spectrogramVisible.delete(trackId);

      console.log(`[SpectrogramManager] ✅ Spectrogram removed for track ${trackId}`);

    } catch (error) {
      console.error(`[SpectrogramManager] ❌ Error removing spectrogram for track ${trackId}:`, error);
    }
  }

  /**
   * Set spectrogram visibility
   */
  setSpectrogramVisible(trackId: string, visible: boolean, container?: HTMLElement, getWaveform?: (trackId: string) => WaveSurfer | undefined): void {
    console.log(`[SpectrogramManager] Setting spectrogram visibility for track ${trackId}: ${visible}`);

    const currentlyVisible = this.spectrogramVisible.get(trackId) || false;

    if (visible && !currentlyVisible) {
      // Show spectrogram
      if (container && getWaveform) {
        this.addSpectrogram(trackId, container, {}, getWaveform);
      } else {
        console.warn(`[SpectrogramManager] Cannot show spectrogram for track ${trackId}: missing container or getWaveform function`);
      }
    } else if (!visible && currentlyVisible) {
      // Hide spectrogram
      if (getWaveform) {
        this.removeSpectrogram(trackId, getWaveform);
      }
    }

    this.spectrogramVisible.set(trackId, visible);
  }

  /**
   * Check if spectrogram is visible
   */
  isSpectrogramVisible(trackId: string): boolean {
    return this.spectrogramVisible.get(trackId) || false;
  }

  /**
   * Get spectrogram plugin
   */
  getSpectrogram(trackId: string): SpectrogramPlugin | undefined {
    return this.spectrograms.get(trackId);
  }

  /**
   * Update spectrogram options
   */
  updateSpectrogramOptions(trackId: string, options: any, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      console.log(`[SpectrogramManager] Updating spectrogram options for track ${trackId}:`, options);

      // Get the spectrogram plugin
      const spectrogramPlugin = this.spectrograms.get(trackId);
      if (!spectrogramPlugin) {
        console.warn(`[SpectrogramManager] No spectrogram found for track ${trackId}`);
        return;
      }

      // Get container
      const container = this.spectrogramContainers.get(trackId);
      if (!container) {
        console.warn(`[SpectrogramManager] No container found for spectrogram ${trackId}`);
        return;
      }

      // Remove and recreate with new options
      this.removeSpectrogram(trackId, getWaveform);
      this.addSpectrogram(trackId, container, options, getWaveform);

      console.log(`[SpectrogramManager] ✅ Spectrogram options updated for track ${trackId}`);

    } catch (error) {
      console.error(`[SpectrogramManager] ❌ Error updating spectrogram options for track ${trackId}:`, error);
    }
  }

  /**
   * Set spectrogram color map
   */
  setSpectrogramColorMap(trackId: string, colorMap: number[][], getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    this.updateSpectrogramOptions(trackId, { colorMap }, getWaveform);
  }

  /**
   * Set spectrogram height
   */
  setSpectrogramHeight(trackId: string, height: number, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    this.updateSpectrogramOptions(trackId, { height }, getWaveform);
  }

  /**
   * Set spectrogram FFT samples
   */
  setSpectrogramFFTSamples(trackId: string, fftSamples: number, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    this.updateSpectrogramOptions(trackId, { fftSamples }, getWaveform);
  }

  /**
   * Get predefined color maps
   */
  getPredefinedColorMaps(): Record<string, number[][]> {
    return {
      'default': [
        [0, 0, 0, 0],
        [0, 0, 255, 1],
        [0, 255, 255, 1],
        [255, 255, 0, 1],
        [255, 0, 0, 1]
      ],
      'grayscale': [
        [0, 0, 0, 0],
        [64, 64, 64, 1],
        [128, 128, 128, 1],
        [192, 192, 192, 1],
        [255, 255, 255, 1]
      ],
      'hot': [
        [0, 0, 0, 0],
        [128, 0, 0, 1],
        [255, 0, 0, 1],
        [255, 128, 0, 1],
        [255, 255, 0, 1],
        [255, 255, 255, 1]
      ],
      'cool': [
        [0, 0, 0, 0],
        [0, 128, 255, 1],
        [0, 255, 255, 1],
        [128, 255, 128, 1],
        [255, 255, 0, 1]
      ],
      'viridis': [
        [68, 1, 84, 1],
        [59, 82, 139, 1],
        [33, 144, 140, 1],
        [93, 201, 99, 1],
        [253, 231, 37, 1]
      ]
    };
  }

  /**
   * Apply predefined color map
   */
  applyPredefinedColorMap(trackId: string, colorMapName: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    const colorMaps = this.getPredefinedColorMaps();
    const colorMap = colorMaps[colorMapName];
    
    if (colorMap) {
      this.setSpectrogramColorMap(trackId, colorMap, getWaveform);
      console.log(`[SpectrogramManager] Applied ${colorMapName} color map to track ${trackId}`);
    } else {
      console.warn(`[SpectrogramManager] Unknown color map: ${colorMapName}`);
    }
  }

  /**
   * Export spectrogram as image
   */
  exportSpectrogramAsImage(trackId: string): string | null {
    try {
      const spectrogramPlugin = this.spectrograms.get(trackId);
      if (!spectrogramPlugin) {
        console.warn(`[SpectrogramManager] No spectrogram found for track ${trackId}`);
        return null;
      }

      // Get the canvas element from the spectrogram
      const container = this.spectrogramContainers.get(trackId);
      if (!container) {
        console.warn(`[SpectrogramManager] No container found for spectrogram ${trackId}`);
        return null;
      }

      const canvas = container.querySelector('canvas');
      if (!canvas) {
        console.warn(`[SpectrogramManager] No canvas found in spectrogram container for track ${trackId}`);
        return null;
      }

      // Export as data URL
      const dataURL = canvas.toDataURL('image/png');
      console.log(`[SpectrogramManager] ✅ Exported spectrogram as image for track ${trackId}`);
      return dataURL;

    } catch (error) {
      console.error(`[SpectrogramManager] ❌ Error exporting spectrogram as image for track ${trackId}:`, error);
      return null;
    }
  }

  /**
   * Get spectrogram analysis data
   */
  getSpectrogramAnalysisData(trackId: string): any {
    try {
      const spectrogramPlugin = this.spectrograms.get(trackId);
      if (!spectrogramPlugin) {
        console.warn(`[SpectrogramManager] No spectrogram found for track ${trackId}`);
        return null;
      }

      // This would depend on the specific spectrogram plugin implementation
      // For now, return basic info
      return {
        trackId,
        visible: this.spectrogramVisible.get(trackId),
        hasSpectrogram: true
      };

    } catch (error) {
      console.error(`[SpectrogramManager] ❌ Error getting spectrogram analysis data for track ${trackId}:`, error);
      return null;
    }
  }

  /**
   * Clean up spectrogram for a track
   */
  cleanupSpectrogram(trackId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    if (this.spectrograms.has(trackId)) {
      this.removeSpectrogram(trackId, getWaveform);
    }
  }

  /**
   * Get memory stats
   */
  getMemoryStats(): { spectrograms: number } {
    return { spectrograms: this.spectrograms.size };
  }

  /**
   * Dispose all spectrograms
   */
  dispose(getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    // Remove all spectrograms
    this.spectrograms.forEach((_, trackId) => {
      this.removeSpectrogram(trackId, getWaveform);
    });

    // Clear all maps
    this.spectrograms.clear();
    this.spectrogramVisible.clear();
    this.spectrogramContainers.clear();

    console.log('[SpectrogramManager] Disposed all spectrograms');
  }
}
