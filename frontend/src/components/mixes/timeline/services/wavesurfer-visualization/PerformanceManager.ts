/**
 * PerformanceManager
 * 
 * Manages performance optimization and cleanup functionality for WaveSurfer.
 */

import { batchDOMOperations, throttle } from '../../utils/performance';
import { cleanupWaveSurferInstanceForTrack } from '@/utils/wavesurferCleanup';

export class PerformanceManager {
  private memoryStats: Map<string, any> = new Map();
  private performanceMetrics: Map<string, any> = new Map();
  private cleanupCallbacks: Map<string, (() => void)[]> = new Map();

  constructor() {
    console.log('[PerformanceManager] Initialized');
  }

  /**
   * Track memory usage for a waveform
   */
  trackMemoryUsage(trackId: string, type: string, size: number): void {
    if (!this.memoryStats.has(trackId)) {
      this.memoryStats.set(trackId, {});
    }

    const stats = this.memoryStats.get(trackId);
    stats[type] = size;
    stats.lastUpdated = Date.now();

    console.log(`[PerformanceManager] Memory tracked for ${trackId} (${type}): ${size} bytes`);
  }

  /**
   * Get memory stats for a track
   */
  getMemoryStats(trackId: string): any {
    return this.memoryStats.get(trackId) || {};
  }

  /**
   * Get total memory usage
   */
  getTotalMemoryUsage(): number {
    let total = 0;
    this.memoryStats.forEach(stats => {
      Object.keys(stats).forEach(key => {
        if (key !== 'lastUpdated' && typeof stats[key] === 'number') {
          total += stats[key];
        }
      });
    });
    return total;
  }

  /**
   * Track performance metric
   */
  trackPerformanceMetric(trackId: string, metric: string, value: number): void {
    if (!this.performanceMetrics.has(trackId)) {
      this.performanceMetrics.set(trackId, {});
    }

    const metrics = this.performanceMetrics.get(trackId);
    if (!metrics[metric]) {
      metrics[metric] = [];
    }

    metrics[metric].push({
      value,
      timestamp: Date.now()
    });

    // Keep only last 100 measurements
    if (metrics[metric].length > 100) {
      metrics[metric] = metrics[metric].slice(-100);
    }

    console.log(`[PerformanceManager] Performance metric tracked for ${trackId} (${metric}): ${value}`);
  }

  /**
   * Get performance metrics for a track
   */
  getPerformanceMetrics(trackId: string): any {
    return this.performanceMetrics.get(trackId) || {};
  }

  /**
   * Get average performance metric
   */
  getAveragePerformanceMetric(trackId: string, metric: string): number {
    const metrics = this.performanceMetrics.get(trackId);
    if (!metrics || !metrics[metric] || metrics[metric].length === 0) {
      return 0;
    }

    const values = metrics[metric].map((m: any) => m.value);
    return values.reduce((sum: number, val: number) => sum + val, 0) / values.length;
  }

  /**
   * Optimize waveform performance
   */
  optimizeWaveformPerformance(trackId: string, getWaveform: (trackId: string) => any): void {
    try {
      console.log(`[PerformanceManager] Optimizing performance for track ${trackId}`);

      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[PerformanceManager] No waveform found for track ${trackId}`);
        return;
      }

      // Batch DOM operations for better performance
      batchDOMOperations(() => {
        // Optimize rendering settings
        if (wavesurfer.setOptions) {
          wavesurfer.setOptions({
            pixelRatio: Math.min(window.devicePixelRatio, 2), // Limit pixel ratio for performance
            normalize: true, // Normalize waveform for consistent rendering
            hideScrollbar: true, // Hide scrollbar for cleaner UI
          });
        }

        // Optimize canvas rendering
        const canvas = wavesurfer.getWrapper()?.querySelector('canvas');
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            // Enable hardware acceleration
            ctx.imageSmoothingEnabled = false;
            ctx.imageSmoothingQuality = 'low';
          }
        }
      });

      console.log(`[PerformanceManager] ✅ Performance optimized for track ${trackId}`);

    } catch (error) {
      console.error(`[PerformanceManager] ❌ Error optimizing performance for track ${trackId}:`, error);
    }
  }

  /**
   * Clean up resources for a track
   */
  cleanupTrackResources(trackId: string): void {
    try {
      console.log(`[PerformanceManager] Cleaning up resources for track ${trackId}`);

      // Run custom cleanup callbacks
      const callbacks = this.cleanupCallbacks.get(trackId);
      if (callbacks) {
        callbacks.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.warn(`[PerformanceManager] Error in cleanup callback for track ${trackId}:`, error);
          }
        });
      }

      // Use utility function for additional cleanup
      try {
        cleanupWaveSurferInstanceForTrack(trackId);
      } catch (error) {
        console.warn(`[PerformanceManager] Error in cleanup utility for track ${trackId}:`, error);
      }

      // Clean up our tracking data
      this.memoryStats.delete(trackId);
      this.performanceMetrics.delete(trackId);
      this.cleanupCallbacks.delete(trackId);

      console.log(`[PerformanceManager] ✅ Resources cleaned up for track ${trackId}`);

    } catch (error) {
      console.error(`[PerformanceManager] ❌ Error cleaning up resources for track ${trackId}:`, error);
    }
  }

  /**
   * Add cleanup callback for a track
   */
  addCleanupCallback(trackId: string, callback: () => void): void {
    if (!this.cleanupCallbacks.has(trackId)) {
      this.cleanupCallbacks.set(trackId, []);
    }

    this.cleanupCallbacks.get(trackId)!.push(callback);
    console.log(`[PerformanceManager] Cleanup callback added for track ${trackId}`);
  }

  /**
   * Force garbage collection (if available)
   */
  forceGarbageCollection(): void {
    try {
      // Force garbage collection if available (Chrome DevTools)
      if (window.gc) {
        window.gc();
        console.log('[PerformanceManager] Forced garbage collection');
      } else {
        console.log('[PerformanceManager] Garbage collection not available');
      }
    } catch (error) {
      console.warn('[PerformanceManager] Error forcing garbage collection:', error);
    }
  }

  /**
   * Monitor memory usage
   */
  monitorMemoryUsage(): any {
    try {
      // Get memory info if available (Chrome)
      if (performance.memory) {
        const memInfo = {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
          timestamp: Date.now()
        };

        console.log('[PerformanceManager] Memory usage:', memInfo);
        return memInfo;
      } else {
        console.log('[PerformanceManager] Memory monitoring not available');
        return null;
      }
    } catch (error) {
      console.warn('[PerformanceManager] Error monitoring memory usage:', error);
      return null;
    }
  }

  /**
   * Optimize all waveforms
   */
  optimizeAllWaveforms(getAllWaveformIds: () => string[], getWaveform: (trackId: string) => any): void {
    try {
      console.log('[PerformanceManager] Optimizing all waveforms');

      const trackIds = getAllWaveformIds();
      
      // Batch optimize all waveforms
      batchDOMOperations(() => {
        trackIds.forEach(trackId => {
          this.optimizeWaveformPerformance(trackId, getWaveform);
        });
      });

      console.log(`[PerformanceManager] ✅ Optimized ${trackIds.length} waveforms`);

    } catch (error) {
      console.error('[PerformanceManager] ❌ Error optimizing all waveforms:', error);
    }
  }

  /**
   * Clean up all resources
   */
  cleanupAllResources(getAllWaveformIds: () => string[]): void {
    try {
      console.log('[PerformanceManager] Cleaning up all resources');

      const trackIds = getAllWaveformIds();
      
      trackIds.forEach(trackId => {
        this.cleanupTrackResources(trackId);
      });

      // Force garbage collection after cleanup
      setTimeout(() => {
        this.forceGarbageCollection();
      }, 100);

      console.log(`[PerformanceManager] ✅ Cleaned up resources for ${trackIds.length} tracks`);

    } catch (error) {
      console.error('[PerformanceManager] ❌ Error cleaning up all resources:', error);
    }
  }

  /**
   * Get performance report
   */
  getPerformanceReport(): any {
    const report = {
      totalTracks: this.memoryStats.size,
      totalMemoryUsage: this.getTotalMemoryUsage(),
      memoryByTrack: {},
      performanceByTrack: {},
      systemMemory: this.monitorMemoryUsage(),
      timestamp: Date.now()
    };

    // Add per-track data
    this.memoryStats.forEach((stats, trackId) => {
      report.memoryByTrack[trackId] = stats;
    });

    this.performanceMetrics.forEach((metrics, trackId) => {
      report.performanceByTrack[trackId] = {};
      Object.keys(metrics).forEach(metric => {
        report.performanceByTrack[trackId][metric] = this.getAveragePerformanceMetric(trackId, metric);
      });
    });

    return report;
  }

  /**
   * Log performance report
   */
  logPerformanceReport(): void {
    const report = this.getPerformanceReport();
    console.log('[PerformanceManager] Performance Report:', report);
  }

  /**
   * Throttled performance optimization
   */
  throttledOptimize = throttle((trackId: string, getWaveform: (trackId: string) => any) => {
    this.optimizeWaveformPerformance(trackId, getWaveform);
  }, 1000);

  /**
   * Dispose all performance tracking
   */
  dispose(): void {
    // Clean up all tracking data
    this.memoryStats.clear();
    this.performanceMetrics.clear();
    this.cleanupCallbacks.clear();

    console.log('[PerformanceManager] Disposed all performance tracking');
  }
}
