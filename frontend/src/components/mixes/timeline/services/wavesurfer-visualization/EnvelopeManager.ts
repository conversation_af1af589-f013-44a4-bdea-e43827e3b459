/**
 * EnvelopeManager
 * 
 * Manages envelope/fade management functionality for WaveSurfer.
 */

import WaveSurfer from 'wavesurfer.js';
import EnvelopePlugin from 'wavesurfer.js/dist/plugins/envelope.esm.js';

export class EnvelopeManager {
  private envelopes: Map<string, EnvelopePlugin> = new Map();
  private envelopeVisible: Map<string, boolean> = new Map();

  constructor() {
    console.log('[EnvelopeManager] Initialized');
  }

  /**
   * Get envelope plugin for a track
   */
  getEnvelope(trackId: string): EnvelopePlugin | undefined {
    return this.envelopes.get(trackId);
  }

  /**
   * Set envelope plugin for a track
   */
  setEnvelope(trackId: string, envelope: EnvelopePlugin): void {
    this.envelopes.set(trackId, envelope);
  }

  /**
   * Add fade in to a track
   */
  addFadeIn(trackId: string, duration: number): void {
    try {
      console.log(`[EnvelopeManager] Adding fade in (${duration}s) to track ${trackId}`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      // Add fade in point at the beginning
      envelope.addPoint({ time: 0, volume: 0 });
      envelope.addPoint({ time: duration, volume: 1 });

      console.log(`[EnvelopeManager] ✅ Fade in added to track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error adding fade in to track ${trackId}:`, error);
    }
  }

  /**
   * Add fade out to a track
   */
  addFadeOut(trackId: string, startTime: number, duration: number, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      console.log(`[EnvelopeManager] Adding fade out (${duration}s) to track ${trackId} starting at ${startTime}s`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      // Get track duration
      const wavesurfer = getWaveform(trackId);
      const trackDuration = wavesurfer ? wavesurfer.getDuration() : startTime + duration;

      // Add fade out points
      envelope.addPoint({ time: startTime, volume: 1 });
      envelope.addPoint({ time: Math.min(startTime + duration, trackDuration), volume: 0 });

      console.log(`[EnvelopeManager] ✅ Fade out added to track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error adding fade out to track ${trackId}:`, error);
    }
  }

  /**
   * Add crossfade between two tracks
   */
  addCrossfade(fromTrackId: string, toTrackId: string, crossfadeTime: number, duration: number, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      console.log(`[EnvelopeManager] Adding crossfade from track ${fromTrackId} to ${toTrackId} at ${crossfadeTime}s (${duration}s duration)`);

      // Add fade out to the first track
      this.addFadeOut(fromTrackId, crossfadeTime, duration, getWaveform);

      // Add fade in to the second track
      this.addFadeIn(toTrackId, duration);

      console.log(`[EnvelopeManager] ✅ Crossfade added between tracks ${fromTrackId} and ${toTrackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error adding crossfade between tracks ${fromTrackId} and ${toTrackId}:`, error);
    }
  }

  /**
   * Add envelope point
   */
  addEnvelopePoint(trackId: string, time: number, volume: number): void {
    try {
      console.log(`[EnvelopeManager] Adding envelope point to track ${trackId} at ${time}s with volume ${volume}`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      // Validate volume (0-1 range)
      const clampedVolume = Math.max(0, Math.min(1, volume));
      if (clampedVolume !== volume) {
        console.warn(`[EnvelopeManager] Volume ${volume} clamped to ${clampedVolume} for track ${trackId}`);
      }

      // Add the point
      envelope.addPoint({ time, volume: clampedVolume });

      console.log(`[EnvelopeManager] ✅ Envelope point added to track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error adding envelope point to track ${trackId}:`, error);
    }
  }

  /**
   * Remove envelope point
   */
  removeEnvelopePoint(trackId: string, pointIndex: number): void {
    try {
      console.log(`[EnvelopeManager] Removing envelope point ${pointIndex} from track ${trackId}`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      // Remove the point (implementation depends on envelope plugin API)
      if (typeof envelope.removePoint === 'function') {
        envelope.removePoint(pointIndex);
      } else {
        console.warn(`[EnvelopeManager] removePoint method not available for track ${trackId}`);
      }

      console.log(`[EnvelopeManager] ✅ Envelope point removed from track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error removing envelope point from track ${trackId}:`, error);
    }
  }

  /**
   * Clear all envelope points
   */
  clearEnvelope(trackId: string): void {
    try {
      console.log(`[EnvelopeManager] Clearing envelope for track ${trackId}`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      // Clear all points (implementation depends on envelope plugin API)
      if (typeof envelope.clearPoints === 'function') {
        envelope.clearPoints();
      } else if (typeof envelope.clear === 'function') {
        envelope.clear();
      } else {
        console.warn(`[EnvelopeManager] Clear method not available for track ${trackId}`);
      }

      console.log(`[EnvelopeManager] ✅ Envelope cleared for track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error clearing envelope for track ${trackId}:`, error);
    }
  }

  /**
   * Set envelope visibility
   */
  setEnvelopeVisible(trackId: string, visible: boolean): void {
    console.log(`[EnvelopeManager] Setting envelope visibility for track ${trackId}: ${visible}`);

    this.envelopeVisible.set(trackId, visible);

    const envelope = this.envelopes.get(trackId);
    if (envelope) {
      try {
        // Show/hide envelope (implementation depends on envelope plugin API)
        if (typeof envelope.setVisible === 'function') {
          envelope.setVisible(visible);
        } else if (typeof envelope.show === 'function' && typeof envelope.hide === 'function') {
          if (visible) {
            envelope.show();
          } else {
            envelope.hide();
          }
        } else {
          console.warn(`[EnvelopeManager] Visibility methods not available for track ${trackId}`);
        }
      } catch (error) {
        console.warn(`[EnvelopeManager] Error setting envelope visibility for track ${trackId}:`, error);
      }
    }
  }

  /**
   * Check if envelope is visible
   */
  isEnvelopeVisible(trackId: string): boolean {
    return this.envelopeVisible.get(trackId) || false;
  }

  /**
   * Get envelope points
   */
  getEnvelopePoints(trackId: string): any[] {
    try {
      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        return [];
      }

      // Get points (implementation depends on envelope plugin API)
      if (typeof envelope.getPoints === 'function') {
        return envelope.getPoints();
      } else if (envelope.points) {
        return envelope.points;
      } else {
        console.warn(`[EnvelopeManager] getPoints method not available for track ${trackId}`);
        return [];
      }

    } catch (error) {
      console.error(`[EnvelopeManager] Error getting envelope points for track ${trackId}:`, error);
      return [];
    }
  }

  /**
   * Set envelope curve type
   */
  setEnvelopeCurveType(trackId: string, curveType: 'linear' | 'exponential' | 'logarithmic'): void {
    try {
      console.log(`[EnvelopeManager] Setting envelope curve type for track ${trackId}: ${curveType}`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      // Set curve type (implementation depends on envelope plugin API)
      if (typeof envelope.setCurveType === 'function') {
        envelope.setCurveType(curveType);
      } else {
        console.warn(`[EnvelopeManager] setCurveType method not available for track ${trackId}`);
      }

      console.log(`[EnvelopeManager] ✅ Envelope curve type set for track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error setting envelope curve type for track ${trackId}:`, error);
    }
  }

  /**
   * Apply envelope to audio
   */
  applyEnvelope(trackId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      console.log(`[EnvelopeManager] Applying envelope to track ${trackId}`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[EnvelopeManager] No waveform found for track ${trackId}`);
        return;
      }

      // Apply envelope (implementation depends on envelope plugin API)
      if (typeof envelope.apply === 'function') {
        envelope.apply();
      } else {
        console.warn(`[EnvelopeManager] apply method not available for track ${trackId}`);
      }

      console.log(`[EnvelopeManager] ✅ Envelope applied to track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error applying envelope to track ${trackId}:`, error);
    }
  }

  /**
   * Create preset fade shapes
   */
  createPresetFade(trackId: string, type: 'linear' | 'exponential' | 'logarithmic' | 'scurve', startTime: number, endTime: number, startVolume: number = 1, endVolume: number = 0): void {
    try {
      console.log(`[EnvelopeManager] Creating ${type} fade for track ${trackId} from ${startTime}s to ${endTime}s`);

      const envelope = this.envelopes.get(trackId);
      if (!envelope) {
        console.warn(`[EnvelopeManager] No envelope plugin found for track ${trackId}`);
        return;
      }

      const duration = endTime - startTime;
      const steps = 10; // Number of intermediate points for smooth curves

      switch (type) {
        case 'linear':
          envelope.addPoint({ time: startTime, volume: startVolume });
          envelope.addPoint({ time: endTime, volume: endVolume });
          break;

        case 'exponential':
          for (let i = 0; i <= steps; i++) {
            const progress = i / steps;
            const time = startTime + (progress * duration);
            const volume = startVolume + (endVolume - startVolume) * Math.pow(progress, 2);
            envelope.addPoint({ time, volume });
          }
          break;

        case 'logarithmic':
          for (let i = 0; i <= steps; i++) {
            const progress = i / steps;
            const time = startTime + (progress * duration);
            const volume = startVolume + (endVolume - startVolume) * Math.sqrt(progress);
            envelope.addPoint({ time, volume });
          }
          break;

        case 'scurve':
          for (let i = 0; i <= steps; i++) {
            const progress = i / steps;
            const time = startTime + (progress * duration);
            // S-curve using smoothstep function
            const smoothProgress = progress * progress * (3 - 2 * progress);
            const volume = startVolume + (endVolume - startVolume) * smoothProgress;
            envelope.addPoint({ time, volume });
          }
          break;
      }

      console.log(`[EnvelopeManager] ✅ ${type} fade created for track ${trackId}`);

    } catch (error) {
      console.error(`[EnvelopeManager] ❌ Error creating preset fade for track ${trackId}:`, error);
    }
  }

  /**
   * Clean up envelope for a track
   */
  cleanupEnvelope(trackId: string): void {
    this.envelopes.delete(trackId);
    this.envelopeVisible.delete(trackId);
  }

  /**
   * Get memory stats
   */
  getMemoryStats(): { envelopes: number } {
    return { envelopes: this.envelopes.size };
  }

  /**
   * Dispose all envelopes
   */
  dispose(): void {
    this.envelopes.clear();
    this.envelopeVisible.clear();
    console.log('[EnvelopeManager] Disposed all envelopes');
  }
}
