/**
 * WaveSurfer Visualization Package
 * 
 * Modular WaveSurfer visualization system with specialized managers.
 */

// Export the main visualization class
export { default } from './WaveSurferVisualization';

// Export individual managers for direct access if needed
export { WaveformManager } from './WaveformManager';
export { RegionsMarkersManager } from './RegionsMarkersManager';
export { BeatGridManager } from './BeatGridManager';
export { SpectrogramManager } from './SpectrogramManager';
export { EnvelopeManager } from './EnvelopeManager';
export { RecordingManager } from './RecordingManager';
export { PerformanceManager } from './PerformanceManager';
