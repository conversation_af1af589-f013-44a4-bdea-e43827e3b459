/**
 * RecordingManager
 * 
 * Manages recording functionality for WaveSurfer visualization.
 */

import WaveSurfer from 'wavesurfer.js';
import RecordPlugin from 'wavesurfer.js/dist/plugins/record.esm.js';

export class RecordingManager {
  private recordPlugins: Map<string, RecordPlugin> = new Map();
  private recordingStates: Map<string, any> = new Map();
  private recordingContainers: Map<string, HTMLElement> = new Map();

  constructor() {
    console.log('[RecordingManager] Initialized');
  }

  /**
   * Initialize recorder for a track
   */
  async initializeRecorder(trackId: string, container: HTMLElement, options: any = {}): Promise<void> {
    try {
      console.log(`[RecordingManager] Initializing recorder for track ${trackId}`);

      // Check if recorder already exists
      if (this.recordPlugins.has(trackId)) {
        console.log(`[RecordingManager] Recorder already exists for track ${trackId}, destroying first`);
        await this.destroyRecorder(trackId);
      }

      // Default recording options
      const defaultOptions = {
        scrollingWaveform: true,
        renderRecordedAudio: true,
        audioBitsPerSecond: 128000,
        mimeType: 'audio/webm',
        ...options
      };

      // Create record plugin
      const recordPlugin = RecordPlugin.create(defaultOptions);

      // Store references
      this.recordPlugins.set(trackId, recordPlugin);
      this.recordingContainers.set(trackId, container);
      this.recordingStates.set(trackId, {
        isRecording: false,
        isPaused: false,
        duration: 0,
        format: defaultOptions.mimeType
      });

      // Set up event listeners
      this.setupRecordingEventListeners(trackId, recordPlugin);

      console.log(`[RecordingManager] ✅ Recorder initialized for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error initializing recorder for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Set up recording event listeners
   */
  private setupRecordingEventListeners(trackId: string, recordPlugin: RecordPlugin): void {
    // Recording started
    recordPlugin.on('record-start', () => {
      console.log(`[RecordingManager] Recording started for track ${trackId}`);
      const state = this.recordingStates.get(trackId);
      if (state) {
        state.isRecording = true;
        state.isPaused = false;
      }
    });

    // Recording ended
    recordPlugin.on('record-end', (blob: Blob) => {
      console.log(`[RecordingManager] Recording ended for track ${trackId}, blob size:`, blob.size);
      const state = this.recordingStates.get(trackId);
      if (state) {
        state.isRecording = false;
        state.isPaused = false;
      }
    });

    // Recording paused
    recordPlugin.on('record-pause', () => {
      console.log(`[RecordingManager] Recording paused for track ${trackId}`);
      const state = this.recordingStates.get(trackId);
      if (state) {
        state.isPaused = true;
      }
    });

    // Recording resumed
    recordPlugin.on('record-resume', () => {
      console.log(`[RecordingManager] Recording resumed for track ${trackId}`);
      const state = this.recordingStates.get(trackId);
      if (state) {
        state.isPaused = false;
      }
    });

    // Recording progress
    recordPlugin.on('record-progress', (time: number) => {
      const state = this.recordingStates.get(trackId);
      if (state) {
        state.duration = time;
      }
    });
  }

  /**
   * Start recording
   */
  async startRecording(trackId: string): Promise<void> {
    try {
      console.log(`[RecordingManager] Starting recording for track ${trackId}`);

      const recordPlugin = this.recordPlugins.get(trackId);
      if (!recordPlugin) {
        throw new Error(`No recorder found for track ${trackId}`);
      }

      await recordPlugin.startRecording();

      console.log(`[RecordingManager] ✅ Recording started for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error starting recording for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Stop recording
   */
  async stopRecording(trackId: string): Promise<Blob> {
    try {
      console.log(`[RecordingManager] Stopping recording for track ${trackId}`);

      const recordPlugin = this.recordPlugins.get(trackId);
      if (!recordPlugin) {
        throw new Error(`No recorder found for track ${trackId}`);
      }

      const blob = await recordPlugin.stopRecording();

      console.log(`[RecordingManager] ✅ Recording stopped for track ${trackId}, blob size:`, blob.size);
      return blob;

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error stopping recording for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Pause recording
   */
  async pauseRecording(trackId: string): Promise<void> {
    try {
      console.log(`[RecordingManager] Pausing recording for track ${trackId}`);

      const recordPlugin = this.recordPlugins.get(trackId);
      if (!recordPlugin) {
        throw new Error(`No recorder found for track ${trackId}`);
      }

      await recordPlugin.pauseRecording();

      console.log(`[RecordingManager] ✅ Recording paused for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error pausing recording for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Resume recording
   */
  async resumeRecording(trackId: string): Promise<void> {
    try {
      console.log(`[RecordingManager] Resuming recording for track ${trackId}`);

      const recordPlugin = this.recordPlugins.get(trackId);
      if (!recordPlugin) {
        throw new Error(`No recorder found for track ${trackId}`);
      }

      await recordPlugin.resumeRecording();

      console.log(`[RecordingManager] ✅ Recording resumed for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error resuming recording for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Destroy recorder
   */
  async destroyRecorder(trackId: string): Promise<void> {
    try {
      console.log(`[RecordingManager] Destroying recorder for track ${trackId}`);

      const recordPlugin = this.recordPlugins.get(trackId);
      if (recordPlugin) {
        try {
          // Stop recording if active
          const state = this.recordingStates.get(trackId);
          if (state && state.isRecording) {
            await recordPlugin.stopRecording();
          }

          // Destroy the plugin
          recordPlugin.destroy();
        } catch (error) {
          console.warn(`[RecordingManager] Error destroying record plugin for track ${trackId}:`, error);
        }
      }

      // Clean up references
      this.recordPlugins.delete(trackId);
      this.recordingStates.delete(trackId);
      this.recordingContainers.delete(trackId);

      console.log(`[RecordingManager] ✅ Recorder destroyed for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error destroying recorder for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Get recording state
   */
  getRecordingState(trackId: string): any {
    return this.recordingStates.get(trackId) || {
      isRecording: false,
      isPaused: false,
      duration: 0,
      format: 'audio/webm'
    };
  }

  /**
   * Check if recording is active
   */
  isRecording(trackId: string): boolean {
    const state = this.recordingStates.get(trackId);
    return state ? state.isRecording && !state.isPaused : false;
  }

  /**
   * Check if recording is paused
   */
  isRecordingPaused(trackId: string): boolean {
    const state = this.recordingStates.get(trackId);
    return state ? state.isRecording && state.isPaused : false;
  }

  /**
   * Get recording duration
   */
  getRecordingDuration(trackId: string): number {
    const state = this.recordingStates.get(trackId);
    return state ? state.duration : 0;
  }

  /**
   * Set recording options
   */
  setRecordingOptions(trackId: string, options: any): void {
    try {
      console.log(`[RecordingManager] Setting recording options for track ${trackId}:`, options);

      const state = this.recordingStates.get(trackId);
      if (state) {
        // Update state with new options
        Object.assign(state, options);
      }

      // Note: Changing options during recording may require recreating the recorder
      console.log(`[RecordingManager] ✅ Recording options updated for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error setting recording options for track ${trackId}:`, error);
    }
  }

  /**
   * Get supported recording formats
   */
  getSupportedFormats(): string[] {
    const formats = [
      'audio/webm',
      'audio/mp4',
      'audio/wav',
      'audio/ogg'
    ];

    // Filter to only supported formats
    return formats.filter(format => {
      try {
        return MediaRecorder.isTypeSupported(format);
      } catch {
        return false;
      }
    });
  }

  /**
   * Check microphone access
   */
  async checkMicrophoneAccess(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.warn('[RecordingManager] Microphone access denied or unavailable:', error);
      return false;
    }
  }

  /**
   * Get available audio input devices
   */
  async getAudioInputDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('[RecordingManager] Failed to get audio input devices:', error);
      return [];
    }
  }

  /**
   * Set audio input device
   */
  async setAudioInputDevice(trackId: string, deviceId: string): Promise<void> {
    try {
      console.log(`[RecordingManager] Setting audio input device for track ${trackId}: ${deviceId}`);

      // This would require recreating the recorder with the new device
      // For now, just log the request
      const state = this.recordingStates.get(trackId);
      if (state) {
        state.audioInputDeviceId = deviceId;
      }

      console.log(`[RecordingManager] ✅ Audio input device set for track ${trackId}`);

    } catch (error) {
      console.error(`[RecordingManager] ❌ Error setting audio input device for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Clean up recording for a track
   */
  cleanupRecording(trackId: string): void {
    if (this.recordPlugins.has(trackId)) {
      this.destroyRecorder(trackId);
    }
  }

  /**
   * Get memory stats
   */
  getMemoryStats(): { recordings: number } {
    return { recordings: this.recordPlugins.size };
  }

  /**
   * Dispose all recordings
   */
  async dispose(): Promise<void> {
    // Destroy all recorders
    const destroyPromises = Array.from(this.recordPlugins.keys()).map(trackId => 
      this.destroyRecorder(trackId)
    );

    await Promise.all(destroyPromises);

    // Clear all maps
    this.recordPlugins.clear();
    this.recordingStates.clear();
    this.recordingContainers.clear();

    console.log('[RecordingManager] Disposed all recordings');
  }
}
