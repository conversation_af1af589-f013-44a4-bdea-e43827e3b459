/**
 * RegionsMarkersManager
 * 
 * Manages regions and markers for WaveSurfer visualization.
 */

import WaveSurfer from 'wavesurfer.js';
import { batchDOMOperations } from '../../utils/performance';

export class RegionsMarkersManager {
  private regions: Map<string, Map<string, any>> = new Map();

  constructor() {
    console.log('[RegionsMarkersManager] Initialized');
  }

  /**
   * Set up region click handler
   */
  onRegionClick(trackId: string, regionId: string, callback: () => void, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    // Get the instance
    const wavesurfer = getWaveform(trackId);
    if (!wavesurfer) {
      console.warn(`[RegionsMarkersManager] Cannot set region click handler: no waveform for track ${trackId}`);
      return;
    }

    try {
      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        console.warn(`[RegionsMarkersManager] No regions plugin found for track ${trackId}`);
        return;
      }

      // Find the region
      const region = regionsPlugin.getRegions().find((r: any) => r.id === regionId);
      if (region) {
        region.on('click', callback);
        console.log(`[RegionsMarkersManager] Set click handler for region ${regionId} on track ${trackId}`);
      } else {
        console.warn(`[RegionsMarkersManager] Region ${regionId} not found on track ${trackId}`);
      }
    } catch (error) {
      console.error(`[RegionsMarkersManager] Error setting region click handler for track ${trackId}, region ${regionId}:`, error);
    }
  }

  /**
   * Add a region to a waveform
   */
  addRegion(trackId: string, regionId: string, options: any, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    console.log(`[RegionsMarkersManager] addRegion called:`, {
      trackId,
      regionId,
      options
    });

    // Get the instance
    const wavesurfer = getWaveform(trackId);
    if (!wavesurfer) {
      console.warn(`[RegionsMarkersManager] Cannot add region: no waveform for track ${trackId}`);
      return;
    }

    try {
      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        console.warn(`[RegionsMarkersManager] No regions plugin found for track ${trackId}`);
        return;
      }

      // Check if region already exists
      const existingRegion = regionsPlugin.getRegions().find((r: any) => r.id === regionId);
      if (existingRegion) {
        console.log(`[RegionsMarkersManager] Region ${regionId} already exists for track ${trackId}, updating instead`);
        this.updateRegion(trackId, regionId, options, getWaveform);
        return;
      }

      // Prepare region options
      const regionOptions = {
        id: regionId,
        start: options.start || 0,
        end: options.end || 1,
        color: options.color || 'rgba(0, 123, 255, 0.3)',
        drag: options.drag !== false, // Default to true
        resize: options.resize !== false, // Default to true
        ...options
      };

      console.log(`[RegionsMarkersManager] Creating region with options:`, regionOptions);

      // Add the region
      const region = regionsPlugin.addRegion(regionOptions);

      // Store region reference
      if (!this.regions.has(trackId)) {
        this.regions.set(trackId, new Map());
      }
      this.regions.get(trackId)!.set(regionId, region);

      // Set up event listeners for the region
      if (region) {
        region.on('update-end', () => {
          console.log(`[RegionsMarkersManager] Region ${regionId} updated on track ${trackId}`);
        });

        region.on('click', () => {
          console.log(`[RegionsMarkersManager] Region ${regionId} clicked on track ${trackId}`);
        });

        region.on('enter', () => {
          console.log(`[RegionsMarkersManager] Entered region ${regionId} on track ${trackId}`);
        });

        region.on('leave', () => {
          console.log(`[RegionsMarkersManager] Left region ${regionId} on track ${trackId}`);
        });
      }

      console.log(`[RegionsMarkersManager] ✅ Region ${regionId} added to track ${trackId}`);

    } catch (error) {
      console.error(`[RegionsMarkersManager] ❌ Error adding region ${regionId} to track ${trackId}:`, error);
    }
  }

  /**
   * Remove a region from a waveform
   */
  removeRegion(trackId: string, regionId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[RegionsMarkersManager] Cannot remove region: no waveform for track ${trackId}`);
        return;
      }

      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        console.warn(`[RegionsMarkersManager] No regions plugin found for track ${trackId}`);
        return;
      }

      // Find and remove the region
      const region = regionsPlugin.getRegions().find((r: any) => r.id === regionId);
      if (region) {
        region.remove();
        console.log(`[RegionsMarkersManager] ✅ Region ${regionId} removed from track ${trackId}`);
      } else {
        console.warn(`[RegionsMarkersManager] Region ${regionId} not found on track ${trackId}`);
      }

      // Remove from our tracking
      const trackRegions = this.regions.get(trackId);
      if (trackRegions) {
        trackRegions.delete(regionId);
        if (trackRegions.size === 0) {
          this.regions.delete(trackId);
        }
      }

    } catch (error) {
      console.error(`[RegionsMarkersManager] ❌ Error removing region ${regionId} from track ${trackId}:`, error);
    }
  }

  /**
   * Update a region
   */
  updateRegion(trackId: string, regionId: string, options: any, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    // Get the instance
    const wavesurfer = getWaveform(trackId);
    if (!wavesurfer) {
      console.warn(`[RegionsMarkersManager] Cannot update region: no waveform for track ${trackId}`);
      return;
    }

    try {
      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        console.warn(`[RegionsMarkersManager] No regions plugin found for track ${trackId}`);
        return;
      }

      // Find the region
      const region = regionsPlugin.getRegions().find((r: any) => r.id === regionId);
      if (region) {
        // Update region properties
        if (options.start !== undefined) region.start = options.start;
        if (options.end !== undefined) region.end = options.end;
        if (options.color !== undefined) region.color = options.color;
        if (options.drag !== undefined) region.drag = options.drag;
        if (options.resize !== undefined) region.resize = options.resize;

        // Render the changes
        region.render();

        console.log(`[RegionsMarkersManager] ✅ Region ${regionId} updated on track ${trackId}`);
      } else {
        console.warn(`[RegionsMarkersManager] Region ${regionId} not found on track ${trackId}`);
      }
    } catch (error) {
      console.error(`[RegionsMarkersManager] ❌ Error updating region ${regionId} on track ${trackId}:`, error);
    }
  }

  /**
   * Get a region
   */
  getRegion(trackId: string, regionId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): any {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[RegionsMarkersManager] Cannot get region: no waveform for track ${trackId}`);
        return null;
      }

      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        console.warn(`[RegionsMarkersManager] No regions plugin found for track ${trackId}`);
        return null;
      }

      // Find the region
      const region = regionsPlugin.getRegions().find((r: any) => r.id === regionId);
      return region || null;

    } catch (error) {
      console.error(`[RegionsMarkersManager] Error getting region ${regionId} from track ${trackId}:`, error);
      return null;
    }
  }

  /**
   * Add a marker (special case of region with same start/end)
   */
  addMarker(trackId: string, markerId: string, time: number, options: any = {}, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    // Markers are just regions with the same start and end time
    this.addRegion(trackId, markerId, {
      start: time,
      end: time,
      color: options.color || 'rgba(255, 0, 0, 0.8)',
      drag: options.drag !== false,
      resize: false, // Markers typically don't resize
      ...options
    }, getWaveform);
  }

  /**
   * Remove a marker
   */
  removeMarker(trackId: string, markerId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    this.removeRegion(trackId, markerId, getWaveform);
  }

  /**
   * Update a marker
   */
  updateMarker(trackId: string, markerId: string, time: number, options: any = {}, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    this.updateRegion(trackId, markerId, {
      start: time,
      end: time,
      ...options
    }, getWaveform);
  }

  /**
   * Add region with error handling
   */
  addRegionWithErrorHandling(trackId: string, regionId: string, options: any, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[RegionsMarkersManager] Cannot add region with error handling: no waveform for track ${trackId}`);
        return;
      }

      // Validate options
      if (!options.start && options.start !== 0) {
        console.warn(`[RegionsMarkersManager] Invalid region options: missing start time for region ${regionId} on track ${trackId}`);
        return;
      }

      if (!options.end && options.end !== 0) {
        console.warn(`[RegionsMarkersManager] Invalid region options: missing end time for region ${regionId} on track ${trackId}`);
        return;
      }

      // Ensure start is before end
      if (options.start >= options.end) {
        console.warn(`[RegionsMarkersManager] Invalid region options: start (${options.start}) must be before end (${options.end}) for region ${regionId} on track ${trackId}`);
        return;
      }

      // Get the duration to validate times
      const duration = wavesurfer.getDuration();
      if (duration > 0) {
        if (options.start < 0 || options.start > duration) {
          console.warn(`[RegionsMarkersManager] Invalid start time ${options.start} for region ${regionId} on track ${trackId} (duration: ${duration})`);
          return;
        }

        if (options.end < 0 || options.end > duration) {
          console.warn(`[RegionsMarkersManager] Invalid end time ${options.end} for region ${regionId} on track ${trackId} (duration: ${duration})`);
          return;
        }
      }

      // Add the region
      this.addRegion(trackId, regionId, options, getWaveform);

    } catch (error) {
      console.error(`[RegionsMarkersManager] ❌ Error adding region with error handling for track ${trackId}, region ${regionId}:`, error);
    }
  }

  /**
   * Clear all regions for a track
   */
  clearRegions(trackId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): void {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        console.warn(`[RegionsMarkersManager] Cannot clear regions: no waveform for track ${trackId}`);
        return;
      }

      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        console.warn(`[RegionsMarkersManager] No regions plugin found for track ${trackId}`);
        return;
      }

      // Clear all regions
      regionsPlugin.clearRegions();

      // Clear from our tracking
      this.regions.delete(trackId);

      console.log(`[RegionsMarkersManager] ✅ All regions cleared for track ${trackId}`);

    } catch (error) {
      console.error(`[RegionsMarkersManager] ❌ Error clearing regions for track ${trackId}:`, error);
    }
  }

  /**
   * Get all regions for a track
   */
  getAllRegions(trackId: string, getWaveform: (trackId: string) => WaveSurfer | undefined): any[] {
    try {
      // Get the instance
      const wavesurfer = getWaveform(trackId);
      if (!wavesurfer) {
        return [];
      }

      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin.constructor.name === 'RegionsPlugin');
      if (!regionsPlugin) {
        return [];
      }

      return regionsPlugin.getRegions();

    } catch (error) {
      console.error(`[RegionsMarkersManager] Error getting all regions for track ${trackId}:`, error);
      return [];
    }
  }

  /**
   * Clean up regions for a track
   */
  cleanupRegions(trackId: string): void {
    this.regions.delete(trackId);
  }

  /**
   * Get memory stats
   */
  getMemoryStats(): { regions: number } {
    let totalRegions = 0;
    this.regions.forEach(trackRegions => {
      totalRegions += trackRegions.size;
    });

    return { regions: totalRegions };
  }

  /**
   * Dispose all regions
   */
  dispose(): void {
    this.regions.clear();
    console.log('[RegionsMarkersManager] Disposed all regions');
  }
}
