/**
 * TimelineLoopManager
 * 
 * Manages Ableton-style timeline looping functionality
 * for the timeline coordinator.
 */

export interface TimelineLoopState {
  enabled: boolean;
  start: number;
  end: number;
}

export class TimelineLoopManager {
  // Timeline loop properties (Ableton-style)
  private timelineLoopEnabled: boolean = false;
  private timelineLoopStart: number = 0;
  private timelineLoopEnd: number = 0;

  // External callbacks
  private seekTo?: (time: number) => Promise<void>;

  constructor() {
    // Initialize
  }

  /**
   * Set external callbacks
   */
  setCallbacks(
    seekTo?: (time: number) => Promise<void>
  ): void {
    this.seekTo = seekTo;
  }

  /**
   * Set timeline looping enabled/disabled
   */
  setTimelineLooping(enabled: boolean): void {
    this.timelineLoopEnabled = enabled;
    console.log(`[TimelineLoopManager] Timeline looping ${enabled ? 'enabled' : 'disabled'}`);

    // Dispatch event for UI updates
    window.dispatchEvent(new CustomEvent('timelineLoopStateChanged', {
      detail: this.getTimelineLoopState()
    }));
  }

  /**
   * Set timeline loop region
   */
  setTimelineLoopRegion(start: number, end: number): void {
    this.timelineLoopStart = Math.max(0, start);
    this.timelineLoopEnd = Math.max(this.timelineLoopStart + 0.1, end);

    console.log(`[TimelineLoopManager] Timeline loop region set: ${this.timelineLoopStart.toFixed(2)}s - ${this.timelineLoopEnd.toFixed(2)}s`);

    // Dispatch event for UI updates
    window.dispatchEvent(new CustomEvent('timelineLoopRegionChanged', {
      detail: {
        start: this.timelineLoopStart,
        end: this.timelineLoopEnd
      }
    }));
  }

  /**
   * Get timeline loop state
   */
  getTimelineLoopState(): TimelineLoopState {
    return {
      enabled: this.timelineLoopEnabled,
      start: this.timelineLoopStart,
      end: this.timelineLoopEnd
    };
  }

  /**
   * Check if current time should loop back
   * Returns the loop start time if looping should occur, null otherwise
   */
  checkTimelineLoop(currentTime: number): number | null {
    if (!this.timelineLoopEnabled) return null;

    // Check if we've reached or passed the loop end
    if (currentTime >= this.timelineLoopEnd) {
      console.log(`[TimelineLoopManager] Timeline loop triggered: ${currentTime.toFixed(2)}s >= ${this.timelineLoopEnd.toFixed(2)}s, jumping to ${this.timelineLoopStart.toFixed(2)}s`);
      return this.timelineLoopStart;
    }

    return null;
  }

  /**
   * Toggle timeline looping
   */
  toggleTimelineLooping(): boolean {
    this.setTimelineLooping(!this.timelineLoopEnabled);
    return this.timelineLoopEnabled;
  }

  /**
   * Set loop start time
   */
  setLoopStart(start: number): void {
    this.timelineLoopStart = Math.max(0, start);
    
    // Ensure end is after start
    if (this.timelineLoopEnd <= this.timelineLoopStart) {
      this.timelineLoopEnd = this.timelineLoopStart + 1.0; // Default 1 second loop
    }

    console.log(`[TimelineLoopManager] Loop start set to ${this.timelineLoopStart.toFixed(2)}s`);

    // Dispatch event for UI updates
    window.dispatchEvent(new CustomEvent('timelineLoopRegionChanged', {
      detail: {
        start: this.timelineLoopStart,
        end: this.timelineLoopEnd
      }
    }));
  }

  /**
   * Set loop end time
   */
  setLoopEnd(end: number): void {
    this.timelineLoopEnd = Math.max(this.timelineLoopStart + 0.1, end);

    console.log(`[TimelineLoopManager] Loop end set to ${this.timelineLoopEnd.toFixed(2)}s`);

    // Dispatch event for UI updates
    window.dispatchEvent(new CustomEvent('timelineLoopRegionChanged', {
      detail: {
        start: this.timelineLoopStart,
        end: this.timelineLoopEnd
      }
    }));
  }

  /**
   * Get loop duration
   */
  getLoopDuration(): number {
    return this.timelineLoopEnd - this.timelineLoopStart;
  }

  /**
   * Check if a time is within the loop region
   */
  isTimeInLoop(time: number): boolean {
    return time >= this.timelineLoopStart && time <= this.timelineLoopEnd;
  }

  /**
   * Seek to loop start
   */
  seekToLoopStart(): void {
    if (this.seekTo) {
      this.seekTo(this.timelineLoopStart);
      console.log(`[TimelineLoopManager] Seeking to loop start: ${this.timelineLoopStart.toFixed(2)}s`);
    }
  }

  /**
   * Seek to loop end
   */
  seekToLoopEnd(): void {
    if (this.seekTo) {
      this.seekTo(this.timelineLoopEnd);
      console.log(`[TimelineLoopManager] Seeking to loop end: ${this.timelineLoopEnd.toFixed(2)}s`);
    }
  }

  /**
   * Set loop region from current selection
   */
  setLoopFromSelection(start: number, end: number): void {
    if (start >= end) {
      console.warn('[TimelineLoopManager] Invalid loop selection: start must be before end');
      return;
    }

    this.setTimelineLoopRegion(start, end);
    
    // Auto-enable looping when setting from selection
    if (!this.timelineLoopEnabled) {
      this.setTimelineLooping(true);
    }

    console.log(`[TimelineLoopManager] Loop set from selection: ${start.toFixed(2)}s - ${end.toFixed(2)}s`);
  }

  /**
   * Clear loop region
   */
  clearLoop(): void {
    this.setTimelineLooping(false);
    this.timelineLoopStart = 0;
    this.timelineLoopEnd = 0;

    console.log('[TimelineLoopManager] Loop region cleared');

    // Dispatch event for UI updates
    window.dispatchEvent(new CustomEvent('timelineLoopCleared', {
      detail: {}
    }));
  }

  /**
   * Expand loop region by a factor
   */
  expandLoop(factor: number): void {
    if (factor <= 0) return;

    const center = (this.timelineLoopStart + this.timelineLoopEnd) / 2;
    const halfDuration = (this.timelineLoopEnd - this.timelineLoopStart) / 2;
    const newHalfDuration = halfDuration * factor;

    const newStart = Math.max(0, center - newHalfDuration);
    const newEnd = center + newHalfDuration;

    this.setTimelineLoopRegion(newStart, newEnd);

    console.log(`[TimelineLoopManager] Loop expanded by factor ${factor}: ${newStart.toFixed(2)}s - ${newEnd.toFixed(2)}s`);
  }

  /**
   * Shift loop region by a time offset
   */
  shiftLoop(offset: number): void {
    const newStart = Math.max(0, this.timelineLoopStart + offset);
    const newEnd = this.timelineLoopEnd + offset;

    this.setTimelineLoopRegion(newStart, newEnd);

    console.log(`[TimelineLoopManager] Loop shifted by ${offset.toFixed(2)}s: ${newStart.toFixed(2)}s - ${newEnd.toFixed(2)}s`);
  }

  /**
   * Snap loop boundaries to beat grid
   */
  snapLoopToBeatGrid(beatGrid: any): void {
    if (!beatGrid || !beatGrid.beats || beatGrid.beats.length === 0) {
      console.warn('[TimelineLoopManager] Cannot snap to beat grid: no beat grid available');
      return;
    }

    // Find closest beats to current loop boundaries
    const beats = beatGrid.beats.sort((a: any, b: any) => a.time - b.time);
    
    // Snap start to closest beat
    let closestStartBeat = beats[0];
    let minStartDistance = Math.abs(beats[0].time - this.timelineLoopStart);
    
    for (const beat of beats) {
      const distance = Math.abs(beat.time - this.timelineLoopStart);
      if (distance < minStartDistance) {
        minStartDistance = distance;
        closestStartBeat = beat;
      }
    }

    // Snap end to closest beat (must be after start)
    let closestEndBeat = beats[beats.length - 1];
    let minEndDistance = Math.abs(beats[beats.length - 1].time - this.timelineLoopEnd);
    
    for (const beat of beats) {
      if (beat.time > closestStartBeat.time) {
        const distance = Math.abs(beat.time - this.timelineLoopEnd);
        if (distance < minEndDistance) {
          minEndDistance = distance;
          closestEndBeat = beat;
        }
      }
    }

    this.setTimelineLoopRegion(closestStartBeat.time, closestEndBeat.time);

    console.log(`[TimelineLoopManager] Loop snapped to beat grid: ${closestStartBeat.time.toFixed(2)}s - ${closestEndBeat.time.toFixed(2)}s`);
  }

  /**
   * Get loop info for display
   */
  getLoopInfo(): {
    enabled: boolean;
    start: number;
    end: number;
    duration: number;
    startFormatted: string;
    endFormatted: string;
    durationFormatted: string;
  } {
    const duration = this.getLoopDuration();
    
    return {
      enabled: this.timelineLoopEnabled,
      start: this.timelineLoopStart,
      end: this.timelineLoopEnd,
      duration: duration,
      startFormatted: this.formatTime(this.timelineLoopStart),
      endFormatted: this.formatTime(this.timelineLoopEnd),
      durationFormatted: this.formatTime(duration)
    };
  }

  /**
   * Format time for display
   */
  private formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 100);
    return `${minutes}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
  }
}
