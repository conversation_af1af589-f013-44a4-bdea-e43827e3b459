/**
 * TrackManager
 * 
 * Manages track addition, removal, and track-specific operations
 * for the timeline coordinator.
 */

import { Track } from '@/types/api/tracks';
import { Transition } from '@/types/api/transitions';
import { StoreApi } from 'zustand';
import enhancedToneAudioEngine from '../audio/EnhancedToneAudioEngine';
import waveSurferVisualization from '../WaveSurferVisualization';

export class TrackManager {
  // Tracks and transitions
  private tracks: Track[] = [];
  private transitions: Record<string, Transition> = {};

  // Store reference
  private store: StoreApi<any> | null = null;

  // Track which tracks have already had their deferred elements processed
  private processedDeferredTracks = new Set<string>();

  // Deferred cue points and loops (for when tracks aren't ready yet)
  private deferredCuePoints: Record<string, Array<{cueId: string, time: number, label: string, color: string}>> = {};
  private deferredLoops: Record<string, Array<{loopId: string, start: number, end: number, label: string, color: string}>> = {};

  // External callbacks
  private onTracksChanged?: (tracks: Track[]) => void;
  private onTransitionsChanged?: (transitions: Record<string, Transition>) => void;

  constructor() {
    // Initialize
  }

  /**
   * Set external callbacks
   */
  setCallbacks(
    onTracksChanged?: (tracks: Track[]) => void,
    onTransitionsChanged?: (transitions: Record<string, Transition>) => void
  ): void {
    this.onTracksChanged = onTracksChanged;
    this.onTransitionsChanged = onTransitionsChanged;
  }

  /**
   * Set store reference
   */
  setStore(store: StoreApi<any>): void {
    this.store = store;
  }

  /**
   * Set tracks
   */
  setTracks(tracks: Track[], skipIfEqual: boolean = true): void {
    console.log(`[TrackManager] setTracks called with ${tracks.length} tracks`);

    // Skip if the tracks are the same (to prevent infinite loops)
    if (skipIfEqual && this.tracksAreEqual(this.tracks, tracks)) {
      console.log('[TrackManager] Tracks are equal, skipping update');
      return;
    }

    // Update tracks
    this.setTracksWithoutStoreUpdate(tracks);

    // Update store if available
    if (this.store) {
      this.store.setState({ tracks: [...this.tracks] });
    }

    // Calculate track times
    this.calculateTrackTimes();

    // Process deferred elements for new tracks
    tracks.forEach(track => {
      this.processDeferredElements(track.id.toString());
    });

    // Notify callback
    if (this.onTracksChanged) {
      this.onTracksChanged([...this.tracks]);
    }

    console.log(`[TrackManager] Updated to ${this.tracks.length} tracks`);
  }

  /**
   * Set tracks without updating store
   */
  setTracksWithoutStoreUpdate(tracks: Track[]): void {
    // CRITICAL FIX: Use original references to maintain object identity
    // This prevents unnecessary object recreation that triggers React re-renders
    this.tracks = tracks;
    console.log(`[TrackManager] Set ${tracks.length} tracks without store update`);
  }

  /**
   * Set transitions
   */
  setTransitions(transitions: Record<string, Transition>, skipIfEqual: boolean = true): void {
    // Skip if the transitions are the same (to prevent infinite loops)
    if (skipIfEqual && this.transitionsAreEqual(this.transitions, transitions)) {
      console.log('[TrackManager] Transitions are equal, skipping update');
      return;
    }

    this.transitions = { ...transitions };

    // Update store if available
    if (this.store) {
      this.store.setState({ transitions: { ...this.transitions } });
    }

    // Notify callback
    if (this.onTransitionsChanged) {
      this.onTransitionsChanged({ ...this.transitions });
    }

    console.log(`[TrackManager] Updated transitions: ${Object.keys(this.transitions).length} transitions`);
  }

  /**
   * Calculate track times based on their positions
   */
  private calculateTrackTimes(): void {
    console.log(`[TrackManager] calculateTrackTimes() called with ${this.tracks.length} tracks`);

    this.tracks.forEach((track, index) => {
      // Calculate start time based on track position
      let startTime = 0;

      // If track has a specific startTime, use it
      if (track.startTime !== undefined) {
        startTime = track.startTime;
      } else {
        // Calculate based on previous tracks
        for (let i = 0; i < index; i++) {
          const prevTrack = this.tracks[i];
          const prevDuration = prevTrack.duration || 0;
          const prevStartTime = prevTrack.startTime || 0;
          startTime = Math.max(startTime, prevStartTime + prevDuration);
        }
      }

      // Update track start time
      track.startTime = startTime;

      console.log(`[TrackManager] Track ${track.id} (${track.title}): startTime=${startTime.toFixed(2)}s, duration=${(track.duration || 0).toFixed(2)}s`);
    });
  }

  /**
   * Check if two track arrays are equal
   */
  private tracksAreEqual(a: Track[], b: Track[]): boolean {
    if (a.length !== b.length) {
      return false;
    }

    for (let i = 0; i < a.length; i++) {
      const trackA = a[i];
      const trackB = b[i];

      // Compare essential properties
      if (
        trackA.id !== trackB.id ||
        trackA.title !== trackB.title ||
        trackA.startTime !== trackB.startTime ||
        trackA.duration !== trackB.duration ||
        trackA.bpm !== trackB.bpm
      ) {
        return false;
      }
    }

    return true;
  }

  /**
   * Check if two transition objects are equal
   */
  private transitionsAreEqual(a: Record<string, Transition>, b: Record<string, Transition>): boolean {
    const aKeys = Object.keys(a);
    const bKeys = Object.keys(b);

    if (aKeys.length !== bKeys.length) {
      return false;
    }

    for (const key of aKeys) {
      if (!b[key] || JSON.stringify(a[key]) !== JSON.stringify(b[key])) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get tracks
   */
  getTracks(): Track[] {
    return [...this.tracks];
  }

  /**
   * Get transitions
   */
  getTransitions(): Record<string, Transition> {
    return { ...this.transitions };
  }

  /**
   * Get a specific track by ID
   */
  getTrack(trackId: string): Track | undefined {
    return this.tracks.find(track => track.id.toString() === trackId);
  }

  /**
   * Add a track to the timeline
   */
  async addTrack(track: Track, updateStore: boolean = true, skipBeatmatching: boolean = false): Promise<void> {
    console.log(`[TrackManager] Adding track ${track.id}: ${track.title} (${track.bpm} BPM)`);

    // Add track to timeline
    this.addTrackToTimeline(track, updateStore);

    console.log(`[TrackManager] ✅ Track ${track.id} added successfully`);
  }

  /**
   * Add track to timeline without beatmatching
   */
  private addTrackToTimeline(track: Track, updateStore: boolean): void {
    // Set track order
    (track as any).order = this.tracks.length;

    // Add to tracks array
    this.tracks.push(track);

    // Update store if requested
    if (updateStore && this.store) {
      this.store.setState({ tracks: [...this.tracks] });
    }

    // Calculate track times
    this.calculateTrackTimes();

    // Notify callback
    if (this.onTracksChanged) {
      this.onTracksChanged([...this.tracks]);
    }
  }

  /**
   * Reorder tracks
   */
  reorderTracks(tracks: Track[], updateStore: boolean = true): void {
    // Update the tracks
    this.setTracks(tracks, false);

    // Update store if requested
    if (updateStore && this.store) {
      this.store.setState({ tracks: [...this.tracks] });
    }
  }

  /**
   * Process deferred elements for a track
   */
  private processDeferredElements(trackId: string): void {
    // Prevent processing the same track multiple times
    if (this.processedDeferredTracks.has(trackId)) {
      return;
    }

    // Process deferred cue points
    const deferredCues = this.deferredCuePoints[trackId];
    if (deferredCues && deferredCues.length > 0) {
      console.log(`[TrackManager] Processing ${deferredCues.length} deferred cue points for track ${trackId}`);
      
      deferredCues.forEach(cue => {
        this.addCuePointDirect(trackId, cue.cueId, cue.time, cue.label, cue.color);
      });

      // Clear deferred cue points
      delete this.deferredCuePoints[trackId];
    }

    // Process deferred loops
    const deferredLoopsForTrack = this.deferredLoops[trackId];
    if (deferredLoopsForTrack && deferredLoopsForTrack.length > 0) {
      console.log(`[TrackManager] Processing ${deferredLoopsForTrack.length} deferred loops for track ${trackId}`);
      
      deferredLoopsForTrack.forEach(loop => {
        this.addLoopDirect(trackId, loop.loopId, loop.start, loop.end, loop.label, loop.color);
      });

      // Clear deferred loops
      delete this.deferredLoops[trackId];
    }

    // Mark as processed
    this.processedDeferredTracks.add(trackId);
  }

  /**
   * Load a track with WaveSurfer
   */
  async loadTrack(track: Track, container: HTMLElement): Promise<any> {
    try {
      console.log(`🎵🎵🎵 [TrackManager] LOADING TRACK ${track.id} (${track.title}) WITH SINGLE WAVESURFER + TONE.JS 🎵🎵🎵`);

      // Load track in enhanced audio engine
      const waveSurferInstance = await enhancedToneAudioEngine.loadTrack(track, container);

      console.log(`✅ [TrackManager] Track ${track.id} loaded successfully`);
      return waveSurferInstance;

    } catch (error) {
      console.error(`❌ [TrackManager] Failed to load track ${track.id}:`, error);
      throw error;
    }
  }

  /**
   * Unload a track
   */
  async unloadTrack(trackId: string): Promise<void> {
    console.log(`[TrackManager] Unloading track ${trackId}`);

    // Unload from enhanced audio engine
    await enhancedToneAudioEngine.unloadTrack(trackId);

    // Remove from processed deferred tracks
    this.processedDeferredTracks.delete(trackId);

    console.log(`[TrackManager] Track ${trackId} unloaded`);
  }

  // Direct methods for adding cue points and loops (used by deferred processing)
  private addCuePointDirect(trackId: string, cueId: string, time: number, label: string, color: string): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize cuePoints array if it doesn't exist
    if (!(track as any).cuePoints) {
      (track as any).cuePoints = [];
    }

    // Add the cue point
    (track as any).cuePoints.push({
      id: cueId,
      time: time,
      label: label,
      color: color
    });
  }

  private addLoopDirect(trackId: string, loopId: string, start: number, end: number, label: string, color?: string): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize loops array if it doesn't exist
    if (!(track as any).loops) {
      (track as any).loops = [];
    }

    // Add the loop
    (track as any).loops.push({
      id: loopId,
      start: start,
      end: end,
      label: label,
      color: color || 'rgba(0, 255, 0, 0.3)',
      active: false
    });
  }

  /**
   * Get track info
   */
  getTrackInfo(trackId: string): {startTime: number, endTime: number, duration: number} {
    const track = this.getTrack(trackId);
    if (!track) return { startTime: 0, endTime: 0, duration: 0 };

    const startTime = track.startTime || 0;
    const duration = track.duration || 0;
    const endTime = startTime + duration;

    return { startTime, endTime, duration };
  }

  /**
   * Get track start time
   */
  getTrackStartTime(trackId: string): number {
    const track = this.getTrack(trackId);
    return track?.startTime || 0;
  }

  /**
   * Get track end time
   */
  getTrackEndTime(trackId: string): number {
    const track = this.getTrack(trackId);
    if (!track) return 0;

    const startTime = track.startTime || 0;
    const duration = track.duration || 0;
    return startTime + duration;
  }
}
