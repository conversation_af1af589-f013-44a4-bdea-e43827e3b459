/**
 * Timeline Coordinator Package
 * 
 * Modular timeline coordination system with specialized managers.
 */

// Export the main coordinator
export { default } from './TimelineCoordinatorEnhanced';

// Export individual managers for direct access if needed
export { TimelineStateManager } from './TimelineStateManager';
export { TrackManager } from './TrackManager';
export { BeatmatchingManager } from './BeatmatchingManager';
export { BeatAlignmentManager } from './BeatAlignmentManager';
export { SegmentsCuePointsManager } from './SegmentsCuePointsManager';
export { TimelineLoopManager } from './TimelineLoopManager';
export { RecordingManager } from './RecordingManager';

// Export types
export type { TimelineState } from './TimelineStateManager';
export type { Segment, CuePoint, Loop } from './SegmentsCuePointsManager';
export type { TimelineLoopState } from './TimelineLoopManager';
export type { RecordingState } from './RecordingManager';
