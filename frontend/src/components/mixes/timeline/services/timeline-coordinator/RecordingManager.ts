/**
 * RecordingManager
 * 
 * Manages recording-related functionality for the timeline coordinator.
 * Delegates to WaveSurferVisualization for actual recording operations.
 */

import waveSurferVisualization from '../WaveSurferVisualization';

export interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  format: string;
}

export class RecordingManager {
  // External callbacks
  private addTrack?: (track: any) => Promise<void>;

  constructor() {
    // Initialize
  }

  /**
   * Set external callbacks
   */
  setCallbacks(
    addTrack?: (track: any) => Promise<void>
  ): void {
    this.addTrack = addTrack;
  }

  /**
   * Initialize recorder
   */
  async initializeRecorder(container: HTMLElement, options: any = {}): Promise<void> {
    try {
      await waveSurferVisualization.initializeRecorder(container, options);
      console.log('[RecordingManager] Recorder initialized successfully');
    } catch (error) {
      console.error('[RecordingManager] Failed to initialize recorder:', error);
      throw error;
    }
  }

  /**
   * Start recording
   */
  async startRecording(): Promise<void> {
    try {
      await waveSurferVisualization.startRecording();
      console.log('[RecordingManager] Recording started');

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordingStateChanged', {
        detail: { isRecording: true, isPaused: false }
      }));
    } catch (error) {
      console.error('[RecordingManager] Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * Stop recording
   */
  async stopRecording(): Promise<Blob> {
    try {
      const blob = await waveSurferVisualization.stopRecording();
      console.log('[RecordingManager] Recording stopped, blob size:', blob.size);

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordingStateChanged', {
        detail: { isRecording: false, isPaused: false }
      }));

      // Dispatch event with recording result
      window.dispatchEvent(new CustomEvent('recordingCompleted', {
        detail: { blob, size: blob.size, type: blob.type }
      }));

      return blob;
    } catch (error) {
      console.error('[RecordingManager] Failed to stop recording:', error);
      throw error;
    }
  }

  /**
   * Pause recording
   */
  async pauseRecording(): Promise<void> {
    try {
      await waveSurferVisualization.pauseRecording();
      console.log('[RecordingManager] Recording paused');

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordingStateChanged', {
        detail: { isRecording: true, isPaused: true }
      }));
    } catch (error) {
      console.error('[RecordingManager] Failed to pause recording:', error);
      throw error;
    }
  }

  /**
   * Resume recording
   */
  async resumeRecording(): Promise<void> {
    try {
      await waveSurferVisualization.resumeRecording();
      console.log('[RecordingManager] Recording resumed');

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordingStateChanged', {
        detail: { isRecording: true, isPaused: false }
      }));
    } catch (error) {
      console.error('[RecordingManager] Failed to resume recording:', error);
      throw error;
    }
  }

  /**
   * Destroy recorder
   */
  async destroyRecorder(): Promise<void> {
    try {
      await waveSurferVisualization.destroyRecorder();
      console.log('[RecordingManager] Recorder destroyed');

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordingStateChanged', {
        detail: { isRecording: false, isPaused: false }
      }));
    } catch (error) {
      console.error('[RecordingManager] Failed to destroy recorder:', error);
      throw error;
    }
  }

  /**
   * Get recording state
   */
  getRecordingState(): RecordingState {
    try {
      const state = waveSurferVisualization.getRecordingState();
      return {
        isRecording: state?.isRecording || false,
        isPaused: state?.isPaused || false,
        duration: state?.duration || 0,
        format: state?.format || 'audio/wav'
      };
    } catch (error) {
      console.error('[RecordingManager] Failed to get recording state:', error);
      return {
        isRecording: false,
        isPaused: false,
        duration: 0,
        format: 'audio/wav'
      };
    }
  }

  /**
   * Check if recording is active
   */
  isRecording(): boolean {
    const state = this.getRecordingState();
    return state.isRecording && !state.isPaused;
  }

  /**
   * Check if recording is paused
   */
  isRecordingPaused(): boolean {
    const state = this.getRecordingState();
    return state.isRecording && state.isPaused;
  }

  /**
   * Get recording duration
   */
  getRecordingDuration(): number {
    const state = this.getRecordingState();
    return state.duration;
  }

  /**
   * Add recorded track to timeline
   */
  async addRecordedTrack(blob: Blob, name: string): Promise<void> {
    try {
      if (!this.addTrack) {
        console.warn('[RecordingManager] Cannot add recorded track: addTrack callback not set');
        return;
      }

      // Create a URL for the blob
      const audioUrl = URL.createObjectURL(blob);

      // Create a track object from the recording
      const recordedTrack = {
        id: `recorded_${Date.now()}`,
        title: name || `Recording ${new Date().toLocaleTimeString()}`,
        artist: 'Recorded',
        duration: 0, // Will be determined when loaded
        file_path: audioUrl,
        bpm: 120, // Default BPM, can be analyzed later
        key: '1A', // Default key
        energy: 5, // Default energy
        startTime: 0,
        isRecorded: true,
        recordingBlob: blob
      };

      // Add the track
      await this.addTrack(recordedTrack);

      console.log(`[RecordingManager] Added recorded track: ${recordedTrack.title}`);

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordedTrackAdded', {
        detail: { track: recordedTrack, blob }
      }));

    } catch (error) {
      console.error('[RecordingManager] Failed to add recorded track:', error);
      throw error;
    }
  }

  /**
   * Save recording as file
   */
  saveRecording(blob: Blob, filename?: string): void {
    try {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `recording_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log(`[RecordingManager] Recording saved as: ${a.download}`);

      // Dispatch event for UI updates
      window.dispatchEvent(new CustomEvent('recordingSaved', {
        detail: { filename: a.download, size: blob.size }
      }));

    } catch (error) {
      console.error('[RecordingManager] Failed to save recording:', error);
      throw error;
    }
  }

  /**
   * Get supported recording formats
   */
  getSupportedFormats(): string[] {
    // Common audio formats supported by MediaRecorder
    const formats = [
      'audio/wav',
      'audio/webm',
      'audio/mp4',
      'audio/ogg'
    ];

    // Filter to only supported formats
    return formats.filter(format => {
      try {
        return MediaRecorder.isTypeSupported(format);
      } catch {
        return false;
      }
    });
  }

  /**
   * Set recording format
   */
  setRecordingFormat(format: string): boolean {
    try {
      if (!MediaRecorder.isTypeSupported(format)) {
        console.warn(`[RecordingManager] Format ${format} not supported`);
        return false;
      }

      // This would need to be implemented in waveSurferVisualization
      // For now, just log the request
      console.log(`[RecordingManager] Recording format set to: ${format}`);
      return true;

    } catch (error) {
      console.error('[RecordingManager] Failed to set recording format:', error);
      return false;
    }
  }

  /**
   * Get recording quality options
   */
  getQualityOptions(): Array<{label: string, value: number}> {
    return [
      { label: 'Low (64 kbps)', value: 64000 },
      { label: 'Medium (128 kbps)', value: 128000 },
      { label: 'High (192 kbps)', value: 192000 },
      { label: 'Very High (320 kbps)', value: 320000 }
    ];
  }

  /**
   * Set recording quality
   */
  setRecordingQuality(bitrate: number): void {
    // This would need to be implemented in waveSurferVisualization
    // For now, just log the request
    console.log(`[RecordingManager] Recording quality set to: ${bitrate} bps`);
  }

  /**
   * Check if microphone access is available
   */
  async checkMicrophoneAccess(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.warn('[RecordingManager] Microphone access denied or unavailable:', error);
      return false;
    }
  }

  /**
   * Get available audio input devices
   */
  async getAudioInputDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('[RecordingManager] Failed to get audio input devices:', error);
      return [];
    }
  }
}
