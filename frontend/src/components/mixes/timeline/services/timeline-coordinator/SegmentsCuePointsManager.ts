/**
 * SegmentsCuePointsManager
 * 
 * Manages segment management, cue points, and loop functionality
 * for the timeline coordinator.
 */

import { Track } from '@/types/api/tracks';
import enhancedToneAudioEngine from '../audio/EnhancedToneAudioEngine';

export interface Segment {
  id: string;
  start: number;
  end: number;
  label: string;
  color?: string;
}

export interface CuePoint {
  id: string;
  time: number;
  label: string;
  color: string;
}

export interface Loop {
  id: string;
  start: number;
  end: number;
  label: string;
  color: string;
  active: boolean;
}

export class SegmentsCuePointsManager {
  // External callbacks
  private getTrack?: (trackId: string) => Track | undefined;
  private seekTo?: (time: number) => Promise<void>;

  // Deferred cue points and loops (for when tracks aren't ready yet)
  private deferredCuePoints: Record<string, Array<{cueId: string, time: number, label: string, color: string}>> = {};
  private deferredLoops: Record<string, Array<{loopId: string, start: number, end: number, label: string, color: string}>> = {};

  constructor() {
    // Initialize
  }

  /**
   * Set external callbacks
   */
  setCallbacks(
    getTrack?: (trackId: string) => Track | undefined,
    seekTo?: (time: number) => Promise<void>
  ): void {
    this.getTrack = getTrack;
    this.seekTo = seekTo;
  }

  // ==================== SEGMENTS ====================

  /**
   * Add a segment to a track
   */
  addSegment(trackId: string, segment: any): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize segments array if it doesn't exist
    if (!(track as any).segments) {
      (track as any).segments = [];
    }

    // Generate ID if not provided
    if (!segment.id) {
      segment.id = `segment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Add the segment
    (track as any).segments.push({
      id: segment.id,
      start: segment.start,
      end: segment.end,
      label: segment.label || `Segment ${(track as any).segments.length + 1}`,
      color: segment.color || 'rgba(0, 123, 255, 0.3)'
    });

    console.log(`[SegmentsCuePointsManager] Added segment ${segment.id} to track ${trackId}: ${segment.start.toFixed(2)}s - ${segment.end.toFixed(2)}s`);
  }

  /**
   * Get segments for a track
   */
  getSegments(trackId: string): Segment[] {
    if (!this.getTrack) return [];

    const track = this.getTrack(trackId);
    return (track as any)?.segments || [];
  }

  /**
   * Remove a segment from a track
   */
  removeSegment(trackId: string, segmentId: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).segments) return;

    const segmentIndex = (track as any).segments.findIndex((s: any) => s.id === segmentId);
    if (segmentIndex >= 0) {
      (track as any).segments.splice(segmentIndex, 1);
      console.log(`[SegmentsCuePointsManager] Removed segment ${segmentId} from track ${trackId}`);
    }
  }

  /**
   * Update a segment
   */
  updateSegment(trackId: string, segmentId: string, start: number, end: number, label: string, color?: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).segments) return;

    const segment = (track as any).segments.find((s: any) => s.id === segmentId);
    if (segment) {
      segment.start = start;
      segment.end = end;
      segment.label = label;
      if (color) segment.color = color;

      console.log(`[SegmentsCuePointsManager] Updated segment ${segmentId} for track ${trackId}: ${start.toFixed(2)}s - ${end.toFixed(2)}s`);
    }
  }

  /**
   * Clear all segments for a track
   */
  clearAllSegments(trackId: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track) return;

    (track as any).segments = [];
    console.log(`[SegmentsCuePointsManager] Cleared all segments for track ${trackId}`);
  }

  /**
   * Seek to a segment
   */
  seekToSegment(trackId: string, segmentId: string): void {
    if (!this.getTrack || !this.seekTo) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).segments) return;

    const segment = (track as any).segments.find((s: any) => s.id === segmentId);
    if (segment) {
      const globalTime = (track.startTime || 0) + segment.start;
      this.seekTo(globalTime);
      console.log(`[SegmentsCuePointsManager] Seeking to segment ${segmentId} at ${globalTime.toFixed(2)}s`);
    }
  }

  // ==================== CUE POINTS ====================

  /**
   * Add a cue point to a track
   */
  addCuePoint(trackId: string, cueId: string, time: number, label: string, color: string = 'rgba(255, 0, 0, 0.5)'): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track) {
      // Track not ready yet, defer the cue point
      if (!this.deferredCuePoints[trackId]) {
        this.deferredCuePoints[trackId] = [];
      }
      this.deferredCuePoints[trackId].push({ cueId, time, label, color });
      console.log(`[SegmentsCuePointsManager] Deferred cue point ${cueId} for track ${trackId} (track not ready)`);
      return;
    }

    // Initialize cuePoints array if it doesn't exist
    if (!(track as any).cuePoints) {
      (track as any).cuePoints = [];
    }

    // Check if cue point already exists
    const existingCueIndex = (track as any).cuePoints.findIndex((cue: any) => cue.id === cueId);
    if (existingCueIndex >= 0) {
      // Update existing cue point
      (track as any).cuePoints[existingCueIndex] = {
        id: cueId,
        time: time,
        label: label,
        color: color
      };
      console.log(`[SegmentsCuePointsManager] Updated cue point ${cueId} for track ${trackId} at ${time.toFixed(2)}s`);
    } else {
      // Add new cue point
      (track as any).cuePoints.push({
        id: cueId,
        time: time,
        label: label,
        color: color
      });
      console.log(`[SegmentsCuePointsManager] Added cue point ${cueId} to track ${trackId} at ${time.toFixed(2)}s`);
    }

    // Sort cue points by time
    (track as any).cuePoints.sort((a: any, b: any) => a.time - b.time);
  }

  /**
   * Remove a cue point from a track
   */
  removeCuePoint(trackId: string, cueId: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).cuePoints) return;

    const cueIndex = (track as any).cuePoints.findIndex((cue: any) => cue.id === cueId);
    if (cueIndex >= 0) {
      (track as any).cuePoints.splice(cueIndex, 1);
      console.log(`[SegmentsCuePointsManager] Removed cue point ${cueId} from track ${trackId}`);
    }
  }

  // ==================== LOOPS ====================

  /**
   * Add a loop to a track
   */
  addLoop(trackId: string, loopId: string, start: number, end: number, label: string, color?: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track) {
      // Track not ready yet, defer the loop
      if (!this.deferredLoops[trackId]) {
        this.deferredLoops[trackId] = [];
      }
      this.deferredLoops[trackId].push({ loopId, start, end, label, color: color || 'rgba(0, 255, 0, 0.3)' });
      console.log(`[SegmentsCuePointsManager] Deferred loop ${loopId} for track ${trackId} (track not ready)`);
      return;
    }

    // Initialize loops array if it doesn't exist
    if (!(track as any).loops) {
      (track as any).loops = [];
    }

    // Check if loop already exists
    const existingLoopIndex = (track as any).loops.findIndex((loop: any) => loop.id === loopId);
    if (existingLoopIndex >= 0) {
      // Update existing loop
      (track as any).loops[existingLoopIndex] = {
        id: loopId,
        start: start,
        end: end,
        label: label,
        color: color || 'rgba(0, 255, 0, 0.3)',
        active: false
      };
      console.log(`[SegmentsCuePointsManager] Updated loop ${loopId} for track ${trackId}: ${start.toFixed(2)}s - ${end.toFixed(2)}s`);
    } else {
      // Add new loop
      (track as any).loops.push({
        id: loopId,
        start: start,
        end: end,
        label: label,
        color: color || 'rgba(0, 255, 0, 0.3)',
        active: false
      });
      console.log(`[SegmentsCuePointsManager] Added loop ${loopId} to track ${trackId}: ${start.toFixed(2)}s - ${end.toFixed(2)}s`);
    }

    // Sort loops by start time
    (track as any).loops.sort((a: any, b: any) => a.start - b.start);
  }

  /**
   * Remove a loop from a track
   */
  removeLoop(trackId: string, loopId: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).loops) return;

    const loopIndex = (track as any).loops.findIndex((loop: any) => loop.id === loopId);
    if (loopIndex >= 0) {
      // Deactivate loop in audio engine if it was active
      const loop = (track as any).loops[loopIndex];
      if (loop.active) {
        enhancedToneAudioEngine.setTrackLoop(trackId, null);
      }

      (track as any).loops.splice(loopIndex, 1);
      console.log(`[SegmentsCuePointsManager] Removed loop ${loopId} from track ${trackId}`);
    }
  }

  /**
   * Activate a loop
   */
  activateLoop(trackId: string, loopId: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).loops) return;

    const loop = (track as any).loops.find((l: any) => l.id === loopId);
    if (loop) {
      // Deactivate all other loops for this track
      (track as any).loops.forEach((l: any) => {
        l.active = false;
      });

      // Activate this loop
      loop.active = true;

      // Set loop in audio engine
      enhancedToneAudioEngine.setTrackLoop(trackId, {
        start: loop.start,
        end: loop.end
      });

      console.log(`[SegmentsCuePointsManager] Activated loop ${loopId} for track ${trackId}: ${loop.start.toFixed(2)}s - ${loop.end.toFixed(2)}s`);
    }
  }

  /**
   * Deactivate a loop
   */
  deactivateLoop(trackId: string, loopId: string): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).loops) return;

    const loop = (track as any).loops.find((l: any) => l.id === loopId);
    if (loop && loop.active) {
      loop.active = false;

      // Remove loop from audio engine
      enhancedToneAudioEngine.setTrackLoop(trackId, null);

      console.log(`[SegmentsCuePointsManager] Deactivated loop ${loopId} for track ${trackId}`);
    }
  }

  /**
   * Get deferred cue points for a track
   */
  getDeferredCuePoints(trackId: string): Array<{cueId: string, time: number, label: string, color: string}> {
    return this.deferredCuePoints[trackId] || [];
  }

  /**
   * Get deferred loops for a track
   */
  getDeferredLoops(trackId: string): Array<{loopId: string, start: number, end: number, label: string, color: string}> {
    return this.deferredLoops[trackId] || [];
  }

  /**
   * Clear deferred elements for a track
   */
  clearDeferredElements(trackId: string): void {
    delete this.deferredCuePoints[trackId];
    delete this.deferredLoops[trackId];
  }
}
