/**
 * BeatAlignmentManager
 * 
 * Manages beat alignment, beat grid, and snapping functionality
 * for the timeline coordinator.
 */

import { Track } from '@/types/api/tracks';
import { beatAlignmentService, BeatAlignment } from '../../../../../services/beatAlignmentService';
import { beatBoundarySnappingService, TrackPositioningResult } from '../../../../../services/beatBoundarySnappingService';
import { BeatAlignmentUtils } from '../../../../../utils/beatAlignment';
import { getTrackBeatGrid, BeatGrid } from '../../../../../services/api/beatGrid';
import { beatAlignmentOptimizer } from '../../../../../utils/performance/beatAlignmentOptimization';

export class BeatAlignmentManager {
  // Phase 3: Beat alignment properties
  private beatAlignmentEnabled: boolean = true;
  private beatSnapTolerance: number = 0.1; // 100ms snap tolerance
  private preferDownbeats: boolean = true;
  private trackBeatAlignments: Map<string, BeatAlignment> = new Map();
  private trackBeatGrids: Map<string, BeatGrid> = new Map();

  // External callbacks
  private getTrack?: (trackId: string) => Track | undefined;
  private getAllTracks?: () => Track[];
  private getMasterBPM?: () => number | null;

  constructor() {
    // Initialize beat boundary snapping service with default config
    beatBoundarySnappingService.updateConfig({
      snap_tolerance: this.beatSnapTolerance,
      prefer_downbeats: this.preferDownbeats
    });
  }

  /**
   * Set external callbacks
   */
  setCallbacks(
    getTrack?: (trackId: string) => Track | undefined,
    getAllTracks?: () => Track[],
    getMasterBPM?: () => number | null
  ): void {
    this.getTrack = getTrack;
    this.getAllTracks = getAllTracks;
    this.getMasterBPM = getMasterBPM;
  }

  /**
   * Set beat alignment enabled/disabled
   */
  setBeatAlignmentEnabled(enabled: boolean): void {
    this.beatAlignmentEnabled = enabled;
    console.log(`[BeatAlignmentManager] Beat alignment ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Set beat snap tolerance
   */
  setBeatSnapTolerance(tolerance: number): void {
    this.beatSnapTolerance = tolerance;
    beatBoundarySnappingService.updateConfig({ snap_tolerance: tolerance });
    console.log(`[BeatAlignmentManager] Beat snap tolerance set to ${tolerance}s`);
  }

  /**
   * Set prefer downbeats
   */
  setPreferDownbeats(prefer: boolean): void {
    this.preferDownbeats = prefer;
    beatBoundarySnappingService.updateConfig({ prefer_downbeats: prefer });
    console.log(`[BeatAlignmentManager] Prefer downbeats: ${prefer}`);
  }

  /**
   * Apply beat alignment to a track
   */
  async applyBeatAlignmentToTrack(track: Track, masterBPM: number): Promise<void> {
    if (!this.beatAlignmentEnabled) {
      console.log(`[BeatAlignmentManager] Beat alignment disabled, skipping track ${track.id}`);
      return;
    }

    try {
      console.log(`[BeatAlignmentManager] 🎯 Applying beat alignment to track ${track.id} (${track.bpm} BPM) against master ${masterBPM} BPM`);

      // Get or create beat grid for the track
      let beatGrid = this.trackBeatGrids.get(track.id.toString());
      if (!beatGrid) {
        console.log(`[BeatAlignmentManager] Fetching beat grid for track ${track.id}`);
        beatGrid = await getTrackBeatGrid(track.id);
        if (beatGrid) {
          this.trackBeatGrids.set(track.id.toString(), beatGrid);
        }
      }

      if (!beatGrid) {
        console.warn(`[BeatAlignmentManager] No beat grid available for track ${track.id}, skipping beat alignment`);
        return;
      }

      // Calculate beat alignment
      const alignment = await beatAlignmentService.calculateBeatAlignment(
        track,
        masterBPM,
        beatGrid,
        {
          snapTolerance: this.beatSnapTolerance,
          preferDownbeats: this.preferDownbeats,
          optimizeForTransitions: true
        }
      );

      if (alignment) {
        // Store the alignment
        this.trackBeatAlignments.set(track.id.toString(), alignment);

        // Apply the alignment to the track
        this.applyAlignmentToTrack(track, alignment);

        console.log(`[BeatAlignmentManager] ✅ Beat alignment applied to track ${track.id}: offset=${alignment.offset.toFixed(3)}s, confidence=${alignment.confidence.toFixed(2)}`);
      } else {
        console.warn(`[BeatAlignmentManager] Failed to calculate beat alignment for track ${track.id}`);
      }

    } catch (error) {
      console.error(`[BeatAlignmentManager] Error applying beat alignment to track ${track.id}:`, error);
    }
  }

  /**
   * Apply alignment to track
   */
  private applyAlignmentToTrack(track: Track, alignment: BeatAlignment): void {
    // Apply the beat alignment offset to the track's start time
    const originalStartTime = track.startTime || 0;
    const alignedStartTime = originalStartTime + alignment.offset;

    // Update track start time
    track.startTime = alignedStartTime;

    // Store alignment metadata
    (track as any).beatAlignment = alignment;

    console.log(`[BeatAlignmentManager] Applied alignment to track ${track.id}: ${originalStartTime.toFixed(3)}s → ${alignedStartTime.toFixed(3)}s`);
  }

  /**
   * Apply beat alignment to all tracks
   */
  async applyBeatAlignmentToAllTracks(newMasterBPM: number): Promise<void> {
    if (!this.beatAlignmentEnabled) {
      console.log('[BeatAlignmentManager] Beat alignment disabled, skipping alignment update');
      return;
    }

    if (!this.getAllTracks) {
      console.warn('[BeatAlignmentManager] Cannot apply beat alignment: getAllTracks callback not set');
      return;
    }

    const tracks = this.getAllTracks();
    console.log(`[BeatAlignmentManager] 🎯 Applying beat alignment to ${tracks.length} tracks for master BPM ${newMasterBPM}`);

    // Process tracks in parallel for better performance
    const alignmentPromises = tracks.map(track => 
      this.applyBeatAlignmentToTrack(track, newMasterBPM)
    );

    try {
      await Promise.all(alignmentPromises);
      console.log('[BeatAlignmentManager] ✅ Beat alignment applied to all tracks');

      // Optimize performance after batch alignment
      this.optimizeBeatAlignmentPerformance();

    } catch (error) {
      console.error('[BeatAlignmentManager] Error applying beat alignment to tracks:', error);
    }
  }

  /**
   * Get track beat alignment
   */
  getTrackBeatAlignment(trackId: string): BeatAlignment | undefined {
    return this.trackBeatAlignments.get(trackId);
  }

  /**
   * Get track beat grid
   */
  getTrackBeatGrid(trackId: string): BeatGrid | undefined {
    return this.trackBeatGrids.get(trackId);
  }

  /**
   * Snap position to beat
   */
  async snapPositionToBeat(trackId: string, position: number): Promise<{
    snapped_position: number;
    distance_to_beat: number;
    beat_number: number;
    is_downbeat: boolean;
  }> {
    try {
      const beatGrid = this.trackBeatGrids.get(trackId);
      if (!beatGrid) {
        console.warn(`[BeatAlignmentManager] No beat grid for track ${trackId}, returning original position`);
        return {
          snapped_position: position,
          distance_to_beat: 0,
          beat_number: 0,
          is_downbeat: false
        };
      }

      const result = await beatBoundarySnappingService.snapToBeatBoundary(
        trackId,
        position,
        beatGrid
      );

      return result;

    } catch (error) {
      console.error(`[BeatAlignmentManager] Error snapping position for track ${trackId}:`, error);
      return {
        snapped_position: position,
        distance_to_beat: 0,
        beat_number: 0,
        is_downbeat: false
      };
    }
  }

  /**
   * Set track beat grid visibility
   */
  setTrackBeatGridVisible(trackId: string, enabled: boolean): void {
    // This will be handled by the BeatGridRegions component
    // We just store the preference and let the component react to it
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (track) {
      (track as any).beatGridVisible = enabled;
      console.log(`[BeatAlignmentManager] Set beat grid visibility for track ${trackId}: ${enabled}`);
    }
  }

  /**
   * Add beat grid marker
   */
  addBeatGridMarker(trackId: string, beat: number, time: number): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize beat grid if it doesn't exist
    if (!(track as any).beatGrid) {
      (track as any).beatGrid = {
        beats: [],
        bpm: track.bpm || 120,
        offset: 0
      };
    }

    // Add or update the beat marker
    const beatGrid = (track as any).beatGrid;
    const existingBeatIndex = beatGrid.beats.findIndex((b: any) => b.beat === beat);

    if (existingBeatIndex >= 0) {
      // Update existing beat
      beatGrid.beats[existingBeatIndex].time = time;
    } else {
      // Add new beat
      beatGrid.beats.push({ beat, time });
      // Sort beats by beat number
      beatGrid.beats.sort((a: any, b: any) => a.beat - b.beat);
    }

    console.log(`[BeatAlignmentManager] Added beat grid marker for track ${trackId}: beat ${beat} at ${time.toFixed(3)}s`);
  }

  /**
   * Remove beat grid marker
   */
  removeBeatGridMarker(trackId: string, beat: number): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track || !(track as any).beatGrid) return;

    const beatGrid = (track as any).beatGrid;
    const beatIndex = beatGrid.beats.findIndex((b: any) => b.beat === beat);

    if (beatIndex >= 0) {
      beatGrid.beats.splice(beatIndex, 1);
      console.log(`[BeatAlignmentManager] Removed beat grid marker for track ${trackId}: beat ${beat}`);
    }
  }

  /**
   * Set beat grid offset
   */
  setBeatGridOffset(trackId: string, offset: number): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (track) {
      (track as any).beatGridOffset = offset;
      console.log(`[BeatAlignmentManager] Set beat grid offset for track ${trackId}: ${offset.toFixed(3)}s`);
    }
  }

  /**
   * Get beat grid offset
   */
  getBeatGridOffset(trackId: string): number | undefined {
    if (!this.getTrack) return undefined;

    const track = this.getTrack(trackId);
    return (track as any)?.beatGridOffset;
  }

  /**
   * Seek to beat
   */
  seekToBeat(trackId: string, beat: number): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (!track) return;

    const beatGrid = (track as any).beatGrid;
    if (!beatGrid || !beatGrid.beats) return;

    // Find the beat time
    const beatMarker = beatGrid.beats.find((b: any) => b.beat === beat);
    if (beatMarker) {
      const globalTime = (track.startTime || 0) + beatMarker.time;
      
      // Dispatch seek event
      window.dispatchEvent(new CustomEvent('timeline-seek', {
        detail: {
          globalTime,
          trackId,
          preservePlayState: true
        }
      }));

      console.log(`[BeatAlignmentManager] Seeking to beat ${beat} (${beatMarker.time.toFixed(3)}s) for track ${trackId}`);
    }
  }

  /**
   * Get beat alignment performance stats
   */
  getBeatAlignmentPerformanceStats(): {
    cache: any;
    performance: any;
  } {
    return {
      cache: beatAlignmentOptimizer.getCacheStats(),
      performance: beatAlignmentOptimizer.getPerformanceStats()
    };
  }

  /**
   * Optimize beat alignment performance
   */
  optimizeBeatAlignmentPerformance(): void {
    console.log('[BeatAlignmentManager] Optimizing beat alignment performance');

    // Clear old cache entries
    beatAlignmentOptimizer.clearOldCacheEntries();

    // Precompute alignments for common BPM combinations
    if (this.getMasterBPM && this.getAllTracks) {
      const masterBPM = this.getMasterBPM();
      const tracks = this.getAllTracks();

      if (masterBPM && tracks.length > 0) {
        beatAlignmentOptimizer.precomputeAlignments(tracks, masterBPM);
      }
    }

    console.log('[BeatAlignmentManager] Performance optimization complete');
  }
}
