/**
 * BeatmatchingManager
 * 
 * Manages master BPM, beatmatching, and BPM tolerance logic
 * for the timeline coordinator.
 */

import { Track } from '@/types/api/tracks';
import * as Tone from 'tone';
import enhancedToneAudioEngine from '../audio/EnhancedToneAudioEngine';
import { timeStretchingService } from '../../../../../services/timeStretchingService';

export class BeatmatchingManager {
  // Master BPM and beatmatching properties
  private masterBPM: number | null = null;
  private bpmTolerance: number = 6; // ±6 BPM default (as per user requirement)
  private beatmatchingEnabled: boolean = true;

  // External callbacks
  private getTrack?: (trackId: string) => Track | undefined;
  private getAllTracks?: () => Track[];

  constructor() {
    // Initialize
  }

  /**
   * Set external callbacks
   */
  setCallbacks(
    getTrack?: (trackId: string) => Track | undefined,
    getAllTracks?: () => Track[]
  ): void {
    this.getTrack = getTrack;
    this.getAllTracks = getAllTracks;
  }

  /**
   * Set master BPM
   */
  setMasterBPM(bpm: number): void {
    // Validate BPM range (typical DJ range)
    const clampedBPM = Math.max(60, Math.min(200, bpm));
    
    if (clampedBPM !== bpm) {
      console.warn(`[BeatmatchingManager] BPM ${bpm} clamped to ${clampedBPM} (valid range: 60-200)`);
    }

    const previousBPM = this.masterBPM;
    this.masterBPM = clampedBPM;

    // Update Tone.js transport BPM
    Tone.getTransport().bpm.value = clampedBPM;

    console.log(`[BeatmatchingManager] Master BPM set to ${clampedBPM} BPM`);

    // If beatmatching is enabled and we have tracks, update their playback rates
    if (this.beatmatchingEnabled && previousBPM !== clampedBPM) {
      this.updateAllTrackPlaybackRates();
    }

    // Dispatch custom event for UI updates
    window.dispatchEvent(new CustomEvent('masterBPMChanged', {
      detail: { bpm: clampedBPM, previousBPM }
    }));
  }

  /**
   * Get master BPM
   */
  getMasterBPM(): number | null {
    return this.masterBPM;
  }

  /**
   * Set BPM directly in Tone.js transport
   */
  setBpm(bpm: number): void {
    Tone.getTransport().bpm.value = bpm;
    console.log(`[BeatmatchingManager] Tone.js transport BPM set to ${bpm}`);
  }

  /**
   * Ramp BPM in Tone.js transport
   */
  rampBpm(targetBpm: number, duration: number): void {
    Tone.getTransport().bpm.rampTo(targetBpm, duration);
    console.log(`[BeatmatchingManager] Ramping Tone.js transport BPM to ${targetBpm} over ${duration}s`);
  }

  /**
   * Calculate stretch ratio for a track BPM
   */
  calculateStretchRatio(trackBPM: number): number {
    if (!this.masterBPM) {
      console.warn('[BeatmatchingManager] Cannot calculate stretch ratio: master BPM not set');
      return 1.0;
    }

    const ratio = this.masterBPM / trackBPM;
    console.log(`[BeatmatchingManager] Stretch ratio for ${trackBPM} BPM track: ${ratio.toFixed(3)}`);
    return ratio;
  }

  /**
   * Check if track BPM is within acceptable range
   */
  isWithinAcceptableRange(trackBPM: number): boolean {
    if (!this.masterBPM) {
      return true; // If no master BPM set, all tracks are acceptable
    }

    const difference = Math.abs(trackBPM - this.masterBPM);
    const isAcceptable = difference <= this.bpmTolerance;

    console.log(`[BeatmatchingManager] Track BPM ${trackBPM} vs Master ${this.masterBPM}: difference=${difference.toFixed(1)}, tolerance=±${this.bpmTolerance}, acceptable=${isAcceptable}`);
    
    return isAcceptable;
  }

  /**
   * Set BPM tolerance
   */
  setBPMTolerance(tolerance: number): void {
    this.bpmTolerance = Math.max(1, Math.min(20, tolerance)); // Clamp between 1-20 BPM
    console.log(`[BeatmatchingManager] Set BPM tolerance to ±${this.bpmTolerance} BPM`);
  }

  /**
   * Get BPM tolerance
   */
  getBPMTolerance(): number {
    return this.bpmTolerance;
  }

  /**
   * Set beatmatching enabled/disabled
   */
  setBeatmatchingEnabled(enabled: boolean): void {
    this.beatmatchingEnabled = enabled;
    console.log(`[BeatmatchingManager] Beatmatching ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Check if beatmatching is enabled
   */
  isBeatmatchingEnabled(): boolean {
    return this.beatmatchingEnabled;
  }

  /**
   * Update all track playback rates based on master BPM
   */
  updateAllTrackPlaybackRates(): void {
    if (!this.masterBPM) {
      console.warn('[BeatmatchingManager] Cannot update track playback rates: no master BPM set');
      return;
    }

    if (!this.getAllTracks) {
      console.warn('[BeatmatchingManager] Cannot update track playback rates: getAllTracks callback not set');
      return;
    }

    const tracks = this.getAllTracks();
    console.log(`[BeatmatchingManager] Updating playback rates for ${tracks.length} tracks to match master BPM ${this.masterBPM}`);

    tracks.forEach(track => {
      if (track.bpm) {
        const stretchRatio = this.calculateStretchRatio(track.bpm);
        
        // Update the track's playback rate
        enhancedToneAudioEngine.setTrackPlaybackRate(track.id.toString(), stretchRatio);
        
        console.log(`[BeatmatchingManager] Updated track ${track.id} (${track.bpm} BPM) playback rate to ${stretchRatio.toFixed(3)}`);
      }
    });
  }

  /**
   * Apply beatmatching to a specific track
   */
  async applyBeatmatching(track: Track): Promise<void> {
    if (!this.masterBPM || !track.bpm) return;

    const bpmDifference = Math.abs(track.bpm - this.masterBPM);
    
    console.log(`[BeatmatchingManager] 🎵 Applying beatmatching to track ${track.id} (${track.bpm} BPM) against master ${this.masterBPM} BPM`);

    // Check if track is within acceptable BPM range
    if (!this.isWithinAcceptableRange(track.bpm)) {
      this.showIncompatibleTrackWarning(track, bpmDifference);
      // Still apply beatmatching but with a warning
    }

    try {
      // Use time stretching service for advanced beatmatching
      const result = await timeStretchingService.stretchTrackToMatchBPM(
        track,
        this.masterBPM,
        {
          preservePitch: true,
          algorithm: 'phase_vocoder',
          quality: 'high'
        }
      );

      // Show feedback about the beatmatching result
      this.showBeatmatchingFeedback(track, result);

      console.log(`[BeatmatchingManager] ✅ Beatmatching applied to track ${track.id}`);

    } catch (error) {
      console.warn(`[BeatmatchingManager] Advanced beatmatching failed for track ${track.id}, falling back to basic:`, error);
      
      // Fallback to basic beatmatching
      this.applyBasicBeatmatching(track);
    }
  }

  /**
   * Apply basic beatmatching (playback rate adjustment)
   */
  private applyBasicBeatmatching(track: Track): void {
    if (!this.masterBPM || !track.bpm) return;

    const stretchRatio = this.calculateStretchRatio(track.bpm);
    
    // Set the playback rate
    enhancedToneAudioEngine.setTrackPlaybackRate(track.id.toString(), stretchRatio);
    
    console.log(`[BeatmatchingManager] Applied basic beatmatching to track ${track.id}: playback rate ${stretchRatio.toFixed(3)}`);
  }

  /**
   * Show beatmatching feedback
   */
  private showBeatmatchingFeedback(track: Track, result: any): void {
    const mode = result.mode;
    const originalBpm = track.originalBPM || track.bpm;
    
    let message = '';
    let type = 'info';

    switch (mode) {
      case 'no_change':
        message = `Track "${track.title}" already matches master BPM (${this.masterBPM} BPM)`;
        type = 'success';
        break;
      case 'playback_rate':
        message = `Track "${track.title}" beatmatched using playback rate adjustment (${originalBpm} → ${this.masterBPM} BPM)`;
        type = 'info';
        break;
      case 'time_stretch':
        message = `Track "${track.title}" beatmatched using time stretching (${originalBpm} → ${this.masterBPM} BPM)`;
        type = 'success';
        break;
      case 'incompatible':
        message = `Track "${track.title}" BPM too different for clean beatmatching (${originalBpm} vs ${this.masterBPM} BPM)`;
        type = 'warning';
        break;
      default:
        message = `Track "${track.title}" beatmatched (${originalBpm} → ${this.masterBPM} BPM)`;
        type = 'info';
    }

    // Dispatch custom event for UI feedback
    window.dispatchEvent(new CustomEvent('beatmatchingFeedback', {
      detail: { message, type, trackId: track.id }
    }));
  }

  /**
   * Show warning for incompatible track BPM
   */
  private showIncompatibleTrackWarning(track: Track, bpmDifference: number): void {
    // Create a custom event to show toast notification
    const warningEvent = new CustomEvent('beatmatchingWarning', {
      detail: {
        trackId: track.id,
        trackTitle: track.title,
        trackBPM: track.bpm,
        masterBPM: this.masterBPM,
        bpmDifference: bpmDifference,
        tolerance: this.bpmTolerance,
        message: `Track "${track.title}" (${track.bpm} BPM) differs significantly from master BPM (${this.masterBPM} BPM). Difference: ${bpmDifference.toFixed(1)} BPM (tolerance: ±${this.bpmTolerance} BPM)`
      }
    });

    window.dispatchEvent(warningEvent);
  }

  /**
   * Apply beatmatching to all existing tracks
   */
  applyBeatmatchingToAllTracks(): void {
    console.log('[BeatmatchingManager] 🎵 MANUAL: Applying beatmatching to all existing tracks');

    if (!this.getAllTracks) {
      console.warn('[BeatmatchingManager] Cannot apply beatmatching: getAllTracks callback not set');
      return;
    }

    const tracks = this.getAllTracks();
    
    if (!this.masterBPM) {
      console.warn('[BeatmatchingManager] Cannot apply beatmatching: no master BPM set');
      return;
    }

    if (!this.beatmatchingEnabled) {
      console.log('[BeatmatchingManager] Beatmatching is disabled, skipping');
      return;
    }

    console.log(`[BeatmatchingManager] Applying beatmatching to ${tracks.length} tracks against master BPM ${this.masterBPM}`);

    tracks.forEach(async (track) => {
      if (track.bpm) {
        await this.applyBeatmatching(track);
      } else {
        console.warn(`[BeatmatchingManager] Track ${track.id} has no BPM, skipping beatmatching`);
      }
    });

    console.log('[BeatmatchingManager] ✅ Beatmatching applied to all tracks');
  }

  /**
   * Set track BPM
   */
  setTrackBPM(trackId: string, bpm: number): void {
    if (!this.getTrack) return;

    const track = this.getTrack(trackId);
    if (track) {
      track.bpm = bpm;
      console.log(`[BeatmatchingManager] Set track ${trackId} BPM to ${bpm}`);
      
      // Reapply beatmatching if enabled
      if (this.beatmatchingEnabled && this.masterBPM) {
        this.applyBeatmatching(track);
      }
    }
  }

  /**
   * Get track BPM
   */
  getTrackBPM(trackId: string): number | undefined {
    if (!this.getTrack) return undefined;

    const track = this.getTrack(trackId);
    return track?.bpm;
  }
}
