/**
 * TimelineStateManager
 * 
 * Manages playback state, time management, and animation loop logic
 * for the timeline coordinator.
 */

import * as Tone from 'tone';
import enhancedToneAudioEngine from '../audio/EnhancedToneAudioEngine';
import { Track } from '@/types/api/tracks';
import { Transition } from '@/types/api/transitions';

export interface TimelineState {
  isPlaying: boolean;
  currentTime: number;
  totalDuration: number;
}

export class TimelineStateManager {
  // Playback state
  private isPlaying: boolean = false;
  private currentTime: number = 0;

  // Animation frame for updates - CONSOLIDATED LOOP
  private animationFrame: number | null = null;
  private lastUpdateTime: number = 0;
  private updateInterval: number = 16; // ms between updates (60fps target)

  // Time update callbacks
  private timeUpdateCallbacks: ((time: number) => void)[] = [];

  // Add debouncing for seek events
  private lastSeekEventTime: number = 0;

  // References to external data (injected)
  private tracks: Track[] = [];
  private transitions: Record<string, Transition> = {};
  private activeTransitionsCallback?: (currentTime: number) => void;

  constructor() {
    // Set up event listener for timeline seek events from TrackManager
    window.addEventListener('timeline-seek', this.handleTimelineSeekEvent.bind(this));
  }

  /**
   * Set external data references
   */
  setDataReferences(
    tracks: Track[], 
    transitions: Record<string, Transition>,
    activeTransitionsCallback?: (currentTime: number) => void
  ): void {
    this.tracks = tracks;
    this.transitions = transitions;
    this.activeTransitionsCallback = activeTransitionsCallback;
  }

  /**
   * Handle timeline seek events from TrackManager
   */
  private handleTimelineSeekEvent(event: CustomEvent): void {
    const { globalTime, trackId, clickTime, preservePlayState } = event.detail;

    // Prevent duplicate seeks within 100ms and ignore seeks to same position
    const now = Date.now();
    const timeDiff = Math.abs(globalTime - this.currentTime);

    if ((this.lastSeekEventTime && now - this.lastSeekEventTime < 100) || timeDiff < 0.1) {
      return; // Ignore duplicate or micro-seeks
    }
    this.lastSeekEventTime = now;

    // Store current play state if we need to preserve it
    const wasPlaying = this.isPlaying;

    // Perform the seek
    this.seekTo(globalTime).then(() => {
      // Restore play state if requested
      if (preservePlayState && wasPlaying && !this.isPlaying) {
        this.play();
      }
    });
  }

  /**
   * Start playback
   */
  play(): void {
    if (this.isPlaying) return;

    this.isPlaying = true;
    enhancedToneAudioEngine.play();
    this.startUpdateLoop();

    console.log('[TimelineStateManager] Playback started');
  }

  /**
   * Pause playback
   */
  pause(): void {
    if (!this.isPlaying) return;

    this.isPlaying = false;
    enhancedToneAudioEngine.pause();
    this.stopUpdateLoop();

    console.log('[TimelineStateManager] Playback paused');
  }

  /**
   * Stop playback
   */
  stop(): void {
    // Stop playback
    enhancedToneAudioEngine.stop();
    this.isPlaying = false;
    this.stopUpdateLoop();

    // Reset to beginning
    this.currentTime = 0;
    this.notifyTimeUpdateCallbacks(this.currentTime);

    console.log('[TimelineStateManager] Playback stopped');
  }

  /**
   * Seek to a specific time
   */
  async seekTo(time: number): Promise<void> {
    // Clamp the time to the valid range
    const totalDuration = this.getTotalDuration();
    const clampedTime = Math.max(0, Math.min(time, totalDuration));

    // Update current time
    this.currentTime = clampedTime;

    // Seek the audio engine
    await enhancedToneAudioEngine.seekTo(clampedTime);

    // Notify callbacks
    this.notifyTimeUpdateCallbacks(this.currentTime);

    console.log(`[TimelineStateManager] Seeked to ${clampedTime.toFixed(2)}s`);
  }

  /**
   * Legacy seek method for compatibility
   */
  seek(time: number): void {
    this.seekTo(time);
  }

  /**
   * Get total duration of all tracks
   */
  getTotalDuration(): number {
    if (this.tracks.length === 0) return 0;

    // Find the track that ends latest
    let maxEndTime = 0;
    for (const track of this.tracks) {
      const trackEndTime = (track.startTime || 0) + (track.duration || 0);
      maxEndTime = Math.max(maxEndTime, trackEndTime);
    }

    return maxEndTime;
  }

  /**
   * Start the update loop
   */
  private startUpdateLoop(): void {
    // Stop any existing loop
    this.stopUpdateLoop();

    const updateLoop = (timestamp: number) => {
      if (!this.isPlaying) return;

      // Throttle updates to target FPS
      if (timestamp - this.lastUpdateTime >= this.updateInterval) {
        // Get current time from audio engine
        const audioTime = enhancedToneAudioEngine.getCurrentTime();
        
        // Update our current time
        this.currentTime = audioTime;

        // Check for timeline loop
        const loopTime = this.checkTimelineLoop?.(this.currentTime);
        if (loopTime !== null && loopTime !== undefined) {
          this.seekTo(loopTime);
          return; // Exit early as seekTo will restart the loop
        }

        // Update active transitions
        if (this.activeTransitionsCallback) {
          this.activeTransitionsCallback(this.currentTime);
        }

        // Notify time update callbacks
        this.notifyTimeUpdateCallbacks(this.currentTime);

        this.lastUpdateTime = timestamp;
      }

      // Schedule next frame
      this.animationFrame = requestAnimationFrame(updateLoop);
    };

    // Start the loop
    this.animationFrame = requestAnimationFrame(updateLoop);
  }

  /**
   * Stop the update loop
   */
  private stopUpdateLoop(): void {
    if (this.animationFrame !== null) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  /**
   * Add time update callback
   */
  onTimeUpdate(callback: (time: number) => void): void {
    this.timeUpdateCallbacks.push(callback);
  }

  /**
   * Remove time update callback
   */
  offTimeUpdate(callback: (time: number) => void): void {
    this.timeUpdateCallbacks = this.timeUpdateCallbacks.filter(cb => cb !== callback);
  }

  /**
   * Notify all time update callbacks
   */
  private notifyTimeUpdateCallbacks(time: number): void {
    this.timeUpdateCallbacks.forEach(callback => callback(time));
  }

  /**
   * Get current playback state
   */
  getState(): TimelineState {
    return {
      isPlaying: this.isPlaying,
      currentTime: this.currentTime,
      totalDuration: this.getTotalDuration()
    };
  }

  /**
   * Get current time
   */
  getCurrentTime(): number {
    return this.currentTime;
  }

  /**
   * Get playing state
   */
  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * Set current time (uses seekTo internally)
   */
  setCurrentTime(time: number): void {
    this.seekTo(time);
  }

  // Timeline loop checking (will be injected by TimelineLoopManager)
  private checkTimelineLoop?: (currentTime: number) => number | null;

  /**
   * Set timeline loop checker
   */
  setTimelineLoopChecker(checker: (currentTime: number) => number | null): void {
    this.checkTimelineLoop = checker;
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.stopUpdateLoop();
    this.timeUpdateCallbacks = [];
    window.removeEventListener('timeline-seek', this.handleTimelineSeekEvent.bind(this));
  }
}
