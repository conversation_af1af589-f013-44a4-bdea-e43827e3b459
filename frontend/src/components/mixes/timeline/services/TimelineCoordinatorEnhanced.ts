/**
 * TimelineCoordinatorEnhanced
 * 
 * Main coordinator class that orchestrates all timeline functionality
 * by integrating specialized manager modules.
 */

import { Track } from '@/types/api/tracks';
import { Transition } from '@/types/api/transitions';
import { StoreApi } from 'zustand';
import enhancedToneAudioEngine from './audio/EnhancedToneAudioEngine';

// Import all manager modules
import { TimelineStateManager } from './timeline-coordinator/TimelineStateManager';
import { TrackManager } from './timeline-coordinator/TrackManager';
import { BeatmatchingManager } from './timeline-coordinator/BeatmatchingManager';
import { BeatAlignmentManager } from './timeline-coordinator/BeatAlignmentManager';
import { SegmentsCuePointsManager } from './timeline-coordinator/SegmentsCuePointsManager';
import { TimelineLoopManager } from './timeline-coordinator/TimelineLoopManager';
import { RecordingManager } from './timeline-coordinator/RecordingManager';

class TimelineCoordinatorEnhanced {
  // Manager instances
  private stateManager: TimelineStateManager;
  private trackManager: TrackManager;
  private beatmatchingManager: BeatmatchingManager;
  private beatAlignmentManager: BeatAlignmentManager;
  private segmentsCuePointsManager: SegmentsCuePointsManager;
  private timelineLoopManager: TimelineLoopManager;
  private recordingManager: RecordingManager;

  constructor() {
    // Initialize all managers
    this.stateManager = new TimelineStateManager();
    this.trackManager = new TrackManager();
    this.beatmatchingManager = new BeatmatchingManager();
    this.beatAlignmentManager = new BeatAlignmentManager();
    this.segmentsCuePointsManager = new SegmentsCuePointsManager();
    this.timelineLoopManager = new TimelineLoopManager();
    this.recordingManager = new RecordingManager();

    // Set up cross-manager callbacks
    this.setupManagerCallbacks();
  }

  /**
   * Set up callbacks between managers
   */
  private setupManagerCallbacks(): void {
    // State manager callbacks
    this.stateManager.setDataReferences(
      [],
      {},
      this.updateActiveTransitions.bind(this)
    );
    this.stateManager.setTimelineLoopChecker(
      this.timelineLoopManager.checkTimelineLoop.bind(this.timelineLoopManager)
    );

    // Track manager callbacks
    this.trackManager.setCallbacks(
      this.onTracksChanged.bind(this),
      this.onTransitionsChanged.bind(this)
    );

    // Beatmatching manager callbacks
    this.beatmatchingManager.setCallbacks(
      this.trackManager.getTrack.bind(this.trackManager),
      this.trackManager.getTracks.bind(this.trackManager)
    );

    // Beat alignment manager callbacks
    this.beatAlignmentManager.setCallbacks(
      this.trackManager.getTrack.bind(this.trackManager),
      this.trackManager.getTracks.bind(this.trackManager),
      this.beatmatchingManager.getMasterBPM.bind(this.beatmatchingManager)
    );

    // Segments/cue points manager callbacks
    this.segmentsCuePointsManager.setCallbacks(
      this.trackManager.getTrack.bind(this.trackManager),
      this.stateManager.seekTo.bind(this.stateManager)
    );

    // Timeline loop manager callbacks
    this.timelineLoopManager.setCallbacks(
      this.stateManager.seekTo.bind(this.stateManager)
    );

    // Recording manager callbacks
    this.recordingManager.setCallbacks(
      this.addTrack.bind(this)
    );
  }

  /**
   * Initialize the coordinator
   */
  async initialize(): Promise<void> {
    // Initialize the audio engine
    await enhancedToneAudioEngine.initialize();
    console.log('[TimelineCoordinatorEnhanced] Initialized');
  }

  // ==================== DELEGATION METHODS ====================
  // These methods delegate to the appropriate manager

  // State management
  play(): void { this.stateManager.play(); }
  pause(): void { this.stateManager.pause(); }
  stop(): void { this.stateManager.stop(); }
  async seekTo(time: number): Promise<void> { return this.stateManager.seekTo(time); }
  seek(time: number): void { this.stateManager.seek(time); }
  getCurrentTime(): number { return this.stateManager.getCurrentTime(); }
  getIsPlaying(): boolean { return this.stateManager.getIsPlaying(); }
  setCurrentTime(time: number): void { this.stateManager.setCurrentTime(time); }
  onTimeUpdate(callback: (time: number) => void): void { this.stateManager.onTimeUpdate(callback); }
  offTimeUpdate(callback: (time: number) => void): void { this.stateManager.offTimeUpdate(callback); }
  getTotalDuration(): number { return this.stateManager.getTotalDuration(); }

  // Track management
  setTracks(tracks: Track[], skipIfEqual: boolean = true): void { 
    this.trackManager.setTracks(tracks, skipIfEqual);
    this.updateStateManagerData();
  }
  setTracksWithoutStoreUpdate(tracks: Track[]): void { 
    this.trackManager.setTracksWithoutStoreUpdate(tracks);
    this.updateStateManagerData();
  }
  setTransitions(transitions: Record<string, Transition>, skipIfEqual: boolean = true): void { 
    this.trackManager.setTransitions(transitions, skipIfEqual);
    this.updateStateManagerData();
  }
  getTracks(): Track[] { return this.trackManager.getTracks(); }
  getTransitions(): Record<string, Transition> { return this.trackManager.getTransitions(); }
  getTrack(trackId: string): Track | undefined { return this.trackManager.getTrack(trackId); }
  async addTrack(track: Track, updateStore: boolean = true, skipBeatmatching: boolean = false): Promise<void> {
    await this.trackManager.addTrack(track, updateStore, skipBeatmatching);
    
    // Apply beatmatching if enabled and not skipped
    if (!skipBeatmatching && this.beatmatchingManager.isBeatmatchingEnabled()) {
      await this.beatmatchingManager.applyBeatmatching(track);
    }

    // Apply beat alignment if enabled
    const masterBPM = this.beatmatchingManager.getMasterBPM();
    if (masterBPM && this.beatAlignmentManager) {
      await this.beatAlignmentManager.applyBeatAlignmentToTrack(track, masterBPM);
    }

    this.updateStateManagerData();
  }
  async loadTrack(track: Track, container: HTMLElement): Promise<any> { 
    return this.trackManager.loadTrack(track, container); 
  }
  async unloadTrack(trackId: string): Promise<void> { 
    return this.trackManager.unloadTrack(trackId); 
  }
  reorderTracks(tracks: Track[], updateStore: boolean = true): void { 
    this.trackManager.reorderTracks(tracks, updateStore);
    this.updateStateManagerData();
  }
  getTrackInfo(trackId: string): {startTime: number, endTime: number, duration: number} { 
    return this.trackManager.getTrackInfo(trackId); 
  }
  getTrackStartTime(trackId: string): number { return this.trackManager.getTrackStartTime(trackId); }
  getTrackEndTime(trackId: string): number { return this.trackManager.getTrackEndTime(trackId); }

  // Beatmatching
  setMasterBPM(bpm: number): void { this.beatmatchingManager.setMasterBPM(bpm); }
  getMasterBPM(): number | null { return this.beatmatchingManager.getMasterBPM(); }
  setBpm(bpm: number): void { this.beatmatchingManager.setBpm(bpm); }
  rampBpm(targetBpm: number, duration: number): void { this.beatmatchingManager.rampBpm(targetBpm, duration); }
  calculateStretchRatio(trackBPM: number): number { return this.beatmatchingManager.calculateStretchRatio(trackBPM); }
  isWithinAcceptableRange(trackBPM: number): boolean { return this.beatmatchingManager.isWithinAcceptableRange(trackBPM); }
  setBPMTolerance(tolerance: number): void { this.beatmatchingManager.setBPMTolerance(tolerance); }
  getBPMTolerance(): number { return this.beatmatchingManager.getBPMTolerance(); }
  setBeatmatchingEnabled(enabled: boolean): void { this.beatmatchingManager.setBeatmatchingEnabled(enabled); }
  isBeatmatchingEnabled(): boolean { return this.beatmatchingManager.isBeatmatchingEnabled(); }
  updateAllTrackPlaybackRates(): void { this.beatmatchingManager.updateAllTrackPlaybackRates(); }
  applyBeatmatchingToAllTracks(): void { this.beatmatchingManager.applyBeatmatchingToAllTracks(); }
  setTrackBPM(trackId: string, bpm: number): void { this.beatmatchingManager.setTrackBPM(trackId, bpm); }
  getTrackBPM(trackId: string): number | undefined { return this.beatmatchingManager.getTrackBPM(trackId); }

  // Beat alignment
  setBeatAlignmentEnabled(enabled: boolean): void { this.beatAlignmentManager.setBeatAlignmentEnabled(enabled); }
  setBeatSnapTolerance(tolerance: number): void { this.beatAlignmentManager.setBeatSnapTolerance(tolerance); }
  setPreferDownbeats(prefer: boolean): void { this.beatAlignmentManager.setPreferDownbeats(prefer); }
  async applyBeatAlignmentToAllTracks(newMasterBPM: number): Promise<void> { 
    return this.beatAlignmentManager.applyBeatAlignmentToAllTracks(newMasterBPM); 
  }
  getTrackBeatAlignment(trackId: string): any { return this.beatAlignmentManager.getTrackBeatAlignment(trackId); }
  getTrackBeatGrid(trackId: string): any { return this.beatAlignmentManager.getTrackBeatGrid(trackId); }
  async snapPositionToBeat(trackId: string, position: number): Promise<any> { 
    return this.beatAlignmentManager.snapPositionToBeat(trackId, position); 
  }
  setTrackBeatGridVisible(trackId: string, enabled: boolean): void { 
    this.beatAlignmentManager.setTrackBeatGridVisible(trackId, enabled); 
  }
  getBeatAlignmentPerformanceStats(): any { return this.beatAlignmentManager.getBeatAlignmentPerformanceStats(); }
  optimizeBeatAlignmentPerformance(): void { this.beatAlignmentManager.optimizeBeatAlignmentPerformance(); }
  addBeatGridMarker(trackId: string, beat: number, time: number): void { 
    this.beatAlignmentManager.addBeatGridMarker(trackId, beat, time); 
  }
  removeBeatGridMarker(trackId: string, beat: number): void { 
    this.beatAlignmentManager.removeBeatGridMarker(trackId, beat); 
  }
  setBeatGridOffset(trackId: string, offset: number): void { 
    this.beatAlignmentManager.setBeatGridOffset(trackId, offset); 
  }
  getBeatGridOffset(trackId: string): number | undefined { 
    return this.beatAlignmentManager.getBeatGridOffset(trackId); 
  }
  seekToBeat(trackId: string, beat: number): void { this.beatAlignmentManager.seekToBeat(trackId, beat); }

  // Segments and cue points
  addSegment(trackId: string, segment: any): void { this.segmentsCuePointsManager.addSegment(trackId, segment); }
  getSegments(trackId: string): any[] { return this.segmentsCuePointsManager.getSegments(trackId); }
  removeSegment(trackId: string, segmentId: string): void { 
    this.segmentsCuePointsManager.removeSegment(trackId, segmentId); 
  }
  updateSegment(trackId: string, segmentId: string, start: number, end: number, label: string, color?: string): void { 
    this.segmentsCuePointsManager.updateSegment(trackId, segmentId, start, end, label, color); 
  }
  clearAllSegments(trackId: string): void { this.segmentsCuePointsManager.clearAllSegments(trackId); }
  seekToSegment(trackId: string, segmentId: string): void { 
    this.segmentsCuePointsManager.seekToSegment(trackId, segmentId); 
  }
  addCuePoint(trackId: string, cueId: string, time: number, label: string, color?: string): void { 
    this.segmentsCuePointsManager.addCuePoint(trackId, cueId, time, label, color); 
  }
  removeCuePoint(trackId: string, cueId: string): void { 
    this.segmentsCuePointsManager.removeCuePoint(trackId, cueId); 
  }
  addLoop(trackId: string, loopId: string, start: number, end: number, label: string, color?: string): void { 
    this.segmentsCuePointsManager.addLoop(trackId, loopId, start, end, label, color); 
  }
  removeLoop(trackId: string, loopId: string): void { this.segmentsCuePointsManager.removeLoop(trackId, loopId); }
  activateLoop(trackId: string, loopId: string): void { this.segmentsCuePointsManager.activateLoop(trackId, loopId); }
  deactivateLoop(trackId: string, loopId: string): void { 
    this.segmentsCuePointsManager.deactivateLoop(trackId, loopId); 
  }

  // Timeline looping
  setTimelineLooping(enabled: boolean): void { this.timelineLoopManager.setTimelineLooping(enabled); }
  setTimelineLoopRegion(start: number, end: number): void { 
    this.timelineLoopManager.setTimelineLoopRegion(start, end); 
  }
  getTimelineLoopState(): any { return this.timelineLoopManager.getTimelineLoopState(); }
  checkTimelineLoop(currentTime: number): number | null { 
    return this.timelineLoopManager.checkTimelineLoop(currentTime); 
  }

  // Recording
  async initializeRecorder(container: HTMLElement, options: any = {}): Promise<void> { 
    return this.recordingManager.initializeRecorder(container, options); 
  }
  async startRecording(): Promise<void> { return this.recordingManager.startRecording(); }
  async stopRecording(): Promise<Blob> { return this.recordingManager.stopRecording(); }
  async pauseRecording(): Promise<void> { return this.recordingManager.pauseRecording(); }
  async resumeRecording(): Promise<void> { return this.recordingManager.resumeRecording(); }
  async destroyRecorder(): Promise<void> { return this.recordingManager.destroyRecorder(); }
  getRecordingState(): any { return this.recordingManager.getRecordingState(); }
  async addRecordedTrack(blob: Blob, name: string): Promise<void> { 
    return this.recordingManager.addRecordedTrack(blob, name); 
  }

  // ==================== INTERNAL METHODS ====================

  /**
   * Update state manager with current data
   */
  private updateStateManagerData(): void {
    this.stateManager.setDataReferences(
      this.trackManager.getTracks(),
      this.trackManager.getTransitions(),
      this.updateActiveTransitions.bind(this)
    );
  }

  /**
   * Handle tracks changed
   */
  private onTracksChanged(tracks: Track[]): void {
    this.updateStateManagerData();
  }

  /**
   * Handle transitions changed
   */
  private onTransitionsChanged(transitions: Record<string, Transition>): void {
    this.updateStateManagerData();
  }

  /**
   * Update active transitions
   */
  private updateActiveTransitions(currentTime: number): void {
    // Find active transitions
    const transitions = this.trackManager.getTransitions();
    const activeTransitions = Object.entries(transitions)
      .filter(([_, transition]) => {
  const startTime = transition.startPoint || 0;
        const duration = transition.duration || 0;
        return currentTime >= startTime && currentTime <= startTime + duration;
      });

    // Apply transition effects (this would be implemented based on transition types)
    activeTransitions.forEach(([key, transition]) => {
      // Implementation would depend on transition type
      console.log(`[TimelineCoordinatorEnhanced] Active transition: ${key}`, transition);
    });
  }

  // ==================== LEGACY COMPATIBILITY METHODS ====================
  // These methods maintain compatibility with existing code

  setStore(store: StoreApi<any>): void {
    this.trackManager.setStore(store);
  }

  // Audio engine delegation
  setMasterVolume(volume: number): void {
    enhancedToneAudioEngine.setMasterVolume(volume * 100);
  }

  getMasterVolume(): number {
    return enhancedToneAudioEngine.getMasterVolume();
  }

  setMasterMuted(muted: boolean): void {
    enhancedToneAudioEngine.setMasterVolume(muted ? 0 : 100);
  }

  isMasterMuted(): boolean {
    return enhancedToneAudioEngine.getMasterVolume() === 0;
  }

  setTrackVolume(trackId: string, volume: number): void {
    enhancedToneAudioEngine.setTrackVolume(trackId, volume);
  }

  setTrackPlaybackRate(trackId: string, rate: number): void {
    enhancedToneAudioEngine.setTrackPlaybackRate(trackId, rate);
  }

  setTrackZoom(trackId: string, zoomLevel: number): void {
    enhancedToneAudioEngine.setTrackZoom(trackId, zoomLevel);
  }

  isTrackZooming(trackId: string): boolean {
    return enhancedToneAudioEngine.isTrackZooming(trackId);
  }

  getWaveSurferInstance(trackId: string): any {
    return enhancedToneAudioEngine.getWaveSurferInstance(trackId);
  }

  hasWaveform(trackId: string): boolean {
    return enhancedToneAudioEngine.hasTrack(trackId);
  }

  setTrackMuted(trackId: string, muted: boolean): void {
    enhancedToneAudioEngine.setTrackMuted(trackId, muted);
  }

  // Transition methods
  seekToTransition(transitionKey: string): void {
    const transitions = this.trackManager.getTransitions();
    const transition = transitions[transitionKey];
    if (!transition) return;

  const startTime = transition.startPoint || 0;
    this.seekTo(startTime);
  }

  updateTransition(fromTrackId: string, toTrackId: string, transition: Transition): void {
    const transitionKey = `${fromTrackId}-${toTrackId}`;
    const transitions = { ...this.trackManager.getTransitions() };
    transitions[transitionKey] = transition;
    this.trackManager.setTransitions(transitions);
  }
}

// Create and export singleton instance
const timelineCoordinatorEnhanced = new TimelineCoordinatorEnhanced();
export default timelineCoordinatorEnhanced;
