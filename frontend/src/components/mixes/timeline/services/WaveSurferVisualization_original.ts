import WaveSurfer from 'wavesurfer.js';
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.esm.js';
import HoverPlugin from 'wavesurfer.js/dist/plugins/hover.esm.js';
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline.esm.js';
import EnvelopePlugin from 'wavesurfer.js/dist/plugins/envelope.esm.js';
import SpectrogramPlugin from 'wavesurfer.js/dist/plugins/spectrogram.esm.js';

import RecordPlugin from 'wavesurfer.js/dist/plugins/record.esm.js';
// NOTE: Updated to use EnhancedToneAudioEngine (force rebuild)
import enhancedToneAudioEngine from './audio/EnhancedToneAudioEngine';
import timelineCoordinatorEnhanced from './TimelineCoordinatorEnhanced';
import { ErrorHandler, ErrorType, ErrorSeverity } from '../utils/ErrorHandler';
import { AudioFallback } from '../utils/AudioFallback';
// import errorHandling from './WaveSurferVisualization.errorHandling';
// import optimize from './WaveSurferVisualization.optimize';
import { cleanupWaveSurferInstanceForTrack } from '@/utils/wavesurferCleanup';
import { generateColormap } from '../utils/SpectrogramUtils';
import { batchDOMOperations, throttle } from '../utils/performance';

/**
 * WaveSurferVisualization is a service that manages waveform visualization using WaveSurfer.js
 * It handles waveform rendering, regions, markers, and interaction
 */
class WaveSurferVisualization {
  private instances: Map<string, WaveSurfer> = new Map();
  private regions: Map<string, Map<string, any>> = new Map();
  private containers: Map<string, HTMLElement> = new Map();
  private loadingPromises: Map<string, Promise<void>> = new Map();
  private spectrograms: Map<string, SpectrogramPlugin> = new Map();

  private envelopes: Map<string, EnvelopePlugin> = new Map();
  private recorder: RecordPlugin | null = null;

  constructor() {
    console.log('[WaveSurferVisualization] Initialized');
  }

  /**
   * Create a WaveSurfer instance for a track
   * @param trackId The ID of the track
   * @param container The container element
   * @param options Additional options for WaveSurfer
   */
  async createWaveform(trackId: string, container: HTMLElement, options: any = {}): Promise<WaveSurfer> {
    // First, check if the container already has waveform elements
    // This is a more reliable check than just using the map
    const existingWaveformElements = container.querySelectorAll('wave, canvas, .wavesurfer-wrapper');
    if (existingWaveformElements.length > 0) {
      console.log(`[WaveSurferVisualization] Container already has ${existingWaveformElements.length} waveform elements for track ${trackId}, cleaning up first`);

      // Remove all existing waveform elements from the container
      existingWaveformElements.forEach(element => {
        element.remove();
      });
    }

    // Check if we already have an instance for this track
    if (this.instances.has(trackId)) {
      // Destroy the existing instance
      console.log(`[WaveSurferVisualization] Destroying existing waveform instance for track ${trackId}`);
      await this.destroyWaveform(trackId);
    }

    // Check if we're already loading this track
    if (this.loadingPromises.has(trackId)) {
      console.log(`[WaveSurferVisualization] Waiting for existing loading promise for track ${trackId}`);
      try {
        await this.loadingPromises.get(trackId);
      } catch (error) {
        console.warn(`[WaveSurferVisualization] Previous loading attempt for track ${trackId} failed, will try again`);
      }

      // If we have an instance after waiting, return it
      if (this.instances.has(trackId)) {
        return this.instances.get(trackId)!;
      }
    }

    // If options.url is provided, ensure it's a streaming URL
    if (options.url) {
      try {
        // Import the tracks API dynamically to avoid circular dependencies
        const { getAudioStreamUrl } = await import('@/services/api/tracks');

        // Convert local file path to streaming URL if needed
        // Use trackId directly for more reliable streaming URL generation
        options.url = getAudioStreamUrl(trackId);
        console.log(`[WaveSurferVisualization] Using streaming URL for track ${trackId}: ${options.url}`);
      } catch (error) {
        console.error(`[WaveSurferVisualization] Error creating streaming URL for track ${trackId}:`, error);
      }
    }

    // Create a loading promise
    const loadingPromise = new Promise<void>(async (resolve, reject) => {
      try {
        console.log(`[WaveSurferVisualization] Creating waveform for track ${trackId}`);

        // Store the container
        this.containers.set(trackId, container);

        // Add a data-track-id attribute to the container for easier cleanup
        container.setAttribute('data-track-id', trackId);

        // PROFESSIONAL DAW FIX: Calculate minPxPerSec for proper waveform display
        const containerWidth = container.clientWidth || 800; // Fallback width
        const trackDuration = options.duration || 300; // Fallback duration

        // CRITICAL: Ensure minimum 10 px/s for proper waveform shape, even if track is long
        const calculatedMinPxPerSec = Math.max(10, containerWidth / trackDuration);

        console.log(`[WaveSurferVisualization] PROFESSIONAL WAVEFORM CALCULATION: containerWidth=${containerWidth}px, trackDuration=${trackDuration}s, calculatedMinPxPerSec=${calculatedMinPxPerSec.toFixed(2)} (min 10 px/s for proper shape)`);

        // PROFESSIONAL DAW FIX: Static waveform configuration like Ableton
        const defaultOptions = {
          container,
          waveColor: options.waveColor || '#4a5568', // CRITICAL: Use track color or default gray
          progressColor: 'transparent', // CRITICAL: Make progress completely invisible
          cursorColor: 'transparent',   // CRITICAL: Make cursor completely invisible
          barWidth: 1,  // CRITICAL: Thinner bars for better waveform detail
          barRadius: 0, // CRITICAL: No radius for cleaner waveform appearance
          cursorWidth: 0, // CRITICAL: Set cursor width to 0
          height: 80,
          barGap: 1,    // CRITICAL: Smaller gap for better waveform continuity
          responsive: false, // Keep disabled to prevent layout thrashing
          normalize: true,
          // Use WebAudio backend for better performance
          backend: 'WebAudio',
          // Increase buffer size to reduce clicking
          audioBufferSize: 4096,
          // Set audio context latency hint for better playback
          audioContextLatencyHint: 'playback',
          // CRITICAL: Use calculated minPxPerSec to fit complete track in container
          minPxPerSec: calculatedMinPxPerSec, // Dynamically calculated to show complete track
          fillParent: false, // DISABLED: fillParent causes continuous shadow DOM style thrashing
          scrollParent: false, // Disable scrollParent to prevent overflow issues
          hideScrollbar: true, // Hide WaveSurfer's scrollbar, use container scrolling
          autoCenter: false, // Disable auto-center to allow manual positioning
          // Force width calculation based on container
          width: undefined, // Let WaveSurfer calculate based on container
          renderFunction: undefined,
          dragToSeek: false, // CRITICAL: Completely disable WaveSurfer's internal seeking
          interact: false, // CRITICAL: Disable ALL WaveSurfer interaction to prevent internal movement
          // PROFESSIONAL DAW FIX: Completely disconnect from any audio playback
          media: undefined, // No media element connection
          audioRate: 1, // Fixed playback rate
          autoplay: false, // Never autoplay
          preload: 'none', // Don't preload audio data
        };

        // PROFESSIONAL DAW FIX: NO PLUGINS - completely static waveform like Ableton
        // Plugins cause interaction and internal movement - disabled for professional DAW behavior
        console.log(`[WaveSurferVisualization] PROFESSIONAL DAW MODE: Creating static waveform with NO PLUGINS for track ${trackId}`);

        // PROFESSIONAL DAW FIX: Create completely static WaveSurfer with NO PLUGINS
        const wavesurfer = WaveSurfer.create({
          ...defaultOptions,
          ...options,
          plugins: [], // CRITICAL: NO PLUGINS for completely static behavior
          // Add these options to prevent runtime.lastError messages
          xhr: {
            // Disable cache to prevent issues with Chrome extensions
            cache: 'no-cache',
            // Add a timeout to prevent hanging requests
            timeout: 30000,
            // Use GET method for all requests
            method: 'GET',
            // Add custom request headers
            requestHeaders: [
              {
                key: 'X-Requested-With',
                value: 'XMLHttpRequest'
              },
              {
                key: 'Cache-Control',
                value: 'no-cache, no-store, must-revalidate'
              },
              {
                key: 'Pragma',
                value: 'no-cache'
              }
            ]
          },
          // Prevent message channel errors by using a more robust fetch strategy
          fetchStrategy: 'arraybuffer'
        });

        // Store the envelope plugin
        this.envelopes.set(trackId, envelope);

        // Create a cleanup function to remove event listeners
        const cleanupListeners = () => {
          // Remove all event listeners when done
          // Use unAll() to remove all listeners for an event
          wavesurfer.unAll();
        };

        // PROFESSIONAL DAW FIX: Minimal event listeners - only ready event
        wavesurfer.on('ready', () => {
          console.log(`[WaveSurferVisualization] STATIC WAVEFORM ready for track ${trackId} - NO INTERACTION ENABLED`);

          // PROFESSIONAL DAW FIX: Completely disable all WaveSurfer internal state updates
          const waveformContainer = container.querySelector('div[part="wrapper"]') as HTMLElement;
          if (waveformContainer) {
            waveformContainer.style.pointerEvents = 'none';
            console.log(`[WaveSurferVisualization] DISABLED pointer events on waveform for track ${trackId}`);
          }

          // CRITICAL: Override WaveSurfer's setTime method to prevent any internal updates
          const originalSetTime = wavesurfer.setTime;
          wavesurfer.setTime = function(time: number) {
            console.log(`[WaveSurferVisualization] 🔒 BLOCKED setTime(${time}) call for track ${trackId} - maintaining static display`);
            // Completely ignore all setTime calls
            return;
          };

          // CRITICAL: Override WaveSurfer's seekTo method to prevent any internal updates
          const originalSeekTo = wavesurfer.seekTo;
          wavesurfer.seekTo = function(progress: number) {
            console.log(`[WaveSurferVisualization] 🔒 BLOCKED seekTo(${progress}) call for track ${trackId} - maintaining static display`);
            // Completely ignore all seekTo calls
            return;
          };

          // Dispatch custom event for components that need to know when waveform is ready
          const readyEvent = new CustomEvent('waveform-ready', {
            detail: { trackId }
          });
          window.dispatchEvent(readyEvent);

          resolve();
        });



        wavesurfer.on('error', (error) => {
          console.error(`[WaveSurferVisualization] Error creating waveform for track ${trackId}:`, error);
          cleanupListeners();
          reject(error);
        });

        // Add destroy listener to clean up
        wavesurfer.on('destroy', () => {
          console.log(`[WaveSurferVisualization] Waveform destroyed for track ${trackId}`);
          cleanupListeners();
        });

        // Store the instance
        this.instances.set(trackId, wavesurfer);
        this.regions.set(trackId, new Map());

        // Get the audio buffer from Tone.js
        const buffer = enhancedToneAudioEngine.getAudioBuffer(trackId);

        if (buffer) {
          try {
            // Convert the Tone.js buffer to an AudioBuffer
            const audioContext = new AudioContext();
            const audioBuffer = audioContext.createBuffer(
              buffer.numberOfChannels,
              buffer.length,
              buffer.sampleRate
            );

            // Copy the channel data
            for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
              const channelData = buffer.getChannelData(channel);
              audioBuffer.copyToChannel(channelData, channel);
            }

            // Load the buffer into WaveSurfer using the appropriate method
            // For WaveSurfer v6+, we use loadBlob with a Blob created from the buffer
            const arrayBuffer = audioBufferToWav(audioBuffer);
            const blob = new Blob([arrayBuffer], { type: 'audio/wav' });
            wavesurfer.loadBlob(blob);
          } catch (err) {
            console.error(`[WaveSurferVisualization] Error loading buffer for track ${trackId}:`, err);

            // Try to load from URL directly if buffer conversion fails
            if (options.url) {
              console.log(`[WaveSurferVisualization] Falling back to URL loading for track ${trackId}`);
              wavesurfer.load(options.url);
            } else {
              reject(err);
            }
          }
        } else {
          console.warn(`[WaveSurferVisualization] No audio buffer available for track ${trackId}`);

          // If no buffer is available but we have a URL, try to load from URL directly
          if (options.url) {
            console.log(`[WaveSurferVisualization] Falling back to URL loading for track ${trackId}: ${options.url}`);

            // Add error handling for the load operation
            try {
              console.log(`[WaveSurferVisualization] Loading waveform directly for ${options.url}`);

              // Load the URL directly without pre-checking
              // This avoids the HEAD request that might not be supported by all backends
              wavesurfer.load(options.url);

              // Add error handling for the load operation
              wavesurfer.once('error', (error) => {
                console.error(`[WaveSurferVisualization] Error loading waveform for ${options.url}:`, error);
                createPlaceholderWaveform();
              });
            } catch (error) {
              console.error(`[WaveSurferVisualization] Error checking URL for track ${trackId}:`, error);
              createPlaceholderWaveform();
            }
          } else {
            createPlaceholderWaveform();
          }

          // Function to create a placeholder waveform
          function createPlaceholderWaveform() {
            // Create a placeholder waveform with a simple sine wave
            console.log(`[WaveSurferVisualization] Creating placeholder waveform for track ${trackId}`);

            // Create a simple sine wave buffer
            const audioContext = new AudioContext();
            const sampleRate = audioContext.sampleRate;
            const duration = 60; // 60 seconds placeholder
            const placeholderBuffer = audioContext.createBuffer(2, sampleRate * duration, sampleRate);

            // Fill with a sine wave
            for (let channel = 0; channel < 2; channel++) {
              const channelData = placeholderBuffer.getChannelData(channel);
              for (let i = 0; i < channelData.length; i++) {
                // Simple sine wave at 440Hz
                channelData[i] = Math.sin(i * 2 * Math.PI * 440 / sampleRate) * 0.5;
              }
            }

            // Convert to WAV and load
            const arrayBuffer = audioBufferToWav(placeholderBuffer);
            const blob = new Blob([arrayBuffer], { type: 'audio/wav' });

            try {
              wavesurfer.loadBlob(blob);
              console.log(`[WaveSurferVisualization] Placeholder waveform loaded for track ${trackId}`);

              // Make sure the container has the track ID attribute
              if (container) {
                container.setAttribute('data-track-id', trackId);
              }
            } catch (error) {
              console.error(`[WaveSurferVisualization] Error loading placeholder waveform for track ${trackId}:`, error);
            }
          }
        }

        // Helper function to convert AudioBuffer to WAV format
        function audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
          const numOfChan = buffer.numberOfChannels;
          const length = buffer.length * numOfChan * 2 + 44;
          const result = new ArrayBuffer(length);
          const view = new DataView(result);
          const channels = [];
          let sample = 0;
          let offset = 0;
          let pos = 0;

          // Write WAV header
          setUint32(0x46464952); // "RIFF"
          setUint32(length - 8); // file length - 8
          setUint32(0x45564157); // "WAVE"
          setUint32(0x20746d66); // "fmt " chunk
          setUint32(16); // length = 16
          setUint16(1); // PCM (uncompressed)
          setUint16(numOfChan);
          setUint32(buffer.sampleRate);
          setUint32(buffer.sampleRate * 2 * numOfChan); // avg. bytes/sec
          setUint16(numOfChan * 2); // block-align
          setUint16(16); // 16-bit
          setUint32(0x61746164); // "data" chunk
          setUint32(length - pos - 4); // chunk length

          // Write interleaved data
          for (let i = 0; i < buffer.numberOfChannels; i++) {
            channels.push(buffer.getChannelData(i));
          }

          while (pos < length) {
            for (let i = 0; i < numOfChan; i++) {
              // interleave channels
              sample = Math.max(-1, Math.min(1, channels[i][offset])); // clamp
              sample = (0.5 + sample < 0 ? sample * 32768 : sample * 32767) | 0; // scale to 16-bit signed int
              view.setInt16(pos, sample, true); // write 16-bit sample
              pos += 2;
            }
            offset++; // next source sample

            if (offset >= buffer.length) break;
          }

          return result;

          function setUint16(data: number) {
            view.setUint16(pos, data, true);
            pos += 2;
          }

          function setUint32(data: number) {
            view.setUint32(pos, data, true);
            pos += 4;
          }
        }
      } catch (error) {
        console.error(`[WaveSurferVisualization] Error creating waveform for track ${trackId}:`, error);
        reject(error);
      }
    });

    // Store the loading promise
    this.loadingPromises.set(trackId, loadingPromise);

    try {
      // Wait for the waveform to load
      await loadingPromise;

      // Remove the loading promise
      this.loadingPromises.delete(trackId);

      // Return the instance
      return this.instances.get(trackId)!;
    } catch (error) {
      // Remove the loading promise
      this.loadingPromises.delete(trackId);

      throw error;
    }
  }

  /**
   * Destroy a WaveSurfer instance
   * @param trackId The ID of the track
   */
  async destroyWaveform(trackId: string): Promise<void> {
    try {
      console.log(`[WaveSurferVisualization] Destroying waveform for track ${trackId}`);

      // Get the instance
      const wavesurfer = this.instances.get(trackId);
      if (!wavesurfer) {
        console.log(`[WaveSurferVisualization] No waveform instance found for track ${trackId}`);

        // Clean up any orphaned entries in our maps
        this.regions.delete(trackId);
        this.containers.delete(trackId);
        this.envelopes.delete(trackId);

        // Use the global cleanup utility to ensure all DOM elements are removed
        cleanupWaveSurferInstanceForTrack(trackId);
        return;
      }

      // Get the container
      const container = this.containers.get(trackId);

      // Remove all event listeners first to prevent memory leaks
      wavesurfer.unAll();

      // Cancel any pending operations
      if (this.loadingPromises.has(trackId)) {
        console.log(`[WaveSurferVisualization] Cancelling pending load for track ${trackId}`);
        this.loadingPromises.delete(trackId);
      }

      // Destroy any associated plugins
      await this.destroySpectrogram(trackId);

      // Destroy the instance
      wavesurfer.destroy();

      // Remove from maps
      this.instances.delete(trackId);
      this.regions.delete(trackId);
      this.envelopes.delete(trackId);

      // Clean up the container if it exists
      if (container) {
        // Remove all waveform-related elements from the container
        const waveformElements = container.querySelectorAll('wave, canvas, .wavesurfer-wrapper');
        waveformElements.forEach(element => {
          element.remove();
        });

        // Keep the container reference for potential reuse
        console.log(`[WaveSurferVisualization] Cleaned up ${waveformElements.length} waveform elements from container for track ${trackId}`);
      }

      // Use the global cleanup utility to ensure all DOM elements are removed
      cleanupWaveSurferInstanceForTrack(trackId);

      // Force garbage collection
      setTimeout(() => {
        console.log(`[WaveSurferVisualization] Cleanup complete for track ${trackId}`);
      }, 100);

      console.log(`[WaveSurferVisualization] Destroyed waveform for track ${trackId}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error destroying waveform for track ${trackId}:`, error);

      // Clean up maps even if there was an error
      this.instances.delete(trackId);
      this.regions.delete(trackId);
      this.containers.delete(trackId);
      this.envelopes.delete(trackId);

      // Use the global cleanup utility to ensure all DOM elements are removed
      cleanupWaveSurferInstanceForTrack(trackId);
    }
  }

  /**
   * Get a WaveSurfer instance
   * @param trackId The ID of the track
   */
  getWaveform(trackId: string): WaveSurfer | undefined {
    return this.instances.get(trackId);
  }

  /**
   * Get a region from a waveform
   * @param trackId The ID of the track
   * @param regionId The ID of the region
   * @returns The region, or undefined if not found
   */
  getRegion(trackId: string, regionId: string): any {
    try {
      // Get the instance
      const wavesurfer = this.instances.get(trackId);
      if (!wavesurfer) {
        console.warn(`[WaveSurferVisualization] Cannot get region from track ${trackId}: waveform not created`);
        return undefined;
      }

      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
      if (!regionsPlugin) {
        console.warn(`[WaveSurferVisualization] Cannot get region from track ${trackId}: regions plugin not found`);
        return undefined;
      }

      // Find the region in the WaveSurfer instance
      const region = regionsPlugin.getRegions().find(r => r.id === regionId);

      // Also check our internal map
      if (!region) {
        const trackRegions = this.regions.get(trackId);
        if (trackRegions) {
          return trackRegions.get(regionId);
        }
      }

      return region;
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error getting region ${regionId} from track ${trackId}:`, error);
      return undefined;
    }
  }

  /**
   * Get the wrapper element for a waveform
   * @param trackId The ID of the track
   * @returns The wrapper element or undefined if not found
   */
  getWrapper(trackId: string): HTMLElement | undefined {
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) return undefined;

    // Try to get the wrapper element
    try {
      // For WaveSurfer v6+
      return wavesurfer.getWrapper();
    } catch (error) {
      // Fallback to container
      return this.containers.get(trackId);
    }
  }

  /**
   * Check if a waveform exists for a track
   * @param trackId The ID of the track
   * @returns Whether a waveform exists for the track
   */
  hasWaveform(trackId: string): boolean {
    try {
      // Check if the instance exists
      const hasInstance = this.instances.has(trackId);

      if (hasInstance) {
        // Get the instance to verify it's valid
        const wavesurfer = this.instances.get(trackId);

        // Check if the instance is valid and has been properly initialized
        if (wavesurfer && typeof wavesurfer.getActivePlugins === 'function') {
          return true;
        } else {
          // Instance exists but is invalid, remove it
          console.warn(`[WaveSurferVisualization] Found invalid waveform instance for track ${trackId}, removing it`);
          this.instances.delete(trackId);
          return false;
        }
      }

      return false;
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error checking if waveform exists for track ${trackId}:`, error);
      return false;
    }
  }

  /**
   * Get all waveform IDs
   * @returns Array of all track IDs that have waveforms
   */
  getAllWaveformIds(): string[] {
    return Array.from(this.instances.keys());
  }

  /**
   * Check if a waveform is ready (fully loaded) for a track
   * @param trackId The ID of the track
   * @returns Whether the waveform is ready
   */
  isWaveformReady(trackId: string): boolean {
    try {
      // First check if the waveform exists
      if (!this.hasWaveform(trackId)) {
        return false;
      }

      // Get the instance
      const wavesurfer = this.instances.get(trackId)!;

      // Check if the duration is available (indicates the waveform is ready)
      try {
        const duration = wavesurfer.getDuration();
        return duration !== undefined && !isNaN(duration) && duration > 0;
      } catch (durationError) {
        console.warn(`[WaveSurferVisualization] Error checking duration for track ${trackId}:`, durationError);
        return false;
      }
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error checking if waveform is ready for track ${trackId}:`, error);
      return false;
    }
  }



  /**
   * Set up a click handler for a region
   * @param trackId The ID of the track
   * @param regionId The ID of the region
   * @param callback The callback to execute when the region is clicked
   */
  onRegionClick(trackId: string, regionId: string, callback: () => void): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot set up region click handler for track ${trackId}: waveform not created`);
      return;
    }

    // Get the regions plugin
    const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
    if (!regionsPlugin) {
      console.warn(`[WaveSurferVisualization] Cannot set up region click handler for track ${trackId}: regions plugin not found`);
      return;
    }

    // Get the region
    const region = regionsPlugin.getRegions().find(r => r.id === regionId);
    if (!region) {
      console.warn(`[WaveSurferVisualization] Cannot set up region click handler for track ${trackId}: region ${regionId} not found`);
      return;
    }

    // Add the click handler
    region.on('click', callback);
  }

  /**
   * Add a region to a waveform
   * @param trackId The ID of the track
   * @param regionId The ID of the region
   * @param options Options for the region
   */
  addRegion(trackId: string, regionId: string, options: any): void {
    console.log(`[WaveSurferVisualization] addRegion called:`, {
      trackId,
      regionId,
      options
    });

    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot add region to track ${trackId}: waveform not created`);
      return;
    }

    // Get the regions plugin
    const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
    if (!regionsPlugin) {
      console.warn(`[WaveSurferVisualization] Cannot add region to track ${trackId}: regions plugin not found`);
      return;
    }

    // Create the region with content property for WaveSurfer.js v7
    const region = regionsPlugin.addRegion({
      id: regionId,
      start: options.start,
      end: options.end,
      color: options.color || 'rgba(0, 0, 255, 0.2)',
      drag: options.drag || false,
      resize: options.resize || false,
      content: options.label || '',
      ...options,
    });

    console.log(`[WaveSurferVisualization] Region created:`, {
      trackId,
      regionId,
      regionCreated: !!region,
      regionElement: !!region?.element,
      regionElementVisible: region?.element?.style?.display !== 'none',
      regionElementOpacity: region?.element?.style?.opacity,
      start: options.start,
      end: options.end,
      color: options.color,
      allRegionsCount: regionsPlugin.getRegions().length
    });

    // Additional debugging: check if the region is actually visible in the DOM
    if (region?.element) {
      console.log(`[WaveSurferVisualization] Region element details:`, {
        trackId,
        regionId,
        elementTagName: region.element.tagName,
        elementClasses: region.element.className,
        elementStyle: region.element.style.cssText,
        elementBounds: region.element.getBoundingClientRect(),
        elementParent: !!region.element.parentElement
      });
    }

    // Set data attributes on the region element for CSS targeting
    if (region.element) {
      if (options.attributes) {
        for (const [key, value] of Object.entries(options.attributes)) {
          region.element.dataset[key] = value as string;
        }
      }

      // Add additional class for segment type if it exists
      if (options.attributes && options.attributes.type === 'segment') {
        region.element.classList.add('segment-region');

        // Add specific segment type class if it exists
        if (options.attributes.segmentType) {
          region.element.classList.add(`segment-type-${options.attributes.segmentType}`);
        }

        // Apply direct styles to ensure visibility for segments
        region.element.style.backgroundColor = options.color || 'rgba(0, 128, 255, 0.2)';
        region.element.style.border = '1px solid ' + (options.color || 'rgba(0, 128, 255, 0.8)');
        region.element.style.opacity = '0.3';
        region.element.style.zIndex = '5';

        // Ensure the content element is visible
        const contentEl = region.element.querySelector('.wavesurfer-region-content');
        if (contentEl && options.label) {
          contentEl.textContent = options.label;
          // Use type assertion to handle styling
          const contentElWithStyle = contentEl as HTMLElement;
          contentElWithStyle.style.color = '#ffffff';
          contentElWithStyle.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
          contentElWithStyle.style.padding = '2px 4px';
          contentElWithStyle.style.borderRadius = '2px';
          contentElWithStyle.style.fontSize = '10px';
          contentElWithStyle.style.position = 'absolute';
          contentElWithStyle.style.top = '0';
          contentElWithStyle.style.left = '0';
          contentElWithStyle.style.whiteSpace = 'nowrap';
          contentElWithStyle.style.display = 'block';
          contentElWithStyle.style.pointerEvents = 'none';
        }
      }

      // Set title attribute for tooltip
      if (options.label) {
        region.element.title = options.label;
      }
    }

    // Log the region creation for debugging
    console.log(`[WaveSurferVisualization] Created region ${regionId} with attributes:`,
      options.attributes,
      'Element:', region.element,
      'Content:', options.label);

    // Store the region
    const trackRegions = this.regions.get(trackId) || new Map();
    trackRegions.set(regionId, region);
    this.regions.set(trackId, trackRegions);

    console.log(`[WaveSurferVisualization] Added region ${regionId} to track ${trackId}`);
  }

  /**
   * Remove a region from a waveform
   * @param trackId The ID of the track
   * @param regionId The ID of the region
   */
  removeRegion(trackId: string, regionId: string): void {
    try {
      // Get the instance
      const wavesurfer = this.instances.get(trackId);
      if (!wavesurfer) {
        console.warn(`[WaveSurferVisualization] Cannot remove region from track ${trackId}: waveform not created`);
        return;
      }

      // Get the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
      if (!regionsPlugin) {
        console.warn(`[WaveSurferVisualization] Cannot remove region from track ${trackId}: regions plugin not found`);
        return;
      }

      // Find the region in the WaveSurfer instance
      const region = regionsPlugin.getRegions().find(r => r.id === regionId);

      if (region) {
        // Remove the region from WaveSurfer
        region.remove();
        console.log(`[WaveSurferVisualization] Removed region ${regionId} from WaveSurfer for track ${trackId}`);
      } else {
        console.warn(`[WaveSurferVisualization] Region ${regionId} not found in WaveSurfer for track ${trackId}`);
      }

      // Get the region from our internal map
      const trackRegions = this.regions.get(trackId);
      if (trackRegions && trackRegions.has(regionId)) {
        // Remove from our internal map
        trackRegions.delete(regionId);
        console.log(`[WaveSurferVisualization] Removed region ${regionId} from internal map for track ${trackId}`);
      }

      // Force a DOM cleanup for any orphaned region elements
      if (region && region.element) {
        const regionElement = region.element;
        if (regionElement && regionElement.parentNode) {
          regionElement.parentNode.removeChild(regionElement);
          console.log(`[WaveSurferVisualization] Forcibly removed region element from DOM for ${regionId}`);
        }
      }

      // Look for any orphaned elements with the same ID
      const orphanedElements = document.querySelectorAll(`[data-id="${regionId}"]`);
      if (orphanedElements.length > 0) {
        orphanedElements.forEach(el => {
          if (el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });
        console.log(`[WaveSurferVisualization] Removed ${orphanedElements.length} orphaned elements for region ${regionId}`);
      }

      console.log(`[WaveSurferVisualization] Removed region ${regionId} from track ${trackId}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error removing region ${regionId} from track ${trackId}:`, error);

      // Try a more aggressive approach if the normal removal fails
      try {
        // Force removal of any elements with this region ID
        const elements = document.querySelectorAll(`[data-id="${regionId}"], .wavesurfer-region[id="${regionId}"]`);
        elements.forEach(el => {
          if (el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });

        // Clean up our internal map
        const trackRegions = this.regions.get(trackId);
        if (trackRegions) {
          trackRegions.delete(regionId);
        }

        console.log(`[WaveSurferVisualization] Forcibly removed region ${regionId} elements from DOM`);
      } catch (innerError) {
        console.error(`[WaveSurferVisualization] Failed to forcibly remove region ${regionId}:`, innerError);
      }
    }
  }

  /**
   * Update a region on a waveform
   * @param trackId The ID of the track
   * @param regionId The ID of the region
   * @param options Options to update
   */
  updateRegion(trackId: string, regionId: string, options: any): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot update region on track ${trackId}: waveform not created`);
      return;
    }

    // Get the regions plugin
    const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
    if (!regionsPlugin) {
      console.warn(`[WaveSurferVisualization] Cannot update region on track ${trackId}: regions plugin not found`);
      return;
    }

    // Get the region
    const region = regionsPlugin.getRegions().find(r => r.id === regionId);
    if (!region) {
      console.warn(`[WaveSurferVisualization] Cannot update region ${regionId} on track ${trackId}: region not found`);
      return;
    }

    // Update the region
    if (options.start !== undefined) region.setOptions({ start: options.start });
    if (options.end !== undefined) region.setOptions({ end: options.end });
    if (options.color !== undefined) region.setOptions({ color: options.color });
    if (options.drag !== undefined) region.setOptions({ drag: options.drag });
    if (options.resize !== undefined) region.setOptions({ resize: options.resize });

    console.log(`[WaveSurferVisualization] Updated region ${regionId} on track ${trackId}`);
  }

  /**
   * Add a marker to a waveform
   * @param trackId The ID of the track
   * @param markerId The ID of the marker
   * @param time The time of the marker (in seconds)
   * @param options Options for the marker
   */
  addMarker(trackId: string, markerId: string, time: number, options: any = {}): void {
    // Markers are just regions with the same start and end time
    this.addRegion(trackId, markerId, {
      start: time,
      end: time,
      color: options.color || 'rgba(255, 0, 0, 0.5)',
      drag: options.drag || false,
      resize: false,
      ...options,
    });
  }

  /**
   * Add beat grid markers to a waveform
   * @param trackId The ID of the track
   * @param beatTimes Array of beat times in seconds
   * @param options Options for the beat grid markers
   */
  addBeatGrid(trackId: string, beatTimes: number[], options: any = {}): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot add beat grid to track ${trackId}: waveform not created`);
      return;
    }

    // Remove any existing beat grid markers
    this.removeBeatGrid(trackId);

    // Add a marker for each beat
    beatTimes.forEach((time, index) => {
      // Create a unique ID for the marker
      const markerId = `beat-${trackId}-${index}`;

      // Determine if this is a downbeat (first beat of a bar)
      // Assuming 4/4 time signature
      const isDownbeat = index % 4 === 0;

      // Add the marker
      this.addMarker(trackId, markerId, time, {
        color: isDownbeat ? (options.downbeatColor || 'rgba(255, 0, 0, 0.7)') : (options.color || 'rgba(0, 0, 255, 0.5)'),
        width: isDownbeat ? (options.downbeatWidth || 2) : (options.width || 1),
        label: options.showLabels ? `${Math.floor(index / 4) + 1}.${(index % 4) + 1}` : '',
        drag: options.draggable || false,
        resize: false,
        className: isDownbeat ? 'beat-marker downbeat' : 'beat-marker',
      });
    });

    console.log(`[WaveSurferVisualization] Added ${beatTimes.length} beat markers to track ${trackId}`);
  }

  /**
   * Remove all beat grid markers from a waveform
   * @param trackId The ID of the track
   */
  removeBeatGrid(trackId: string): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot remove beat grid from track ${trackId}: waveform not created`);
      return;
    }

    // Get the regions plugin
    const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
    if (!regionsPlugin) {
      console.warn(`[WaveSurferVisualization] Cannot remove beat grid from track ${trackId}: regions plugin not found`);
      return;
    }

    // Get all beat grid markers
    const beatMarkers = regionsPlugin.getRegions().filter(region => region.id.startsWith(`beat-${trackId}`));

    // Remove each marker
    beatMarkers.forEach(marker => marker.remove());

    console.log(`[WaveSurferVisualization] Removed ${beatMarkers.length} beat markers from track ${trackId}`);
  }

  /**
   * Load and display the beat grid for a track
   * @param trackId The ID of the track
   * @param options Options for the beat grid markers
   */
  async loadBeatGrid(trackId: string, options: any = {}): Promise<void> {
    try {
      // Import the beat grid service
      const { getTrackBeatGrid, parseBeatTimes } = await import('@/services/api/beatGrid');

      // Get the beat grid
      const beatGrid = await getTrackBeatGrid(trackId);

      if (!beatGrid) {
        console.warn(`[WaveSurferVisualization] No beat grid found for track ${trackId}`);
        return;
      }

      // Parse the beat times
      let beatTimes: number[] = [];

      if (beatGrid.beat_times) {
        beatTimes = parseBeatTimes(beatGrid.beat_times);
      } else if ((beatGrid as any).beat_grid && (beatGrid as any).beat_grid.beat_times) {
        beatTimes = parseBeatTimes((beatGrid as any).beat_grid.beat_times);
      }

      if (beatTimes.length === 0) {
        console.warn(`[WaveSurferVisualization] No beat times found in beat grid for track ${trackId}`);
        return;
      }

      // Add the beat grid
      this.addBeatGrid(trackId, beatTimes, options);

      console.log(`[WaveSurferVisualization] Loaded beat grid for track ${trackId} with ${beatTimes.length} beats`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error loading beat grid for track ${trackId}:`, error);
    }
  }

  /**
   * Remove a marker from a waveform
   * @param trackId The ID of the track
   * @param markerId The ID of the marker
   */
  removeMarker(trackId: string, markerId: string): void {
    this.removeRegion(trackId, markerId);
  }

  /**
   * Update a marker on a waveform
   * @param trackId The ID of the track
   * @param markerId The ID of the marker
   * @param time The new time of the marker (in seconds)
   * @param options Options to update
   */
  updateMarker(trackId: string, markerId: string, time: number, options: any = {}): void {
    this.updateRegion(trackId, markerId, {
      start: time,
      end: time,
      ...options,
    });
  }

  /**
   * Set the current time of a waveform
   * CRITICAL FIX: In beat-locked timeline, waveforms should NEVER move!
   * Waveforms are STATIC and locked to their timeline positions.
   * @param trackId The ID of the track
   * @param time The time to set (in seconds)
   */
  setCurrentTime(trackId: string, time: number): void {
    // ARCHITECTURAL FIX: Waveforms are STATIC in professional timeline
    // Only the global playhead moves, waveforms stay locked to timeline positions
    console.log(`[WaveSurferVisualization] 🔒 STATIC WAVEFORM MODE: Ignoring setTime(${time.toFixed(3)}s) for track ${trackId}`);

    // COMPLETELY DISABLED: No waveform movement allowed
    // This prevents the "waveform sliding inside track" issue
    return;
  }

  /**
   * Set zoom level for a waveform (timeline-synchronized zoom control)
   * @param trackId The ID of the track
   * @param timelinePixelsPerSecond The timeline zoom level in pixels per second (5-200)
   */
  setZoom(trackId: string, timelinePixelsPerSecond: number): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot set zoom for track ${trackId}: waveform not created`);
      return;
    }

    try {
      // CRITICAL FIX: Convert timeline pixelsPerSecond to WaveSurfer minPxPerSec
      // Timeline zoom: 1-200 px/s (for timeline grid/ruler scaling) - EXTENDED RANGE
      // WaveSurfer zoom: 10-1600 minPxPerSec (for waveform detail) - EXTENDED RANGE
      //
      // Professional conversion formula to maintain visual consistency:
      // - At timeline zoom 1 px/s (extremely zoomed out): WaveSurfer should show 10 minPxPerSec (extreme overview)
      // - At timeline zoom 200 px/s (very zoomed in): WaveSurfer should show 1600 minPxPerSec (detail)
      const minTimelineZoom = 1;
      const maxTimelineZoom = 200;
      const minWaveSurferZoom = 10;
      const maxWaveSurferZoom = 1600;

      // Linear interpolation to map timeline zoom to WaveSurfer zoom
      const normalizedZoom = (timelinePixelsPerSecond - minTimelineZoom) / (maxTimelineZoom - minTimelineZoom);
      const waveSurferMinPxPerSec = minWaveSurferZoom + (normalizedZoom * (maxWaveSurferZoom - minWaveSurferZoom));

      // Clamp to safe range
      const clampedMinPxPerSec = Math.max(minWaveSurferZoom, Math.min(maxWaveSurferZoom, waveSurferMinPxPerSec));

      // Apply the zoom
      wavesurfer.zoom(clampedMinPxPerSec);

      console.log(`[WaveSurferVisualization] 🔍 ZOOM SYNC: Timeline ${timelinePixelsPerSecond} px/s → WaveSurfer ${clampedMinPxPerSec.toFixed(0)} minPxPerSec`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error setting zoom for track ${trackId}:`, error);
    }
  }

  /**
   * Batch update the current time of multiple waveforms at once
   * CRITICAL FIX: In professional timeline, waveforms are STATIC!
   * @param updates Array of updates with trackId and time
   */
  batchUpdateTimes(updates: Array<{ trackId: string, time: number }>): void {
    // ARCHITECTURAL FIX: Waveforms are STATIC in professional timeline
    // Only the global playhead moves, waveforms stay locked to timeline positions
    console.log(`[WaveSurferVisualization] 🔒 STATIC WAVEFORM MODE: Ignoring batch time updates for ${updates.length} tracks`);

    // COMPLETELY DISABLED: No waveform movement allowed
    // This prevents the "waveform sliding inside track" issue
    return;
  }

  /**
   * Update cursor positions for all tracks based on global timeline time (optimized)
   * CRITICAL FIX: In a beat-locked timeline, waveforms should NOT move when seeking!
   * Only the playhead/cursor should move to show the current position.
   * @param globalTime The global timeline time in seconds
   */
  updateAllCursorPositions = throttle((globalTime: number): void => {
    // CRITICAL FIX: For beat-locked timeline, we should NOT move waveforms
    // Instead, we should only update visual cursors/playheads
    // The waveforms should stay locked to their timeline positions

    console.log(`[WaveSurferVisualization] 🔒 STATIC WAVEFORM MODE: Ignoring cursor update for globalTime ${globalTime.toFixed(3)}s`);

    // ARCHITECTURAL PRINCIPLE: Waveforms are STATIC visual elements
    // They represent audio content at fixed timeline positions
    // Only the global playhead/cursor moves to show current playback position
    return;

    // DISABLED: The old code that was moving waveforms incorrectly
    // batchDOMOperations(() => {
    //   this.instances.forEach((wavesurfer, trackId) => {
    //     try {
    //       const trackStartTime = timelineCoordinatorEnhanced.getTrackStartTime(trackId);
    //       const trackTime = globalTime - trackStartTime;
    //       const trackDuration = wavesurfer.getDuration();
    //       if (trackTime >= 0 && trackTime <= trackDuration) {
    //         wavesurfer.setTime(trackTime); // ← THIS WAS MOVING WAVEFORMS!
    //       }
    //     } catch (error) {
    //       console.error(`[WaveSurferVisualization] Error updating cursor position for track ${trackId}:`, error);
    //     }
    //   });
    // });
  }, 16); // ~60fps throttling

  /**
   * Set display options for a waveform
   * @param trackId The ID of the track
   * @param options Display options for the waveform
   */
  setWaveformDisplayOptions(trackId: string, options: {
    barWidth?: number;
    barGap?: number;
    waveColor?: string;
    progressColor?: string;
  }): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot set display options for track ${trackId}: waveform not created`);
      return;
    }

    // Apply the display options
    wavesurfer.setOptions({
      barWidth: options.barWidth !== undefined ? options.barWidth : 1,
      barGap: options.barGap !== undefined ? options.barGap : 1,
      waveColor: options.waveColor || '#4F46E5',
      progressColor: options.progressColor || '#818CF8',
    });
  }

  /**
   * Add an event listener to a waveform
   * @param trackId The ID of the track
   * @param event The event to listen for
   * @param callback The callback to execute
   */
  on(trackId: string, event: string, callback: (arg?: any) => void): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot add event listener to track ${trackId}: waveform not created`);
      return;
    }

    // Add the event listener using type assertion to handle string event names
    (wavesurfer as any).on(event, callback);
  }

  /**
   * Remove an event listener from a waveform
   * @param trackId The ID of the track
   * @param event The event to stop listening for
   * @param callback The callback to remove
   */
  off(trackId: string, event: string, callback: (arg?: any) => void): void {
    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot remove event listener from track ${trackId}: waveform not created`);
      return;
    }

    // Remove the event listener using type assertion to handle string event names
    (wavesurfer as any).un(event, callback);
  }

  /**
   * Create a waveform with error handling and fallbacks
   * @param trackId The ID of the track
   * @param container The container element
   * @param options Options for the waveform
   */
  async createWaveformWithErrorHandling(trackId: string, container: HTMLElement, options: any = {}): Promise<void> {
    try {
      console.log(`[WaveSurferVisualization] Creating waveform for track ${trackId}`);

      // Check if we already have a waveform for this track
      if (this.instances.has(trackId)) {
        console.log(`[WaveSurferVisualization] Waveform already exists for track ${trackId}`);
        return;
      }

      // Store the container
      this.containers.set(trackId, container);

      // Create a loading promise
      const loadingPromise = new Promise<void>(async (resolve, reject) => {
        try {
          // Create the waveform
          const wavesurfer = WaveSurfer.create({
            container,
            waveColor: options.waveColor || '#4F46E5',
            progressColor: options.progressColor || '#818CF8',
            cursorColor: options.cursorColor || '#C7D2FE',
            barWidth: options.barWidth || 2,
            barGap: options.barGap || 1,
            height: options.height || 80,
            backend: 'WebAudio',
          });

          // Get the URL from options
          const url = options.url;
          if (!url) {
            throw new Error(`No URL provided for track ${trackId}`);
          }

          // Load the URL
          wavesurfer.load(url);

          // Store the instance
          this.instances.set(trackId, wavesurfer);

          // Initialize regions
          this.regions.set(trackId, new Map());

          // Add event listeners
          wavesurfer.on('ready', () => {
            console.log(`[WaveSurferVisualization] Waveform ready for track ${trackId}`);

            // Dispatch custom event for components that need to know when waveform is ready
            const readyEvent = new CustomEvent('waveform-ready', {
              detail: { trackId }
            });
            window.dispatchEvent(readyEvent);

            resolve();
          });

          wavesurfer.on('error', (error) => {
            console.error(`[WaveSurferVisualization] Error creating waveform for track ${trackId}:`, error);

            // Handle the error with a fallback
            this.handleWaveformError(trackId, container, options, error, resolve, reject);
          });
        } catch (error) {
          console.error(`[WaveSurferVisualization] Error creating waveform for track ${trackId}:`, error);

          // Handle the error with a fallback
          this.handleWaveformError(trackId, container, options, error, resolve, reject);
        }
      });

      // Store the loading promise
      this.loadingPromises.set(trackId, loadingPromise);

      // Wait for the waveform to load
      await loadingPromise;

      console.log(`[WaveSurferVisualization] Created waveform for track ${trackId}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error creating waveform for track ${trackId}:`, error);

      // Handle the error
      ErrorHandler.handleError({
        type: ErrorType.WAVEFORM_CREATE_ERROR,
        severity: ErrorSeverity.WARNING,
        message: `Failed to create waveform for track. The track will still play but may not display correctly.`,
        details: error instanceof Error ? error.message : 'Unknown error',
        error: error instanceof Error ? error : new Error('Unknown error')
      });

      throw error;
    }
  }

  /**
   * Handle a waveform error with fallbacks
   * @param trackId The ID of the track
   * @param container The container element
   * @param options Options for the waveform
   * @param error The error that occurred
   * @param resolve The resolve function for the loading promise
   * @param reject The reject function for the loading promise
   */
  private async handleWaveformError(
    trackId: string,
    container: HTMLElement,
    options: any,
    error: any,
    resolve: () => void,
    reject: (error: any) => void
  ): Promise<void> {
    try {
      // Log the error using our error handling module
      errorHandling.handleError(error, {
        operation: 'createWaveform',
        trackId,
        critical: false
      });

      try {
        // Create a fallback waveform using our error handling module
        errorHandling.createFallbackWaveform(container, trackId);

        // Create a fallback peaks array
        const duration = options.duration || 30;
        const peaks = AudioFallback.createFallbackPeaks(duration);

        // Create a simple WaveSurfer instance
        const wavesurfer = WaveSurfer.create({
          container,
          waveColor: '#9CA3AF', // Gray color for fallback
          progressColor: '#6B7280',
          cursorColor: '#C7D2FE',
          barWidth: 1,
          barGap: 0,
          height: options.height || 80,
          backend: 'WebAudio',
        });

        // Load the peaks
        wavesurfer.load(
          'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA',
          [peaks]
        );

        // Store the instance
        this.instances.set(trackId, wavesurfer);

        // Initialize regions
        this.regions.set(trackId, new Map());

        console.log(`[WaveSurferVisualization] Created fallback waveform for track ${trackId}`);

        // Apply optimization settings for the fallback
        optimize.setTrackVisibility(this.instances, this.containers, trackId, true);

        // Resolve the promise
        resolve();
      } catch (fallbackError) {
        console.error(`[WaveSurferVisualization] Error creating fallback waveform for track ${trackId}:`, fallbackError);

        // Reject the promise
        reject(fallbackError);
      }
    } catch (handlerError) {
      console.error(`[WaveSurferVisualization] Error handling waveform error for track ${trackId}:`, handlerError);

      // Reject the promise
      reject(handlerError);
    }
  }

  /**
   * Set the current time with error handling
   * @param trackId The ID of the track
   * @param time The time to set
   */
  setCurrentTimeWithErrorHandling(trackId: string, time: number): void {
    // CRITICAL FIX: For beat-locked timeline, disable waveform movement
    console.log(`[WaveSurferVisualization] 🔒 BEAT-LOCKED MODE: NOT setting time ${time.toFixed(3)}s for track ${trackId} (with error handling)`);

    // DISABLED: The old code that was moving waveforms
    // try {
    //   const wavesurfer = this.instances.get(trackId);
    //   if (!wavesurfer) {
    //     console.warn(`[WaveSurferVisualization] Cannot set current time for track ${trackId}: waveform not created`);
    //     return;
    //   }
    //   wavesurfer.setTime(time); // ← THIS WAS MOVING WAVEFORMS!
    // } catch (error) {
    //   console.error(`[WaveSurferVisualization] Error setting current time for track ${trackId}:`, error);
    //   ErrorHandler.handleError({...});
    // }
  }

  /**
   * Add a region with error handling
   * @param trackId The ID of the track
   * @param regionId The ID of the region
   * @param options Options for the region
   */
  addRegionWithErrorHandling(trackId: string, regionId: string, options: any): void {
    try {
      // Get the instance
      const wavesurfer = this.instances.get(trackId);
      if (!wavesurfer) {
        console.warn(`[WaveSurferVisualization] Cannot add region to track ${trackId}: waveform not created`);
        return;
      }

      // Get the regions map
      const regions = this.regions.get(trackId);
      if (!regions) {
        console.warn(`[WaveSurferVisualization] Cannot add region to track ${trackId}: regions map not created`);
        return;
      }

      // Check if the region already exists
      if (regions.has(regionId)) {
        console.log(`[WaveSurferVisualization] Region ${regionId} already exists for track ${trackId}`);
        return;
      }

      // Create the region using the regions plugin
      const regionsPlugin = wavesurfer.getActivePlugins().find(plugin => plugin instanceof RegionsPlugin) as RegionsPlugin;
      if (!regionsPlugin) {
        console.warn(`[WaveSurferVisualization] Cannot add region to track ${trackId}: regions plugin not found`);
        return;
      }

      const region = regionsPlugin.addRegion({
        id: regionId,
        start: options.start || 0,
        end: options.end || 0,
        color: options.color || 'rgba(0, 0, 255, 0.2)',
        drag: options.drag !== undefined ? options.drag : false,
        resize: options.resize !== undefined ? options.resize : false,
      });

      // Add a label if provided
      if (options.label) {
        region.element.title = options.label;

        // Add a label element
        const label = document.createElement('div');
        label.className = 'region-label';
        label.textContent = options.label;
        label.style.position = 'absolute';
        label.style.top = '0';
        label.style.left = '0';
        label.style.padding = '2px 4px';
        label.style.fontSize = '10px';
        label.style.color = 'white';
        label.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        label.style.borderRadius = '2px';
        label.style.pointerEvents = 'none';

        region.element.appendChild(label);
      }

      // Store the region
      regions.set(regionId, region);

      console.log(`[WaveSurferVisualization] Added region ${regionId} to track ${trackId}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error adding region ${regionId} to track ${trackId}:`, error);

      // Handle the error
      ErrorHandler.handleError({
        type: ErrorType.WAVEFORM_RENDER_ERROR,
        severity: ErrorSeverity.INFO,
        message: `Failed to add region to waveform. The track will still play but some visual elements may be missing.`,
        details: error instanceof Error ? error.message : 'Unknown error',
        error: error instanceof Error ? error : new Error('Unknown error')
      });
    }
  }

  /**
   * Create a spectrogram for a track
   * @param trackId The ID of the track
   * @param container The container element
   * @param options Options for the spectrogram
   */
  async createSpectrogram(trackId: string, container: HTMLElement, options: any = {}): Promise<void> {
    // Check if the waveform exists and is ready
    if (!this.isWaveformReady(trackId)) {
      console.warn(`[WaveSurferVisualization] Cannot create spectrogram for track ${trackId}: waveform not ready`);
      return;
    }

    // Get the instance (we know it exists and is ready)
    const wavesurfer = this.instances.get(trackId)!;

    // Check if we already have a spectrogram for this track
    if (this.spectrograms.has(trackId)) {
      // Destroy the existing spectrogram
      await this.destroySpectrogram(trackId);
    }

    try {
      console.log(`[WaveSurferVisualization] Creating spectrogram for track ${trackId}`);

      // Define the base color points
      const baseColorPoints: [number, number, number, number][] = options.colorPoints || [
        [0, 0, 0, 0],       // transparent for silence
        [0, 0, 255, 0.2],   // low frequencies (blue)
        [0, 255, 0, 0.3],   // mid frequencies (green)
        [255, 255, 0, 0.4], // mid-high frequencies (yellow)
        [255, 0, 0, 0.5],   // high frequencies (red)
      ];

      // Generate a 256-element colormap from the base color points
      const fullColormap = generateColormap(baseColorPoints);

      // Default options
      const defaultOptions = {
        container,
        labels: true,
        height: 128,
        colorMap: fullColormap, // Use the 256-element colormap
        frequencyMin: 0,
        frequencyMax: 20000,
        noverlap: 128,
        windowSize: 1024,
        // Use the same audio context as the waveform
        wavesurfer,
      };

      // Create the spectrogram
      const spectrogram = SpectrogramPlugin.create({
        ...defaultOptions,
        ...options,
        // Always use our generated colormap, even if options includes a colorMap
        colorMap: options.colorMap && options.colorMap.length === 256
          ? options.colorMap
          : fullColormap,
      });

      // Add the spectrogram to the waveform
      wavesurfer.registerPlugin(spectrogram);

      // Store the spectrogram
      this.spectrograms.set(trackId, spectrogram);

      console.log(`[WaveSurferVisualization] Created spectrogram for track ${trackId}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error creating spectrogram for track ${trackId}:`, error);
      throw error;
    }
  }

  /**
   * Destroy a spectrogram
   * @param trackId The ID of the track
   */
  async destroySpectrogram(trackId: string): Promise<void> {
    // Get the spectrogram
    const spectrogram = this.spectrograms.get(trackId);
    if (!spectrogram) return;

    // Get the instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot destroy spectrogram for track ${trackId}: waveform not created`);
      return;
    }

    // Destroy the spectrogram - use type assertion since unregisterPlugin is not in the type definitions
    const wavesurferAny = wavesurfer as any;
    if (typeof wavesurferAny.unregisterPlugin === 'function') {
      wavesurferAny.unregisterPlugin(spectrogram);
    } else if (spectrogram && typeof spectrogram.destroy === 'function') {
      // If unregisterPlugin is not available, try to destroy the plugin directly
      spectrogram.destroy();
    }

    // Remove from map
    this.spectrograms.delete(trackId);

    console.log(`[WaveSurferVisualization] Destroyed spectrogram for track ${trackId}`);
  }



  /**
   * Set fade in for a track using the envelope plugin
   * @param trackId The ID of the track
   * @param fadeInEnd The end time of the fade in (in seconds)
   */
  setFadeIn(trackId: string, fadeInEnd: number): void {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot set fade in for track ${trackId}: envelope not created`);
      return;
    }

    // Get the wavesurfer instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot set fade in for track ${trackId}: waveform not created`);
      return;
    }

    // Get the duration
    const duration = wavesurfer.getDuration();

    // Set the fade in by adding points
    // We need to use the generic API since the specific methods might not be available
    try {
      // Clear existing points
      (envelope as any).params.points = [];

      // Add fade in points
      (envelope as any).addPoint(0, 0);
      (envelope as any).addPoint(fadeInEnd, 1);

      // Add a point at the end to maintain full volume
      (envelope as any).addPoint(duration, 1);

      console.log(`[WaveSurferVisualization] Set fade in for track ${trackId} to ${fadeInEnd}s`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error setting fade in for track ${trackId}:`, error);
    }
  }

  /**
   * Set fade out for a track using the envelope plugin
   * @param trackId The ID of the track
   * @param fadeOutStart The start time of the fade out (in seconds)
   */
  setFadeOut(trackId: string, fadeOutStart: number): void {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot set fade out for track ${trackId}: envelope not created`);
      return;
    }

    // Get the wavesurfer instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot set fade out for track ${trackId}: waveform not created`);
      return;
    }

    // Get the duration
    const duration = wavesurfer.getDuration();

    // Set the fade out by adding points
    // We need to use the generic API since the specific methods might not be available
    try {
      // Clear existing points
      (envelope as any).params.points = [];

      // Add a point at the beginning to maintain full volume
      (envelope as any).addPoint(0, 1);

      // Add fade out points
      (envelope as any).addPoint(fadeOutStart, 1);
      (envelope as any).addPoint(duration, 0);

      console.log(`[WaveSurferVisualization] Set fade out for track ${trackId} from ${fadeOutStart}s to ${duration}s`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error setting fade out for track ${trackId}:`, error);
    }
  }

  /**
   * Set both fade in and fade out for a track
   * @param trackId The ID of the track
   * @param fadeInEnd The end time of the fade in (in seconds)
   * @param fadeOutStart The start time of the fade out (in seconds)
   */
  setFades(trackId: string, fadeInEnd: number, fadeOutStart: number): void {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot set fades for track ${trackId}: envelope not created`);
      return;
    }

    // Get the wavesurfer instance
    const wavesurfer = this.instances.get(trackId);
    if (!wavesurfer) {
      console.warn(`[WaveSurferVisualization] Cannot set fades for track ${trackId}: waveform not created`);
      return;
    }

    // Get the duration
    const duration = wavesurfer.getDuration();

    // Set the fades by adding points
    // We need to use the generic API since the specific methods might not be available
    try {
      // Clear existing points
      (envelope as any).params.points = [];

      // Add fade in points
      (envelope as any).addPoint(0, 0);
      (envelope as any).addPoint(fadeInEnd, 1);

      // Add fade out points
      (envelope as any).addPoint(fadeOutStart, 1);
      (envelope as any).addPoint(duration, 0);

      console.log(`[WaveSurferVisualization] Set fades for track ${trackId}: in to ${fadeInEnd}s, out from ${fadeOutStart}s`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error setting fades for track ${trackId}:`, error);
    }
  }

  /**
   * Set volume for a track
   * @param trackId The ID of the track
   * @param volume The volume (0-1)
   */
  setEnvelopeVolume(trackId: string, volume: number): void {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot set volume for track ${trackId}: envelope not created`);
      return;
    }

    // Set the volume
    try {
      (envelope as any).setVolume(volume);
      console.log(`[WaveSurferVisualization] Set volume for track ${trackId} to ${volume}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error setting volume for track ${trackId}:`, error);

      // Try an alternative approach
      try {
        // Get the wavesurfer instance
        const wavesurfer = this.instances.get(trackId);
        if (wavesurfer) {
          // Set the volume directly on the wavesurfer instance
          wavesurfer.setVolume(volume);
          console.log(`[WaveSurferVisualization] Set volume directly for track ${trackId} to ${volume}`);
        }
      } catch (fallbackError) {
        console.error(`[WaveSurferVisualization] Error setting volume directly for track ${trackId}:`, fallbackError);
      }
    }
  }

  /**
   * Add a volume point to the envelope
   * @param trackId The ID of the track
   * @param time The time of the point (in seconds)
   * @param volume The volume at the point (0-1)
   */
  addVolumePoint(trackId: string, time: number, volume: number): void {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot add volume point for track ${trackId}: envelope not created`);
      return;
    }

    // Add the point
    try {
      (envelope as any).addPoint(time, volume);
      console.log(`[WaveSurferVisualization] Added volume point for track ${trackId} at ${time}s with volume ${volume}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error adding volume point for track ${trackId}:`, error);
    }
  }

  /**
   * Get the envelope data for a track
   * @param trackId The ID of the track
   * @returns The envelope data
   */
  getEnvelopeData(trackId: string): any {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot get envelope data for track ${trackId}: envelope not created`);
      return null;
    }

    // Get the data
    try {
      return (envelope as any).params.points || [];
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error getting envelope data for track ${trackId}:`, error);
      return [];
    }
  }

  /**
   * Apply the envelope to the audio
   * @param trackId The ID of the track
   */
  applyEnvelope(trackId: string): void {
    // Get the envelope
    const envelope = this.envelopes.get(trackId);
    if (!envelope) {
      console.warn(`[WaveSurferVisualization] Cannot apply envelope for track ${trackId}: envelope not created`);
      return;
    }

    // Get the envelope data
    const envelopeData = this.getEnvelopeData(trackId);

    // Apply the envelope to the audio using Tone.js
    // We need to convert the envelope data to a format that Tone.js can use
    const automationPoints = envelopeData.map((point: any) => ({
      time: point.time,
      value: point.volume
    }));

    // Apply the automation
    try {
      // Use the automateParameter method to apply volume automation
      enhancedToneAudioEngine.automateParameter(
        trackId,
        'volume',
        'volume',
        automationPoints[0]?.value || 1,
        automationPoints[automationPoints.length - 1]?.value || 0,
        automationPoints[0]?.time || 0,
        (automationPoints[automationPoints.length - 1]?.time || 1) - (automationPoints[0]?.time || 0),
        'linear'
      );

      console.log(`[WaveSurferVisualization] Applied envelope for track ${trackId}`);
    } catch (error) {
      console.error(`[WaveSurferVisualization] Error applying envelope for track ${trackId}:`, error);
    }
  }

  /**
   * Initialize the recorder
   * @param container The container element for the recorder visualization
   * @param options Options for the recorder
   */
  async initializeRecorder(container: HTMLElement, options: any = {}): Promise<void> {
    try {
      console.log('[WaveSurferVisualization] Initializing recorder');

      // Create a new WaveSurfer instance for recording
      const wavesurfer = WaveSurfer.create({
        container,
        waveColor: '#4F46E5',
        progressColor: '#818CF8',
        cursorColor: '#C7D2FE',
        barWidth: 2,
        barGap: 1,
        height: 100,
        ...options,
      });

      // Create the recorder plugin with custom options
      // Use type assertion to bypass TypeScript restrictions
      this.recorder = RecordPlugin.create({
        // Customize recording settings
        encoderPath: '/encoders/wav-worker.js',
        mediaType: 'audio/wav',
        sampleRate: 44100,
        numberOfChannels: 2,
      } as any);

      // Register the recorder plugin
      wavesurfer.registerPlugin(this.recorder);

      // Store the instance
      this.instances.set('recorder', wavesurfer);

      console.log('[WaveSurferVisualization] Recorder initialized');
    } catch (error) {
      console.error('[WaveSurferVisualization] Error initializing recorder:', error);
      throw error;
    }
  }

  /**
   * Start recording
   */
  async startRecording(): Promise<void> {
    if (!this.recorder) {
      console.warn('[WaveSurferVisualization] Cannot start recording: recorder not initialized');
      return;
    }

    try {
      console.log('[WaveSurferVisualization] Starting recording');

      // Start recording
      await this.recorder.startRecording();

      console.log('[WaveSurferVisualization] Recording started');
    } catch (error) {
      console.error('[WaveSurferVisualization] Error starting recording:', error);
      throw error;
    }
  }

  /**
   * Stop recording
   * @returns The recorded audio blob
   */
  async stopRecording(): Promise<Blob> {
    if (!this.recorder) {
      console.warn('[WaveSurferVisualization] Cannot stop recording: recorder not initialized');
      return new Blob();
    }

    try {
      console.log('[WaveSurferVisualization] Stopping recording');

      // Stop recording - use type assertion to handle the return type
      const blob = (this.recorder as any).stopRecording() as Blob;

      console.log('[WaveSurferVisualization] Recording stopped');

      return blob;
    } catch (error) {
      console.error('[WaveSurferVisualization] Error stopping recording:', error);
      throw error;
    }
  }

  /**
   * Pause recording
   */
  async pauseRecording(): Promise<void> {
    if (!this.recorder) {
      console.warn('[WaveSurferVisualization] Cannot pause recording: recorder not initialized');
      return;
    }

    try {
      console.log('[WaveSurferVisualization] Pausing recording');

      // Pause recording - use type assertion
      (this.recorder as any).pauseRecording();

      console.log('[WaveSurferVisualization] Recording paused');
    } catch (error) {
      console.error('[WaveSurferVisualization] Error pausing recording:', error);
      throw error;
    }
  }

  /**
   * Resume recording
   */
  async resumeRecording(): Promise<void> {
    if (!this.recorder) {
      console.warn('[WaveSurferVisualization] Cannot resume recording: recorder not initialized');
      return;
    }

    try {
      console.log('[WaveSurferVisualization] Resuming recording');

      // Resume recording - use type assertion
      (this.recorder as any).resumeRecording();

      console.log('[WaveSurferVisualization] Recording resumed');
    } catch (error) {
      console.error('[WaveSurferVisualization] Error resuming recording:', error);
      throw error;
    }
  }

  /**
   * Get the recording state
   * @returns The recording state
   */
  getRecordingState(): 'inactive' | 'recording' | 'paused' {
    if (!this.recorder) {
      return 'inactive';
    }

    // Use type assertion to access getState method
    return (this.recorder as any).getState();
  }

  /**
   * Destroy the recorder
   */
  async destroyRecorder(): Promise<void> {
    if (!this.recorder) return;

    // Get the instance
    const wavesurfer = this.instances.get('recorder');
    if (!wavesurfer) return;

    // Destroy the recorder - use type assertion since unregisterPlugin is not in the type definitions
    (wavesurfer as any).unregisterPlugin(this.recorder);

    // Destroy the instance
    wavesurfer.destroy();

    // Remove from map
    this.instances.delete('recorder');

    // Clear the recorder
    this.recorder = null;

    console.log('[WaveSurferVisualization] Recorder destroyed');
  }


  /**
   * Clean up unused instances to free memory
   */
  cleanupUnusedInstances(): void {
    // Get active track IDs from timeline store
    const activeTrackIds = new Set();
    try {
      const timelineStore = (window as any).useTimelineStore?.getState?.();
      if (timelineStore?.tracks) {
        timelineStore.tracks.forEach((track: any) => {
          activeTrackIds.add(track.id.toString());
        });
      }
    } catch (error) {
      console.warn('[WaveSurferVisualization] Could not get active track IDs for cleanup');
      return;
    }

    let cleanedCount = 0;

    // Clean up instances for tracks that are no longer active
    for (const [trackId] of this.instances) {
      if (!activeTrackIds.has(trackId)) {
        this.destroyWaveform(trackId);
        cleanedCount++;
      }
    }

    // Clean up orphaned regions
    for (const [trackId] of this.regions) {
      if (!activeTrackIds.has(trackId)) {
        this.regions.delete(trackId);
        cleanedCount++;
      }
    }

    // Clean up orphaned containers
    for (const [trackId] of this.containers) {
      if (!activeTrackIds.has(trackId)) {
        this.containers.delete(trackId);
        cleanedCount++;
      }
    }

    // Clean up orphaned envelopes
    for (const [trackId] of this.envelopes) {
      if (!activeTrackIds.has(trackId)) {
        this.envelopes.delete(trackId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`[WaveSurferVisualization] Cleaned up ${cleanedCount} unused instances/resources`);

      // Force garbage collection
      if ('gc' in window && typeof (window as any).gc === 'function') {
        setTimeout(() => (window as any).gc(), 100);
      }
    }
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    instances: number;
    regions: number;
    containers: number;
    envelopes: number;
    loadingPromises: number;
  } {
    return {
      instances: this.instances.size,
      regions: this.regions.size,
      containers: this.containers.size,
      envelopes: this.envelopes.size,
      loadingPromises: this.loadingPromises.size,
    };
  }

  /**
   * Get the count of active waveform instances
   */
  getInstanceCount(): number {
    return this.instances.size;
  }

  /**
   * Clean up all instances and resources
   */
  dispose(): void {
    // Destroy all instances
    this.instances.forEach((wavesurfer, trackId) => {
      this.destroyWaveform(trackId);
    });

    // Clear all maps
    this.instances.clear();
    this.regions.clear();
    this.containers.clear();
    this.envelopes.clear();
    this.loadingPromises.clear();

    console.log('[WaveSurferVisualization] Disposed of all resources');
  }
}

// Create a singleton instance
const waveSurferVisualization = new WaveSurferVisualization();

// Make it globally accessible for performance monitoring
(window as any).waveSurferVisualization = waveSurferVisualization;
(window as any).__waveSurferVisualizationModule = waveSurferVisualization;

export default waveSurferVisualization;
