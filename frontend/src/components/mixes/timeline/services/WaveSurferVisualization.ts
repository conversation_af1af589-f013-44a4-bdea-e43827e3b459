/**
 * WaveSurferVisualization
 *
 * Main visualization class that orchestrates all WaveSurfer functionality
 * by integrating specialized manager modules.
 */

import WaveSurfer from 'wavesurfer.js';

// Import all manager modules
import { WaveformManager } from './wavesurfer-visualization/WaveformManager';
import { RegionsMarkersManager } from './wavesurfer-visualization/RegionsMarkersManager';
import { BeatGridManager } from './wavesurfer-visualization/BeatGridManager';
import { SpectrogramManager } from './wavesurfer-visualization/SpectrogramManager';
import { EnvelopeManager } from './wavesurfer-visualization/EnvelopeManager';
import { PerformanceManager } from './wavesurfer-visualization/PerformanceManager';
import { RecordingManager } from './wavesurfer-visualization/RecordingManager';

class WaveSurferVisualization {
  // Manager instances
  private waveformManager: WaveformManager;
  private regionsMarkersManager: RegionsMarkersManager;
  private beatGridManager: BeatGridManager;
  private spectrogramManager: SpectrogramManager;
  private envelopeManager: EnvelopeManager;
  private recordingManager: RecordingManager;
  private performanceManager: PerformanceManager;

  constructor() {
    // Initialize all managers
    this.waveformManager = new WaveformManager();
    this.regionsMarkersManager = new RegionsMarkersManager();
    this.beatGridManager = new BeatGridManager();
    this.spectrogramManager = new SpectrogramManager();
    this.envelopeManager = new EnvelopeManager();
    this.recordingManager = new RecordingManager();
    this.performanceManager = new PerformanceManager();

    console.log('[WaveSurferVisualization] Initialized with all managers');
  }

  // ==================== WAVEFORM MANAGEMENT ====================

  async createWaveform(trackId: string, container: HTMLElement, options: any = {}): Promise<WaveSurfer> {
    return this.waveformManager.createWaveform(trackId, container, options);
  }

  async destroyWaveform(trackId: string): Promise<void> {
    // Clean up all managers first
    this.regionsMarkersManager.cleanupRegions(trackId);
    this.beatGridManager.cleanupBeatGrid(trackId);
    this.spectrogramManager.cleanupSpectrogram(trackId, this.getWaveform.bind(this));
    this.envelopeManager.cleanupEnvelope(trackId);
    this.recordingManager.cleanupRecording(trackId);
    this.performanceManager.cleanupTrackResources(trackId);

    // Then destroy the waveform
    return this.waveformManager.destroyWaveform(trackId);
  }

  getWaveform(trackId: string): WaveSurfer | undefined {
    return this.waveformManager.getWaveform(trackId);
  }

  getWrapper(trackId: string): HTMLElement | undefined {
    return this.waveformManager.getWrapper(trackId);
  }

  hasWaveform(trackId: string): boolean {
    return this.waveformManager.hasWaveform(trackId);
  }

  getAllWaveformIds(): string[] {
    return this.waveformManager.getAllWaveformIds();
  }

  isWaveformReady(trackId: string): boolean {
    return this.waveformManager.isWaveformReady(trackId);
  }

  getInstanceCount(): number {
    return this.waveformManager.getInstanceCount();
  }

  // ==================== REGIONS & MARKERS ====================

  // Regions and markers
  addRegion(trackId: string, regionId: string, options: any): void {
    this.regionsMarkersManager.addRegion(trackId, regionId, options, this.getWaveform.bind(this));
  }

  removeRegion(trackId: string, regionId: string): void {
    this.regionsMarkersManager.removeRegion(trackId, regionId, this.getWaveform.bind(this));
  }

  updateRegion(trackId: string, regionId: string, options: any): void {
    this.regionsMarkersManager.updateRegion(trackId, regionId, options, this.getWaveform.bind(this));
  }

  getRegion(trackId: string, regionId: string): any {
    return this.regionsMarkersManager.getRegion(trackId, regionId, this.getWaveform.bind(this));
  }

  addMarker(trackId: string, markerId: string, time: number, options: any = {}): void {
    this.regionsMarkersManager.addMarker(trackId, markerId, time, options, this.getWaveform.bind(this));
  }

  removeMarker(trackId: string, markerId: string): void {
    this.regionsMarkersManager.removeMarker(trackId, markerId, this.getWaveform.bind(this));
  }

  updateMarker(trackId: string, markerId: string, time: number, options: any = {}): void {
    this.regionsMarkersManager.updateMarker(trackId, markerId, time, options, this.getWaveform.bind(this));
  }

  addRegionWithErrorHandling(trackId: string, regionId: string, options: any): void {
    this.regionsMarkersManager.addRegionWithErrorHandling(trackId, regionId, options, this.getWaveform.bind(this));
  }

  clearRegions(trackId: string): void {
    this.regionsMarkersManager.clearRegions(trackId, this.getWaveform.bind(this));
  }

  getAllRegions(trackId: string): any[] {
    return this.regionsMarkersManager.getAllRegions(trackId, this.getWaveform.bind(this));
  }

  onRegionClick(trackId: string, regionId: string, callback: () => void): void {
    this.regionsMarkersManager.onRegionClick(trackId, regionId, callback, this.getWaveform.bind(this));
  }

  // Beat grid
  addBeatGridRegions(trackId: string, beatGrid: any): void {
    this.beatGridManager.addBeatGridRegions(trackId, beatGrid, this.getWaveform.bind(this), this.addRegion.bind(this));
  }

  removeBeatGridRegions(trackId: string): void {
    this.beatGridManager.removeBeatGridRegions(trackId, this.getAllRegions.bind(this), this.removeRegion.bind(this));
  }

  setBeatGridVisible(trackId: string, visible: boolean): void {
    this.beatGridManager.setBeatGridVisible(
      trackId, 
      visible, 
      this.getWaveform.bind(this), 
      this.addRegion.bind(this), 
      this.getAllRegions.bind(this), 
      this.removeRegion.bind(this)
    );
  }

  isBeatGridVisible(trackId: string): boolean {
    return this.beatGridManager.isBeatGridVisible(trackId);
  }

  getBeatGrid(trackId: string): any {
    return this.beatGridManager.getBeatGrid(trackId);
  }

  updateBeatGrid(trackId: string, beatGrid: any): void {
    this.beatGridManager.updateBeatGrid(
      trackId, 
      beatGrid, 
      this.getWaveform.bind(this), 
      this.addRegion.bind(this), 
      this.getAllRegions.bind(this), 
      this.removeRegion.bind(this)
    );
  }

  addBeatMarker(trackId: string, beatNumber: number, time: number): void {
    this.beatGridManager.addBeatMarker(trackId, beatNumber, time, this.getWaveform.bind(this), this.addRegion.bind(this));
  }

  removeBeatMarker(trackId: string, beatNumber: number): void {
    this.beatGridManager.removeBeatMarker(trackId, beatNumber, this.removeRegion.bind(this));
  }

  getBeatAtTime(trackId: string, time: number): any {
    return this.beatGridManager.getBeatAtTime(trackId, time);
  }

  snapToBeat(trackId: string, time: number, tolerance: number = 0.1): number {
    return this.beatGridManager.snapToBeat(trackId, time, tolerance);
  }

  getBeatGridStats(trackId: string): any {
    return this.beatGridManager.getBeatGridStats(trackId);
  }

  generateBeatGridFromBPM(trackId: string, bpm: number, offset: number = 0): any {
    return this.beatGridManager.generateBeatGridFromBPM(trackId, bpm, offset, this.getWaveform.bind(this));
  }

  // Spectrogram
  addSpectrogram(trackId: string, container: HTMLElement, options: any = {}): void {
    this.spectrogramManager.addSpectrogram(trackId, container, options, this.getWaveform.bind(this));
  }

  removeSpectrogram(trackId: string): void {
    this.spectrogramManager.removeSpectrogram(trackId, this.getWaveform.bind(this));
  }

  setSpectrogramVisible(trackId: string, visible: boolean, container?: HTMLElement): void {
    this.spectrogramManager.setSpectrogramVisible(trackId, visible, container, this.getWaveform.bind(this));
  }

  isSpectrogramVisible(trackId: string): boolean {
    return this.spectrogramManager.isSpectrogramVisible(trackId);
  }

  getSpectrogram(trackId: string): any {
    return this.spectrogramManager.getSpectrogram(trackId);
  }

  updateSpectrogramOptions(trackId: string, options: any): void {
    this.spectrogramManager.updateSpectrogramOptions(trackId, options, this.getWaveform.bind(this));
  }

  setSpectrogramColorMap(trackId: string, colorMap: number[][]): void {
    this.spectrogramManager.setSpectrogramColorMap(trackId, colorMap, this.getWaveform.bind(this));
  }

  applyPredefinedColorMap(trackId: string, colorMapName: string): void {
    this.spectrogramManager.applyPredefinedColorMap(trackId, colorMapName, this.getWaveform.bind(this));
  }

  exportSpectrogramAsImage(trackId: string): string | null {
    return this.spectrogramManager.exportSpectrogramAsImage(trackId);
  }

  // Envelope
  getEnvelope(trackId: string): any {
    return this.envelopeManager.getEnvelope(trackId);
  }

  addFadeIn(trackId: string, duration: number): void {
    this.envelopeManager.addFadeIn(trackId, duration);
  }

  addFadeOut(trackId: string, startTime: number, duration: number): void {
    this.envelopeManager.addFadeOut(trackId, startTime, duration, this.getWaveform.bind(this));
  }

  addCrossfade(fromTrackId: string, toTrackId: string, crossfadeTime: number, duration: number): void {
    this.envelopeManager.addCrossfade(fromTrackId, toTrackId, crossfadeTime, duration, this.getWaveform.bind(this));
  }

  addEnvelopePoint(trackId: string, time: number, volume: number): void {
    this.envelopeManager.addEnvelopePoint(trackId, time, volume);
  }

  removeEnvelopePoint(trackId: string, pointIndex: number): void {
    this.envelopeManager.removeEnvelopePoint(trackId, pointIndex);
  }

  clearEnvelope(trackId: string): void {
    this.envelopeManager.clearEnvelope(trackId);
  }

  setEnvelopeVisible(trackId: string, visible: boolean): void {
    this.envelopeManager.setEnvelopeVisible(trackId, visible);
  }

  isEnvelopeVisible(trackId: string): boolean {
    return this.envelopeManager.isEnvelopeVisible(trackId);
  }

  getEnvelopePoints(trackId: string): any[] {
    return this.envelopeManager.getEnvelopePoints(trackId);
  }

  createPresetFade(trackId: string, type: 'linear' | 'exponential' | 'logarithmic' | 'scurve', startTime: number, endTime: number, startVolume?: number, endVolume?: number): void {
    this.envelopeManager.createPresetFade(trackId, type, startTime, endTime, startVolume, endVolume);
  }

  // Recording
  async initializeRecorder(container: HTMLElement, options: any = {}): Promise<void> {
    return this.recordingManager.initializeRecorder('default', container, options);
  }

  async startRecording(): Promise<void> {
    return this.recordingManager.startRecording('default');
  }

  async stopRecording(): Promise<Blob> {
    return this.recordingManager.stopRecording('default');
  }

  async pauseRecording(): Promise<void> {
    return this.recordingManager.pauseRecording('default');
  }

  async resumeRecording(): Promise<void> {
    return this.recordingManager.resumeRecording('default');
  }

  async destroyRecorder(): Promise<void> {
    return this.recordingManager.destroyRecorder('default');
  }

  getRecordingState(): any {
    return this.recordingManager.getRecordingState('default');
  }

  getSupportedFormats(): string[] {
    return this.recordingManager.getSupportedFormats();
  }

  async checkMicrophoneAccess(): Promise<boolean> {
    return this.recordingManager.checkMicrophoneAccess();
  }

  async getAudioInputDevices(): Promise<MediaDeviceInfo[]> {
    return this.recordingManager.getAudioInputDevices();
  }

  // Performance
  trackMemoryUsage(trackId: string, type: string, size: number): void {
    this.performanceManager.trackMemoryUsage(trackId, type, size);
  }

  getMemoryStats(trackId: string): any {
    return this.performanceManager.getMemoryStats(trackId);
  }

  getTotalMemoryUsage(): number {
    return this.performanceManager.getTotalMemoryUsage();
  }

  optimizeWaveformPerformance(trackId: string): void {
    this.performanceManager.optimizeWaveformPerformance(trackId, this.getWaveform.bind(this));
  }

  optimizeAllWaveforms(): void {
    this.performanceManager.optimizeAllWaveforms(this.getAllWaveformIds.bind(this), this.getWaveform.bind(this));
  }

  getPerformanceReport(): any {
    return this.performanceManager.getPerformanceReport();
  }

  logPerformanceReport(): void {
    this.performanceManager.logPerformanceReport();
  }

  forceGarbageCollection(): void {
    this.performanceManager.forceGarbageCollection();
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Dispose all resources
   */
  dispose(): void {
    // Dispose all managers
    this.waveformManager.dispose();
    this.regionsMarkersManager.dispose();
    this.beatGridManager.dispose();
    this.spectrogramManager.dispose(this.getWaveform.bind(this));
    this.envelopeManager.dispose();
    this.recordingManager.dispose();
    this.performanceManager.dispose();

    console.log('[WaveSurferVisualization] Disposed all managers');
  }

  /**
   * Get overall memory stats
   */
  getOverallMemoryStats(): any {
    return {
  waveforms: this.getInstanceCount(),
      regions: this.regionsMarkersManager.getMemoryStats(),
      beatGrids: this.beatGridManager.getMemoryStats(),
      spectrograms: this.spectrogramManager.getMemoryStats(),
      envelopes: this.envelopeManager.getMemoryStats(),
      recordings: this.recordingManager.getMemoryStats(),
      totalMemoryUsage: this.getTotalMemoryUsage()
    };
  }
}

// Create and export singleton instance
const waveSurferVisualization = new WaveSurferVisualization();
export default waveSurferVisualization;
