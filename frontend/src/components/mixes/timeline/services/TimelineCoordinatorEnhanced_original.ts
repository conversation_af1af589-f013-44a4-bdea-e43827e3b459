import { Track } from '@/types/api/tracks';
import { Transition } from '@/types/api/transitions';
import * as Tone from 'tone';
import enhancedToneAudioEngine from './audio/EnhancedToneAudioEngine';
import waveSurferVisualization from './WaveSurferVisualization';
import { StoreApi } from 'zustand';
import { timeStretchingService } from '../../../../services/timeStretchingService';
import { beatAlignmentService, BeatAlignment } from '../../../../services/beatAlignmentService';
import { beatBoundarySnappingService, TrackPositioningResult } from '../../../../services/beatBoundarySnappingService';
import { BeatAlignmentUtils } from '../../../../utils/beatAlignment';
import { getTrackBeatGrid, BeatGrid } from '../../../../services/api/beatGrid';
import { beatAlignmentOptimizer } from '../../../../utils/performance/beatAlignmentOptimization';

/**
 * TimelineCoordinatorEnhanced
 *
 * Coordinates between the timeline, the enhanced audio engine, and the visualization
 * using the optimized approach from the multitrack-player.
 */
class TimelineCoordinatorEnhanced {
  // Tracks and transitions
  private tracks: Track[] = [];
  private transitions: Record<string, Transition> = {};

  // Playback state
  private isPlaying: boolean = false;
  private currentTime: number = 0;

  // Animation frame for updates - CONSOLIDATED LOOP
  private animationFrame: number | null = null;
  private lastUpdateTime: number = 0;
  private updateInterval: number = 16; // ms between updates (60fps target)

  // Store reference
  private store: StoreApi<any> | null = null;

  // Time update callbacks
  private timeUpdateCallbacks: ((time: number) => void)[] = [];

  // Deferred cue points and loops (for when tracks aren't ready yet)
  private deferredCuePoints: Record<string, Array<{cueId: string, time: number, label: string, color: string}>> = {};
  private deferredLoops: Record<string, Array<{loopId: string, start: number, end: number, label: string, color: string}>> = {};

  // Master BPM and beatmatching properties
  private masterBPM: number | null = null;
  private bpmTolerance: number = 6; // ±6 BPM default (as per user requirement)
  private beatmatchingEnabled: boolean = true;

  // Phase 3: Beat alignment properties
  private beatAlignmentEnabled: boolean = true;
  private beatSnapTolerance: number = 0.1; // 100ms snap tolerance
  private preferDownbeats: boolean = true;
  private trackBeatAlignments: Map<string, BeatAlignment> = new Map();
  private trackBeatGrids: Map<string, BeatGrid> = new Map();

  // Timeline loop properties (Ableton-style)
  private timelineLoopEnabled: boolean = false;
  private timelineLoopStart: number = 0;
  private timelineLoopEnd: number = 0;

  /**
   * Initialize the coordinator
   */
  async initialize(): Promise<void> {
    // Initialize the audio engine
    await enhancedToneAudioEngine.initialize();

    // Set up event listener for timeline seek events from TrackManager
    window.addEventListener('timeline-seek', this.handleTimelineSeekEvent.bind(this));

    console.log('[TimelineCoordinatorEnhanced] Initialized');
  }

  /**
   * Handle timeline seek events from TrackManager
   * @param event The custom event containing seek details
   */
  private handleTimelineSeekEvent(event: CustomEvent): void {
    const { globalTime, trackId, clickTime, preservePlayState } = event.detail;

    // Prevent duplicate seeks within 100ms and ignore seeks to same position
    const now = Date.now();
    const timeDiff = Math.abs(globalTime - this.currentTime);

    if ((this.lastSeekEventTime && now - this.lastSeekEventTime < 100) || timeDiff < 0.1) {
      return; // Ignore duplicate or micro-seeks
    }
    this.lastSeekEventTime = now;

    // Store current play state if we need to preserve it
    const wasPlaying = this.isPlaying;

    // Perform the seek
    this.seekTo(globalTime).then(() => {
      // Restore play state if requested
      if (preservePlayState && wasPlaying && !this.isPlaying) {
        this.play();
      }
    });
  }

  // Add debouncing for seek events
  private lastSeekEventTime: number = 0;

  /**
   * Set the tracks in the timeline
   * @param tracks The tracks to set
   * @param skipIfEqual Whether to skip the update if the tracks are the same (default: true)
   */
  setTracks(tracks: Track[], skipIfEqual: boolean = true): void {
    console.log(`[TimelineCoordinatorEnhanced] setTracks called with ${tracks.length} tracks`);

    // ENHANCED DEBUG: Validate tracks received by coordinator
    console.log('🔍 [TimelineCoordinatorEnhanced] Tracks validation:');
    const coordinatorValidation = {
      totalTracks: tracks.length,
      tracksWithValidIds: tracks.filter(t => !!t.id).length,
      tracksWithValidTitles: tracks.filter(t => !!t.title && t.title !== 'Unknown Title').length,
      tracksWithValidAudioUrls: tracks.filter(t => !!t.audioUrl).length,
      tracksWithColors: tracks.filter(t => !!t.color).length,
      allTracksValid: tracks.every(t => t.id && t.title && t.audioUrl)
    };
    console.log('📊 [TimelineCoordinatorEnhanced] Validation summary:', coordinatorValidation);

    // Skip if the tracks are the same (to prevent infinite loops)
    if (skipIfEqual && this.tracksAreEqual(this.tracks, tracks)) {
      console.log(`[TimelineCoordinatorEnhanced] Tracks are equal, skipping update to prevent infinite loop`);
      return;
    }

    // CRITICAL FIX: Only create shallow copies if we need to modify the objects
    // For timeline display, we don't need to modify the original track objects
    // This prevents unnecessary object recreation that triggers React re-renders
    this.tracks = tracks; // Use original references to maintain object identity

    // Calculate track start and end times (this modifies internal timing, not track objects)
    this.calculateTrackTimes();

    // Update the audio engine (TrackManager handles audio playback)
    enhancedToneAudioEngine.setTracks(this.tracks);

    // Schedule sequential playback (TrackManager coordinates with Tone.js)
    enhancedToneAudioEngine.scheduleSequentialPlayback(this.tracks);

    // SIMPLIFIED FIX: Let TrackItem components handle loading for ALL tracks
    // Both manual and Smart Mix tracks will load through normal TrackItem mounting
    // This eliminates the complex bulk loading event system that was causing race conditions
    console.log(`🔧 [TimelineCoordinatorEnhanced] SIMPLIFIED FIX: All ${tracks.length} tracks will load through normal TrackItem mounting (no bulk loading events)`);

    console.log(`✅ [TimelineCoordinatorEnhanced] Successfully set ${tracks.length} tracks for dual WaveSurfer architecture`);
  }

  /**
   * Set the tracks in the timeline without updating the store
   * This is used when the tracks are updated from the store
   * @param tracks The tracks to set
   */
  setTracksWithoutStoreUpdate(tracks: Track[]): void {
    // CRITICAL FIX: Use original references to maintain object identity
    // This prevents unnecessary object recreation that triggers React re-renders
    this.tracks = tracks;

    // Calculate track start and end times
    this.calculateTrackTimes();

    // Update the audio engine
    enhancedToneAudioEngine.setTracks(this.tracks);

    // Schedule sequential playback
    enhancedToneAudioEngine.scheduleSequentialPlayback(this.tracks);

    // DISABLED: ensureAllTracksLoaded() was interfering with normal track loading

    console.log(`[TimelineCoordinatorEnhanced] Set ${tracks.length} tracks without store update`);
  }



  /**
   * Set the transitions in the timeline
   * @param transitions The transitions to set
   * @param skipIfEqual Whether to skip the update if the transitions are the same (default: true)
   */
  setTransitions(transitions: Record<string, Transition>, skipIfEqual: boolean = true): void {
    // Skip if the transitions are the same (to prevent infinite loops)
    if (skipIfEqual && this.transitionsAreEqual(this.transitions, transitions)) {
      return;
    }

    this.transitions = { ...transitions };

    // Convert the transitions record to an array for the audio engine
    const transitionsArray = Object.entries(this.transitions).map(([key, transition]) => {
      // Parse the key to get fromTrackId and toTrackId
      const [fromTrackId, toTrackId] = key.split('-');

      // Add fade curve types for the enhanced transition system
      return {
        ...transition,
        fromTrackId,
        toTrackId,
        fadeOutCurve: (transition as any).fadeOutCurve || 'linear',
        fadeInCurve: (transition as any).fadeInCurve || 'linear'
      };
    });

    // Update the audio engine
    enhancedToneAudioEngine.setTransitions(transitionsArray);

    // Recalculate track times as transitions may affect them
    this.calculateTrackTimes();

    console.log(`[TimelineCoordinatorEnhanced] Set ${Object.keys(transitions).length} transitions`);
  }

  /**
   * Calculate track start and end times based on transitions
   * PHASE 2: Now supports overlapping tracks during transitions for DJ-style mixing
   * PHASE 3: Now supports beat-perfect transition timing
   */
  private calculateTrackTimes(): void {
    console.log(`[TimelineCoordinatorEnhanced] calculateTrackTimes() called with ${this.tracks.length} tracks`);

    // Sort tracks by their order
    const sortedTracks = [...this.tracks].sort((a, b) => {
      return ((a as any).order || 0) - ((b as any).order || 0);
    });

    console.log(`[TimelineCoordinatorEnhanced] Sorted tracks:`, sortedTracks.map(t => ({ id: t.id, order: (t as any).order, duration: t.duration })));

    // PHASE 2: Calculate start and end times with transition overlaps
    let currentTime = 0;
    sortedTracks.forEach((track, index) => {
      // PROFESSIONAL DAW FIX: Only set startTime if track doesn't have one (preserve user positioning)
      if (track.startTime === undefined || track.startTime === null) {
        (track as any).startTime = currentTime;
        console.log(`[TimelineCoordinatorEnhanced] Setting initial startTime for track ${track.id}: ${currentTime}s`);
      } else {
        console.log(`[TimelineCoordinatorEnhanced] Preserving user-set startTime for track ${track.id}: ${track.startTime}s`);
        // Update currentTime to account for user-positioned track
        currentTime = Math.max(currentTime, track.startTime);
      }

      // Calculate duration - ensure we have a valid duration
      const duration = typeof track.duration === 'number' ? track.duration :
                      typeof track.duration === 'string' ? parseFloat(track.duration) : 180;
      if (!track.duration) {
        console.warn(`[TimelineCoordinatorEnhanced] Track ${track.id} has no duration, using default 180s`);
      }

      // Check if there's a transition to the next track
      if (index < sortedTracks.length - 1) {
        const nextTrack = sortedTracks[index + 1];
        const transitionKey = `${track.id}-${nextTrack.id}`;
        const transition = this.transitions[transitionKey];

        if (transition) {
          // PHASE 2: Calculate overlapping timing for DJ-style transitions
          const transitionDurationSeconds = transition.duration || 0; // Already converted from beats to seconds

          // Current track plays its full duration
          (track as any).endTime = currentTime + duration;

          // PHASE 3: Apply beat-perfect transition alignment if enabled
          let nextTrackStartTime = (track as any).endTime - transitionDurationSeconds;

          // TODO: Implement beat snapping in future update (requires async refactoring)
          // For now, use calculated timing without beat snapping
          currentTime = nextTrackStartTime;

          console.log(`[TimelineCoordinatorEnhanced] PHASE 2: Track ${track.id} → ${nextTrack.id} overlap: ${transitionDurationSeconds.toFixed(2)}s`);
        } else {
          // If there's no transition, tracks play sequentially (no overlap)
          (track as any).endTime = currentTime + duration;
          currentTime = (track as any).endTime;
          console.log(`[TimelineCoordinatorEnhanced] Track ${track.id} plays sequentially (no transition)`);
        }
      } else {
        // Last track
        (track as any).endTime = currentTime + duration;
        currentTime = (track as any).endTime;
        console.log(`[TimelineCoordinatorEnhanced] Track ${track.id} is the last track`);
      }
    });

    // PHASE 2: Log the calculated timing for debugging
    sortedTracks.forEach((track, index) => {
      const startTime = (track as any).startTime?.toFixed(2) || '0.00';
      const endTime = (track as any).endTime?.toFixed(2) || '0.00';
      console.log(`[TimelineCoordinatorEnhanced] PHASE 2: Track ${track.id} (${track.title}) timing: ${startTime}s → ${endTime}s (duration: ${track.duration}s)`);
    });

    console.log(`[TimelineCoordinatorEnhanced] PHASE 2: Calculated track times with transition overlaps. Total duration: ${currentTime.toFixed(2)}s`);
  }

  /**
   * Check if two track arrays are equal (ignoring calculated timing properties)
   * @param a First track array
   * @param b Second track array
   * @returns True if the track arrays are equal
   */
  private tracksAreEqual(a: Track[], b: Track[]): boolean {
    if (a.length !== b.length) {
      return false;
    }

    for (let i = 0; i < a.length; i++) {
      if (a[i].id !== b[i].id) {
        return false;
      }
      if ((a[i] as any).order !== (b[i] as any).order) {
        return false;
      }
      // Compare core properties that matter for track identity
      if (a[i].duration !== b[i].duration) {
        return false;
      }
      if (a[i].audioUrl !== b[i].audioUrl) {
        return false;
      }
      // NOTE: We ignore startTime and endTime as these are calculated by the coordinator
    }

    return true;
  }

  /**
   * Check if two transition objects are equal
   * @param a First transition object
   * @param b Second transition object
   * @returns True if the transition objects are equal
   */
  private transitionsAreEqual(a: Record<string, Transition>, b: Record<string, Transition>): boolean {
    const aKeys = Object.keys(a);
    const bKeys = Object.keys(b);

    if (aKeys.length !== bKeys.length) return false;

    for (const key of aKeys) {
      if (!b[key]) return false;
      if (a[key].startPoint !== b[key].startPoint) return false;
      if (a[key].duration !== b[key].duration) return false;
    }

    return true;
  }

  /**
   * Play the timeline
   */
  play(): void {
    if (this.isPlaying) return;

    // Start playback
    enhancedToneAudioEngine.play();
    this.isPlaying = true;

    // Start the update loop
    this.startUpdateLoop();

    console.log('[TimelineCoordinatorEnhanced] Started playback');
  }

  /**
   * Pause the timeline
   */
  pause(): void {
    if (!this.isPlaying) return;

    // Pause playback
    enhancedToneAudioEngine.pause();
    this.isPlaying = false;

    // Stop the update loop
    this.stopUpdateLoop();

    console.log('[TimelineCoordinatorEnhanced] Paused playback');
  }

  /**
   * Stop the timeline
   */
  stop(): void {
    // Stop playback
    enhancedToneAudioEngine.stop();
    this.isPlaying = false;
    this.currentTime = 0;

    // Stop the update loop
    this.stopUpdateLoop();

    console.log('[TimelineCoordinatorEnhanced] Stopped playback');
  }

  /**
   * Seek to a specific time in the timeline with smart buffer management
   * @param time The time to seek to (in seconds)
   */
  async seekTo(time: number): Promise<void> {
    // Clamp the time to the valid range
    const totalDuration = this.getTotalDuration();
    const clampedTime = Math.max(0, Math.min(totalDuration, time));

    if (clampedTime !== time) {
      console.log(`[TimelineCoordinatorEnhanced] ⚠️ CLAMPED seek from ${time.toFixed(3)}s to ${clampedTime.toFixed(3)}s`);
    }

    // Seek in the audio engine with smart buffer management
    await enhancedToneAudioEngine.seekTo(clampedTime);
    this.currentTime = clampedTime;

    // Update active transitions and notify callbacks
    this.updateActiveTransitions(this.currentTime);
    this.notifyTimeUpdateCallbacks(this.currentTime);

    // PROFESSIONAL DAW FIX: NO waveform cursor updates - completely static waveforms
    console.log(`[TimelineCoordinatorEnhanced] 🔒 STATIC WAVEFORM MODE: No cursor updates - waveforms remain static`);

    console.log(`[TimelineCoordinatorEnhanced] ✅ Seek completed to ${clampedTime.toFixed(3)}s`);
  }

  /**
   * Alias for seekTo for compatibility
   * @param time The time to seek to (in seconds)
   */
  seek(time: number): void {
    this.seekTo(time);
  }

  /**
   * Get the total duration of the timeline
   * @returns The total duration (in seconds)
   */
  getTotalDuration(): number {
    if (this.tracks.length === 0) return 0;

    // Find the track with the latest end time
    const lastTrack = this.tracks.reduce((latest, track) => {
      return ((track as any).endTime || 0) > ((latest as any).endTime || 0) ? track : latest;
    }, this.tracks[0]);

    return (lastTrack as any).endTime || 0;
  }

  /**
   * Start the CONSOLIDATED update loop using requestAnimationFrame
   * This replaces the dual animation loops to improve performance
   */
  private startUpdateLoop(): void {
    // Stop any existing loop
    this.stopUpdateLoop();

    // CONSOLIDATED ANIMATION LOOP - handles both timeline and audio engine updates
    const updateTimeline = () => {
      const now = performance.now();

      // Only update if enough time has passed (throttling for 60fps)
      if (!this.lastUpdateTime || now - this.lastUpdateTime >= this.updateInterval) {
        // Get the current time from the audio engine
        if (enhancedToneAudioEngine.getPlaybackState() === 'playing') {
          // Get exact time from Tone.js for more accurate timing
          this.currentTime = Tone.getTransport().seconds;

          // CONSOLIDATED: Update both timeline and audio engine in single loop
          // 1. Update timeline state
          this.updateActiveTransitions(this.currentTime);
          this.notifyTimeUpdateCallbacks(this.currentTime);

          // 2. Update audio engine components (replaces its separate loop)
          enhancedToneAudioEngine.updateInConsolidatedLoop(this.currentTime);
        }

        this.lastUpdateTime = now;
      }

      // Continue the loop only if playing (performance optimization)
      if (this.isPlaying) {
        this.animationFrame = requestAnimationFrame(updateTimeline);
      }
    };

    // Start the consolidated update loop
    updateTimeline();

    console.log('[TimelineCoordinatorEnhanced] Started CONSOLIDATED update loop (60fps target)');
  }

  /**
   * Update active transitions based on the current time
   * @param currentTime The current time in the timeline
   */
  private updateActiveTransitions(currentTime: number): void {
    // Find active transitions
    const activeTransitions = Object.entries(this.transitions)
      .filter(([_key, transition]) => {
        const startTime = transition.startPoint || 0;
        const duration = transition.duration || 0;
        return currentTime >= startTime && currentTime < (startTime + duration);
      })
      .map(([key, transition]) => {
        // Parse the key to get fromTrackId and toTrackId
        const [fromTrackId, toTrackId] = key.split('-');

        return {
          ...transition,
          fromTrackId,
          toTrackId,
          fadeOutCurve: (transition as any).fadeOutCurve || 'linear',
          fadeInCurve: (transition as any).fadeInCurve || 'linear'
        };
      });

    // Update the audio engine with active transitions
    enhancedToneAudioEngine.updateActiveTransitions(activeTransitions, currentTime);
  }

  /**
   * Stop the update loop
   */
  private stopUpdateLoop(): void {
    if (this.animationFrame !== null) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
      console.log('[TimelineCoordinatorEnhanced] Stopped update loop');
    }
  }



  /**
   * Set the master volume
   * @param volume The volume (0-1)
   */
  setMasterVolume(volume: number): void {
    enhancedToneAudioEngine.setMasterVolume(volume * 100);
  }

  /**
   * Get the master volume
   * @returns The master volume (0-1)
   */
  getMasterVolume(): number {
    return enhancedToneAudioEngine.getMasterVolume();
  }

  /**
   * Set whether the master output is muted
   * @param muted Whether the master output is muted
   */
  setMasterMuted(muted: boolean): void {
    enhancedToneAudioEngine.setMasterVolume(muted ? 0 : 100);
  }

  /**
   * Get whether the master output is muted
   * @returns Whether the master output is muted
   */
  isMasterMuted(): boolean {
    return enhancedToneAudioEngine.getMasterVolume() === 0;
  }



  /**
   * Set the store for the coordinator
   * @param store The store to set
   */
  setStore(store: StoreApi<any>): void {
    this.store = store;
    console.log('[TimelineCoordinatorEnhanced] Store set');
  }

  // NOTE: Duplicate method removed - using the one at line 134

  /**
   * Register a callback for time updates
   * @param callback The callback to register
   */
  onTimeUpdate(callback: (time: number) => void): void {
    this.timeUpdateCallbacks.push(callback);
  }

  /**
   * Unregister a callback for time updates
   * @param callback The callback to unregister
   */
  offTimeUpdate(callback: (time: number) => void): void {
    this.timeUpdateCallbacks = this.timeUpdateCallbacks.filter(cb => cb !== callback);
  }

  /**
   * Notify all time update callbacks
   * @param time The current time
   */
  private notifyTimeUpdateCallbacks(time: number): void {
    this.timeUpdateCallbacks.forEach(callback => callback(time));
  }

  /**
   * Get the current time in the timeline
   * @returns The current time (in seconds)
   */
  getCurrentTime(): number {
    return this.currentTime;
  }



  /**
   * Get all tracks in the timeline
   * @returns The tracks
   */
  getTracks(): Track[] {
    return [...this.tracks];
  }

  /**
   * Add a track to the timeline with automatic beatmatching
   * @param track The track to add
   * @param updateStore Whether to update the store (default: true)
   * @param skipBeatmatching Whether to skip automatic beatmatching (default: false)
   * @returns Promise that resolves when the track is added and beatmatched
   */
  async addTrack(track: Track, updateStore: boolean = true, skipBeatmatching: boolean = false): Promise<void> {
    console.log(`[TimelineCoordinatorEnhanced] Adding track ${track.id}: ${track.title} (${track.bpm} BPM)`);

    // Step 1: Handle master BPM setup
    if (!this.masterBPM) {
      // First track or user hasn't set master BPM - use this track's BPM as master
      if (track.bpm && track.bpm > 0) {
        this.setMasterBPM(track.bpm);
        console.log(`[TimelineCoordinatorEnhanced] Set master BPM to ${track.bpm} from first track`);
      } else {
        console.warn(`[TimelineCoordinatorEnhanced] Track ${track.id} has invalid BPM (${track.bpm}), cannot set master BPM`);
        // Add track without beatmatching
        this.addTrackWithoutBeatmatching(track, updateStore);
        return;
      }
    }

    // Step 2: Check if beatmatching should be skipped
    if (skipBeatmatching) {
      console.log(`[TimelineCoordinatorEnhanced] Skipping beatmatching for track ${track.id} (requested)`);
      this.addTrackWithoutBeatmatching(track, updateStore);
      return;
    }

    // Step 3: Validate BPM compatibility if beatmatching is enabled
    if (this.beatmatchingEnabled && track.bpm && track.bpm > 0) {
      if (!this.isWithinAcceptableRange(track.bpm)) {
        const bpmDifference = Math.abs(track.bpm - this.masterBPM!);
        console.warn(`[TimelineCoordinatorEnhanced] Track ${track.id} BPM (${track.bpm}) is ${bpmDifference} BPM away from master BPM (${this.masterBPM}), exceeds tolerance (±${this.bpmTolerance})`);

        // Show user warning for incompatible track
        this.showIncompatibleTrackWarning(track, bpmDifference);

        // Add without beatmatching
        this.addTrackWithoutBeatmatching(track, updateStore);
        return;
      }

      // Step 3: Apply beatmatching
      try {
        console.log(`[TimelineCoordinatorEnhanced] 🔄 Attempting beatmatching for track ${track.id}...`);
        await this.applyBeatmatching(track);
        console.log(`[TimelineCoordinatorEnhanced] ✅ Beatmatching completed for track ${track.id}`);

        // Verify beatmatching was applied
        if (!track.stretchRatio || track.stretchRatio === 1.0) {
          console.warn(`[TimelineCoordinatorEnhanced] ⚠️ Track ${track.id} beatmatching may have failed - stretch ratio is ${track.stretchRatio}`);
        }
      } catch (error) {
        console.error(`[TimelineCoordinatorEnhanced] ❌ Beatmatching failed for track ${track.id}:`, error);

        // Apply fallback beatmatching
        console.log(`[TimelineCoordinatorEnhanced] 🔄 Applying fallback beatmatching for track ${track.id}`);
        this.applyBasicBeatmatching(track);
      }
    }

    // Step 4: Calculate position and add to timeline
    this.addTrackToTimeline(track, updateStore);

    console.log(`[TimelineCoordinatorEnhanced] Successfully added track ${track.id} with beatmatching`);
  }

  /**
   * Apply beatmatching to a track using dual-mode time stretching
   * @param track The track to beatmatch
   */
  private async applyBeatmatching(track: Track): Promise<void> {
    if (!this.masterBPM || !track.bpm) return;

    console.log(`[TimelineCoordinatorEnhanced] 🎵 Starting dual-mode beatmatching for track ${track.id}: ${track.bpm} BPM → ${this.masterBPM} BPM`);

    try {
      // Use the time stretching service to apply beatmatching
      const result = await timeStretchingService.applyTimeStretching(track, this.masterBPM);

      console.log(`[TimelineCoordinatorEnhanced] 🎵 Beatmatching completed using ${result.mode} mode:`, result);

      // Handle different modes
      if (result.mode === 'backend') {
        // Backend mode: high-quality time stretching
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Using backend time stretching with quality score: ${result.qualityMetrics?.quality_score}`);

        // The track now has stretchedAudioUrl and should use that for playback
        // The audio engine will need to handle loading the stretched audio
        if (result.stretchedAudioUrl) {
          track.stretchedAudioUrl = result.stretchedAudioUrl;
          console.log(`[TimelineCoordinatorEnhanced] 🎵 Track ${track.id} will use stretched audio: ${result.stretchedAudioUrl}`);
        }
      } else {
        // Real-time mode: playback rate adjustment
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Using real-time playback rate adjustment: ${result.stretchRatio?.toFixed(3)}`);

        // The track properties are already set by the service
        // Apply playback rate when track is loaded
      }

      // Show quality feedback to user
      this.showBeatmatchingFeedback(track, result);

    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] ❌ Beatmatching failed for track ${track.id}:`, error);

      // Fallback to basic real-time stretching
      console.log(`[TimelineCoordinatorEnhanced] 🔄 Falling back to basic real-time stretching`);
      this.applyBasicBeatmatching(track);
    }
  }

  /**
   * Apply basic beatmatching as fallback (original implementation)
   * @param track The track to beatmatch
   */
  private applyBasicBeatmatching(track: Track): void {
    if (!this.masterBPM || !track.bpm) return;

    // Calculate stretch ratio
    const stretchRatio = this.calculateStretchRatio(track.bpm);

    console.log(`[TimelineCoordinatorEnhanced] Basic beatmatching track ${track.id}: ${track.bpm} BPM → ${this.masterBPM} BPM (ratio: ${stretchRatio.toFixed(3)})`);

    // Store original BPM and set effective BPM
    track.originalBPM = track.bpm;
    track.effectiveBPM = this.masterBPM;
    track.stretchRatio = stretchRatio;
    track.stretchingMode = 'realtime';

    // Apply real-time playback rate adjustment
    // Note: This will be applied when the track is loaded into the audio engine
    console.log(`[TimelineCoordinatorEnhanced] Track ${track.id} will use playback rate ${stretchRatio.toFixed(3)} for tempo matching`);
  }

  /**
   * Show beatmatching feedback to the user
   * @param track The track that was beatmatched
   * @param result The beatmatching result
   */
  private showBeatmatchingFeedback(track: Track, result: any): void {
    const mode = result.mode;
    const originalBpm = track.originalBPM || track.bpm;
    const targetBpm = track.effectiveBPM || this.masterBPM;

    // Create feedback message
    let message = `🎵 Track "${track.title}" beatmatched: ${originalBpm} → ${targetBpm} BPM`;

    if (mode === 'backend') {
      const quality = result.qualityMetrics?.quality_rating || 'unknown';
      message += ` (High-quality processing: ${quality})`;
    } else {
      message += ` (Real-time adjustment)`;
    }

    // Show toast notification (if available)
    if (typeof window !== 'undefined' && (window as any).showToast) {
      (window as any).showToast(message, 'success');
    } else {
      console.log(`[TimelineCoordinatorEnhanced] ${message}`);
    }

    // Update UI indicators (if store is available)
    if (this.store) {
      try {
        this.store.setState((state: any) => ({
          ...state,
          beatmatchingStatus: {
            ...state.beatmatchingStatus,
            [track.id]: {
              mode,
              originalBpm,
              targetBpm,
              quality: result.qualityMetrics?.quality_rating || 'good',
              timestamp: Date.now()
            }
          }
        }));
      } catch (error) {
        console.warn('[TimelineCoordinatorEnhanced] Failed to update beatmatching status in store:', error);
      }
    }
  }

  /**
   * Add a track to the timeline without beatmatching
   * @param track The track to add
   * @param updateStore Whether to update the store
   */
  private addTrackWithoutBeatmatching(track: Track, updateStore: boolean): void {
    console.log(`[TimelineCoordinatorEnhanced] Adding track ${track.id} without beatmatching`);

    // Preserve original BPM
    track.originalBPM = track.bpm;
    track.effectiveBPM = track.bpm;
    track.stretchRatio = 1.0;

    // Add to timeline - let timeline page handle loading with proper containers
    this.addTrackToTimeline(track, updateStore);
  }

  /**
   * Add a track to the timeline (final step)
   * @param track The track to add
   * @param updateStore Whether to update the store
   */
  private addTrackToTimeline(track: Track, updateStore: boolean): void {
    // Set track order
    (track as any).order = this.tracks.length;

    // Add to tracks array
    const newTracks = [...this.tracks, track];

    // Update timeline
    this.setTracks(newTracks, false);

    // Update store if requested
    if (updateStore && this.store) {
      this.store.getState().setTracks(newTracks);
    }

    console.log(`[TimelineCoordinatorEnhanced] Added track ${track.id} at position ${(track as any).order}`);
  }

  /**
   * Get all transitions in the timeline
   * @returns The transitions
   */
  getTransitions(): Record<string, Transition> {
    return { ...this.transitions };
  }

  /**
   * Get a track by ID
   * @param trackId The ID of the track
   * @returns The track, or undefined if not found
   */
  getTrack(trackId: string): Track | undefined {
    return this.tracks.find(track => track.id.toString() === trackId);
  }

  /**
   * Set the BPM for a track
   * @param trackId The ID of the track
   * @param bpm The BPM to set
   */
  setTrackBPM(trackId: string, bpm: number): void {
    const track = this.getTrack(trackId);
    if (track) {
      track.bpm = bpm;
      console.log(`[TimelineCoordinatorEnhanced] Set BPM for track ${trackId} to ${bpm}`);
    }
  }

  /**
   * Get the BPM for a track
   * @param trackId The ID of the track
   * @returns The BPM, or undefined if the track is not found
   */
  getTrackBPM(trackId: string): number | undefined {
    const track = this.getTrack(trackId);
    return track?.bpm;
  }

  /**
   * Set the beat grid offset for a track
   * @param trackId The ID of the track
   * @param offset The offset to set (in seconds)
   */
  setBeatGridOffset(trackId: string, offset: number): void {
    const track = this.getTrack(trackId);
    if (track) {
      (track as any).beatGridOffset = offset;
      console.log(`[TimelineCoordinatorEnhanced] Set beat grid offset for track ${trackId} to ${offset}`);
    }
  }

  /**
   * Get the beat grid offset for a track
   * @param trackId The ID of the track
   * @returns The offset (in seconds), or undefined if the track is not found
   */
  getBeatGridOffset(trackId: string): number | undefined {
    const track = this.getTrack(trackId);
    return (track as any)?.beatGridOffset;
  }

  /**
   * Seek to a specific beat in a track
   * @param trackId The ID of the track
   * @param beat The beat to seek to
   */
  seekToBeat(trackId: string, beat: number): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    const bpm = track.bpm || 120;
    const offset = (track as any)?.beatGridOffset || 0;
    const beatDuration = 60 / bpm; // Duration of one beat in seconds
    const time = (track.startTime || 0) + (beat * beatDuration) + offset;

    this.seekTo(time);
    console.log(`[TimelineCoordinatorEnhanced] Seeked to beat ${beat} in track ${trackId} (${time}s)`);
  }

  /**
   * Add a segment to a track
   * @param trackId The ID of the track
   * @param segment The segment to add
   */
  addSegment(trackId: string, segment: any): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    if (!(track as any).segments) {
      (track as any).segments = [];
    }

    (track as any).segments.push(segment);

    // Add the segment to WaveSurfer visualization (same as regular coordinator)
    try {
      console.log(`[TimelineCoordinatorEnhanced] Adding segment ${segment.id} to WaveSurfer for track ${trackId}`);

      // Check if the segment already exists and remove it first to prevent duplication
      const existingRegion = waveSurferVisualization.getRegion(trackId, segment.id);
      if (existingRegion) {
        console.log(`[TimelineCoordinatorEnhanced] Segment ${segment.id} already exists for track ${trackId}, removing it first`);
        waveSurferVisualization.removeRegion(trackId, segment.id);
      }

      // Add the segment with a small delay to ensure any cleanup is complete
      setTimeout(() => {
        waveSurferVisualization.addRegion(trackId, segment.id, {
          start: segment.startTime,
          end: segment.endTime,
          color: segment.color || 'rgba(0, 128, 255, 0.2)',
          label: segment.label || '',
          drag: true,
          resize: true,
          attributes: {
            type: 'segment',
            segmentType: segment.type
          }
        });
      }, 50);
    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] Error adding segment ${segment.id} to track ${trackId}:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Added segment ${segment.id} to track ${trackId}`);
  }

  /**
   * Get all segments for a track
   * @param trackId The ID of the track
   * @returns The segments, or an empty array if the track is not found
   */
  getSegments(trackId: string): any[] {
    const track = this.getTrack(trackId);
    return (track as any)?.segments || [];
  }

  /**
   * Remove a segment from a track
   * @param trackId The ID of the track
   * @param segmentId The ID of the segment to remove
   */
  removeSegment(trackId: string, segmentId: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).segments) return;

    (track as any).segments = (track as any).segments.filter((s: any) => s.id !== segmentId);

    // Remove the segment from WaveSurfer visualization
    try {
      waveSurferVisualization.removeRegion(trackId, segmentId);
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error removing segment ${segmentId} from WaveSurfer:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Removed segment ${segmentId} from track ${trackId}`);
  }

  /**
   * Update a segment in a track
   * @param trackId The ID of the track
   * @param segmentId The ID of the segment to update
   * @param start The new start time
   * @param end The new end time
   * @param label The new label
   * @param color The new color
   */
  updateSegment(trackId: string, segmentId: string, start: number, end: number, label: string, color?: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).segments) return;

    const segment = (track as any).segments.find((s: any) => s.id === segmentId);
    if (!segment) return;

    // Update segment properties
    segment.startTime = start;
    segment.endTime = end;
    segment.label = label;
    if (color) segment.color = color;

    // Update the segment in WaveSurfer visualization
    try {
      waveSurferVisualization.updateRegion(trackId, segmentId, {
        start,
        end,
        color: color || 'rgba(0, 128, 255, 0.2)',
        label,
      });
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error updating segment ${segmentId} in WaveSurfer:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Updated segment ${segmentId} in track ${trackId}`);
  }

  /**
   * Clear all segments from a track
   * @param trackId The ID of the track
   */
  clearAllSegments(trackId: string): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    // Get all segment IDs before clearing
    const segments = (track as any).segments || [];

    // Clear segments from memory
    (track as any).segments = [];

    // Remove all segments from WaveSurfer visualization
    try {
      segments.forEach((segment: any) => {
        waveSurferVisualization.removeRegion(trackId, segment.id);
      });
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error clearing segments from WaveSurfer:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Cleared all segments from track ${trackId}`);
  }

  /**
   * Seek to a segment in a track
   * @param trackId The ID of the track
   * @param segmentId The ID of the segment to seek to
   */
  seekToSegment(trackId: string, segmentId: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).segments) return;

    const segment = (track as any).segments.find((s: any) => s.id === segmentId);
    if (!segment) return;

    const time = (track.startTime || 0) + segment.startTime;
    this.seekTo(time);
    console.log(`[TimelineCoordinatorEnhanced] Seeked to segment ${segmentId} in track ${trackId} (${time}s)`);
  }

  // ===== CUE POINTS METHODS =====

  /**
   * Add a cue point to a track
   * @param trackId The ID of the track
   * @param cueId The ID of the cue point
   * @param time The time of the cue point (in seconds)
   * @param label The label for the cue point
   * @param color The color of the cue point
   */
  addCuePoint(trackId: string, cueId: string, time: number, label: string, color: string = 'rgba(255, 0, 0, 0.5)'): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize cuePoints array if it doesn't exist
    if (!(track as any).cuePoints) {
      (track as any).cuePoints = [];
    }

    // Add cue point to track data
    const cuePoint = {
      id: cueId,
      time,
      label,
      color
    };
    (track as any).cuePoints.push(cuePoint);

    // Add the cue point to WaveSurfer visualization using addMarker (same as segments pattern)
    try {
      console.log(`[TimelineCoordinatorEnhanced] Adding cue point ${cueId} to WaveSurfer for track ${trackId}`);

      // Check if track is ready before adding marker (same as segments)
      if (!enhancedToneAudioEngine.hasTrack(trackId)) {
        console.warn(`[TimelineCoordinatorEnhanced] Track ${trackId} not loaded, deferring cue point ${cueId} addition`);
        // Store the cue point for later addition when track is ready
        this.deferredCuePoints = this.deferredCuePoints || {};
        if (!this.deferredCuePoints[trackId]) {
          this.deferredCuePoints[trackId] = [];
        }
        this.deferredCuePoints[trackId].push({ cueId, time, label, color });
        return;
      }

      // Check if the cue point already exists and remove it first to prevent duplication
      // Note: getMarker method doesn't exist on WaveSurferVisualization, so we'll just try to remove it
      try {
        console.log(`[TimelineCoordinatorEnhanced] Attempting to remove any existing cue point ${cueId} for track ${trackId}`);
        waveSurferVisualization.removeMarker(trackId, cueId);
      } catch (error) {
        // Ignore errors if marker doesn't exist
        console.log(`[TimelineCoordinatorEnhanced] No existing marker to remove for ${cueId}`);
      }

      // Add the cue point with a small delay to ensure any cleanup is complete (same as segments)
      setTimeout(() => {
        waveSurferVisualization.addMarker(trackId, cueId, time, {
          color,
          label,
          drag: true,
        });
      }, 50);
    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] Error adding cue point ${cueId} to track ${trackId}:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Added cue point ${cueId} to track ${trackId} at ${time}s`);
  }

  /**
   * Remove a cue point from a track
   * @param trackId The ID of the track
   * @param cueId The ID of the cue point
   */
  removeCuePoint(trackId: string, cueId: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).cuePoints) return;

    // Remove from track data
    (track as any).cuePoints = (track as any).cuePoints.filter((c: any) => c.id !== cueId);

    // Remove the cue point from WaveSurfer visualization
    try {
      waveSurferVisualization.removeMarker(trackId, cueId);
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error removing cue point ${cueId} from WaveSurfer:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Removed cue point ${cueId} from track ${trackId}`);
  }

  // ===== LOOPS METHODS =====

  /**
   * Add a loop region to a track
   * @param trackId The ID of the track
   * @param loopId The ID of the loop
   * @param start The start time of the loop (in seconds)
   * @param end The end time of the loop (in seconds)
   * @param label The label for the loop
   * @param color The color for the loop
   */
  addLoop(trackId: string, loopId: string, start: number, end: number, label: string, color?: string): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize loops array if it doesn't exist
    if (!(track as any).loops) {
      (track as any).loops = [];
    }

    // Add loop to track data
    const loop = {
      id: loopId,
      start,
      end,
      label,
      color: color || 'rgba(255, 165, 0, 0.3)',
      active: false
    };
    (track as any).loops.push(loop);

    // Add the loop to WaveSurfer visualization using addRegion (same as segments pattern)
    try {
      console.log(`[TimelineCoordinatorEnhanced] Adding loop ${loopId} to WaveSurfer for track ${trackId}`);

      // Check if track is ready before adding region (same as segments)
      if (!enhancedToneAudioEngine.hasTrack(trackId)) {
        console.warn(`[TimelineCoordinatorEnhanced] Track ${trackId} not loaded, deferring loop ${loopId} addition`);
        // Store the loop for later addition when track is ready
        this.deferredLoops = this.deferredLoops || {};
        if (!this.deferredLoops[trackId]) {
          this.deferredLoops[trackId] = [];
        }
        this.deferredLoops[trackId].push({ loopId, start, end, label, color: color || 'rgba(255, 165, 0, 0.3)' });
        return;
      }

      // Check if the loop already exists and remove it first to prevent duplication
      const existingRegion = waveSurferVisualization.getRegion(trackId, loopId);
      if (existingRegion) {
        console.log(`[TimelineCoordinatorEnhanced] Loop ${loopId} already exists for track ${trackId}, removing it first`);
        waveSurferVisualization.removeRegion(trackId, loopId);
      }

      // Add the loop with a small delay to ensure any cleanup is complete (same as segments)
      setTimeout(() => {
        waveSurferVisualization.addRegion(trackId, loopId, {
          start,
          end,
          color: color || 'rgba(255, 165, 0, 0.3)',
          label,
          drag: false,
          resize: true,
          attributes: {
            type: 'loop'
          }
        });
      }, 50);
    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] Error adding loop ${loopId} to track ${trackId}:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Added loop ${loopId} to track ${trackId} from ${start}s to ${end}s`);
  }

  /**
   * Remove a loop from a track
   * @param trackId The ID of the track
   * @param loopId The ID of the loop
   */
  removeLoop(trackId: string, loopId: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).loops) return;

    // Remove from track data
    (track as any).loops = (track as any).loops.filter((l: any) => l.id !== loopId);

    // Remove the loop from WaveSurfer visualization
    try {
      waveSurferVisualization.removeRegion(trackId, loopId);
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error removing loop ${loopId} from WaveSurfer:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Removed loop ${loopId} from track ${trackId}`);
  }

  /**
   * Activate a loop for playback
   * @param trackId The ID of the track
   * @param loopId The ID of the loop
   */
  activateLoop(trackId: string, loopId: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).loops) return;

    // Find the loop in track data
    const loop = (track as any).loops.find((l: any) => l.id === loopId);
    if (!loop) return;

    // Mark loop as active
    loop.active = true;

    // Get the region from WaveSurfer
    const region = waveSurferVisualization.getRegion(trackId, loopId);
    if (!region) return;

    // Create a Tone.js Loop using the enhanced audio engine
    // Note: We'll need to add this method to the enhanced audio engine
    // For now, we'll update the visual appearance
    try {
      // Update the region appearance to show it's active
      waveSurferVisualization.updateRegion(trackId, loopId, {
        color: 'rgba(255, 165, 0, 0.6)',
      });
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error activating loop ${loopId} visually:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Activated loop ${loopId} for track ${trackId}`);
  }

  /**
   * Deactivate a loop
   * @param trackId The ID of the track
   * @param loopId The ID of the loop
   */
  deactivateLoop(trackId: string, loopId: string): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).loops) return;

    // Find the loop in track data
    const loop = (track as any).loops.find((l: any) => l.id === loopId);
    if (!loop) return;

    // Mark loop as inactive
    loop.active = false;

    // Remove the Tone.js Loop using the enhanced audio engine
    // Note: We'll need to add this method to the enhanced audio engine
    // For now, we'll update the visual appearance
    try {
      // Update the region appearance to show it's inactive
      waveSurferVisualization.updateRegion(trackId, loopId, {
        color: 'rgba(255, 165, 0, 0.3)',
      });
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error deactivating loop ${loopId} visually:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Deactivated loop ${loopId} for track ${trackId}`);
  }

  // ===== BEAT GRID OPERATIONS =====

  /**
   * Add a beat grid marker to a track
   * @param trackId The ID of the track
   * @param beat The beat number
   * @param time The time of the beat (in seconds)
   */
  addBeatGridMarker(trackId: string, beat: number, time: number): void {
    const track = this.getTrack(trackId);
    if (!track) return;

    // Initialize beatGrid array if it doesn't exist
    if (!(track as any).beatGrid) {
      (track as any).beatGrid = [];
    }

    // Add beat marker to track data
    const beatMarker = {
      beat,
      time,
      id: `beat-${beat}`
    };
    (track as any).beatGrid.push(beatMarker);

    // Add the beat marker to WaveSurfer visualization using addMarker (same as segments pattern)
    try {
      console.log(`[TimelineCoordinatorEnhanced] Adding beat grid marker ${beat} to WaveSurfer for track ${trackId}`);

      // Check if track is ready before adding marker (same as segments)
      if (!enhancedToneAudioEngine.hasTrack(trackId)) {
        console.warn(`[TimelineCoordinatorEnhanced] Track ${trackId} not loaded, skipping beat grid marker ${beat} addition`);
        return;
      }

      // Add the beat marker with a small delay to ensure any cleanup is complete (same as segments)
      setTimeout(() => {
        waveSurferVisualization.addMarker(trackId, `beat-${beat}`, time, {
          color: 'rgba(0, 255, 0, 0.5)',
          label: `${beat}`,
          drag: false,
        });
      }, 50);
    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] Error adding beat grid marker ${beat} to track ${trackId}:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Added beat grid marker ${beat} to track ${trackId} at ${time}s`);
  }

  /**
   * Remove a beat grid marker from a track
   * @param trackId The ID of the track
   * @param beat The beat number
   */
  removeBeatGridMarker(trackId: string, beat: number): void {
    const track = this.getTrack(trackId);
    if (!track || !(track as any).beatGrid) return;

    // Remove from track data
    (track as any).beatGrid = (track as any).beatGrid.filter((b: any) => b.beat !== beat);

    // Remove the beat marker from WaveSurfer visualization
    try {
      waveSurferVisualization.removeMarker(trackId, `beat-${beat}`);
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error removing beat grid marker ${beat} from WaveSurfer:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Removed beat grid marker ${beat} from track ${trackId}`);
  }

  // ===== AUDIO CONTROLS =====

  /**
   * Set the volume of a track
   * @param trackId The ID of the track
   * @param volume The volume (0-100)
   */
  setTrackVolume(trackId: string, volume: number): void {
    enhancedToneAudioEngine.setTrackVolume(trackId, volume);
    console.log(`[TimelineCoordinatorEnhanced] Set track ${trackId} volume to ${volume}%`);
  }

  /**
   * Set the playback rate of a track
   * @param trackId The ID of the track
   * @param rate The playback rate (0.5-2.0)
   */
  setTrackPlaybackRate(trackId: string, rate: number): void {
    // Delegate to the enhanced audio engine
    enhancedToneAudioEngine.setTrackPlaybackRate(trackId, rate);
    console.log(`[TimelineCoordinatorEnhanced] Set track ${trackId} playback rate to ${rate}`);
  }

  /**
   * Set the BPM of the timeline
   * @param bpm The BPM
   */
  setBpm(bpm: number): void {
    // Set the BPM directly in Tone.js transport
    Tone.getTransport().bpm.value = bpm;
    console.log(`[TimelineCoordinatorEnhanced] Set BPM to ${bpm}`);
  }

  /**
   * Ramp the BPM of the timeline over time
   * @param targetBpm The target BPM
   * @param duration The duration of the ramp (in seconds)
   */
  rampBpm(targetBpm: number, duration: number): void {
    // Ramp the BPM directly in Tone.js transport
    Tone.getTransport().bpm.rampTo(targetBpm, duration);
    console.log(`[TimelineCoordinatorEnhanced] Ramping BPM to ${targetBpm} over ${duration}s`);
  }

  // ===== MASTER BPM MANAGEMENT =====

  /**
   * Set the master BPM for the mix (Option A: User-defined master BPM)
   * @param bpm The master BPM to set
   */
  setMasterBPM(bpm: number): void {
    // Validate BPM range (typical DJ range)
    const clampedBPM = Math.max(60, Math.min(200, bpm));
    if (bpm !== clampedBPM) {
      console.warn(`[TimelineCoordinatorEnhanced] Master BPM ${bpm} clamped to ${clampedBPM}`);
    }

    const oldMasterBPM = this.masterBPM;
    this.masterBPM = clampedBPM;

    // Update Tone.js transport BPM
    this.setBpm(clampedBPM);

    console.log(`[TimelineCoordinatorEnhanced] Set master BPM to ${clampedBPM}`);

    // Apply new playback rates to all loaded tracks if master BPM changed
    if (oldMasterBPM !== clampedBPM && this.beatmatchingEnabled) {
      this.updateAllTrackPlaybackRates();
    }

    // Phase 3: Apply beat alignment to all tracks when master BPM changes
    if (oldMasterBPM !== clampedBPM && this.beatAlignmentEnabled) {
      this.applyBeatAlignmentToAllTracks(clampedBPM).catch(error => {
        console.warn('[TimelineCoordinatorEnhanced] Error applying beat alignment to tracks:', error);
      });
    }
  }

  /**
   * Get the current master BPM
   * @returns The master BPM, or null if not set
   */
  getMasterBPM(): number | null {
    return this.masterBPM;
  }

  /**
   * Calculate the stretch ratio needed for a track to match the master BPM
   * @param trackBPM The original BPM of the track
   * @returns The stretch ratio (1.0 = no change, >1.0 = faster, <1.0 = slower)
   */
  calculateStretchRatio(trackBPM: number): number {
    if (!this.masterBPM) {
      console.warn('[TimelineCoordinatorEnhanced] Cannot calculate stretch ratio: master BPM not set');
      return 1.0;
    }

    return this.masterBPM / trackBPM;
  }

  /**
   * Check if a track's BPM is within acceptable range of the master BPM
   * @param trackBPM The BPM of the track to check
   * @returns True if the track is compatible, false otherwise
   */
  isWithinAcceptableRange(trackBPM: number): boolean {
    if (!this.masterBPM) {
      return true; // If no master BPM set, all tracks are acceptable
    }

    const bpmDifference = Math.abs(trackBPM - this.masterBPM);
    return bpmDifference <= this.bpmTolerance;
  }

  /**
   * Set the BPM tolerance for beatmatching
   * @param tolerance The tolerance in BPM (±tolerance)
   */
  setBPMTolerance(tolerance: number): void {
    this.bpmTolerance = Math.max(1, Math.min(20, tolerance)); // Clamp between 1-20 BPM
    console.log(`[TimelineCoordinatorEnhanced] Set BPM tolerance to ±${this.bpmTolerance} BPM`);
  }

  /**
   * Get the current BPM tolerance
   * @returns The BPM tolerance
   */
  getBPMTolerance(): number {
    return this.bpmTolerance;
  }

  /**
   * Enable or disable automatic beatmatching
   * @param enabled Whether beatmatching should be enabled
   */
  setBeatmatchingEnabled(enabled: boolean): void {
    this.beatmatchingEnabled = enabled;
    console.log(`[TimelineCoordinatorEnhanced] Beatmatching ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Check if beatmatching is enabled
   * @returns True if beatmatching is enabled
   */
  isBeatmatchingEnabled(): boolean {
    return this.beatmatchingEnabled;
  }

  /**
   * Update playback rates for all loaded tracks based on current master BPM
   * This is called when master BPM changes to apply new tempo to existing tracks
   */
  updateAllTrackPlaybackRates(): void {
    if (!this.masterBPM) {
      console.warn('[TimelineCoordinatorEnhanced] Cannot update track playback rates: no master BPM set');
      return;
    }

    console.log(`[TimelineCoordinatorEnhanced] 🎵 Updating all track playback rates for new master BPM: ${this.masterBPM}`);

    this.tracks.forEach(track => {
      if (!track.bpm || track.bpm <= 0) {
        console.warn(`[TimelineCoordinatorEnhanced] Skipping track ${track.id} - invalid BPM: ${track.bpm}`);
        return;
      }

      // Check if track is compatible with new master BPM
      if (!this.isWithinAcceptableRange(track.bpm)) {
        console.warn(`[TimelineCoordinatorEnhanced] Track ${track.id} (${track.bpm} BPM) is incompatible with new master BPM (${this.masterBPM})`);
        // Reset to no beatmatching
        track.stretchRatio = 1.0;
        track.effectiveBPM = track.originalBPM || track.bpm;

        // Apply normal playback rate if track is loaded
        if (this.hasWaveform(track.id.toString())) {
          this.setTrackPlaybackRate(track.id.toString(), 1.0);
        }
        return;
      }

      // Calculate new stretch ratio
      const newStretchRatio = this.calculateStretchRatio(track.bpm);

      // Update track properties
      track.originalBPM = track.originalBPM || track.bpm;
      track.effectiveBPM = this.masterBPM;
      track.stretchRatio = newStretchRatio;

      console.log(`[TimelineCoordinatorEnhanced] 🎵 Track ${track.id}: ${track.bpm} BPM → ${this.masterBPM} BPM (ratio: ${newStretchRatio.toFixed(3)})`);

      // Apply new playback rate if track is loaded
      if (this.hasWaveform(track.id.toString())) {
        this.setTrackPlaybackRate(track.id.toString(), newStretchRatio);
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Applied new playback rate ${newStretchRatio.toFixed(3)} to loaded track ${track.id}`);
      } else {
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Track ${track.id} not loaded yet - playback rate will be applied when loaded`);
      }
    });

    console.log(`[TimelineCoordinatorEnhanced] 🎵 Finished updating playback rates for ${this.tracks.length} tracks`);
  }

  /**
   * Show warning for incompatible track
   * @param track The incompatible track
   * @param bpmDifference The BPM difference from master
   */
  private showIncompatibleTrackWarning(track: Track, bpmDifference: number): void {
    // Create a custom event to show toast notification
    const warningEvent = new CustomEvent('beatmatchingWarning', {
      detail: {
        type: 'incompatible_track',
        track: {
          id: track.id,
          title: track.title,
          artist: track.artist,
          bpm: track.bpm
        },
        masterBPM: this.masterBPM,
        bpmDifference,
        tolerance: this.bpmTolerance,
        message: `Track "${track.title}" (${track.bpm} BPM) is ${bpmDifference} BPM away from Master BPM (${this.masterBPM}). Exceeds ±${this.bpmTolerance} BPM tolerance.`,
        suggestion: `Try adjusting BPM tolerance to ±${Math.ceil(bpmDifference)} BPM in settings, or choose a track closer to ${this.masterBPM} BPM.`
      }
    });

    // Dispatch the event for UI components to handle
    window.dispatchEvent(warningEvent);

    console.warn(`[TimelineCoordinatorEnhanced] 🚨 INCOMPATIBLE TRACK: ${track.title} (${track.bpm} BPM) vs Master BPM (${this.masterBPM}) - difference: ${bpmDifference} BPM, tolerance: ±${this.bpmTolerance} BPM`);
  }

  /**
   * HELPER: Apply beatmatching to all existing tracks (for debugging/manual activation)
   * Call this from console to enable beatmatching for current tracks
   */
  applyBeatmatchingToAllTracks(): void {
    console.log('[TimelineCoordinatorEnhanced] 🎵 MANUAL: Applying beatmatching to all existing tracks');

    if (!this.masterBPM) {
      // Set master BPM from first track
      const firstTrack = this.tracks.find(t => t.bpm && t.bpm > 0);
      if (firstTrack) {
        this.setMasterBPM(firstTrack.bpm);
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Set master BPM to ${firstTrack.bpm} from first track`);
      } else {
        console.error('[TimelineCoordinatorEnhanced] No tracks with valid BPM found');
        return;
      }
    }

    // Enable beatmatching if not enabled
    if (!this.beatmatchingEnabled) {
      this.setBeatmatchingEnabled(true);
      console.log('[TimelineCoordinatorEnhanced] 🎵 Enabled beatmatching');
    }

    // Apply beatmatching to all tracks
    this.updateAllTrackPlaybackRates();

    console.log('[TimelineCoordinatorEnhanced] 🎵 MANUAL: Beatmatching applied to all tracks');
  }

  // ===== TRACK UTILITIES =====

  /**
   * Get the start time of a track
   * @param trackId The ID of the track
   * @returns The start time of the track (in seconds)
   */
  getTrackStartTime(trackId: string): number {
    const track = this.getTrack(trackId);
    return track?.startTime || 0;
  }

  /**
   * Get the end time of a track
   * @param trackId The ID of the track
   * @returns The end time of the track (in seconds)
   */
  getTrackEndTime(trackId: string): number {
    const track = this.getTrack(trackId);
    if (!track) return 0;
    const duration = typeof track.duration === 'number' ? track.duration :
                    typeof track.duration === 'string' ? parseFloat(track.duration) : 0;
    return (track as any).endTime || (((track as any).startTime || 0) + duration);
  }

  /**
   * Get track information
   * @param trackId The ID of the track
   * @returns Track information including start time, end time, and duration
   */
  getTrackInfo(trackId: string): {startTime: number, endTime: number, duration: number} {
    const track = this.getTrack(trackId);
    if (!track) return { startTime: 0, endTime: 0, duration: 0 };

    const startTime = (track as any).startTime || 0;
    const duration = typeof track.duration === 'number' ? track.duration :
                    typeof track.duration === 'string' ? parseFloat(track.duration) : 0;
    const endTime = (track as any).endTime || (startTime + duration);

    return { startTime, endTime, duration };
  }

  /**
   * Check if the timeline is playing
   * @returns Whether the timeline is playing
   */
  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * Set the current time of the timeline
   * @param time The time to set (in seconds)
   */
  setCurrentTime(time: number): void {
    // Use seekTo to handle all the synchronization logic
    this.seekTo(time);

    // If we have a store, update it
    if (this.store) {
      this.store.getState().setCurrentTime(time);
    }

    console.log(`[TimelineCoordinatorEnhanced] Set current time to ${time}s`);
  }

  /**
   * Reorder tracks in the timeline
   * @param tracks The new track order
   * @param updateStore Whether to update the store (default: true)
   */
  reorderTracks(tracks: Track[], updateStore: boolean = true): void {
    // Update the tracks
    this.setTracks(tracks, false);

    // Update the store if requested
    if (updateStore && this.store) {
      this.store.getState().setTracks(tracks);
    }

    console.log(`[TimelineCoordinatorEnhanced] Reordered ${tracks.length} tracks`);
  }

  // Track which tracks have already had their deferred elements processed
  private processedDeferredTracks = new Set<string>();

  /**
   * Process deferred cue points and loops for a track when it becomes ready
   * @param trackId The ID of the track that is now ready
   */
  private processDeferredElements(trackId: string): void {
    // Prevent processing the same track multiple times
    if (this.processedDeferredTracks.has(trackId)) {
      console.log(`[TimelineCoordinatorEnhanced] Skipping deferred elements for track ${trackId} - already processed`);
      return;
    }

    console.log(`[TimelineCoordinatorEnhanced] Processing deferred elements for track ${trackId}`);
    this.processedDeferredTracks.add(trackId);

    // Process deferred cue points
    if (this.deferredCuePoints[trackId]) {
      console.log(`[TimelineCoordinatorEnhanced] Processing ${this.deferredCuePoints[trackId].length} deferred cue points for track ${trackId}`);

      this.deferredCuePoints[trackId].forEach(({ cueId, time, label, color }) => {
        try {
          // Add the cue point with a delay to ensure track is fully ready
          setTimeout(() => {
            waveSurferVisualization.addMarker(trackId, cueId, time, {
              color,
              label,
              drag: true,
            });
            console.log(`[TimelineCoordinatorEnhanced] Added deferred cue point ${cueId} to track ${trackId}`);
          }, 100);
        } catch (error) {
          console.error(`[TimelineCoordinatorEnhanced] Error adding deferred cue point ${cueId}:`, error);
        }
      });

      // Clear the deferred cue points for this track
      delete this.deferredCuePoints[trackId];
    }

    // Process deferred loops
    if (this.deferredLoops[trackId]) {
      console.log(`[TimelineCoordinatorEnhanced] Processing ${this.deferredLoops[trackId].length} deferred loops for track ${trackId}`);

      this.deferredLoops[trackId].forEach(({ loopId, start, end, label, color }) => {
        try {
          // Add the loop with a delay to ensure track is fully ready
          setTimeout(() => {
            waveSurferVisualization.addRegion(trackId, loopId, {
              start,
              end,
              color,
              label,
              drag: false,
              resize: true,
              attributes: {
                type: 'loop'
              }
            });
            console.log(`[TimelineCoordinatorEnhanced] Added deferred loop ${loopId} to track ${trackId}`);
          }, 100);
        } catch (error) {
          console.error(`[TimelineCoordinatorEnhanced] Error adding deferred loop ${loopId}:`, error);
        }
      });

      // Clear the deferred loops for this track
      delete this.deferredLoops[trackId];
    }
  }

  /**
   * Seek to a transition
   * @param transitionKey The key of the transition to seek to (fromTrackId-toTrackId)
   */
  seekToTransition(transitionKey: string): void {
    const transition = this.transitions[transitionKey];
    if (!transition) return;

    const time = transition.startPoint || 0;
    this.seekTo(time);
    console.log(`[TimelineCoordinatorEnhanced] Seeked to transition ${transitionKey} (${time}s)`);
  }

  /**
   * Update a transition between two tracks
   * @param fromTrackId The ID of the source track
   * @param toTrackId The ID of the target track
   * @param transition The updated transition data
   */
  updateTransition(fromTrackId: string, toTrackId: string, transition: Transition): void {
    const transitionKey = `${fromTrackId}-${toTrackId}`;

    // Update the transition in our local state
    this.transitions[transitionKey] = { ...transition };

    // Convert to the format expected by the audio engine
    // PHASE 3: Use beat grid system for beat-perfect transition timing
    const sourceTrack = this.tracks.find(t => t.id.toString() === fromTrackId);
    const crossfadeLengthBeats = transition.crossfadeLength || 8; // Default 8 beats

    // PHASE 3: Try to use beat grid for precise timing, fallback to BPM calculation
    let durationSeconds: number;
    let sourceBPM = sourceTrack?.bpm || 120; // Default 120 BPM (declare outside for console log)
    const beatGrid = this.getTrackBeatGrid(fromTrackId);

    if (beatGrid && this.beatAlignmentEnabled) {
      // Use beat grid for beat-perfect timing
      durationSeconds = this.calculateBeatPerfectTransitionDuration(crossfadeLengthBeats, beatGrid);
      console.log(`[TimelineCoordinatorEnhanced] PHASE 3: Using beat grid for transition timing: ${crossfadeLengthBeats} beats = ${durationSeconds.toFixed(3)}s`);
    } else {
      // Fallback to simple BPM calculation
      durationSeconds = (crossfadeLengthBeats / 4) * (60 / sourceBPM); // Convert beats to seconds
      console.log(`[TimelineCoordinatorEnhanced] PHASE 3: Using BPM fallback for transition timing: ${crossfadeLengthBeats} beats = ${durationSeconds.toFixed(3)}s at ${sourceBPM} BPM`);
    }

    const enhancedTransition = {
      ...transition,
      fromTrackId,
      toTrackId,
      duration: durationSeconds, // Add duration in seconds for audio processing
      fadeOutCurve: (transition as any).fadeOutCurve || 'linear',
      fadeInCurve: (transition as any).fadeInCurve || 'linear'
    };

    // Update the audio engine
    enhancedToneAudioEngine.updateTransition(enhancedTransition);

    // Recalculate track times as transitions may affect them
    this.calculateTrackTimes();

    console.log(`[TimelineCoordinatorEnhanced] Updated transition ${transitionKey}: ${crossfadeLengthBeats} beats (${durationSeconds.toFixed(2)}s) at ${sourceBPM} BPM`);
  }

  /**
   * Load a track into the single WaveSurfer + Tone.js architecture
   * @param track The track to load
   * @param container The container for the WaveSurfer waveform
   * @returns A promise that resolves when the track is loaded
   */
  async loadTrack(track: Track, container: HTMLElement): Promise<any> {
    try {
      console.log(`🎵🎵🎵 [TimelineCoordinatorEnhanced] LOADING TRACK ${track.id} (${track.title}) WITH SINGLE WAVESURFER + TONE.JS 🎵🎵🎵`);

      // Determine which audio URL to use based on stretching mode
      let finalAudioUrl: string;

      if (track.stretchingMode === 'backend' && track.stretchedAudioUrl) {
        // Use backend-stretched audio
        finalAudioUrl = track.stretchedAudioUrl;
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Using backend-stretched audio for track ${track.id}: ${finalAudioUrl}`);
      } else {
        // Use original audio with potential real-time stretching
        const audioUrl = track.audioUrl ||
                        (track as any).audio_url ||
                        track.filepath ||
                        track.file_path ||
                        track.filePath;

        if (!audioUrl) {
          // Import the tracks API to get the audio stream URL
          const { getAudioStreamUrl } = await import('@/services/api/tracks');
          finalAudioUrl = getAudioStreamUrl(track.id);
          console.log(`[TimelineCoordinatorEnhanced] Using streaming URL for track ${track.id}: ${finalAudioUrl}`);
        } else {
          // Check if audioUrl is a local file path that needs to be converted to a streaming URL
          if (audioUrl.startsWith('/') && !audioUrl.startsWith('/api/')) {
            // This is a local file path, convert it to a streaming URL
            const { getAudioStreamUrl } = await import('@/services/api/tracks');
            finalAudioUrl = getAudioStreamUrl(audioUrl); // Pass the file path to get streaming URL
            console.log(`[TimelineCoordinatorEnhanced] Converting file path to streaming URL for track ${track.id}: ${audioUrl} → ${finalAudioUrl}`);
          } else {
            // This is already a proper URL
            finalAudioUrl = audioUrl;
            console.log(`[TimelineCoordinatorEnhanced] Using original audio URL for track ${track.id}: ${finalAudioUrl}`);
          }
        }
      }

      // BULLETPROOF FIX: Only calculate colors if track doesn't already have a valid color
      // This prevents Smart Mix V2 pre-calculated colors from being overwritten
      let colors: { waveColor: string; progressColor: string; cursorColor: string };
      if (track.color && track.color !== null && track.color !== '#10b981') {
        // Track already has a valid pre-calculated color (from Smart Mix V2)
        console.log(`[TimelineCoordinatorEnhanced] Using pre-calculated color ${track.color} for track ${track.id}`);

        // Import darkenColor function for progress color
        const { darkenColor } = await import('../utils/trackTransitionColors');
        colors = {
          waveColor: track.color,
          progressColor: darkenColor(track.color),
          cursorColor: '#ffffff'
        };
      } else {
        // Calculate transition colors based on track position in timeline
        console.log(`[TimelineCoordinatorEnhanced] Calculating harmonic colors for track ${track.id} (no pre-calculated color)`);
        const { getTrackColorsInTimeline } = await import('../utils/trackTransitionColors');
        const trackIndex = this.tracks.findIndex(t => t.id === track.id);
        const calculatedColors = getTrackColorsInTimeline(this.tracks, trackIndex);
        colors = {
          ...calculatedColors,
          cursorColor: '#ffffff'
        };

        console.log(`[TimelineCoordinatorEnhanced] Track ${track.id} calculated colors:`, colors);
        console.log(`[TimelineCoordinatorEnhanced] Track ${track.id} data for color calculation:`, {
          id: track.id,
          key: track.key,
          bpm: track.bpm,
          trackIndex: trackIndex,
          previousTrack: trackIndex > 0 ? { id: this.tracks[trackIndex - 1].id, key: this.tracks[trackIndex - 1].key } : null
        });

        // Store the calculated color back to the track object
        track.color = colors.waveColor;
        console.log(`[TimelineCoordinatorEnhanced] Stored calculated harmonic color ${colors.waveColor} to track ${track.id}`);
      }

      // Load track into TrackManager with container for single WaveSurfer (audio + visualization)
      console.log(`[TimelineCoordinatorEnhanced] Loading track ${track.id} into TrackManager with single WaveSurfer using: ${finalAudioUrl}`);
      await enhancedToneAudioEngine.loadTrackWithContainer(track.id.toString(), finalAudioUrl, container, {
        waveColor: colors.waveColor,
        progressColor: colors.progressColor,
        cursorColor: '#C7D2FE',
        duration: track.duration // CRITICAL: Pass track duration for proper WaveSurfer sizing
      });

      console.log(`[TimelineCoordinatorEnhanced] Successfully loaded track ${track.id} (${track.title}) in single WaveSurfer + Tone.js architecture`);

      // Apply beatmatching based on stretching mode
      if (track.stretchingMode === 'backend') {
        // Backend mode: audio is already stretched, use normal playback rate
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Track ${track.id} using backend-stretched audio, playback rate: 1.0`);
        this.setTrackPlaybackRate(track.id.toString(), 1.0);
      } else if (track.stretchRatio && track.stretchRatio !== 1.0) {
        // Real-time mode: apply playback rate adjustment
        console.log(`[TimelineCoordinatorEnhanced] 🎵 Applying real-time playback rate ${track.stretchRatio.toFixed(3)} to track ${track.id}`);
        this.setTrackPlaybackRate(track.id.toString(), track.stretchRatio);
      }

      // Process any deferred cue points and loops for this track
      setTimeout(() => {
        this.processDeferredElements(track.id.toString());
      }, 200); // Small delay to ensure track is fully ready

      return { success: true };
    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] Error loading track ${track.id}:`, error);
      return { success: false, error };
    }
  }

  /**
   * Unload a track from the audio engine and destroy its waveform
   * @param trackId The ID of the track to unload
   */
  async unloadTrack(trackId: string): Promise<void> {
    console.log(`[TimelineCoordinatorEnhanced] Unloading track ${trackId}`);

    // Disconnect the track from the audio engine (includes WaveSurfer cleanup)
    try {
      // Note: EnhancedToneAudioEngine doesn't have unloadTrack, but TrackManager has disconnectTrack
      // For now, we'll let the track remain loaded but inactive
      console.log(`[TimelineCoordinatorEnhanced] Track ${trackId} removed from timeline (audio engine cleanup handled by TrackManager)`);
    } catch (error) {
      console.warn(`[TimelineCoordinatorEnhanced] Error during track ${trackId} cleanup:`, error);
    }

    console.log(`[TimelineCoordinatorEnhanced] Unloaded track ${trackId}`);
  }

  /**
   * Set zoom level for a track's waveform (native WaveSurfer zoom)
   * @param trackId The ID of the track
   * @param zoomLevel The zoom level in pixels per second
   */
  setTrackZoom(trackId: string, zoomLevel: number): void {
    enhancedToneAudioEngine.setTrackZoom(trackId, zoomLevel);
  }

  /**
   * Check if a track is currently zooming (to prevent React re-renders during zoom)
   * @param trackId The ID of the track
   * @returns Whether the track is currently zooming
   */
  isTrackZooming(trackId: string): boolean {
    return enhancedToneAudioEngine.isTrackZooming(trackId);
  }

  /**
   * Get the WaveSurfer instance for a track (for direct access)
   * @param trackId The ID of the track
   * @returns The WaveSurfer instance, or undefined if not found
   */
  getWaveSurferInstance(trackId: string): any {
    return enhancedToneAudioEngine.getWaveSurferInstance(trackId);
  }

  /**
   * Check if a waveform exists for a track
   * @param trackId The ID of the track
   * @returns Whether a waveform exists for the track
   */
  hasWaveform(trackId: string): boolean {
    return enhancedToneAudioEngine.hasTrack(trackId);
  }

  /**
   * Set whether a track is muted
   * @param trackId The ID of the track
   * @param muted Whether the track is muted
   */
  setTrackMuted(trackId: string, muted: boolean): void {
    enhancedToneAudioEngine.setTrackMuted(trackId, muted);
    console.log(`[TimelineCoordinatorEnhanced] Set track ${trackId} muted: ${muted}`);
  }

  // ===== PHASE 3: BEAT ALIGNMENT METHODS =====

  /**
   * Enable or disable beat alignment for track positioning
   * @param enabled Whether beat alignment is enabled
   */
  setBeatAlignmentEnabled(enabled: boolean): void {
    this.beatAlignmentEnabled = enabled;
    console.log(`[TimelineCoordinatorEnhanced] Beat alignment ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Set beat snap tolerance for track positioning
   * @param tolerance Snap tolerance in seconds (default: 0.1)
   */
  setBeatSnapTolerance(tolerance: number): void {
    this.beatSnapTolerance = tolerance;
    beatBoundarySnappingService.updateConfig({ snap_tolerance: tolerance });
    console.log(`[TimelineCoordinatorEnhanced] Beat snap tolerance set to ${tolerance}s`);
  }

  /**
   * Set preference for downbeat snapping
   * @param prefer Whether to prefer downbeats for snapping
   */
  setPreferDownbeats(prefer: boolean): void {
    this.preferDownbeats = prefer;
    beatBoundarySnappingService.updateConfig({ prefer_downbeats: prefer });
    console.log(`[TimelineCoordinatorEnhanced] Downbeat preference set to ${prefer}`);
  }

  /**
   * Load track with beat-perfect alignment
   * @param track The track to load
   * @param container The container element for the waveform
   * @param targetPosition Optional target position for beat alignment
   */
  async loadTrackWithBeatAlignment(
    track: Track,
    container: HTMLElement,
    targetPosition?: number
  ): Promise<{ success: boolean; error?: any; positioning?: TrackPositioningResult }> {
    try {
      console.log(`[TimelineCoordinatorEnhanced] Loading track ${track.id} with beat alignment`);

      // First load the track normally
      const loadResult = await this.loadTrack(track, container);
      if (!loadResult.success) {
        return loadResult;
      }

      // Apply beat alignment if enabled and master BPM is set
      if (this.beatAlignmentEnabled && this.masterBPM) {
        console.log(`🎯 [TimelineCoordinatorEnhanced] Beat alignment enabled, master BPM: ${this.masterBPM}, loading beat grid for track ${track.id}`);
        try {
          // Get beat grid for the track (optimized with caching)
          console.log(`🔍 [TimelineCoordinatorEnhanced] Loading beat grid for track ${track.id}...`);
          const beatGrid = await beatAlignmentOptimizer.optimizedBeatGridLoad(track.id.toString());

          if (beatGrid) {
            console.log(`✅ [TimelineCoordinatorEnhanced] Beat grid loaded for track ${track.id}:`, {
              tempo: beatGrid.tempo,
              beatTimesCount: beatGrid.beat_times ? (Array.isArray(beatGrid.beat_times) ? beatGrid.beat_times.length : JSON.parse(beatGrid.beat_times).length) : 0,
              confidence: beatGrid.confidence
            });
            this.trackBeatGrids.set(track.id.toString(), beatGrid);

            // Calculate beat alignment (optimized with caching)
            console.log(`🔍 [TimelineCoordinatorEnhanced] Calculating beat alignment for track ${track.id}...`);
            const beatAlignment = await beatAlignmentOptimizer.optimizedBeatAlignment(
              track,
              this.masterBPM
            );

            if (beatAlignment) {
              console.log(`✅ [TimelineCoordinatorEnhanced] Beat alignment calculated for track ${track.id}:`, {
                syncQuality: beatAlignment.sync_quality,
                beatOffset: beatAlignment.beat_offset,
                stretchRatio: beatAlignment.stretch_ratio
              });

              // Store beat alignment
              this.trackBeatAlignments.set(track.id.toString(), beatAlignment);

              // Position track with beat boundary snapping
              console.log(`🔍 [TimelineCoordinatorEnhanced] Positioning track ${track.id} with beat boundary snapping...`);
              const positioningResult = await beatBoundarySnappingService.positionTrackWithBeatAlignment(
                track,
                targetPosition || 0,
                this.masterBPM,
                beatGrid
              );

              // Update track with beat alignment data
              track.beatAlignment = beatAlignment;
              track.beatmatchedPosition = positioningResult.final_position;
              track.syncQuality = beatAlignment.sync_quality;
              track.beatOffset = beatAlignment.beat_offset;

              console.log(`✅ [TimelineCoordinatorEnhanced] Track ${track.id} positioned with beat alignment: ${positioningResult.positioning_quality}`);

              return {
                success: true,
                positioning: positioningResult
              };
            } else {
              console.warn(`⚠️ [TimelineCoordinatorEnhanced] Beat alignment calculation failed for track ${track.id}`);
            }
          } else {
            console.warn(`⚠️ [TimelineCoordinatorEnhanced] No beat grid loaded for track ${track.id} - beat alignment skipped`);
          }
        } catch (error) {
          console.warn(`❌ [TimelineCoordinatorEnhanced] Beat alignment failed for track ${track.id}:`, error);
          // Continue without beat alignment
        }
      } else {
        console.log(`⚠️ [TimelineCoordinatorEnhanced] Beat alignment disabled or no master BPM - beatAlignmentEnabled: ${this.beatAlignmentEnabled}, masterBPM: ${this.masterBPM}`);
      }

      return { success: true };

    } catch (error) {
      console.error(`[TimelineCoordinatorEnhanced] Error loading track with beat alignment:`, error);
      return { success: false, error };
    }
  }

  /**
   * Apply beat alignment to existing tracks when master BPM changes (optimized)
   * @param newMasterBPM The new master BPM
   */
  async applyBeatAlignmentToAllTracks(newMasterBPM: number): Promise<void> {
    if (!this.beatAlignmentEnabled) {
      console.log('[TimelineCoordinatorEnhanced] Beat alignment disabled, skipping alignment update');
      return;
    }

    console.log(`[TimelineCoordinatorEnhanced] Applying beat alignment to all tracks with master BPM ${newMasterBPM} (optimized)`);

    try {
      // Use optimized batch processing
      const results = await beatAlignmentOptimizer.batchUpdateTracks(this.tracks, newMasterBPM);

      // Update track data with results
      results.forEach(({ track, alignment }) => {
        if (alignment) {
          // Update stored alignment
          this.trackBeatAlignments.set(track.id.toString(), alignment);

          // Update track properties
          track.beatAlignment = alignment;
          track.syncQuality = alignment.sync_quality;
          track.beatOffset = alignment.beat_offset;

          console.log(`[TimelineCoordinatorEnhanced] Updated beat alignment for track ${track.id}: ${alignment.sync_quality}`);
        } else {
          console.warn(`[TimelineCoordinatorEnhanced] Failed to update beat alignment for track ${track.id}`);
        }
      });

      console.log('[TimelineCoordinatorEnhanced] Optimized beat alignment update completed for all tracks');

      // Trigger debounced visual updates
      this.tracks.forEach(track => {
        beatAlignmentOptimizer.debouncedVisualUpdate(
          track.id.toString(),
          () => {
            // Visual update will be handled by BeatGridRegions component
            // This just triggers the debounced update mechanism
          }
        );
      });

    } catch (error) {
      console.error('[TimelineCoordinatorEnhanced] Batch beat alignment update failed:', error);

      // Fallback to individual processing if batch fails
      console.log('[TimelineCoordinatorEnhanced] Falling back to individual track processing');
      for (const track of this.tracks) {
        try {
          const alignment = await beatAlignmentOptimizer.optimizedBeatAlignment(track, newMasterBPM);
          if (alignment) {
            this.trackBeatAlignments.set(track.id.toString(), alignment);
            track.beatAlignment = alignment;
            track.syncQuality = alignment.sync_quality;
            track.beatOffset = alignment.beat_offset;
          }
        } catch (trackError) {
          console.warn(`[TimelineCoordinatorEnhanced] Individual track ${track.id} alignment failed:`, trackError);
        }
      }
    }
  }

  /**
   * Get beat alignment for a track
   * @param trackId The ID of the track
   * @returns The beat alignment data, or undefined if not available
   */
  getTrackBeatAlignment(trackId: string): BeatAlignment | undefined {
    return this.trackBeatAlignments.get(trackId);
  }

  /**
   * Get beat grid for a track
   * @param trackId The ID of the track
   * @returns The beat grid data, or undefined if not available
   */
  getTrackBeatGrid(trackId: string): BeatGrid | undefined {
    return this.trackBeatGrids.get(trackId);
  }

  /**
   * Snap position to nearest beat for a track
   * @param trackId The ID of the track
   * @param position The position to snap
   * @returns The snapped position and snap information
   */
  async snapPositionToBeat(trackId: string, position: number): Promise<{
    snapped_position: number;
    distance_to_beat: number;
    snapped: boolean;
  }> {
    const beatGrid = this.trackBeatGrids.get(trackId);
    if (!beatGrid || !this.beatAlignmentEnabled) {
      return {
        snapped_position: position,
        distance_to_beat: Infinity,
        snapped: false
      };
    }

    const snapResult = await beatBoundarySnappingService.snapToNearestBeat(
      position,
      beatGrid,
      { snap_tolerance: this.beatSnapTolerance }
    );

    return {
      snapped_position: snapResult.snapped_position,
      distance_to_beat: snapResult.distance_to_beat,
      snapped: snapResult.snapped
    };
  }

  /**
   * Enable beat grid visualization for a track
   * @param trackId The ID of the track
   * @param enabled Whether to show beat grid
   */
  setTrackBeatGridVisible(trackId: string, enabled: boolean): void {
    // This will be handled by the BeatGridRegions component
    // We just store the preference and let the component react to it
    console.log(`[TimelineCoordinatorEnhanced] Beat grid visibility for track ${trackId}: ${enabled}`);

    // Trigger beat grid update in WaveSurfer
    const waveSurferInstance = this.getWaveSurferInstance(trackId);
    if (waveSurferInstance && enabled) {
      // The BeatGridRegions component will handle the actual visualization
      // This method provides the interface for controlling visibility
    }
  }

  /**
   * Get performance statistics for beat alignment operations
   */
  getBeatAlignmentPerformanceStats(): {
    cache: any;
    performance: any;
    recommendations: string[];
  } {
    const stats = beatAlignmentOptimizer.getPerformanceStats();
    const recommendations: string[] = [];

    // Generate performance recommendations
    if (stats.performance.averageBeatAlignmentTime > 100) {
      recommendations.push('Beat alignment operations are slow - consider reducing track count or optimizing beat grid data');
    }

    if (stats.cache.totalMemoryEstimate > 50 * 1024 * 1024) { // 50MB
      recommendations.push('High memory usage detected - consider clearing caches periodically');
    }

    if (stats.performance.totalOperations > 1000) {
      recommendations.push('High operation count - performance optimization is working well');
    }

    return {
      ...stats,
      recommendations
    };
  }

  /**
   * Optimize beat alignment performance
   */
  optimizeBeatAlignmentPerformance(): void {
    console.log('[TimelineCoordinatorEnhanced] Optimizing beat alignment performance');

    // Clear old caches and optimize memory
    beatAlignmentOptimizer.optimizeMemory();

    // Log performance stats
    const stats = this.getBeatAlignmentPerformanceStats();
    console.log('[TimelineCoordinatorEnhanced] Performance stats:', stats);

    if (stats.recommendations.length > 0) {
      console.log('[TimelineCoordinatorEnhanced] Performance recommendations:', stats.recommendations);
    }
  }

  /**
   * PHASE 3: Calculate beat-perfect transition duration using beat grid data
   * @param crossfadeLengthBeats Number of beats for the transition
   * @param beatGrid Beat grid data for the track
   * @returns Duration in seconds based on actual beat timing
   */
  private calculateBeatPerfectTransitionDuration(crossfadeLengthBeats: number, beatGrid: any): number {
    try {
      // Get beat times from the beat grid
      const beatTimes = beatGrid.beat_times || beatGrid.beatTimes || [];

      if (beatTimes.length < 2) {
        console.warn('[TimelineCoordinatorEnhanced] PHASE 3: Insufficient beat data, falling back to BPM calculation');
        // Fallback to BPM-based calculation
        const tempo = beatGrid.tempo || 120;
        return (crossfadeLengthBeats / 4) * (60 / tempo);
      }

      // Calculate average beat interval from actual beat grid data
      let totalInterval = 0;
      let intervalCount = 0;

      // Sample multiple beat intervals for accuracy
      const sampleSize = Math.min(beatTimes.length - 1, 16); // Use up to 16 beat intervals
      const startIndex = Math.max(0, Math.floor((beatTimes.length - sampleSize) / 2)); // Start from middle

      for (let i = startIndex; i < startIndex + sampleSize; i++) {
        if (i + 1 < beatTimes.length) {
          totalInterval += beatTimes[i + 1] - beatTimes[i];
          intervalCount++;
        }
      }

      if (intervalCount === 0) {
        console.warn('[TimelineCoordinatorEnhanced] PHASE 3: No valid beat intervals found');
        const tempo = beatGrid.tempo || 120;
        return (crossfadeLengthBeats / 4) * (60 / tempo);
      }

      const averageBeatInterval = totalInterval / intervalCount;
      const durationSeconds = crossfadeLengthBeats * averageBeatInterval;

      console.log(`[TimelineCoordinatorEnhanced] PHASE 3: Beat-perfect timing calculated: ${crossfadeLengthBeats} beats × ${averageBeatInterval.toFixed(3)}s/beat = ${durationSeconds.toFixed(3)}s`);

      return durationSeconds;

    } catch (error) {
      console.error('[TimelineCoordinatorEnhanced] PHASE 3: Error calculating beat-perfect duration:', error);
      // Fallback to BPM-based calculation
      const tempo = beatGrid.tempo || 120;
      return (crossfadeLengthBeats / 4) * (60 / tempo);
    }
  }

  /**
   * PHASE 3: Snap transition start point to beat boundary
   * @param fromTrack Source track
   * @param toTrack Target track
   * @param proposedStartTime Proposed transition start time
   * @returns Snapped position result
   */
  private async snapTransitionToBeat(
    _fromTrack: Track,
    _toTrack: Track,
    proposedStartTime: number
  ): Promise<{
    snapped_position: number;
    distance_to_beat: number;
    snapped: boolean;
  }> {
    try {
      // Use the source track's beat grid for transition timing
      const fromTrackId = _fromTrack.id.toString();
      const beatGrid = this.getTrackBeatGrid(fromTrackId);

      if (!beatGrid) {
        console.warn(`[TimelineCoordinatorEnhanced] PHASE 3: No beat grid for track ${fromTrackId}, cannot snap transition`);
        return {
          snapped_position: proposedStartTime,
          distance_to_beat: Infinity,
          snapped: false
        };
      }

      // Convert global timeline position to track-local position
      const trackStartTime = (_fromTrack as any).startTime || 0;
      const localPosition = proposedStartTime - trackStartTime;

      // Use existing beat boundary snapping service
      const snapResult = await beatBoundarySnappingService.snapToNearestBeat(
        localPosition,
        beatGrid,
        { snap_tolerance: this.beatSnapTolerance }
      );

      // Convert back to global timeline position
      const globalSnappedPosition = snapResult.snapped_position + trackStartTime;

      return {
        snapped_position: globalSnappedPosition,
        distance_to_beat: snapResult.distance_to_beat,
        snapped: snapResult.snapped
      };

    } catch (error) {
      console.error('[TimelineCoordinatorEnhanced] PHASE 3: Error snapping transition to beat:', error);
      return {
        snapped_position: proposedStartTime,
        distance_to_beat: Infinity,
        snapped: false
      };
    }
  }

  // ===== TIMELINE LOOP METHODS (ABLETON-STYLE) =====

  /**
   * Enable/disable timeline looping
   * @param enabled Whether timeline looping is enabled
   */
  setTimelineLooping(enabled: boolean): void {
    this.timelineLoopEnabled = enabled;
    console.log(`[TimelineCoordinatorEnhanced] Timeline looping ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Set the timeline loop region
   * @param start Loop start time in seconds
   * @param end Loop end time in seconds
   */
  setTimelineLoopRegion(start: number, end: number): void {
    this.timelineLoopStart = Math.max(0, start);
    this.timelineLoopEnd = Math.max(this.timelineLoopStart + 0.1, end);
    console.log(`[TimelineCoordinatorEnhanced] Timeline loop region set: ${this.timelineLoopStart.toFixed(2)}s - ${this.timelineLoopEnd.toFixed(2)}s`);
  }

  /**
   * Get timeline loop state
   * @returns Timeline loop configuration
   */
  getTimelineLoopState(): { enabled: boolean; start: number; end: number } {
    return {
      enabled: this.timelineLoopEnabled,
      start: this.timelineLoopStart,
      end: this.timelineLoopEnd
    };
  }

  /**
   * Check if current time should loop back to start
   * @param currentTime Current playback time
   * @returns New time if loop should occur, null otherwise
   */
  checkTimelineLoop(currentTime: number): number | null {
    if (!this.timelineLoopEnabled) return null;

    if (currentTime >= this.timelineLoopEnd) {
      console.log(`[TimelineCoordinatorEnhanced] Timeline loop: jumping from ${currentTime.toFixed(2)}s to ${this.timelineLoopStart.toFixed(2)}s`);
      return this.timelineLoopStart;
    }

    return null;
  }

  // ===== RECORDING METHODS =====

  /**
   * Initialize the recorder
   * @param container The container element for the recorder visualization
   * @param options Options for the recorder
   */
  async initializeRecorder(container: HTMLElement, options: any = {}): Promise<void> {
    return waveSurferVisualization.initializeRecorder(container, options);
  }

  /**
   * Start recording
   */
  async startRecording(): Promise<void> {
    return waveSurferVisualization.startRecording();
  }

  /**
   * Stop recording
   * @returns The recorded audio blob
   */
  async stopRecording(): Promise<Blob> {
    return waveSurferVisualization.stopRecording();
  }

  /**
   * Pause recording
   */
  async pauseRecording(): Promise<void> {
    return waveSurferVisualization.pauseRecording();
  }

  /**
   * Resume recording
   */
  async resumeRecording(): Promise<void> {
    return waveSurferVisualization.resumeRecording();
  }

  /**
   * Destroy the recorder
   */
  async destroyRecorder(): Promise<void> {
    return waveSurferVisualization.destroyRecorder();
  }

  /**
   * Get the recording state
   * @returns The recording state
   */
  getRecordingState(): any {
    return waveSurferVisualization.getRecordingState();
  }

  /**
   * Add a recorded track to the timeline
   * @param blob The recorded audio blob
   * @param name The name for the recorded track
   */
  async addRecordedTrack(_blob: Blob, _name: string): Promise<void> {
    // TODO: Implement addRecordedTrack functionality
    // This would need to convert the blob to a track and add it to the timeline
    console.warn('[TimelineCoordinatorEnhanced] addRecordedTrack not yet implemented');
    throw new Error('addRecordedTrack functionality not yet implemented');
  }
}

// Create a singleton instance
const timelineCoordinatorEnhanced = new TimelineCoordinatorEnhanced();

export default timelineCoordinatorEnhanced;
