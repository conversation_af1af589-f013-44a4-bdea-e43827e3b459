import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit2, Trash2, Bookmark } from 'lucide-react';
import { cn } from '@/lib/utils';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';

interface Marker {
  id: string;
  time: number;
  label: string;
  color: string;
}

interface MarkerLaneProps {
  totalDuration: number;
  pixelsPerSecond: number;
  currentTime: number;
  onSeek: (time: number) => void;
  markers?: Marker[];
  onMarkersChange?: (markers: Marker[]) => void;
}

/**
 * MarkerLane - Dedicated lane for timeline markers
 * 
 * Features:
 * - Visual markers spanning across timeline
 * - Add/edit/delete markers
 * - Click to seek to marker
 * - Drag to reposition markers
 * - Professional DAW-style appearance
 */
const MarkerLane: React.FC<MarkerLaneProps> = ({
  totalDuration,
  pixelsPerSecond,
  currentTime,
  onSeek,
  markers = [],
  onMarkersChange,
}) => {
  const [isAddingMarker, setIsAddingMarker] = useState(false);
  const [editingMarkerId, setEditingMarkerId] = useState<string | null>(null);
  const [editingLabel, setEditingLabel] = useState('');
  const [draggedMarkerId, setDraggedMarkerId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Default marker colors
  const markerColors = [
    '#ef4444', // red
    '#f97316', // orange  
    '#eab308', // yellow
    '#22c55e', // green
    '#06b6d4', // cyan
    '#3b82f6', // blue
    '#8b5cf6', // violet
    '#ec4899', // pink
  ];

  // Format time for display
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    const centiseconds = Math.floor((time % 1) * 100);
    return `${minutes}:${seconds.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
  }, []);

  // Add marker at current time
  const handleAddMarker = useCallback(() => {
    const newMarker: Marker = {
      id: `marker-${Date.now()}`,
      time: currentTime,
      label: `Marker ${markers.length + 1}`,
      color: markerColors[markers.length % markerColors.length]
    };
    
    const updatedMarkers = [...markers, newMarker].sort((a, b) => a.time - b.time);
    onMarkersChange?.(updatedMarkers);
    setIsAddingMarker(false);
  }, [currentTime, markers, onMarkersChange]);

  // Delete marker
  const handleDeleteMarker = useCallback((markerId: string) => {
    const updatedMarkers = markers.filter(m => m.id !== markerId);
    onMarkersChange?.(updatedMarkers);
  }, [markers, onMarkersChange]);

  // Start editing marker label
  const handleStartEdit = useCallback((marker: Marker) => {
    setEditingMarkerId(marker.id);
    setEditingLabel(marker.label);
  }, []);

  // Save marker label edit
  const handleSaveEdit = useCallback(() => {
    if (!editingMarkerId) return;
    
    const updatedMarkers = markers.map(m => 
      m.id === editingMarkerId ? { ...m, label: editingLabel } : m
    );
    onMarkersChange?.(updatedMarkers);
    setEditingMarkerId(null);
    setEditingLabel('');
  }, [editingMarkerId, editingLabel, markers, onMarkersChange]);

  // Cancel editing
  const handleCancelEdit = useCallback(() => {
    setEditingMarkerId(null);
    setEditingLabel('');
  }, []);

  // Handle marker drag
  const handleMarkerMouseDown = useCallback((e: React.MouseEvent, markerId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setDraggedMarkerId(markerId);
  }, []);

  // Handle mouse move during drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!draggedMarkerId || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const time = Math.max(0, Math.min(totalDuration, mouseX / pixelsPerSecond));

    const updatedMarkers = markers.map(m => 
      m.id === draggedMarkerId ? { ...m, time } : m
    ).sort((a, b) => a.time - b.time);
    
    onMarkersChange?.(updatedMarkers);
  }, [draggedMarkerId, totalDuration, pixelsPerSecond, markers, onMarkersChange]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    setDraggedMarkerId(null);
  }, []);

  // Set up global mouse events for dragging
  useEffect(() => {
    if (draggedMarkerId) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedMarkerId, handleMouseMove, handleMouseUp]);

  // Handle double-click to add marker
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = e.clientX - rect.left;
    const clickTime = clickX / pixelsPerSecond;
    
    const newMarker: Marker = {
      id: `marker-${Date.now()}`,
      time: Math.max(0, Math.min(totalDuration, clickTime)),
      label: `Marker ${markers.length + 1}`,
      color: markerColors[markers.length % markerColors.length]
    };
    
    const updatedMarkers = [...markers, newMarker].sort((a, b) => a.time - b.time);
    onMarkersChange?.(updatedMarkers);
  }, [pixelsPerSecond, totalDuration, markers, onMarkersChange]);

  return (
    <div className="h-12 bg-muted/30 border-b border-border relative">
      {/* Lane Header */}
      <div className="absolute left-0 top-0 w-20 h-full bg-muted border-r border-border flex items-center justify-center">
        <div className="flex items-center gap-1">
          <Bookmark className="h-3 w-3 text-muted-foreground" />
          <span className="text-xs font-medium text-muted-foreground">Markers</span>
        </div>
      </div>

      {/* Marker Area */}
      <div 
        ref={containerRef}
        className="absolute left-20 right-0 top-0 bottom-0 cursor-pointer"
        onDoubleClick={handleDoubleClick}
      >
        {/* Markers */}
        {markers.map((marker) => {
          const markerX = marker.time * pixelsPerSecond;
          
          return (
            <div
              key={marker.id}
              className="absolute top-0 bottom-0 group cursor-pointer"
              style={{ left: markerX - 1 }}
              onMouseDown={(e) => handleMarkerMouseDown(e, marker.id)}
              onClick={() => onSeek(marker.time)}
            >
              {/* Marker Line */}
              <div 
                className="w-0.5 h-full group-hover:w-1 transition-all"
                style={{ backgroundColor: marker.color }}
              />
              
              {/* Marker Flag */}
              <div 
                className="absolute top-0 left-0 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent group-hover:scale-110 transition-transform"
                style={{ borderBottomColor: marker.color }}
              />
              
              {/* Marker Label */}
              <div className="absolute top-4 left-1 min-w-max">
                {editingMarkerId === marker.id ? (
                  <div className="flex items-center gap-1">
                    <Input
                      value={editingLabel}
                      onChange={(e) => setEditingLabel(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') handleCancelEdit();
                      }}
                      onBlur={handleSaveEdit}
                      className="h-6 px-2 text-xs w-20"
                      autoFocus
                    />
                  </div>
                ) : (
                  <Badge
                    variant="secondary"
                    className="text-xs px-2 py-0.5 cursor-pointer hover:bg-accent group-hover:opacity-100 opacity-80"
                    style={{ borderColor: marker.color }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStartEdit(marker);
                    }}
                  >
                    {marker.label}
                  </Badge>
                )}
              </div>

              {/* Marker Controls (on hover) */}
              <div className="absolute top-0 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleStartEdit(marker);
                  }}
                >
                  <Edit2 className="h-2 w-2" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 text-destructive hover:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteMarker(marker.id);
                  }}
                >
                  <Trash2 className="h-2 w-2" />
                </Button>
              </div>

              {/* Time Tooltip */}
              <div className="absolute bottom-0 left-1 opacity-0 group-hover:opacity-100 transition-opacity bg-background border border-border rounded px-1 py-0.5 text-xs whitespace-nowrap">
                {formatTime(marker.time)}
              </div>
            </div>
          );
        })}

        {/* Add Marker Button */}
        <div className="absolute top-1 right-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAddMarker}
            className="h-6 px-2 text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Marker
          </Button>
        </div>

        {/* Instructions */}
        {markers.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center text-xs text-muted-foreground">
            Double-click to add markers • Click markers to seek
          </div>
        )}
      </div>
    </div>
  );
};

export default MarkerLane;
