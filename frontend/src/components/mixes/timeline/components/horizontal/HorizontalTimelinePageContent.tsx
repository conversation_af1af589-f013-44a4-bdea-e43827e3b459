import React, { useState, useEffect, useCallback } from 'react';
import { useTimelineStore } from '../../stores/TimelineStore';
import { useParams } from 'react-router-dom';
import { PanelGroup, Panel } from 'react-resizable-panels';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn, debounce } from '@/lib/utils';

// Import existing components we'll reuse
import TrackSelectorPanel from '../panels/TrackSelectorPanel';
import TrackDetailsPanel from '../panels/TrackDetailsPanel'; // ADDED: Proper right sidebar
import EnhancedTimelineFooter from '../controls/EnhancedTimelineFooter';

// Import layout components
import { PanelResizeHandle } from 'react-resizable-panels';

// Import horizontal-specific components (to be created)
import HorizontalTimelineMain from './HorizontalTimelineMain';

// Import services
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';

// OPTIMIZED: Import performance and memory management (same as vertical timeline)
import { TimelinePerformanceMonitor, batchDOMOperations } from '../../utils/performance';
import waveSurferVisualization from '../../services/WaveSurferVisualization';

// Inline components (copied from TimelinePage.tsx)
const TopNavbar = ({
  mixTitle = "New Mix",
  onMixTitleChange,
  onResetLayout = () => {},
  isPublic = false,
  setIsPublic,
  genre = "",
  setGenre,
  autoAnalyzeEnabled = false,
  setAutoAnalyzeEnabled,
  mixProgress = 0,
  showSaveSuccess = false,
  onSaveMix = () => {},
  tracks = [],
  totalDuration = 0,
  currentTrack = null,
  isPlaying = false,
  // Undo-redo props
  canUndo = false,
  canRedo = false,
  onUndo = () => {},
  onRedo = () => {},
  undoDescription = null,
  redoDescription = null,
  // Mix structure overview props
  showMixStructureOverview = true,
  onToggleMixStructureOverview = () => {},
  onOpenShortcuts = () => {}
}) => {
  return (
    <div className="panel-header flex justify-between items-center flex-shrink-0 bg-background border-b border-border px-4 py-2">
      <div className="flex items-center gap-4">
        <h2 className="text-lg font-semibold">Horizontal Timeline</h2>
        <span className="text-sm text-muted-foreground">
          {tracks.length} tracks • {Math.round(totalDuration)}s
        </span>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={onSaveMix}>
          Save Mix
        </Button>
      </div>
    </div>
  );
};

const HoverResizeHandle = ({ id, className = "" }: { id: string; className?: string }) => {
  return (
    <PanelResizeHandle
      id={id}
      className={cn(
        "w-1 bg-border hover:bg-primary/50 transition-standard mx-1",
        className
      )}
    />
  );
};

const CollapsedPanel = ({
  id,
  side,
  onExpand
}: {
  id: string;
  side: 'left' | 'right';
  onExpand: () => void;
}) => {
  return (
    <div
      id={id}
      onClick={onExpand}
    >
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6"
        aria-label="Expand panel"
        title="Expand panel"
      >
        {side === 'left' ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
      </Button>
    </div>
  );
};

// REMOVED: Custom RightSidebar - using proper TrackDetailsPanel instead

// Panel size constants
const DEFAULT_LEFT_PANEL_SIZE = 20;
const DEFAULT_RIGHT_PANEL_SIZE = 25;
const DEFAULT_MAIN_PANEL_SIZE = 55;

/**
 * HorizontalTimelinePageContent - Main content component for horizontal timeline
 * 
 * This component provides the same layout structure as TimelinePage but with
 * horizontal timeline arrangement. It reuses most existing components and
 * only replaces the main timeline area with horizontal layout.
 */
const HorizontalTimelinePageContent: React.FC = () => {
  const { mixId } = useParams<{ mixId?: string }>();
  
  // Timeline store state
  const {
    tracks,
    transitions,
    selectedTrackId,
    setSelectedTrackId,
    currentTime,
    isPlaying,
    setCurrentTime,
    setIsPlaying,
  } = useTimelineStore();

  // Panel state
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [leftPanelSize, setLeftPanelSize] = useState(DEFAULT_LEFT_PANEL_SIZE);
  const [rightPanelSize, setRightPanelSize] = useState(DEFAULT_RIGHT_PANEL_SIZE);

  // Mix metadata state
  const [mixTitle, setMixTitle] = useState('Untitled Mix');
  const [isPublic, setIsPublic] = useState(false);
  const [genre, setGenre] = useState('');
  const [autoAnalyzeEnabled, setAutoAnalyzeEnabled] = useState(true);
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);
  const [showMixStructureOverview, setShowMixStructureOverview] = useState(false);

  // Undo/Redo state (placeholder for now)
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [undoDescription, setUndoDescription] = useState('');
  const [redoDescription, setRedoDescription] = useState('');

  // Current track and mix progress
  const currentTrack = tracks.find(track => track.id === selectedTrackId);
  const mixProgress = tracks.length > 0 ? (currentTime / timelineCoordinatorEnhanced.getTotalDuration()) * 100 : 0;

  // Debounced resize handler
  const debouncedHandleResizeEnd = useCallback(
    debounce(() => {
      // Handle resize end logic if needed
    }, 300),
    []
  );

  // Handle mix title change
  const handleMixTitleChange = useCallback((newTitle: string) => {
    setMixTitle(newTitle);
  }, []);

  // Handle layout reset
  const handleResetLayout = useCallback(() => {
    setLeftPanelCollapsed(false);
    setRightPanelCollapsed(false);
    setLeftPanelSize(DEFAULT_LEFT_PANEL_SIZE);
    setRightPanelSize(DEFAULT_RIGHT_PANEL_SIZE);
  }, []);

  // Handle seek - ARCHITECTURAL FIX: Only call coordinator, let callback update store
  const handleSeek = useCallback((time: number) => {
    // CRITICAL FIX: Don't update store directly - let the coordinator's time update callback handle it
    // This prevents double updates and maintains proper synchronization flow
    timelineCoordinatorEnhanced.seekTo(time);
  }, []);

  // Handle track selection
  const handleTrackSelect = useCallback((trackId: string) => {
    setSelectedTrackId(trackId);
  }, [setSelectedTrackId]);

  // Undo/Redo handlers (placeholder)
  const undo = useCallback(() => {
    console.log('Undo action');
  }, []);

  const redo = useCallback(() => {
    console.log('Redo action');
  }, []);

  // CRITICAL FIX: Initialize coordinator and register time update callback
  useEffect(() => {
    // Set the store reference in the coordinator (needed for some operations)
    timelineCoordinatorEnhanced.setStore(useTimelineStore);

    // Handle time updates from the coordinator
    const handleTimeUpdate = (time: number) => {
      setCurrentTime(time);
    };

    // Register the callback
    timelineCoordinatorEnhanced.onTimeUpdate(handleTimeUpdate);
    console.log('[HorizontalTimelinePageContent] Initialized coordinator and registered time update callback');

    // Cleanup function
    return () => {
      timelineCoordinatorEnhanced.offTimeUpdate(handleTimeUpdate);
      console.log('[HorizontalTimelinePageContent] Unregistered time update callback');
    };
  }, [setCurrentTime]);

  // CRITICAL FIX: Sync store tracks with coordinator (missing from original implementation)
  useEffect(() => {
    console.log(`[HorizontalTimelinePageContent] DEBUG: useEffect tracks - length: ${tracks.length}`);

    // CRITICAL FIX: Always sync tracks to coordinator, even if 0 tracks
    // This ensures coordinator state is always in sync with React state
    console.log(`[HorizontalTimelinePageContent] DEBUG: Calling timelineCoordinatorEnhanced.setTracks with ${tracks.length} tracks`);

    // Force coordinator update - don't skip if equal to ensure sync
    timelineCoordinatorEnhanced.setTracks(tracks, false);

    // Verify the sync worked
    const coordinatorTracks = timelineCoordinatorEnhanced.getTracks();
    console.log(`[HorizontalTimelinePageContent] DEBUG: Coordinator now has ${coordinatorTracks.length} tracks after sync`);
  }, [tracks]);

  // OPTIMIZED: Memory management and cleanup (same as vertical timeline)
  useEffect(() => {
    // Periodic memory cleanup
    const cleanupInterval = setInterval(() => {
      batchDOMOperations(() => {
        // Clean up unused waveform instances
        waveSurferVisualization.cleanupUnusedInstances();

        // Get current track IDs
        const currentTrackIds = tracks.map(track => track.id?.toString()).filter(Boolean);

        // Clean up any orphaned instances
        const performanceMonitor = TimelinePerformanceMonitor.getInstance();
        const metrics = performanceMonitor.getCurrentMetrics();

        if (metrics) {
          console.log(`[HorizontalTimelinePageContent] Memory cleanup - Active waveforms: ${metrics.activeWaveforms}, Memory: ${metrics.memoryUsage}MB`);
        }
      });
    }, 30000); // Every 30 seconds

    return () => {
      clearInterval(cleanupInterval);
    };
  }, [tracks]);

  return (
    <div className="flex h-full w-full p-2.5 bg-background min-w-0 overflow-hidden">
      {/* Main Content Area with Resizable Panels */}
      <div className="flex-1 flex flex-col min-w-0 relative overflow-hidden">
        {/* Top Navbar */}
        <TopNavbar
          mixTitle={mixTitle}
          onMixTitleChange={handleMixTitleChange}
          onResetLayout={handleResetLayout}
          isPublic={isPublic}
          setIsPublic={setIsPublic}
          genre={genre}
          setGenre={setGenre}
          autoAnalyzeEnabled={autoAnalyzeEnabled}
          setAutoAnalyzeEnabled={setAutoAnalyzeEnabled}
          mixProgress={mixProgress}
          showSaveSuccess={showSaveSuccess}
          tracks={tracks}
          totalDuration={timelineCoordinatorEnhanced.getTotalDuration()}
          currentTrack={currentTrack}
          isPlaying={isPlaying}
          // Undo-redo props
          canUndo={canUndo}
          canRedo={canRedo}
          onUndo={undo}
          onRedo={redo}
          undoDescription={undoDescription}
          redoDescription={redoDescription}
          onSaveMix={() => {
            console.log('Saving mix...');
            setShowSaveSuccess(true);
            setTimeout(() => setShowSaveSuccess(false), 3000);
          }}
          // Mix structure overview props
          showMixStructureOverview={showMixStructureOverview}
          onToggleMixStructureOverview={() => setShowMixStructureOverview(!showMixStructureOverview)}
          onOpenShortcuts={() => {}} // TODO: Implement shortcuts dialog
        />

        {/* Main Content Area with Resizable Panels */}
        <div className="flex-1 flex min-w-0 pb-14 overflow-hidden">
          {/* Main Panel Group - Contains all panels including collapsed ones */}
          <PanelGroup
            direction="horizontal"
            className="flex-1 gap-2.5 min-w-0 h-full"
            onLayout={(sizes) => {
              if (sizes.length === 3) {
                const newLeftSize = sizes[0];
                const newRightSize = sizes[2];

                if (!leftPanelCollapsed && Math.abs(newLeftSize - leftPanelSize) > 1) {
                  setLeftPanelSize(newLeftSize);
                }
                if (!rightPanelCollapsed && Math.abs(newRightSize - rightPanelSize) > 1) {
                  setRightPanelSize(newRightSize);
                }

                debouncedHandleResizeEnd();
              }
            }}
            style={{ direction: "ltr" }}
          >
            {/* Left Sidebar - Track Selector */}
            <Panel
              id="left-panel"
              defaultSize={leftPanelCollapsed ? 3 : leftPanelSize}
              minSize={leftPanelCollapsed ? 3 : 15}
              maxSize={leftPanelCollapsed ? 3 : 30}
              className={cn(
                "bg-background rounded-lg shadow-sm border border-border group transition-all duration-200 overflow-hidden",
                leftPanelCollapsed && "flex items-center justify-center"
              )}
            >
              {leftPanelCollapsed ? (
                <CollapsedPanel
                  id="left-panel-collapsed"
                  side="left"
                  onExpand={() => setLeftPanelCollapsed(false)}
                />
              ) : (
                <div className="h-full relative">
                  <TrackSelectorPanel />

                  {/* Collapse/Expand Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-4 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setLeftPanelCollapsed(true)}
                    aria-label="Collapse panel"
                    title="Collapse panel"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </Panel>

            {/* Custom Resize Handle */}
            <HoverResizeHandle
              id="left-resize-handle"
              className={leftPanelCollapsed ? "hidden" : ""}
            />

            {/* Main Content - Horizontal Timeline */}
            <Panel
              id="main-panel"
              defaultSize={DEFAULT_MAIN_PANEL_SIZE}
              className="bg-background rounded-lg shadow-sm border border-border transition-all duration-200 overflow-hidden h-full"
            >
              <div className="h-full flex flex-col">
                {/* Fixed Panel Header */}
                <div className="panel-header flex justify-between items-center flex-shrink-0">
                  <h2 className="panel-title">Horizontal Timeline</h2>
                </div>

                {/* Main Horizontal Timeline Component */}
                <div className="flex-1 min-h-0">
                  <HorizontalTimelineMain
                    tracks={tracks}
                    transitions={transitions}
                    currentTime={currentTime}
                    isPlaying={isPlaying}
                    selectedTrackId={selectedTrackId}
                    onTrackSelect={handleTrackSelect}
                    onSeek={handleSeek}
                    totalDuration={timelineCoordinatorEnhanced.getTotalDuration()}
                  />
                </div>
              </div>
            </Panel>

            {/* Custom Resize Handle */}
            <HoverResizeHandle
              id="right-resize-handle"
              className={rightPanelCollapsed ? "hidden" : ""}
            />

            {/* Right Sidebar - Context-aware content */}
            <Panel
              id="right-panel"
              defaultSize={rightPanelCollapsed ? 3 : rightPanelSize}
              minSize={rightPanelCollapsed ? 3 : 20}
              maxSize={rightPanelCollapsed ? 3 : 40}
              className={cn(
                "bg-background rounded-lg shadow-sm border border-border group transition-all duration-200 overflow-hidden",
                rightPanelCollapsed && "flex items-center justify-center"
              )}
            >
              {rightPanelCollapsed ? (
                <CollapsedPanel
                  id="right-panel-collapsed"
                  side="right"
                  onExpand={() => setRightPanelCollapsed(false)}
                />
              ) : (
                <div className="h-full relative">
                  <TrackDetailsPanel />

                  {/* Collapse/Expand Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-4 left-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setRightPanelCollapsed(true)}
                    aria-label="Collapse panel"
                    title="Collapse panel"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </Panel>
          </PanelGroup>
        </div>

        {/* Timeline Footer */}
        <EnhancedTimelineFooter />
      </div>
    </div>
  );
};

export default HorizontalTimelinePageContent;
