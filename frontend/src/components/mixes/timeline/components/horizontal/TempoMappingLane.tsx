import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit2, Trash2, Activity } from 'lucide-react';
import { cn } from '@/lib/utils';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';

interface TempoPoint {
  id: string;
  time: number;
  bpm: number;
}

interface TempoMappingLaneProps {
  totalDuration: number;
  pixelsPerSecond: number;
  currentTime: number;
  masterBPM: number;
  onSeek: (time: number) => void;
  tempoPoints?: TempoPoint[];
  onTempoPointsChange?: (points: TempoPoint[]) => void;
}

/**
 * TempoMappingLane - BPM automation lane for tempo changes over time
 * 
 * Features:
 * - Visual tempo curve across timeline
 * - Add/edit/delete tempo points
 * - Smooth tempo transitions
 * - Integration with master BPM system
 * - Professional DAW-style appearance
 */
const TempoMappingLane: React.FC<TempoMappingLaneProps> = ({
  totalDuration,
  pixelsPerSecond,
  currentTime,
  masterBPM,
  onSeek,
  tempoPoints = [],
  onTempoPointsChange,
}) => {
  const [editingPointId, setEditingPointId] = useState<string | null>(null);
  const [editingBPM, setEditingBPM] = useState('');
  const [draggedPointId, setDraggedPointId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // BPM range for visualization
  const MIN_BPM = 60;
  const MAX_BPM = 200;
  const LANE_HEIGHT = 60;

  // Calculate BPM at any given time
  const getBPMAtTime = useCallback((time: number): number => {
    if (tempoPoints.length === 0) return masterBPM;
    
    // Sort points by time
    const sortedPoints = [...tempoPoints].sort((a, b) => a.time - b.time);
    
    // Find surrounding points
    let beforePoint = null;
    let afterPoint = null;
    
    for (let i = 0; i < sortedPoints.length; i++) {
      if (sortedPoints[i].time <= time) {
        beforePoint = sortedPoints[i];
      }
      if (sortedPoints[i].time > time && !afterPoint) {
        afterPoint = sortedPoints[i];
        break;
      }
    }
    
    // If no points before, use master BPM
    if (!beforePoint) return masterBPM;
    
    // If no points after, use the last point's BPM
    if (!afterPoint) return beforePoint.bpm;
    
    // Interpolate between points
    const timeDiff = afterPoint.time - beforePoint.time;
    const bpmDiff = afterPoint.bpm - beforePoint.bpm;
    const progress = (time - beforePoint.time) / timeDiff;
    
    return beforePoint.bpm + (bpmDiff * progress);
  }, [tempoPoints, masterBPM]);

  // Convert BPM to Y position
  const bpmToY = useCallback((bpm: number): number => {
    const normalizedBPM = Math.max(MIN_BPM, Math.min(MAX_BPM, bpm));
    const progress = (normalizedBPM - MIN_BPM) / (MAX_BPM - MIN_BPM);
    return LANE_HEIGHT - (progress * (LANE_HEIGHT - 20)) - 10; // 10px padding
  }, []);

  // Convert Y position to BPM
  const yToBPM = useCallback((y: number): number => {
    const progress = (LANE_HEIGHT - y - 10) / (LANE_HEIGHT - 20);
    return MIN_BPM + (progress * (MAX_BPM - MIN_BPM));
  }, []);

  // Draw tempo curve
  const drawTempoCurve = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw grid lines
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines (BPM values)
    for (let bpm = MIN_BPM; bpm <= MAX_BPM; bpm += 20) {
      const y = bpmToY(bpm);
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Draw tempo curve
    if (tempoPoints.length > 0) {
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2;
      ctx.beginPath();

      // Start from beginning with master BPM or first point
      const startBPM = tempoPoints.length > 0 && tempoPoints[0].time === 0 
        ? tempoPoints[0].bpm 
        : masterBPM;
      ctx.moveTo(0, bpmToY(startBPM));

      // Draw curve through all points
      const sortedPoints = [...tempoPoints].sort((a, b) => a.time - b.time);
      
      for (let i = 0; i < sortedPoints.length; i++) {
        const point = sortedPoints[i];
        const x = point.time * pixelsPerSecond;
        const y = bpmToY(point.bpm);
        ctx.lineTo(x, y);
      }

      // Extend to end
      if (sortedPoints.length > 0) {
        const lastPoint = sortedPoints[sortedPoints.length - 1];
        ctx.lineTo(width, bpmToY(lastPoint.bpm));
      }

      ctx.stroke();
    } else {
      // Draw flat line at master BPM
      ctx.strokeStyle = '#6b7280';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(0, bpmToY(masterBPM));
      ctx.lineTo(width, bpmToY(masterBPM));
      ctx.stroke();
    }

    // Draw current time indicator
    const currentX = currentTime * pixelsPerSecond;
    const currentBPM = getBPMAtTime(currentTime);
    const currentY = bpmToY(currentBPM);

    ctx.fillStyle = '#ef4444';
    ctx.beginPath();
    ctx.arc(currentX, currentY, 4, 0, 2 * Math.PI);
    ctx.fill();

  }, [tempoPoints, masterBPM, pixelsPerSecond, currentTime, bpmToY, getBPMAtTime]);

  // Update canvas size and redraw
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = LANE_HEIGHT;
    
    drawTempoCurve();
  }, [totalDuration, pixelsPerSecond, tempoPoints, masterBPM, currentTime, drawTempoCurve]);

  // Add tempo point
  const handleAddTempoPoint = useCallback((time: number, bpm: number) => {
    const newPoint: TempoPoint = {
      id: `tempo-${Date.now()}`,
      time: Math.max(0, Math.min(totalDuration, time)),
      bpm: Math.max(MIN_BPM, Math.min(MAX_BPM, bpm))
    };
    
    const updatedPoints = [...tempoPoints, newPoint].sort((a, b) => a.time - b.time);
    onTempoPointsChange?.(updatedPoints);
  }, [tempoPoints, totalDuration, onTempoPointsChange]);

  // Delete tempo point
  const handleDeleteTempoPoint = useCallback((pointId: string) => {
    const updatedPoints = tempoPoints.filter(p => p.id !== pointId);
    onTempoPointsChange?.(updatedPoints);
  }, [tempoPoints, onTempoPointsChange]);

  // Handle double-click to add point
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = e.clientX - rect.left;
    const clickY = e.clientY - rect.top;
    const clickTime = clickX / pixelsPerSecond;
    const clickBPM = yToBPM(clickY);
    
    handleAddTempoPoint(clickTime, clickBPM);
  }, [pixelsPerSecond, yToBPM, handleAddTempoPoint]);

  // Handle point drag
  const handlePointMouseDown = useCallback((e: React.MouseEvent, pointId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setDraggedPointId(pointId);
  }, []);

  // Handle mouse move during drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!draggedPointId || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    const time = Math.max(0, Math.min(totalDuration, mouseX / pixelsPerSecond));
    const bpm = Math.max(MIN_BPM, Math.min(MAX_BPM, yToBPM(mouseY)));

    const updatedPoints = tempoPoints.map(p => 
      p.id === draggedPointId ? { ...p, time, bpm } : p
    ).sort((a, b) => a.time - b.time);
    
    onTempoPointsChange?.(updatedPoints);
  }, [draggedPointId, totalDuration, pixelsPerSecond, yToBPM, tempoPoints, onTempoPointsChange]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    setDraggedPointId(null);
  }, []);

  // Set up global mouse events for dragging
  useEffect(() => {
    if (draggedPointId) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedPointId, handleMouseMove, handleMouseUp]);

  return (
    <div className="h-16 bg-muted/30 border-b border-border relative">
      {/* Lane Header */}
      <div className="absolute left-0 top-0 w-20 h-full bg-muted border-r border-border flex flex-col items-center justify-center">
        <div className="flex items-center gap-1">
          <Activity className="h-3 w-3 text-muted-foreground" />
          <span className="text-xs font-medium text-muted-foreground">Tempo</span>
        </div>
        <div className="text-xs text-muted-foreground mt-1">
          {Math.round(getBPMAtTime(currentTime))} BPM
        </div>
      </div>

      {/* Tempo Curve Area */}
      <div 
        ref={containerRef}
        className="absolute left-20 right-0 top-0 bottom-0 cursor-crosshair"
        onDoubleClick={handleDoubleClick}
      >
        {/* Canvas for tempo curve */}
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
        />

        {/* Tempo Points */}
        {tempoPoints.map((point) => {
          const pointX = point.time * pixelsPerSecond;
          const pointY = bpmToY(point.bpm);
          
          return (
            <div
              key={point.id}
              className="absolute group cursor-pointer"
              style={{ 
                left: pointX - 6, 
                top: pointY - 6,
                width: 12,
                height: 12
              }}
              onMouseDown={(e) => handlePointMouseDown(e, point.id)}
              onClick={() => onSeek(point.time)}
            >
              {/* Point Circle */}
              <div className="w-3 h-3 bg-primary border-2 border-background rounded-full group-hover:scale-125 transition-transform" />
              
              {/* BPM Label */}
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  {Math.round(point.bpm)}
                </Badge>
              </div>

              {/* Delete Button */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-1 -right-1 h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteTempoPoint(point.id);
                }}
              >
                <Trash2 className="h-2 w-2" />
              </Button>
            </div>
          );
        })}

        {/* BPM Scale */}
        <div className="absolute right-2 top-0 bottom-0 flex flex-col justify-between text-xs text-muted-foreground pointer-events-none">
          <span>{MAX_BPM}</span>
          <span>{(MIN_BPM + MAX_BPM) / 2}</span>
          <span>{MIN_BPM}</span>
        </div>

        {/* Instructions */}
        {tempoPoints.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center text-xs text-muted-foreground pointer-events-none">
            Double-click to add tempo points • Drag points to adjust
          </div>
        )}
      </div>
    </div>
  );
};

export default TempoMappingLane;
