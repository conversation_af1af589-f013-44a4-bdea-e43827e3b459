import React from 'react';
import { cn } from '@/lib/utils';

interface HorizontalPlayheadProps {
  currentTime: number;
  pixelsPerSecond: number;
  timelineHeight: number;
  isPlaying: boolean;
}

/**
 * HorizontalPlayhead - Vertical playhead for horizontal timeline
 * 
 * This component displays a vertical line that spans all tracks to show
 * the current playback position. Features:
 * - Vertical line spanning the full timeline height
 * - Animated when playing
 * - Draggable for seeking (future enhancement)
 * - Visual feedback for current position
 */
const HorizontalPlayhead: React.FC<HorizontalPlayheadProps> = ({
  currentTime,
  pixelsPerSecond,
  timelineHeight,
  isPlaying,
}) => {
  // Calculate playhead position
  const playheadX = currentTime * pixelsPerSecond;

  return (
    <div
      className={cn(
        "absolute top-0 pointer-events-none z-30",
        // PERFORMANCE FIX: Remove CSS transitions during playback to prevent shaking
        // CSS transitions conflict with rapid position updates during playback
        !isPlaying && "transition-all duration-75"
      )}
      style={{
        left: playheadX,
        height: timelineHeight,
        transform: 'translateX(-1px)', // Center the line
      }}
    >
      {/* Main playhead line */}
      <div 
        className="w-0.5 h-full bg-primary shadow-lg"
        style={{
          boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)',
        }}
      />
      
      {/* Playhead handle at top */}
      <div 
        className="absolute -top-1 -left-2 w-4 h-4 bg-primary rounded-full shadow-lg border-2 border-background pointer-events-auto cursor-grab"
        style={{
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
        }}
      >
        {/* Inner dot */}
        <div className="absolute inset-1 bg-background rounded-full" />
      </div>
      
      {/* Playhead handle at bottom */}
      <div 
        className="absolute -bottom-1 -left-2 w-4 h-4 bg-primary rounded-full shadow-lg border-2 border-background pointer-events-auto cursor-grab"
        style={{
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
        }}
      >
        {/* Inner dot */}
        <div className="absolute inset-1 bg-background rounded-full" />
      </div>

      {/* Glow effect when playing - PERFORMANCE FIX: Remove animate-pulse to prevent conflicts */}
      {isPlaying && (
        <div
          className="absolute inset-0 w-1 -left-0.5 bg-primary/30 blur-sm"
          style={{ height: timelineHeight }}
        />
      )}
    </div>
  );
};

export default HorizontalPlayhead;
