import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Grid3X3,
  Music,
  Clock,
  Zap,
  Settings,
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Sliders
} from 'lucide-react';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';
import { cn } from '@/lib/utils';

interface ProfessionalTimelineHeaderProps {
  masterBPM: number;
  isPlaying: boolean;
  currentTime: number;
  totalDuration: number;
  onPlay: () => void;
  onPause: () => void;
  onStop: () => void;
  onSeek: (time: number) => void;
}

/**
 * ProfessionalTimelineHeader - Professional DAW-style timeline header
 * 
 * Features:
 * - Master BPM display and controls
 * - Transport controls (play/pause/stop)
 * - Grid controls (snap, density)
 * - Timeline position display
 * - Professional appearance
 */
const ProfessionalTimelineHeader: React.FC<ProfessionalTimelineHeaderProps> = ({
  masterBPM,
  isPlaying,
  currentTime,
  totalDuration,
  onPlay,
  onPause,
  onStop,
  onSeek,
}) => {
  // Grid and snap state
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [gridDensity, setGridDensity] = useState<'1/4' | '1/8' | '1/16'>('1/4');
  const [showGrid, setShowGrid] = useState(true);
  const [editingBPM, setEditingBPM] = useState(false);
  const [bpmInputValue, setBpmInputValue] = useState(masterBPM.toString());

  // Master audio state
  const [masterVolume, setMasterVolume] = useState(80);
  const [isMasterMuted, setIsMasterMuted] = useState(false);
  const [showEffects, setShowEffects] = useState(false);

  // Format time as MM:SS.ms
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    const milliseconds = Math.floor((time % 1) * 100);
    return `${minutes}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`;
  }, []);

  // Handle BPM change
  const handleBPMChange = useCallback((newBPM: number) => {
    if (newBPM >= 60 && newBPM <= 200) {
      timelineCoordinatorEnhanced.setMasterBPM(newBPM);
    }
  }, []);

  // Handle BPM input
  const handleBPMInputSubmit = useCallback(() => {
    const newBPM = parseFloat(bpmInputValue);
    if (!isNaN(newBPM)) {
      handleBPMChange(newBPM);
    }
    setBpmInputValue(masterBPM.toString());
    setEditingBPM(false);
  }, [bpmInputValue, masterBPM, handleBPMChange]);

  // Handle BPM input key press
  const handleBPMInputKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleBPMInputSubmit();
    } else if (e.key === 'Escape') {
      setBpmInputValue(masterBPM.toString());
      setEditingBPM(false);
    }
  }, [handleBPMInputSubmit, masterBPM]);

  // Handle master volume change
  const handleMasterVolumeChange = useCallback((value: number[]) => {
    const newVolume = value[0];
    setMasterVolume(newVolume);
    timelineCoordinatorEnhanced.setMasterVolume(newVolume / 100);

    // Unmute if volume is increased
    if (isMasterMuted && newVolume > 0) {
      setIsMasterMuted(false);
    }
  }, [isMasterMuted]);

  // Handle master mute toggle
  const handleMasterMuteToggle = useCallback(() => {
    const newMuteState = !isMasterMuted;
    setIsMasterMuted(newMuteState);

    if (newMuteState) {
      timelineCoordinatorEnhanced.setMasterVolume(0);
    } else {
      timelineCoordinatorEnhanced.setMasterVolume(masterVolume / 100);
    }
  }, [isMasterMuted, masterVolume]);

  // Calculate current beat position
  const beatsPerSecond = masterBPM / 60;
  const currentBeat = Math.floor(currentTime * beatsPerSecond);
  const currentBar = Math.floor(currentBeat / 4) + 1;
  const currentBeatInBar = (currentBeat % 4) + 1;

  return (
    <div className="h-12 bg-muted/50 border-b border-border px-4 flex items-center justify-between">
      {/* Left Section - Transport Controls */}
      <div className="flex items-center gap-2">
        {/* Transport Buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => onSeek(0)}
            title="Go to Start"
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          
          <Button
            variant={isPlaying ? "default" : "ghost"}
            size="icon"
            className="h-8 w-8"
            onClick={isPlaying ? onPause : onPlay}
            title={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={onStop}
            title="Stop"
          >
            <Square className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => onSeek(totalDuration)}
            title="Go to End"
          >
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Position Display */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm font-mono font-medium min-w-[80px]">
              {formatTime(currentTime)}
            </span>
          </div>
          
          <div className="flex items-center gap-1">
            <Music className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm font-mono font-medium min-w-[40px]">
              {currentBar}.{currentBeatInBar}
            </span>
          </div>
        </div>
      </div>

      {/* Center Section - Master BPM */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label className="text-sm font-medium">Master BPM</Label>
          {editingBPM ? (
            <Input
              type="number"
              value={bpmInputValue}
              onChange={(e) => setBpmInputValue(e.target.value)}
              onBlur={handleBPMInputSubmit}
              onKeyDown={handleBPMInputKeyPress}
              className="w-16 h-8 text-center font-mono"
              min="60"
              max="200"
              step="0.1"
              autoFocus
            />
          ) : (
            <Badge
              variant="secondary"
              className="cursor-pointer hover:bg-secondary/80 transition-colors font-mono text-sm px-3 py-1"
              onClick={() => {
                setEditingBPM(true);
                setBpmInputValue(masterBPM.toString());
              }}
            >
              {masterBPM.toFixed(1)}
            </Badge>
          )}
          
          {/* BPM Adjustment Buttons */}
          <div className="flex flex-col gap-0">
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 p-0"
              onClick={() => handleBPMChange(masterBPM + 0.1)}
              title="Increase BPM"
            >
              <div className="w-0 h-0 border-l-2 border-r-2 border-b-2 border-l-transparent border-r-transparent border-b-foreground" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 p-0"
              onClick={() => handleBPMChange(masterBPM - 0.1)}
              title="Decrease BPM"
            >
              <div className="w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-foreground" />
            </Button>
          </div>
        </div>
      </div>

      {/* Right Section - Grid Controls */}
      <div className="flex items-center gap-2">
        {/* Grid Toggle */}
        <div className="flex items-center gap-2">
          <Label className="text-sm">Grid</Label>
          <Switch
            checked={showGrid}
            onCheckedChange={setShowGrid}
            className="scale-75"
          />
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Snap Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={snapToGrid ? "default" : "ghost"}
            size="icon"
            className="h-8 w-8"
            onClick={() => setSnapToGrid(!snapToGrid)}
            title={snapToGrid ? "Snap to Grid: ON" : "Snap to Grid: OFF"}
          >
            <Zap className={cn("h-4 w-4", snapToGrid && "text-primary-foreground")} />
          </Button>
        </div>

        {/* Grid Density */}
        <div className="flex items-center gap-1">
          {(['1/4', '1/8', '1/16'] as const).map((density) => (
            <Button
              key={density}
              variant={gridDensity === density ? "default" : "ghost"}
              size="sm"
              className="h-7 px-2 text-xs font-mono"
              onClick={() => setGridDensity(density)}
            >
              {density}
            </Button>
          ))}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Master Volume */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={handleMasterMuteToggle}
            title={isMasterMuted ? "Unmute Master" : "Mute Master"}
          >
            {isMasterMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
          </Button>

          <div className="flex items-center gap-1">
            <input
              type="range"
              min="0"
              max="100"
              value={isMasterMuted ? 0 : masterVolume}
              onChange={(e) => handleMasterVolumeChange([parseInt(e.target.value)])}
              className="w-16 h-1 bg-border rounded-lg appearance-none cursor-pointer"
              title="Master Volume"
            />
            <span className="text-xs font-mono w-8 text-center">
              {isMasterMuted ? 0 : masterVolume}
            </span>
          </div>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Effects Toggle */}
        <Button
          variant={showEffects ? "default" : "ghost"}
          size="icon"
          className="h-8 w-8"
          onClick={() => setShowEffects(!showEffects)}
          title="Master Effects"
        >
          <Sliders className="h-4 w-4" />
        </Button>

        {/* Settings */}
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          title="Timeline Settings"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default ProfessionalTimelineHeader;
