import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Track } from '@/types/api/tracks';
import { cn } from '@/lib/utils';
import { Play, Pause, Volume2, VolumeX, Settings, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Import services - OPTIMIZED: Use same services as fast vertical timeline
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';
import enhancedToneAudioEngine from '../../services/audio/EnhancedToneAudioEngine';
import { cleanupWaveSurferInstanceForTrack } from '@/utils/wavesurferCleanup';
import { useTimelineStore } from '../../stores/TimelineStore';
import TrackContextMenu from '../core/TrackContextMenu';

// Import professional timeline components
import BeatGridRegions from '../editors/BeatGridRegions';
import SegmentRegions from '../editors/SegmentRegions';
import CuePointRegions from '../editors/CuePointRegions';
import LoopRegions from '../editors/LoopRegions';
import BeatGridDisplay from '@/components/tracks/ui/BeatGridDisplay';

// Declare the global trackLoadingMap to prevent TypeScript errors (same as vertical timeline)
declare global {
  interface Window {
    trackLoadingMap?: Map<string, boolean>;
    trackLoadingTimestamps?: Map<string, number>;
  }
}

interface HorizontalTrackLaneProps {
  track: Track;
  isSelected: boolean;
  onSelect: () => void;
  pixelsPerSecond: number;
  trackHeight: number;
  currentTime: number;
  isPlaying: boolean;
  masterBPM: number;
  snapToGrid?: boolean;
}

/**
 * HorizontalTrackLane - Individual horizontal track lane component
 * 
 * This component represents a single track in the horizontal timeline:
 * - Shows track waveform horizontally
 * - Displays track info (title, artist, BPM, key)
 * - Provides track controls (play/pause, volume, settings)
 * - Shows beat grid alignment
 * - Handles track selection and interaction
 */
const HorizontalTrackLane: React.FC<HorizontalTrackLaneProps> = ({
  track,
  isSelected,
  onSelect,
  pixelsPerSecond,
  trackHeight,
  currentTime,
  isPlaying,
  masterBPM,
  snapToGrid = true,
}) => {
  // Store access - SAME AS VERTICAL TIMELINE
  const { removeTrack } = useTimelineStore();

  // Refs - OPTIMIZED: Match vertical timeline structure
  const containerRef = useRef<HTMLDivElement>(null);
  const waveformRef = useRef<HTMLDivElement>(null);
  const trackIdRef = useRef<string | null>(null);

  // State - OPTIMIZED: Use same state management as fast vertical timeline
  const [isTrackPlaying, setIsTrackPlaying] = useState(false);
  const [trackVolume, setTrackVolume] = useState(track.volume || 100);
  const [isMuted, setIsMuted] = useState(false); // ADDED: Missing mute state
  const [isWaveformLoaded, setIsWaveformLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [contextMenuTime, setContextMenuTime] = useState(0); // ADDED: For context menu

  // Debouncing ref to prevent rapid successive loads (same as vertical timeline)
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Drag state for horizontal track movement
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [initialStartTime, setInitialStartTime] = useState(0);

  // PROFESSIONAL TRACK POSITIONING - Use beat-aligned positioning like Ableton
  const trackWidth = (track.duration || 0) * pixelsPerSecond;

  // CRITICAL FIX: Use beat-aligned positioning instead of basic time positioning
  // This ensures tracks snap to beat grid like professional DAWs
  const trackStartX = useMemo(() => {
    if (!track.startTime && track.startTime !== 0) {
      return 0; // Default to start if no startTime
    }

    // TODO: Integrate with beat alignment service for professional positioning
    // For now, use time-based positioning but this should be beat-aligned
    const timeBasedPosition = track.startTime * pixelsPerSecond;

    // PROFESSIONAL DAW FIX: Snap to beat grid like Ableton/Logic
    if (snapToGrid && masterBPM) {
      const beatsPerSecond = masterBPM / 60;
      const beatDuration = 1 / beatsPerSecond;
      const nearestBeat = Math.round(track.startTime / beatDuration) * beatDuration;
      const snappedPosition = nearestBeat * pixelsPerSecond;

      console.log(`[HorizontalTrackLane] BEAT GRID SNAP: track ${track.id}, startTime=${track.startTime}s, masterBPM=${masterBPM}, beatDuration=${beatDuration.toFixed(3)}s, snappedPosition=${snappedPosition}px`);
      return snappedPosition;
    }

    return timeBasedPosition;
  }, [track.startTime, pixelsPerSecond, snapToGrid, masterBPM]);

  // REMOVED: Console log that was causing infinite re-renders
  // Only log positioning changes when values actually change

  // OPTIMIZED: Use stable track ID and audioUrl to prevent infinite re-renders (same as vertical timeline)
  const trackId = track.id?.toString() || `track-${track.title}`;
  const trackAudioUrl = track.audioUrl || track.file_path || '';
  const trackTitle = track.title;

  // REMOVED: useLayoutEffect - vertical timeline doesn't have this, so we don't need it either

  // OPTIMIZED: Load the track using the SAME strategy as fast vertical timeline
  useEffect(() => {
    console.log(`🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK ${trackId} - ${trackTitle} 🔥🔥🔥`);
    console.log(`🔍 [HorizontalTrackLane] TRACK DATA:`, {
      id: track.id,
      title: track.title,
      audioUrl: track.audioUrl,
      color: track.color,
      hasAudioUrl: !!trackAudioUrl,
      trackStructure: Object.keys(track),
      processedCorrectly: !!(track.id && track.title && trackAudioUrl && track.color)
    });

    // CRITICAL DEBUG: Check if track is in audio engine
    const isInAudioEngine = enhancedToneAudioEngine.hasTrack(trackId);
    const hasWaveform = timelineCoordinatorEnhanced.hasWaveform(trackId);
    console.log(`🎵 [HorizontalTrackLane] AUDIO ENGINE STATUS:`, {
      trackId,
      isInAudioEngine,
      hasWaveform,
      audioEngineTrackCount: enhancedToneAudioEngine.getTrackCount?.() || 'unknown'
    });

    if (!waveformRef.current) {
      console.log(`[HorizontalTrackLane] No waveformRef.current, returning early for track ${trackId}`);
      return;
    }

    // REMOVED: Premature DOM attachment check - let container mount naturally
    // The container will be attached to DOM after the component renders

    // Store the current track ID for cleanup
    trackIdRef.current = trackId;

    console.log(`[HorizontalTrackLane] Starting mount effect for track ${trackId} (${trackTitle})`);

    // CRITICAL FIX: Validate audioUrl before proceeding
    if (!trackAudioUrl) {
      console.error(`[HorizontalTrackLane] No audioUrl found for track ${trackId} (${trackTitle}). Track data:`, {
        audioUrl: track.audioUrl,
        file_path: track.file_path,
        filepath: track.filepath,
        filePath: track.filePath,
        trackStructure: Object.keys(track)
      });
      setHasError(true);
      return;
    }

    // CRITICAL FIX: Enhanced duplicate loading prevention (same as vertical timeline)
    if (!window.trackLoadingMap) {
      window.trackLoadingMap = new Map();
    }

    if (!window.trackLoadingTimestamps) {
      window.trackLoadingTimestamps = new Map();
    }

    // Check if this track is already being loaded
    const isCurrentlyLoading = window.trackLoadingMap.get(trackId);
    const lastLoadTime = window.trackLoadingTimestamps.get(trackId) || 0;
    const timeSinceLastLoad = Date.now() - lastLoadTime;

    // Prevent rapid re-loading (within 2 seconds) - same as vertical timeline
    if (isCurrentlyLoading || timeSinceLastLoad < 2000) {
      console.log(`[HorizontalTrackLane] Track ${trackId} is already being loaded or was loaded recently (${timeSinceLastLoad}ms ago), skipping duplicate load`);
      return;
    }

    // Check if waveform already exists using coordinator (same as vertical timeline)
    const hasExistingWaveform = timelineCoordinatorEnhanced.hasWaveform(trackId);

    if (hasExistingWaveform) {
      console.log(`[HorizontalTrackLane] Waveform already exists for track ${trackId} (${trackTitle}), updating state and skipping load`);
      if (!isWaveformLoaded) {
        setIsWaveformLoaded(true);
        setIsLoading(false);
      }
      return;
    }

    console.log(`[HorizontalTrackLane] Loading track ${trackId} (${trackTitle})`);

    // Clear any existing timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }

    // Mark this track as being loaded with timestamp
    window.trackLoadingMap.set(trackId, true);
    window.trackLoadingTimestamps.set(trackId, Date.now());
    setIsLoading(true);

    let isMounted = true;
    let retryCount = 0;
    const maxRetries = 2;

    const loadTrack = async () => {
      try {
        // SEQUENTIAL DEBUG: Track loading start
        console.log(`SEQUENTIAL_TRACK_LOAD_START: Track ${trackId} (${trackTitle})`);

        // First, clean up any existing WaveSurfer instances for this track
        console.log(`[HorizontalTrackLane] Cleaning up any existing WaveSurfer instances for track ${trackId}`);
        cleanupWaveSurferInstanceForTrack(trackId);

        // Also clean up using the coordinator
        if (hasExistingWaveform) {
          console.log(`[HorizontalTrackLane] Destroying existing waveform via coordinator for track ${trackId}`);
          await timelineCoordinatorEnhanced.unloadTrack(trackId);
        }

        // Clear any existing content in the waveform container
        if (waveformRef.current) {
          const container = waveformRef.current;
          const existingWaveformElements = container.querySelectorAll('wave, canvas, .wavesurfer-wrapper');
          existingWaveformElements.forEach(element => {
            element.remove();
          });

          if (existingWaveformElements.length > 0) {
            console.log(`[HorizontalTrackLane] Removed ${existingWaveformElements.length} existing waveform elements for track ${trackId}`);
          }
        }

        // CRITICAL FIX: Use loadTrackWithBeatAlignment for beat grid integration
        console.log(`🎵🎵🎵 [HorizontalTrackLane] CALLING LOADTRACKWITHBEATALIGNMENT FOR TRACK ${trackId} (BEAT GRID ENABLED) 🎵🎵🎵`);
        const loadResult = await timelineCoordinatorEnhanced.loadTrackWithBeatAlignment(track, waveformRef.current!);

        if (loadResult.success) {
          console.log(`🎯 [HorizontalTrackLane] Track ${trackId} loaded with beat alignment successfully`);
          if (loadResult.positioning) {
            console.log(`🎯 [HorizontalTrackLane] Beat positioning quality: ${loadResult.positioning.positioning_quality}`);
          }
        } else {
          console.warn(`⚠️ [HorizontalTrackLane] Track ${trackId} beat alignment failed, falling back to normal load`);
          await timelineCoordinatorEnhanced.loadTrack(track, waveformRef.current!);
        }

        // SEQUENTIAL DEBUG: Track loading complete
        const trackLoaded = enhancedToneAudioEngine.hasTrack(trackId);
        const hasWaveform = timelineCoordinatorEnhanced.hasWaveform(trackId);
        console.log(`SEQUENTIAL_TRACK_LOAD_COMPLETE: Track ${trackId} (${trackTitle}) - Audio: ${trackLoaded}, Waveform: ${hasWaveform}, Ready: ${trackLoaded && hasWaveform}`);

        // Only update state if the component is still mounted
        if (isMounted) {
          console.log(`[HorizontalTrackLane-${trackId}] DEBUG: Setting isWaveformLoaded=true, isLoading=false`);
          setIsWaveformLoaded(true);
          setIsLoading(false);
          setHasError(false);

          // Clear loading state from global tracking
          window.trackLoadingMap.set(trackId, false);

          // CRITICAL FIX: Do NOT sync WaveSurfer zoom with timeline zoom
          // WaveSurfer should ALWAYS show complete track, timeline zoom only affects grid/ruler
          console.log(`[HorizontalTrackLane] 🔒 PROFESSIONAL DAW MODE: Track ${trackId} shows complete waveform - no zoom sync needed`);

          console.log(`✅✅✅ [HorizontalTrackLane] Successfully loaded track ${trackId} (${trackTitle}) ✅✅✅`);

          // CRITICAL DEBUG: Verify track is now in audio engine
          const finalIsInAudioEngine = enhancedToneAudioEngine.hasTrack(trackId);
          const finalHasWaveform = timelineCoordinatorEnhanced.hasWaveform(trackId);
          console.log(`🎵 [HorizontalTrackLane] FINAL AUDIO ENGINE STATUS:`, {
            trackId,
            finalIsInAudioEngine,
            finalHasWaveform,
            audioEngineTrackCount: enhancedToneAudioEngine.getTrackCount?.() || 'unknown'
          });
        }
      } catch (error) {
        console.error(`[HorizontalTrackLane] Error loading track ${trackId}:`, error);

        // Retry loading if we haven't exceeded the max retries
        if (retryCount < maxRetries && isMounted) {
          retryCount++;
          console.log(`[HorizontalTrackLane] Retrying load for track ${trackId} (attempt ${retryCount}/${maxRetries})`);
          setTimeout(loadTrack, 1000); // Wait 1 second before retrying
        } else {
          // Mark the track as not loading so we can try again later
          window.trackLoadingMap.set(trackId, false);
          if (window.trackLoadingTimestamps) {
            window.trackLoadingTimestamps.delete(trackId);
          }
          setIsLoading(false);
          setHasError(true);
        }
      }
    };

    // FIXED: Use EXACT SAME simple approach as vertical timeline (no container readiness checks!)
    // Start loading the track with a small delay to debounce rapid calls (same as vertical timeline)
    loadTimeoutRef.current = setTimeout(() => {
      loadTrack();
    }, 100); // EXACT SAME as vertical timeline - 100ms debounce

    // Clean up when the component unmounts (same as vertical timeline)
    return () => {
      isMounted = false;
      console.log(`[HorizontalTrackLane] Unmounting track ${trackId}`);

      // Clear any pending timeout
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
        loadTimeoutRef.current = null;
      }

      // FIXED: Don't unload tracks during React StrictMode double-mounting
      const isStrictModeCleanup = import.meta.env.DEV;

      if (!isStrictModeCleanup && trackIdRef.current === trackId) {
        console.log(`[HorizontalTrackLane] Real unmount - unloading track ${trackId}`);
        timelineCoordinatorEnhanced.unloadTrack(trackId);
        cleanupWaveSurferInstanceForTrack(trackId);
      } else {
        console.log(`[HorizontalTrackLane] StrictMode cleanup or track ID change - skipping unload for track ${trackId}`);
        if (window.trackLoadingMap) {
          window.trackLoadingMap.set(trackId, false);
        }
        if (window.trackLoadingTimestamps) {
          window.trackLoadingTimestamps.delete(trackId);
        }
      }
    };
  }, [trackId, trackAudioUrl, trackTitle]); // OPTIMIZED: Use stable dependencies like vertical timeline

  // ARCHITECTURAL FIX: WaveSurfer should NOT reposition based on track movement
  // WaveSurfer is just audio visualization - only the track container should move on the timeline
  // The audio engine (TrackManager/TimelineCoordinator) handles actual playback positioning
  //
  // REMOVED: All WaveSurfer seeking based on track.startTime changes
  // This was causing the "waveform movement" issue and is architecturally incorrect
  //
  // WaveSurfer will be positioned by the audio engine during actual playback,
  // not by React effects responding to track position changes

  // Handle track play/pause
  const handlePlayPause = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!track.id) return;

    // CRITICAL ARCHITECTURAL FIX: Use timeline coordinator for playback control
    // WaveSurfer is VISUALIZATION ONLY - audio playback handled by Tone.js
    if (isTrackPlaying) {
      timelineCoordinatorEnhanced.pause();
      setIsTrackPlaying(false);
      console.log(`[HorizontalTrackLane] 🔒 PROFESSIONAL DAW MODE: Paused timeline via coordinator for track ${track.id}`);
    } else {
      timelineCoordinatorEnhanced.play();
      setIsTrackPlaying(true);
      console.log(`[HorizontalTrackLane] 🔒 PROFESSIONAL DAW MODE: Started timeline via coordinator for track ${track.id}`);
    }
  };

  // Handle volume change
  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value);
    setTrackVolume(newVolume);

    if (track.id) {
      // CRITICAL ARCHITECTURAL FIX: Use timeline coordinator for volume control
      // WaveSurfer is VISUALIZATION ONLY - audio volume handled by Tone.js
      timelineCoordinatorEnhanced.updateTrackVolume(track.id.toString(), newVolume);
      console.log(`[HorizontalTrackLane] 🔒 PROFESSIONAL DAW MODE: Volume ${newVolume} set via coordinator for track ${track.id}`);
    }
  };

  // Handle track settings
  const handleSettings = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Open track settings panel in right sidebar
    console.log('Open track settings for:', track.title);
  };

  // ADDED: Handle mute toggle (SAME AS VERTICAL TIMELINE)
  const handleMuteToggle = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the click from selecting the track
    // Toggle the mute state
    const newMuteState = !isMuted;
    setIsMuted(newMuteState);
    timelineCoordinatorEnhanced.setTrackMuted(track.id.toString(), newMuteState);
  };

  // ADDED: Handle track removal (SAME AS VERTICAL TIMELINE)
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    removeTrack(track.id.toString());
  };

  // Calculate BPM compatibility
  const bpmDifference = track.bpm ? Math.abs(track.bpm - masterBPM) : 0;
  const bpmCompatibility = bpmDifference <= 3 ? 'excellent' : 
                          bpmDifference <= 6 ? 'good' : 
                          bpmDifference <= 10 ? 'fair' : 'poor';

  const bpmColor = bpmCompatibility === 'excellent' ? 'bg-green-500/20 text-green-400' :
                   bpmCompatibility === 'good' ? 'bg-blue-500/20 text-blue-400' :
                   bpmCompatibility === 'fair' ? 'bg-yellow-500/20 text-yellow-400' :
                   'bg-red-500/20 text-red-400';

  // ADDED: Handle context menu (SAME AS VERTICAL TIMELINE)
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();

    // Calculate time position for context menu
    if (track.duration) {
      const rect = containerRef.current?.getBoundingClientRect();
      if (rect) {
        const clickX = e.clientX - rect.left;
        const clickTime = (clickX / trackWidth) * track.duration;
        setContextMenuTime(clickTime);
      }
    }
  };

  // Handle track drag start
  const handleTrackDragStart = useCallback((e: React.MouseEvent) => {
    // Only allow drag on track header, not on waveform
    const target = e.target as HTMLElement;
    if (!target.closest('.track-header')) return;

    e.preventDefault();
    e.stopPropagation();

    const startX = e.clientX;
    const initialTime = track.startTime || 0;

    setIsDragging(true);
    setDragStartX(startX);
    setInitialStartTime(initialTime);

    // CRITICAL FIX: Optimize drag performance with throttling and non-blocking updates
    let lastSnapTime = 0;
    const snapThrottleMs = 50; // Throttle snap calculations to 20fps for performance

    const handleDragMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaTime = deltaX / pixelsPerSecond;
      let newStartTime = Math.max(0, initialTime + deltaTime);

      // CRITICAL FIX: Apply beat snapping with throttling to prevent stuttering
      if (snapToGrid) {
        const now = Date.now();
        if (now - lastSnapTime > snapThrottleMs) {
          lastSnapTime = now;

          // Non-blocking snap calculation
          timelineCoordinatorEnhanced.snapPositionToBeat(
            track.id.toString(),
            newStartTime
          ).then(snapResult => {
            if (snapResult.snapped) {
              // Only update if the user is still dragging and position hasn't changed much
              const currentDelta = moveEvent.clientX - startX;
              const currentTime = Math.max(0, initialTime + currentDelta / pixelsPerSecond);

              if (Math.abs(currentTime - newStartTime) < 0.1) { // Within 100ms tolerance
                const { updateTrack } = useTimelineStore.getState();
                updateTrack(track.id.toString(), { startTime: snapResult.snapped_position });
              }
            }
          }).catch(error => {
            console.warn('[HorizontalTrackLane] Beat snapping failed:', error);
          });
        }
      }

      // PROFESSIONAL DAW FIX: Always update position immediately for smooth dragging
      const { updateTrack } = useTimelineStore.getState();
      updateTrack(track.id.toString(), { startTime: newStartTime });

      // CRITICAL: Also update the coordinator's internal track reference to prevent reset
      const coordinatorTrack = timelineCoordinatorEnhanced.tracks.find(t => t.id.toString() === track.id.toString());
      if (coordinatorTrack) {
        (coordinatorTrack as any).startTime = newStartTime;
      }
    };

    const handleDragEnd = () => {
      setIsDragging(false);

      // ARCHITECTURAL FIX: No WaveSurfer re-sync needed
      // WaveSurfer position is handled by the audio engine during playback,
      // not by track position changes on the timeline grid

      // Remove global mouse event listeners
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
    };

    // Add global mouse event listeners
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  }, [pixelsPerSecond, track.id, track.startTime]);

  // Cleanup any remaining event listeners on unmount
  useEffect(() => {
    return () => {
      // Clean up any potential remaining listeners
      setIsDragging(false);
    };
  }, []);

  return (
    <TrackContextMenu
      trackId={trackId}
      clickTime={contextMenuTime}
    >
      <div
        ref={containerRef}
        className={cn(
          "absolute border-b border-border/50 transition-all duration-200 cursor-pointer group overflow-hidden",
          // CRITICAL FIX: Transparent background to show grid behind tracks
          isSelected && "ring-2 ring-primary ring-inset",
          "hover:bg-accent/5"
        )}
        style={{
          left: trackStartX,
          width: trackWidth,
          height: trackHeight,
          // CRITICAL FIX: Ensure tracks are above grid but allow grid visibility
          zIndex: 5,
        }}
        onClick={onSelect}
        onContextMenu={handleContextMenu}
        onMouseDown={handleTrackDragStart}
      >
      {/* Track Header - Info Only */}
      <div className="track-header absolute top-0 left-0 right-0 h-12 bg-background/40 backdrop-blur-sm border-b border-border/30 px-3 flex items-center z-10 cursor-move">
        {/* Track Info */}
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <div className="min-w-0 flex-1">
            <div className="text-sm font-medium truncate">{track.title}</div>
            <div className="text-xs text-muted-foreground truncate">{track.artist}</div>
          </div>

          {/* BPM Badge */}
          {track.bpm && (
            <Badge variant="outline" className={cn("text-xs", bpmColor)}>
              {track.bpm} BPM
            </Badge>
          )}

          {/* Key Badge */}
          {track.key && (
            <Badge variant="outline" className="text-xs">
              {track.key}
            </Badge>
          )}
        </div>
      </div>

      {/* Waveform Container - CRITICAL FIX: Transparent background to show grid */}
      <div className="absolute top-12 left-0 right-0 bottom-8 overflow-hidden">
        <div
          ref={waveformRef}
          className="w-full h-full relative overflow-hidden"
          style={{
            position: 'relative',
            zIndex: 2,
            // CRITICAL FIX: Transparent background to allow grid visibility
            backgroundColor: 'transparent',
            // PROFESSIONAL DAW FIX: Disable ALL pointer events on waveform container
            pointerEvents: 'none'
          }}
          data-track-id={track.id?.toString()}
          data-waveform-container="true"
        >
          {/* Professional Beat Grid Regions - SAME AS VERTICAL TIMELINE */}
          <BeatGridRegions trackId={track.id.toString()} />

          {/* CRITICAL FIX: Individual Track Beat Grid Display */}
          {(() => {
            const beatGrid = timelineCoordinatorEnhanced.getTrackBeatGrid(track.id.toString());
            const beatTimes = beatGrid?.beat_times ?
              (Array.isArray(beatGrid.beat_times) ? beatGrid.beat_times : JSON.parse(beatGrid.beat_times)) :
              [];

            return beatTimes.length > 0 ? (
              <BeatGridDisplay
                beatTimes={beatTimes}
                trackDuration={track.duration || 0}
                trackId={track.id}
                pxPerSecond={pixelsPerSecond}
                viewportStart={0}
                viewportWidthSec={track.duration || 0}
                barColor="rgba(255, 255, 255, 0.4)"
                accentColor="rgba(255, 255, 255, 0.8)"
                showNumbers={false}
                zoomLevel={pixelsPerSecond / 50} // Normalize zoom level
              />
            ) : null;
          })()}

          {/* Segment Regions - SAME AS VERTICAL TIMELINE */}
          <SegmentRegions trackId={track.id.toString()} />

          {/* Cue Point Regions - SAME AS VERTICAL TIMELINE */}
          <CuePointRegions trackId={track.id.toString()} />

          {/* Loop Regions - SAME AS VERTICAL TIMELINE */}
          <LoopRegions trackId={track.id.toString()} />
        </div>

        {/* Loading/Error Overlays - Show on top of container, don't replace it */}
        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted/50 text-muted-foreground text-sm">
            <div className="text-center">
              <div>Failed to load waveform</div>
              <div className="text-xs mt-1">Audio will still play</div>
            </div>
          </div>
        )}

        {isLoading && !hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted/20 text-muted-foreground text-sm">
            <div className="text-center">
              <div className="animate-pulse">Loading waveform...</div>
              <div className="text-xs mt-1">Please wait</div>
            </div>
          </div>
        )}
      </div>

      {/* Track Footer - Beat Grid Info */}
      <div className="absolute bottom-0 left-0 right-0 h-8 bg-background/80 backdrop-blur-sm border-t border-border/30 px-3 flex items-center justify-between text-xs text-muted-foreground">
        <div className="flex items-center gap-2">
          <span>Duration: {Math.round(track.duration || 0)}s</span>
          {track.stretchRatio && track.stretchRatio !== 1 && (
            <Badge variant="outline" className="text-xs">
              {(track.stretchRatio * 100).toFixed(1)}% speed
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {track.beatAlignment && (
            <Badge variant="outline" className="text-xs">
              {track.beatAlignment.sync_quality}
            </Badge>
          )}
          <span>Start: {Math.round(track.startTime || 0)}s</span>
        </div>
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-0 left-0 w-1 h-full bg-primary" />
      )}
    </div>
    </TrackContextMenu>
  );
};

export default HorizontalTrackLane;
