import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Track } from '@/types/api/tracks';
import { Transition } from '@/types/api/transitions';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

// Import horizontal-specific components
import HorizontalTrackLane from './HorizontalTrackLane';
import HorizontalPlayhead from './HorizontalPlayhead';
import ProfessionalTimelineRuler from './ProfessionalTimelineRuler';
import ProfessionalTimelineGrid from './ProfessionalTimelineGrid';
import TimelineLoopBrackets from './TimelineLoopBrackets';
import MarkerLane from './MarkerLane';
import TempoMappingLane from './TempoMappingLane';

// Import services
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';
import { useTimelineStore } from '../../stores/TimelineStore';

// OPTIMIZED: Import performance monitoring (same as vertical timeline)
import { TimelinePerformanceMonitor, batchDOMOperations, throttle } from '../../utils/performance';
import enhancedToneAudioEngine from '../../services/audio/EnhancedToneAudioEngine';

interface HorizontalTimelineMainProps {
  tracks: Track[];
  transitions: Record<string, Transition>;
  currentTime: number;
  isPlaying: boolean;
  selectedTrackId: string | null;
  onTrackSelect: (trackId: string) => void;
  onSeek: (time: number) => void;
  totalDuration: number;
}

/**
 * HorizontalTimelineMain - Main horizontal timeline component
 * 
 * This component provides a horizontal timeline layout where:
 * - Tracks are arranged as horizontal lanes (one per row)
 * - Time flows from left to right
 * - Vertical time ruler on the left shows time markers
 * - Vertical playhead spans all tracks
 * - Each track lane shows waveform, regions, and controls
 */
const HorizontalTimelineMain: React.FC<HorizontalTimelineMainProps> = ({
  tracks,
  transitions,
  currentTime,
  isPlaying,
  selectedTrackId,
  onTrackSelect,
  onSeek,
  totalDuration,
}) => {
  // Timeline viewport state
  const [pixelsPerSecond, setPixelsPerSecond] = useState(1); // PROFESSIONAL DAW: Start at 1px for complete overview
  const [scrollLeft, setScrollLeft] = useState(0);
  const [viewportWidth, setViewportWidth] = useState(0);

  // PERFORMANCE OPTIMIZATION: Track zoom state to prevent unnecessary updates
  const lastZoomLevel = useRef(1);
  const isZooming = useRef(false);

  // Grid control state
  const [showGrid, setShowGrid] = useState(true);
  const [gridDensity, setGridDensity] = useState<'1/4' | '1/8' | '1/16'>('1/4');
  const [snapToGrid, setSnapToGrid] = useState(true);

  // Scrolling state
  const [isSpacePressed, setIsSpacePressed] = useState(false);
  const [isDraggingTimeline, setIsDraggingTimeline] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartScrollLeft, setDragStartScrollLeft] = useState(0);

  // Refs
  const timelineRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // OPTIMIZED: Performance monitoring (same as vertical timeline)
  const performanceMonitor = useRef<TimelinePerformanceMonitor | null>(null);

  // Constants
  const TRACK_HEIGHT = 120; // Height of each track lane
  const MIN_PIXELS_PER_SECOND = 1; // CRITICAL FIX: Allow extreme zoom out for complete timeline overview
  const MAX_PIXELS_PER_SECOND = 200;

  // Get timeline loop state
  const {
    isTimelineLooping,
    timelineLoopStart,
    timelineLoopEnd,
    setTimelineLooping,
    setTimelineLoopRegion,
    toggleTimelineLoop
  } = useTimelineStore();

  // Timeline markers and tempo points state
  const [markers, setMarkers] = useState<Array<{id: string; time: number; label: string; color: string}>>([]);
  const [tempoPoints, setTempoPoints] = useState<Array<{id: string; time: number; bpm: number}>>([]);

  // Get master BPM for grid calculations (FIXED: Move before usage)
  const masterBPM = timelineCoordinatorEnhanced.getMasterBPM() || 120;

  // Calculate timeline dimensions with minimum duration for empty timeline
  const MIN_TIMELINE_DURATION = 60; // 60 seconds minimum for empty timeline

  // CRITICAL FIX: Calculate actual timeline extent including all track positions
  const actualTimelineExtent = Math.max(
    totalDuration,
    MIN_TIMELINE_DURATION,
    // Include all track end positions to ensure grid/ruler cover everything
    ...tracks.map(track => (track.startTime || 0) + (track.duration || 0))
  );

  const effectiveDuration = actualTimelineExtent;
  const timelineWidth = effectiveDuration * pixelsPerSecond; // Full timeline width
  const timelineHeight = Math.max(tracks.length * TRACK_HEIGHT, 200); // Minimum height for empty timeline

  // PROFESSIONAL DAW FIX: Calculate viewport-aware dimensions for grid/ruler
  // Grid and ruler should always extend to cover the entire visible area, not just content
  const viewportDuration = viewportWidth / pixelsPerSecond; // How much time is visible in viewport
  const gridRulerDuration = Math.max(effectiveDuration, scrollLeft / pixelsPerSecond + viewportDuration * 1.5); // Extend beyond viewport
  const gridRulerWidth = Math.max(timelineWidth, viewportWidth + scrollLeft + viewportWidth); // Always cover viewport + buffer

  // REMOVED: Console log that was causing infinite re-renders
  // Only log state changes when debugging specific issues
  const beatsPerSecond = masterBPM / 60;
  const pixelsPerBeat = pixelsPerSecond / beatsPerSecond;

  // OPTIMIZED: Initialize performance monitoring (same as vertical timeline)
  useEffect(() => {
    if (!performanceMonitor.current) {
      performanceMonitor.current = TimelinePerformanceMonitor.getInstance();
      performanceMonitor.current.startMonitoring();
      console.log('[HorizontalTimelineMain] Performance monitoring started');
    }

    return () => {
      if (performanceMonitor.current) {
        performanceMonitor.current.stopMonitoring();
        console.log('[HorizontalTimelineMain] Performance monitoring stopped');
      }
    };
  }, []);

  // Handle viewport resize
  useEffect(() => {
    const updateViewportWidth = () => {
      if (timelineRef.current) {
        const rect = timelineRef.current.getBoundingClientRect();
        setViewportWidth(rect.width); // No ruler width to subtract
      }
    };

    updateViewportWidth();
    window.addEventListener('resize', updateViewportWidth);
    return () => window.removeEventListener('resize', updateViewportWidth);
  }, []);

  // Handle zoom - PERFORMANCE OPTIMIZED
  const handleZoom = useCallback((delta: number, centerX?: number) => {
    // PERFORMANCE FIX: Prevent zoom conflicts during active zoom operations
    if (isZooming.current) return;

    const zoomFactor = 1.1;
    const newPixelsPerSecond = delta > 0
      ? Math.min(pixelsPerSecond * zoomFactor, MAX_PIXELS_PER_SECOND)
      : Math.max(pixelsPerSecond / zoomFactor, MIN_PIXELS_PER_SECOND);

    if (newPixelsPerSecond !== pixelsPerSecond && Math.abs(newPixelsPerSecond - lastZoomLevel.current) > 0.1) {
      isZooming.current = true;

      // PERFORMANCE FIX: Calculate scroll position before state update to prevent bounce
      let newScrollLeft = 0;
      const scrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');

      if (centerX !== undefined && scrollContainer) {
        const currentScrollLeft = scrollContainer.scrollLeft;
        const zoomRatio = newPixelsPerSecond / pixelsPerSecond;
        newScrollLeft = Math.max(0, (currentScrollLeft + centerX) * zoomRatio - centerX);
      }

      // PERFORMANCE FIX: Batch all updates together using React's automatic batching
      setPixelsPerSecond(newPixelsPerSecond);
      lastZoomLevel.current = newPixelsPerSecond;

      // PERFORMANCE FIX: Update scroll immediately, no setTimeout delay
      if (centerX !== undefined && scrollContainer) {
        scrollContainer.scrollLeft = newScrollLeft;
        setScrollLeft(newScrollLeft); // Keep state in sync
      }

      // CRITICAL FIX: Do NOT sync WaveSurfer zoom with timeline zoom
      // WaveSurfer should ALWAYS show complete track, timeline zoom only affects grid/ruler
      console.log(`[HorizontalTimelineMain] 🔒 PROFESSIONAL DAW MODE: Timeline zoom ${newPixelsPerSecond}px/s affects grid/ruler only - tracks show complete waveforms`);

      // Reset zoom flag after a short delay to allow for smooth continuous zooming
      setTimeout(() => {
        isZooming.current = false;
      }, 50);
    }
  }, [pixelsPerSecond, tracks]);

  // PERFORMANCE OPTIMIZED: Handle wheel zoom AND horizontal scroll with optimized throttling
  const handleWheel = useCallback(throttle((e: WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      // Zoom with Ctrl+Wheel - PERFORMANCE OPTIMIZED
      e.preventDefault();
      const rect = timelineRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = e.clientX - rect.left;
        handleZoom(-e.deltaY, centerX);
      }
    } else {
      // PERFORMANCE OPTIMIZED: Horizontal scroll with regular wheel
      e.preventDefault();
      const scrollAmount = e.deltaY * 2;
      const scrollAreaElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');

      if (scrollAreaElement) {
        const currentScrollLeft = scrollAreaElement.scrollLeft;
        const newScrollLeft = Math.max(0, currentScrollLeft + scrollAmount);
        scrollAreaElement.scrollLeft = newScrollLeft;
        setScrollLeft(newScrollLeft);
      }
    }
  }, 8), [handleZoom]); // PERFORMANCE FIX: Reduced throttling from 16ms to 8ms for snappier response

  // CRITICAL FIX: Add wheel event listener with passive: false to allow preventDefault
  useEffect(() => {
    const timelineElement = timelineRef.current;
    if (timelineElement) {
      timelineElement.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        timelineElement.removeEventListener('wheel', handleWheel);
      };
    }
  }, [handleWheel]);

  // OPTIMIZED: Handle timeline click for seeking with batched DOM operations
  const handleTimelineClick = useCallback((e: React.MouseEvent) => {
    batchDOMOperations(() => {
      const rect = timelineRef.current?.getBoundingClientRect();
      if (rect) {
        const clickX = e.clientX - rect.left + scrollLeft; // No ruler width to subtract
        const clickTime = clickX / pixelsPerSecond;

        if (clickTime >= 0 && clickTime <= effectiveDuration) {
          onSeek(clickTime);
        }
      }
    });
  }, [scrollLeft, pixelsPerSecond, effectiveDuration, onSeek]);

  // PERFORMANCE OPTIMIZED: Handle scroll tracking with minimal overhead
  const handleScroll = useCallback(throttle((e: Event) => {
    const target = e.target as HTMLElement;
    if (target) {
      setScrollLeft(target.scrollLeft);
    }
  }, 8), [pixelsPerSecond]); // PERFORMANCE FIX: Reduced throttling and removed console.log for better performance

  // Set up scroll listener
  useEffect(() => {
    const scrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Handle space key for drag scrolling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !e.repeat && !isSpacePressed) {
        e.preventDefault();
        setIsSpacePressed(true);
        document.body.style.cursor = 'grab';
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault();
        setIsSpacePressed(false);
        setIsDraggingTimeline(false);
        document.body.style.cursor = '';
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      document.body.style.cursor = '';
    };
  }, [isSpacePressed]);

  // Handle timeline drag scrolling
  const handleTimelineDragStart = useCallback((e: React.MouseEvent) => {
    if (!isSpacePressed) return;

    e.preventDefault();
    e.stopPropagation();

    setIsDraggingTimeline(true);
    setDragStartX(e.clientX);
    setDragStartScrollLeft(scrollLeft);
    document.body.style.cursor = 'grabbing';
  }, [isSpacePressed, scrollLeft]);

  const handleTimelineDragMove = useCallback((e: MouseEvent) => {
    if (!isDraggingTimeline || !isSpacePressed) return;

    const deltaX = e.clientX - dragStartX;
    const newScrollLeft = Math.max(0, dragStartScrollLeft - deltaX);

    const scrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
    if (scrollContainer) {
      scrollContainer.scrollLeft = newScrollLeft;
    }
  }, [isDraggingTimeline, isSpacePressed, dragStartX, dragStartScrollLeft]);

  const handleTimelineDragEnd = useCallback(() => {
    setIsDraggingTimeline(false);
    document.body.style.cursor = isSpacePressed ? 'grab' : '';
  }, [isSpacePressed]);

  // Set up drag event listeners
  useEffect(() => {
    if (isDraggingTimeline) {
      document.addEventListener('mousemove', handleTimelineDragMove);
      document.addEventListener('mouseup', handleTimelineDragEnd);

      return () => {
        document.removeEventListener('mousemove', handleTimelineDragMove);
        document.removeEventListener('mouseup', handleTimelineDragEnd);
      };
    }
  }, [isDraggingTimeline, handleTimelineDragMove, handleTimelineDragEnd]);

  // Handle transport controls
  const handlePlay = () => {
    // TODO: Implement play functionality
    console.log('Play clicked');
  };

  const handlePause = () => {
    // TODO: Implement pause functionality
    console.log('Pause clicked');
  };

  const handleStop = () => {
    // TODO: Implement stop functionality
    console.log('Stop clicked');
  };

  // Handle timeline loop controls
  const handleLoopToggle = () => {
    const newLoopState = !isTimelineLooping;
    setTimelineLooping(newLoopState);
    timelineCoordinatorEnhanced.setTimelineLooping(newLoopState);

    // If enabling loop and no region set, set default region
    if (newLoopState && timelineLoopStart === 0 && timelineLoopEnd === 0) {
      const defaultEnd = Math.min(effectiveDuration, 8); // 8 second default loop
      setTimelineLoopRegion(0, defaultEnd);
      timelineCoordinatorEnhanced.setTimelineLoopRegion(0, defaultEnd);
    }
  };

  const handleLoopRegionChange = (start: number, end: number) => {
    setTimelineLoopRegion(start, end);
    timelineCoordinatorEnhanced.setTimelineLoopRegion(start, end);
  };

  return (
    <div className="h-full flex flex-col bg-background relative overflow-hidden">
      {/* Professional Timeline Ruler - TOP HEADER */}
      <div className="flex-shrink-0 bg-muted border-b border-border relative z-10 overflow-hidden">
        {/* CRITICAL FIX: Ruler container that scrolls with timeline - VIEWPORT AWARE */}
        <div
          className="relative h-full"
          style={{
            width: gridRulerWidth, // Use viewport-aware width
            transform: `translateX(-${scrollLeft}px)`
          }}
        >
          <ProfessionalTimelineRuler
            totalDuration={gridRulerDuration} // Use viewport-aware duration
            currentTime={currentTime}
            pixelsPerSecond={pixelsPerSecond}
            masterBPM={masterBPM}
            onSeek={onSeek}
            scrollLeft={0} // No longer needed - handled by container transform
          />
        </div>
      </div>

      {/* Main Timeline Area */}
      <div
        ref={timelineRef}
        className="flex-1 flex flex-col bg-background relative overflow-hidden"
      >
        {/* CANVAS-WIDE GRID - Spans entire timeline area like Ableton - VIEWPORT AWARE */}
        <div
          className="absolute inset-0 pointer-events-none z-0"
          style={{
            transform: `translateX(-${scrollLeft}px)`, // Sync with scroll
            width: gridRulerWidth, // Use viewport-aware width
            height: '100%', // Full canvas height
          }}
        >
          <ProfessionalTimelineGrid
            totalDuration={gridRulerDuration} // Use viewport-aware duration
            pixelsPerSecond={pixelsPerSecond}
            masterBPM={masterBPM}
            showGrid={showGrid}
            gridDensity={gridDensity}
            snapToGrid={snapToGrid}
          />
        </div>

        {/* Marker Lane */}
        <MarkerLane
          totalDuration={effectiveDuration}
          pixelsPerSecond={pixelsPerSecond}
          currentTime={currentTime}
          onSeek={onSeek}
          markers={markers}
          onMarkersChange={setMarkers}
        />

        {/* Tempo Mapping Lane */}
        <TempoMappingLane
          totalDuration={effectiveDuration}
          pixelsPerSecond={pixelsPerSecond}
          currentTime={currentTime}
          masterBPM={masterBPM}
          onSeek={onSeek}
          tempoPoints={tempoPoints}
          onTempoPointsChange={setTempoPoints}
        />

        {/* Track Lanes Area */}
        <ScrollArea
          ref={scrollAreaRef}
          className="flex-1 w-full"
        >
          <div
            className="relative"
            style={{
              width: timelineWidth,
              height: timelineHeight,
              cursor: isSpacePressed ? 'grab' : 'default'
            }}
            onClick={isSpacePressed ? undefined : handleTimelineClick}
            onMouseDown={handleTimelineDragStart}
          >
            {/* Track Lanes */}
            {tracks.length > 0 ? (
              tracks.map((track, index) => {
                const trackTop = index * TRACK_HEIGHT;
                // REMOVED: Console log that was causing infinite re-renders

                return (
                  <div
                    key={track.id}
                    className="absolute left-0 right-0"
                    style={{
                      top: trackTop,
                      height: TRACK_HEIGHT,
                    }}
                  >
                  <HorizontalTrackLane
                    track={track}
                    isSelected={selectedTrackId === track.id.toString()}
                    onSelect={() => onTrackSelect(track.id.toString())}
                    pixelsPerSecond={pixelsPerSecond}
                    trackHeight={TRACK_HEIGHT}
                    currentTime={currentTime}
                    isPlaying={isPlaying}
                    masterBPM={masterBPM}
                    snapToGrid={snapToGrid}
                  />
                  </div>
                );
              })
            ) : (
              <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg mb-2">No tracks in timeline</div>
                  <div className="text-sm">Generate a mix or add tracks to get started</div>
                </div>
              </div>
            )}

            {/* Timeline Loop Brackets (Ableton-style) */}
            <TimelineLoopBrackets
              totalDuration={effectiveDuration}
              pixelsPerSecond={pixelsPerSecond}
              currentTime={currentTime}
              isLooping={isTimelineLooping}
              loopStart={timelineLoopStart}
              loopEnd={timelineLoopEnd || effectiveDuration}
              onLoopToggle={handleLoopToggle}
              onLoopRegionChange={handleLoopRegionChange}
              onSeek={onSeek}
            />

            {/* Playhead */}
            <HorizontalPlayhead
              currentTime={currentTime}
              pixelsPerSecond={pixelsPerSecond}
              timelineHeight={timelineHeight}
              isPlaying={isPlaying}
            />
          </div>
        </ScrollArea>

        {/* FIXED TRACK CONTROLS - Always visible on the right side */}
        {tracks.length > 0 && (
          <div className="absolute right-4 top-20 flex flex-col gap-2 z-30">
            {tracks.map((track, index) => {
              const trackTop = index * TRACK_HEIGHT;
              return (
                <div
                  key={`controls-${track.id}`}
                  className="flex items-center gap-1 bg-background/95 backdrop-blur-sm border border-border rounded-md p-2 shadow-lg"
                  style={{
                    marginTop: trackTop, // Align with track position
                  }}
                >
                  {/* Track Controls */}
                  <button
                    className="p-1 hover:bg-accent rounded text-xs"
                    onClick={() => {
                      // Handle play/pause for this track
                      console.log(`Play/pause track ${track.id}`);
                    }}
                    title={`Play/Pause: ${track.title}`}
                  >
                    ▶️
                  </button>
                  <button
                    className="p-1 hover:bg-accent rounded text-xs"
                    onClick={() => {
                      // Handle settings for this track
                      console.log(`Settings for track ${track.id}`);
                    }}
                    title={`Settings: ${track.title}`}
                  >
                    ⚙️
                  </button>
                  <button
                    className="p-1 hover:bg-accent rounded text-xs text-destructive"
                    onClick={() => {
                      // Handle remove track
                      console.log(`Remove track ${track.id}`);
                    }}
                    title={`Remove: ${track.title}`}
                  >
                    🗑️
                  </button>
                </div>
              );
            })}
          </div>
        )}
      {/* Close main timeline area */}
      </div>

      {/* Timeline Controls */}
      <div className="absolute bottom-4 right-4 flex gap-4 z-20">
        {/* Grid Controls */}
        <div className="flex items-center gap-2 px-3 py-2 bg-background border border-border rounded">
          {/* Grid Toggle */}
          <button
            className={`px-2 py-1 rounded text-xs transition-colors ${
              showGrid
                ? 'bg-primary text-primary-foreground'
                : 'bg-muted text-muted-foreground hover:bg-accent'
            }`}
            onClick={() => setShowGrid(!showGrid)}
            title="Toggle Grid Visibility"
          >
            Grid
          </button>

          {/* Snap Toggle */}
          <button
            className={`px-2 py-1 rounded text-xs transition-colors ${
              snapToGrid
                ? 'bg-primary text-primary-foreground'
                : 'bg-muted text-muted-foreground hover:bg-accent'
            }`}
            onClick={() => setSnapToGrid(!snapToGrid)}
            title={`Beat Snapping: ${snapToGrid ? 'ON' : 'OFF'} - Tracks snap to beat boundaries when moved`}
          >
            Snap
          </button>

          {/* Grid Density */}
          <div className="flex gap-1 ml-2">
            {(['1/4', '1/8', '1/16'] as const).map((density) => (
              <button
                key={density}
                className={`px-2 py-1 rounded text-xs font-mono transition-colors ${
                  gridDensity === density
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground hover:bg-accent'
                }`}
                onClick={() => setGridDensity(density)}
                title={`Grid Density: ${density} notes at ${masterBPM} BPM`}
              >
                {density}
              </button>
            ))}
          </div>

          {/* BPM Display - CRITICAL FIX: Make clickable to set master BPM */}
          <button
            className="ml-2 px-2 py-1 bg-muted rounded text-xs font-mono text-muted-foreground hover:bg-accent cursor-pointer"
            onClick={() => {
              const newBPM = prompt(`Set Master BPM (current: ${masterBPM})`, masterBPM.toString());
              if (newBPM && !isNaN(Number(newBPM))) {
                const bpm = Number(newBPM);
                if (bpm >= 60 && bpm <= 200) {
                  timelineCoordinatorEnhanced.setMasterBPM(bpm);
                  console.log(`[HorizontalTimelineMain] Master BPM set to ${bpm}`);
                } else {
                  alert('BPM must be between 60 and 200');
                }
              }
            }}
            title="Click to set master BPM (60-200)"
          >
            {masterBPM} BPM
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center gap-2 px-3 py-2 bg-background border border-border rounded">
          <button
            className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs hover:bg-accent"
            onClick={() => handleZoom(-1)}
            title="Zoom Out (Ctrl+Wheel also works)"
          >
            −
          </button>
          <span
            className="px-2 py-1 text-xs font-mono min-w-[60px] text-center"
            title="Current zoom level in pixels per second"
          >
            {Math.round(pixelsPerSecond)}px/s
          </span>
          <button
            className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs hover:bg-accent"
            onClick={() => handleZoom(1)}
            title="Zoom In (Ctrl+Wheel also works)"
          >
            +
          </button>
        </div>

        {/* Help Indicator */}
        <div className="px-2 py-1 bg-muted/50 border border-border rounded text-xs text-muted-foreground">
          <span title="Hold SPACE and drag to scroll timeline horizontally">
            Space+Drag to scroll
          </span>
        </div>
      </div>
    {/* Close main container */}
    </div>
  );
};

export default HorizontalTimelineMain;
