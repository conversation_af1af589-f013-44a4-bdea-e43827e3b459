import React, { useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { calculateBeatDensity, calculateTimeDensity, getDensityDebugInfo } from '../../utils/timelineAdaptiveDensity';

interface ProfessionalTimelineRulerProps {
  totalDuration: number;
  currentTime: number;
  pixelsPerSecond: number;
  masterBPM: number;
  onSeek: (time: number) => void;
  scrollLeft?: number; // CRITICAL FIX: Add scroll offset for synchronization
}

/**
 * ProfessionalTimelineRuler - Professional DAW-style HORIZONTAL timeline ruler
 *
 * Features:
 * - Horizontal beat/bar ruler with beat numbers (1.1, 1.2, 1.3, 1.4, 2.1, etc.)
 * - Horizontal time ruler with time positions (0:00, 0:01, 0:02, etc.)
 * - Master BPM integration for accurate beat divisions
 * - Professional DAW-style appearance
 * - Click-to-seek functionality
 * - Current time/beat indicator
 */
const ProfessionalTimelineRuler: React.FC<ProfessionalTimelineRulerProps> = ({
  totalDuration,
  currentTime,
  pixelsPerSecond,
  masterBPM,
  onSeek,
  scrollLeft = 0, // CRITICAL FIX: Default scroll offset
}) => {
  // Calculate ruler dimensions - HORIZONTAL
  const rulerWidth = Math.max(totalDuration * pixelsPerSecond, 800);

  // CRITICAL DEBUG: Log ruler state
  console.log(`[ProfessionalTimelineRuler] Render: totalDuration=${totalDuration}, pixelsPerSecond=${pixelsPerSecond}, scrollLeft=${scrollLeft}, rulerWidth=${rulerWidth}`);

  // Beat calculations
  const beatsPerSecond = masterBPM / 60;
  const pixelsPerBeat = pixelsPerSecond / beatsPerSecond;
  const secondsPerBeat = 60 / masterBPM;

  // CRITICAL FIX: Calculate padded duration at component level for reuse
  const paddedDuration = totalDuration * 1.1; // 10% padding
  
  // Format time as MM:SS
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Format beat position as Bar.Beat (e.g., 1.1, 1.2, 1.3, 1.4, 2.1)
  const formatBeatPosition = useCallback((beatNumber: number): string => {
    const barNumber = Math.floor(beatNumber / 4) + 1;
    const beatInBar = (beatNumber % 4) + 1;
    return `${barNumber}.${beatInBar}`;
  }, []);

  // Generate beat markers - HORIZONTAL with PROFESSIONAL ADAPTIVE DENSITY using shared utility
  const beatMarkers = useMemo(() => {
    const markers = [];
    const totalBeats = Math.ceil(paddedDuration * beatsPerSecond);

    // Use shared adaptive density calculation for consistency with grid
    const adaptiveDensity = calculateBeatDensity({ pixelsPerSecond, masterBPM });
    const beatInterval = adaptiveDensity.beatInterval;

    for (let beatIndex = 0; beatIndex < totalBeats; beatIndex += beatInterval) {
      const time = beatIndex * secondsPerBeat;
      const x = time * pixelsPerSecond; // HORIZONTAL: x instead of y
      const isDownbeat = beatIndex % 4 === 0; // First beat of each bar
      const isHalfBar = beatIndex % 2 === 0; // Half-bar markers
      const beatNumber = beatIndex;

      markers.push({
        time,
        x, // HORIZONTAL: x instead of y
        beatNumber,
        isDownbeat,
        isHalfBar,
        beatLabel: formatBeatPosition(beatNumber),
        timeLabel: formatTime(time),
      });
    }

    const debugInfo = getDensityDebugInfo({ pixelsPerSecond, masterBPM });
    console.log(`[ProfessionalTimelineRuler] 🎵 ADAPTIVE BEATS: ${debugInfo.pixelsPerBeat}px/beat, interval=${beatInterval}, markers=${markers.length}`);
    return markers;
  }, [paddedDuration, beatsPerSecond, secondsPerBeat, pixelsPerSecond, masterBPM, formatBeatPosition, formatTime]);

  // Generate time markers (for time ruler track) - HORIZONTAL with PROFESSIONAL ADAPTIVE DENSITY using shared utility
  const timeMarkers = useMemo(() => {
    const markers = [];

    // Use shared adaptive density calculation for consistency
    const timeDensity = calculateTimeDensity({ pixelsPerSecond, masterBPM });
    const timeInterval = timeDensity.timeInterval;
    const showMinorMarkers = timeDensity.showMinorMarkers;

    const numMarkers = Math.ceil(paddedDuration / timeInterval);

    for (let i = 0; i <= numMarkers; i++) {
      const time = i * timeInterval;
      const x = time * pixelsPerSecond; // HORIZONTAL: x instead of y

      // Determine marker importance
      const isMajor = time % (timeInterval * 4) === 0 || time === 0; // Major every 4 intervals or at start
      const isMinor = time % (timeInterval * 2) === 0; // Minor every 2 intervals

      // Only show minor markers if zoom level allows
      if (!showMinorMarkers && !isMajor) continue;

      markers.push({
        time,
        x, // HORIZONTAL: x instead of y
        isMajor,
        isMinor,
        label: formatTime(time),
      });
    }

    const debugInfo = getDensityDebugInfo({ pixelsPerSecond, masterBPM });
    console.log(`[ProfessionalTimelineRuler] ⏱️ ADAPTIVE TIME: ${pixelsPerSecond}px/s, interval=${timeInterval}s, markers=${markers.length}`);
    return markers;
  }, [paddedDuration, pixelsPerSecond, masterBPM, formatTime]);

  // Handle ruler click for seeking - HORIZONTAL with scroll offset
  const handleRulerClick = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left + scrollLeft; // CRITICAL FIX: Add scroll offset
    const clickTime = clickX / pixelsPerSecond;

    if (clickTime >= 0 && clickTime <= totalDuration) {
      onSeek(clickTime);
    }
  }, [pixelsPerSecond, totalDuration, onSeek, scrollLeft]);

  // Calculate current time and beat positions - HORIZONTAL
  const currentTimeX = currentTime * pixelsPerSecond; // HORIZONTAL: X instead of Y
  const currentBeatNumber = Math.floor(currentTime * beatsPerSecond);
  const currentBeatLabel = formatBeatPosition(currentBeatNumber);

  return (
    <div className="w-full h-16 bg-muted relative overflow-hidden border-b border-border/50">
      {/* Ruler Background - HORIZONTAL (scroll handled by parent container) */}
      <div
        className="absolute inset-0 cursor-pointer select-none"
        style={{
          width: rulerWidth,
          // FIXED: No transform needed - parent container handles scroll synchronization
        }}
        onClick={handleRulerClick}
      >
        {/* Beat/Bar Ruler Track (Top Half) */}
        <div className="absolute top-0 left-0 right-0 h-8 bg-muted border-b border-border/30">
          {/* Beat markers with PROFESSIONAL ADAPTIVE STYLING */}
          {beatMarkers.map((marker) => (
            <div
              key={`beat-${marker.beatNumber}`}
              className="absolute top-0 bottom-0 flex flex-col justify-center"
              style={{ left: marker.x }}
            >
              {/* Beat tick mark with professional hierarchy */}
              <div
                className={cn(
                  "border-l",
                  marker.isDownbeat
                    ? "border-foreground h-full border-l-2" // Downbeats: full height, thick line
                    : marker.isHalfBar
                    ? "border-muted-foreground h-3/4 mt-auto" // Half-bars: 3/4 height
                    : "border-muted-foreground/60 h-1/2 mt-auto" // Regular beats: 1/2 height
                )}
              />

              {/* Beat label - show for downbeats and important markers */}
              {marker.isDownbeat && (
                <div
                  className="absolute top-1 bg-muted text-foreground px-1 text-xs font-mono font-medium"
                  style={{
                    left: 2,
                    fontSize: '10px',
                    lineHeight: '14px',
                  }}
                >
                  {marker.beatLabel}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Time Ruler Track (Bottom Half) */}
        <div className="absolute bottom-0 left-0 right-0 h-8 bg-muted/80">
          {/* Time markers with PROFESSIONAL ADAPTIVE STYLING */}
          {timeMarkers.map((marker) => (
            <div
              key={`time-${marker.time}`}
              className="absolute top-0 bottom-0 flex flex-col justify-center"
              style={{ left: marker.x }}
            >
              {/* Time tick mark with professional hierarchy */}
              <div
                className={cn(
                  "border-l",
                  marker.isMajor
                    ? "border-foreground h-full border-l-2" // Major: full height, thick line
                    : marker.isMinor
                    ? "border-muted-foreground h-3/4" // Minor: 3/4 height
                    : "border-muted-foreground/60 h-1/2" // Regular: 1/2 height
                )}
              />

              {/* Time label - show for major markers and some minor ones */}
              {(marker.isMajor || (marker.isMinor && marker.time % 30 === 0)) && (
                <div
                  className="absolute bottom-1 bg-muted text-muted-foreground px-1 text-xs font-mono"
                  style={{
                    left: 2,
                    fontSize: '9px',
                    lineHeight: '14px',
                  }}
                >
                  {marker.label}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Current Time/Beat Indicator - HORIZONTAL */}
        <div
          className="absolute top-0 bottom-0 z-20 pointer-events-none"
          style={{ left: currentTimeX }}
        >
          {/* Current time line - VERTICAL line for horizontal timeline */}
          <div className="w-0.5 h-full bg-primary shadow-sm" />

          {/* Current beat label */}
          <div
            className="absolute top-1 bg-primary text-primary-foreground px-1.5 py-0.5 rounded text-xs font-mono font-bold shadow-md"
            style={{
              left: 2,
              fontSize: '10px',
              lineHeight: '12px',
            }}
          >
            {currentBeatLabel}
          </div>

          {/* Current time label */}
          <div
            className="absolute bottom-1 bg-primary/90 text-primary-foreground px-1.5 py-0.5 rounded text-xs font-mono"
            style={{
              left: 2,
              fontSize: '9px',
              lineHeight: '12px',
            }}
          >
            {formatTime(currentTime)}
          </div>

          {/* Playhead triangle indicators - HORIZONTAL */}
          <div
            className="absolute top-0 w-0 h-0 border-b-4 border-b-primary border-l-2 border-r-2 border-l-transparent border-r-transparent"
            style={{ left: -2 }}
          />
          <div
            className="absolute bottom-0 w-0 h-0 border-t-4 border-t-primary border-l-2 border-r-2 border-l-transparent border-r-transparent"
            style={{ left: -2 }}
          />
        </div>

        {/* Hover indicator */}
        <div className="absolute inset-0 hover:bg-accent/10 transition-colors pointer-events-none" />
      </div>
    </div>
  );
};

export default ProfessionalTimelineRuler;
