import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RotateCcw, Square } from 'lucide-react';
import { cn } from '@/lib/utils';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';

interface TimelineLoopBracketsProps {
  totalDuration: number;
  pixelsPerSecond: number;
  currentTime: number;
  isLooping: boolean;
  loopStart: number;
  loopEnd: number;
  onLoopToggle: () => void;
  onLoopRegionChange: (start: number, end: number) => void;
  onSeek: (time: number) => void;
}

/**
 * TimelineLoopBrackets - Ableton-style timeline loop system
 * 
 * Features:
 * - Visual loop brackets spanning across timeline
 * - Draggable loop start/end markers
 * - Loop toggle button
 * - Visual feedback when looping is active
 * - Click-to-set loop region
 * - Professional DAW-style appearance
 */
const TimelineLoopBrackets: React.FC<TimelineLoopBracketsProps> = ({
  totalDuration,
  pixelsPerSecond,
  currentTime,
  isLooping,
  loopStart,
  loopEnd,
  onLoopToggle,
  onLoopRegionChange,
  onSeek,
}) => {
  const [isDragging, setIsDragging] = useState<'start' | 'end' | 'region' | null>(null);
  const [dragOffset, setDragOffset] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate positions
  const loopStartX = loopStart * pixelsPerSecond;
  const loopEndX = loopEnd * pixelsPerSecond;
  const loopWidth = loopEndX - loopStartX;
  const currentTimeX = currentTime * pixelsPerSecond;

  // Handle mouse down on loop markers
  const handleMouseDown = useCallback((e: React.MouseEvent, type: 'start' | 'end' | 'region') => {
    e.preventDefault();
    e.stopPropagation();
    
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = e.clientX - rect.left;
    
    if (type === 'region') {
      setDragOffset(clickX - loopStartX);
    } else {
      setDragOffset(0);
    }
    
    setIsDragging(type);
  }, [loopStartX]);

  // Handle mouse move during drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const time = Math.max(0, Math.min(totalDuration, mouseX / pixelsPerSecond));

    if (isDragging === 'start') {
      const newStart = Math.max(0, Math.min(loopEnd - 0.1, time));
      onLoopRegionChange(newStart, loopEnd);
    } else if (isDragging === 'end') {
      const newEnd = Math.max(loopStart + 0.1, Math.min(totalDuration, time));
      onLoopRegionChange(loopStart, newEnd);
    } else if (isDragging === 'region') {
      const regionDuration = loopEnd - loopStart;
      const newStart = Math.max(0, Math.min(totalDuration - regionDuration, (mouseX - dragOffset) / pixelsPerSecond));
      const newEnd = newStart + regionDuration;
      onLoopRegionChange(newStart, newEnd);
    }
  }, [isDragging, loopStart, loopEnd, totalDuration, pixelsPerSecond, dragOffset, onLoopRegionChange]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
    setDragOffset(0);
  }, []);

  // Set up global mouse events
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Handle double-click to set loop region
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = e.clientX - rect.left;
    const clickTime = clickX / pixelsPerSecond;
    
    // Set loop region around click point (4 seconds by default)
    const regionDuration = 4;
    const newStart = Math.max(0, clickTime - regionDuration / 2);
    const newEnd = Math.min(totalDuration, newStart + regionDuration);
    
    onLoopRegionChange(newStart, newEnd);
  }, [pixelsPerSecond, totalDuration, onLoopRegionChange]);

  // Format time for display
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    const centiseconds = Math.floor((time % 1) * 100);
    return `${minutes}:${seconds.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
  }, []);

  return (
    <div 
      ref={containerRef}
      className="absolute inset-0 pointer-events-none"
      onDoubleClick={handleDoubleClick}
    >
      {/* Loop Region Background */}
      {isLooping && (
        <div
          className="absolute top-0 bottom-0 bg-primary/10 border-l-2 border-r-2 border-primary pointer-events-none"
          style={{
            left: loopStartX,
            width: loopWidth,
          }}
        />
      )}

      {/* Loop Start Bracket */}
      <div
        className={cn(
          "absolute top-0 bottom-0 cursor-ew-resize pointer-events-auto group",
          isLooping && "opacity-100",
          !isLooping && "opacity-60 hover:opacity-100"
        )}
        style={{ left: loopStartX - 2 }}
        onMouseDown={(e) => handleMouseDown(e, 'start')}
      >
        {/* Vertical line */}
        <div className="w-1 h-full bg-primary group-hover:bg-primary/80 transition-colors" />
        
        {/* Top bracket */}
        <div className="absolute top-0 left-0 w-4 h-1 bg-primary group-hover:bg-primary/80 transition-colors" />
        
        {/* Bottom bracket */}
        <div className="absolute bottom-0 left-0 w-4 h-1 bg-primary group-hover:bg-primary/80 transition-colors" />
        
        {/* Time label */}
        <div className="absolute top-1 left-1 bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-mono whitespace-nowrap">
          {formatTime(loopStart)}
        </div>
      </div>

      {/* Loop End Bracket */}
      <div
        className={cn(
          "absolute top-0 bottom-0 cursor-ew-resize pointer-events-auto group",
          isLooping && "opacity-100",
          !isLooping && "opacity-60 hover:opacity-100"
        )}
        style={{ left: loopEndX + 1 }}
        onMouseDown={(e) => handleMouseDown(e, 'end')}
      >
        {/* Vertical line */}
        <div className="w-1 h-full bg-primary group-hover:bg-primary/80 transition-colors" />
        
        {/* Top bracket */}
        <div className="absolute top-0 right-0 w-4 h-1 bg-primary group-hover:bg-primary/80 transition-colors" />
        
        {/* Bottom bracket */}
        <div className="absolute bottom-0 right-0 w-4 h-1 bg-primary group-hover:bg-primary/80 transition-colors" />
        
        {/* Time label */}
        <div className="absolute top-1 right-1 bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-mono whitespace-nowrap">
          {formatTime(loopEnd)}
        </div>
      </div>

      {/* Loop Region (draggable area) */}
      {loopWidth > 20 && (
        <div
          className={cn(
            "absolute top-0 bottom-0 cursor-move pointer-events-auto group",
            isLooping && "opacity-100",
            !isLooping && "opacity-0 hover:opacity-30"
          )}
          style={{
            left: loopStartX + 5,
            width: loopWidth - 10,
          }}
          onMouseDown={(e) => handleMouseDown(e, 'region')}
        >
          {/* Invisible drag area */}
          <div className="w-full h-full" />
          
          {/* Center label */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-primary/80 text-primary-foreground px-2 py-1 rounded text-xs font-mono whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
            Loop: {formatTime(loopEnd - loopStart)}
          </div>
        </div>
      )}

      {/* Loop Toggle Button (floating) */}
      <div className="absolute top-2 right-2 pointer-events-auto">
        <Button
          variant={isLooping ? "default" : "outline"}
          size="sm"
          onClick={onLoopToggle}
          className={cn(
            "h-8 px-3 text-xs font-medium transition-all",
            isLooping && "bg-primary text-primary-foreground shadow-md",
            !isLooping && "bg-background/80 backdrop-blur-sm hover:bg-accent"
          )}
        >
          <RotateCcw className="h-3 w-3 mr-1" />
          {isLooping ? "Loop ON" : "Loop OFF"}
        </Button>
      </div>

      {/* Current Time Indicator (enhanced when looping) */}
      {isLooping && currentTime >= loopStart && currentTime <= loopEnd && (
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-accent shadow-lg pointer-events-none z-20"
          style={{ left: currentTimeX }}
        >
          {/* Playhead triangle */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-b-4 border-l-transparent border-r-transparent border-b-accent" />
        </div>
      )}

      {/* Instructions (when not looping) */}
      {!isLooping && loopStart === 0 && loopEnd === totalDuration && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background/90 backdrop-blur-sm border border-border rounded-lg px-4 py-2 text-sm text-muted-foreground text-center pointer-events-auto">
          <div className="font-medium mb-1">Timeline Loop</div>
          <div>Double-click to set loop region</div>
          <div className="text-xs mt-1">Drag brackets to adjust</div>
        </div>
      )}
    </div>
  );
};

export default TimelineLoopBrackets;
