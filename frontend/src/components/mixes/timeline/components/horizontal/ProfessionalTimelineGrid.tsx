import React, { useMemo } from 'react';
import { cn } from '@/lib/utils';
import { calculateBeatDensity, getDensityDebugInfo } from '../../utils/timelineAdaptiveDensity';

interface ProfessionalTimelineGridProps {
  totalDuration: number;
  pixelsPerSecond: number;
  masterBPM: number;
  showGrid?: boolean;
  gridDensity?: '1/4' | '1/8' | '1/16';
  snapToGrid?: boolean;
}

/**
 * ProfessionalTimelineGrid - Professional DAW-style timeline grid
 * 
 * Features:
 * - Multiple grid densities (1/4, 1/8, 1/16 notes)
 * - Beat and bar grid lines
 * - Professional visual hierarchy
 * - Snap-to-grid visual feedback
 * - Optimized rendering for performance
 */
const ProfessionalTimelineGrid: React.FC<ProfessionalTimelineGridProps> = ({
  totalDuration,
  pixelsPerSecond,
  masterBPM,
  showGrid = true,
  gridDensity = '1/4',
  snapToGrid = true,
}) => {
  // Beat calculations
  const beatsPerSecond = masterBPM / 60;
  const pixelsPerBeat = pixelsPerSecond / beatsPerSecond;
  const secondsPerBeat = 60 / masterBPM;

  // PROFESSIONAL: Adaptive grid divisions using shared utility for consistency with ruler
  const adaptiveDensity = useMemo(() => {
    return calculateBeatDensity({ pixelsPerSecond, masterBPM });
  }, [pixelsPerSecond, masterBPM]);

  const gridDivisions = adaptiveDensity.gridDivisions;

  // Generate grid lines
  const gridLines = useMemo(() => {
    if (!showGrid) {
      console.log('[ProfessionalTimelineGrid] Grid disabled, returning empty array');
      return [];
    }

    const lines = [];
    // CRITICAL FIX: Add padding to ensure grid extends beyond all content
    const paddedDuration = totalDuration * 1.1; // 10% padding
    const totalBeats = Math.ceil(paddedDuration * beatsPerSecond);
    const totalDivisions = totalBeats * gridDivisions;

    const debugInfo = getDensityDebugInfo({ pixelsPerSecond, masterBPM });
    console.log('[ProfessionalTimelineGrid] 🎵 ADAPTIVE GRID:', {
      totalDuration,
      masterBPM,
      beatsPerSecond,
      pixelsPerSecond,
      totalBeats,
      totalDivisions,
      gridDensity,
      ...debugInfo
    });

    for (let i = 0; i <= totalDivisions; i++) {
      const time = (i / gridDivisions) * secondsPerBeat;
      // CRITICAL FIX: Don't break early - let grid extend to full timeline width
      // This ensures grid covers all tracks even when they extend beyond calculated duration

      const x = time * pixelsPerSecond;
      const beatIndex = Math.floor(i / gridDivisions);
      const divisionIndex = i % gridDivisions;

      // Determine line type
      const isBar = beatIndex % 4 === 0 && divisionIndex === 0; // First beat of bar
      const isBeat = divisionIndex === 0; // Beat boundary
      const isSubdivision = divisionIndex > 0; // Subdivision within beat

      lines.push({
        x,
        time,
        beatIndex,
        divisionIndex,
        isBar,
        isBeat,
        isSubdivision,
        type: isBar ? 'bar' : isBeat ? 'beat' : 'subdivision'
      });
    }

    console.log(`[ProfessionalTimelineGrid] Generated ${lines.length} grid lines`);
    if (lines.length > 0) {
      console.log('[ProfessionalTimelineGrid] First few lines:', lines.slice(0, 5));
    }

    return lines;
  }, [totalDuration, beatsPerSecond, pixelsPerSecond, secondsPerBeat, gridDivisions, showGrid]);

  if (!showGrid) {
    return null;
  }

  return (
    <div
      className="absolute inset-0 pointer-events-none"
      style={{
        zIndex: 1,
        opacity: showGrid ? 1 : 0,
        transition: 'opacity 0.2s ease-in-out',
        // CRITICAL: Ensure grid spans full canvas like Ableton
        width: '100%',
        height: '100%',
        minHeight: '200px', // Minimum height even when no tracks
      }}
    >

      {/* PROFESSIONAL GRID - Canvas-wide like Ableton */}

      {/* Grid Lines - FIXED: Use all grid lines with proper styling */}
      {gridLines.map((line, index) => (
        <div
          key={`grid-${index}`}
          className="absolute top-0 bottom-0 z-10"
          style={{
            left: line.x,
            width: line.isBar ? '2px' : line.isBeat ? '1px' : '1px',
            backgroundColor: line.isBar
              ? 'rgba(255, 255, 255, 0.12)' // PRODUCTION: Subtle bars like Ableton
              : line.isBeat
                ? 'rgba(255, 255, 255, 0.08)' // PRODUCTION: Subtle beats
                : 'rgba(255, 255, 255, 0.04)', // PRODUCTION: Very subtle subdivisions
            pointerEvents: 'none'
          }}
        />
      ))}

      {/* Grid Density Indicator (for debugging/development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-muted-foreground">
          Grid: {gridDensity} | BPM: {masterBPM} | Snap: {snapToGrid ? 'ON' : 'OFF'}
        </div>
      )}

      {/* Snap Zones (invisible areas for snap feedback) */}
      {snapToGrid && gridLines.filter(line => line.isBeat).map((line, index) => (
        <div
          key={`snap-${index}`}
          className="absolute top-0 bottom-0 pointer-events-none"
          style={{
            left: line.x - 5, // 5px snap tolerance on each side
            width: 10,
          }}
          data-snap-time={line.time}
          data-snap-beat={line.beatIndex}
        />
      ))}
    </div>
  );
};

export default ProfessionalTimelineGrid;
