import React, { useEffect, useRef, useState } from 'react';
import { useTimelineStore } from '../../stores/TimelineStore';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';
import { Loop, LoopType } from '../../stores/TimelineStore';

// Default loop options
const DEFAULT_LOOP_OPTIONS = {
  draggable: true,
  resizable: true,
};

// Loop colors by type
const getLoopColor = (type: LoopType, active: boolean, customColor?: string): string => {
  if (customColor) return customColor;
  
  const baseColors: Record<LoopType, string> = {
    [LoopType.STANDARD]: active ? 'rgba(255, 165, 0, 0.6)' : 'rgba(255, 165, 0, 0.3)',
    [LoopType.JUMP]: active ? 'rgba(255, 20, 147, 0.6)' : 'rgba(255, 20, 147, 0.3)',
    [LoopType.ROLL]: active ? 'rgba(50, 205, 50, 0.6)' : 'rgba(50, 205, 50, 0.3)',
    [LoopType.ECHO]: active ? 'rgba(138, 43, 226, 0.6)' : 'rgba(138, 43, 226, 0.3)',
    [LoopType.CUSTOM]: active ? 'rgba(30, 144, 255, 0.6)' : 'rgba(30, 144, 255, 0.3)',
  };
  
  return baseColors[type] || baseColors[LoopType.STANDARD];
};

// Loop labels by type
const getLoopLabel = (type: LoopType): string => {
  const labels: Record<LoopType, string> = {
    [LoopType.STANDARD]: 'Loop',
    [LoopType.JUMP]: 'Jump Loop',
    [LoopType.ROLL]: 'Roll Loop',
    [LoopType.ECHO]: 'Echo Loop',
    [LoopType.CUSTOM]: 'Custom Loop',
  };
  
  return labels[type] || 'Loop';
};

interface LoopRegionsProps {
  trackId: string;
}

/**
 * LoopRegions component
 *
 * This component creates loop regions using WaveSurfer.js's Regions plugin.
 * It follows the same pattern as BeatGridRegions and SegmentRegions for consistency.
 * 
 * Note: This is for track-specific loops, separate from timeline loops.
 */
const LoopRegions: React.FC<LoopRegionsProps> = ({ trackId }) => {
  const loopRegionsRef = useRef<any[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Get timeline store state
  const { showLoops, loops, updateLoop, toggleLoopActive } = useTimelineStore();

  // Get track loops
  const trackLoops = loops[trackId] || [];

  // Get WaveSurfer instance
  const getWaveSurferInstance = () => {
    try {
      return timelineCoordinatorEnhanced.getWaveform(trackId);
    } catch (error) {
      console.warn(`[LoopRegions] Error getting WaveSurfer instance for track ${trackId}:`, error);
      return null;
    }
  };

  // Clear all loop regions
  const clearLoops = () => {
    loopRegionsRef.current.forEach(region => {
      try {
        region.remove();
      } catch (error) {
        console.warn(`[LoopRegions] Error removing loop region:`, error);
      }
    });
    loopRegionsRef.current = [];
  };

  // Initialize component
  useEffect(() => {
    const wavesurfer = getWaveSurferInstance();
    if (wavesurfer) {
      setIsInitialized(true);
      console.log(`[LoopRegions] Initialized for track ${trackId}`);
    }

    return () => {
      clearLoops();
      setIsInitialized(false);
    };
  }, [trackId]);

  // Render loops
  const renderLoops = () => {
    if (!showLoops || trackLoops.length === 0) {
      console.log(`[LoopRegions] Skipping render - showLoops: ${showLoops}, trackLoops: ${trackLoops.length}`);
      return;
    }

    const wavesurfer = getWaveSurferInstance();
    if (!wavesurfer) {
      console.log(`[LoopRegions] No WaveSurfer instance found for track ${trackId}`);
      return;
    }

    // Get regions plugin
    const plugins = wavesurfer.getActivePlugins();
    const regionsPlugin = plugins.find((plugin: any) => plugin.constructor.name === 'RegionsPlugin');

    if (!regionsPlugin) {
      console.warn(`[LoopRegions] Regions plugin not found for track ${trackId}`);
      return;
    }

    // Clear existing loop regions
    clearLoops();

    console.log(`[LoopRegions] Rendering ${trackLoops.length} loops for track ${trackId}`);

    // Create regions for each loop
    trackLoops.forEach((loop: Loop, index: number) => {
      try {
        const region = regionsPlugin.addRegion({
          start: loop.startTime,
          end: loop.endTime,
          drag: DEFAULT_LOOP_OPTIONS.draggable,
          resize: DEFAULT_LOOP_OPTIONS.resizable,
          color: getLoopColor(loop.type, loop.active, loop.color),
          id: `loop-${trackId}-${loop.id}`,
          content: loop.label || getLoopLabel(loop.type),
          attributes: {
            type: 'loop',
            loopType: loop.type,
            trackId: trackId,
            loopId: loop.id.toString(),
            active: loop.active.toString()
          }
        });

        // Add event listeners
        region.on('update-end', () => {
          // Update loop times when dragged/resized
          const updatedLoop: Partial<Loop> = {
            startTime: region.start,
            endTime: region.end
          };
          updateLoop(trackId, loop.id.toString(), updatedLoop);
        });

        region.on('click', (e: MouseEvent) => {
          // Toggle loop activation with Ctrl/Cmd+click
          if (e.ctrlKey || e.metaKey) {
            e.stopPropagation();
            toggleLoopActive(trackId, loop.id.toString());
          }
        });

        region.on('dblclick', () => {
          console.log(`[LoopRegions] Loop double-clicked:`, loop);
          // TODO: Add loop editing functionality
        });

        loopRegionsRef.current.push(region);
      } catch (error) {
        console.error(`[LoopRegions] Error creating region for loop ${index}:`, error);
      }
    });

    console.log(`[LoopRegions] Created ${loopRegionsRef.current.length} loop regions for track ${trackId}`);
  };

  // Render loops when initialized or when loops change
  useEffect(() => {
    if (!isInitialized) return;

    if (showLoops && trackLoops.length > 0) {
      renderLoops();
    } else {
      clearLoops();
    }
  }, [
    isInitialized,
    showLoops,
    trackLoops,
    trackId
  ]);

  // Add CSS styles for loop regions
  useEffect(() => {
    const styleId = `loop-styles-${trackId}`;
    let existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* Loop region styles */
        .wavesurfer-region[data-id^="loop-${trackId}-"] {
          border: 2px solid currentColor !important;
          border-radius: 4px !important;
          z-index: 12 !important;
          cursor: pointer !important;
        }

        .wavesurfer-region[data-id^="loop-${trackId}-"]:hover {
          opacity: 0.9 !important;
          border-width: 3px !important;
        }

        /* Active loop styling */
        .wavesurfer-region[data-id^="loop-${trackId}-"][data-active="true"] {
          border-width: 3px !important;
          box-shadow: 0 0 8px currentColor !important;
          animation: pulse-loop 2s infinite !important;
        }

        @keyframes pulse-loop {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 0.9; }
        }

        /* Loop labels */
        .wavesurfer-region[data-id^="loop-${trackId}-"] .wavesurfer-region-content {
          position: absolute !important;
          top: 2px !important;
          left: 4px !important;
          background: rgba(0, 0, 0, 0.8) !important;
          color: white !important;
          padding: 2px 6px !important;
          border-radius: 3px !important;
          font-size: 10px !important;
          font-weight: bold !important;
          white-space: nowrap !important;
          pointer-events: none !important;
        }

        /* Loop resize handles */
        .wavesurfer-region[data-id^="loop-${trackId}-"] .wavesurfer-handle {
          background: currentColor !important;
          width: 6px !important;
          opacity: 0.8 !important;
        }

        .wavesurfer-region[data-id^="loop-${trackId}-"]:hover .wavesurfer-handle {
          opacity: 1 !important;
          width: 8px !important;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const style = document.getElementById(styleId);
      if (style) {
        style.remove();
      }
    };
  }, [trackId]);

  // This component doesn't render anything visible
  return null;
};

export default LoopRegions;
