import React, { useEffect, useRef, useState } from 'react';
import { useTimelineStore } from '../../stores/TimelineStore';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';
import { CuePoint, CuePointType } from '@/types/api/cuePoints';

// Default cue point options
const DEFAULT_CUE_POINT_OPTIONS = {
  draggable: true,
  showLabels: true,
};

// Cue point colors by type
const getCuePointColor = (type: CuePointType, customColor?: string): string => {
  if (customColor) return customColor;
  
  const colors: Record<CuePointType, string> = {
    [CuePointType.INTRO_START]: 'rgba(34, 197, 94, 0.8)', // Green
    [CuePointType.INTRO_END]: 'rgba(34, 197, 94, 0.6)',
    [CuePointType.OUTRO_START]: 'rgba(239, 68, 68, 0.8)', // Red
    [CuePointType.OUTRO_END]: 'rgba(239, 68, 68, 0.6)',
    [CuePointType.DROP]: 'rgba(236, 72, 153, 0.8)', // Pink
    [CuePointType.BREAKDOWN]: 'rgba(147, 51, 234, 0.8)', // Purple
    [CuePointType.CUSTOM]: 'rgba(59, 130, 246, 0.8)', // Blue
  };
  
  return colors[type] || colors[CuePointType.CUSTOM];
};

// Cue point labels by type
const getCuePointLabel = (type: CuePointType): string => {
  const labels: Record<CuePointType, string> = {
    [CuePointType.INTRO_START]: 'Intro Start',
    [CuePointType.INTRO_END]: 'Intro End',
    [CuePointType.OUTRO_START]: 'Outro Start',
    [CuePointType.OUTRO_END]: 'Outro End',
    [CuePointType.DROP]: 'Drop',
    [CuePointType.BREAKDOWN]: 'Breakdown',
    [CuePointType.CUSTOM]: 'Cue Point',
  };
  
  return labels[type] || 'Cue Point';
};

interface CuePointRegionsProps {
  trackId: string;
}

/**
 * CuePointRegions component
 *
 * This component creates cue point markers using WaveSurfer.js's Regions plugin.
 * It follows the same pattern as BeatGridRegions for consistency.
 */
const CuePointRegions: React.FC<CuePointRegionsProps> = ({ trackId }) => {
  const cuePointRegionsRef = useRef<any[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Get timeline store state
  const { showCuePoints, tracks } = useTimelineStore();

  // Get track data
  const track = tracks.find(t => t.id.toString() === trackId);
  const cuePoints = (track as any)?.cuePoints || [];

  // Get WaveSurfer instance
  const getWaveSurferInstance = () => {
    try {
      return timelineCoordinatorEnhanced.getWaveform(trackId);
    } catch (error) {
      console.warn(`[CuePointRegions] Error getting WaveSurfer instance for track ${trackId}:`, error);
      return null;
    }
  };

  // Clear all cue point regions
  const clearCuePoints = () => {
    cuePointRegionsRef.current.forEach(region => {
      try {
        region.remove();
      } catch (error) {
        console.warn(`[CuePointRegions] Error removing cue point region:`, error);
      }
    });
    cuePointRegionsRef.current = [];
  };

  // Initialize component
  useEffect(() => {
    const wavesurfer = getWaveSurferInstance();
    if (wavesurfer) {
      setIsInitialized(true);
      console.log(`[CuePointRegions] Initialized for track ${trackId}`);
    }

    return () => {
      clearCuePoints();
      setIsInitialized(false);
    };
  }, [trackId]);

  // Render cue points
  const renderCuePoints = () => {
    if (!showCuePoints || cuePoints.length === 0) {
      console.log(`[CuePointRegions] Skipping render - showCuePoints: ${showCuePoints}, cuePoints: ${cuePoints.length}`);
      return;
    }

    const wavesurfer = getWaveSurferInstance();
    if (!wavesurfer) {
      console.log(`[CuePointRegions] No WaveSurfer instance found for track ${trackId}`);
      return;
    }

    // Get regions plugin
    const plugins = wavesurfer.getActivePlugins();
    const regionsPlugin = plugins.find((plugin: any) => plugin.constructor.name === 'RegionsPlugin');

    if (!regionsPlugin) {
      console.warn(`[CuePointRegions] Regions plugin not found for track ${trackId}`);
      return;
    }

    // Clear existing cue point regions
    clearCuePoints();

    console.log(`[CuePointRegions] Rendering ${cuePoints.length} cue points for track ${trackId}`);

    // Create regions for each cue point
    cuePoints.forEach((cuePoint: CuePoint, index: number) => {
      try {
        const region = regionsPlugin.addRegion({
          start: cuePoint.time,
          end: cuePoint.time + 0.01, // Small width to ensure visibility
          drag: DEFAULT_CUE_POINT_OPTIONS.draggable,
          resize: false,
          color: getCuePointColor(cuePoint.type, cuePoint.color),
          id: `cue-${trackId}-${cuePoint.id}`,
          content: DEFAULT_CUE_POINT_OPTIONS.showLabels 
            ? (cuePoint.label || getCuePointLabel(cuePoint.type))
            : '',
          attributes: {
            type: 'cue-point',
            cuePointType: cuePoint.type,
            trackId: trackId,
            cuePointId: cuePoint.id.toString()
          }
        });

        // Add event listeners
        region.on('update-end', () => {
          // Update cue point time when dragged
          const updatedCuePoint = { ...cuePoint, time: region.start };
          updateCuePoint(updatedCuePoint);
        });

        region.on('click', () => {
          console.log(`[CuePointRegions] Cue point clicked:`, cuePoint);
          // TODO: Add cue point click handling (seek, edit, etc.)
        });

        cuePointRegionsRef.current.push(region);
      } catch (error) {
        console.error(`[CuePointRegions] Error creating region for cue point ${index}:`, error);
      }
    });

    console.log(`[CuePointRegions] Created ${cuePointRegionsRef.current.length} cue point regions for track ${trackId}`);
  };

  // Update cue point
  const updateCuePoint = (updatedCuePoint: CuePoint) => {
    try {
      const { tracks, setTracks } = useTimelineStore.getState();
      const updatedTracks = tracks.map(t => {
        if (t.id.toString() === trackId) {
          const updatedCuePoints = ((t as any).cuePoints || []).map((cp: CuePoint) =>
            cp.id === updatedCuePoint.id ? updatedCuePoint : cp
          );
          return { ...t, cuePoints: updatedCuePoints };
        }
        return t;
      });
      setTracks(updatedTracks);
    } catch (error) {
      console.error(`[CuePointRegions] Error updating cue point:`, error);
    }
  };

  // Render cue points when initialized or when cue points change
  useEffect(() => {
    if (!isInitialized) return;

    if (showCuePoints && cuePoints.length > 0) {
      renderCuePoints();
    } else {
      clearCuePoints();
    }
  }, [
    isInitialized,
    showCuePoints,
    cuePoints,
    trackId
  ]);

  // Add CSS styles for cue point regions
  useEffect(() => {
    const styleId = `cue-point-styles-${trackId}`;
    let existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* Cue point region styles */
        .wavesurfer-region[data-id^="cue-${trackId}-"] {
          border-left: 3px solid currentColor !important;
          background-color: transparent !important;
          width: 0 !important;
          height: 100% !important;
          z-index: 15 !important;
          cursor: pointer !important;
        }

        .wavesurfer-region[data-id^="cue-${trackId}-"]:hover {
          border-left-width: 4px !important;
          opacity: 1 !important;
        }

        /* Cue point labels */
        .wavesurfer-region[data-id^="cue-${trackId}-"] .wavesurfer-region-content {
          position: absolute !important;
          top: -20px !important;
          left: 5px !important;
          background: rgba(0, 0, 0, 0.8) !important;
          color: white !important;
          padding: 2px 6px !important;
          border-radius: 3px !important;
          font-size: 10px !important;
          font-weight: bold !important;
          white-space: nowrap !important;
          pointer-events: none !important;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const style = document.getElementById(styleId);
      if (style) {
        style.remove();
      }
    };
  }, [trackId]);

  // This component doesn't render anything visible
  return null;
};

export default CuePointRegions;
