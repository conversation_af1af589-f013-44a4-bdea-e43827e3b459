/**
 * Shared utility functions for adaptive density in timeline components
 * Ensures grid and ruler use the same calculation base for visual consistency
 */

export interface AdaptiveDensityConfig {
  pixelsPerSecond: number;
  masterBPM: number;
}

export interface BeatDensityResult {
  beatInterval: number; // How many beats between markers (1 = every beat, 2 = every 2 beats, 4 = every bar)
  gridDivisions: number; // Grid subdivisions (0.25 = bars only, 1 = quarter notes, 2 = 8th notes, 4 = 16th notes)
  showSubdivisions: boolean; // Whether to show beat subdivisions
}

export interface TimeDensityResult {
  timeInterval: number; // Seconds between time markers
  showMinorMarkers: boolean; // Whether to show minor time markers
}

/**
 * Calculate adaptive beat density based on zoom level
 * Used by both grid and ruler for consistent behavior
 */
export function calculateBeatDensity(config: AdaptiveDensityConfig): BeatDensityResult {
  const { pixelsPerSecond, masterBPM } = config;
  const beatsPerSecond = masterBPM / 60;
  const pixelsPerBeat = pixelsPerSecond / beatsPerSecond;

  // PROFESSIONAL: Adaptive density thresholds like pro DAWs - EXTREMELY AGGRESSIVE FOR OVERVIEW
  if (pixelsPerBeat < 2) {
    // Ultra zoomed out - show only every 16 bars (every 64 beats) for complete timeline overview
    return {
      beatInterval: 64,
      gridDivisions: 0.0625,
      showSubdivisions: false
    };
  } else if (pixelsPerBeat < 4) {
    // Extremely zoomed out - show only every 8 bars (every 32 beats)
    return {
      beatInterval: 32,
      gridDivisions: 0.125,
      showSubdivisions: false
    };
  } else if (pixelsPerBeat < 8) {
    // Very zoomed out - show only every 2 bars (every 8 beats)
    return {
      beatInterval: 8,
      gridDivisions: 0.125,
      showSubdivisions: false
    };
  } else if (pixelsPerBeat < 20) {
    // Zoomed out - show only bars (every 4 beats)
    return {
      beatInterval: 4,
      gridDivisions: 0.25,
      showSubdivisions: false
    };
  } else if (pixelsPerBeat < 40) {
    // Medium-low zoom - show bars and half-bars (every 2 beats)
    return {
      beatInterval: 2,
      gridDivisions: 0.5,
      showSubdivisions: false
    };
  } else if (pixelsPerBeat < 80) {
    // Medium zoom - show all beats (every beat)
    return {
      beatInterval: 1,
      gridDivisions: 1,
      showSubdivisions: false
    };
  } else if (pixelsPerBeat < 160) {
    // Zoomed in - show beats with 8th note grid
    return {
      beatInterval: 1,
      gridDivisions: 2,
      showSubdivisions: true
    };
  } else {
    // Very zoomed in - show beats with 16th note grid
    return {
      beatInterval: 1,
      gridDivisions: 4,
      showSubdivisions: true
    };
  }
}

/**
 * Calculate adaptive time density based on zoom level
 * Used by ruler for consistent time marker behavior
 */
export function calculateTimeDensity(config: AdaptiveDensityConfig): TimeDensityResult {
  const { pixelsPerSecond } = config;

  // PROFESSIONAL: Adaptive time intervals based on zoom level like pro DAWs - EXTREMELY AGGRESSIVE FOR OVERVIEW
  if (pixelsPerSecond < 1.5) {
    return {
      timeInterval: 600, // Ultra zoomed out - 10 minute intervals for complete timeline overview
      showMinorMarkers: false
    };
  } else if (pixelsPerSecond < 3) {
    return {
      timeInterval: 300, // Extremely zoomed out - 5 minute intervals
      showMinorMarkers: false
    };
  } else if (pixelsPerSecond < 5) {
    return {
      timeInterval: 120, // Very zoomed out - 2 minute intervals
      showMinorMarkers: false
    };
  } else if (pixelsPerSecond < 8) {
    return {
      timeInterval: 60, // Zoomed out - 1 minute intervals
      showMinorMarkers: false
    };
  } else if (pixelsPerSecond < 15) {
    return {
      timeInterval: 30, // Medium-low zoom - 30s intervals
      showMinorMarkers: false
    };
  } else if (pixelsPerSecond < 25) {
    return {
      timeInterval: 15, // Medium zoom - 15s intervals
      showMinorMarkers: true
    };
  } else if (pixelsPerSecond < 50) {
    return {
      timeInterval: 10, // Zoomed in - 10s intervals
      showMinorMarkers: true
    };
  } else if (pixelsPerSecond < 100) {
    return {
      timeInterval: 5, // Very zoomed in - 5s intervals
      showMinorMarkers: true
    };
  } else {
    return {
      timeInterval: 2, // Extremely zoomed in - 2s intervals
      showMinorMarkers: true
    };
  }
}

/**
 * Get debug information about current density settings
 */
export function getDensityDebugInfo(config: AdaptiveDensityConfig) {
  const beatDensity = calculateBeatDensity(config);
  const timeDensity = calculateTimeDensity(config);
  const beatsPerSecond = config.masterBPM / 60;
  const pixelsPerBeat = config.pixelsPerSecond / beatsPerSecond;

  return {
    pixelsPerBeat: pixelsPerBeat.toFixed(1),
    beatInterval: beatDensity.beatInterval,
    gridDivisions: beatDensity.gridDivisions,
    timeInterval: timeDensity.timeInterval,
    showMinorMarkers: timeDensity.showMinorMarkers,
    showSubdivisions: beatDensity.showSubdivisions
  };
}
