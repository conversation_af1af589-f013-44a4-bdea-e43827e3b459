import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardT<PERSON>le } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  Code, 
  Palette, 
  AlertTriangle, 
  CheckCircle, 
  Copy, 
  ExternalLink,
  Target,
  Layers,
  FileText,
  Lightbulb
} from 'lucide-react';

interface StyleAnalysis {
  property: string;
  value: string;
  source: 'inline' | 'class' | 'css-variable' | 'computed';
  isThemeCompliant: boolean;
  suggestion?: string;
  severity: 'low' | 'medium' | 'high';
}

interface ComponentInfo {
  tagName: string;
  className: string;
  id?: string;
  reactComponent?: string;
  filePath?: string;
  styles: StyleAnalysis[];
  children: number;
  position: { x: number; y: number; width: number; height: number };
}

interface InspectModeProps {
  isActive: boolean;
  onToggle: () => void;
  onElementSelected: (info: ComponentInfo) => void;
}

const InspectMode: React.FC<InspectModeProps> = ({ isActive, onToggle, onElementSelected }) => {
  const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);
  const [selectedElement, setSelectedElement] = useState<HTMLElement | null>(null);
  const [componentInfo, setComponentInfo] = useState<ComponentInfo | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Analyze element styles
  const analyzeElementStyles = (element: HTMLElement): StyleAnalysis[] => {
    const computedStyle = getComputedStyle(element);
    const styles: StyleAnalysis[] = [];

    // Key properties to analyze
    const importantProps = [
      'backgroundColor', 'color', 'borderColor', 'boxShadow',
      'padding', 'margin', 'gap', 'borderRadius',
      'fontSize', 'fontWeight', 'lineHeight'
    ];

    importantProps.forEach(prop => {
      const value = computedStyle.getPropertyValue(prop);
      if (value && value !== 'none' && value !== 'normal' && value !== '0px') {
        const analysis = analyzeStyleProperty(prop, value, element);
        if (analysis) styles.push(analysis);
      }
    });

    return styles;
  };

  // Analyze individual style property
  const analyzeStyleProperty = (property: string, value: string, element: HTMLElement): StyleAnalysis | null => {
    const inlineStyle = element.style.getPropertyValue(property);
    const classList = Array.from(element.classList);
    
    let source: StyleAnalysis['source'] = 'computed';
    let isThemeCompliant = false;
    let suggestion = '';
    let severity: StyleAnalysis['severity'] = 'low';

    // Check if it's inline style
    if (inlineStyle) {
      source = 'inline';
      severity = 'high';
      suggestion = 'Move to CSS class or use theme variables';
    }
    // Check if it uses CSS variables
    else if (value.includes('var(--')) {
      source = 'css-variable';
      isThemeCompliant = true;
    }
    // Check if it's from classes
    else if (classList.length > 0) {
      source = 'class';
      
      // Check for hardcoded colors
      if (property.includes('color') || property.includes('Color')) {
        if (value.includes('rgb') || value.includes('#') || value === 'white' || value === 'black') {
          severity = 'high';
          suggestion = getSuggestionForColor(property, value);
        } else if (classList.some(cls => cls.includes('gray-') || cls.includes('slate-'))) {
          severity = 'medium';
          suggestion = 'Consider using theme color variables';
        }
      }
      
      // Check for hardcoded spacing
      if (property.includes('padding') || property.includes('margin') || property === 'gap') {
        if (classList.some(cls => /[pm]-\d+|gap-\d+/.test(cls))) {
          severity = 'medium';
          suggestion = 'Consider using theme spacing variables';
        }
      }
    }

    return {
      property,
      value,
      source,
      isThemeCompliant,
      suggestion,
      severity
    };
  };

  // Get color suggestion
  const getSuggestionForColor = (property: string, value: string): string => {
    const colorMap: Record<string, string> = {
      'backgroundColor': {
        'white': 'bg-background',
        'black': 'bg-foreground',
        '#ffffff': 'bg-background',
        '#000000': 'bg-foreground'
      }[value] || 'Use bg-background, bg-card, or bg-primary',
      'color': {
        'white': 'text-background',
        'black': 'text-foreground',
        '#ffffff': 'text-background',
        '#000000': 'text-foreground'
      }[value] || 'Use text-foreground, text-muted-foreground, or text-primary',
      'borderColor': 'Use border-border or border-input'
    };

    return colorMap[property] || 'Use appropriate theme color variable';
  };

  // Try to detect React component name
  const detectReactComponent = (element: HTMLElement): string | undefined => {
    // Look for React Fiber properties
    const fiberKey = Object.keys(element).find(key => 
      key.startsWith('__reactFiber') || key.startsWith('__reactInternalInstance')
    );
    
    if (fiberKey) {
      const fiber = (element as any)[fiberKey];
      if (fiber?.type?.name) return fiber.type.name;
      if (fiber?.elementType?.name) return fiber.elementType.name;
    }

    // Fallback: look for data attributes or class patterns
    const dataComponent = element.getAttribute('data-component');
    if (dataComponent) return dataComponent;

    // Try to infer from class names
    const classList = Array.from(element.classList);
    const componentClass = classList.find(cls => 
      /^[A-Z]/.test(cls) || cls.includes('Component') || cls.includes('component')
    );
    
    return componentClass;
  };

  // Handle element hover
  const handleMouseOver = (e: MouseEvent) => {
    if (!isActive) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const target = e.target as HTMLElement;
    if (target === overlayRef.current) return;
    
    setHoveredElement(target);
    updateOverlay(target);
  };

  // Handle element click
  const handleClick = (e: MouseEvent) => {
    if (!isActive) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const target = e.target as HTMLElement;
    if (target === overlayRef.current) return;
    
    setSelectedElement(target);
    
    const rect = target.getBoundingClientRect();
    const styles = analyzeElementStyles(target);
    const reactComponent = detectReactComponent(target);
    
    const info: ComponentInfo = {
      tagName: target.tagName.toLowerCase(),
      className: target.className,
      id: target.id,
      reactComponent,
      filePath: undefined, // Would need build-time analysis
      styles,
      children: target.children.length,
      position: {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height
      }
    };
    
    setComponentInfo(info);
    onElementSelected(info);
  };

  // Update overlay position
  const updateOverlay = (element: HTMLElement) => {
    if (!overlayRef.current) return;
    
    const rect = element.getBoundingClientRect();
    const overlay = overlayRef.current;
    
    overlay.style.left = `${rect.left}px`;
    overlay.style.top = `${rect.top}px`;
    overlay.style.width = `${rect.width}px`;
    overlay.style.height = `${rect.height}px`;
    overlay.style.display = 'block';
  };

  // Setup event listeners
  useEffect(() => {
    if (isActive) {
      document.addEventListener('mouseover', handleMouseOver, true);
      document.addEventListener('click', handleClick, true);
      document.body.style.cursor = 'crosshair';
    } else {
      document.removeEventListener('mouseover', handleMouseOver, true);
      document.removeEventListener('click', handleClick, true);
      document.body.style.cursor = '';
      if (overlayRef.current) {
        overlayRef.current.style.display = 'none';
      }
    }

    return () => {
      document.removeEventListener('mouseover', handleMouseOver, true);
      document.removeEventListener('click', handleClick, true);
      document.body.style.cursor = '';
    };
  }, [isActive]);

  return (
    <>
      {/* Inspection Overlay */}
      {isActive && (
        <div
          ref={overlayRef}
          className="fixed pointer-events-none z-[9999] border-2 border-blue-500 bg-blue-500/10"
          style={{ display: 'none' }}
        >
          <div className="absolute -top-6 left-0 bg-blue-500 text-white px-2 py-1 text-xs rounded">
            {hoveredElement?.tagName.toLowerCase()}
            {hoveredElement?.className && `.${hoveredElement.className.split(' ')[0]}`}
          </div>
        </div>
      )}

      {/* Toggle Button */}
      <Button
        onClick={onToggle}
        variant={isActive ? "default" : "outline"}
        size="sm"
        className="gap-2"
      >
        <Target className="h-3 w-3" />
        {isActive ? 'Exit Inspect' : 'Inspect Mode'}
      </Button>

      {/* Component Info Panel */}
      {componentInfo && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              <Search className="h-4 w-4" />
              Element Inspector
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Basic Info */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{componentInfo.tagName}</Badge>
                {componentInfo.reactComponent && (
                  <Badge variant="secondary">{componentInfo.reactComponent}</Badge>
                )}
              </div>
              
              {componentInfo.className && (
                <div className="text-xs font-mono bg-muted p-2 rounded">
                  className="{componentInfo.className}"
                </div>
              )}
            </div>

            <Separator />

            {/* Style Analysis */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Style Analysis
              </h4>
              
              <ScrollArea className="h-48">
                <div className="space-y-2">
                  {componentInfo.styles.map((style, index) => (
                    <div key={index} className="p-2 border rounded text-xs">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium">{style.property}</span>
                        <div className="flex items-center gap-1">
                          <Badge 
                            variant={style.source === 'css-variable' ? 'default' : 'outline'}
                            className="text-xs"
                          >
                            {style.source}
                          </Badge>
                          {style.severity === 'high' && (
                            <AlertTriangle className="h-3 w-3 text-red-500" />
                          )}
                          {style.isThemeCompliant && (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          )}
                        </div>
                      </div>
                      
                      <div className="font-mono text-muted-foreground mb-1">
                        {style.value}
                      </div>
                      
                      {style.suggestion && (
                        <Alert className="mt-2">
                          <Lightbulb className="h-3 w-3" />
                          <AlertDescription className="text-xs">
                            {style.suggestion}
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2">
              <Button size="sm" variant="outline" className="text-xs">
                <Code className="h-3 w-3 mr-1" />
                View Source
              </Button>
              <Button size="sm" variant="outline" className="text-xs">
                <Copy className="h-3 w-3 mr-1" />
                Copy Selector
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default InspectMode;
