import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Code, 
  Copy, 
  Check, 
  Wand2, 
  FileText,
  Palette,
  Layout,
  Zap
} from 'lucide-react';

interface CodeFix {
  type: 'component' | 'css' | 'theme-variable';
  title: string;
  description: string;
  before: string;
  after: string;
  file?: string;
  explanation: string;
}

const CodeGenerator: React.FC<{ inspectedElement?: any }> = ({ inspectedElement }) => {
  const [generatedFixes, setGeneratedFixes] = useState<CodeFix[]>([]);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // Generate fixes for inspected element
  const generateFixes = () => {
    if (!inspectedElement) return;

    const fixes: CodeFix[] = [];

    // Generate component fixes
    inspectedElement.issues?.forEach((issue: any) => {
      if (issue.type === 'hardcoded-color') {
        fixes.push(generateColorFix(issue, inspectedElement));
      } else if (issue.type === 'inline-style') {
        fixes.push(generateInlineStyleFix(issue, inspectedElement));
      } else if (issue.type === 'hardcoded-spacing') {
        fixes.push(generateSpacingFix(issue, inspectedElement));
      }
    });

    // Generate theme variable additions
    const missingVariables = detectMissingThemeVariables(inspectedElement);
    missingVariables.forEach(variable => {
      fixes.push(generateThemeVariableFix(variable));
    });

    setGeneratedFixes(fixes);
  };

  // Generate color fix
  const generateColorFix = (issue: any, element: any): CodeFix => {
    const componentName = element.componentName || 'Component';
    const className = element.element?.className || '';
    
    const before = issue.value.includes('style=') 
      ? `<${componentName} style={{backgroundColor: '${issue.value}'}}>`
      : `<${componentName} className="${className}">`;
    
    const suggestedClass = getSuggestedColorClass(issue.property, issue.value);
    const after = `<${componentName} className="${className.replace(/bg-\w+-\w+|bg-\w+/, '').trim()} ${suggestedClass}">`;

    return {
      type: 'component',
      title: `Fix ${issue.property} in ${componentName}`,
      description: `Replace hardcoded color with theme variable`,
      before: before.trim(),
      after: after.trim(),
      file: `src/components/${componentName}.tsx`,
      explanation: `Using ${suggestedClass} ensures consistency and dark mode support`
    };
  };

  // Generate inline style fix
  const generateInlineStyleFix = (issue: any, element: any): CodeFix => {
    const componentName = element.componentName || 'Component';
    const inlineStyles = element.element?.style || {};
    
    const styleEntries = Object.entries(inlineStyles).filter(([_, value]) => value);
    const styleString = styleEntries.map(([prop, value]) => `${prop}: '${value}'`).join(', ');
    
    const before = `<${componentName} style={{${styleString}}}>`;
    
    const classNames = styleEntries.map(([prop, value]) => 
      getClassForStyle(prop, value as string)
    ).filter(Boolean).join(' ');
    
    const after = `<${componentName} className="${classNames}">`;

    return {
      type: 'component',
      title: `Remove inline styles from ${componentName}`,
      description: `Convert inline styles to CSS classes`,
      before,
      after,
      file: `src/components/${componentName}.tsx`,
      explanation: `CSS classes are more maintainable and support responsive design`
    };
  };

  // Generate spacing fix
  const generateSpacingFix = (issue: any, element: any): CodeFix => {
    const componentName = element.componentName || 'Component';
    const className = element.element?.className || '';
    
    const before = `<${componentName} className="${className}">`;
    
    const newClassName = className.replace(/[pm]-\d+|gap-\d+/g, (match) => {
      if (match.startsWith('p-')) return 'p-panel-padding';
      if (match.startsWith('m-')) return 'm-section-gap';
      if (match.startsWith('gap-')) return 'gap-control-gap';
      return match;
    });
    
    const after = `<${componentName} className="${newClassName}">`;

    return {
      type: 'component',
      title: `Fix spacing in ${componentName}`,
      description: `Replace hardcoded spacing with theme variables`,
      before,
      after,
      file: `src/components/${componentName}.tsx`,
      explanation: `Theme spacing variables ensure consistency across the app`
    };
  };

  // Generate theme variable fix
  const generateThemeVariableFix = (variable: any): CodeFix => {
    const before = `// Missing theme variable`;
    const after = `// Add to your CSS variables
:root {
  --${variable.name}: ${variable.value};
}

// Or add to tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        '${variable.name}': 'hsl(var(--${variable.name}))',
      }
    }
  }
}`;

    return {
      type: 'theme-variable',
      title: `Add missing theme variable: ${variable.name}`,
      description: `Create theme variable for commonly used value`,
      before,
      after,
      file: 'globals.css or tailwind.config.js',
      explanation: `This value is used ${variable.count} times across your app`
    };
  };

  // Get suggested color class
  const getSuggestedColorClass = (property: string, value: string): string => {
    const colorMap: Record<string, string> = {
      'rgb(255, 255, 255)': property === 'backgroundColor' ? 'bg-background' : 'text-background',
      'rgb(0, 0, 0)': property === 'backgroundColor' ? 'bg-foreground' : 'text-foreground',
      'white': property === 'backgroundColor' ? 'bg-background' : 'text-background',
      'black': property === 'backgroundColor' ? 'bg-foreground' : 'text-foreground'
    };
    
    return colorMap[value] || (property === 'backgroundColor' ? 'bg-muted' : 'text-muted-foreground');
  };

  // Get class for style property
  const getClassForStyle = (property: string, value: string): string => {
    const styleMap: Record<string, (value: string) => string> = {
      backgroundColor: (v) => getSuggestedColorClass('backgroundColor', v),
      color: (v) => getSuggestedColorClass('color', v),
      padding: () => 'p-panel-padding',
      margin: () => 'm-section-gap',
      borderRadius: () => 'rounded-lg',
      fontSize: (v) => v.includes('14px') ? 'text-sm' : v.includes('16px') ? 'text-base' : 'text-lg',
      fontWeight: (v) => v === 'bold' ? 'font-bold' : v === '600' ? 'font-semibold' : 'font-medium'
    };

    return styleMap[property]?.(value) || '';
  };

  // Detect missing theme variables
  const detectMissingThemeVariables = (element: any): any[] => {
    // This would analyze common values and suggest new theme variables
    return [
      {
        name: 'panel-bg-lighter',
        value: '210 40% 98%',
        count: 5,
        usage: 'Used for lighter panel backgrounds'
      }
    ];
  };

  // Copy code to clipboard
  const copyCode = async (code: string, index: number) => {
    await navigator.clipboard.writeText(code);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  // Generate complete component refactor
  const generateComponentRefactor = (): string => {
    if (!inspectedElement?.componentName) return '';

    const componentName = inspectedElement.componentName;
    const fixes = generatedFixes.filter(f => f.type === 'component');
    
    let refactor = `// Refactored ${componentName} component\n\n`;
    refactor += `import React from 'react';\nimport { cn } from '@/lib/utils';\n\n`;
    refactor += `interface ${componentName}Props {\n`;
    refactor += `  className?: string;\n`;
    refactor += `  children?: React.ReactNode;\n`;
    refactor += `}\n\n`;
    refactor += `const ${componentName}: React.FC<${componentName}Props> = ({ className, children, ...props }) => {\n`;
    refactor += `  return (\n`;
    refactor += `    <div\n`;
    refactor += `      className={cn(\n`;
    refactor += `        "bg-background text-foreground p-panel-padding rounded-lg",\n`;
    refactor += `        className\n`;
    refactor += `      )}\n`;
    refactor += `      {...props}\n`;
    refactor += `    >\n`;
    refactor += `      {children}\n`;
    refactor += `    </div>\n`;
    refactor += `  );\n`;
    refactor += `};\n\n`;
    refactor += `export default ${componentName};`;

    return refactor;
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">Code Generator</h3>
        <Button 
          onClick={generateFixes} 
          disabled={!inspectedElement}
          size="sm"
          className="gap-2"
        >
          <Wand2 className="h-3 w-3" />
          Generate Fixes
        </Button>
      </div>

      {!inspectedElement && (
        <Alert>
          <Code className="h-4 w-4" />
          <AlertDescription>
            Use Inspect Mode to select an element, then generate code fixes.
          </AlertDescription>
        </Alert>
      )}

      {generatedFixes.length > 0 && (
        <Tabs defaultValue="fixes" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="fixes">Quick Fixes</TabsTrigger>
            <TabsTrigger value="component">Component</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
          </TabsList>

          <TabsContent value="fixes" className="space-y-3">
            {generatedFixes.filter(f => f.type === 'component').map((fix, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center justify-between">
                    <span>{fix.title}</span>
                    <Badge variant="outline" className="text-xs">
                      {fix.file}
                    </Badge>
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">{fix.description}</p>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-red-600">Before:</span>
                    </div>
                    <div className="relative">
                      <pre className="text-xs bg-red-50 dark:bg-red-950 p-3 rounded border overflow-x-auto">
                        <code>{fix.before}</code>
                      </pre>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-green-600">After:</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => copyCode(fix.after, index)}
                      >
                        {copiedIndex === index ? (
                          <Check className="h-3 w-3 text-green-500" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <div className="relative">
                      <pre className="text-xs bg-green-50 dark:bg-green-950 p-3 rounded border overflow-x-auto">
                        <code>{fix.after}</code>
                      </pre>
                    </div>
                  </div>

                  <Alert>
                    <Zap className="h-3 w-3" />
                    <AlertDescription className="text-xs">
                      {fix.explanation}
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="component" className="space-y-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center justify-between">
                  Complete Component Refactor
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={() => copyCode(generateComponentRefactor(), -1)}
                  >
                    {copiedIndex === -1 ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={generateComponentRefactor()}
                  readOnly
                  className="font-mono text-xs h-64"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="theme" className="space-y-3">
            {generatedFixes.filter(f => f.type === 'theme-variable').map((fix, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center justify-between">
                    {fix.title}
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0"
                      onClick={() => copyCode(fix.after, index + 100)}
                    >
                      {copiedIndex === index + 100 ? (
                        <Check className="h-3 w-3 text-green-500" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-3 rounded border overflow-x-auto">
                    <code>{fix.after}</code>
                  </pre>
                  <Alert className="mt-2">
                    <Palette className="h-3 w-3" />
                    <AlertDescription className="text-xs">
                      {fix.explanation}
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default CodeGenerator;
