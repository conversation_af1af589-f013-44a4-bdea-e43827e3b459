/**
 * Component Style Detector
 * Analyzes components for hardcoded styles vs theme variable usage
 */

export interface StyleIssue {
  type: 'hardcoded-color' | 'hardcoded-spacing' | 'inline-style' | 'missing-theme-var' | 'deprecated-class';
  description: string;
  suggestion: string;
  line?: number;
  severity: 'low' | 'medium' | 'high';
}

export interface ComponentAnalysis {
  component: string;
  file: string;
  issues: StyleIssue[];
  themeCompliance: number; // 0-100%
  totalStyles: number;
  themeStyles: number;
}

export class ComponentStyleDetector {
  private hardcodedColorPatterns = [
    /bg-white|bg-black|text-white|text-black/g,
    /bg-gray-\d+|text-gray-\d+|border-gray-\d+/g,
    /bg-red-\d+|bg-blue-\d+|bg-green-\d+|bg-yellow-\d+/g,
    /#[0-9a-fA-F]{3,6}/g,
    /rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/g,
    /rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)/g
  ];

  private themeColorPatterns = [
    /bg-background|bg-foreground|bg-card|bg-primary|bg-secondary|bg-muted|bg-accent/g,
    /text-background|text-foreground|text-card|text-primary|text-secondary|text-muted|text-accent/g,
    /border-background|border-foreground|border-card|border-primary|border-secondary|border-muted|border-accent/g,
    /var\(--[a-zA-Z-]+\)/g
  ];

  private hardcodedSpacingPatterns = [
    /p-\d+|px-\d+|py-\d+|pt-\d+|pb-\d+|pl-\d+|pr-\d+/g,
    /m-\d+|mx-\d+|my-\d+|mt-\d+|mb-\d+|ml-\d+|mr-\d+/g,
    /gap-\d+|space-x-\d+|space-y-\d+/g,
    /w-\d+|h-\d+|min-w-\d+|min-h-\d+|max-w-\d+|max-h-\d+/g
  ];

  private themeSpacingPatterns = [
    /panel-gap|panel-padding|section-gap|control-gap|item-gap/g
  ];

  /**
   * Analyze a component's source code for style issues
   */
  analyzeComponent(componentCode: string, fileName: string): ComponentAnalysis {
    const issues: StyleIssue[] = [];
    let totalStyles = 0;
    let themeStyles = 0;

    // Split code into lines for line number tracking
    const lines = componentCode.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Check for hardcoded colors
      this.hardcodedColorPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          totalStyles += matches.length;
          matches.forEach(match => {
            issues.push({
              type: 'hardcoded-color',
              description: `Hardcoded color found: ${match}`,
              suggestion: this.suggestThemeColor(match),
              line: lineNumber,
              severity: this.getColorSeverity(match)
            });
          });
        }
      });

      // Check for theme colors (positive)
      this.themeColorPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          themeStyles += matches.length;
          totalStyles += matches.length;
        }
      });

      // Check for hardcoded spacing
      this.hardcodedSpacingPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          totalStyles += matches.length;
          matches.forEach(match => {
            issues.push({
              type: 'hardcoded-spacing',
              description: `Hardcoded spacing found: ${match}`,
              suggestion: this.suggestThemeSpacing(match),
              line: lineNumber,
              severity: 'medium'
            });
          });
        }
      });

      // Check for theme spacing (positive)
      this.themeSpacingPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          themeStyles += matches.length;
          totalStyles += matches.length;
        }
      });

      // Check for inline styles
      if (line.includes('style={{') || line.includes('style={')) {
        const styleMatch = line.match(/style=\{[^}]+\}/);
        if (styleMatch) {
          totalStyles++;
          issues.push({
            type: 'inline-style',
            description: `Inline style found: ${styleMatch[0]}`,
            suggestion: 'Consider using Tailwind classes or CSS variables',
            line: lineNumber,
            severity: 'high'
          });
        }
      }

      // Check for deprecated classes
      const deprecatedPatterns = [
        /bg-white|bg-black/g,
        /text-white|text-black/g
      ];

      deprecatedPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          matches.forEach(match => {
            issues.push({
              type: 'deprecated-class',
              description: `Deprecated class found: ${match}`,
              suggestion: match.includes('white') ? 'Use bg-background or text-foreground' : 'Use bg-foreground or text-background',
              line: lineNumber,
              severity: 'medium'
            });
          });
        }
      });
    });

    const themeCompliance = totalStyles > 0 ? Math.round((themeStyles / totalStyles) * 100) : 100;

    return {
      component: this.extractComponentName(fileName),
      file: fileName,
      issues,
      themeCompliance,
      totalStyles,
      themeStyles
    };
  }

  /**
   * Suggest theme color replacement
   */
  private suggestThemeColor(hardcodedColor: string): string {
    const colorMap: Record<string, string> = {
      'bg-white': 'bg-background',
      'bg-black': 'bg-foreground',
      'text-white': 'text-background',
      'text-black': 'text-foreground',
      'bg-gray-100': 'bg-muted',
      'bg-gray-200': 'bg-muted',
      'bg-gray-800': 'bg-card',
      'bg-gray-900': 'bg-background',
      'text-gray-600': 'text-muted-foreground',
      'text-gray-700': 'text-foreground',
      'border-gray-200': 'border-border',
      'border-gray-300': 'border-border'
    };

    return colorMap[hardcodedColor] || 'Use appropriate theme variable (bg-background, text-foreground, etc.)';
  }

  /**
   * Suggest theme spacing replacement
   */
  private suggestThemeSpacing(hardcodedSpacing: string): string {
    if (hardcodedSpacing.includes('gap')) {
      return 'Consider using gap-panel-gap, gap-control-gap, or gap-item-gap';
    }
    if (hardcodedSpacing.includes('p-') || hardcodedSpacing.includes('px-') || hardcodedSpacing.includes('py-')) {
      return 'Consider using p-panel-padding or custom spacing variables';
    }
    if (hardcodedSpacing.includes('m-') || hardcodedSpacing.includes('mx-') || hardcodedSpacing.includes('my-')) {
      return 'Consider using mb-section-gap or custom spacing variables';
    }
    return 'Use theme spacing variables for consistency';
  }

  /**
   * Get severity level for color issues
   */
  private getColorSeverity(color: string): 'low' | 'medium' | 'high' {
    if (color.includes('white') || color.includes('black')) {
      return 'high'; // These break theme switching
    }
    if (color.startsWith('#') || color.includes('rgb')) {
      return 'high'; // Hardcoded hex/rgb values
    }
    if (color.includes('gray')) {
      return 'medium'; // Gray colors should use theme
    }
    return 'low';
  }

  /**
   * Extract component name from file path
   */
  private extractComponentName(filePath: string): string {
    const fileName = filePath.split('/').pop() || '';
    return fileName.replace(/\.(tsx|ts|jsx|js)$/, '');
  }

  /**
   * Analyze multiple components
   */
  async analyzeComponents(componentFiles: { path: string; content: string }[]): Promise<ComponentAnalysis[]> {
    return componentFiles.map(file => 
      this.analyzeComponent(file.content, file.path)
    );
  }

  /**
   * Generate summary report
   */
  generateSummaryReport(analyses: ComponentAnalysis[]): {
    totalComponents: number;
    averageCompliance: number;
    totalIssues: number;
    highSeverityIssues: number;
    componentsNeedingWork: ComponentAnalysis[];
  } {
    const totalComponents = analyses.length;
    const averageCompliance = analyses.reduce((sum, analysis) => sum + analysis.themeCompliance, 0) / totalComponents;
    const totalIssues = analyses.reduce((sum, analysis) => sum + analysis.issues.length, 0);
    const highSeverityIssues = analyses.reduce((sum, analysis) => 
      sum + analysis.issues.filter(issue => issue.severity === 'high').length, 0
    );
    const componentsNeedingWork = analyses
      .filter(analysis => analysis.themeCompliance < 80 || analysis.issues.some(issue => issue.severity === 'high'))
      .sort((a, b) => a.themeCompliance - b.themeCompliance);

    return {
      totalComponents,
      averageCompliance: Math.round(averageCompliance),
      totalIssues,
      highSeverityIssues,
      componentsNeedingWork
    };
  }
}

export const styleDetector = new ComponentStyleDetector();
