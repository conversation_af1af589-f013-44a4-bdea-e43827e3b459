/**
 * Advanced Style Analyzer for AI-coded apps
 * Detects styling inconsistencies and suggests theme replacements
 */

export interface StyleRule {
  selector: string;
  property: string;
  value: string;
  source: 'inline' | 'tailwind' | 'css-module' | 'global-css' | 'styled-component';
  file?: string;
  line?: number;
}

export interface StyleSuggestion {
  current: string;
  suggested: string;
  reason: string;
  impact: 'low' | 'medium' | 'high';
  autoFixable: boolean;
}

export interface ComponentStyleReport {
  element: HTMLElement;
  componentName?: string;
  filePath?: string;
  issues: StyleIssue[];
  suggestions: StyleSuggestion[];
  themeCompliance: number;
  inconsistencies: string[];
}

export interface StyleIssue {
  type: 'hardcoded-color' | 'hardcoded-spacing' | 'inline-style' | 'inconsistent-pattern' | 'accessibility';
  severity: 'low' | 'medium' | 'high';
  description: string;
  element: string;
  property: string;
  value: string;
  suggestion: string;
}

export class StyleAnalyzer {
  private themeColors = new Set([
    'background', 'foreground', 'card', 'card-foreground', 'popover', 'popover-foreground',
    'primary', 'primary-foreground', 'secondary', 'secondary-foreground', 'muted', 
    'muted-foreground', 'accent', 'accent-foreground', 'destructive', 'destructive-foreground',
    'border', 'input', 'ring'
  ]);

  private themeSpacing = new Set([
    'panel-gap', 'panel-padding', 'section-gap', 'control-gap', 'item-gap'
  ]);

  /**
   * Analyze an element's styling comprehensively
   */
  analyzeElement(element: HTMLElement): ComponentStyleReport {
    const computedStyle = getComputedStyle(element);
    const issues: StyleIssue[] = [];
    const suggestions: StyleSuggestion[] = [];
    const inconsistencies: string[] = [];

    // Get component info
    const componentName = this.detectComponentName(element);
    const filePath = this.detectFilePath(element);

    // Analyze all style properties
    const styleProps = this.getRelevantStyleProperties(computedStyle);
    
    styleProps.forEach(({ property, value }) => {
      // Check for hardcoded colors
      if (this.isColorProperty(property)) {
        const colorIssue = this.analyzeColorProperty(property, value, element);
        if (colorIssue) issues.push(colorIssue);
      }

      // Check for hardcoded spacing
      if (this.isSpacingProperty(property)) {
        const spacingIssue = this.analyzeSpacingProperty(property, value, element);
        if (spacingIssue) issues.push(spacingIssue);
      }

      // Check for inline styles
      if (element.style.getPropertyValue(property)) {
        issues.push({
          type: 'inline-style',
          severity: 'high',
          description: `Inline style detected for ${property}`,
          element: this.getElementSelector(element),
          property,
          value,
          suggestion: 'Move to CSS class or use theme variables'
        });
      }
    });

    // Check for accessibility issues
    const accessibilityIssues = this.checkAccessibility(element, computedStyle);
    issues.push(...accessibilityIssues);

    // Generate suggestions
    issues.forEach(issue => {
      const suggestion = this.generateSuggestion(issue);
      if (suggestion) suggestions.push(suggestion);
    });

    // Calculate theme compliance
    const themeCompliance = this.calculateThemeCompliance(element, computedStyle);

    // Detect inconsistencies
    const elementInconsistencies = this.detectInconsistencies(element, computedStyle);
    inconsistencies.push(...elementInconsistencies);

    return {
      element,
      componentName,
      filePath,
      issues,
      suggestions,
      themeCompliance,
      inconsistencies
    };
  }

  /**
   * Detect React component name from element
   */
  private detectComponentName(element: HTMLElement): string | undefined {
    // Try React DevTools approach
    const reactFiberKey = Object.keys(element).find(key => 
      key.startsWith('__reactFiber') || key.startsWith('__reactInternalInstance')
    );
    
    if (reactFiberKey) {
      const fiber = (element as any)[reactFiberKey];
      
      // Try different fiber properties
      if (fiber?.type?.displayName) return fiber.type.displayName;
      if (fiber?.type?.name) return fiber.type.name;
      if (fiber?.elementType?.name) return fiber.elementType.name;
      
      // Walk up the fiber tree
      let currentFiber = fiber;
      while (currentFiber) {
        if (currentFiber.type?.name && currentFiber.type.name !== 'div') {
          return currentFiber.type.name;
        }
        currentFiber = currentFiber.return;
      }
    }

    // Fallback methods
    const dataComponent = element.getAttribute('data-component');
    if (dataComponent) return dataComponent;

    // Try to infer from class names
    const classList = Array.from(element.classList);
    const componentClass = classList.find(cls => 
      /^[A-Z][a-zA-Z]*$/.test(cls) || cls.includes('Component')
    );
    
    return componentClass;
  }

  /**
   * Try to detect file path (limited without build-time info)
   */
  private detectFilePath(element: HTMLElement): string | undefined {
    // This would ideally be injected during build time
    const dataFile = element.getAttribute('data-file');
    if (dataFile) return dataFile;

    // Try to infer from component name
    const componentName = this.detectComponentName(element);
    if (componentName) {
      return `src/components/${componentName}.tsx`; // Best guess
    }

    return undefined;
  }

  /**
   * Get relevant style properties for analysis
   */
  private getRelevantStyleProperties(computedStyle: CSSStyleDeclaration): Array<{property: string, value: string}> {
    const relevantProps = [
      'backgroundColor', 'color', 'borderColor', 'borderTopColor', 'borderRightColor', 
      'borderBottomColor', 'borderLeftColor', 'boxShadow', 'textShadow',
      'padding', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
      'margin', 'marginTop', 'marginRight', 'marginBottom', 'marginLeft',
      'gap', 'rowGap', 'columnGap', 'borderRadius', 'fontSize', 'lineHeight'
    ];

    return relevantProps
      .map(prop => ({ property: prop, value: computedStyle.getPropertyValue(prop) }))
      .filter(({ value }) => value && value !== 'none' && value !== 'normal' && value !== '0px');
  }

  /**
   * Check if property is color-related
   */
  private isColorProperty(property: string): boolean {
    return property.toLowerCase().includes('color') || 
           property === 'backgroundColor' || 
           property.includes('Shadow');
  }

  /**
   * Check if property is spacing-related
   */
  private isSpacingProperty(property: string): boolean {
    return property.includes('padding') || 
           property.includes('margin') || 
           property.includes('gap') || 
           property === 'borderRadius';
  }

  /**
   * Analyze color property for issues
   */
  private analyzeColorProperty(property: string, value: string, element: HTMLElement): StyleIssue | null {
    // Check for hardcoded colors
    if (value.includes('rgb') || value.startsWith('#') || value === 'white' || value === 'black') {
      return {
        type: 'hardcoded-color',
        severity: 'high',
        description: `Hardcoded color value: ${value}`,
        element: this.getElementSelector(element),
        property,
        value,
        suggestion: this.suggestThemeColor(property, value)
      };
    }

    // Check for non-theme CSS variables
    if (value.includes('var(--') && !this.usesThemeVariable(value)) {
      return {
        type: 'inconsistent-pattern',
        severity: 'medium',
        description: `Non-theme CSS variable: ${value}`,
        element: this.getElementSelector(element),
        property,
        value,
        suggestion: 'Consider using theme color variables'
      };
    }

    return null;
  }

  /**
   * Analyze spacing property for issues
   */
  private analyzeSpacingProperty(property: string, value: string, element: HTMLElement): StyleIssue | null {
    // Check for hardcoded pixel values
    if (value.includes('px') && !value.includes('var(--')) {
      const classList = Array.from(element.classList);
      const hasTailwindSpacing = classList.some(cls => /[pm]-\d+|gap-\d+/.test(cls));
      
      if (hasTailwindSpacing) {
        return {
          type: 'hardcoded-spacing',
          severity: 'medium',
          description: `Hardcoded spacing value: ${value}`,
          element: this.getElementSelector(element),
          property,
          value,
          suggestion: this.suggestThemeSpacing(property)
        };
      }
    }

    return null;
  }

  /**
   * Check accessibility issues
   */
  private checkAccessibility(element: HTMLElement, computedStyle: CSSStyleDeclaration): StyleIssue[] {
    const issues: StyleIssue[] = [];

    const backgroundColor = computedStyle.backgroundColor;
    const color = computedStyle.color;

    if (backgroundColor && color) {
      const contrast = this.calculateContrastRatio(backgroundColor, color);
      if (contrast < 4.5) {
        issues.push({
          type: 'accessibility',
          severity: 'high',
          description: `Low contrast ratio: ${contrast.toFixed(2)}:1`,
          element: this.getElementSelector(element),
          property: 'contrast',
          value: `${contrast.toFixed(2)}:1`,
          suggestion: 'Increase contrast to meet WCAG AA standards (4.5:1)'
        });
      }
    }

    return issues;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  private calculateContrastRatio(color1: string, color2: string): number {
    // Simplified contrast calculation
    // In a real implementation, you'd parse RGB values and calculate luminance
    return 4.5; // Placeholder
  }

  /**
   * Generate suggestion for fixing an issue
   */
  private generateSuggestion(issue: StyleIssue): StyleSuggestion | null {
    switch (issue.type) {
      case 'hardcoded-color':
        return {
          current: issue.value,
          suggested: this.suggestThemeColor(issue.property, issue.value),
          reason: 'Use theme colors for consistency and dark mode support',
          impact: 'high',
          autoFixable: true
        };
      
      case 'hardcoded-spacing':
        return {
          current: issue.value,
          suggested: this.suggestThemeSpacing(issue.property),
          reason: 'Use theme spacing for consistency across the app',
          impact: 'medium',
          autoFixable: true
        };
      
      case 'inline-style':
        return {
          current: `style="${issue.property}: ${issue.value}"`,
          suggested: `className="..."`,
          reason: 'Move styles to CSS classes for better maintainability',
          impact: 'medium',
          autoFixable: false
        };
      
      default:
        return null;
    }
  }

  /**
   * Suggest theme color replacement
   */
  private suggestThemeColor(property: string, value: string): string {
    const colorMap: Record<string, Record<string, string>> = {
      backgroundColor: {
        'white': 'bg-background',
        '#ffffff': 'bg-background',
        'black': 'bg-foreground',
        '#000000': 'bg-foreground'
      },
      color: {
        'white': 'text-background',
        '#ffffff': 'text-background',
        'black': 'text-foreground',
        '#000000': 'text-foreground'
      }
    };

    return colorMap[property]?.[value] || 'Use appropriate theme color variable';
  }

  /**
   * Suggest theme spacing replacement
   */
  private suggestThemeSpacing(property: string): string {
    if (property.includes('gap')) return 'Use gap-panel-gap or gap-control-gap';
    if (property.includes('padding')) return 'Use p-panel-padding';
    if (property.includes('margin')) return 'Use mb-section-gap';
    return 'Use theme spacing variables';
  }

  /**
   * Check if value uses theme variables
   */
  private usesThemeVariable(value: string): boolean {
    return Array.from(this.themeColors).some(color => value.includes(`--${color}`)) ||
           Array.from(this.themeSpacing).some(spacing => value.includes(`--${spacing}`));
  }

  /**
   * Calculate theme compliance percentage
   */
  private calculateThemeCompliance(element: HTMLElement, computedStyle: CSSStyleDeclaration): number {
    const styleProps = this.getRelevantStyleProperties(computedStyle);
    let compliantProps = 0;

    styleProps.forEach(({ property, value }) => {
      if (value.includes('var(--') && this.usesThemeVariable(value)) {
        compliantProps++;
      }
    });

    return styleProps.length > 0 ? Math.round((compliantProps / styleProps.length) * 100) : 100;
  }

  /**
   * Detect styling inconsistencies
   */
  private detectInconsistencies(element: HTMLElement, computedStyle: CSSStyleDeclaration): string[] {
    const inconsistencies: string[] = [];

    // Check for mixed spacing units
    const spacingProps = ['padding', 'margin', 'gap'];
    const units = new Set<string>();
    
    spacingProps.forEach(prop => {
      const value = computedStyle.getPropertyValue(prop);
      if (value.includes('px')) units.add('px');
      if (value.includes('rem')) units.add('rem');
      if (value.includes('em')) units.add('em');
    });

    if (units.size > 1) {
      inconsistencies.push(`Mixed spacing units: ${Array.from(units).join(', ')}`);
    }

    return inconsistencies;
  }

  /**
   * Get CSS selector for element
   */
  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`;
    
    const classes = Array.from(element.classList).slice(0, 2).join('.');
    if (classes) return `.${classes}`;
    
    return element.tagName.toLowerCase();
  }
}

export const styleAnalyzer = new StyleAnalyzer();
