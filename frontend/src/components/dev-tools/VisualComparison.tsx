import React, { useState, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>lider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Camera, 
  Upload, 
  Eye, 
  EyeOff, 
  Move, 
  RotateCcw,
  Ruler,
  Palette,
  Download,
  Layers
} from 'lucide-react';

interface ComparisonState {
  screenshot: string | null;
  figmaImage: string | null;
  opacity: number;
  offsetX: number;
  offsetY: number;
  scale: number;
  showOverlay: boolean;
  showGrid: boolean;
}

const VisualComparison: React.FC = () => {
  const [comparison, setComparison] = useState<ComparisonState>({
    screenshot: null,
    figmaImage: null,
    opacity: 50,
    offsetX: 0,
    offsetY: 0,
    scale: 1,
    showOverlay: true,
    showGrid: false
  });

  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const figmaInputRef = useRef<HTMLInputElement>(null);

  // Capture screenshot of current app
  const captureScreenshot = useCallback(async () => {
    try {
      // Use html2canvas or similar library in real implementation
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // For demo, create a placeholder
      canvas.width = 800;
      canvas.height = 600;
      if (ctx) {
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#333';
        ctx.font = '16px Arial';
        ctx.fillText('App Screenshot (Demo)', 20, 40);
        ctx.fillText('In real implementation, use html2canvas', 20, 70);
      }
      
      const screenshot = canvas.toDataURL();
      setComparison(prev => ({ ...prev, screenshot }));
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
    }
  }, []);

  // Handle Figma image upload
  const handleFigmaUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const figmaImage = e.target?.result as string;
        setComparison(prev => ({ ...prev, figmaImage }));
      };
      reader.readAsDataURL(file);
    }
  }, []);

  // Handle mouse events for dragging
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: event.clientX, y: event.clientY });
  }, []);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isDragging) return;
    
    const deltaX = event.clientX - dragStart.x;
    const deltaY = event.clientY - dragStart.y;
    
    setComparison(prev => ({
      ...prev,
      offsetX: prev.offsetX + deltaX,
      offsetY: prev.offsetY + deltaY
    }));
    
    setDragStart({ x: event.clientX, y: event.clientY });
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Reset comparison
  const resetComparison = useCallback(() => {
    setComparison(prev => ({
      ...prev,
      offsetX: 0,
      offsetY: 0,
      scale: 1,
      opacity: 50
    }));
  }, []);

  // Analyze color differences
  const analyzeColorDifferences = useCallback(() => {
    if (!comparison.screenshot || !comparison.figmaImage) return [];

    // In real implementation, this would:
    // 1. Sample colors from both images
    // 2. Compare color palettes
    // 3. Identify mismatched colors
    // 4. Suggest theme variable replacements

    return [
      {
        position: { x: 100, y: 50 },
        appColor: '#ffffff',
        figmaColor: '#f8f9fa',
        difference: 'Background color mismatch',
        suggestion: 'Use bg-background instead of bg-white'
      },
      {
        position: { x: 200, y: 100 },
        appColor: '#000000',
        figmaColor: '#1a1a1a',
        difference: 'Text color too dark',
        suggestion: 'Use text-foreground for better contrast'
      }
    ];
  }, [comparison.screenshot, comparison.figmaImage]);

  // Measure spacing differences
  const measureSpacing = useCallback(() => {
    // In real implementation, this would detect spacing patterns
    return [
      {
        element: 'Button padding',
        app: '12px',
        figma: '16px',
        suggestion: 'Use p-panel-padding (16px)'
      },
      {
        element: 'Card gap',
        app: '8px',
        figma: '12px',
        suggestion: 'Use gap-control-gap (12px)'
      }
    ];
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">Visual Comparison</h3>
        <div className="flex gap-2">
          <Button onClick={captureScreenshot} size="sm" variant="outline">
            <Camera className="h-3 w-3 mr-1" />
            Screenshot
          </Button>
          <Button 
            onClick={() => figmaInputRef.current?.click()} 
            size="sm" 
            variant="outline"
          >
            <Upload className="h-3 w-3 mr-1" />
            Upload Figma
          </Button>
        </div>
      </div>

      <input
        ref={figmaInputRef}
        type="file"
        accept="image/*"
        onChange={handleFigmaUpload}
        className="hidden"
      />

      {/* Comparison Canvas */}
      {(comparison.screenshot || comparison.figmaImage) && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center justify-between">
              <span>Visual Overlay</span>
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => setComparison(prev => ({ ...prev, showOverlay: !prev.showOverlay }))}
                >
                  {comparison.showOverlay ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => setComparison(prev => ({ ...prev, showGrid: !prev.showGrid }))}
                >
                  <Ruler className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={resetComparison}
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Controls */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs">Opacity</Label>
                  <Slider
                    value={[comparison.opacity]}
                    onValueChange={([value]) => setComparison(prev => ({ ...prev, opacity: value }))}
                    max={100}
                    step={1}
                    className="w-full"
                  />
                  <span className="text-xs text-muted-foreground">{comparison.opacity}%</span>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Scale</Label>
                  <Slider
                    value={[comparison.scale * 100]}
                    onValueChange={([value]) => setComparison(prev => ({ ...prev, scale: value / 100 }))}
                    min={50}
                    max={200}
                    step={5}
                    className="w-full"
                  />
                  <span className="text-xs text-muted-foreground">{Math.round(comparison.scale * 100)}%</span>
                </div>
              </div>

              {/* Canvas */}
              <div 
                className="relative border rounded-lg overflow-hidden bg-checkered"
                style={{ height: '400px' }}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
              >
                {/* App Screenshot */}
                {comparison.screenshot && (
                  <img
                    src={comparison.screenshot}
                    alt="App Screenshot"
                    className="absolute inset-0 w-full h-full object-contain"
                    style={{ zIndex: 1 }}
                  />
                )}

                {/* Figma Overlay */}
                {comparison.figmaImage && comparison.showOverlay && (
                  <img
                    src={comparison.figmaImage}
                    alt="Figma Design"
                    className="absolute inset-0 w-full h-full object-contain cursor-move"
                    style={{
                      zIndex: 2,
                      opacity: comparison.opacity / 100,
                      transform: `translate(${comparison.offsetX}px, ${comparison.offsetY}px) scale(${comparison.scale})`,
                      transformOrigin: 'top left'
                    }}
                  />
                )}

                {/* Grid Overlay */}
                {comparison.showGrid && (
                  <div 
                    className="absolute inset-0 pointer-events-none"
                    style={{
                      zIndex: 3,
                      backgroundImage: `
                        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
                      `,
                      backgroundSize: '20px 20px'
                    }}
                  />
                )}

                {/* Drag Hint */}
                {comparison.figmaImage && (
                  <div className="absolute top-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                    <Move className="h-3 w-3 inline mr-1" />
                    Drag to align
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Results */}
      {comparison.screenshot && comparison.figmaImage && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Color Differences */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Color Differences
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analyzeColorDifferences().map((diff, index) => (
                  <div key={index} className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div 
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: diff.appColor }}
                        title="App Color"
                      />
                      <span className="text-xs">→</span>
                      <div 
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: diff.figmaColor }}
                        title="Figma Color"
                      />
                      <Badge variant="outline" className="text-xs ml-auto">
                        x: {diff.position.x}, y: {diff.position.y}
                      </Badge>
                    </div>
                    <p className="text-xs mb-1">{diff.difference}</p>
                    <Alert>
                      <AlertDescription className="text-xs">
                        {diff.suggestion}
                      </AlertDescription>
                    </Alert>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Spacing Differences */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <Ruler className="h-4 w-4" />
                Spacing Differences
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {measureSpacing().map((spacing, index) => (
                  <div key={index} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium">{spacing.element}</span>
                      <div className="flex items-center gap-2 text-xs">
                        <Badge variant="outline">{spacing.app}</Badge>
                        <span>→</span>
                        <Badge variant="default">{spacing.figma}</Badge>
                      </div>
                    </div>
                    <Alert>
                      <AlertDescription className="text-xs">
                        {spacing.suggestion}
                      </AlertDescription>
                    </Alert>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Instructions */}
      {!comparison.screenshot && !comparison.figmaImage && (
        <Alert>
          <Layers className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p>Compare your app with Figma designs:</p>
              <ol className="list-decimal list-inside text-xs space-y-1 ml-4">
                <li>Click "Screenshot" to capture your current app</li>
                <li>Click "Upload Figma" to add your design mockup</li>
                <li>Adjust opacity and position to align the images</li>
                <li>Review color and spacing differences</li>
                <li>Apply suggested theme fixes</li>
              </ol>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default VisualComparison;
