import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>ting<PERSON>, Palette, Download, Eye, EyeOff, RotateCcw, Co<PERSON>, Check, AlertTriangle, TestTube, Shield, Zap, Target, Code, Lightbulb, CheckCircle } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/utils/ui/classNames';
import { themeValidator, type ThemeValidationReport } from './ThemeValidator';
import ComponentTestingGrid from './ComponentTestingGrid';
import InspectMode from './InspectMode';
import { styleAnalyzer, type ComponentStyleReport } from './StyleAnalyzer';
import AppScanner from './AppScanner';
import CodeGenerator from './CodeGenerator';
import VisualComparison from './VisualComparison';
import StyleDetector from './StyleDetector';

interface ThemeVariable {
  name: string;
  value: string;
  category: 'colors' | 'spacing' | 'shadows' | 'typography' | 'custom' | 'tailwind';
  description?: string;
  configPath?: string; // For Tailwind config variables
}

interface ComponentStyleIssue {
  component: string;
  file: string;
  issues: string[];
  severity: 'low' | 'medium' | 'high';
}

const ThemeDeveloperPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [activeTab, setActiveTab] = useState('colors');
  const [themeVariables, setThemeVariables] = useState<ThemeVariable[]>([]);
  const [originalVariables, setOriginalVariables] = useState<ThemeVariable[]>([]);
  const [styleIssues, setStyleIssues] = useState<ComponentStyleIssue[]>([]);
  const [exportedCSS, setExportedCSS] = useState('');
  const [copied, setCopied] = useState(false);
  const [validationReport, setValidationReport] = useState<ThemeValidationReport | null>(null);
  const [showTestingGrid, setShowTestingGrid] = useState(false);
  const [tailwindVariables, setTailwindVariables] = useState<ThemeVariable[]>([]);
  const [inspectMode, setInspectMode] = useState(false);
  const [inspectedElement, setInspectedElement] = useState<ComponentStyleReport | null>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  // Extract current CSS variables from document
  useEffect(() => {
    const extractThemeVariables = () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      const variables: ThemeVariable[] = [];

      // Define variable categories based on your actual index.css
      const colorVars = [
        'background', 'foreground', 'card', 'card-foreground', 'popover', 'popover-foreground',
        'primary', 'primary-foreground', 'secondary', 'secondary-foreground', 'muted',
        'muted-foreground', 'accent', 'accent-foreground', 'destructive', 'destructive-foreground',
        'border', 'input', 'ring'
      ];

      const spacingVars = [
        'panel-gap', 'panel-padding', 'section-gap', 'control-gap', 'item-gap',
        'radius', 'panel-radius'
      ];

      const shadowVars = ['panel-shadow'];

      const panelVars = ['panel-bg', 'panel-bg-lighter', 'panel-bg-darker'];

      const analyticsVars = [
        'analytics-bar', 'analytics-node-a', 'analytics-node-b', 'analytics-node-selected',
        'analytics-label', 'analytics-tooltip-bg', 'analytics-tooltip-border', 'analytics-tooltip-shadow',
        'analytics-score-overall', 'analytics-score-key', 'analytics-score-bpm',
        'analytics-score-energy', 'analytics-score-artist'
      ];

      // Extract color variables
      colorVars.forEach(varName => {
        const value = computedStyle.getPropertyValue(`--${varName}`).trim();
        if (value) {
          variables.push({
            name: varName,
            value,
            category: 'colors',
            description: getVariableDescription(varName)
          });
        }
      });

      // Extract spacing variables
      spacingVars.forEach(varName => {
        const value = computedStyle.getPropertyValue(`--${varName}`).trim();
        if (value) {
          variables.push({
            name: varName,
            value,
            category: 'spacing',
            description: getVariableDescription(varName)
          });
        }
      });

      // Extract shadow variables
      shadowVars.forEach(varName => {
        const value = computedStyle.getPropertyValue(`--${varName}`).trim();
        if (value) {
          variables.push({
            name: varName,
            value,
            category: 'shadows',
            description: getVariableDescription(varName)
          });
        }
      });

      // Extract panel variables
      panelVars.forEach(varName => {
        const value = computedStyle.getPropertyValue(`--${varName}`).trim();
        if (value) {
          variables.push({
            name: varName,
            value,
            category: 'custom',
            description: getVariableDescription(varName)
          });
        }
      });

      // Extract analytics variables
      analyticsVars.forEach(varName => {
        const value = computedStyle.getPropertyValue(`--${varName}`).trim();
        if (value) {
          variables.push({
            name: varName,
            value,
            category: 'custom',
            description: getVariableDescription(varName)
          });
        }
      });

      setThemeVariables(variables);
      setOriginalVariables([...variables]);
    };

    // Extract Tailwind config variables
    const extractTailwindVariables = () => {
      const tailwindVars: ThemeVariable[] = [
        {
          name: 'panel-shadow',
          value: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          category: 'tailwind',
          description: 'Enhanced shadow for panels',
          configPath: 'theme.extend.boxShadow.panel'
        }
      ];
      setTailwindVariables(tailwindVars);
    };

    extractThemeVariables();
    extractTailwindVariables();
  }, []);

  // Get variable description
  const getVariableDescription = (varName: string): string => {
    const descriptions: Record<string, string> = {
      // Core colors
      'background': 'Main background color',
      'foreground': 'Main text color',
      'card': 'Card background color',
      'card-foreground': 'Card text color',
      'popover': 'Popover background color',
      'popover-foreground': 'Popover text color',
      'primary': 'Primary brand color',
      'primary-foreground': 'Primary text color',
      'secondary': 'Secondary color for less prominent elements',
      'secondary-foreground': 'Secondary text color',
      'muted': 'Muted background color',
      'muted-foreground': 'Muted text color',
      'accent': 'Accent color for highlights',
      'accent-foreground': 'Accent text color',
      'destructive': 'Destructive/error color',
      'destructive-foreground': 'Destructive text color',
      'border': 'Border color',
      'input': 'Input border color',
      'ring': 'Focus ring color',

      // Spacing
      'panel-gap': 'Standard gap between panels (10px)',
      'panel-padding': 'Standard padding inside panels (16px)',
      'section-gap': 'Gap between major sections (24px)',
      'control-gap': 'Gap between controls (8px)',
      'item-gap': 'Gap between related items (4px)',
      'radius': 'Border radius for components',
      'panel-radius': 'Border radius for panels',

      // Shadows
      'panel-shadow': 'Shadow for panel elements',

      // Panel backgrounds
      'panel-bg': 'Panel background color',
      'panel-bg-lighter': 'Lighter panel background',
      'panel-bg-darker': 'Darker panel background',

      // Analytics colors
      'analytics-bar': 'Analytics bar color',
      'analytics-node-a': 'Analytics node A color',
      'analytics-node-b': 'Analytics node B color',
      'analytics-node-selected': 'Selected analytics node color',
      'analytics-label': 'Analytics label color',
      'analytics-tooltip-bg': 'Analytics tooltip background',
      'analytics-tooltip-border': 'Analytics tooltip border',
      'analytics-tooltip-shadow': 'Analytics tooltip shadow',
      'analytics-score-overall': 'Overall score color',
      'analytics-score-key': 'Key score color',
      'analytics-score-bpm': 'BPM score color',
      'analytics-score-energy': 'Energy score color',
      'analytics-score-artist': 'Artist score color'
    };
    return descriptions[varName] || 'Custom theme variable';
  };

  // Update CSS variable in real-time
  const updateVariable = (name: string, value: string) => {
    document.documentElement.style.setProperty(`--${name}`, value);
    setThemeVariables(prev => 
      prev.map(v => v.name === name ? { ...v, value } : v)
    );
  };

  // Reset all variables to original values
  const resetToOriginal = () => {
    originalVariables.forEach(variable => {
      document.documentElement.style.setProperty(`--${variable.name}`, variable.value);
    });
    setThemeVariables([...originalVariables]);
  };

  // Generate CSS export
  const generateCSSExport = () => {
    const lightVars = themeVariables.filter(v => v.category === 'colors' || v.category === 'spacing' || v.category === 'shadows');
    const customVars = themeVariables.filter(v => v.category === 'custom');

    let css = ':root {\n';
    lightVars.forEach(variable => {
      css += `  --${variable.name}: ${variable.value};\n`;
    });
    css += '}\n\n';

    css += '.dark {\n';
    lightVars.forEach(variable => {
      // For dark mode, we'd need to define dark variants
      css += `  --${variable.name}: ${variable.value}; /* TODO: Define dark variant */\n`;
    });
    css += '}\n';

    if (customVars.length > 0) {
      css += '\n/* Custom Variables */\n:root {\n';
      customVars.forEach(variable => {
        css += `  --${variable.name}: ${variable.value};\n`;
      });
      css += '}\n';
    }

    setExportedCSS(css);
  };

  // Copy CSS to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(exportedCSS);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  // Run theme validation
  const runValidation = () => {
    const report = themeValidator.validateTheme();
    setValidationReport(report);
  };

  // Auto-run validation when variables change
  useEffect(() => {
    if (themeVariables.length > 0) {
      const timeoutId = setTimeout(() => {
        runValidation();
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [themeVariables]);

  // Handle element inspection
  const handleElementSelected = (info: any) => {
    const report = styleAnalyzer.analyzeElement(info.element || document.createElement('div'));
    setInspectedElement(report);
    setActiveTab('inspect');
  };

  // Convert HSL to hex for color picker
  const hslToHex = (hsl: string): string => {
    try {
      const matches = hsl.match(/(\d+(?:\.\d+)?)\s+(\d+(?:\.\d+)?)%\s+(\d+(?:\.\d+)?)%/);
      if (!matches) return '#000000';

      const h = parseFloat(matches[1]) / 360;
      const s = parseFloat(matches[2]) / 100;
      const l = parseFloat(matches[3]) / 100;

      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      let r, g, b;
      if (s === 0) {
        r = g = b = l;
      } else {
        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
      }

      const toHex = (c: number) => {
        const hex = Math.round(c * 255).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      };

      return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    } catch {
      return '#000000';
    }
  };

  // Convert hex to HSL for updating variables
  const hexToHsl = (hex: string): string => {
    try {
      const r = parseInt(hex.slice(1, 3), 16) / 255;
      const g = parseInt(hex.slice(3, 5), 16) / 255;
      const b = parseInt(hex.slice(5, 7), 16) / 255;

      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h, s, l = (max + min) / 2;

      if (max === min) {
        h = s = 0;
      } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
          default: h = 0;
        }
        h /= 6;
      }

      return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
    } catch {
      return '0 0% 0%';
    }
  };

  // Get original value for a variable
  const getOriginalValue = (varName: string): string => {
    const original = originalVariables.find(v => v.name === varName);
    return original?.value || '';
  };

  // Reset individual variable
  const resetVariable = (varName: string) => {
    const originalValue = getOriginalValue(varName);
    if (originalValue) {
      updateVariable(varName, originalValue);
    }
  };

  // Render color input with preview and picker
  const renderColorInput = (variable: ThemeVariable) => {
    const isHSL = variable.value.includes(' ');
    const displayValue = isHSL ? `hsl(${variable.value})` : variable.value;
    const hexValue = isHSL ? hslToHex(variable.value) : variable.value;
    const originalValue = getOriginalValue(variable.name);
    const isModified = originalValue && variable.value !== originalValue;

    return (
      <div key={variable.name} className="space-y-3 p-3 border rounded-lg bg-card/50">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium flex items-center gap-2">
            {variable.name}
            {isModified && <div className="w-2 h-2 bg-primary rounded-full" title="Modified" />}
          </Label>
          <div className="flex items-center gap-2">
            {isModified && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => resetVariable(variable.name)}
                title="Reset to original"
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
            )}
            <div
              className="w-8 h-8 rounded border border-border cursor-pointer"
              style={{ backgroundColor: displayValue }}
              title="Current color"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <input
            type="color"
            value={hexValue}
            onChange={(e) => {
              const hslValue = hexToHsl(e.target.value);
              updateVariable(variable.name, hslValue);
            }}
            className="w-12 h-8 rounded border border-border cursor-pointer"
            title="Pick color"
          />
          <Input
            value={variable.value}
            onChange={(e) => updateVariable(variable.name, e.target.value)}
            className="font-mono text-xs flex-1"
            placeholder="HSL or hex value"
          />
        </div>

        <p className="text-xs text-muted-foreground">{variable.description}</p>
      </div>
    );
  };

  // Render spacing input with quick presets
  const renderSpacingInput = (variable: ThemeVariable) => {
    const isPixelValue = variable.value.includes('px');
    const numericValue = isPixelValue ? parseInt(variable.value) : 0;
    const originalValue = getOriginalValue(variable.name);
    const isModified = originalValue && variable.value !== originalValue;

    const presets = variable.name.includes('gap')
      ? ['4px', '8px', '12px', '16px', '24px']
      : variable.name.includes('padding')
      ? ['8px', '12px', '16px', '20px', '24px']
      : variable.name.includes('radius')
      ? ['0px', '4px', '8px', '12px', '16px']
      : ['0px', '2px', '4px', '8px', '16px'];

    return (
      <div key={variable.name} className="space-y-3 p-3 border rounded-lg bg-card/50">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium flex items-center gap-2">
            {variable.name}
            {isModified && <div className="w-2 h-2 bg-primary rounded-full" title="Modified" />}
          </Label>
          {isModified && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => resetVariable(variable.name)}
              title="Reset to original"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          )}
        </div>

        <div className="flex gap-1 flex-wrap">
          {presets.map(preset => (
            <Button
              key={preset}
              variant={variable.value === preset ? "default" : "outline"}
              size="sm"
              className="text-xs h-6 px-2"
              onClick={() => updateVariable(variable.name, preset)}
            >
              {preset}
            </Button>
          ))}
        </div>

        <Input
          value={variable.value}
          onChange={(e) => updateVariable(variable.name, e.target.value)}
          className="font-mono text-xs"
          placeholder="Custom value (e.g., 16px, 1rem)"
        />

        <p className="text-xs text-muted-foreground">{variable.description}</p>
      </div>
    );
  };

  if (showTestingGrid) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-auto">
        <div className="sticky top-0 bg-background border-b p-4 flex justify-between items-center">
          <h1 className="text-xl font-bold">Component Testing Grid</h1>
          <Button onClick={() => setShowTestingGrid(false)} variant="outline">
            Close Testing Grid
          </Button>
        </div>
        <ComponentTestingGrid />
      </div>
    );
  }

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 rounded-full w-12 h-12"
        size="icon"
        variant="outline"
      >
        <Settings className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <div
      ref={panelRef}
      className={cn(
        "fixed right-4 bottom-4 z-50 bg-background border border-border rounded-lg shadow-lg transition-all duration-300",
        isMinimized ? "w-80 h-12" : "w-[420px] h-[700px]"
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          <span className="font-medium text-sm">Theme Developer</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setIsMinimized(!isMinimized)}
            title={isMinimized ? "Expand panel" : "Minimize panel"}
          >
            {isMinimized ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setIsVisible(false)}
            title="Close panel (Ctrl+Shift+T to reopen)"
          >
            ×
          </Button>
        </div>
      </div>

      {!isMinimized && (
        <div className="flex flex-col h-[calc(100%-49px)]">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-5 mx-3 mt-3">
              <TabsTrigger value="theme" className="text-xs">Theme</TabsTrigger>
              <TabsTrigger value="detect" className="text-xs">Detect</TabsTrigger>
              <TabsTrigger value="inspect" className="text-xs">Inspect</TabsTrigger>
              <TabsTrigger value="scan" className="text-xs">Scan</TabsTrigger>
              <TabsTrigger value="compare" className="text-xs">Compare</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden">
              <TabsContent value="theme" className="h-full m-0">
                <Tabs defaultValue="colors" className="h-full">
                  <TabsList className="grid w-full grid-cols-5 mx-3 mt-3">
                    <TabsTrigger value="colors" className="text-xs">Colors</TabsTrigger>
                    <TabsTrigger value="spacing" className="text-xs">Spacing</TabsTrigger>
                    <TabsTrigger value="custom" className="text-xs">Custom</TabsTrigger>
                    <TabsTrigger value="validation" className="text-xs">Validate</TabsTrigger>
                    <TabsTrigger value="export" className="text-xs">Export</TabsTrigger>
                  </TabsList>

                  <TabsContent value="colors" className="h-full m-0">
                    <ScrollArea className="h-full px-3 pb-6">
                      <div className="space-y-5 pt-3">
                        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                          🎨 Use color pickers or enter HSL values (e.g., "222.2 84% 4.9%")
                        </div>
                        {themeVariables
                          .filter(v => v.category === 'colors')
                          .map(renderColorInput)}
                      </div>
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent value="spacing" className="h-full m-0">
                    <ScrollArea className="h-full px-3 pb-6">
                      <div className="space-y-5 pt-3">
                        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                          📏 Click presets or enter custom values (px, rem, em)
                        </div>
                        {themeVariables
                          .filter(v => v.category === 'spacing' || v.category === 'shadows')
                          .map(renderSpacingInput)}
                      </div>
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent value="custom" className="h-full m-0">
                <ScrollArea className="h-full px-3 pb-6">
                  <div className="space-y-6 pt-3">
                    <div className="space-y-4">
                      <h3 className="font-medium text-sm flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        Panel Variables
                      </h3>
                      <div className="space-y-4">
                        {themeVariables
                          .filter(v => v.name.startsWith('panel-') && v.category === 'custom')
                          .map(renderColorInput)}
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <h3 className="font-medium text-sm flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Analytics Variables
                      </h3>
                      <div className="space-y-4">
                        {themeVariables
                          .filter(v => v.name.startsWith('analytics-') && v.category === 'custom')
                          .map(renderColorInput)}
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <h3 className="font-medium text-sm flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        Tailwind Config
                      </h3>
                      <p className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                        💡 These values are defined in tailwind.config.js and require a rebuild to take effect
                      </p>
                      <div className="space-y-4">
                        {tailwindVariables.map((variable, index) => (
                          <div key={index} className="space-y-3 p-3 border rounded-lg bg-muted/20">
                            <div className="flex items-center justify-between">
                              <Label className="text-sm font-medium">{variable.name}</Label>
                              <Badge variant="outline" className="text-xs">Config</Badge>
                            </div>
                            <Input
                              value={variable.value}
                              onChange={(e) => {
                                setTailwindVariables(prev =>
                                  prev.map(v => v.name === variable.name ? { ...v, value: e.target.value } : v)
                                );
                              }}
                              className="font-mono text-xs"
                              placeholder="Enter shadow value"
                            />
                            <p className="text-xs text-muted-foreground">{variable.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

                  <TabsContent value="validation" className="h-full m-0">
                    <ScrollArea className="h-full px-3 pb-6">
                      <div className="space-y-4 pt-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-sm">Theme Validation</h3>
                          <Button onClick={validateTheme} size="sm" variant="outline">
                            <Shield className="h-3 w-3 mr-1" />
                            Validate
                          </Button>
                        </div>

                        {validationReport && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">{validationReport.passed}</div>
                                <div className="text-muted-foreground">Passed</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-yellow-600">{validationReport.warnings}</div>
                                <div className="text-muted-foreground">Warnings</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">{validationReport.errors}</div>
                                <div className="text-muted-foreground">Errors</div>
                              </div>
                            </div>

                            <ScrollArea className="h-48">
                              <div className="space-y-2">
                                {validationReport.issues.map((issue, index) => (
                                  <Alert key={index} variant={issue.severity === 'error' ? 'destructive' : 'default'}>
                                    <AlertTriangle className="h-4 w-4" />
                                    <AlertDescription className="text-xs">
                                      <strong>{issue.variable}:</strong> {issue.message}
                                      {issue.suggestion && (
                                        <div className="mt-1 text-muted-foreground">
                                          💡 {issue.suggestion}
                                        </div>
                                      )}
                                    </AlertDescription>
                                  </Alert>
                                ))}
                              </div>
                            </ScrollArea>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent value="export" className="h-full m-0">
                    <ScrollArea className="h-full px-3 pb-6">
                      <div className="space-y-4 pt-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-sm">Export Theme</h3>
                          <div className="flex gap-2">
                            <Button onClick={resetTheme} size="sm" variant="outline">
                              <RotateCcw className="h-3 w-3 mr-1" />
                              Reset
                            </Button>
                            <Button onClick={exportTheme} size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              Export
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <Label className="text-sm font-medium">CSS Variables</Label>
                            <Textarea
                              value={generateCSSVariables()}
                              readOnly
                              className="font-mono text-xs h-32 mt-2"
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              className="mt-2"
                              onClick={() => copyToClipboard(generateCSSVariables())}
                            >
                              {copiedCSS ? <Check className="h-3 w-3 mr-1" /> : <Copy className="h-3 w-3 mr-1" />}
                              {copiedCSS ? 'Copied!' : 'Copy CSS'}
                            </Button>
                          </div>

                          <div>
                            <Label className="text-sm font-medium">Tailwind Config</Label>
                            <Textarea
                              value={generateTailwindConfig()}
                              readOnly
                              className="font-mono text-xs h-32 mt-2"
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              className="mt-2"
                              onClick={() => copyToClipboard(generateTailwindConfig())}
                            >
                              {copiedTailwind ? <Check className="h-3 w-3 mr-1" /> : <Copy className="h-3 w-3 mr-1" />}
                              {copiedTailwind ? 'Copied!' : 'Copy Config'}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </ScrollArea>
                  </TabsContent>
                </Tabs>
              </TabsContent>

              <TabsContent value="detect" className="h-full m-0">
                <ScrollArea className="h-full px-3 pb-6">
                  <div className="pt-3">
                    <StyleDetector />
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="inspect" className="h-full m-0">
                <ScrollArea className="h-full px-3 pb-6">
                  <div className="space-y-4 pt-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-sm">Element Inspector</h3>
                      <InspectMode
                        isActive={inspectMode}
                        onToggle={() => setInspectMode(!inspectMode)}
                        onElementSelected={handleElementSelected}
                      />
                    </div>

                    {inspectMode && (
                      <Alert>
                        <Target className="h-4 w-4" />
                        <AlertDescription className="text-xs">
                          Click on any element to inspect its styling and get theme suggestions
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Code Generator Integration */}
                    <CodeGenerator inspectedElement={inspectedElement} />

                    {inspectedElement && (
                      <div className="space-y-4">
                        {/* Component Info */}
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm flex items-center gap-2">
                              <Code className="h-4 w-4" />
                              Component Info
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{inspectedElement.element.tagName.toLowerCase()}</Badge>
                              {inspectedElement.componentName && (
                                <Badge variant="secondary">{inspectedElement.componentName}</Badge>
                              )}
                              <Badge variant="outline" className="text-xs">
                                {inspectedElement.themeCompliance}% compliant
                              </Badge>
                            </div>

                            {inspectedElement.filePath && (
                              <p className="text-xs font-mono text-muted-foreground">
                                {inspectedElement.filePath}
                              </p>
                            )}
                          </CardContent>
                        </Card>

                        {/* Style Issues */}
                        {inspectedElement.issues.length > 0 && (
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 text-orange-500" />
                                Style Issues ({inspectedElement.issues.length})
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {inspectedElement.issues.map((issue, index) => (
                                  <div key={index} className="p-3 border rounded-lg">
                                    <div className="flex items-center justify-between mb-2">
                                      <Badge
                                        variant={issue.severity === 'high' ? 'destructive' :
                                                issue.severity === 'medium' ? 'default' : 'secondary'}
                                        className="text-xs"
                                      >
                                        {issue.severity} - {issue.type}
                                      </Badge>
                                    </div>

                                    <p className="text-xs mb-2">{issue.description}</p>

                                    <div className="text-xs font-mono bg-muted p-2 rounded mb-2">
                                      {issue.property}: {issue.value}
                                    </div>

                                    <Alert className="mt-2">
                                      <Lightbulb className="h-3 w-3" />
                                      <AlertDescription className="text-xs">
                                        {issue.suggestion}
                                      </AlertDescription>
                                    </Alert>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* Suggestions */}
                        {inspectedElement.suggestions.length > 0 && (
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm flex items-center gap-2">
                                <Zap className="h-4 w-4 text-blue-500" />
                                Theme Suggestions
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {inspectedElement.suggestions.map((suggestion, index) => (
                                  <div key={index} className="p-3 border rounded-lg">
                                    <div className="flex items-center justify-between mb-2">
                                      <Badge variant="outline" className="text-xs">
                                        {suggestion.impact} impact
                                      </Badge>
                                      {suggestion.autoFixable && (
                                        <Badge variant="secondary" className="text-xs">
                                          Auto-fixable
                                        </Badge>
                                      )}
                                    </div>

                                    <div className="space-y-2 text-xs">
                                      <div>
                                        <span className="text-muted-foreground">Current:</span>
                                        <div className="font-mono bg-red-50 dark:bg-red-950 p-1 rounded mt-1">
                                          {suggestion.current}
                                        </div>
                                      </div>

                                      <div>
                                        <span className="text-muted-foreground">Suggested:</span>
                                        <div className="font-mono bg-green-50 dark:bg-green-950 p-1 rounded mt-1">
                                          {suggestion.suggested}
                                        </div>
                                      </div>

                                      <p className="text-muted-foreground">{suggestion.reason}</p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* No Issues */}
                        {inspectedElement.issues.length === 0 && (
                          <Alert>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <AlertDescription>
                              Great! This element follows theme best practices.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    )}

                    {!inspectedElement && !inspectMode && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">Click "Inspect Mode" to analyze elements</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="scan" className="h-full m-0">
                <ScrollArea className="h-full px-3 pb-6">
                  <div className="pt-3">
                    <AppScanner />
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="compare" className="h-full m-0">
                <ScrollArea className="h-full px-3 pb-6">
                  <div className="pt-3">
                    <VisualComparison />
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="validation" className="h-full m-0">
                <ScrollArea className="h-full px-3 pb-6">
                  <div className="space-y-4 pt-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Theme Validation</h3>
                      <Button onClick={runValidation} size="sm">
                        <Shield className="h-3 w-3 mr-1" />
                        Validate
                      </Button>
                    </div>

                    {validationReport && (
                      <div className="space-y-4">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Overall Score:</span>
                          <Badge variant={validationReport.overallScore >= 80 ? 'default' : 'destructive'}>
                            {validationReport.overallScore}%
                          </Badge>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Accessibility Issues:</Label>
                          {validationReport.accessibilityIssues.length === 0 ? (
                            <p className="text-xs text-green-600">✓ No accessibility issues found</p>
                          ) : (
                            <div className="space-y-1">
                              {validationReport.accessibilityIssues.map((issue, index) => (
                                <Alert key={index} variant="destructive" className="py-2">
                                  <AlertTriangle className="h-3 w-3" />
                                  <AlertDescription className="text-xs">{issue}</AlertDescription>
                                </Alert>
                              ))}
                            </div>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Contrast Ratios:</Label>
                          <div className="space-y-2">
                            {validationReport.colorValidations.map((validation, index) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span>{validation.variable}</span>
                                <div className="flex items-center gap-2">
                                  <span>{validation.contrast.ratio.toFixed(2)}:1</span>
                                  <Badge
                                    variant={validation.contrast.passes ? 'default' : 'destructive'}
                                    className="text-xs"
                                  >
                                    {validation.contrast.level}
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {validationReport.recommendations.length > 0 && (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">Recommendations:</Label>
                            <div className="space-y-1">
                              {validationReport.recommendations.map((rec, index) => (
                                <p key={index} className="text-xs text-muted-foreground">• {rec}</p>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="export" className="h-full m-0">
                <div className="p-3 h-full flex flex-col">
                  <div className="flex items-center gap-2 mb-3">
                    <Button onClick={generateCSSExport} size="sm">
                      <Download className="h-3 w-3 mr-1" />
                      Generate CSS
                    </Button>
                    <Button onClick={resetToOriginal} variant="outline" size="sm">
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Reset
                    </Button>
                  </div>
                  
                  {exportedCSS && (
                    <div className="flex-1 flex flex-col">
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-sm">Generated CSS:</Label>
                        <Button
                          onClick={copyToClipboard}
                          variant="ghost"
                          size="sm"
                          className="h-6"
                        >
                          {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                        </Button>
                      </div>
                      <ScrollArea className="flex-1 border rounded p-2">
                        <pre className="text-xs font-mono whitespace-pre-wrap">
                          {exportedCSS}
                        </pre>
                      </ScrollArea>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="test" className="h-full m-0">
                <div className="p-3 h-full flex flex-col">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <TestTube className="h-4 w-4" />
                      <span className="text-sm font-medium">Component Testing</span>
                    </div>

                    <Button
                      onClick={() => setShowTestingGrid(true)}
                      className="w-full"
                      variant="outline"
                    >
                      <Zap className="h-3 w-3 mr-2" />
                      Open Component Testing Grid
                    </Button>

                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>• Test all UI components with current theme</p>
                      <p>• Verify colors and spacing consistency</p>
                      <p>• Check component behavior across themes</p>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Quick Actions:</Label>
                      <div className="grid grid-cols-2 gap-2">
                        <Button size="sm" variant="outline" onClick={runValidation}>
                          <Shield className="h-3 w-3 mr-1" />
                          Validate
                        </Button>
                        <Button size="sm" variant="outline" onClick={generateCSSExport}>
                          <Download className="h-3 w-3 mr-1" />
                          Export
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default ThemeDeveloperPanel;
