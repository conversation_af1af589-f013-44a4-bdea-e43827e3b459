# Theme Developer System

A comprehensive theme development and testing system for real-time theme editing, component testing, and style validation.

## Overview

The Theme Developer System provides:
- **Real-time theme variable editing** with live preview
- **Component testing grid** to test all UI components
- **Accessibility validation** with WCAG compliance checking
- **Style issue detection** for hardcoded styles vs theme variables
- **CSS export functionality** for production deployment
- **Theme consistency validation**

## Quick Start

### 1. Enable the Theme Developer Panel

Add the provider to your app:

```tsx
// In your main App.tsx or main.tsx
import { ThemeDeveloperProvider } from '@/providers/ThemeDeveloperProvider';

function App() {
  return (
    <ThemeDeveloperProvider enableInProduction={false}>
      {/* Your app content */}
    </ThemeDeveloperProvider>
  );
}
```

### 2. Activate Developer Mode

**Method 1: Keyboard Shortcut**
- Press `Ctrl+Shift+T` (or `Cmd+Shift+T` on Mac)

**Method 2: Programmatically**
```tsx
import { useEnableThemeDeveloper } from '@/providers/ThemeDeveloperProvider';

function MyComponent() {
  const enableDeveloper = useEnableThemeDeveloper();
  
  return (
    <button onClick={enableDeveloper}>
      Enable Theme Developer
    </button>
  );
}
```

**Method 3: URL Parameter**
Add `?theme-dev=true` to your URL

## Features

### 🎨 Real-Time Theme Editing

**Colors Tab:**
- Edit all CSS color variables in real-time
- Color preview swatches
- HSL color format support
- Instant visual feedback

**Spacing Tab:**
- Modify spacing variables (gaps, padding, margins)
- Shadow and border radius controls
- Consistent spacing system

### 🧪 Component Testing Grid

**Full Component Coverage:**
- All Shadcn UI components
- Form elements (inputs, buttons, selects)
- Display components (cards, badges, avatars)
- Layout components (tabs, alerts, progress)

**Testing Features:**
- Interactive components with state
- Different variants and sizes
- Disabled states
- Theme consistency verification

### 🛡️ Accessibility Validation

**WCAG Compliance:**
- Contrast ratio checking (AA/AAA standards)
- Color accessibility validation
- Automatic recommendations

**Validation Metrics:**
- Overall accessibility score
- Individual contrast ratios
- Severity-based issue reporting

### 📤 Export & Integration

**CSS Export:**
- Generate production-ready CSS
- Light and dark mode variables
- Custom variable definitions
- Copy to clipboard functionality

**Theme Backup:**
- Save current theme state
- Restore original values
- Version comparison

## Usage Guide

### Basic Theme Editing

1. **Open the panel** using `Ctrl+Shift+T`
2. **Navigate to Colors tab**
3. **Edit color values** in real-time
4. **See changes immediately** across all components

### Component Testing

1. **Go to Test tab**
2. **Click "Open Component Testing Grid"**
3. **Review all components** with your theme
4. **Check for consistency issues**

### Accessibility Validation

1. **Navigate to Validate tab**
2. **Click "Validate" button**
3. **Review accessibility score**
4. **Fix contrast issues** based on recommendations

### Export Your Theme

1. **Go to Export tab**
2. **Click "Generate CSS"**
3. **Copy the generated CSS**
4. **Update your `index.css` file**

## Advanced Features

### Keyboard Shortcuts

- `Ctrl+Shift+T` - Toggle developer panel
- `Ctrl+Shift+R` - Reset to original theme
- `Ctrl+Shift+E` - Export current theme

### URL Parameters

- `?theme-dev=true` - Enable developer mode
- `?theme-test=true` - Open testing grid directly

### Local Storage

The system automatically saves:
- Developer mode state
- Current theme modifications
- Panel position and size

## Integration with Your Codebase

### Theme Variable Structure

Your theme should follow this structure in `index.css`:

```css
:root {
  /* Core colors */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 202.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  
  /* Spacing */
  --panel-gap: 10px;
  --panel-padding: 16px;
  --section-gap: 24px;
}

.dark {
  /* Dark mode variants */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
}
```

### Component Best Practices

**Use theme variables:**
```tsx
// ✅ Good - uses theme variables
<div className="bg-background text-foreground border-border">

// ❌ Bad - hardcoded colors
<div className="bg-white text-black border-gray-200">
```

**Use spacing variables:**
```tsx
// ✅ Good - uses theme spacing
<div className="gap-panel-gap p-panel-padding">

// ❌ Bad - hardcoded spacing
<div className="gap-4 p-6">
```

## Troubleshooting

### Panel Not Appearing
- Check if `enableInProduction` is set correctly
- Verify keyboard shortcut (`Ctrl+Shift+T`)
- Check browser console for errors

### Colors Not Updating
- Ensure CSS variables are properly defined
- Check for CSS specificity issues
- Verify HSL format is correct

### Export Issues
- Make sure all variables are valid
- Check for missing color pairs
- Verify CSS syntax

## Development Notes

### File Structure
```
src/components/dev-tools/
├── ThemeDeveloperPanel.tsx      # Main panel component
├── ComponentTestingGrid.tsx     # Testing grid
├── ComponentStyleDetector.ts    # Style analysis
├── ThemeValidator.ts           # Accessibility validation
└── THEME_DEVELOPER_SYSTEM.md  # This documentation
```

### Dependencies
- React 18+
- Tailwind CSS
- Shadcn UI components
- Lucide React icons

### Performance
- Debounced validation (500ms)
- Lazy loading of testing grid
- Efficient CSS variable updates

## Contributing

To extend the Theme Developer System:

1. **Add new validation rules** in `ThemeValidator.ts`
2. **Extend component testing** in `ComponentTestingGrid.tsx`
3. **Add new export formats** in the main panel
4. **Improve accessibility checks** with additional WCAG criteria

## License

Part of the DJ Mix Constructor application theme system.
