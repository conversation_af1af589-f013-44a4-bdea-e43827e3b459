/**
 * Theme Validator and Accessibility Checker
 * Validates theme colors for accessibility and consistency
 */

export interface ContrastResult {
  ratio: number;
  level: 'AAA' | 'AA' | 'A' | 'FAIL';
  passes: boolean;
}

export interface ColorValidation {
  variable: string;
  foreground: string;
  background: string;
  contrast: ContrastResult;
  issues: string[];
}

export interface ThemeValidationReport {
  overallScore: number;
  colorValidations: ColorValidation[];
  consistencyIssues: string[];
  accessibilityIssues: string[];
  recommendations: string[];
}

export class ThemeValidator {
  /**
   * Convert HSL string to RGB values
   */
  private hslToRgb(hsl: string): { r: number; g: number; b: number } {
    // Parse HSL string like "222.2 84% 4.9%"
    const matches = hsl.match(/(\d+(?:\.\d+)?)\s+(\d+(?:\.\d+)?)%\s+(\d+(?:\.\d+)?)%/);
    if (!matches) {
      // Fallback for other formats
      return { r: 0, g: 0, b: 0 };
    }

    const h = parseFloat(matches[1]) / 360;
    const s = parseFloat(matches[2]) / 100;
    const l = parseFloat(matches[3]) / 100;

    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  }

  /**
   * Calculate relative luminance of a color
   */
  private getLuminance(rgb: { r: number; g: number; b: number }): number {
    const { r, g, b } = rgb;
    
    const rsRGB = r / 255;
    const gsRGB = g / 255;
    const bsRGB = b / 255;

    const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
    const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
    const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  private getContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.hslToRgb(color1);
    const rgb2 = this.hslToRgb(color2);
    
    const lum1 = this.getLuminance(rgb1);
    const lum2 = this.getLuminance(rgb2);
    
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  }

  /**
   * Evaluate contrast ratio against WCAG standards
   */
  private evaluateContrast(ratio: number): ContrastResult {
    if (ratio >= 7) {
      return { ratio, level: 'AAA', passes: true };
    } else if (ratio >= 4.5) {
      return { ratio, level: 'AA', passes: true };
    } else if (ratio >= 3) {
      return { ratio, level: 'A', passes: false };
    } else {
      return { ratio, level: 'FAIL', passes: false };
    }
  }

  /**
   * Get current theme variables from document
   */
  private getCurrentThemeVariables(): Record<string, string> {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    const variables: Record<string, string> = {};

    const themeVars = [
      'background', 'foreground', 'card', 'card-foreground', 'popover', 'popover-foreground',
      'primary', 'primary-foreground', 'secondary', 'secondary-foreground', 'muted', 
      'muted-foreground', 'accent', 'accent-foreground', 'destructive', 'destructive-foreground',
      'border', 'input', 'ring'
    ];

    themeVars.forEach(varName => {
      const value = computedStyle.getPropertyValue(`--${varName}`).trim();
      if (value) {
        variables[varName] = value;
      }
    });

    return variables;
  }

  /**
   * Validate theme colors for accessibility
   */
  validateTheme(): ThemeValidationReport {
    const variables = this.getCurrentThemeVariables();
    const colorValidations: ColorValidation[] = [];
    const consistencyIssues: string[] = [];
    const accessibilityIssues: string[] = [];
    const recommendations: string[] = [];

    // Define color pairs to check
    const colorPairs = [
      { bg: 'background', fg: 'foreground', name: 'Main background/text' },
      { bg: 'card', fg: 'card-foreground', name: 'Card background/text' },
      { bg: 'primary', fg: 'primary-foreground', name: 'Primary button' },
      { bg: 'secondary', fg: 'secondary-foreground', name: 'Secondary button' },
      { bg: 'muted', fg: 'muted-foreground', name: 'Muted elements' },
      { bg: 'accent', fg: 'accent-foreground', name: 'Accent elements' },
      { bg: 'destructive', fg: 'destructive-foreground', name: 'Destructive elements' }
    ];

    // Check contrast ratios
    colorPairs.forEach(pair => {
      const bgColor = variables[pair.bg];
      const fgColor = variables[pair.fg];

      if (bgColor && fgColor) {
        const ratio = this.getContrastRatio(bgColor, fgColor);
        const contrast = this.evaluateContrast(ratio);
        const issues: string[] = [];

        if (!contrast.passes) {
          issues.push(`Contrast ratio ${ratio.toFixed(2)}:1 fails WCAG AA standards`);
          accessibilityIssues.push(`${pair.name} has insufficient contrast (${ratio.toFixed(2)}:1)`);
        }

        if (ratio < 3) {
          issues.push('Extremely low contrast - may be unreadable');
          recommendations.push(`Increase contrast for ${pair.name} to at least 4.5:1`);
        }

        colorValidations.push({
          variable: pair.name,
          foreground: fgColor,
          background: bgColor,
          contrast,
          issues
        });
      } else {
        consistencyIssues.push(`Missing color pair: ${pair.bg}/${pair.fg}`);
      }
    });

    // Check for consistency issues
    if (variables.background === variables.card) {
      consistencyIssues.push('Background and card colors are identical - may reduce visual hierarchy');
    }

    if (variables.primary === variables.accent) {
      consistencyIssues.push('Primary and accent colors are identical - may reduce visual distinction');
    }

    // Check border visibility
    if (variables.border && variables.background) {
      const borderRatio = this.getContrastRatio(variables.border, variables.background);
      if (borderRatio < 1.5) {
        accessibilityIssues.push('Border color has very low contrast with background');
        recommendations.push('Increase border contrast for better element separation');
      }
    }

    // Generate recommendations
    if (accessibilityIssues.length === 0) {
      recommendations.push('Great! All color combinations meet accessibility standards');
    } else {
      recommendations.push('Consider using a color contrast checker to improve accessibility');
    }

    if (consistencyIssues.length > 0) {
      recommendations.push('Review color consistency to ensure proper visual hierarchy');
    }

    // Calculate overall score
    const passedContrasts = colorValidations.filter(v => v.contrast.passes).length;
    const totalContrasts = colorValidations.length;
    const contrastScore = totalContrasts > 0 ? (passedContrasts / totalContrasts) * 100 : 100;
    const consistencyScore = Math.max(0, 100 - (consistencyIssues.length * 10));
    const overallScore = Math.round((contrastScore + consistencyScore) / 2);

    return {
      overallScore,
      colorValidations,
      consistencyIssues,
      accessibilityIssues,
      recommendations
    };
  }

  /**
   * Generate color palette suggestions
   */
  generateColorSuggestions(baseColor: string): Record<string, string> {
    // This is a simplified version - in a real implementation,
    // you'd use color theory to generate harmonious palettes
    const suggestions: Record<string, string> = {};
    
    // Parse base color and generate variations
    const matches = baseColor.match(/(\d+(?:\.\d+)?)\s+(\d+(?:\.\d+)?)%\s+(\d+(?:\.\d+)?)%/);
    if (matches) {
      const h = parseFloat(matches[1]);
      const s = parseFloat(matches[2]);
      const l = parseFloat(matches[3]);

      suggestions.primary = `${h} ${s}% ${l}%`;
      suggestions.secondary = `${h} ${Math.max(10, s - 20)}% ${Math.min(95, l + 30)}%`;
      suggestions.accent = `${(h + 30) % 360} ${s}% ${l}%`;
      suggestions.muted = `${h} ${Math.max(5, s - 40)}% ${Math.min(95, l + 40)}%`;
    }

    return suggestions;
  }

  /**
   * Export theme validation report as JSON
   */
  exportValidationReport(report: ThemeValidationReport): string {
    return JSON.stringify(report, null, 2);
  }
}

export const themeValidator = new ThemeValidator();
