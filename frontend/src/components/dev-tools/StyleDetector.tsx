import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
// import { Switch } from '@/components/ui/switch'; // Create if doesn't exist
// For now, using a simple checkbox as Switch
const Switch: React.FC<{checked: boolean, onCheckedChange: (checked: boolean) => void, id: string}> = ({checked, onCheckedChange, id}) => (
  <input
    type="checkbox"
    id={id}
    checked={checked}
    onChange={(e) => onCheckedChange(e.target.checked)}
    className="w-4 h-4"
  />
);
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Eye, 
  EyeOff, 
  AlertTriangle, 
  Z<PERSON>, 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Target,
  X
} from 'lucide-react';

interface StyleViolation {
  element: HTMLElement;
  issues: Array<{
    type: 'hardcoded-color' | 'hardcoded-spacing' | 'inline-style' | 'non-theme-class';
    severity: 'high' | 'medium' | 'low';
    property: string;
    value: string;
    suggestion: string;
  }>;
  rect: DOMRect;
  id: string;
}

interface DetectorSettings {
  showHardcodedColors: boolean;
  showInlineStyles: boolean;
  showHardcodedSpacing: boolean;
  showNonThemeClasses: boolean;
  highlightSeverity: 'all' | 'high' | 'medium';
}

const StyleDetector: React.FC = () => {
  const [isActive, setIsActive] = useState(false);
  const [violations, setViolations] = useState<StyleViolation[]>([]);
  const [settings, setSettings] = useState<DetectorSettings>({
    showHardcodedColors: true,
    showInlineStyles: true,
    showHardcodedSpacing: true,
    showNonThemeClasses: true,
    highlightSeverity: 'all'
  });
  const [selectedViolation, setSelectedViolation] = useState<StyleViolation | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Theme-compliant classes and values
  const themeColors = new Set([
    'bg-background', 'bg-foreground', 'bg-card', 'bg-card-foreground',
    'bg-popover', 'bg-popover-foreground', 'bg-primary', 'bg-primary-foreground',
    'bg-secondary', 'bg-secondary-foreground', 'bg-muted', 'bg-muted-foreground',
    'bg-accent', 'bg-accent-foreground', 'bg-destructive', 'bg-destructive-foreground',
    'bg-border', 'bg-input', 'bg-ring',
    'text-background', 'text-foreground', 'text-card', 'text-card-foreground',
    'text-popover', 'text-popover-foreground', 'text-primary', 'text-primary-foreground',
    'text-secondary', 'text-secondary-foreground', 'text-muted', 'text-muted-foreground',
    'text-accent', 'text-accent-foreground', 'text-destructive', 'text-destructive-foreground'
  ]);

  const themeSpacing = new Set([
    'p-panel-padding', 'm-section-gap', 'gap-control-gap', 'gap-panel-gap'
  ]);

  // Scan for style violations
  const scanForViolations = () => {
    const allElements = document.querySelectorAll('*');
    const newViolations: StyleViolation[] = [];

    allElements.forEach((element, index) => {
      if (element instanceof HTMLElement && !isThemeDevElement(element)) {
        const issues = analyzeElement(element);
        if (issues.length > 0) {
          const rect = element.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) { // Only visible elements
            newViolations.push({
              element,
              issues,
              rect,
              id: `violation-${index}`
            });
          }
        }
      }
    });

    setViolations(newViolations);
  };

  // Check if element is part of theme developer tools
  const isThemeDevElement = (element: HTMLElement): boolean => {
    return element.closest('[data-theme-developer]') !== null ||
           element.closest('.theme-developer-panel') !== null ||
           element.id === 'theme-developer-overlay';
  };

  // Analyze individual element
  const analyzeElement = (element: HTMLElement) => {
    const issues: StyleViolation['issues'] = [];
    const computedStyle = getComputedStyle(element);
    const classList = Array.from(element.classList);

    // Check for hardcoded colors
    if (settings.showHardcodedColors) {
      const bgColor = computedStyle.backgroundColor;
      const textColor = computedStyle.color;
      const borderColor = computedStyle.borderColor;

      if (isHardcodedColor(bgColor) && !element.style.backgroundColor) {
        const hasThemeClass = classList.some(cls => cls.startsWith('bg-') && themeColors.has(cls));
        if (!hasThemeClass) {
          issues.push({
            type: 'hardcoded-color',
            severity: 'high',
            property: 'backgroundColor',
            value: bgColor,
            suggestion: 'Use bg-background, bg-card, or bg-primary'
          });
        }
      }

      if (isHardcodedColor(textColor) && !element.style.color) {
        const hasThemeClass = classList.some(cls => cls.startsWith('text-') && themeColors.has(cls));
        if (!hasThemeClass) {
          issues.push({
            type: 'hardcoded-color',
            severity: 'high',
            property: 'color',
            value: textColor,
            suggestion: 'Use text-foreground, text-muted-foreground, or text-primary'
          });
        }
      }
    }

    // Check for inline styles
    if (settings.showInlineStyles && element.style.length > 0) {
      Array.from(element.style).forEach(property => {
        const value = element.style.getPropertyValue(property);
        issues.push({
          type: 'inline-style',
          severity: 'high',
          property,
          value,
          suggestion: 'Move to CSS class or use theme variables'
        });
      });
    }

    // Check for hardcoded spacing
    if (settings.showHardcodedSpacing) {
      const hardcodedSpacingClasses = classList.filter(cls => 
        /^[pm]-\d+$/.test(cls) || /^gap-\d+$/.test(cls)
      );
      
      hardcodedSpacingClasses.forEach(cls => {
        if (!themeSpacing.has(cls)) {
          issues.push({
            type: 'hardcoded-spacing',
            severity: 'medium',
            property: 'spacing',
            value: cls,
            suggestion: 'Use theme spacing variables like p-panel-padding'
          });
        }
      });
    }

    // Check for non-theme classes
    if (settings.showNonThemeClasses) {
      const nonThemeColorClasses = classList.filter(cls => 
        (cls.startsWith('bg-') || cls.startsWith('text-')) && 
        !themeColors.has(cls) &&
        (cls.includes('gray-') || cls.includes('slate-') || cls.includes('zinc-'))
      );

      nonThemeColorClasses.forEach(cls => {
        issues.push({
          type: 'non-theme-class',
          severity: 'medium',
          property: 'class',
          value: cls,
          suggestion: 'Replace with theme color classes'
        });
      });
    }

    return issues.filter(issue => 
      settings.highlightSeverity === 'all' || 
      issue.severity === settings.highlightSeverity ||
      (settings.highlightSeverity === 'medium' && issue.severity === 'high')
    );
  };

  // Check if color is hardcoded
  const isHardcodedColor = (color: string): boolean => {
    return color && (
      color.includes('rgb') || 
      color.startsWith('#') || 
      color === 'white' || 
      color === 'black' ||
      color === 'transparent'
    ) && !color.includes('var(--');
  };

  // Get overlay color based on severity
  const getOverlayColor = (severity: string): string => {
    switch (severity) {
      case 'high': return 'rgba(239, 68, 68, 0.3)'; // Red
      case 'medium': return 'rgba(245, 158, 11, 0.3)'; // Orange  
      case 'low': return 'rgba(59, 130, 246, 0.3)'; // Blue
      default: return 'rgba(239, 68, 68, 0.3)';
    }
  };

  // Get border color based on severity
  const getBorderColor = (severity: string): string => {
    switch (severity) {
      case 'high': return '#ef4444'; // Red
      case 'medium': return '#f59e0b'; // Orange
      case 'low': return '#3b82f6'; // Blue
      default: return '#ef4444';
    }
  };

  // Handle violation click
  const handleViolationClick = (violation: StyleViolation) => {
    setSelectedViolation(violation);
    violation.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };

  // Update overlays when violations change
  useEffect(() => {
    if (isActive) {
      scanForViolations();
      const interval = setInterval(scanForViolations, 2000); // Rescan every 2 seconds
      return () => clearInterval(interval);
    } else {
      setViolations([]);
      setSelectedViolation(null);
    }
  }, [isActive, settings]);

  // Global keyboard shortcut (Ctrl+Shift+D for Detect)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setIsActive(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Create overlay elements
  const createOverlays = () => {
    if (!isActive) return null;

    return violations.map((violation) => {
      const highestSeverity = violation.issues.reduce((highest, issue) => {
        const severityOrder = { high: 3, medium: 2, low: 1 };
        return severityOrder[issue.severity] > severityOrder[highest] ? issue.severity : highest;
      }, 'low' as 'high' | 'medium' | 'low');

      return (
        <div
          key={violation.id}
          className="fixed pointer-events-auto z-[9998] cursor-pointer"
          style={{
            left: `${violation.rect.left + window.scrollX}px`,
            top: `${violation.rect.top + window.scrollY}px`,
            width: `${violation.rect.width}px`,
            height: `${violation.rect.height}px`,
            backgroundColor: getOverlayColor(highestSeverity),
            border: `2px solid ${getBorderColor(highestSeverity)}`,
            borderRadius: '4px',
            boxSizing: 'border-box'
          }}
          onClick={() => handleViolationClick(violation)}
          title={`${violation.issues.length} style issue(s) - Click for details`}
        >
          {/* Issue count badge */}
          <div
            className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold"
            style={{ fontSize: '10px' }}
          >
            {violation.issues.length}
          </div>
        </div>
      );
    });
  };

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">Style Detector</h3>
          <div className="flex items-center gap-2">
            <Switch
              checked={isActive}
              onCheckedChange={setIsActive}
              id="style-detector"
            />
            <Label htmlFor="style-detector" className="text-xs">
              {isActive ? 'Active' : 'Inactive'}
            </Label>
          </div>
        </div>

      {/* Settings */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Detection Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="hardcoded-colors"
                checked={settings.showHardcodedColors}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, showHardcodedColors: checked }))
                }
              />
              <Label htmlFor="hardcoded-colors" className="text-xs">Hardcoded Colors</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="inline-styles"
                checked={settings.showInlineStyles}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, showInlineStyles: checked }))
                }
              />
              <Label htmlFor="inline-styles" className="text-xs">Inline Styles</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="hardcoded-spacing"
                checked={settings.showHardcodedSpacing}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, showHardcodedSpacing: checked }))
                }
              />
              <Label htmlFor="hardcoded-spacing" className="text-xs">Hardcoded Spacing</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="non-theme-classes"
                checked={settings.showNonThemeClasses}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, showNonThemeClasses: checked }))
                }
              />
              <Label htmlFor="non-theme-classes" className="text-xs">Non-Theme Classes</Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-xs">Highlight Severity</Label>
            <div className="flex gap-2">
              {(['all', 'high', 'medium'] as const).map((severity) => (
                <Button
                  key={severity}
                  variant={settings.highlightSeverity === severity ? 'default' : 'outline'}
                  size="sm"
                  className="text-xs h-6 px-2"
                  onClick={() => setSettings(prev => ({ ...prev, highlightSeverity: severity }))}
                >
                  {severity.charAt(0).toUpperCase() + severity.slice(1)}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      {isActive && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Target className="h-4 w-4" />
              Detection Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-red-500">
                  {violations.filter(v => v.issues.some(i => i.severity === 'high')).length}
                </div>
                <div className="text-xs text-muted-foreground">High</div>
              </div>
              <div>
                <div className="text-lg font-bold text-orange-500">
                  {violations.filter(v => v.issues.some(i => i.severity === 'medium')).length}
                </div>
                <div className="text-xs text-muted-foreground">Medium</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-500">
                  {violations.filter(v => v.issues.some(i => i.severity === 'low')).length}
                </div>
                <div className="text-xs text-muted-foreground">Low</div>
              </div>
            </div>

            <Button
              onClick={scanForViolations}
              size="sm"
              variant="outline"
              className="w-full"
            >
              <Zap className="h-3 w-3 mr-1" />
              Rescan Now
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Selected Violation Details */}
      {selectedViolation && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-950">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center justify-between">
              <span className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                Element Issues
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setSelectedViolation(null)}
              >
                <X className="h-3 w-3" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {selectedViolation.issues.map((issue, index) => (
              <div key={index} className="p-2 bg-background rounded border">
                <div className="flex items-center justify-between mb-1">
                  <Badge 
                    variant={issue.severity === 'high' ? 'destructive' : 'default'}
                    className="text-xs"
                  >
                    {issue.type}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {issue.severity}
                  </Badge>
                </div>
                <div className="text-xs space-y-1">
                  <div><strong>Property:</strong> {issue.property}</div>
                  <div><strong>Value:</strong> {issue.value}</div>
                  <div className="text-green-600"><strong>Fix:</strong> {issue.suggestion}</div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      {!isActive && (
        <Alert>
          <Eye className="h-4 w-4" />
          <AlertDescription className="text-xs">
            Toggle the Style Detector to highlight elements with non-compliant styling. 
            Red overlays indicate high-priority issues, orange for medium, and blue for low priority.
          </AlertDescription>
        </Alert>
      )}

        {/* Render overlays */}
        {createOverlays()}
      </div>

      {/* Floating Quick Toggle */}
      {isActive && (
        <div className="fixed bottom-20 right-4 z-[9999]">
          <Button
            onClick={() => setIsActive(false)}
            size="sm"
            variant="destructive"
            className="shadow-lg"
          >
            <EyeOff className="h-3 w-3 mr-1" />
            Hide Detector
          </Button>
        </div>
      )}
    </>
  );
};

export default StyleDetector;
