# Inspect Mode - Advanced Element Analysis

The **Inspect Mode** is a powerful tool designed specifically for AI-coded apps with inconsistent styling. It helps you identify styling issues, detect hardcoded values, and get intelligent suggestions for theme compliance.

## 🎯 **Perfect for AI-Coded Apps**

This tool is specifically designed to solve common issues in AI-generated code:
- **Hardcoded colors** instead of theme variables
- **Inline styles** scattered throughout components
- **Inconsistent spacing** patterns
- **Mixed styling approaches** (Tailwind + CSS + inline)
- **Accessibility issues** from poor color choices

## 🔍 **How to Use Inspect Mode**

### **1. Activate Inspect Mode**
1. Open Theme Developer Panel (`Ctrl+Shift+T`)
2. Go to **Inspect** tab
3. Click **"Inspect Mode"** button
4. Your cursor changes to crosshair

### **2. Inspect Elements**
1. **Hover** over any element → See blue highlight
2. **Click** on element → Get detailed analysis
3. **Review** styling issues and suggestions
4. **Apply** recommended fixes

### **3. Exit Inspect Mode**
- Click **"Exit Inspect"** button
- Or press `Escape` key

## 🧠 **Smart Analysis Features**

### **Component Detection**
- **React Component Name**: Detects actual component names
- **File Path**: Attempts to identify source files
- **Element Hierarchy**: Shows DOM structure

### **Style Source Detection**
- **Inline Styles**: `style={{...}}` (❌ High Priority)
- **Tailwind Classes**: `className="bg-red-500"` (⚠️ Medium Priority)
- **CSS Variables**: `var(--primary)` (✅ Good)
- **Computed Styles**: Final rendered values

### **Issue Classification**
- **🔴 High Severity**: Inline styles, hardcoded colors, accessibility issues
- **🟡 Medium Severity**: Non-theme variables, inconsistent patterns
- **🟢 Low Severity**: Minor optimizations

## 📊 **Analysis Reports**

### **Component Info**
```
<Button> (React Component)
src/components/ui/Button.tsx
Theme Compliance: 85%
```

### **Style Issues**
```
❌ Hardcoded Color
Property: backgroundColor
Value: #ffffff
Suggestion: Use bg-background

⚠️ Inline Style  
Property: padding
Value: 16px
Suggestion: Use p-panel-padding
```

### **Theme Suggestions**
```
Current:  style={{backgroundColor: '#ffffff'}}
Suggested: className="bg-background"
Reason: Use theme colors for dark mode support
Impact: High | Auto-fixable: Yes
```

## 🎨 **Common Issues & Fixes**

### **Hardcoded Colors**
**❌ Bad:**
```tsx
<div style={{backgroundColor: '#ffffff', color: '#000000'}}>
<div className="bg-white text-black">
```

**✅ Good:**
```tsx
<div className="bg-background text-foreground">
```

### **Hardcoded Spacing**
**❌ Bad:**
```tsx
<div style={{padding: '16px', margin: '24px'}}>
<div className="p-4 m-6">
```

**✅ Good:**
```tsx
<div className="p-panel-padding mb-section-gap">
```

### **Mixed Patterns**
**❌ Bad:**
```tsx
<div 
  className="bg-gray-100" 
  style={{padding: '12px', borderRadius: '8px'}}
>
```

**✅ Good:**
```tsx
<div className="bg-muted p-panel-padding rounded-lg">
```

## 🚀 **Advanced Features**

### **Accessibility Checking**
- **Contrast Ratios**: WCAG AA/AAA compliance
- **Color Blindness**: Problematic color combinations
- **Focus Indicators**: Missing or insufficient focus styles

### **Consistency Detection**
- **Mixed Units**: px vs rem vs em
- **Pattern Violations**: Different approaches in same component
- **Theme Compliance**: Percentage of theme-compliant styles

### **Smart Suggestions**
- **Context-Aware**: Suggestions based on element type
- **Auto-Fixable**: Indicates if fix can be automated
- **Impact Assessment**: High/Medium/Low priority fixes

## 🛠️ **Integration Workflow**

### **For Your Figma Implementation:**

1. **Start with Layout Elements**
   - Inspect main containers
   - Fix background colors first
   - Establish spacing patterns

2. **Move to Interactive Elements**
   - Buttons, inputs, forms
   - Focus on hover/active states
   - Ensure accessibility compliance

3. **Fine-tune Details**
   - Icons, badges, small elements
   - Consistent border radius
   - Proper text colors

### **Systematic Approach:**
```
1. Inspect → 2. Analyze → 3. Fix → 4. Validate → 5. Export
```

## 📋 **Best Practices**

### **Priority Order:**
1. **Fix High Severity Issues** (inline styles, accessibility)
2. **Replace Hardcoded Colors** (theme compliance)
3. **Standardize Spacing** (consistency)
4. **Optimize Performance** (remove unused styles)

### **Theme Migration Strategy:**
1. **Identify Patterns**: Common color/spacing values
2. **Create Variables**: Add to theme if missing
3. **Replace Systematically**: One component type at a time
4. **Test Thoroughly**: Light/dark mode compatibility

## 🔧 **Technical Details**

### **Detection Methods:**
- **React Fiber**: Component name detection
- **Computed Styles**: Final rendered values
- **Class Analysis**: Tailwind pattern recognition
- **CSS Variable Tracking**: Theme usage detection

### **Limitations:**
- **Build-time Info**: File paths are estimated
- **Dynamic Styles**: Runtime-generated styles may not be caught
- **CSS-in-JS**: Some styled-components may not be fully analyzed

## 💡 **Pro Tips**

### **Efficient Workflow:**
1. **Use with Component Testing Grid**: Test fixes immediately
2. **Combine with Validation**: Check accessibility after fixes
3. **Export Regularly**: Save progress as you go

### **Common Patterns to Look For:**
- **White/Black**: Usually should be theme colors
- **Gray Shades**: Often can use muted variants
- **Fixed Pixels**: Consider responsive alternatives
- **Repeated Values**: Candidates for theme variables

## 🎯 **Perfect for Your Use Case**

This tool is **specifically designed** for apps like yours that were built by AI agents and need systematic theming. It will help you:

- **Identify inconsistencies** across your codebase
- **Prioritize fixes** by impact and severity  
- **Maintain consistency** during theme migration
- **Ensure accessibility** compliance
- **Speed up development** with smart suggestions

The Inspect Mode turns the complex task of theme migration into a systematic, guided process! 🚀
