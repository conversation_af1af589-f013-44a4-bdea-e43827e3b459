import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Scan, 
  AlertTriangle, 
  CheckCircle, 
  Copy, 
  Download,
  FileText,
  Palette,
  Zap,
  Target
} from 'lucide-react';

interface StyleIssue {
  element: HTMLElement;
  selector: string;
  issues: Array<{
    type: 'hardcoded-color' | 'hardcoded-spacing' | 'inline-style' | 'inconsistent';
    property: string;
    value: string;
    suggestion: string;
    fix: string;
  }>;
  component?: string;
  severity: 'high' | 'medium' | 'low';
}

interface ScanResults {
  totalElements: number;
  issuesFound: number;
  highPriority: number;
  mediumPriority: number;
  lowPriority: number;
  issues: StyleIssue[];
  commonPatterns: Array<{
    pattern: string;
    count: number;
    suggestion: string;
  }>;
}

const AppScanner: React.FC = () => {
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResults | null>(null);
  const [scanProgress, setScanProgress] = useState(0);

  // Scan entire app for styling issues
  const scanApp = async () => {
    setIsScanning(true);
    setScanProgress(0);
    
    const allElements = document.querySelectorAll('*');
    const issues: StyleIssue[] = [];
    const patterns = new Map<string, number>();
    
    let processed = 0;
    
    for (const element of Array.from(allElements)) {
      if (element instanceof HTMLElement) {
        const elementIssues = analyzeElement(element);
        if (elementIssues.issues.length > 0) {
          issues.push(elementIssues);
        }
        
        // Track patterns
        const classList = Array.from(element.classList);
        classList.forEach(cls => {
          if (cls.includes('bg-') || cls.includes('text-') || cls.includes('p-') || cls.includes('m-')) {
            patterns.set(cls, (patterns.get(cls) || 0) + 1);
          }
        });
      }
      
      processed++;
      if (processed % 100 === 0) {
        setScanProgress((processed / allElements.length) * 100);
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }
    
    // Find common patterns that should be theme variables
    const commonPatterns = Array.from(patterns.entries())
      .filter(([pattern, count]) => count > 3)
      .map(([pattern, count]) => ({
        pattern,
        count,
        suggestion: getSuggestionForPattern(pattern)
      }))
      .sort((a, b) => b.count - a.count);
    
    const results: ScanResults = {
      totalElements: allElements.length,
      issuesFound: issues.length,
      highPriority: issues.filter(i => i.severity === 'high').length,
      mediumPriority: issues.filter(i => i.severity === 'medium').length,
      lowPriority: issues.filter(i => i.severity === 'low').length,
      issues: issues.sort((a, b) => {
        const severityOrder = { high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      }),
      commonPatterns
    };
    
    setScanResults(results);
    setIsScanning(false);
    setScanProgress(100);
  };

  // Analyze individual element
  const analyzeElement = (element: HTMLElement): StyleIssue => {
    const computedStyle = getComputedStyle(element);
    const issues: StyleIssue['issues'] = [];
    
    // Check for hardcoded colors
    const bgColor = computedStyle.backgroundColor;
    if (bgColor && (bgColor.includes('rgb') || bgColor === 'white' || bgColor === 'black')) {
      if (!element.style.backgroundColor) { // Not inline
        issues.push({
          type: 'hardcoded-color',
          property: 'backgroundColor',
          value: bgColor,
          suggestion: 'Use bg-background, bg-card, or bg-primary',
          fix: getColorFix('backgroundColor', bgColor)
        });
      }
    }
    
    const textColor = computedStyle.color;
    if (textColor && (textColor.includes('rgb') || textColor === 'white' || textColor === 'black')) {
      if (!element.style.color) {
        issues.push({
          type: 'hardcoded-color',
          property: 'color',
          value: textColor,
          suggestion: 'Use text-foreground, text-muted-foreground, or text-primary',
          fix: getColorFix('color', textColor)
        });
      }
    }
    
    // Check for inline styles
    if (element.style.length > 0) {
      Array.from(element.style).forEach(property => {
        const value = element.style.getPropertyValue(property);
        issues.push({
          type: 'inline-style',
          property,
          value,
          suggestion: 'Move to CSS class',
          fix: `className="${getClassFix(property, value)}"`
        });
      });
    }
    
    // Check for hardcoded spacing
    const classList = Array.from(element.classList);
    classList.forEach(cls => {
      if (/^[pm]-\d+$/.test(cls) || /^gap-\d+$/.test(cls)) {
        issues.push({
          type: 'hardcoded-spacing',
          property: 'spacing',
          value: cls,
          suggestion: 'Use theme spacing variables',
          fix: getSpacingFix(cls)
        });
      }
    });
    
    const severity = issues.some(i => i.type === 'inline-style') ? 'high' :
                    issues.some(i => i.type === 'hardcoded-color') ? 'medium' : 'low';
    
    return {
      element,
      selector: getElementSelector(element),
      issues,
      component: detectComponentName(element),
      severity
    };
  };

  // Get suggestion for common pattern
  const getSuggestionForPattern = (pattern: string): string => {
    if (pattern.startsWith('bg-gray-')) return 'Consider bg-muted or bg-card';
    if (pattern.startsWith('text-gray-')) return 'Consider text-muted-foreground';
    if (pattern.startsWith('p-') || pattern.startsWith('m-')) return 'Consider theme spacing variables';
    return 'Consider using theme variables';
  };

  // Generate color fix
  const getColorFix = (property: string, value: string): string => {
    const colorMap: Record<string, string> = {
      'rgb(255, 255, 255)': property === 'backgroundColor' ? 'bg-background' : 'text-background',
      'rgb(0, 0, 0)': property === 'backgroundColor' ? 'bg-foreground' : 'text-foreground',
      'white': property === 'backgroundColor' ? 'bg-background' : 'text-background',
      'black': property === 'backgroundColor' ? 'bg-foreground' : 'text-foreground'
    };
    return colorMap[value] || (property === 'backgroundColor' ? 'bg-muted' : 'text-muted-foreground');
  };

  // Generate class fix
  const getClassFix = (property: string, value: string): string => {
    if (property === 'backgroundColor') return getColorFix(property, value);
    if (property === 'color') return getColorFix(property, value);
    if (property === 'padding') return 'p-panel-padding';
    if (property === 'margin') return 'm-section-gap';
    return 'theme-class';
  };

  // Generate spacing fix
  const getSpacingFix = (cls: string): string => {
    if (cls.startsWith('p-')) return 'p-panel-padding';
    if (cls.startsWith('m-')) return 'm-section-gap';
    if (cls.startsWith('gap-')) return 'gap-control-gap';
    return cls;
  };

  // Get element selector
  const getElementSelector = (element: HTMLElement): string => {
    if (element.id) return `#${element.id}`;
    const classes = Array.from(element.classList).slice(0, 2).join('.');
    if (classes) return `.${classes}`;
    return element.tagName.toLowerCase();
  };

  // Detect component name
  const detectComponentName = (element: HTMLElement): string | undefined => {
    const reactFiberKey = Object.keys(element).find(key => 
      key.startsWith('__reactFiber')
    );
    
    if (reactFiberKey) {
      const fiber = (element as any)[reactFiberKey];
      if (fiber?.type?.name) return fiber.type.name;
    }
    
    return undefined;
  };

  // Generate fix report
  const generateFixReport = (): string => {
    if (!scanResults) return '';
    
    let report = `# Theme Fix Report\n\n`;
    report += `## Summary\n`;
    report += `- Total Elements Scanned: ${scanResults.totalElements}\n`;
    report += `- Issues Found: ${scanResults.issuesFound}\n`;
    report += `- High Priority: ${scanResults.highPriority}\n`;
    report += `- Medium Priority: ${scanResults.mediumPriority}\n`;
    report += `- Low Priority: ${scanResults.lowPriority}\n\n`;
    
    report += `## High Priority Issues\n\n`;
    scanResults.issues
      .filter(issue => issue.severity === 'high')
      .slice(0, 10)
      .forEach((issue, index) => {
        report += `### ${index + 1}. ${issue.selector}\n`;
        if (issue.component) report += `Component: ${issue.component}\n`;
        issue.issues.forEach(i => {
          report += `- **${i.type}**: ${i.property} = ${i.value}\n`;
          report += `  - Fix: ${i.fix}\n`;
        });
        report += `\n`;
      });
    
    report += `## Common Patterns to Replace\n\n`;
    scanResults.commonPatterns.slice(0, 10).forEach(pattern => {
      report += `- **${pattern.pattern}** (used ${pattern.count} times)\n`;
      report += `  - Suggestion: ${pattern.suggestion}\n`;
    });
    
    return report;
  };

  // Copy fix report
  const copyReport = async () => {
    const report = generateFixReport();
    await navigator.clipboard.writeText(report);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">App-Wide Style Scanner</h3>
        <Button 
          onClick={scanApp} 
          disabled={isScanning}
          size="sm"
          className="gap-2"
        >
          <Scan className="h-3 w-3" />
          {isScanning ? 'Scanning...' : 'Scan App'}
        </Button>
      </div>

      {isScanning && (
        <div className="space-y-2">
          <Progress value={scanProgress} />
          <p className="text-xs text-muted-foreground text-center">
            Scanning {Math.round(scanProgress)}% complete...
          </p>
        </div>
      )}

      {scanResults && (
        <div className="space-y-4">
          {/* Summary */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Scan Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Elements:</span>
                  <span className="ml-2 font-medium">{scanResults.totalElements}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Issues:</span>
                  <span className="ml-2 font-medium">{scanResults.issuesFound}</span>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Badge variant="destructive" className="text-xs">
                  {scanResults.highPriority} High
                </Badge>
                <Badge variant="default" className="text-xs">
                  {scanResults.mediumPriority} Medium
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {scanResults.lowPriority} Low
                </Badge>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={copyReport}>
                  <Copy className="h-3 w-3 mr-1" />
                  Copy Report
                </Button>
                <Button size="sm" variant="outline">
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Top Issues */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                Top Issues ({Math.min(5, scanResults.issues.length)})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-48">
                <div className="space-y-3">
                  {scanResults.issues.slice(0, 5).map((issue, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <code className="text-xs bg-muted px-2 py-1 rounded">
                          {issue.selector}
                        </code>
                        <Badge 
                          variant={issue.severity === 'high' ? 'destructive' : 
                                  issue.severity === 'medium' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {issue.severity}
                        </Badge>
                      </div>
                      
                      {issue.component && (
                        <p className="text-xs text-muted-foreground mb-2">
                          Component: {issue.component}
                        </p>
                      )}
                      
                      <div className="space-y-2">
                        {issue.issues.slice(0, 2).map((i, idx) => (
                          <div key={idx} className="text-xs">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge variant="outline" className="text-xs">
                                {i.type}
                              </Badge>
                              <span className="font-mono">{i.property}</span>
                            </div>
                            <div className="bg-red-50 dark:bg-red-950 p-2 rounded mb-1">
                              Current: {i.value}
                            </div>
                            <div className="bg-green-50 dark:bg-green-950 p-2 rounded">
                              Fix: {i.fix}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Common Patterns */}
          {scanResults.commonPatterns.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Palette className="h-4 w-4 text-blue-500" />
                  Common Patterns to Replace
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {scanResults.commonPatterns.slice(0, 5).map((pattern, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <code className="text-xs font-mono">{pattern.pattern}</code>
                        <span className="ml-2 text-xs text-muted-foreground">
                          ({pattern.count} uses)
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {pattern.suggestion}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {!scanResults && !isScanning && (
        <Alert>
          <Scan className="h-4 w-4" />
          <AlertDescription>
            Click "Scan App" to analyze your entire application for styling inconsistencies and get actionable fixes.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default AppScanner;
