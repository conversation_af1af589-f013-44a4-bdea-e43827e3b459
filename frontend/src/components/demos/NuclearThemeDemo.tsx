import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import ThemeDeveloperPanel from '@/components/dev-tools/ThemeDeveloperPanel';
import { 
  Palette, 
  Settings, 
  Search, 
  Bell, 
  User, 
  Heart,
  Star,
  Download,
  Upload,
  Play,
  Pause,
  SkipForward,
  Volume2,
  Shuffle,
  Repeat,
  Target,
  Scan,
  Eye,
  Code
} from 'lucide-react';

/**
 * Nuclear Brilliant Theme Developer Demo
 * Showcases all advanced features for AI-coded app theming
 */
const NuclearThemeDemo: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(75);

  return (
    <div className="min-h-screen bg-background">
      {/* Theme Developer Panel - Always Available */}
      <ThemeDeveloperPanel />

      {/* Demo App Content */}
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="flex items-center justify-between p-4 bg-card border rounded-lg">
          <div className="flex items-center gap-4">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Palette className="h-4 w-4 text-primary-foreground" />
            </div>
            <h1 className="text-xl font-bold">Nuclear Theme Developer Demo</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" style={{ backgroundColor: '#f0f0f0' }}>
              <Search className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="bg-gray-200">
              <Bell className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <User className="h-4 w-4" />
            </Button>
          </div>
        </header>

        {/* Nuclear Features Showcase */}
        <Alert className="border-primary/20 bg-primary/5">
          <Target className="h-4 w-4 text-primary" />
          <AlertDescription>
            <strong>🚀 Nuclear Brilliant Theme System Active!</strong>
            <br />
            <div className="mt-2 space-y-1 text-xs">
              <div>Press <kbd className="px-2 py-1 bg-muted rounded font-mono">Ctrl+Shift+T</kbd> to open Theme Developer Panel</div>
              <div>Press <kbd className="px-2 py-1 bg-muted rounded font-mono">Ctrl+Shift+D</kbd> to toggle Style Detector</div>
            </div>
            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-xs">
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <Eye className="h-3 w-3 text-red-500" />
                <span><strong>Style Detector:</strong> Visual overlays on non-compliant elements</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <Target className="h-3 w-3 text-blue-500" />
                <span><strong>Inspect Mode:</strong> Click any element to analyze styling issues</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <Scan className="h-3 w-3 text-green-500" />
                <span><strong>App Scanner:</strong> Find inconsistencies across your entire app</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <Eye className="h-3 w-3 text-purple-500" />
                <span><strong>Visual Compare:</strong> Upload Figma designs for pixel-perfect matching</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <Code className="h-3 w-3 text-orange-500" />
                <span><strong>Code Generator:</strong> Get copy-paste fixes for all issues</span>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Track Library */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Track Library
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input placeholder="Search tracks..." />
                
                <div className="space-y-2">
                  {[
                    { name: "Summer Vibes", artist: "DJ Cool", duration: "3:45", bpm: 128 },
                    { name: "Night Drive", artist: "Beat Master", duration: "4:12", bpm: 124 },
                    { name: "Electric Dreams", artist: "Synth Wave", duration: "3:58", bpm: 132 }
                  ].map((track, index) => (
                    <div key={index} className="p-3 border rounded-lg hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm">{track.name}</p>
                          <p className="text-xs text-muted-foreground">{track.artist}</p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className="text-xs">{track.bpm} BPM</Badge>
                          <p className="text-xs text-muted-foreground mt-1">{track.duration}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Effects Panel with Intentional Issues */}
            <Card style={{ backgroundColor: '#f8f9fa', padding: '16px' }}>
              <CardHeader>
                <CardTitle className="text-sm" style={{ color: '#333333' }}>
                  Effects Panel (Hardcoded Styles)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    style={{ backgroundColor: 'white', color: 'black', padding: '8px' }}
                  >
                    Reverb
                  </Button>
                  <Button variant="outline" size="sm" className="bg-gray-200">
                    Delay
                  </Button>
                  <Button variant="outline" size="sm">Filter</Button>
                  <Button variant="outline" size="sm">Distortion</Button>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <Label className="text-xs" style={{ color: '#666' }}>Master Volume</Label>
                  <div className="flex items-center gap-2">
                    <Volume2 className="h-3 w-3" />
                    <div className="flex-1 h-2 bg-muted rounded-full">
                      <div 
                        className="h-full bg-primary rounded-full transition-all"
                        style={{ width: `${volume}%` }}
                      />
                    </div>
                    <span className="text-xs text-muted-foreground">{volume}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Center Column - Timeline */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Mix Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Timeline Visualization */}
                <div className="space-y-3">
                  <div className="h-16 bg-muted/30 rounded border-2 border-dashed border-muted-foreground/20 flex items-center justify-center">
                    <p className="text-xs text-muted-foreground">Track 1 - Drop audio here</p>
                  </div>
                  
                  <div className="h-16 bg-primary/10 rounded border border-primary/20 flex items-center px-3">
                    <div className="flex-1">
                      <p className="text-xs font-medium">Summer Vibes - DJ Cool</p>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex-1 h-1 bg-muted rounded-full">
                          <div className="w-1/3 h-full bg-primary rounded-full" />
                        </div>
                        <span className="text-xs text-muted-foreground">1:15 / 3:45</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="h-16 bg-muted/30 rounded border-2 border-dashed border-muted-foreground/20 flex items-center justify-center">
                    <p className="text-xs text-muted-foreground">Track 3 - Drop audio here</p>
                  </div>
                </div>

                {/* Transport Controls */}
                <div className="flex items-center justify-center gap-2 mt-4 p-3 bg-muted/30 rounded">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Shuffle className="h-3 w-3" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <SkipForward className="h-3 w-3 rotate-180" />
                  </Button>
                  <Button 
                    variant="default" 
                    size="icon" 
                    className="h-10 w-10"
                    onClick={() => setIsPlaying(!isPlaying)}
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <SkipForward className="h-3 w-3" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Repeat className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Mix Info with Mixed Styling Issues */}
            <Card className="bg-gray-100" style={{ margin: '12px', padding: '16px' }}>
              <CardHeader style={{ paddingBottom: '8px' }}>
                <CardTitle className="text-sm" style={{ color: '#000000' }}>
                  Mix Info (Mixed Styles)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label className="text-xs" style={{ color: '#666666' }}>Duration</Label>
                    <p className="font-medium">12:34</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Tracks</Label>
                    <p className="font-medium">3</p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    style={{ backgroundColor: '#ffffff', borderColor: '#cccccc' }}
                  >
                    <Download className="h-3 w-3 mr-1" />
                    Export
                  </Button>
                  <Button size="sm" variant="outline">
                    <Upload className="h-3 w-3 mr-1" />
                    Share
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Controls & Info */}
          <div className="space-y-4">
            {/* Beat Matching */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Beat Matching</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span>Track A</span>
                    <Badge variant="outline">128 BPM</Badge>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="w-full h-full bg-green-500 rounded-full animate-pulse" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span>Track B</span>
                    <Badge variant="outline">124 BPM</Badge>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="w-4/5 h-full bg-yellow-500 rounded-full" />
                  </div>
                </div>

                <Alert>
                  <AlertDescription className="text-xs">
                    Adjust Track B tempo to match Track A for seamless mixing
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Nuclear Features Guide */}
            <Card className="border-primary/20 bg-primary/5">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Palette className="h-4 w-4 text-primary" />
                  Nuclear Features Guide
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-xs space-y-2">
                  <div className="p-2 bg-background rounded border">
                    <strong>🔍 Step 1:</strong> Open Theme Panel (Ctrl+Shift+T)
                  </div>
                  <div className="p-2 bg-background rounded border">
                    <strong>🎯 Step 2:</strong> Use Inspect Mode to click elements
                  </div>
                  <div className="p-2 bg-background rounded border">
                    <strong>📊 Step 3:</strong> Run App Scanner for full analysis
                  </div>
                  <div className="p-2 bg-background rounded border">
                    <strong>🎨 Step 4:</strong> Upload Figma for visual comparison
                  </div>
                  <div className="p-2 bg-background rounded border">
                    <strong>⚡ Step 5:</strong> Generate and copy code fixes
                  </div>
                </div>
                
                <Alert>
                  <AlertDescription className="text-xs">
                    <strong>💡 Pro Tip:</strong> This demo includes intentional styling issues (hardcoded colors, inline styles, mixed patterns) to showcase the inspection capabilities!
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Favorites */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Favorites
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {[
                  "Deep House Mix #1",
                  "Techno Journey", 
                  "Chill Vibes Session"
                ].map((mix, index) => (
                  <div key={index} className="flex items-center justify-between p-2 hover:bg-muted/50 rounded">
                    <span className="text-sm">{mix}</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-muted-foreground">4.8</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <footer className="text-center py-4 text-xs text-muted-foreground border-t">
          <p>Nuclear Brilliant Theme Developer Demo</p>
          <p className="mt-1">Advanced theme development tools for AI-coded applications</p>
        </footer>
      </div>
    </div>
  );
};

export default NuclearThemeDemo;
