import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';

/**
 * HorizontalTimelineDemo - Simple demo of horizontal timeline layout
 * 
 * This is a simplified version to test the horizontal timeline concept
 * without complex dependencies.
 */
const HorizontalTimelineDemo: React.FC = () => {
  const [pixelsPerSecond, setPixelsPerSecond] = useState(50);
  const [currentTime, setCurrentTime] = useState(0);
  
  // Mock data
  const tracks = [
    { id: 1, title: "Track 1", artist: "Artist 1", duration: 180, startTime: 0, color: "#3b82f6" },
    { id: 2, title: "Track 2", artist: "Artist 2", duration: 200, startTime: 160, color: "#ef4444" },
    { id: 3, title: "Track 3", artist: "Artist 3", duration: 220, startTime: 340, color: "#10b981" },
  ];
  
  const totalDuration = Math.max(...tracks.map(t => t.startTime + t.duration));
  const timelineWidth = totalDuration * pixelsPerSecond;
  const TRACK_HEIGHT = 120;
  const TIME_RULER_WIDTH = 80;
  
  // Handle zoom
  const handleZoom = (delta: number) => {
    const newPixelsPerSecond = delta > 0 
      ? Math.min(pixelsPerSecond * 1.2, 200)
      : Math.max(pixelsPerSecond / 1.2, 10);
    setPixelsPerSecond(newPixelsPerSecond);
  };
  
  // Handle timeline click
  const handleTimelineClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickTime = clickX / pixelsPerSecond;
    setCurrentTime(Math.max(0, Math.min(clickTime, totalDuration)));
  };
  
  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="h-screen w-full bg-background p-4">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Horizontal Timeline Demo</h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => handleZoom(-1)}>
              Zoom Out
            </Button>
            <span className="text-sm">{Math.round(pixelsPerSecond)}px/s</span>
            <Button variant="outline" size="sm" onClick={() => handleZoom(1)}>
              Zoom In
            </Button>
          </div>
        </div>
        
        {/* Timeline Container */}
        <div className="flex-1 flex bg-muted rounded-lg overflow-hidden">
          {/* Vertical Time Ruler */}
          <div 
            className="flex-shrink-0 bg-background border-r border-border relative"
            style={{ width: TIME_RULER_WIDTH }}
          >
            <div className="h-full relative">
              {/* Time markers */}
              {Array.from({ length: Math.ceil(totalDuration / 30) }).map((_, i) => {
                const time = i * 30;
                const y = (time / totalDuration) * (tracks.length * TRACK_HEIGHT);
                return (
                  <div
                    key={i}
                    className="absolute left-0 right-0 flex items-center text-xs text-muted-foreground"
                    style={{ top: y }}
                  >
                    <div className="w-full border-t border-border" />
                    <div className="absolute left-2 bg-background px-1">
                      {formatTime(time)}
                    </div>
                  </div>
                );
              })}
              
              {/* Current time indicator */}
              <div
                className="absolute left-0 right-0 z-10"
                style={{ top: (currentTime / totalDuration) * (tracks.length * TRACK_HEIGHT) }}
              >
                <div className="w-full h-0.5 bg-primary" />
                <div className="absolute left-2 bg-primary text-primary-foreground px-1 rounded text-xs">
                  {formatTime(currentTime)}
                </div>
              </div>
            </div>
          </div>
          
          {/* Main Timeline Area */}
          <div className="flex-1 relative">
            <ScrollArea className="h-full w-full">
              <div 
                className="relative bg-background"
                style={{ 
                  width: Math.max(timelineWidth, 800),
                  height: tracks.length * TRACK_HEIGHT,
                }}
                onClick={handleTimelineClick}
              >
                {/* Background Grid */}
                <div className="absolute inset-0 pointer-events-none">
                  {/* Vertical grid lines every 10 seconds */}
                  {Array.from({ length: Math.ceil(totalDuration / 10) }).map((_, i) => (
                    <div
                      key={`grid-${i}`}
                      className="absolute top-0 bottom-0 border-l border-border/30"
                      style={{ left: i * 10 * pixelsPerSecond }}
                    />
                  ))}
                </div>

                {/* Track Lanes */}
                {tracks.map((track, index) => (
                  <div
                    key={track.id}
                    className="absolute left-0 border-b border-border/50 hover:bg-accent/20 transition-colors"
                    style={{
                      top: index * TRACK_HEIGHT,
                      height: TRACK_HEIGHT,
                      left: track.startTime * pixelsPerSecond,
                      width: track.duration * pixelsPerSecond,
                    }}
                  >
                    {/* Track Header */}
                    <div className="h-12 bg-background/90 border-b border-border/30 px-3 flex items-center justify-between">
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium truncate">{track.title}</div>
                        <div className="text-xs text-muted-foreground truncate">{track.artist}</div>
                      </div>
                    </div>

                    {/* Waveform Area */}
                    <div className="flex-1 relative" style={{ height: TRACK_HEIGHT - 48 }}>
                      {/* Mock waveform */}
                      <div 
                        className="absolute inset-2 rounded opacity-60"
                        style={{ backgroundColor: track.color }}
                      >
                        <div className="h-full flex items-center justify-center text-white text-xs">
                          Waveform ({Math.round(track.duration)}s)
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Playhead */}
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-primary z-20 pointer-events-none"
                  style={{ left: currentTime * pixelsPerSecond }}
                >
                  {/* Playhead handle */}
                  <div className="absolute -top-1 -left-2 w-4 h-4 bg-primary rounded-full border-2 border-background" />
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
        
        {/* Footer */}
        <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
          <div>Total Duration: {formatTime(totalDuration)}</div>
          <div>Current Time: {formatTime(currentTime)}</div>
          <div>{tracks.length} tracks</div>
        </div>
      </div>
    </div>
  );
};

export default HorizontalTimelineDemo;
