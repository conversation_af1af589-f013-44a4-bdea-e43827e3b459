import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Palette, 
  Settings, 
  TestTube, 
  Shield, 
  Download, 
  Lightbulb,
  Zap,
  Eye,
  Code,
  CheckCircle
} from 'lucide-react';
import { ThemeDeveloperProvider, useEnableThemeDeveloper } from '@/providers/ThemeDeveloperProvider';

const ThemeDeveloperDemoContent: React.FC = () => {
  const enableDeveloper = useEnableThemeDeveloper();
  const [step, setStep] = useState(1);

  const features = [
    {
      icon: <Palette className="h-5 w-5" />,
      title: "Real-Time Theme Editing",
      description: "Edit CSS variables with live preview across all components",
      color: "bg-blue-500"
    },
    {
      icon: <TestTube className="h-5 w-5" />,
      title: "Component Testing Grid",
      description: "Test all UI components with your theme changes",
      color: "bg-green-500"
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: "Accessibility Validation",
      description: "WCAG compliance checking with contrast ratios",
      color: "bg-purple-500"
    },
    {
      icon: <Download className="h-5 w-5" />,
      title: "CSS Export System",
      description: "Export production-ready CSS with your modifications",
      color: "bg-orange-500"
    }
  ];

  const steps = [
    {
      title: "Enable Developer Mode",
      description: "Press Ctrl+Shift+T or click the button below",
      action: "Enable Theme Developer",
      icon: <Settings className="h-4 w-4" />
    },
    {
      title: "Edit Theme Colors",
      description: "Use the Colors tab to modify theme variables in real-time",
      action: "Open Colors Tab",
      icon: <Palette className="h-4 w-4" />
    },
    {
      title: "Test Components",
      description: "Use the Test tab to open the component testing grid",
      action: "Open Testing Grid",
      icon: <TestTube className="h-4 w-4" />
    },
    {
      title: "Validate Accessibility",
      description: "Check WCAG compliance in the Validate tab",
      action: "Run Validation",
      icon: <Shield className="h-4 w-4" />
    },
    {
      title: "Export Your Theme",
      description: "Generate CSS code in the Export tab",
      action: "Export CSS",
      icon: <Download className="h-4 w-4" />
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-primary/10 rounded-full">
            <Palette className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl font-bold">Theme Developer System</h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          A comprehensive tool for real-time theme editing, component testing, and accessibility validation
        </p>
      </div>

      {/* Quick Start */}
      <Card className="border-2 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-primary" />
            Quick Start
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div>
              <h3 className="font-medium">Enable Developer Mode</h3>
              <p className="text-sm text-muted-foreground">
                Press <kbd className="px-2 py-1 bg-background border rounded text-xs">Ctrl+Shift+T</kbd> or click the button
              </p>
            </div>
            <Button onClick={enableDeveloper} className="gap-2">
              <Settings className="h-4 w-4" />
              Enable Theme Developer
            </Button>
          </div>
          
          <Alert>
            <Lightbulb className="h-4 w-4" />
            <AlertTitle>Pro Tip</AlertTitle>
            <AlertDescription>
              The developer panel will appear as a floating window. You can minimize it and access all features from there.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {features.map((feature, index) => (
          <Card key={index} className="relative overflow-hidden">
            <div className={`absolute top-0 left-0 w-1 h-full ${feature.color}`} />
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className={`p-2 ${feature.color} text-white rounded-md`}>
                  {feature.icon}
                </div>
                {feature.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Step-by-Step Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Step-by-Step Guide
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {steps.map((stepItem, index) => (
              <div key={index} className="flex items-start gap-4">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  step > index ? 'bg-green-500 text-white' : 
                  step === index + 1 ? 'bg-primary text-primary-foreground' : 
                  'bg-muted text-muted-foreground'
                }`}>
                  {step > index ? <CheckCircle className="h-4 w-4" /> : index + 1}
                </div>
                <div className="flex-1 space-y-1">
                  <h3 className="font-medium">{stepItem.title}</h3>
                  <p className="text-sm text-muted-foreground">{stepItem.description}</p>
                </div>
                {step === index + 1 && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      if (index === 0) enableDeveloper();
                      setStep(step + 1);
                    }}
                  >
                    {stepItem.icon}
                    {stepItem.action}
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Code Example */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Integration Example
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Add the Theme Developer Provider to your app:
            </p>
            <div className="bg-muted p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm">
{`// In your main App.tsx
import { ThemeDeveloperProvider } from '@/providers/ThemeDeveloperProvider';

function App() {
  return (
    <ThemeDeveloperProvider enableInProduction={false}>
      {/* Your app content */}
    </ThemeDeveloperProvider>
  );
}`}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Benefits */}
      <Card className="bg-gradient-to-r from-primary/5 to-accent/5">
        <CardHeader>
          <CardTitle>Why Use the Theme Developer System?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Badge variant="secondary">Efficiency</Badge>
              <p className="text-sm">Real-time editing eliminates the need for constant page refreshes</p>
            </div>
            <div className="space-y-2">
              <Badge variant="secondary">Accessibility</Badge>
              <p className="text-sm">Built-in WCAG compliance checking ensures your themes are accessible</p>
            </div>
            <div className="space-y-2">
              <Badge variant="secondary">Consistency</Badge>
              <p className="text-sm">Component testing grid helps maintain visual consistency across your app</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Footer */}
      <div className="text-center space-y-2">
        <p className="text-sm text-muted-foreground">
          Ready to start theming? Enable the developer panel and begin customizing your app's appearance.
        </p>
        <Button onClick={enableDeveloper} size="lg" className="gap-2">
          <Palette className="h-4 w-4" />
          Start Theme Development
        </Button>
      </div>
    </div>
  );
};

const ThemeDeveloperDemo: React.FC = () => {
  return (
    <ThemeDeveloperProvider enableInProduction={true}>
      <ThemeDeveloperDemoContent />
    </ThemeDeveloperProvider>
  );
};

export default ThemeDeveloperDemo;
