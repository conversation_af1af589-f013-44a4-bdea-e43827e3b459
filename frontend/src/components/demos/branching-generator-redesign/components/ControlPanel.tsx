import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { GitBranch, Zap, Music, Disc, Shuffle, RotateCcw, Save, Upload, Download } from 'lucide-react';
import { GenerateOptions } from '../types';

interface ControlPanelProps {
  isGenerating: boolean;
  generateOptions: GenerateOptions;
  onUpdateOptions: (options: Partial<GenerateOptions>) => void;
  onReset: () => void;
  onRandomize: () => void;
  onSave?: () => void;
  onLoad?: () => void;
  className?: string;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  isGenerating,
  generateOptions,
  onUpdateOptions,
  onReset,
  onRandomize,
  onSave,
  onLoad,
  className = ''
}) => {
  const {
    count = 3,
    energyRange = 0.2,
    bpmRange = 15,
    genreWeight = 1,
    keyWeight = 2,
    energyWeight = 1.5
  } = generateOptions;
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <GitBranch className="h-5 w-5" />
          Branch Generation Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Number of branches */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="branch-count">Branches per node</Label>
              <span className="text-sm text-muted-foreground">{count}</span>
            </div>
            <Slider
              id="branch-count"
              min={1}
              max={5}
              step={1}
              value={[count]}
              onValueChange={(value) => onUpdateOptions({ count: value[0] })}
              disabled={isGenerating}
            />
          </div>
          
          {/* Energy range */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="energy-range" className="flex items-center gap-1">
                <Zap className="h-4 w-4" />
                Energy Range
              </Label>
              <span className="text-sm text-muted-foreground">±{Math.round(energyRange * 10)}</span>
            </div>
            <Slider
              id="energy-range"
              min={0.1}
              max={0.5}
              step={0.05}
              value={[energyRange]}
              onValueChange={(value) => onUpdateOptions({ energyRange: value[0] })}
              disabled={isGenerating}
            />
          </div>
          
          {/* BPM range */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="bpm-range" className="flex items-center gap-1">
                <Disc className="h-4 w-4" />
                BPM Range
              </Label>
              <span className="text-sm text-muted-foreground">±{bpmRange} BPM</span>
            </div>
            <Slider
              id="bpm-range"
              min={5}
              max={30}
              step={5}
              value={[bpmRange]}
              onValueChange={(value) => onUpdateOptions({ bpmRange: value[0] })}
              disabled={isGenerating}
            />
          </div>
          
          {/* Weights section */}
          <div className="pt-2">
            <Label className="mb-2 block">Compatibility Weights</Label>
            <div className="grid grid-cols-3 gap-2">
              <div className="space-y-1">
                <Label htmlFor="key-weight" className="text-xs">Key</Label>
                <Select
                  value={keyWeight.toString()}
                  onValueChange={(value) => onUpdateOptions({ keyWeight: parseFloat(value) })}
                  disabled={isGenerating}
                >
                  <SelectTrigger id="key-weight" className="h-8">
                    <SelectValue placeholder="Key Weight" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0.5">Low</SelectItem>
                    <SelectItem value="1">Normal</SelectItem>
                    <SelectItem value="2">High</SelectItem>
                    <SelectItem value="3">Very High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="energy-weight" className="text-xs">Energy</Label>
                <Select
                  value={energyWeight.toString()}
                  onValueChange={(value) => onUpdateOptions({ energyWeight: parseFloat(value) })}
                  disabled={isGenerating}
                >
                  <SelectTrigger id="energy-weight" className="h-8">
                    <SelectValue placeholder="Energy Weight" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0.5">Low</SelectItem>
                    <SelectItem value="1">Normal</SelectItem>
                    <SelectItem value="1.5">High</SelectItem>
                    <SelectItem value="2.5">Very High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="genre-weight" className="text-xs">Genre</Label>
                <Select
                  value={genreWeight.toString()}
                  onValueChange={(value) => onUpdateOptions({ genreWeight: parseFloat(value) })}
                  disabled={isGenerating}
                >
                  <SelectTrigger id="genre-weight" className="h-8">
                    <SelectValue placeholder="Genre Weight" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0.5">Low</SelectItem>
                    <SelectItem value="1">Normal</SelectItem>
                    <SelectItem value="1.5">High</SelectItem>
                    <SelectItem value="2">Very High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex flex-wrap gap-2 pt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onRandomize}
              disabled={isGenerating}
              className="flex-1"
            >
              <Shuffle className="h-4 w-4 mr-1" />
              Randomize
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onReset}
              disabled={isGenerating}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            
            {onSave && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onSave}
                disabled={isGenerating}
                className="flex-1"
              >
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
            )}
            
            {onLoad && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onLoad}
                disabled={isGenerating}
                className="flex-1"
              >
                <Upload className="h-4 w-4 mr-1" />
                Load
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ControlPanel;
