import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { BranchNode, Path } from '../types';
import { Key, ArrowRight } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { calculateKeyCompatibility, convertToCamelot } from '../utils/mixAnalysis';

interface KeyCompatibilityVisualizationProps {
  path: Path;
  nodes: BranchNode[];
  className?: string;
}

const KeyCompatibilityVisualization: React.FC<KeyCompatibilityVisualizationProps> = ({
  path,
  nodes,
  className = ''
}) => {
  // Get the nodes in the path
  const pathNodes = path.nodes.map(id => nodes.find(n => n.id === id)).filter(Boolean) as BranchNode[];
  
  // Get a color based on the track's key
  const getKeyColor = (key: string) => {
    // Simple hash function to generate a color from a string
    const hash = Array.from(key).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue = hash % 360;
    return `hsl(${hue}, 70%, 60%)`;
  };
  
  // Get compatibility label based on score
  const getCompatibilityLabel = (score: number) => {
    if (score >= 0.9) return 'Perfect';
    if (score >= 0.8) return 'Excellent';
    if (score >= 0.7) return 'Good';
    if (score >= 0.5) return 'Fair';
    return 'Poor';
  };
  
  // Calculate key transitions
  const keyTransitions = pathNodes.slice(0, -1).map((node, index) => {
    const currentKey = node.key || '1A';
    const nextKey = pathNodes[index + 1].key || '1A';
    const compatibility = calculateKeyCompatibility(currentKey, nextKey);
    
    return {
      fromKey: currentKey,
      toKey: nextKey,
      compatibility,
      camelotFrom: convertToCamelot(currentKey) || currentKey,
      camelotTo: convertToCamelot(nextKey) || nextKey,
    };
  });
  
  // Calculate average key compatibility
  const averageCompatibility = keyTransitions.length > 0
    ? keyTransitions.reduce((sum, t) => sum + t.compatibility, 0) / keyTransitions.length
    : 1;
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Key className="h-5 w-5" />
          Key Compatibility
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Overall compatibility */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Overall Compatibility</div>
              <div className="text-sm">{Math.round(averageCompatibility * 100)}%</div>
            </div>
            <Progress value={averageCompatibility * 100} className="h-2" />
          </div>
          
          {/* Key progression visualization */}
          <div className="flex items-center justify-between gap-2 py-2">
            {pathNodes.map((node, index) => (
              <React.Fragment key={node.id}>
                <div 
                  className="h-10 w-10 rounded-full flex items-center justify-center text-white font-medium"
                  style={{ backgroundColor: getKeyColor(node.key || '1A') }}
                >
                  {node.key || '?'}
                </div>
                
                {index < pathNodes.length - 1 && (
                  <div className="flex-1 h-[2px] bg-muted relative">
                    <div 
                      className="absolute top-0 left-0 h-full"
                      style={{ 
                        width: `${keyTransitions[index].compatibility * 100}%`,
                        backgroundColor: getKeyColor(node.key || '1A')
                      }}
                    ></div>
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
          
          {/* Key transitions */}
          <div className="space-y-2">
            <div className="text-sm font-medium">Key Transitions</div>
            <div className="space-y-2">
              {keyTransitions.map((transition, index) => {
                const { fromKey, toKey, compatibility, camelotFrom, camelotTo } = transition;
                
                return (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center gap-2">
                      <div 
                        className="h-6 w-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                        style={{ backgroundColor: getKeyColor(fromKey) }}
                      >
                        {fromKey}
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      <div 
                        className="h-6 w-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                        style={{ backgroundColor: getKeyColor(toKey) }}
                      >
                        {toKey}
                      </div>
                      <div className="text-xs ml-2">
                        {camelotFrom} → {camelotTo}
                      </div>
                      <div className="ml-auto text-xs">
                        {getCompatibilityLabel(compatibility)}
                      </div>
                    </div>
                    <Progress 
                      value={compatibility * 100} 
                      className="h-1"
                      style={{ color: getKeyColor(toKey) }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default KeyCompatibilityVisualization;
