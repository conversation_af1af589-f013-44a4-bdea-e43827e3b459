import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Path, BranchNode } from '../types';
import { Check, Music, Clock, Zap, Key } from 'lucide-react';

interface MixSummaryProps {
  path: Path;
  nodes: BranchNode[];
  onCommit: () => void;
  className?: string;
}

const MixSummary: React.FC<MixSummaryProps> = ({ path, nodes, onCommit, className = '' }) => {
  // Get the nodes in the path
  const pathNodes = path.nodes.map(id => nodes.find(n => n.id === id)).filter(Boolean) as BranchNode[];
  
  // Calculate overall path score
  const overallScore = path.overallScore || (
    path.transitions.reduce((sum, t) => sum + t.score, 0) / 
    Math.max(1, path.transitions.length)
  );
  
  // Calculate total duration
  const totalDuration = pathNodes.reduce((sum, node) => {
    const trackDuration = node.track.duration || 0;
    return sum + trackDuration;
  }, 0);
  
  // Format duration as mm:ss
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Get average BPM
  const averageBpm = pathNodes.reduce((sum, node) => sum + (node.bpm || 120), 0) / pathNodes.length;
  
  // Get energy range
  const energyValues = pathNodes.map(node => node.energy || 0.5);
  const minEnergy = Math.min(...energyValues);
  const maxEnergy = Math.max(...energyValues);
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Music className="h-5 w-5" />
          Mix Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Basic stats */}
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-muted/20 p-2 rounded-md">
              <div className="text-xs text-muted-foreground">Tracks</div>
              <div className="text-lg font-medium">{pathNodes.length}</div>
            </div>
            
            <div className="bg-muted/20 p-2 rounded-md">
              <div className="text-xs text-muted-foreground">Duration</div>
              <div className="text-lg font-medium">{formatDuration(totalDuration)}</div>
            </div>
            
            <div className="bg-muted/20 p-2 rounded-md">
              <div className="text-xs text-muted-foreground">Avg. BPM</div>
              <div className="text-lg font-medium">{Math.round(averageBpm)}</div>
            </div>
            
            <div className="bg-muted/20 p-2 rounded-md">
              <div className="text-xs text-muted-foreground">Energy Range</div>
              <div className="text-lg font-medium">
                {Math.round(minEnergy * 10)}-{Math.round(maxEnergy * 10)}
              </div>
            </div>
          </div>
          
          {/* Track list */}
          <div className="space-y-1">
            <div className="text-sm font-medium">Tracks in Mix</div>
            <div className="max-h-[150px] overflow-y-auto pr-1 space-y-1">
              {pathNodes.map((node, index) => (
                <div key={node.id} className="flex items-center gap-2 text-xs p-1 rounded-md hover:bg-muted/20">
                  <div className="w-4 h-4 flex items-center justify-center bg-primary/10 rounded-full text-primary">
                    {index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{node.track.title}</div>
                    <div className="text-muted-foreground truncate">{node.track.artist}</div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="flex items-center gap-1 h-5">
                      <Key className="w-3 h-3" />
                      {node.key || '?'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Commit button */}
          <Button 
            className="w-full" 
            onClick={onCommit}
          >
            <Check className="h-4 w-4 mr-2" />
            Commit to Timeline
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default MixSummary;
