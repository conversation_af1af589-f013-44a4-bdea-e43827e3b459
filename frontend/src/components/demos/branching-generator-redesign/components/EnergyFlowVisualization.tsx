import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { BranchNode, Path } from '../types';
import { Zap, ArrowRight } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface EnergyFlowVisualizationProps {
  path: Path;
  nodes: BranchNode[];
  className?: string;
}

const EnergyFlowVisualization: React.FC<EnergyFlowVisualizationProps> = ({
  path,
  nodes,
  className = ''
}) => {
  // Get the nodes in the path
  const pathNodes = path.nodes.map(id => nodes.find(n => n.id === id)).filter(Boolean) as BranchNode[];
  
  // Extract energy values
  const energyValues = pathNodes.map(node => node.energy || 0.5);
  
  // Calculate min and max energy
  const minEnergy = Math.min(...energyValues);
  const maxEnergy = Math.max(...energyValues);
  
  // Calculate energy range
  const energyRange = maxEnergy - minEnergy;
  
  // Get energy color based on value
  const getEnergyColor = (energy: number) => {
    if (energy < 0.33) return 'bg-blue-500';
    if (energy < 0.66) return 'bg-green-500';
    return 'bg-red-500';
  };
  
  // Calculate the height of each energy bar (normalized to 0-100)
  const getBarHeight = (energy: number) => {
    // Normalize to 0-100 scale with a minimum height of 20%
    return 20 + ((energy - minEnergy) / Math.max(0.01, energyRange)) * 80;
  };
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Energy Flow
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Energy range info */}
          <div className="flex items-center justify-between text-sm">
            <div>Range: {Math.round(minEnergy * 10)} - {Math.round(maxEnergy * 10)}</div>
            <div>Variation: {Math.round(energyRange * 10)} points</div>
          </div>
          
          {/* Energy flow visualization */}
          <div className="h-32 flex items-end gap-1">
            {energyValues.map((energy, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div 
                  className={`w-full ${getEnergyColor(energy)} rounded-t-sm`} 
                  style={{ height: `${getBarHeight(energy)}%` }}
                ></div>
                <div className="text-xs mt-1">{Math.round(energy * 10)}</div>
                <div className="text-xs text-muted-foreground truncate max-w-full px-1">
                  {pathNodes[index].track.title.split(' ')[0]}
                </div>
              </div>
            ))}
          </div>
          
          {/* Energy transitions */}
          <div className="space-y-2">
            <div className="text-sm font-medium">Energy Transitions</div>
            <div className="space-y-1">
              {pathNodes.slice(0, -1).map((node, index) => {
                const currentEnergy = node.energy || 0.5;
                const nextEnergy = pathNodes[index + 1].energy || 0.5;
                const difference = nextEnergy - currentEnergy;
                const isIncrease = difference > 0;
                
                return (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-8 text-right text-xs">{Math.round(currentEnergy * 10)}</div>
                    <ArrowRight className={`h-4 w-4 ${isIncrease ? 'text-green-500' : 'text-red-500'}`} />
                    <div className="w-8 text-xs">{Math.round(nextEnergy * 10)}</div>
                    <div className={`text-xs ${isIncrease ? 'text-green-500' : 'text-red-500'}`}>
                      {isIncrease ? '+' : ''}{Math.round(difference * 10)}
                    </div>
                    <Progress 
                      value={50 + Math.abs(difference) * 100} 
                      className="h-1 flex-1"
                      style={{ 
                        background: isIncrease ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)',
                        color: isIncrease ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnergyFlowVisualization;
