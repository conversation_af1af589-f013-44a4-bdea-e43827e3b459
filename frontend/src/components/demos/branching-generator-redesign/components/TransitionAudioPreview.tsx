import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { BranchNode } from '../types';
import { Play, Pause, SkipForward, SkipBack, Music, Volume2, Volume1, VolumeX } from 'lucide-react';
import * as Tone from 'tone';

interface TransitionAudioPreviewProps {
  fromNode: BranchNode;
  toNode: BranchNode;
  className?: string;
}

const TransitionAudioPreview: React.FC<TransitionAudioPreviewProps> = ({
  fromNode,
  toNode,
  className = ''
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<'from' | 'to'>('from');
  const [volume, setVolume] = useState(0.8);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  
  const playerRef = useRef<Tone.Player | null>(null);
  const intervalRef = useRef<number | null>(null);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (playerRef.current) {
        playerRef.current.stop();
        playerRef.current.dispose();
      }
      if (intervalRef.current) {
        window.clearInterval(intervalRef.current);
      }
    };
  }, []);
  
  // Load and play the selected track
  const loadAndPlay = async (track: BranchNode['track']) => {
    setIsLoading(true);
    
    try {
      // Stop and dispose previous player if exists
      if (playerRef.current) {
        playerRef.current.stop();
        playerRef.current.dispose();
      }
      
      // Clear previous interval
      if (intervalRef.current) {
        window.clearInterval(intervalRef.current);
      }
      
      // Create a new player
      const player = new Tone.Player({
        url: track.filePath || `https://example.com/audio/${track.id}.mp3`,
        autostart: false,
        onload: () => {
          setIsLoading(false);
          setDuration(player.buffer.duration);
          player.start();
          setIsPlaying(true);
          
          // Update progress
          intervalRef.current = window.setInterval(() => {
            if (player.state === 'started') {
              setProgress(Tone.Transport.seconds);
            }
          }, 100);
        },
        onerror: () => {
          console.error('Error loading audio');
          setIsLoading(false);
        }
      }).toDestination();
      
      // Set volume
      player.volume.value = Tone.gainToDb(volume);
      
      // Store the player
      playerRef.current = player;
      
      // Start Tone.js context if needed
      if (Tone.context.state !== 'running') {
        await Tone.start();
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      setIsLoading(false);
    }
  };
  
  // Toggle play/pause
  const togglePlay = async () => {
    if (!playerRef.current) {
      // Load and play the current track
      const track = currentTrack === 'from' ? fromNode.track : toNode.track;
      await loadAndPlay(track);
    } else {
      if (isPlaying) {
        // Pause
        playerRef.current.stop();
        if (intervalRef.current) {
          window.clearInterval(intervalRef.current);
        }
        setIsPlaying(false);
      } else {
        // Resume
        playerRef.current.start();
        setIsPlaying(true);
        
        // Update progress
        intervalRef.current = window.setInterval(() => {
          if (playerRef.current && playerRef.current.state === 'started') {
            setProgress(Tone.Transport.seconds);
          }
        }, 100);
      }
    }
  };
  
  // Switch to the other track
  const switchTrack = async () => {
    const newTrack = currentTrack === 'from' ? 'to' : 'from';
    setCurrentTrack(newTrack);
    
    // Stop current playback
    if (playerRef.current) {
      playerRef.current.stop();
      playerRef.current.dispose();
      playerRef.current = null;
    }
    
    if (intervalRef.current) {
      window.clearInterval(intervalRef.current);
    }
    
    setIsPlaying(false);
    setProgress(0);
    
    // Load and play the new track if was playing
    if (isPlaying) {
      const track = newTrack === 'from' ? fromNode.track : toNode.track;
      await loadAndPlay(track);
    }
  };
  
  // Update volume
  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    
    if (playerRef.current) {
      playerRef.current.volume.value = Tone.gainToDb(newVolume);
    }
  };
  
  // Format time as mm:ss
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Get current track info
  const currentTrackInfo = currentTrack === 'from' ? fromNode.track : toNode.track;
  
  // Get volume icon based on level
  const getVolumeIcon = () => {
    if (volume === 0) return <VolumeX className="h-4 w-4" />;
    if (volume < 0.5) return <Volume1 className="h-4 w-4" />;
    return <Volume2 className="h-4 w-4" />;
  };
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Music className="h-5 w-5" />
          Transition Audio Preview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Current track info */}
          <div className="flex items-center gap-3 p-2 rounded-md bg-muted/20">
            <div className="w-10 h-10 rounded-md flex items-center justify-center bg-primary/10 text-primary overflow-hidden">
              {currentTrackInfo.imageUrl ? (
                <img 
                  src={currentTrackInfo.imageUrl} 
                  alt={currentTrackInfo.title} 
                  className="w-full h-full object-cover"
                />
              ) : (
                <Music className="w-5 h-5" />
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm truncate">{currentTrackInfo.title}</div>
              <div className="text-xs text-muted-foreground truncate">{currentTrackInfo.artist}</div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              {currentTrack === 'from' ? 'First Track' : 'Second Track'}
            </div>
          </div>
          
          {/* Playback controls */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="icon"
              onClick={switchTrack}
              disabled={isLoading}
              title={currentTrack === 'from' ? 'Switch to second track' : 'Switch to first track'}
            >
              {currentTrack === 'from' ? (
                <SkipForward className="h-4 w-4" />
              ) : (
                <SkipBack className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant={isPlaying ? "default" : "outline"}
              onClick={togglePlay}
              disabled={isLoading}
              className="w-24"
            >
              {isLoading ? (
                'Loading...'
              ) : isPlaying ? (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Play
                </>
              )}
            </Button>
            
            <div className="flex items-center gap-2">
              {getVolumeIcon()}
              <Slider
                value={[volume]}
                min={0}
                max={1}
                step={0.01}
                onValueChange={handleVolumeChange}
                className="w-20"
              />
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="space-y-1">
            <Slider
              value={[progress]}
              min={0}
              max={duration || 100}
              step={0.1}
              disabled
              className="h-1"
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div>{formatTime(progress)}</div>
              <div>{formatTime(duration)}</div>
            </div>
          </div>
          
          {/* Switch track button */}
          <Button
            variant="outline"
            size="sm"
            onClick={switchTrack}
            className="w-full"
          >
            Switch to {currentTrack === 'from' ? 'Second' : 'First'} Track
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransitionAudioPreview;
