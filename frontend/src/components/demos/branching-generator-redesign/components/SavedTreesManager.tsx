import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Save, 
  FolderOpen, 
  Trash2, 
  Download, 
  Upload, 
  Edit, 
  Check, 
  X,
  FileUp,
  FileDown
} from 'lucide-react';
import { 
  SavedBranchTree, 
  getAllSavedBranchTrees, 
  saveBranchTree, 
  loadBranchTree, 
  deleteSavedBranchTree, 
  renameSavedBranchTree,
  exportBranchTree,
  importBranchTree
} from '../utils/saveLoadUtils';
import { BranchNode, NodePositions, Path } from '../types';

interface SavedTreesManagerProps {
  nodes: BranchNode[];
  nodePositions: NodePositions;
  paths: Path[];
  selectedPathId?: string;
  onLoad: (savedTree: SavedBranchTree) => void;
  className?: string;
}

const SavedTreesManager: React.FC<SavedTreesManagerProps> = ({
  nodes,
  nodePositions,
  paths,
  selectedPathId,
  onLoad,
  className = ''
}) => {
  const [savedTrees, setSavedTrees] = useState<SavedBranchTree[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [treeName, setTreeName] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Load saved trees on mount
  useEffect(() => {
    loadSavedTrees();
  }, []);
  
  // Load saved trees from localStorage
  const loadSavedTrees = () => {
    const trees = getAllSavedBranchTrees();
    setSavedTrees(trees);
  };
  
  // Handle save
  const handleSave = () => {
    try {
      saveBranchTree(nodes, nodePositions, paths, selectedPathId, treeName);
      setIsSaveDialogOpen(false);
      setTreeName('');
      loadSavedTrees();
    } catch (error) {
      console.error('Error saving branch tree:', error);
      alert('Failed to save branch tree');
    }
  };
  
  // Handle load
  const handleLoad = (id: string) => {
    try {
      const tree = loadBranchTree(id);
      onLoad(tree);
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error loading branch tree:', error);
      alert('Failed to load branch tree');
    }
  };
  
  // Handle delete
  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this branch tree?')) {
      try {
        deleteSavedBranchTree(id);
        loadSavedTrees();
      } catch (error) {
        console.error('Error deleting branch tree:', error);
        alert('Failed to delete branch tree');
      }
    }
  };
  
  // Handle rename
  const handleRename = (id: string) => {
    try {
      renameSavedBranchTree(id, editingName);
      setEditingId(null);
      setEditingName('');
      loadSavedTrees();
    } catch (error) {
      console.error('Error renaming branch tree:', error);
      alert('Failed to rename branch tree');
    }
  };
  
  // Handle export
  const handleExport = (id: string) => {
    try {
      exportBranchTree(id);
    } catch (error) {
      console.error('Error exporting branch tree:', error);
      alert('Failed to export branch tree');
    }
  };
  
  // Handle import
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    try {
      const tree = await importBranchTree(file);
      loadSavedTrees();
      alert(`Successfully imported "${tree.name}"`);
    } catch (error) {
      console.error('Error importing branch tree:', error);
      alert('Failed to import branch tree');
    }
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Save className="h-5 w-5" />
          Saved Branch Trees
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={() => setIsSaveDialogOpen(true)}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Current Tree
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsDialogOpen(true)}
            >
              <FolderOpen className="h-4 w-4 mr-2" />
              Load Tree
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImport}
              accept=".json"
              className="hidden"
            />
          </div>
          
          {/* Recent trees */}
          <div className="space-y-2">
            <div className="text-sm font-medium">Recent Trees</div>
            {savedTrees.length > 0 ? (
              <div className="space-y-2 max-h-[200px] overflow-y-auto pr-1">
                {savedTrees.slice(0, 5).map((tree) => (
                  <div 
                    key={tree.id} 
                    className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{tree.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatDate(tree.timestamp)}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleLoad(tree.id)}
                        title="Load"
                      >
                        <FolderOpen className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleExport(tree.id)}
                        title="Export"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center p-4 text-muted-foreground">
                No saved trees yet
              </div>
            )}
          </div>
        </div>
      </CardContent>
      
      {/* Save dialog */}
      <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Branch Tree</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="tree-name" className="text-sm font-medium">
                Tree Name
              </label>
              <Input
                id="tree-name"
                value={treeName}
                onChange={(e) => setTreeName(e.target.value)}
                placeholder="Enter a name for this branch tree"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSaveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!treeName.trim()}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Load dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Load Branch Tree</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {savedTrees.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Nodes</TableHead>
                    <TableHead>Paths</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {savedTrees.map((tree) => (
                    <TableRow key={tree.id}>
                      <TableCell>
                        {editingId === tree.id ? (
                          <div className="flex items-center gap-2">
                            <Input
                              value={editingName}
                              onChange={(e) => setEditingName(e.target.value)}
                              className="h-8"
                            />
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRename(tree.id)}
                              disabled={!editingName.trim()}
                              className="h-8 w-8"
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setEditingId(null)}
                              className="h-8 w-8"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="font-medium">{tree.name}</div>
                        )}
                      </TableCell>
                      <TableCell>{formatDate(tree.timestamp)}</TableCell>
                      <TableCell>{tree.nodes.length}</TableCell>
                      <TableCell>{tree.paths.length}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setEditingId(tree.id);
                              setEditingName(tree.name);
                            }}
                            title="Rename"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleExport(tree.id)}
                            title="Export"
                          >
                            <FileDown className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(tree.id)}
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => handleLoad(tree.id)}
                          >
                            Load
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center p-8 text-muted-foreground">
                No saved trees yet
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default SavedTreesManager;
