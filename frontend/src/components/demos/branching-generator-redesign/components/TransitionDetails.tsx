import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TransitionDetails as TransitionDetailsType } from '../types';
import { ArrowRight, Music, Zap, Clock, Key, Disc } from 'lucide-react';

interface TransitionDetailsProps {
  details: TransitionDetailsType;
  className?: string;
}

const TransitionDetails: React.FC<TransitionDetailsProps> = ({ details, className = '' }) => {
  const {
    fromTrack,
    toTrack,
    score,
    keyCompatibility,
    bpmDifference,
    energyDifference,
    recommendedTransitionType
  } = details;
  
  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-500';
    if (score >= 0.6) return 'text-yellow-500';
    return 'text-red-500';
  };
  
  // Get score label based on value
  const getScoreLabel = (score: number) => {
    if (score >= 0.8) return 'Excellent';
    if (score >= 0.6) return 'Good';
    if (score >= 0.4) return 'Fair';
    return 'Poor';
  };
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <ArrowRight className="h-5 w-5" />
          Transition Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Tracks */}
          <div className="flex items-center gap-2">
            <div className="flex-1 p-2 rounded-md bg-muted/20">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-md flex items-center justify-center bg-primary/10 text-primary overflow-hidden">
                  {fromTrack.imageUrl ? (
                    <img 
                      src={fromTrack.imageUrl} 
                      alt={fromTrack.title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Music className="w-4 h-4" />
                  )}
                </div>
                <div className="min-w-0">
                  <div className="font-medium text-sm truncate">{fromTrack.title}</div>
                  <div className="text-xs text-muted-foreground truncate">{fromTrack.artist}</div>
                </div>
              </div>
            </div>
            
            <ArrowRight className="h-5 w-5 text-muted-foreground" />
            
            <div className="flex-1 p-2 rounded-md bg-muted/20">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-md flex items-center justify-center bg-primary/10 text-primary overflow-hidden">
                  {toTrack.imageUrl ? (
                    <img 
                      src={toTrack.imageUrl} 
                      alt={toTrack.title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Music className="w-4 h-4" />
                  )}
                </div>
                <div className="min-w-0">
                  <div className="font-medium text-sm truncate">{toTrack.title}</div>
                  <div className="text-xs text-muted-foreground truncate">{toTrack.artist}</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Overall score */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Overall Compatibility</div>
              <div className={`text-sm font-medium ${getScoreColor(score)}`}>
                {Math.round(score * 100)}% - {getScoreLabel(score)}
              </div>
            </div>
            <Progress value={score * 100} className="h-2" />
          </div>
          
          {/* Recommended transition */}
          <div className="bg-muted/20 p-2 rounded-md">
            <div className="text-sm font-medium mb-1">Recommended Transition</div>
            <Badge className="text-xs">{recommendedTransitionType}</Badge>
          </div>
          
          {/* Compatibility details */}
          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-xs font-medium">
                <Key className="h-3 w-3" />
                Key Compatibility
              </div>
              <Progress value={keyCompatibility * 100} className="h-1" />
              <div className="text-xs text-muted-foreground">
                {fromTrack.key || '?'} → {toTrack.key || '?'}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-xs font-medium">
                <Clock className="h-3 w-3" />
                BPM Difference
              </div>
              <Progress 
                value={Math.max(0, 100 - (bpmDifference / 30) * 100)} 
                className="h-1" 
              />
              <div className="text-xs text-muted-foreground">
                {fromTrack.bpm || '?'} → {toTrack.bpm || '?'} ({bpmDifference > 0 ? '+' : ''}{bpmDifference.toFixed(1)})
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-xs font-medium">
                <Zap className="h-3 w-3" />
                Energy Change
              </div>
              <Progress 
                value={Math.max(0, 100 - (Math.abs(energyDifference) / 0.4) * 100)} 
                className="h-1" 
              />
              <div className="text-xs text-muted-foreground">
                {(fromTrack.energy !== undefined ? Math.round(fromTrack.energy * 10) : '?')} → 
                {(toTrack.energy !== undefined ? Math.round(toTrack.energy * 10) : '?')} 
                ({energyDifference > 0 ? '+' : ''}{(energyDifference * 10).toFixed(1)})
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransitionDetails;
