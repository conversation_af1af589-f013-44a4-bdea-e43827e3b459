import React from 'react';
import { Path, BranchNode, TransitionDetails } from '../types';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Disc, Music, ArrowRight, Play, Check, Star, Zap, Clock, Key } from 'lucide-react';
import { getTransitionDetails } from '../utils/mixAnalysis';
import { Progress } from '@/components/ui/progress';

interface PathPreviewProps {
  path: Path;
  nodes: BranchNode[];
  onSelect: (path: Path) => void;
  selectedPath?: Path;
  onPreviewTransition?: (fromId: string, toId: string) => void;
}

const PathPreview: React.FC<PathPreviewProps> = ({
  path,
  nodes,
  onSelect,
  selectedPath,
  onPreviewTransition
}) => {
  const isSelected = selectedPath?.id === path.id;
  
  // Get the nodes in the path
  const pathNodes = path.nodes.map(id => nodes.find(n => n.id === id)).filter(Boolean) as BranchNode[];
  
  // Calculate overall path score
  const overallScore = path.overallScore || (
    path.transitions.reduce((sum, t) => sum + t.score, 0) / 
    Math.max(1, path.transitions.length)
  );
  
  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-500';
    if (score >= 0.6) return 'text-yellow-500';
    return 'text-red-500';
  };
  
  // Get score label based on value
  const getScoreLabel = (score: number) => {
    if (score >= 0.8) return 'Excellent';
    if (score >= 0.6) return 'Good';
    if (score >= 0.4) return 'Fair';
    return 'Poor';
  };
  
  return (
    <Card className={`mb-4 overflow-hidden transition-all ${isSelected ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        {/* Path header with score and select button */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className={`p-1 rounded-full ${getScoreColor(overallScore)}`}>
              <Star className="h-5 w-5" />
            </div>
            <div>
              <div className="font-medium">Path Score: {Math.round(overallScore * 100)}%</div>
              <div className="text-xs text-muted-foreground">{getScoreLabel(overallScore)}</div>
            </div>
          </div>
          
          <Button
            variant={isSelected ? "default" : "outline"}
            size="sm"
            onClick={() => onSelect(path)}
          >
            {isSelected ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                Selected
              </>
            ) : (
              'Select Path'
            )}
          </Button>
        </div>
        
        {/* Path visualization */}
        <div className="space-y-2">
          {pathNodes.map((node, index) => (
            <React.Fragment key={node.id}>
              {/* Track card */}
              <div className="flex items-center gap-3 p-2 rounded-md bg-muted/20">
                <div className={`w-10 h-10 rounded-md flex items-center justify-center bg-primary/10 text-primary overflow-hidden`}>
                  {node.track.imageUrl ? (
                    <img 
                      src={node.track.imageUrl} 
                      alt={node.track.title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Music className="w-5 h-5" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">{node.track.title}</div>
                  <div className="text-xs text-muted-foreground truncate">{node.track.artist}</div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Key className="w-3 h-3" />
                    {node.key || '?'}
                  </Badge>
                  
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {node.bpm || '?'}
                  </Badge>
                </div>
              </div>
              
              {/* Transition details */}
              {index < pathNodes.length - 1 && (
                <div className="pl-6 py-1">
                  <TransitionPreview 
                    fromNode={node}
                    toNode={pathNodes[index + 1]}
                    transitionScore={path.transitions[index]?.score || 0}
                    onPreview={onPreviewTransition ? 
                      () => onPreviewTransition(node.id, pathNodes[index + 1].id) : 
                      undefined
                    }
                  />
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface TransitionPreviewProps {
  fromNode: BranchNode;
  toNode: BranchNode;
  transitionScore: number;
  onPreview?: () => void;
}

const TransitionPreview: React.FC<TransitionPreviewProps> = ({
  fromNode,
  toNode,
  transitionScore,
  onPreview
}) => {
  // Get detailed transition information
  const details = getTransitionDetails(fromNode.track, toNode.track);
  
  return (
    <div className="flex items-center gap-2">
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
          <Badge variant="outline" className="text-xs">
            {details.recommendedTransitionType}
          </Badge>
          <div className="text-xs text-muted-foreground">
            Score: {Math.round(transitionScore * 100)}%
          </div>
        </div>
        
        <Progress value={transitionScore * 100} className="h-1" />
      </div>
      
      {onPreview && (
        <Button variant="ghost" size="icon" onClick={onPreview} className="h-6 w-6">
          <Play className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

export default PathPreview;
