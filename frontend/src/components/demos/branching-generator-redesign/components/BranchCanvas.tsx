import React, { useCallback } from 'react';
import <PERSON>act<PERSON><PERSON>, { 
  Background, 
  Controls, 
  Node, 
  Edge, 
  ReactFlowInstance,
  MiniMap
} from 'reactflow';
import 'reactflow/dist/style.css';
import { BranchNode, Path, NodePositions } from '../types';
import BranchNodeComponent from './BranchNode';

interface BranchCanvasProps {
  nodes: BranchNode[];
  flowNodes: Node[];
  flowEdges: Edge[];
  selectedPath?: Path;
  onNodeClick: (node: BranchNode) => void;
  onNodeDragStop: (event: React.MouseEvent, node: Node) => void;
  onInit: (instance: ReactFlowInstance) => void;
  className?: string;
}

const nodeTypes = {
  branchNode: BranchNodeComponent,
};

const BranchCanvas: React.FC<BranchCanvasProps> = ({
  nodes,
  flowNodes,
  flowEdges,
  selectedPath,
  onNodeClick,
  onNodeDragStop,
  onInit,
  className = ''
}) => {
  // Determine which nodes are on the selected path
  const selectedNodeIds = selectedPath?.nodes || [];
  
  // Create a custom minimap node
  const minimapNodeColor = useCallback((node: Node) => {
    if (selectedNodeIds.includes(node.id)) {
      return '#3b82f6'; // blue-500
    }
    return '#64748b'; // slate-500
  }, [selectedNodeIds]);
  
  return (
    <div className={`${className}`}>
      <ReactFlow
        nodes={flowNodes}
        edges={flowEdges}
        nodeTypes={nodeTypes}
        onInit={onInit}
        onNodeDragStop={onNodeDragStop}
        fitView={nodes.length <= 1} // Only fit view automatically when we have just the root node
        className="bg-background"
        fitViewOptions={{ padding: 0.3 }}
        minZoom={0.5}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
      >
        <Background 
          color="#94a3b8" 
          gap={16} 
          size={1}
          variant="dots"
        />
        <Controls />
        <MiniMap 
          nodeColor={minimapNodeColor}
          maskColor="rgba(0, 0, 0, 0.1)"
          className="bg-muted/50 rounded-md border border-border"
        />
      </ReactFlow>
    </div>
  );
};

export default BranchCanvas;
