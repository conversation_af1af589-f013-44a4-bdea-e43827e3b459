import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { BranchNode } from '../types';
import { Disc, Music, Plus, Zap, Key, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface BranchNodeProps {
  data: {
    node: BranchNode;
    onClick: (node: BranchNode) => void;
    isOnSelectedPath: boolean;
  };
  selected?: boolean;
}

const BranchNodeComponent: React.FC<BranchNodeProps> = ({ data, selected }) => {
  const { node, onClick, isOnSelectedPath } = data;
  const { track } = node;

  // Get a color based on energy level for visual indication
  const getEnergyColor = (energy: number) => {
    if (energy < 0.33) return 'bg-blue-500';
    if (energy < 0.66) return 'bg-green-500';
    return 'bg-red-500';
  };

  // Get a color based on the track's key
  const getKeyColor = (key: string) => {
    // Simple hash function to generate a color from a string
    const hash = Array.from(key).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue = hash % 360;
    return `hsl(${hue}, 70%, 60%)`;
  };

  const energyColor = getEnergyColor(node.energy);
  const keyColor = getKeyColor(node.key);

  return (
    <div
      className={`p-3 rounded-lg border-2 ${selected ? 'border-primary' : isOnSelectedPath ? 'border-blue-500' : 'border-border'} 
                 ${isOnSelectedPath ? 'bg-blue-50 dark:bg-blue-950' : 'bg-card'} 
                 shadow-md w-[220px] transition-all duration-200 hover:shadow-lg`}
      onClick={() => onClick(node)}
    >
      {/* Input handle for parent connection */}
      {node.parent && (
        <Handle
          type="target"
          position={Position.Top}
          className={`w-3 h-3 ${isOnSelectedPath ? 'bg-blue-500' : 'bg-primary'}`}
        />
      )}

      {/* Track info */}
      <div className="flex items-center gap-3 mb-3">
        <div 
          className={`w-12 h-12 rounded-md flex items-center justify-center ${energyColor} text-white overflow-hidden`}
        >
          {track.imageUrl ? (
            <img 
              src={track.imageUrl} 
              alt={track.title} 
              className="w-full h-full object-cover"
            />
          ) : (
            <Music className="w-6 h-6" />
          )}
        </div>
        <div className="overflow-hidden flex-1">
          <div className="font-medium text-sm truncate">{track.title}</div>
          <div className="text-xs text-muted-foreground truncate">{track.artist}</div>
          
          {/* Genre badge if available */}
          {track.genre && (
            <Badge variant="outline" className="mt-1 text-xs">
              {track.genre}
            </Badge>
          )}
        </div>
      </div>

      {/* Track details */}
      <div className="grid grid-cols-3 gap-2 text-xs mb-3">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 bg-muted/50 p-1 rounded">
                <Key className="w-3 h-3 text-muted-foreground" />
                <span>{track.key || '?'}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Key: {track.key || 'Unknown'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 bg-muted/50 p-1 rounded">
                <Clock className="w-3 h-3 text-muted-foreground" />
                <span>{track.bpm || '?'}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>BPM: {track.bpm || 'Unknown'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 bg-muted/50 p-1 rounded">
                <Zap className="w-3 h-3 text-muted-foreground" />
                <span>{Math.round((node.energy || 0.5) * 10)}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Energy: {Math.round((node.energy || 0.5) * 10)}/10</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Expand button indicator */}
      {node.children.length === 0 && (
        <div className="flex justify-center mt-1">
          <div className="text-xs text-muted-foreground flex items-center gap-1 bg-muted/30 px-2 py-1 rounded-full">
            <Plus className="w-3 h-3" />
            <span>Click to expand</span>
          </div>
        </div>
      )}

      {/* Output handle for child connections */}
      <Handle
        type="source"
        position={Position.Bottom}
        className={`w-3 h-3 ${isOnSelectedPath ? 'bg-blue-500' : 'bg-primary'}`}
      />
    </div>
  );
};

export default BranchNodeComponent;
