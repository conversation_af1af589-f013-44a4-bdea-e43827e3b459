import { BranchNode, NodePositions, Path } from '../types';

// Interface for saved branch tree
export interface SavedBranchTree {
  id: string;
  name: string;
  timestamp: string;
  nodes: BranchNode[];
  nodePositions: NodePositions;
  paths: Path[];
  selectedPathId?: string;
}

/**
 * Save the current branch tree to localStorage
 */
export const saveBranchTree = (
  nodes: BranchNode[],
  nodePositions: NodePositions,
  paths: Path[],
  selectedPathId?: string,
  name: string = 'Untitled Branch Tree'
): string => {
  try {
    // Generate a unique ID for this saved tree
    const id = `branch-tree-${Date.now()}`;
    
    // Create the saved tree object
    const savedTree: SavedBranchTree = {
      id,
      name,
      timestamp: new Date().toISOString(),
      nodes,
      nodePositions,
      paths,
      selectedPathId
    };
    
    // Get existing saved trees
    const savedTreesJson = localStorage.getItem('branchTrees');
    const savedTrees: Record<string, SavedBranchTree> = savedTreesJson 
      ? JSON.parse(savedTreesJson) 
      : {};
    
    // Add the new tree
    savedTrees[id] = savedTree;
    
    // Save back to localStorage
    localStorage.setItem('branchTrees', JSON.stringify(savedTrees));
    
    return id;
  } catch (error) {
    console.error('Error saving branch tree:', error);
    throw new Error('Failed to save branch tree');
  }
};

/**
 * Load a branch tree from localStorage by ID
 */
export const loadBranchTree = (id: string): SavedBranchTree => {
  try {
    // Get saved trees
    const savedTreesJson = localStorage.getItem('branchTrees');
    if (!savedTreesJson) {
      throw new Error('No saved branch trees found');
    }
    
    const savedTrees: Record<string, SavedBranchTree> = JSON.parse(savedTreesJson);
    
    // Get the requested tree
    const savedTree = savedTrees[id];
    if (!savedTree) {
      throw new Error(`Branch tree with ID ${id} not found`);
    }
    
    return savedTree;
  } catch (error) {
    console.error('Error loading branch tree:', error);
    throw new Error('Failed to load branch tree');
  }
};

/**
 * Get all saved branch trees
 */
export const getAllSavedBranchTrees = (): SavedBranchTree[] => {
  try {
    // Get saved trees
    const savedTreesJson = localStorage.getItem('branchTrees');
    if (!savedTreesJson) {
      return [];
    }
    
    const savedTrees: Record<string, SavedBranchTree> = JSON.parse(savedTreesJson);
    
    // Convert to array and sort by timestamp (newest first)
    return Object.values(savedTrees).sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
  } catch (error) {
    console.error('Error getting saved branch trees:', error);
    return [];
  }
};

/**
 * Delete a saved branch tree
 */
export const deleteSavedBranchTree = (id: string): boolean => {
  try {
    // Get saved trees
    const savedTreesJson = localStorage.getItem('branchTrees');
    if (!savedTreesJson) {
      return false;
    }
    
    const savedTrees: Record<string, SavedBranchTree> = JSON.parse(savedTreesJson);
    
    // Check if the tree exists
    if (!savedTrees[id]) {
      return false;
    }
    
    // Delete the tree
    delete savedTrees[id];
    
    // Save back to localStorage
    localStorage.setItem('branchTrees', JSON.stringify(savedTrees));
    
    return true;
  } catch (error) {
    console.error('Error deleting branch tree:', error);
    return false;
  }
};

/**
 * Update a saved branch tree's name
 */
export const renameSavedBranchTree = (id: string, newName: string): boolean => {
  try {
    // Get saved trees
    const savedTreesJson = localStorage.getItem('branchTrees');
    if (!savedTreesJson) {
      return false;
    }
    
    const savedTrees: Record<string, SavedBranchTree> = JSON.parse(savedTreesJson);
    
    // Check if the tree exists
    if (!savedTrees[id]) {
      return false;
    }
    
    // Update the name
    savedTrees[id].name = newName;
    
    // Save back to localStorage
    localStorage.setItem('branchTrees', JSON.stringify(savedTrees));
    
    return true;
  } catch (error) {
    console.error('Error renaming branch tree:', error);
    return false;
  }
};

/**
 * Export a branch tree as a JSON file
 */
export const exportBranchTree = (id: string): void => {
  try {
    // Load the tree
    const tree = loadBranchTree(id);
    
    // Convert to JSON
    const treeJson = JSON.stringify(tree, null, 2);
    
    // Create a blob
    const blob = new Blob([treeJson], { type: 'application/json' });
    
    // Create a download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${tree.name.replace(/\s+/g, '-').toLowerCase()}-${id}.json`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    
    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting branch tree:', error);
    throw new Error('Failed to export branch tree');
  }
};

/**
 * Import a branch tree from a JSON file
 */
export const importBranchTree = async (file: File): Promise<SavedBranchTree> => {
  try {
    // Read the file
    const text = await file.text();
    
    // Parse the JSON
    const tree: SavedBranchTree = JSON.parse(text);
    
    // Validate the tree
    if (!tree.id || !tree.nodes || !tree.nodePositions || !tree.paths) {
      throw new Error('Invalid branch tree file');
    }
    
    // Generate a new ID to avoid conflicts
    const newId = `branch-tree-${Date.now()}`;
    tree.id = newId;
    
    // Update timestamp
    tree.timestamp = new Date().toISOString();
    
    // Get existing saved trees
    const savedTreesJson = localStorage.getItem('branchTrees');
    const savedTrees: Record<string, SavedBranchTree> = savedTreesJson 
      ? JSON.parse(savedTreesJson) 
      : {};
    
    // Add the imported tree
    savedTrees[newId] = tree;
    
    // Save back to localStorage
    localStorage.setItem('branchTrees', JSON.stringify(savedTrees));
    
    return tree;
  } catch (error) {
    console.error('Error importing branch tree:', error);
    throw new Error('Failed to import branch tree');
  }
};
