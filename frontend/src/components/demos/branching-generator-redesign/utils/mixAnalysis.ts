import { Track } from "@/types/api/tracks";
import { GenerateOptions, TransitionDetails } from "../types";

/**
 * Generate a list of compatible tracks based on the current track
 */
export const generateCompatibleTracks = async (
  currentTrack: Track,
  availableTracks: Track[],
  options: GenerateOptions = {}
): Promise<Track[]> => {
  const {
    count = 3,
    energyRange = 0.2,
    bpmRange = 15,
    genreWeight = 1,
    keyWeight = 2,
    energyWeight = 1.5
  } = options;

  // Filter out the current track
  const tracksToChooseFrom = availableTracks.filter(t => t.id !== currentTrack.id);
  
  if (tracksToChooseFrom.length === 0) {
    return [];
  }

  // Calculate compatibility scores for each track
  const tracksWithScores = tracksToChooseFrom.map(track => {
    const score = calculateCompatibilityScore(
      currentTrack, 
      track, 
      { genreWeight, keyWeight, energyWeight }
    );
    
    return { track, score };
  });

  // Sort by score (descending) and take the top 'count' tracks
  const sortedTracks = tracksWithScores
    .sort((a, b) => b.score - a.score)
    .slice(0, count)
    .map(item => item.track);

  return sortedTracks;
};

/**
 * Calculate a compatibility score between two tracks
 */
export const calculateCompatibilityScore = (
  trackA: Track,
  trackB: Track,
  weights = { genreWeight: 1, keyWeight: 2, energyWeight: 1.5 }
): number => {
  const { genreWeight, keyWeight, energyWeight } = weights;
  
  // Key compatibility (0-1)
  const keyScore = calculateKeyCompatibility(trackA.key || '1A', trackB.key || '1A');
  
  // BPM compatibility (0-1)
  const bpmA = trackA.bpm || 120;
  const bpmB = trackB.bpm || 120;
  const bpmDiff = Math.abs(bpmA - bpmB);
  const bpmScore = Math.max(0, 1 - (bpmDiff / 30)); // 0 if diff >= 30 BPM
  
  // Energy compatibility (0-1)
  const energyA = trackA.energy !== undefined ? trackA.energy : 0.5;
  const energyB = trackB.energy !== undefined ? trackB.energy : 0.5;
  const energyDiff = Math.abs(energyA - energyB);
  const energyScore = Math.max(0, 1 - (energyDiff / 0.4)); // 0 if diff >= 0.4
  
  // Genre compatibility (0-1)
  const genreScore = trackA.genre && trackB.genre && trackA.genre === trackB.genre ? 1 : 0.5;
  
  // Weighted average
  const totalWeight = keyWeight + energyWeight + genreWeight + 1; // +1 for BPM
  const weightedScore = (
    (keyScore * keyWeight) +
    (energyScore * energyWeight) +
    (genreScore * genreWeight) +
    (bpmScore * 1)
  ) / totalWeight;
  
  return weightedScore;
};

/**
 * Calculate transition score between two tracks
 */
export const calculateTransitionScore = (
  fromTrack: Track,
  toTrack: Track
): number => {
  return calculateCompatibilityScore(fromTrack, toTrack);
};

/**
 * Calculate key compatibility based on Camelot wheel
 */
export const calculateKeyCompatibility = (keyA: string, keyB: string): number => {
  // Convert keys to Camelot notation if needed
  const camelotA = convertToCamelot(keyA);
  const camelotB = convertToCamelot(keyB);
  
  if (!camelotA || !camelotB) return 0.5; // Default if keys can't be parsed
  
  // Extract number and letter from Camelot notation
  const [numA, letterA] = [parseInt(camelotA.slice(0, -1)), camelotA.slice(-1)];
  const [numB, letterB] = [parseInt(camelotB.slice(0, -1)), camelotB.slice(-1)];
  
  // Same key = perfect match
  if (numA === numB && letterA === letterB) return 1;
  
  // Adjacent on wheel (same letter, number differs by 1 or 11)
  const numDiff = Math.min(Math.abs(numA - numB), 12 - Math.abs(numA - numB));
  if (letterA === letterB && numDiff === 1) return 0.9;
  
  // Relative major/minor (same number, different letter)
  if (numA === numB && letterA !== letterB) return 0.8;
  
  // Energy boost/drop (number +/- 1, different letter)
  if (numDiff === 1 && letterA !== letterB) return 0.7;
  
  // Further away on wheel
  if (numDiff === 2) return 0.5;
  if (numDiff === 3) return 0.3;
  
  // Default for other combinations
  return 0.2;
};

/**
 * Convert musical key to Camelot notation
 */
export const convertToCamelot = (key: string): string | null => {
  // If already in Camelot notation (e.g., "8A"), return as is
  if (/^([1-9]|1[0-2])[AB]$/.test(key)) {
    return key;
  }
  
  // Map from musical keys to Camelot
  const keyToCamelot: Record<string, string> = {
    'C': '8B', 'Am': '8A',
    'G': '9B', 'Em': '9A',
    'D': '10B', 'Bm': '10A',
    'A': '11B', 'F#m': '11A',
    'E': '12B', 'C#m': '12A',
    'B': '1B', 'G#m': '1A',
    'F#': '2B', 'D#m': '2A',
    'C#': '3B', 'A#m': '3A',
    'G#': '4B', 'Fm': '4A',
    'D#': '5B', 'Cm': '5A',
    'A#': '6B', 'Gm': '6A',
    'F': '7B', 'Dm': '7A',
  };
  
  return keyToCamelot[key] || null;
};

/**
 * Get detailed transition information between two tracks
 */
export const getTransitionDetails = (
  fromTrack: Track,
  toTrack: Track
): TransitionDetails => {
  const keyCompatibility = calculateKeyCompatibility(
    fromTrack.key || '1A', 
    toTrack.key || '1A'
  );
  
  const bpmDifference = Math.abs((fromTrack.bpm || 120) - (toTrack.bpm || 120));
  
  const energyDifference = Math.abs(
    (fromTrack.energy !== undefined ? fromTrack.energy : 0.5) - 
    (toTrack.energy !== undefined ? toTrack.energy : 0.5)
  );
  
  const score = calculateTransitionScore(fromTrack, toTrack);
  
  // Determine recommended transition type based on characteristics
  let recommendedTransitionType = 'Blend';
  
  if (bpmDifference > 15) {
    recommendedTransitionType = 'Hard Cut';
  } else if (keyCompatibility < 0.6) {
    recommendedTransitionType = 'FX Transition';
  } else if (energyDifference > 0.3) {
    recommendedTransitionType = energyDifference > 0 ? 'Build Up' : 'Drop';
  }
  
  return {
    fromTrack,
    toTrack,
    score,
    keyCompatibility,
    bpmDifference,
    energyDifference,
    recommendedTransitionType
  };
};
