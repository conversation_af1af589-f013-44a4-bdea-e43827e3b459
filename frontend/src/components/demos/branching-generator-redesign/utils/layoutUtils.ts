import { BranchNode, NodePositions } from "../types";

/**
 * Calculate positions for all nodes in the tree
 */
export const calculateNodePositions = (
  nodes: BranchNode[],
  existingPositions: NodePositions = {},
  options = { 
    horizontalSpacing: 300, 
    verticalSpacing: 150,
    rootX: 400,
    rootY: 100
  }
): NodePositions => {
  const { horizontalSpacing, verticalSpacing, rootX, rootY } = options;
  
  // Create a map of node IDs to their positions
  const positions: NodePositions = { ...existingPositions };
  
  // Find the root node (node without a parent)
  const rootNode = nodes.find(node => !node.parent);
  if (!rootNode) return positions;
  
  // Set the root node position if not already set
  if (!positions[rootNode.id]) {
    positions[rootNode.id] = { x: rootX, y: rootY };
  }
  
  // Build a map of parent to children
  const childrenMap: Record<string, BranchNode[]> = {};
  nodes.forEach(node => {
    if (node.parent) {
      if (!childrenMap[node.parent]) {
        childrenMap[node.parent] = [];
      }
      childrenMap[node.parent].push(node);
    }
  });
  
  // Recursively position all nodes
  const positionChildren = (
    parentId: string, 
    level: number, 
    startIndex: number
  ): number => {
    const children = childrenMap[parentId] || [];
    const parentPos = positions[parentId];
    
    if (children.length === 0) return startIndex;
    
    // Calculate the total width needed for all children
    const totalWidth = (children.length - 1) * horizontalSpacing;
    
    // Calculate the starting X position for the first child
    const startX = parentPos.x - totalWidth / 2;
    
    // Position each child
    let currentIndex = startIndex;
    children.forEach((child, index) => {
      const childX = startX + index * horizontalSpacing;
      const childY = parentPos.y + verticalSpacing;
      
      // Set the child position if not already set
      if (!positions[child.id]) {
        positions[child.id] = { x: childX, y: childY };
      }
      
      // Position this child's children
      currentIndex = positionChildren(child.id, level + 1, currentIndex + 1);
    });
    
    return currentIndex;
  };
  
  // Start positioning from the root
  positionChildren(rootNode.id, 0, 0);
  
  return positions;
};

/**
 * Optimize node positions to minimize edge crossings
 */
export const optimizeNodePositions = (
  nodes: BranchNode[],
  positions: NodePositions
): NodePositions => {
  // Create a copy of the positions to modify
  const optimizedPositions = { ...positions };
  
  // Build a map of parent to children
  const childrenMap: Record<string, BranchNode[]> = {};
  nodes.forEach(node => {
    if (node.parent) {
      if (!childrenMap[node.parent]) {
        childrenMap[node.parent] = [];
      }
      childrenMap[node.parent].push(node);
    }
  });
  
  // Sort children by their x position
  Object.keys(childrenMap).forEach(parentId => {
    const children = childrenMap[parentId];
    if (children.length <= 1) return;
    
    // Sort children by their current x position
    children.sort((a, b) => {
      const posA = optimizedPositions[a.id];
      const posB = optimizedPositions[b.id];
      return posA.x - posB.x;
    });
    
    // Reposition children to be evenly spaced
    const parentPos = optimizedPositions[parentId];
    const totalWidth = (children.length - 1) * 300;
    const startX = parentPos.x - totalWidth / 2;
    
    children.forEach((child, index) => {
      optimizedPositions[child.id] = {
        ...optimizedPositions[child.id],
        x: startX + index * 300
      };
    });
  });
  
  return optimizedPositions;
};

/**
 * Get the viewport bounds for a set of nodes
 */
export const getViewportBounds = (
  positions: NodePositions,
  padding = 50
): { minX: number; minY: number; maxX: number; maxY: number } => {
  if (Object.keys(positions).length === 0) {
    return { minX: 0, minY: 0, maxX: 800, maxY: 600 };
  }
  
  // Find the min and max coordinates
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;
  
  Object.values(positions).forEach(pos => {
    minX = Math.min(minX, pos.x);
    minY = Math.min(minY, pos.y);
    maxX = Math.max(maxX, pos.x);
    maxY = Math.max(maxY, pos.y);
  });
  
  // Add padding
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;
  
  return { minX, minY, maxX, maxY };
};
