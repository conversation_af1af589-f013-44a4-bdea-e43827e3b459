import { Track } from "@/types/api/tracks";

export interface BranchNode {
  id: string;
  track: Track;
  children: BranchNode[];
  parent?: string;
  energy: number;
  genre?: string;
  key: string;
  bpm: number;
}

export interface Path {
  id: string; // Unique identifier for the path
  nodes: string[];  // Array of node IDs representing the path
  transitions: Array<{
    from: string;
    to: string;
    score: number;
    type?: string; // Optional transition type
  }>;
  overallScore?: number; // Optional overall path score
}

export interface PreviewState {
  currentPath?: Path;
  previewingNode?: string;
  previewingTransition?: {
    from: string;
    to: string;
  };
  isPlaying?: boolean;
}

export interface BranchingGeneratorProps {
  onMixComplete: (tracks: Track[], transitions: any[]) => void;
  availableTracks: Track[];
  initialTrack?: Track;
}

export interface GenerateOptions {
  count?: number;
  energyRange?: number;
  bpmRange?: number;
  genreWeight?: number;
  keyWeight?: number;
  energyWeight?: number;
}

export interface NodePosition {
  x: number;
  y: number;
}

export interface NodePositions {
  [nodeId: string]: NodePosition;
}

export interface TransitionDetails {
  fromTrack: Track;
  toTrack: Track;
  score: number;
  keyCompatibility: number;
  bpmDifference: number;
  energyDifference: number;
  recommendedTransitionType?: string;
}
