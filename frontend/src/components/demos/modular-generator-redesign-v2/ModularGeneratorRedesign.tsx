/**
 * Modular Generator Redesign - Refactored Version
 * Main component that integrates all the modular hooks for better maintainability
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";

// Import all the modular hooks
import { useModularGeneratorState } from './hooks/useModularGeneratorState';
import { useBlockManagement } from './hooks/useBlockManagement';
import { useTrackManagement } from './hooks/useTrackManagement';
import { useMixOperations } from './hooks/useMixOperations';

// Import existing components that we'll reuse
import { CollectionSelector } from './components/CollectionSelector';
import { TemplateSelector } from './components/TemplateSelector';
import { BlockLibrary } from './components/BlockLibrary';
import { MixCanvas } from './components/MixCanvas';
import { TrackRecommendations } from './components/TrackRecommendations';
import { SaveMixModal } from '@/components/mixes/list/SaveMixModal';

// Import types
import { HarmonicBlock } from './types';

interface ModularGeneratorRedesignProps {
  onComplete?: (tracks: any[]) => void;
}

export const ModularGeneratorRedesignRefactored: React.FC<ModularGeneratorRedesignProps> = ({
  onComplete
}) => {
  const { toast } = useToast();

  // Main state management hook
  const state = useModularGeneratorState();

  // Block management hook
  const blockActions = useBlockManagement({
    mixBlocks: state.mixBlocks,
    setMixBlocks: state.setMixBlocks,
    mixBridges: state.mixBridges,
    setMixBridges: state.setMixBridges,
    customBlocks: state.customBlocks,
    selectedBlockId: state.selectedBlockId,
    setSelectedBlockId: state.setSelectedBlockId,
    setDragOverBlockId: state.setDragOverBlockId,
    setIsPopulatingTracks: state.setIsPopulatingTracks,
    autoPopulate: state.autoPopulate,
    selectedCollectionId: state.selectedCollectionId,
    selectedFolderId: state.selectedFolderId,
    selectedSources: state.selectedSources,
    selectedMixStyleId: state.selectedMixStyleId,
    effectiveTracks: state.effectiveTracks,
  });

  // Track management hook
  const trackActions = useTrackManagement({
    mixBlocks: state.mixBlocks,
    setMixBlocks: state.setMixBlocks,
    selectedBlockId: state.selectedBlockId,
    selectedPositionId: state.selectedPositionId,
    selectedCollectionId: state.selectedCollectionId,
    selectedFolderId: state.selectedFolderId,
    selectedSources: state.selectedSources,
    selectedMixStyleId: state.selectedMixStyleId,
    effectiveTracks: state.effectiveTracks,
    trackRecommendations: state.trackRecommendations,
    setTrackRecommendations: state.setTrackRecommendations,
    isLoadingRecommendations: state.isLoadingRecommendations,
    setIsLoadingRecommendations: state.setIsLoadingRecommendations,
  });

  // Mix operations hook
  const mixActions = useMixOperations({
    mixBlocks: state.mixBlocks,
    mixBridges: state.mixBridges,
    effectiveTracks: state.effectiveTracks,
    isSaveModalOpen: state.isSaveModalOpen,
    setIsSaveModalOpen: state.setIsSaveModalOpen,
    onComplete,
  });

  // Calculate total duration
  const totalDuration = React.useMemo(() => {
    return state.mixBlocks.reduce((total, block) => {
      return total + (block.duration || 0);
    }, 0);
  }, [state.mixBlocks]);

  // Calculate completion percentage
  const completionPercentage = React.useMemo(() => {
    const totalPositions = state.mixBlocks.reduce((total, block) => {
      return total + block.trackPositions.length;
    }, 0);

    const filledPositions = state.mixBlocks.reduce((total, block) => {
      return total + block.trackPositions.filter(pos => pos.trackId).length;
    }, 0);

    return totalPositions > 0 ? Math.round((filledPositions / totalPositions) * 100) : 0;
  }, [state.mixBlocks]);

  return (
    <div className="h-full flex flex-col space-y-4 p-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Modular Mix Generator</h1>
          <p className="text-muted-foreground">
            Build your mix using harmonic blocks and transitions
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {state.mixBlocks.length} blocks
          </Badge>
          <Badge variant="outline">
            {Math.round(totalDuration / 60)}min
          </Badge>
          <Badge variant="outline">
            {completionPercentage}% complete
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 grid grid-cols-12 gap-4">
        {/* Left Sidebar - Configuration */}
        <div className="col-span-3 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Collection Selector */}
              <CollectionSelector
                collections={state.apiCollections}
                isLoading={state.isCollectionsLoading}
                selectedCollectionId={state.selectedCollectionId}
                selectedFolderId={state.selectedFolderId}
                selectedSources={state.selectedSources}
                folders={state.folders}
                isLoadingFolders={state.isLoadingFolders}
                onCollectionSelect={state.handleCollectionSelectEnhanced}
                onFolderSelect={state.handleFolderSelectEnhanced}
                onSourcesSelect={state.handleSourcesSelectEnhanced}
              />

              <Separator />

              {/* Template Selector */}
              <TemplateSelector
                selectedTemplate={state.selectedTemplate}
                onTemplateSelect={(templateId) => {
                  state.setSelectedTemplate(templateId);
                  blockActions.loadTemplate(templateId);
                }}
                mixStyles={state.mixStyles}
                selectedMixStyleId={state.selectedMixStyleId}
                onMixStyleSelect={state.setSelectedMixStyleId}
                isLoadingMixStyles={state.isLoadingMixStyles}
              />

              <Separator />

              {/* Quick Actions */}
              <div className="space-y-2">
                <Button
                  onClick={trackActions.handlePopulateAll}
                  disabled={state.mixBlocks.length === 0 || state.effectiveTracks.length === 0}
                  className="w-full"
                >
                  Populate All Blocks
                </Button>
                <Button
                  onClick={mixActions.handleSaveMix}
                  variant="outline"
                  disabled={state.mixBlocks.length === 0}
                  className="w-full"
                >
                  Save Mix
                </Button>
                <Button
                  onClick={mixActions.handleComplete}
                  disabled={completionPercentage === 0}
                  className="w-full"
                >
                  Complete Mix ({completionPercentage}%)
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Block Library */}
          <Card className="flex-1">
            <CardHeader>
              <CardTitle>Block Library</CardTitle>
            </CardHeader>
            <CardContent>
              <BlockLibrary
                searchTerm={state.searchTerm}
                onSearchChange={state.setSearchTerm}
                filterType={state.filterType}
                onFilterTypeChange={state.setFilterType}
                filterDuration={state.filterDuration}
                onFilterDurationChange={state.setFilterDuration}
                customBlocks={state.customBlocks}
                onAddBlock={blockActions.addBlockToMix}
                showCreateCustomBlock={state.showCreateCustomBlock}
                onShowCreateCustomBlock={state.setShowCreateCustomBlock}
              />
            </CardContent>
          </Card>
        </div>

        {/* Center - Mix Canvas */}
        <div className="col-span-6">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Mix Canvas</CardTitle>
            </CardHeader>
            <CardContent className="h-full">
              <MixCanvas
                mixBlocks={state.mixBlocks}
                mixBridges={state.mixBridges}
                selectedBlockId={state.selectedBlockId}
                selectedPositionId={state.selectedPositionId}
                dragOverBlockId={state.dragOverBlockId}
                onBlockSelect={state.setSelectedBlockId}
                onPositionSelect={state.setSelectedPositionId}
                onBlockRemove={blockActions.removeBlock}
                onBlockDuplicate={blockActions.handleDuplicateBlock}
                onBlockPopulate={trackActions.handlePopulateBlock}
                onDragStart={blockActions.handleDragStart}
                onDragOver={blockActions.handleDragOver}
                onDrop={blockActions.handleDrop}
                onDragEnd={blockActions.handleDragEnd}
                showEnergyCurve={state.showEnergyCurve}
                showHarmonicPath={state.showHarmonicPath}
                zoomLevel={state.zoomLevel}
                viewMode={state.viewMode}
                isPopulatingTracks={state.isPopulatingTracks}
              />
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar - Track Recommendations */}
        <div className="col-span-3">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Track Recommendations</CardTitle>
            </CardHeader>
            <CardContent className="h-full">
              <TrackRecommendations
                selectedBlockId={state.selectedBlockId}
                selectedPositionId={state.selectedPositionId}
                trackRecommendations={state.trackRecommendations}
                isLoadingRecommendations={state.isLoadingRecommendations}
                onTrackSelect={trackActions.assignTrackToPosition}
                effectiveTracks={state.effectiveTracks}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Save Mix Modal */}
      <SaveMixModal
        isOpen={state.isSaveModalOpen}
        onClose={() => state.setIsSaveModalOpen(false)}
        onSave={mixActions.onSaveMixConfirm}
      />
    </div>
  );
};
