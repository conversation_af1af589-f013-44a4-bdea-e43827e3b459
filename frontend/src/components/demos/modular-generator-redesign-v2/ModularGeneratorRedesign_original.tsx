import React, { useState, useRef, useEffect } from "react";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { useToast } from "@/components/ui/use-toast";
import { ThemeProvider } from "@/providers/ThemeContext";
import { QueryProvider } from "@/providers/QueryProvider";
import { AnimationProvider } from "@/providers/AnimationProvider";
import { TransitionProvider } from "@/providers/TransitionProvider";
import { AIProvider } from "@/providers/AIProvider";
import { UserPreferencesProvider } from "@/providers/UserPreferencesProvider";
import { useCollections } from '@/hooks/useCollections';
import { getCollectionTracks, getCollectionFolders } from '@/services/api/collections';
import { useEnhancedCollectionData } from '../../mixes/generators/smart-v2/hooks/useEnhancedCollectionData';
import { Loader2 } from "lucide-react";

// MIGRATION: Import real mix style hook from the proper system
import { useMixStyles } from '@/hooks/useMixStyles';

import { useSaveMix } from '@/hooks/useMixes';
import { SaveMixModal, MixCreateData } from '@/components/mixes/list/SaveMixModal';

import BlockLibrary from "./components/BlockLibrary";
import MixCanvas from "./components/MixCanvas";
import DetailsSidebar from "./components/DetailsSidebar";
import ControlBar from "./components/ControlBar";
import EnergyCurveVisualization from "./components/EnergyCurveVisualization";
import HarmonicPathVisualization from "./components/HarmonicPathVisualization";
import CustomBlockCreator from "./components/CustomBlockCreator";

import { blockLibrary } from "./data/blockLibrary";
import { bridgeLibrary } from "./data/bridgeLibrary";
import { templates } from "./data/templates";
// Removed mockTracks import - using real backend data only
import { autoPopulateBlockPositions } from "./utils";

import {
  ModularGeneratorProps,
  HarmonicBlock,
  TransitionBridge,
  Track,
  TrackPosition,
  TrackWithScore,
} from "./types";

/**
 * ModularGeneratorRedesign - A redesigned version of the modular blocks creator
 * with improved UI/UX and code organization
 */
const ModularGeneratorRedesign: React.FC<ModularGeneratorProps> = ({
  onComplete,
  onCancel,
}) => {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLDivElement>(null);

  const { mutate: saveMix, isPending: isSaving } = useSaveMix();
  const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);

  // Main state
  const [setDuration, setSetDuration] = useState<number>(60); // in minutes
  const [selectedTemplate, setSelectedTemplate] = useState<string>("from-scratch");
  const [showEnergyCurve, setShowEnergyCurve] = useState<boolean>(true);
  const [showHarmonicPath, setShowHarmonicPath] = useState<boolean>(true);
  const [autoPopulate, setAutoPopulate] = useState<boolean>(true);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [viewMode, setViewMode] = useState<"horizontal" | "vertical">("horizontal");
  const [showTutorial, setShowTutorial] = useState<boolean>(false);
  const [showCreateCustomBlock, setShowCreateCustomBlock] = useState<boolean>(false);

  // Mix blocks state
  const [mixBlocks, setMixBlocks] = useState<HarmonicBlock[]>([]);
  const [mixBridges, setMixBridges] = useState<TransitionBridge[]>([]);
  const [customBlocks, setCustomBlocks] = useState<HarmonicBlock[]>([]);
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
  const [selectedPositionId, setSelectedPositionId] = useState<string | null>(null);
  const [dragOverBlockId, setDragOverBlockId] = useState<string | null>(null);

  // Block library state
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterDuration, setFilterDuration] = useState<string>("all");

  // Track recommendations
  const [trackRecommendations, setTrackRecommendations] = useState<TrackWithScore[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState<boolean>(false);

  // Local loading states for auto-population
  const [isPopulatingTracks, setIsPopulatingTracks] = useState<boolean>(false);

  // Collection state from API
  const { collections: apiCollections, isLoading: isCollectionsLoading } = useCollections();

  // Enhanced collection data with multiple source support
  const {
    selectedCollectionId,
    selectedFolderId,
    selectedSources,
    folderTracks,
    filteredTracks,
    folders,
    isLoadingFolders,
    isLoadingTracks,
    trackSearchTerm,
    setTrackSearchTerm,
    handleSelectedCollection,
    handleSelectedFolder,
    handleMultipleSourcesSelected
  } = useEnhancedCollectionData();

  // Legacy state for backward compatibility
  const [collections, setCollections] = useState<any[]>([]);

  // MIGRATION: Mix Style State Integration - Step 1.1 (using real mix styles system)
  const {
    mixStyles,
    isLoading: isLoadingMixStyles,
    refetch: refetchMixStyles
  } = useMixStyles('modular'); // Filter for modular generator styles

  const [selectedMixStyleId, setSelectedMixStyleId] = useState<string>("none");

  const handleMixStyleSelect = (styleId: string) => {
    console.log('Mix style selected:', styleId);
    setSelectedMixStyleId(styleId);
  };

  // Collection handling is now managed by useEnhancedCollectionData hook

  // Update collections from API when they change
  useEffect(() => {
    if (apiCollections) {
      console.log('Collections received from API:', apiCollections);
      setCollections(apiCollections);
    } else {
      console.warn('No collections data received from API');
    }
  }, [apiCollections]);

  // MIGRATION: Removed old basic collection loading logic
  // Now using enhanced handlers: handleCollectionSelectEnhanced and handleFolderSelectEnhanced

  // MIGRATION: Effective tracks logic - REAL BACKEND DATA ONLY
  const effectiveTracks = React.useMemo(() => {
    // Use enhanced collection data tracks
    console.log(`MIGRATION: Using ${filteredTracks.length} tracks from enhanced collection data (no fallbacks)`);
    return filteredTracks;
  }, [filteredTracks]);

  // Filter blocks based on search and filters
  const filteredBlocks = React.useMemo(() => {
    let filtered = [...blockLibrary, ...customBlocks];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (block) =>
          block.name.toLowerCase().includes(term) ||
          block.code.toLowerCase().includes(term) ||
          block.type.toLowerCase().includes(term) ||
          (block.genreAffinity &&
            block.genreAffinity.some((genre) => genre.toLowerCase().includes(term)))
      );
    }

    // Apply type filter
    if (filterType !== "all") {
      filtered = filtered.filter((block) => block.type === filterType);
    }

    // Apply duration filter
    if (filterDuration !== "all") {
      const [min, max] = filterDuration.split("-").map(Number);
      filtered = filtered.filter(
        (block) => block.duration >= min && block.duration <= (max || Infinity)
      );
    }

    return filtered;
  }, [blockLibrary, customBlocks, searchTerm, filterType, filterDuration]);

  // Load template
  const loadTemplate = async (templateId: string) => {
    if (templateId === "from-scratch") {
      setMixBlocks([]);
      setMixBridges([]);
      return;
    }

    // Find template
    const template = templates.find((t) => t.id === templateId);
    if (!template) {
      toast({
        title: "Template not found",
        description: `Template with ID ${templateId} not found.`,
        variant: "destructive",
      });
      return;
    }

    // Load blocks from template
    const blocks: HarmonicBlock[] = [];
    const bridges: TransitionBridge[] = [];

    // In a real implementation, this would load from an API
    // For now, we'll just use the template's block IDs to find blocks in the library
    for (const blockId of template.blocks) {
      const block = blockLibrary.find((b) => b.id === blockId);
      if (block) {
        blocks.push({ ...block });
      }
    }

    // Add bridges between blocks
    for (let i = 0; i < blocks.length - 1; i++) {
      const fromBlock = blocks[i];
      const toBlock = blocks[i + 1];

      // Find a compatible bridge in the bridge library
      const bridge = bridgeLibrary.find(
        (b) => b.fromBlockId === fromBlock.id && b.toBlockId === toBlock.id
      );

      if (bridge) {
        bridges.push({ ...bridge });
      }
    }

    setMixBlocks(blocks);
    setMixBridges(bridges);

    toast({
      title: "Template loaded",
      description: `Loaded template: ${template.name}`,
    });
  };

  // Add block to mix
  const addBlockToMix = async (blockId: string) => {
    const block = [...blockLibrary, ...customBlocks].find((b) => b.id === blockId);
    if (!block) return;

    // Create a copy of the block with a new ID to avoid conflicts
    const newBlock = {
      ...block,
      id: `${block.id}-${Date.now()}`,
      trackPositions: block.trackPositions.map((pos) => ({
        ...pos,
        id: `${pos.id}-${Date.now()}`,
      })),
    };

    // Add block to mix
    setMixBlocks((prev) => [...prev, newBlock]);

    // If there are other blocks, try to add a bridge
    if (mixBlocks.length > 0) {
      const lastBlock = mixBlocks[mixBlocks.length - 1];

      // Find a compatible bridge in the bridge library
      const bridge = bridgeLibrary.find(
        (b) => b.fromBlockId === lastBlock.id.split("-")[0] && b.toBlockId === block.id
      );

      if (bridge) {
        // Create a copy of the bridge with updated IDs
        const newBridge = {
          ...bridge,
          id: `${bridge.id}-${Date.now()}`,
          fromBlockId: lastBlock.id,
          toBlockId: newBlock.id,
          trackPositions: bridge.trackPositions.map((pos) => ({
            ...pos,
            id: `${pos.id}-${Date.now()}`,
          })),
        };

        setMixBridges((prev) => [...prev, newBridge]);
      }
    }

    // PHASE 2: Advanced auto-population with performance monitoring and optimization
    // Enhanced from V1 with sophisticated monitoring and caching
    if (autoPopulate && (selectedCollectionId || (selectedSources && selectedSources.length > 0))) {
      toast({
        title: "Auto-populating block",
        description: "Finding compatible tracks using advanced algorithms...",
      });

      setIsPopulatingTracks(true);

      try {
        console.log('PHASE2: Starting advanced block auto-population');

        // Import enhanced services with performance monitoring
        const { populateBlockEnhanced, shouldUseBackendAPI } = await import('./services/apiService');
        const { getBlockPopulationParams, processPopulatedBlockResponse } = await import('./utils/compatibilityUtils');
        const { performanceMonitor } = await import('./services/performanceMonitor');

        // Check if we should use backend API
        const collectionForAPI = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);
        if (shouldUseBackendAPI(collectionForAPI, selectedMixStyleId)) {
          try {
            // Get population parameters
            const populationParams = getBlockPopulationParams(
              newBlock,
              collectionForAPI,
              selectedFolderId,
              selectedMixStyleId
            );

            console.log('PHASE2: Using backend API for block population');
            console.log('Population params:', JSON.stringify(populationParams, null, 2));

            // Call backend populate block API with performance monitoring
            const { populatedData, updatedBlock } = await performanceMonitor.measureAsync(
              'autoPopulation-backend',
              async () => {
                const populatedData = await populateBlockEnhanced(populationParams);
                const updatedBlock = processPopulatedBlockResponse(populatedData, newBlock);
                return { populatedData, updatedBlock };
              },
              {
                blockId: newBlock.id,
                blockType: newBlock.type,
                positionCount: newBlock.trackPositions.length,
                collectionId: collectionForAPI,
                folderId: selectedFolderId,
                mixStyleId: selectedMixStyleId
              }
            );

            // Update the block in state
            setMixBlocks(prev =>
              prev.map(b =>
                b.id === newBlock.id ? updatedBlock : b
              )
            );

            // Count populated positions for user feedback
            const populatedCount = updatedBlock.trackPositions.filter(pos => pos.trackId).length;
            const populationRate = (populatedCount / updatedBlock.trackPositions.length) * 100;

            toast({
              title: "Block populated with backend",
              description: `Populated ${populatedCount}/${updatedBlock.trackPositions.length} positions (${populationRate.toFixed(0)}%) using CamelotRules engine.`,
            });

            console.log(`PHASE2: Backend populated ${populatedCount} positions (${populationRate.toFixed(1)}% success rate)`);

          } catch (backendError) {
            console.error('MIGRATION: Backend auto-population failed, using frontend fallback:', backendError);

            // Fallback to frontend auto-population
            const tracksToUse = effectiveTracks;
            if (tracksToUse.length > 0) {
              const { autoPopulateBlockPositions } = await import('./utils');
              const updatedPositions = autoPopulateBlockPositions(newBlock.trackPositions, tracksToUse);

              setMixBlocks(prev =>
                prev.map(b =>
                  b.id === newBlock.id
                    ? { ...b, trackPositions: updatedPositions }
                    : b
                )
              );

              toast({
                title: "Block populated with fallback",
                description: "Backend unavailable, used local matching.",
                variant: "default",
              });
            }
          }
        } else {
          // Use frontend auto-population when no valid collection/mix style
          console.log('MIGRATION: Using frontend auto-population (no valid collection/mix style)');
          const tracksToUse = effectiveTracks;

          if (tracksToUse.length > 0) {
            const { autoPopulateBlockPositions } = await import('./utils');
            const updatedPositions = autoPopulateBlockPositions(newBlock.trackPositions, tracksToUse);

            setMixBlocks(prev =>
              prev.map(b =>
                b.id === newBlock.id
                  ? { ...b, trackPositions: updatedPositions }
                  : b
              )
            );

            toast({
              title: "Block populated locally",
              description: "Used local track matching.",
            });
          }
        }
      } catch (error) {
        console.error('MIGRATION: Error in enhanced auto-population:', error);
        toast({
          title: "Auto-population failed",
          description: "Could not find compatible tracks. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsPopulatingTracks(false);
      }
    }
  };

  // Remove block from mix
  const removeBlock = (blockId: string) => {
    // Remove block
    setMixBlocks((prev) => prev.filter((b) => b.id !== blockId));

    // Remove bridges connected to this block
    setMixBridges((prev) =>
      prev.filter((b) => b.fromBlockId !== blockId && b.toBlockId !== blockId)
    );

    // Clear selection if the selected block was removed
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null);
      setSelectedPositionId(null);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, blockId: string) => {
    e.dataTransfer.setData("blockId", blockId);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, blockId: string) => {
    e.preventDefault();
    setDragOverBlockId(blockId);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetBlockId: string) => {
    e.preventDefault();
    const sourceBlockId = e.dataTransfer.getData("blockId");

    // Find the source and target blocks
    const sourceIndex = mixBlocks.findIndex((b) => b.id === sourceBlockId);
    const targetIndex = mixBlocks.findIndex((b) => b.id === targetBlockId);

    if (sourceIndex === -1 || targetIndex === -1 || sourceIndex === targetIndex) {
      return;
    }

    // Reorder blocks
    const newBlocks = [...mixBlocks];
    const [movedBlock] = newBlocks.splice(sourceIndex, 1);
    newBlocks.splice(targetIndex, 0, movedBlock);

    setMixBlocks(newBlocks);

    // Update bridges (in a real implementation, this would be more complex)
    // For now, we'll just clear bridges and add new ones between adjacent blocks
    const newBridges: TransitionBridge[] = [];

    for (let i = 0; i < newBlocks.length - 1; i++) {
      const fromBlock = newBlocks[i];
      const toBlock = newBlocks[i + 1];

      // Find a compatible bridge in the bridge library
      const bridge = bridgeLibrary.find(
        (b) =>
          b.fromBlockId === fromBlock.id.split("-")[0] &&
          b.toBlockId === toBlock.id.split("-")[0]
      );

      if (bridge) {
        // Create a copy of the bridge with updated IDs
        const newBridge = {
          ...bridge,
          id: `${bridge.id}-${Date.now()}`,
          fromBlockId: fromBlock.id,
          toBlockId: toBlock.id,
          trackPositions: bridge.trackPositions.map((pos) => ({
            ...pos,
            id: `${pos.id}-${Date.now()}`,
          })),
        };

        newBridges.push(newBridge);
      }
    }

    setMixBridges(newBridges);
  };

  const handleDragEnd = () => {
    setDragOverBlockId(null);
  };

  // Assign track to position
  const assignTrackToPosition = (trackId: string) => {
    if (!selectedBlockId || !selectedPositionId) return;

    // Find the track - use effective tracks (V1 logic) with robust ID comparison
    const tracksToSearch = effectiveTracks;
    const track = tracksToSearch.find((t) => {
      // Handle both string and number IDs
      const tId = String(t.id);
      const searchId = String(trackId);
      return tId === searchId;
    });
    if (!track) return;

    // Update the block
    setMixBlocks((prev) =>
      prev.map((block) =>
        block.id === selectedBlockId
          ? {
              ...block,
              trackPositions: block.trackPositions.map((pos) =>
                pos.id === selectedPositionId
                  ? {
                      ...pos,
                      trackId: track.id,
                      trackName: track.title,
                      artist: track.artist,
                      bpm: track.bpm,
                      file_path: (track as any).file_path || (track as any).filePath,
                    }
                  : pos
              ),
            }
          : block
      )
    );

    toast({
      title: "Track assigned",
      description: `Assigned "${track.title}" to position.`,
    });
  };

  // PHASE 2: Advanced track recommendations with performance monitoring and caching
  // Enhanced from V1 with sophisticated algorithms and optimization
  useEffect(() => {
    if (!selectedBlockId || !selectedPositionId) {
      setTrackRecommendations([]);
      return;
    }

    const selectedBlock = mixBlocks.find((b) => b.id === selectedBlockId);
    const selectedPosition = selectedBlock?.trackPositions.find(
      (p) => p.id === selectedPositionId
    );

    if (!selectedBlock || !selectedPosition) {
      setTrackRecommendations([]);
      return;
    }

    // PHASE 2: Advanced recommendation logic with performance monitoring
    const fetchAdvancedRecommendations = async () => {
      setIsLoadingRecommendations(true);

      try {
        console.log('PHASE2: Starting advanced track recommendations');

        // Import advanced recommendation engine and performance monitor
        const { recommendationEngine } = await import('./services/recommendationEngine');
        const { performanceMonitor } = await import('./services/performanceMonitor');

        // Use performance monitoring
        const result = await performanceMonitor.measureAsync(
          'recommendation-fetch',
          async () => {
            const collectionForRecommendations = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);
            return await recommendationEngine.getRecommendations({
              position: selectedPosition,
              selectedCollectionId: collectionForRecommendations,
              selectedFolderId,
              selectedMixStyleId,
              availableTracks: effectiveTracks, // Only real collection tracks
              useCache: true
            });
          },
          {
            positionKey: selectedPosition.key,
            positionEnergy: selectedPosition.energy,
            collectionId: collectionForRecommendations,
            folderId: selectedFolderId,
            mixStyleId: selectedMixStyleId,
            availableTracksCount: effectiveTracks.length
          }
        );

        console.log(`PHASE2: Recommendation completed - Source: ${result.source}, Time: ${result.processingTime}ms, Cache Hit: ${result.cacheHit}`);
        console.log(`PHASE2: Found ${result.tracks.length} tracks from ${result.totalAvailable} available`);

        // Update state with advanced recommendations
        setTrackRecommendations(result.tracks);

        // Provide user feedback based on results
        if (result.tracks.length === 0) {
          if (effectiveTracks.length === 0) {
            toast({
              title: "No tracks available",
              description: "Please select a collection with tracks",
              variant: "destructive",
            });
          } else {
            const tracksWithKeys = effectiveTracks.filter(t => t.key).length;
            if (tracksWithKeys === 0) {
              toast({
                title: "No tracks with keys",
                description: "The selected collection has no tracks with Camelot keys",
                variant: "destructive",
              });
            } else {
              toast({
                title: "No compatible tracks",
                description: `No tracks compatible with key ${selectedPosition.key}. Try a different collection or mix style.`,
                variant: "destructive",
              });
            }
          }
        } else {
          // Show performance info for fast responses
          if (result.cacheHit) {
            console.log('PHASE2: Recommendations served from cache');
          } else if (result.processingTime < 500) {
            console.log(`PHASE2: Fast recommendation response: ${result.processingTime}ms`);
          }
        }

      } catch (error) {
        console.error("PHASE2: Error in advanced track recommendations:", error);

        // Enhanced fallback with performance monitoring
        try {
          const { performanceMonitor } = await import('./services/performanceMonitor');

          const fallbackTracks = await performanceMonitor.measureAsync(
            'recommendation-fallback',
            async () => {
              const { getLocalTrackRecommendations } = await import('./services/apiService');
              return getLocalTrackRecommendations(selectedPosition, effectiveTracks);
            }
          );

          setTrackRecommendations(fallbackTracks);

          toast({
            title: "Using fallback recommendations",
            description: "Advanced engine unavailable, using local matching",
            variant: "default",
          });
        } catch (fallbackError) {
          console.error("PHASE2: Fallback recommendations also failed:", fallbackError);
          setTrackRecommendations([]);

          toast({
            title: "Recommendation error",
            description: "Unable to generate recommendations. Please try again.",
            variant: "destructive",
          });
        }
      } finally {
        setIsLoadingRecommendations(false);
      }
    };

    fetchAdvancedRecommendations();
  }, [selectedBlockId, selectedPositionId, mixBlocks, effectiveTracks, selectedCollectionId, selectedSources, selectedFolderId, selectedMixStyleId]);

  // Function to handle saving mix to backend
  const onSaveMixConfirm = (formData: { title: string; description?: string; isPublic: boolean }) => {
    const tracksForPayload: Array<{ id: string; position: number }> = [];
    let currentPosition = 0;
    mixBlocks.forEach(block => {
      block.trackPositions.forEach(pos => {
        if (pos.trackId && pos.trackName) { // Ensure track is assigned
          tracksForPayload.push({
            id: pos.trackId,
            position: currentPosition++,
          });
        }
      });
    });

    if (tracksForPayload.length === 0) {
      toast({
        title: "Cannot save mix",
        description: "No tracks assigned to blocks.",
        variant: "destructive",
      });
      setIsSaveModalOpen(false);
      return;
    }

    const payload: MixCreateData = {
      title: formData.title,
      description: formData.description || "",
      isPublic: formData.isPublic,
      tracks: tracksForPayload,
    };

    saveMix(payload, {
      onSuccess: (response) => {
        toast({
          title: "Mix Saved!",
          description: `"${formData.title}" has been saved successfully.`,
        });
        setIsSaveModalOpen(false);
        if (onComplete) {
          onComplete(mixBlocks, mixBridges);
        }
      },
      onError: (error: Error) => {
        toast({
          title: "Error Saving Mix",
          description: error.message || "Could not save the mix. Please try again.",
          variant: "destructive",
        });
        setIsSaveModalOpen(false);
      },
    });
  };

  // Extract tracks from blocks and bridges in sequential order
  const extractTracksFromBlocks = (): any[] => {
    console.log('🎯 [Modular Generator] Extracting tracks from blocks and bridges');

    const extractedTracks: any[] = [];

    // Process blocks in order
    mixBlocks.forEach((block, blockIndex) => {
      console.log(`Processing block ${blockIndex + 1}: ${block.name} (${block.type})`);

      // Extract tracks from block positions
      block.trackPositions.forEach((position, posIndex) => {
        if (position.trackId && position.trackName) {
          // Find the full track data from effectiveTracks with robust ID comparison
          const fullTrack = effectiveTracks.find(t => {
            // Handle both string and number IDs
            const trackId = String(t.id);
            const positionTrackId = String(position.trackId);
            return trackId === positionTrackId;
          });

          if (fullTrack) {
            const extractedTrack = {
              // Use full track data as base
              ...fullTrack,
              // Override with position-specific data
              id: position.trackId,
              title: position.trackName,
              artist: position.artist || fullTrack.artist,
              bpm: position.bpm || fullTrack.bpm,
              key: fullTrack.key,
              energy: fullTrack.energy,
              duration: fullTrack.duration,
              // Add modular-specific metadata
              blockId: block.id,
              blockType: block.type,
              blockName: block.name,
              positionInBlock: posIndex,
              blockPosition: blockIndex,
              // Preserve file path for audio streaming
              file_path: fullTrack.file_path || fullTrack.filePath,
              directory_id: fullTrack.directory_id,
            };

            extractedTracks.push(extractedTrack);
            console.log(`✅ Extracted track from block ${block.name}: ${extractedTrack.title}`);
          } else {
            console.warn(`⚠️ Could not find full track data for ${position.trackId} in block ${block.name}`);
          }
        }
      });
    });

    // Process bridges (transition tracks between blocks)
    mixBridges.forEach((bridge, bridgeIndex) => {
      console.log(`Processing bridge ${bridgeIndex + 1}: ${bridge.type} (${bridge.fromBlockId} -> ${bridge.toBlockId})`);

      bridge.trackPositions.forEach((position, posIndex) => {
        if (position.trackId && position.trackName) {
          // Find the full track data from effectiveTracks with robust ID comparison
          const fullTrack = effectiveTracks.find(t => {
            // Handle both string and number IDs
            const trackId = String(t.id);
            const positionTrackId = String(position.trackId);
            return trackId === positionTrackId;
          });

          if (fullTrack) {
            const extractedTrack = {
              // Use full track data as base
              ...fullTrack,
              // Override with position-specific data
              id: position.trackId,
              title: position.trackName,
              artist: position.artist || fullTrack.artist,
              bpm: position.bpm || fullTrack.bpm,
              key: fullTrack.key,
              energy: fullTrack.energy,
              duration: fullTrack.duration,
              // Add bridge-specific metadata
              bridgeId: bridge.id,
              bridgeType: bridge.type,
              fromBlockId: bridge.fromBlockId,
              toBlockId: bridge.toBlockId,
              positionInBridge: posIndex,
              bridgePosition: bridgeIndex,
              // Preserve file path for audio streaming
              file_path: fullTrack.file_path || fullTrack.filePath,
              directory_id: fullTrack.directory_id,
            };

            extractedTracks.push(extractedTrack);
            console.log(`✅ Extracted track from bridge: ${extractedTrack.title}`);
          } else {
            console.warn(`⚠️ Could not find full track data for ${position.trackId} in bridge`);
          }
        }
      });
    });

    console.log(`🎯 [Modular Generator] Extracted ${extractedTracks.length} tracks total`);
    return extractedTracks;
  };

  // Transform tracks to timeline-compatible format (matching Smart Mix V2)
  const transformTracksForTimeline = async (tracks: any[]): Promise<any[]> => {
    console.log('🎯 [Modular Generator] Transforming tracks for timeline compatibility');

    if (tracks.length === 0) {
      console.warn('No tracks to transform');
      return [];
    }

    // Import required functions (same as Smart Mix V2)
    let getTrackColorsInTimeline;
    try {
      const trackTransitionColors = await import('../../mixes/timeline/utils/trackTransitionColors');
      getTrackColorsInTimeline = trackTransitionColors.getTrackColorsInTimeline;
      console.log('✅ [Modular Generator] Successfully imported color calculation functions');
    } catch (error) {
      console.error('❌ [Modular Generator] Failed to import required functions:', error);
      throw new Error('Failed to import track processing functions');
    }

    const transformedTracks: any[] = [];
    let currentStartPosition = 0;

    for (let index = 0; index < tracks.length; index++) {
      const track = tracks[index];

      // Calculate duration (ensure it's numeric)
      const duration = typeof track.duration === 'number' ? track.duration :
                      typeof track.length === 'number' ? track.length :
                      parseFloat(track.duration || track.length) || 180;

      // Generate audio URL (same format as Smart Mix V2)
      const cacheBuster = Date.now();
      const audioUrl = `/api/v1/audio/stream/${track.id}?_cb=${cacheBuster}`;

      // Create timeline-compatible track (matching Smart Mix V2 structure)
      const timelineTrack = {
        id: track.id, // Keep as is (can be string or number)
        title: track.title || 'Unknown Title',
        artist: track.artist || 'Unknown Artist',
        duration: duration,
        bpm: typeof track.bpm === 'number' ? track.bpm : parseFloat(track.bpm) || 120,
        key: track.key || '1A',
        energy: typeof track.energy === 'number' ? track.energy : parseFloat(track.energy) || 5,

        // CRITICAL: Include backend fields that timeline expects
        file_path: track.file_path || track.filePath,
        directory_id: track.directory_id,

        // Audio URL for streaming
        audioUrl: audioUrl,

        // Timeline positioning
        startPosition: currentStartPosition,

        // Optional metadata
        genre: track.genre,
        mixed_in_key_key: track.mixed_in_key_key,
        mixed_in_key_energy: track.mixed_in_key_energy,
        librosa_key: track.librosa_key,
        librosa_bpm: track.librosa_bpm,

        // Preserve modular-specific metadata
        blockId: track.blockId,
        blockType: track.blockType,
        blockName: track.blockName,
        bridgeId: track.bridgeId,
        bridgeType: track.bridgeType,
      };

      // Calculate harmonic colors (same as Smart Mix V2)
      try {
        // Validate track has a valid key for harmonic calculation
        if (!timelineTrack.key || timelineTrack.key === 'Unknown' || timelineTrack.key === '') {
          console.warn(`⚠️ [Modular Generator] Track ${index + 1} has invalid key "${timelineTrack.key}", using default key "1A"`);
          timelineTrack.key = '1A';
        }

        const tempTracksForColorCalc = [...transformedTracks, timelineTrack];
        const trackIndex = tempTracksForColorCalc.length - 1;
        const colors = getTrackColorsInTimeline(tempTracksForColorCalc, trackIndex);

        // Apply the calculated harmonic color
        timelineTrack.color = colors.waveColor;

        console.log(`🎨 [Modular Generator] Pre-calculated harmonic color for track ${index + 1}: ${colors.waveColor} (key: ${timelineTrack.key})`);
      } catch (colorError) {
        console.error(`❌ [Modular Generator] Failed to calculate harmonic color for track ${index + 1}:`, colorError);
        // Fallback to a default color
        timelineTrack.color = '#4f46e5'; // Indigo as fallback
        console.log(`🎨 [Modular Generator] Using fallback color ${timelineTrack.color} for track ${index + 1}`);
      }

      // Validate critical fields before adding
      if (!timelineTrack.id || !timelineTrack.title || !timelineTrack.audioUrl) {
        console.error(`❌ [Modular Generator] Track ${index + 1} failed validation:`, {
          hasId: !!timelineTrack.id,
          hasTitle: !!timelineTrack.title,
          hasAudioUrl: !!timelineTrack.audioUrl,
          track: timelineTrack
        });
        throw new Error(`Track ${index + 1} failed validation - missing critical fields`);
      }

      // Update start position for next track
      currentStartPosition += duration;

      // Add to transformed tracks
      transformedTracks.push(timelineTrack);

      console.log(`✅ [Modular Generator] Transformed track ${index + 1}/${tracks.length}: ${timelineTrack.title}`);
    }

    console.log(`🎯 [Modular Generator] Successfully transformed ${transformedTracks.length} tracks for timeline`);
    return transformedTracks;
  };

  // Handle completion - now extracts and transforms tracks
  const handleComplete = async () => {
    if (mixBlocks.length === 0) {
      toast({
        title: "Cannot save empty mix",
        description: "Please add some blocks to your mix.",
        variant: "destructive",
      });
      return;
    }

    // Check if we have any assigned tracks
    const hasAssignedTracks = mixBlocks.some(block =>
      block.trackPositions.some(pos => pos.trackId && pos.trackName)
    ) || mixBridges.some(bridge =>
      bridge.trackPositions.some(pos => pos.trackId && pos.trackName)
    );

    if (!hasAssignedTracks) {
      toast({
        title: "No tracks assigned",
        description: "Please assign tracks to your blocks before completing the mix.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Extract tracks from blocks and bridges
      const extractedTracks = extractTracksFromBlocks();

      if (extractedTracks.length === 0) {
        toast({
          title: "No tracks found",
          description: "Could not extract any tracks from the mix.",
          variant: "destructive",
        });
        return;
      }

      // Transform tracks for timeline compatibility
      const timelineTracks = await transformTracksForTimeline(extractedTracks);

      console.log(`🎯 [Modular Generator] Calling onComplete with ${timelineTracks.length} timeline-ready tracks`);

      // Call the completion handler with timeline-ready tracks
      if (onComplete) {
        onComplete(timelineTracks);
      }
    } catch (error) {
      console.error('❌ [Modular Generator] Error processing tracks for timeline:', error);
      toast({
        title: "Error processing tracks",
        description: "Failed to prepare tracks for timeline. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle save mix (separate from completion)
  const handleSaveMix = () => {
    if (mixBlocks.length === 0) {
      toast({
        title: "Cannot save empty mix",
        description: "Please add some blocks to your mix.",
        variant: "destructive",
      });
      return;
    }
    setIsSaveModalOpen(true);
  };

  // Handle populate all blocks
  const handlePopulateAll = async () => {
    if (!effectiveTracks.length) {
      toast({
        title: "No tracks available",
        description: "Please select a collection with tracks first.",
        variant: "destructive",
      });
      return;
    }

    if (!mixBlocks.length) {
      toast({
        title: "No blocks to populate",
        description: "Add some blocks to the mix first.",
        variant: "default",
      });
      return;
    }

    setIsPopulatingTracks(true);

    try {
      console.log('MIGRATION: Using backend auto-population for populate all');

      // Import enhanced services
      const { populateBlockEnhanced } = await import('./services/apiService');
      const { getBlockPopulationParams, processPopulatedBlockResponse } = await import('./utils/compatibilityUtils');

      // Always use backend API for proper harmonic rules
      const collectionForAPI = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);

      if (!collectionForAPI) {
        toast({
          title: "No collection selected",
          description: "Please select a collection first to use proper harmonic matching.",
          variant: "destructive",
        });
        return;
      }

      console.log('MIGRATION: Using backend auto-population for all blocks');

      // Populate all blocks using backend
      const updatedBlocks = await Promise.all(
        mixBlocks.map(async (block) => {
          const emptyPositions = block.trackPositions.filter(pos => !pos.trackId);
          if (emptyPositions.length === 0) return block; // Skip if already populated

          try {
            // Get population parameters
            const populationParams = getBlockPopulationParams(block, collectionForAPI, selectedFolderId, selectedMixStyleId);

            // Call backend API
            const populatedData = await populateBlockEnhanced(populationParams);

            // Process the response
            return processPopulatedBlockResponse(populatedData, block);
          } catch (error) {
            console.error(`Error populating block ${block.name}:`, error);
            return block; // Return original block if population fails
          }
        })
      );

      setMixBlocks(updatedBlocks);

      // Count populated positions
      const totalPositions = mixBlocks.reduce((sum, block) => sum + block.trackPositions.length, 0);
      const populatedPositions = updatedBlocks.reduce((sum, block) =>
        sum + block.trackPositions.filter(pos => pos.trackId).length, 0
      );

      toast({
        title: "All blocks populated",
        description: `Populated ${populatedPositions}/${totalPositions} positions across ${mixBlocks.length} blocks using backend compatibility scoring.`,
      });

    } catch (error) {
      console.error('Error populating all blocks:', error);
      toast({
        title: "Population failed",
        description: "Could not populate blocks. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsPopulatingTracks(false);
    }
  };

  // Handle populate single block (right-click context menu)
  const handlePopulateBlock = async (blockId: string) => {
    const block = mixBlocks.find(b => b.id === blockId);
    if (!block) return;

    if (!effectiveTracks.length) {
      toast({
        title: "No tracks available",
        description: "Please select a collection with tracks first.",
        variant: "destructive",
      });
      return;
    }

    setIsPopulatingTracks(true);

    try {
      console.log('MIGRATION: Using backend auto-population for right-click populate');

      // Import enhanced services with performance monitoring
      const { populateBlockEnhanced } = await import('./services/apiService');
      const { getBlockPopulationParams, processPopulatedBlockResponse } = await import('./utils/compatibilityUtils');

      // Always use backend API for proper harmonic rules
      const collectionForAPI = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);

      if (!collectionForAPI) {
        toast({
          title: "No collection selected",
          description: "Please select a collection first to use proper harmonic matching.",
          variant: "destructive",
        });
        return;
      }

      console.log('MIGRATION: Using backend auto-population for single block');

      // Get population parameters
      const populationParams = getBlockPopulationParams(block, collectionForAPI, selectedFolderId, selectedMixStyleId);

      // Call backend API
      const populatedData = await populateBlockEnhanced(populationParams);

      // Process the response
      const updatedBlock = processPopulatedBlockResponse(populatedData, block);

      // Update the block in state
      setMixBlocks(prev =>
        prev.map(b =>
          b.id === blockId ? updatedBlock : b
        )
      );

      const populatedCount = updatedBlock.trackPositions.filter(pos => pos.trackId).length;
      toast({
        title: "Block populated",
        description: `Populated ${populatedCount}/${updatedBlock.trackPositions.length} positions in ${block.name} using backend compatibility scoring.`,
      });

    } catch (error) {
      console.error('Error populating block:', error);
      toast({
        title: "Population failed",
        description: "Could not populate block. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsPopulatingTracks(false);
    }
  };

  // Handle duplicate block (right-click context menu)
  const handleDuplicateBlock = (blockId: string) => {
    const block = mixBlocks.find(b => b.id === blockId);
    if (!block) return;

    // Create a copy of the block with a new ID
    const duplicatedBlock = {
      ...block,
      id: `${block.id}-copy-${Date.now()}`,
      name: `${block.name} (Copy)`,
      trackPositions: block.trackPositions.map((pos) => ({
        ...pos,
        id: `${pos.id}-copy-${Date.now()}`,
      })),
    };

    // Add the duplicated block after the original
    const blockIndex = mixBlocks.findIndex(b => b.id === blockId);
    const newBlocks = [...mixBlocks];
    newBlocks.splice(blockIndex + 1, 0, duplicatedBlock);
    setMixBlocks(newBlocks);

    toast({
      title: "Block duplicated",
      description: `Created a copy of ${block.name}.`,
    });
  };

  return (
    <ThemeProvider>
      <QueryProvider>
        <AnimationProvider>
          <TransitionProvider>
            <UserPreferencesProvider>
              <AIProvider>
                <div className="w-full h-full bg-background flex flex-col">
                  {/* Top Bar */}
                  <ControlBar
                    setDuration={setDuration}
                    setSetDuration={setSetDuration}
                    selectedTemplate={selectedTemplate}
                    setSelectedTemplate={setSelectedTemplate}
                    loadTemplate={loadTemplate}
                    showEnergyCurve={showEnergyCurve}
                    setShowEnergyCurve={setShowEnergyCurve}
                    showHarmonicPath={showHarmonicPath}
                    setShowHarmonicPath={setShowHarmonicPath}
                    autoPopulate={autoPopulate}
                    setAutoPopulate={setAutoPopulate}
                    setShowTutorial={setShowTutorial}
                    onCancel={onCancel || (() => {})}
                    handleComplete={handleComplete}
                    handleSaveMix={handleSaveMix}
                    templates={templates}
                    collections={collections}
                    folders={folders}
                    selectedCollectionId={selectedCollectionId}
                    selectedFolderId={selectedFolderId}
                    setSelectedCollectionId={handleSelectedCollection}
                    setSelectedFolderId={handleSelectedFolder}
                    onMultipleSourcesSelected={handleMultipleSourcesSelected}
                    isLoadingCollections={isCollectionsLoading}
                    isLoadingFolders={isLoadingFolders}
                    isLoadingTracks={isLoadingTracks || isPopulatingTracks}
                    trackCount={effectiveTracks.length}
                    // MIGRATION: Mix Style Props - Step 1.1
                    mixStyles={mixStyles}
                    selectedMixStyleId={selectedMixStyleId}
                    onMixStyleSelect={handleMixStyleSelect}
                    isLoadingMixStyles={isLoadingMixStyles}
                    // Auto-populate functionality
                    onPopulateAll={handlePopulateAll}
                  />

                  <div className="flex-1 flex overflow-hidden">
                    <ResizablePanelGroup direction="horizontal">
                      {/* Left Panel - Block Library */}
                      <ResizablePanel defaultSize={20} minSize={15}>
                        <BlockLibrary
                          blockLibrary={blockLibrary}
                          bridgeLibrary={bridgeLibrary}
                          customBlocks={customBlocks}
                          searchTerm={searchTerm}
                          setSearchTerm={setSearchTerm}
                          filterType={filterType}
                          setFilterType={setFilterType}
                          filterDuration={filterDuration}
                          setFilterDuration={setFilterDuration}
                          filteredBlocks={filteredBlocks}
                          addBlockToMix={addBlockToMix}
                          setShowCreateCustomBlock={setShowCreateCustomBlock}
                        />
                      </ResizablePanel>

                      <ResizableHandle />

                      {/* Main Canvas */}
                      <ResizablePanel defaultSize={60} minSize={40}>
                        <div className="flex-1 flex flex-col overflow-hidden">
                          {/* Energy curve visualization */}
                          <EnergyCurveVisualization
                            mixBlocks={mixBlocks}
                            showEnergyCurve={showEnergyCurve}
                          />

                          {/* Harmonic path visualization */}
                          <HarmonicPathVisualization
                            mixBlocks={mixBlocks}
                            showHarmonicPath={showHarmonicPath}
                          />

                          <MixCanvas
                            mixBlocks={mixBlocks}
                            mixBridges={mixBridges}
                            templates={templates}
                            selectedBlockId={selectedBlockId}
                            selectedPositionId={selectedPositionId}
                            dragOverBlockId={dragOverBlockId}
                            viewMode={viewMode}
                            zoomLevel={zoomLevel}
                            canvasRef={canvasRef}
                            setSelectedBlockId={setSelectedBlockId}
                            setSelectedPositionId={setSelectedPositionId}
                            setSelectedTemplate={setSelectedTemplate}
                            loadTemplate={loadTemplate}
                            setZoomLevel={setZoomLevel}
                            setViewMode={setViewMode}
                            handleDragStart={handleDragStart}
                            handleDragOver={handleDragOver}
                            handleDrop={handleDrop}
                            handleDragEnd={handleDragEnd}
                            removeBlock={removeBlock}
                            onPopulateBlock={handlePopulateBlock}
                            onDuplicateBlock={handleDuplicateBlock}
                          />
                        </div>
                      </ResizablePanel>

                      <ResizableHandle />

                      {/* Right Panel - Details */}
                      <ResizablePanel defaultSize={20} minSize={15}>
                        <DetailsSidebar
                          selectedBlockId={selectedBlockId}
                          selectedPositionId={selectedPositionId}
                          mixBlocks={mixBlocks}
                          trackRecommendations={trackRecommendations}
                          isLoadingRecommendations={isLoadingRecommendations}
                          assignTrackToPosition={assignTrackToPosition}
                        />
                      </ResizablePanel>
                    </ResizablePanelGroup>
                  </div>

                  {/* Custom Block Dialog */}
                  {showCreateCustomBlock && (
                    <CustomBlockCreator
                      isOpen={showCreateCustomBlock}
                      onClose={() => setShowCreateCustomBlock(false)}
                      onSave={(block) => {
                        setCustomBlocks((prev) => [...prev, block]);
                        setShowCreateCustomBlock(false);
                        toast({
                          title: "Custom block created",
                          description: `Created custom block: ${block.name}`,
                        });
                      }}
                    />
                  )}

                  {/* Save Mix Modal */}
                  {isSaveModalOpen && (
                    <SaveMixModal
                      open={isSaveModalOpen}
                      onOpenChange={setIsSaveModalOpen}
                      tracks={mixBlocks.flatMap(block =>
                        block.trackPositions
                          .filter(pos => pos.trackId && pos.trackName)
                          .map(pos => ({
                            id: pos.trackId!,
                            title: pos.trackName!,
                            artist: pos.artist || "Unknown Artist",
                          }))
                      )}
                      onSave={onSaveMixConfirm}
                      isSaving={isSaving}
                    />
                  )}

                  {/* Toaster removed - using the global Toaster from App.tsx instead */}
                </div>
              </AIProvider>
            </UserPreferencesProvider>
          </TransitionProvider>
        </AnimationProvider>
      </QueryProvider>
    </ThemeProvider>
  );
};

export default ModularGeneratorRedesign;
