/**
 * Mix operations hook for the Modular Generator
 * Handles saving mixes, completion, and track extraction/transformation
 */

import { useToast } from "@/components/ui/use-toast";
import { useSaveMix } from '@/hooks/useMixes';
import { MixCreateData } from '@/components/mixes/list/SaveMixModal';
import { HarmonicBlock, TransitionBridge } from '../types';

export interface MixOperationsActions {
  handleComplete: () => Promise<void>;
  handleSaveMix: () => void;
  onSaveMixConfirm: (formData: { title: string; description?: string; isPublic: boolean }) => void;
  extractTracksFromBlocks: () => any[];
  transformTracksForTimeline: (tracks: any[]) => Promise<any[]>;
}

interface UseMixOperationsProps {
  mixBlocks: HarmonicBlock[];
  mixBridges: TransitionBridge[];
  effectiveTracks: any[];
  isSaveModalOpen: boolean;
  setIsSaveModalOpen: (open: boolean) => void;
  onComplete?: (tracks: any[]) => void;
}

export const useMixOperations = ({
  mixBlocks,
  mixBridges,
  effectiveTracks,
  isSaveModalOpen,
  setIsSaveModalOpen,
  onComplete,
}: UseMixOperationsProps): MixOperationsActions => {
  const { toast } = useToast();
  const { mutate: saveMix, isPending: isSaving } = useSaveMix();

  // Extract tracks from blocks and bridges in sequential order
  const extractTracksFromBlocks = (): any[] => {
    console.log('🎯 [Modular Generator] Extracting tracks from blocks and bridges');

    const extractedTracks: any[] = [];

    // Process blocks in order
    mixBlocks.forEach((block, blockIndex) => {
      console.log(`Processing block ${blockIndex + 1}: ${block.name} (${block.type})`);

      // Extract tracks from block positions
      block.trackPositions.forEach((position, posIndex) => {
        if (position.trackId && position.trackName) {
          // Find the full track data from effectiveTracks with robust ID comparison
          const fullTrack = effectiveTracks.find(t => {
            // Handle both string and number IDs
            const trackId = String(t.id);
            const positionTrackId = String(position.trackId);
            return trackId === positionTrackId;
          });

          if (fullTrack) {
            const extractedTrack = {
              // Use full track data as base
              ...fullTrack,
              // Override with position-specific data
              id: position.trackId,
              title: position.trackName,
              artist: position.trackArtist || fullTrack.artist,
              bpm: position.trackBpm || fullTrack.bpm,
              key: position.trackKey || fullTrack.key,
              energy: position.trackEnergy || fullTrack.energy,

              // Add modular-specific metadata
              blockId: block.id,
              blockType: block.type,
              blockName: block.name,
              positionId: position.id,
              positionIndex: posIndex,

              // Preserve file path for audio streaming
              file_path: fullTrack.file_path || fullTrack.filePath,
              directory_id: fullTrack.directory_id,
              folder_id: fullTrack.folder_id,
            };

            extractedTracks.push(extractedTrack);
            console.log(`  ✅ Added track ${posIndex + 1}: ${extractedTrack.title} (ID: ${extractedTrack.id})`);
          } else {
            console.warn(`  ⚠️ Track not found in effectiveTracks: ${position.trackId}`);
          }
        }
      });
    });

    // Process bridges (transition tracks between blocks)
    mixBridges.forEach((bridge, bridgeIndex) => {
      console.log(`Processing bridge ${bridgeIndex + 1}: ${bridge.type} (${bridge.fromBlockId} -> ${bridge.toBlockId})`);

      bridge.trackPositions?.forEach((position, posIndex) => {
        if (position.trackId && position.trackName) {
          // Find the full track data from effectiveTracks with robust ID comparison
          const fullTrack = effectiveTracks.find(t => {
            // Handle both string and number IDs
            const trackId = String(t.id);
            const positionTrackId = String(position.trackId);
            return trackId === positionTrackId;
          });

          if (fullTrack) {
            const extractedTrack = {
              // Use full track data as base
              ...fullTrack,
              // Override with position-specific data
              id: position.trackId,
              title: position.trackName,
              artist: position.trackArtist || fullTrack.artist,
              bpm: position.trackBpm || fullTrack.bpm,
              key: position.trackKey || fullTrack.key,
              energy: position.trackEnergy || fullTrack.energy,

              // Add bridge-specific metadata
              bridgeId: bridge.id,
              bridgeType: bridge.type,
              fromBlockId: bridge.fromBlockId,
              toBlockId: bridge.toBlockId,
              positionIndex: posIndex,

              // Preserve file path for audio streaming
              file_path: fullTrack.file_path || fullTrack.filePath,
              directory_id: fullTrack.directory_id,
              folder_id: fullTrack.folder_id,
            };

            extractedTracks.push(extractedTrack);
            console.log(`  ✅ Added bridge track ${posIndex + 1}: ${extractedTrack.title} (ID: ${extractedTrack.id})`);
          } else {
            console.warn(`  ⚠️ Bridge track not found in effectiveTracks: ${position.trackId}`);
          }
        }
      });
    });

    console.log(`🎯 [Modular Generator] Extracted ${extractedTracks.length} tracks total`);
    return extractedTracks;
  };

  // Transform tracks to timeline-compatible format (matching Smart Mix V2)
  const transformTracksForTimeline = async (tracks: any[]): Promise<any[]> => {
    console.log('🎯 [Modular Generator] Transforming tracks for timeline compatibility');

    if (tracks.length === 0) {
      console.warn('⚠️ [Modular Generator] No tracks to transform');
      return [];
    }

    // Import required functions (same as Smart Mix V2)
    let getTrackColorsInTimeline;
    try {
      const trackTransitionColors = await import('../../mixes/timeline/utils/trackTransitionColors');
      getTrackColorsInTimeline = trackTransitionColors.getTrackColorsInTimeline;
      console.log('✅ [Modular Generator] Successfully imported color calculation functions');
    } catch (error) {
      console.error('❌ [Modular Generator] Failed to import required functions:', error);
      throw new Error('Failed to import track processing functions');
    }

    const transformedTracks: any[] = [];
    let currentStartPosition = 0;

    for (let index = 0; index < tracks.length; index++) {
      const track = tracks[index];

      // Calculate duration (ensure it's numeric)
      const duration = typeof track.duration === 'number' ? track.duration :
                      typeof track.length === 'number' ? track.length :
                      parseFloat(track.duration || track.length) || 180;

      // Generate audio URL (same format as Smart Mix V2)
      const cacheBuster = Date.now();
      const audioUrl = `/api/v1/audio/stream/${track.id}?_cb=${cacheBuster}`;

      // Create timeline-compatible track (matching Smart Mix V2 structure)
      const timelineTrack = {
        id: track.id, // Keep as is (can be string or number)
        title: track.title || 'Unknown Title',
        artist: track.artist || 'Unknown Artist',
        bpm: track.bpm || 120,
        key: track.key || '1A',
        energy: track.energy || 5,
        duration: duration,

        // CRITICAL: Include backend fields that timeline expects
        file_path: track.file_path || track.filePath,
        directory_id: track.directory_id,
        folder_id: track.folder_id,

        // Audio URL for streaming
        audioUrl: audioUrl,

        // Timeline positioning
        startPosition: currentStartPosition,

        // Optional metadata
        genre: track.genre,
        mixed_in_key_key: track.mixed_in_key_key,
        danceability: track.danceability,
        valence: track.valence,
        acousticness: track.acousticness,

        // Preserve modular-specific metadata
        blockId: track.blockId,
        blockType: track.blockType,
        blockName: track.blockName,
        bridgeId: track.bridgeId,
        bridgeType: track.bridgeType,
        positionId: track.positionId,
      };

      // Calculate harmonic colors (same as Smart Mix V2)
      try {
        // Validate track has a valid key for harmonic calculation
        if (!timelineTrack.key || timelineTrack.key === 'Unknown' || timelineTrack.key === '') {
          console.warn(`⚠️ [Modular Generator] Track ${index + 1} has invalid key "${timelineTrack.key}", using default key "1A"`);
          timelineTrack.key = '1A';
        }

        const tempTracksForColorCalc = [...transformedTracks, timelineTrack];
        const trackIndex = tempTracksForColorCalc.length - 1;
        const colors = getTrackColorsInTimeline(tempTracksForColorCalc, trackIndex);

        // Apply the calculated harmonic color
        timelineTrack.color = colors.waveColor;

        console.log(`🎨 [Modular Generator] Applied harmonic color ${timelineTrack.color} to track ${index + 1} (key: ${timelineTrack.key})`);
      } catch (colorError) {
        console.error(`❌ [Modular Generator] Failed to calculate harmonic color for track ${index + 1}:`, colorError);
        // Fallback to a default color
        timelineTrack.color = '#4f46e5'; // Indigo as fallback
        console.log(`🎨 [Modular Generator] Using fallback color ${timelineTrack.color} for track ${index + 1}`);
      }

      // Validate critical fields before adding
      if (!timelineTrack.id || !timelineTrack.title || !timelineTrack.audioUrl) {
        console.error(`❌ [Modular Generator] Track ${index + 1} failed validation:`, {
          id: timelineTrack.id,
          title: timelineTrack.title,
          audioUrl: timelineTrack.audioUrl
        });
        continue; // Skip invalid tracks
      }

      // Update start position for next track
      currentStartPosition += duration;

      // Add to transformed tracks
      transformedTracks.push(timelineTrack);

      console.log(`✅ [Modular Generator] Transformed track ${index + 1}: ${timelineTrack.title} (${duration}s, starts at ${timelineTrack.startPosition}s)`);
    }

    console.log(`🎯 [Modular Generator] Successfully transformed ${transformedTracks.length}/${tracks.length} tracks for timeline`);
    return transformedTracks;
  };

  // Handle completion - now extracts and transforms tracks
  const handleComplete = async () => {
    if (mixBlocks.length === 0) {
      toast({
        title: "No blocks in mix",
        description: "Add some blocks to your mix before completing.",
        variant: "destructive",
      });
      return;
    }

    // Check if we have any assigned tracks
    const hasAssignedTracks = mixBlocks.some(block =>
      block.trackPositions.some(pos => pos.trackId && pos.trackName)
    ) || mixBridges.some(bridge =>
      bridge.trackPositions?.some(pos => pos.trackId && pos.trackName)
    );

    if (!hasAssignedTracks) {
      toast({
        title: "No tracks assigned",
        description: "Assign some tracks to your blocks before completing the mix.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Extract tracks from blocks and bridges
      const extractedTracks = extractTracksFromBlocks();

      if (extractedTracks.length === 0) {
        toast({
          title: "No tracks to export",
          description: "No valid tracks found in your mix blocks.",
          variant: "destructive",
        });
        return;
      }

      // Transform tracks for timeline compatibility
      const timelineTracks = await transformTracksForTimeline(extractedTracks);

      console.log(`🎯 [Modular Generator] Calling onComplete with ${timelineTracks.length} timeline-ready tracks`);

      // Call the completion handler with timeline-ready tracks
      if (onComplete) {
        onComplete(timelineTracks);
      }

    } catch (error) {
      console.error('❌ [Modular Generator] Error in handleComplete:', error);
      toast({
        title: "Export failed",
        description: "Failed to prepare tracks for timeline. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle save mix (separate from completion)
  const handleSaveMix = () => {
    if (mixBlocks.length === 0) {
      toast({
        title: "No blocks in mix",
        description: "Add some blocks to your mix before saving.",
        variant: "destructive",
      });
      return;
    }

    setIsSaveModalOpen(true);
  };

  // Function to handle saving mix to backend
  const onSaveMixConfirm = (formData: { title: string; description?: string; isPublic: boolean }) => {
    const tracksForPayload: Array<{ id: string; position: number }> = [];
    let currentPosition = 0;
    mixBlocks.forEach(block => {
      block.trackPositions.forEach(pos => {
        if (pos.trackId) {
          tracksForPayload.push({
            id: String(pos.trackId),
            position: currentPosition++
          });
        }
      });
    });

    // Add bridge tracks
    mixBridges.forEach(bridge => {
      bridge.trackPositions?.forEach(pos => {
        if (pos.trackId) {
          tracksForPayload.push({
            id: String(pos.trackId),
            position: currentPosition++
          });
        }
      });
    });

    const payload: MixCreateData = {
      title: formData.title,
      description: formData.description || "",
      is_public: formData.isPublic,
      tracks: tracksForPayload
    };

    saveMix(payload, {
      onSuccess: () => {
        toast({
          title: "Mix saved successfully",
          description: `"${formData.title}" has been saved to your mixes.`,
        });
        setIsSaveModalOpen(false);
      },
      onError: (error) => {
        console.error('Failed to save mix:', error);
        toast({
          title: "Failed to save mix",
          description: "There was an error saving your mix. Please try again.",
          variant: "destructive",
        });
      }
    });
  };

  return {
    handleComplete,
    handleSaveMix,
    onSaveMixConfirm,
    extractTracksFromBlocks,
    transformTracksForTimeline,
  };
};
