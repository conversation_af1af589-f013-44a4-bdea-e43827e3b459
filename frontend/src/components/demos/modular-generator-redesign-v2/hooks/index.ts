/**
 * Modular Generator Hooks Package
 * 
 * Exports all the modular hooks for the Modular Generator component.
 * This provides a clean interface for importing hooks and promotes reusability.
 */

// Export the main state management hook
export { useModularGeneratorState } from './useModularGeneratorState';
export type { ModularGeneratorState, ModularGeneratorActions, ModularGeneratorHookReturn } from './useModularGeneratorState';

// Export block management hook
export { useBlockManagement } from './useBlockManagement';
export type { BlockManagementActions } from './useBlockManagement';

// Export track management hook
export { useTrackManagement } from './useTrackManagement';
export type { TrackManagementActions } from './useTrackManagement';

// Export mix operations hook
export { useMixOperations } from './useMixOperations';
export type { MixOperationsActions } from './useMixOperations';

// Re-export the refactored main component
export { ModularGeneratorRedesignRefactored } from '../ModularGeneratorRedesignRefactored';
