/**
 * Track management hook for the Modular Generator
 * Handles track assignment, recommendations, and auto-population
 */

import { useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { HarmonicBlock, TrackWithScore } from '../types';

export interface TrackManagementActions {
  assignTrackToPosition: (trackId: string) => void;
  handlePopulateAll: () => Promise<void>;
  handlePopulateBlock: (blockId: string) => Promise<void>;
}

interface UseTrackManagementProps {
  mixBlocks: HarmonicBlock[];
  setMixBlocks: React.Dispatch<React.SetStateAction<HarmonicBlock[]>>;
  selectedBlockId: string | null;
  selectedPositionId: string | null;
  selectedCollectionId: string | null;
  selectedFolderId: string | null;
  selectedSources: string[] | null;
  selectedMixStyleId: string;
  effectiveTracks: any[];
  trackRecommendations: TrackWithScore[];
  setTrackRecommendations: React.Dispatch<React.SetStateAction<TrackWithScore[]>>;
  isLoadingRecommendations: boolean;
  setIsLoadingRecommendations: (loading: boolean) => void;
}

export const useTrackManagement = ({
  mixBlocks,
  setMixBlocks,
  selectedBlockId,
  selectedPositionId,
  selectedCollectionId,
  selectedFolderId,
  selectedSources,
  selectedMixStyleId,
  effectiveTracks,
  trackRecommendations,
  setTrackRecommendations,
  isLoadingRecommendations,
  setIsLoadingRecommendations,
}: UseTrackManagementProps): TrackManagementActions => {
  const { toast } = useToast();

  // Assign track to position
  const assignTrackToPosition = (trackId: string) => {
    if (!selectedBlockId || !selectedPositionId) return;

    // Find the track - use effective tracks (V1 logic) with robust ID comparison
    const tracksToSearch = effectiveTracks;
    const track = tracksToSearch.find((t) => {
      // Handle both string and number IDs
      const tId = String(t.id);
      const searchId = String(trackId);
      return tId === searchId;
    });

    if (!track) return;

    // Update the block
    setMixBlocks((prev) =>
      prev.map((block) =>
        block.id === selectedBlockId
          ? {
              ...block,
              trackPositions: block.trackPositions.map((pos) =>
                pos.id === selectedPositionId
                  ? {
                      ...pos,
                      trackId: track.id,
                      trackName: track.title || track.name,
                      trackArtist: track.artist,
                      trackBpm: track.bpm,
                      trackKey: track.key,
                      trackEnergy: track.energy,
                    }
                  : pos
              ),
            }
          : block
      )
    );

    toast({
      title: "Track assigned",
      description: `Assigned "${track.title || track.name}" to the selected position.`,
    });
  };

  // PHASE 2: Advanced track recommendations with performance monitoring and caching
  // Enhanced from V1 with sophisticated algorithms and optimization
  useEffect(() => {
    if (!selectedBlockId || !selectedPositionId) {
      setTrackRecommendations([]);
      return;
    }

    const selectedBlock = mixBlocks.find((b) => b.id === selectedBlockId);
    const selectedPosition = selectedBlock?.trackPositions.find(
      (p) => p.id === selectedPositionId
    );

    if (!selectedPosition) {
      setTrackRecommendations([]);
      return;
    }

    // PHASE 2: Advanced recommendation logic with performance monitoring
    const fetchAdvancedRecommendations = async () => {
      setIsLoadingRecommendations(true);

      try {
        console.log('PHASE2: Fetching advanced track recommendations with performance monitoring');

        // Import advanced recommendation engine and performance monitor
        const { recommendationEngine } = await import('../services/recommendationEngine');
        const { performanceMonitor } = await import('../services/performanceMonitor');

        // Use performance monitoring
        const result = await performanceMonitor.measureAsync(
          'recommendation-fetch',
          async () => {
            const collectionForRecommendations = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);
            return await recommendationEngine.getRecommendations({
              position: selectedPosition,
              block: selectedBlock,
              collectionId: collectionForRecommendations,
              folderId: selectedFolderId,
              mixStyleId: selectedMixStyleId,
              availableTracks: effectiveTracks,
              context: {
                allBlocks: mixBlocks,
                selectedBlockIndex: mixBlocks.findIndex(b => b.id === selectedBlockId),
                totalBlocks: mixBlocks.length
              }
            });
          },
          {
            logResults: true,
            warnThreshold: 1000 // 1 second
          }
        );

        // Update state with advanced recommendations
        setTrackRecommendations(result.tracks);

        // Provide user feedback based on results
        if (result.tracks.length === 0) {
          if (effectiveTracks.length === 0) {
            toast({
              title: "No tracks available",
              description: "Please select a collection or folder to get track recommendations.",
              variant: "destructive",
            });
          } else {
            const tracksWithKeys = effectiveTracks.filter(t => t.key).length;
            if (tracksWithKeys === 0) {
              toast({
                title: "Limited recommendations",
                description: "No tracks with key information found. Consider analyzing your tracks for better recommendations.",
                variant: "destructive",
              });
            } else {
              console.log('PHASE2: No recommendations found despite having tracks with keys');
            }
          }
        } else {
          // Show performance info for fast responses
          if (result.cacheHit) {
            console.log('PHASE2: Recommendations served from cache');
          }
        }

      } catch (error) {
        console.error('PHASE2: Advanced recommendation failed, using enhanced fallback:', error);

        // Enhanced fallback with performance monitoring
        try {
          const { performanceMonitor } = await import('../services/performanceMonitor');

          const fallbackTracks = await performanceMonitor.measureAsync(
            'recommendation-fallback',
            async () => {
              const { getLocalTrackRecommendations } = await import('../services/apiService');
              return getLocalTrackRecommendations(selectedPosition, effectiveTracks);
            }
          );

          setTrackRecommendations(fallbackTracks);

          if (fallbackTracks.length === 0) {
            toast({
              title: "No recommendations",
              description: "No suitable tracks found for this position.",
            });
          }

        } catch (fallbackError) {
          console.error('PHASE2: Fallback recommendation also failed:', fallbackError);
          setTrackRecommendations([]);
          toast({
            title: "Recommendation error",
            description: "Unable to get track recommendations. Please try again.",
            variant: "destructive",
          });
        }
      } finally {
        setIsLoadingRecommendations(false);
      }
    };

    fetchAdvancedRecommendations();
  }, [selectedBlockId, selectedPositionId, mixBlocks, effectiveTracks, selectedCollectionId, selectedFolderId, selectedSources, selectedMixStyleId]);

  // Handle populate all blocks
  const handlePopulateAll = async () => {
    if (!effectiveTracks.length) {
      toast({
        title: "No tracks available",
        description: "Please select a collection or folder first.",
        variant: "destructive",
      });
      return;
    }

    if (mixBlocks.length === 0) {
      toast({
        title: "No blocks to populate",
        description: "Add some blocks to your mix first.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Populating all blocks...",
      description: "This may take a moment for large collections.",
    });

    try {
      // Import enhanced services
      const { populateBlockEnhanced } = await import('../services/apiService');
      const { getBlockPopulationParams, processPopulatedBlockResponse } = await import('../utils/compatibilityUtils');

      // Always use backend API for proper harmonic rules
      const collectionForAPI = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);

      if (!collectionForAPI) {
        toast({
          title: "Collection required",
          description: "Please select a collection to use advanced population features.",
          variant: "destructive",
        });
        return;
      }

      // Populate all blocks using backend
      const updatedBlocks = await Promise.all(
        mixBlocks.map(async (block) => {
          const emptyPositions = block.trackPositions.filter(pos => !pos.trackId);
          if (emptyPositions.length === 0) return block; // Skip if already populated

          try {
            // Get population parameters
            const populationParams = getBlockPopulationParams(block, collectionForAPI, selectedFolderId, selectedMixStyleId);

            // Call backend API
            const populatedData = await populateBlockEnhanced(populationParams);

            // Process the response
            return processPopulatedBlockResponse(populatedData, block);
          } catch (error) {
            console.error(`Failed to populate block ${block.id}:`, error);
            return block; // Return original block if population fails
          }
        })
      );

      // Update all blocks
      setMixBlocks(updatedBlocks);

      // Count populated positions
      const totalPositions = mixBlocks.reduce((sum, block) => sum + block.trackPositions.length, 0);
      const populatedPositions = updatedBlocks.reduce((sum, block) =>
        sum + block.trackPositions.filter(pos => pos.trackId).length, 0
      );

      toast({
        title: "All blocks populated",
        description: `Populated ${populatedPositions}/${totalPositions} positions across ${mixBlocks.length} blocks`,
      });

    } catch (error) {
      console.error('Failed to populate all blocks:', error);
      toast({
        title: "Population failed",
        description: "Could not populate all blocks. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle populate single block (right-click context menu)
  const handlePopulateBlock = async (blockId: string) => {
    const block = mixBlocks.find(b => b.id === blockId);
    if (!block) return;

    const emptyPositions = block.trackPositions.filter(pos => !pos.trackId);
    if (emptyPositions.length === 0) {
      toast({
        title: "Block already populated",
        description: "This block has no empty positions to fill.",
      });
      return;
    }

    try {
      // Import enhanced services with performance monitoring
      const { populateBlockEnhanced } = await import('../services/apiService');
      const { getBlockPopulationParams, processPopulatedBlockResponse } = await import('../utils/compatibilityUtils');

      // Always use backend API for proper harmonic rules
      const collectionForAPI = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);

      if (!collectionForAPI) {
        toast({
          title: "Collection required",
          description: "Please select a collection to use advanced population features.",
          variant: "destructive",
        });
        return;
      }

      // Get population parameters
      const populationParams = getBlockPopulationParams(block, collectionForAPI, selectedFolderId, selectedMixStyleId);

      // Call backend API
      const populatedData = await populateBlockEnhanced(populationParams);

      // Process the response
      const updatedBlock = processPopulatedBlockResponse(populatedData, block);

      // Update the block in state
      setMixBlocks(prev =>
        prev.map(b =>
          b.id === blockId ? updatedBlock : b
        )
      );

      const populatedCount = updatedBlock.trackPositions.filter(pos => pos.trackId).length;
      toast({
        title: "Block populated",
        description: `Populated ${populatedCount}/${updatedBlock.trackPositions.length} positions`,
      });

    } catch (error) {
      console.error('Failed to populate block:', error);
      toast({
        title: "Population failed",
        description: "Could not populate the block. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    assignTrackToPosition,
    handlePopulateAll,
    handlePopulateBlock,
  };
};
