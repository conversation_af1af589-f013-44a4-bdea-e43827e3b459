/**
 * Block management hook for the Modular Generator
 * Handles adding, removing, duplicating, and organizing blocks
 */

import { useToast } from "@/components/ui/use-toast";
import { HarmonicBlock, TransitionBridge } from '../types';
import { blockLibrary } from "../data/blockLibrary";
import { bridgeLibrary } from "../data/bridgeLibrary";
import { templates } from "../data/templates";

export interface BlockManagementActions {
  addBlockToMix: (blockId: string) => Promise<void>;
  removeBlock: (blockId: string) => void;
  handleDuplicateBlock: (blockId: string) => void;
  loadTemplate: (templateId: string) => Promise<void>;
  handleDragStart: (e: React.DragEvent<HTMLDivElement>, blockId: string) => void;
  handleDragOver: (e: React.DragEvent<HTMLDivElement>, blockId: string) => void;
  handleDrop: (e: React.DragEvent<HTMLDivElement>, targetBlockId: string) => void;
  handleDragEnd: () => void;
}

interface UseBlockManagementProps {
  mixBlocks: HarmonicBlock[];
  setMixBlocks: React.Dispatch<React.SetStateAction<HarmonicBlock[]>>;
  mixBridges: TransitionBridge[];
  setMixBridges: React.Dispatch<React.SetStateAction<TransitionBridge[]>>;
  customBlocks: HarmonicBlock[];
  selectedBlockId: string | null;
  setSelectedBlockId: (id: string | null) => void;
  setDragOverBlockId: (id: string | null) => void;
  setIsPopulatingTracks: (populating: boolean) => void;
  autoPopulate: boolean;
  selectedCollectionId: string | null;
  selectedFolderId: string | null;
  selectedSources: string[] | null;
  selectedMixStyleId: string;
  effectiveTracks: any[];
}

export const useBlockManagement = ({
  mixBlocks,
  setMixBlocks,
  mixBridges,
  setMixBridges,
  customBlocks,
  selectedBlockId,
  setSelectedBlockId,
  setDragOverBlockId,
  setIsPopulatingTracks,
  autoPopulate,
  selectedCollectionId,
  selectedFolderId,
  selectedSources,
  selectedMixStyleId,
  effectiveTracks,
}: UseBlockManagementProps): BlockManagementActions => {
  const { toast } = useToast();

  // Load template
  const loadTemplate = async (templateId: string) => {
    if (templateId === "from-scratch") {
      setMixBlocks([]);
      setMixBridges([]);
      return;
    }

    // Find template
    const template = templates.find((t) => t.id === templateId);
    if (!template) {
      toast({
        title: "Template not found",
        description: `Template with ID ${templateId} was not found.`,
        variant: "destructive",
      });
      return;
    }

    // Load blocks from template
    const blocks: HarmonicBlock[] = [];
    const bridges: TransitionBridge[] = [];

    // In a real implementation, this would load from an API
    // For now, we'll just use the template's block IDs to find blocks in the library
    for (const blockId of template.blocks) {
      const block = blockLibrary.find((b) => b.id === blockId);
      if (block) {
        blocks.push({ ...block });
      }
    }

    // Add bridges between blocks
    for (let i = 0; i < blocks.length - 1; i++) {
      const fromBlock = blocks[i];
      const toBlock = blocks[i + 1];

      // Find a compatible bridge in the bridge library
      const bridge = bridgeLibrary.find(
        (b) => b.fromBlockId === fromBlock.id && b.toBlockId === toBlock.id
      );

      if (bridge) {
        bridges.push({ ...bridge });
      }
    }

    setMixBlocks(blocks);
    setMixBridges(bridges);

    toast({
      title: "Template loaded",
      description: `Loaded template "${template.name}" with ${blocks.length} blocks.`,
    });
  };

  // Add block to mix
  const addBlockToMix = async (blockId: string) => {
    const block = [...blockLibrary, ...customBlocks].find((b) => b.id === blockId);
    if (!block) return;

    // Create a copy of the block with a new ID to avoid conflicts
    const newBlock = {
      ...block,
      id: `${block.id}-${Date.now()}`,
      trackPositions: block.trackPositions.map((pos) => ({
        ...pos,
        trackId: null,
        trackName: null,
      })),
    };

    // Add block to mix
    setMixBlocks((prev) => [...prev, newBlock]);

    // If there are other blocks, try to add a bridge
    if (mixBlocks.length > 0) {
      const lastBlock = mixBlocks[mixBlocks.length - 1];

      // Find a compatible bridge in the bridge library
      const bridge = bridgeLibrary.find(
        (b) => b.fromBlockId === lastBlock.id.split("-")[0] && b.toBlockId === block.id
      );

      if (bridge) {
        // Create a copy of the bridge with updated IDs
        const newBridge = {
          ...bridge,
          id: `${bridge.id}-${Date.now()}`,
          fromBlockId: lastBlock.id,
          toBlockId: newBlock.id,
        };

        setMixBridges((prev) => [...prev, newBridge]);
      }
    }

    toast({
      title: "Block added",
      description: `Added "${block.name}" to your mix.`,
    });

    // PHASE 2: Advanced auto-population with performance monitoring and optimization
    // Enhanced from V1 with sophisticated monitoring and caching
    if (autoPopulate && (selectedCollectionId || (selectedSources && selectedSources.length > 0))) {
      toast({
        title: "Auto-populating block...",
        description: "Finding the best tracks for this block",
      });

      try {
        setIsPopulatingTracks(true);

        // Import enhanced services with performance monitoring
        const { populateBlockEnhanced, shouldUseBackendAPI } = await import('../services/apiService');
        const { getBlockPopulationParams, processPopulatedBlockResponse } = await import('../utils/compatibilityUtils');
        const { performanceMonitor } = await import('../services/performanceMonitor');

        // Check if we should use backend API
        const collectionForAPI = selectedCollectionId || (selectedSources && selectedSources.length > 0 ? selectedSources[0] : null);
        if (shouldUseBackendAPI(collectionForAPI, selectedMixStyleId)) {
          try {
            // Get population parameters
            const populationParams = getBlockPopulationParams(
              newBlock,
              collectionForAPI,
              selectedFolderId,
              selectedMixStyleId
            );

            // Call backend populate block API with performance monitoring
            const { populatedData, updatedBlock } = await performanceMonitor.measureAsync(
              'autoPopulation-backend',
              async () => {
                const populatedData = await populateBlockEnhanced(populationParams);
                const updatedBlock = processPopulatedBlockResponse(populatedData, newBlock);
                return { populatedData, updatedBlock };
              },
              {
                logResults: true,
                warnThreshold: 2000 // 2 seconds
              }
            );

            // Update the block in state
            setMixBlocks(prev =>
              prev.map(b =>
                b.id === newBlock.id ? updatedBlock : b
              )
            );

            // Count populated positions for user feedback
            const populatedCount = updatedBlock.trackPositions.filter(pos => pos.trackId).length;
            const populationRate = (populatedCount / updatedBlock.trackPositions.length) * 100;

            toast({
              title: "Block auto-populated",
              description: `Populated ${populatedCount}/${updatedBlock.trackPositions.length} positions (${populationRate.toFixed(0)}%) using backend API`,
            });

          } catch (backendError) {
            console.error('Backend auto-population failed, falling back to frontend:', backendError);
            
            // Fallback to frontend auto-population
            const tracksToUse = effectiveTracks;
            if (tracksToUse.length > 0) {
              const { autoPopulateBlockPositions } = await import('../utils');
              const updatedPositions = autoPopulateBlockPositions(newBlock.trackPositions, tracksToUse);

              setMixBlocks(prev =>
                prev.map(b =>
                  b.id === newBlock.id ? { ...b, trackPositions: updatedPositions } : b
                )
              );

              const populatedCount = updatedPositions.filter(pos => pos.trackId).length;
              toast({
                title: "Block auto-populated (fallback)",
                description: `Populated ${populatedCount}/${updatedPositions.length} positions using frontend logic`,
              });
            }
          }
        } else {
          // Use frontend auto-population when no valid collection/mix style
          console.log('MIGRATION: Using frontend auto-population (no valid collection/mix style)');
          const tracksToUse = effectiveTracks;

          if (tracksToUse.length > 0) {
            const { autoPopulateBlockPositions } = await import('../utils');
            const updatedPositions = autoPopulateBlockPositions(newBlock.trackPositions, tracksToUse);

            setMixBlocks(prev =>
              prev.map(b =>
                b.id === newBlock.id ? { ...b, trackPositions: updatedPositions } : b
              )
            );

            const populatedCount = updatedPositions.filter(pos => pos.trackId).length;
            toast({
              title: "Block auto-populated",
              description: `Populated ${populatedCount}/${updatedPositions.length} positions`,
            });
          }
        }
      } catch (error) {
        console.error('Auto-population failed:', error);
        toast({
          title: "Auto-population failed",
          description: "Could not auto-populate the block. You can manually assign tracks.",
          variant: "destructive",
        });
      } finally {
        setIsPopulatingTracks(false);
      }
    }
  };

  // Remove block from mix
  const removeBlock = (blockId: string) => {
    // Remove block
    setMixBlocks((prev) => prev.filter((b) => b.id !== blockId));

    // Remove bridges connected to this block
    setMixBridges((prev) =>
      prev.filter((b) => b.fromBlockId !== blockId && b.toBlockId !== blockId)
    );

    // Clear selection if the selected block was removed
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, blockId: string) => {
    e.dataTransfer.setData("blockId", blockId);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, blockId: string) => {
    e.preventDefault();
    setDragOverBlockId(blockId);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetBlockId: string) => {
    e.preventDefault();
    const sourceBlockId = e.dataTransfer.getData("blockId");

    // Find the source and target blocks
    const sourceIndex = mixBlocks.findIndex((b) => b.id === sourceBlockId);
    const targetIndex = mixBlocks.findIndex((b) => b.id === targetBlockId);

    if (sourceIndex === -1 || targetIndex === -1 || sourceIndex === targetIndex) {
      return;
    }

    // Reorder blocks
    const newBlocks = [...mixBlocks];
    const [movedBlock] = newBlocks.splice(sourceIndex, 1);
    newBlocks.splice(targetIndex, 0, movedBlock);

    setMixBlocks(newBlocks);

    // Update bridges (in a real implementation, this would be more complex)
    // For now, we'll just clear bridges and add new ones between adjacent blocks
    const newBridges: TransitionBridge[] = [];

    for (let i = 0; i < newBlocks.length - 1; i++) {
      const fromBlock = newBlocks[i];
      const toBlock = newBlocks[i + 1];

      // Find a compatible bridge in the bridge library
      const bridge = bridgeLibrary.find(
        (b) =>
          b.fromBlockId === fromBlock.id.split("-")[0] &&
          b.toBlockId === toBlock.id.split("-")[0]
      );

      if (bridge) {
        // Create a copy of the bridge with updated IDs
        const newBridge = {
          ...bridge,
          id: `${bridge.id}-${Date.now()}`,
          fromBlockId: fromBlock.id,
          toBlockId: toBlock.id,
        };

        newBridges.push(newBridge);
      }
    }

    setMixBridges(newBridges);
  };

  const handleDragEnd = () => {
    setDragOverBlockId(null);
  };

  // Handle duplicate block (right-click context menu)
  const handleDuplicateBlock = (blockId: string) => {
    const block = mixBlocks.find(b => b.id === blockId);
    if (!block) return;

    // Create a copy of the block with a new ID
    const duplicatedBlock = {
      ...block,
      id: `${block.id}-copy-${Date.now()}`,
      trackPositions: block.trackPositions.map(pos => ({
        ...pos,
        // Keep track assignments in the duplicate
      })),
    };

    // Add the duplicated block after the original
    const blockIndex = mixBlocks.findIndex(b => b.id === blockId);
    const newBlocks = [...mixBlocks];
    newBlocks.splice(blockIndex + 1, 0, duplicatedBlock);
    setMixBlocks(newBlocks);

    toast({
      title: "Block duplicated",
      description: `Created a copy of "${block.name}"`,
    });
  };

  return {
    addBlockToMix,
    removeBlock,
    handleDuplicateBlock,
    loadTemplate,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleDragEnd,
  };
};
