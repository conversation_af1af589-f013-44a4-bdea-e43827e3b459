/**
 * State management hook for the Modular Generator
 * Centralizes all state logic and provides clean interface
 */

import React, { useState, useEffect } from 'react';
import { useCollections } from '@/hooks/useCollections';
import { useMixStyles } from '@/hooks/useMixStyles';
import { useEnhancedCollectionData } from '../../../mixes/generators/smart-v2/hooks/useEnhancedCollectionData';
import { HarmonicBlock, TransitionBridge, TrackWithScore } from '../types';

export interface ModularGeneratorState {
  // Main configuration state
  setDuration: number;
  selectedTemplate: string;
  showEnergyCurve: boolean;
  showHarmonicPath: boolean;
  autoPopulate: boolean;
  zoomLevel: number;
  viewMode: "horizontal" | "vertical";
  showTutorial: boolean;
  showCreateCustomBlock: boolean;

  // Mix composition state
  mixBlocks: HarmonicBlock[];
  mixBridges: TransitionBridge[];
  customBlocks: HarmonicBlock[];
  selectedBlockId: string | null;
  selectedPositionId: string | null;
  dragOverBlockId: string | null;

  // Block library state
  searchTerm: string;
  filterType: string;
  filterDuration: string;

  // Track recommendations state
  trackRecommendations: TrackWithScore[];
  isLoadingRecommendations: boolean;
  isPopulatingTracks: boolean;

  // Collection and mix style state
  selectedMixStyleId: string;
  collections: any[];

  // Save modal state
  isSaveModalOpen: boolean;
}

export interface ModularGeneratorActions {
  // Configuration setters
  setSetDuration: (duration: number) => void;
  setSelectedTemplate: (template: string) => void;
  setShowEnergyCurve: (show: boolean) => void;
  setShowHarmonicPath: (show: boolean) => void;
  setAutoPopulate: (auto: boolean) => void;
  setZoomLevel: (zoom: number) => void;
  setViewMode: (mode: "horizontal" | "vertical") => void;
  setShowTutorial: (show: boolean) => void;
  setShowCreateCustomBlock: (show: boolean) => void;

  // Mix composition setters
  setMixBlocks: React.Dispatch<React.SetStateAction<HarmonicBlock[]>>;
  setMixBridges: React.Dispatch<React.SetStateAction<TransitionBridge[]>>;
  setCustomBlocks: React.Dispatch<React.SetStateAction<HarmonicBlock[]>>;
  setSelectedBlockId: (id: string | null) => void;
  setSelectedPositionId: (id: string | null) => void;
  setDragOverBlockId: (id: string | null) => void;

  // Block library setters
  setSearchTerm: (term: string) => void;
  setFilterType: (type: string) => void;
  setFilterDuration: (duration: string) => void;

  // Track recommendations setters
  setTrackRecommendations: React.Dispatch<React.SetStateAction<TrackWithScore[]>>;
  setIsLoadingRecommendations: (loading: boolean) => void;
  setIsPopulatingTracks: (populating: boolean) => void;

  // Collection and mix style setters
  setSelectedMixStyleId: (id: string) => void;
  setCollections: React.Dispatch<React.SetStateAction<any[]>>;

  // Save modal setters
  setIsSaveModalOpen: (open: boolean) => void;
}

export interface ModularGeneratorHookReturn extends ModularGeneratorState, ModularGeneratorActions {
  // External data
  apiCollections: any[];
  isCollectionsLoading: boolean;
  mixStyles: any[];
  isLoadingMixStyles: boolean;
  
  // Enhanced collection data
  selectedCollectionId: string | null;
  selectedFolderId: string | null;
  selectedSources: string[] | null;
  folderTracks: any[];
  filteredTracks: any[];
  folders: any[];
  isLoadingFolders: boolean;
  isLoadingTracks: boolean;
  handleCollectionSelectEnhanced: (collectionId: string) => void;
  handleFolderSelectEnhanced: (folderId: string) => void;
  handleSourcesSelectEnhanced: (sources: string[]) => void;

  // Computed values
  effectiveTracks: any[];
}

export const useModularGeneratorState = (): ModularGeneratorHookReturn => {
  // Main configuration state
  const [setDuration, setSetDuration] = useState<number>(60); // in minutes
  const [selectedTemplate, setSelectedTemplate] = useState<string>("from-scratch");
  const [showEnergyCurve, setShowEnergyCurve] = useState<boolean>(true);
  const [showHarmonicPath, setShowHarmonicPath] = useState<boolean>(true);
  const [autoPopulate, setAutoPopulate] = useState<boolean>(true);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [viewMode, setViewMode] = useState<"horizontal" | "vertical">("horizontal");
  const [showTutorial, setShowTutorial] = useState<boolean>(false);
  const [showCreateCustomBlock, setShowCreateCustomBlock] = useState<boolean>(false);

  // Mix composition state
  const [mixBlocks, setMixBlocks] = useState<HarmonicBlock[]>([]);
  const [mixBridges, setMixBridges] = useState<TransitionBridge[]>([]);
  const [customBlocks, setCustomBlocks] = useState<HarmonicBlock[]>([]);
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
  const [selectedPositionId, setSelectedPositionId] = useState<string | null>(null);
  const [dragOverBlockId, setDragOverBlockId] = useState<string | null>(null);

  // Block library state
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterDuration, setFilterDuration] = useState<string>("all");

  // Track recommendations state
  const [trackRecommendations, setTrackRecommendations] = useState<TrackWithScore[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState<boolean>(false);
  const [isPopulatingTracks, setIsPopulatingTracks] = useState<boolean>(false);

  // Save modal state
  const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);

  // Collection state from API
  const { collections: apiCollections, isLoading: isCollectionsLoading } = useCollections();

  // Enhanced collection data with multiple source support
  const {
    selectedCollectionId,
    selectedFolderId,
    selectedSources,
    folderTracks,
    filteredTracks,
    folders,
    isLoadingFolders,
    isLoadingTracks,
    handleCollectionSelectEnhanced,
    handleFolderSelectEnhanced,
    handleSourcesSelectEnhanced,
  } = useEnhancedCollectionData();

  // Legacy state for backward compatibility
  const [collections, setCollections] = useState<any[]>([]);

  // Mix Style State Integration
  const {
    mixStyles,
    isLoading: isLoadingMixStyles,
  } = useMixStyles();

  const [selectedMixStyleId, setSelectedMixStyleId] = useState<string>("none");

  // Update collections from API when they change
  useEffect(() => {
    if (apiCollections) {
      setCollections(apiCollections);
    }
  }, [apiCollections]);

  // Effective tracks logic - REAL BACKEND DATA ONLY
  const effectiveTracks = React.useMemo(() => {
    // Use enhanced collection data tracks
    console.log(`MIGRATION: Using ${filteredTracks.length} tracks from enhanced collection data (no fallbacks)`);
    return filteredTracks;
  }, [filteredTracks]);

  return {
    // State values
    setDuration,
    selectedTemplate,
    showEnergyCurve,
    showHarmonicPath,
    autoPopulate,
    zoomLevel,
    viewMode,
    showTutorial,
    showCreateCustomBlock,
    mixBlocks,
    mixBridges,
    customBlocks,
    selectedBlockId,
    selectedPositionId,
    dragOverBlockId,
    searchTerm,
    filterType,
    filterDuration,
    trackRecommendations,
    isLoadingRecommendations,
    isPopulatingTracks,
    selectedMixStyleId,
    collections,
    isSaveModalOpen,

    // State setters
    setSetDuration,
    setSelectedTemplate,
    setShowEnergyCurve,
    setShowHarmonicPath,
    setAutoPopulate,
    setZoomLevel,
    setViewMode,
    setShowTutorial,
    setShowCreateCustomBlock,
    setMixBlocks,
    setMixBridges,
    setCustomBlocks,
    setSelectedBlockId,
    setSelectedPositionId,
    setDragOverBlockId,
    setSearchTerm,
    setFilterType,
    setFilterDuration,
    setTrackRecommendations,
    setIsLoadingRecommendations,
    setIsPopulatingTracks,
    setSelectedMixStyleId,
    setCollections,
    setIsSaveModalOpen,

    // External data
    apiCollections,
    isCollectionsLoading,
    mixStyles,
    isLoadingMixStyles,

    // Enhanced collection data
    selectedCollectionId,
    selectedFolderId,
    selectedSources,
    folderTracks,
    filteredTracks,
    folders,
    isLoadingFolders,
    isLoadingTracks,
    handleCollectionSelectEnhanced,
    handleFolderSelectEnhanced,
    handleSourcesSelectEnhanced,

    // Computed values
    effectiveTracks,
  };
};
