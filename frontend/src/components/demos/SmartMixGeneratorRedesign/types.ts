import { ReactNode } from 'react';

/**
 * Track interface representing a music track
 */
export interface Track {
  id: string;
  title: string;
  artist: string;
  key: string;
  bpm: number;
  duration: number;
  energy?: number;
  genre?: string;
  color?: string;
  imageUrl?: string;
  filePath?: string;
  album?: string;
  directory_id?: string;
  transitionType?: string;
  transition_style_class?: string;
  audioUrl?: string;
  path?: string;
  file_path?: string;
}

/**
 * Folder interface representing a collection folder
 */
export interface Folder {
  id: string;
  name: string;
  track_count?: number;
}

/**
 * Collection interface representing a music collection
 */
export interface Collection {
  id: string;
  name: string;
  description?: string;
  track_count?: number;
}

/**
 * Mix statistics interface
 */
export interface MixStats {
  harmonyScore: number;
  bpmConsistency: number;
  energyFlow: number;
  overallScore: number;
  keyProgression?: string[];
  bpmRange?: [number, number];
  energyRange?: [number, number];
  duration?: number;
  transitionTypes?: Record<string, number>;
}

/**
 * Mix style interface
 */
export interface MixStyle {
  id: string;
  name: string;
  description: string;
  icon?: ReactNode;
  score?: number;
  recommended?: boolean;
  constraints?: {
    bpm_range?: [number, number];
    energy_range?: [number, number];
    key_rules?: string[];
  };
  key_rules?: string[];
}

/**
 * Mix configuration interface
 */
export interface MixConfig {
  mixStyle: string;
  trackCount: number;
  firstTrackOption: 'random' | 'select';
  firstTrack?: string;
  powerBlockSize: number;
  bpmTrend: 'steady' | 'rising' | 'falling' | 'random';
}

/**
 * Track replacement interface
 */
export interface TrackReplacement {
  id: string;
  title: string;
  artist: string;
  key: string;
  bpm: number;
  score: number;
  compatibility: number;
  reason?: string;
}

/**
 * View mode for the application
 */
export type ViewMode = 'configure' | 'results';

/**
 * Generator state interface
 */
export interface GeneratorState {
  selectedCollectionId: string;
  selectedFolderId: string;
  trackSearchTerm: string;
  mixConfig: MixConfig;
  result: Track[];
  mixStats: MixStats | null;
  isGenerating: boolean;
  viewMode: ViewMode;
  selectedTrackIndex: number | null;
  saveModalOpen: boolean;
  isReplaceModalOpen: boolean;
  trackToReplace: Track | null;
}

/**
 * Props for the main SmartMixGeneratorRedesign component
 */
export interface SmartMixGeneratorRedesignProps {
  className?: string;
}
