import React, { useState, useEffect } from 'react';
import { SmartMixGeneratorRedesignProps, Track } from './types';
import { useGeneratorState, useCollectionData, useMixStyles, useMixGeneration, useTrackReplacements } from './hooks';
import { ThemeProvider } from '@/providers/ThemeContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Loader2, Save, ChevronLeft, ChevronRight } from 'lucide-react';
import { SaveMixModal } from '@/components/mixes/list/SaveMixModal';
import TrackReplaceModal from '@/components/tracks/replacements/TrackReplaceModal';
import { useSaveMix } from '@/hooks/useMixes';
import { convertTracksForPlaylist } from './utils';
import { useToast } from '@/components/ui/use-toast';

// Import components (to be created)
import CollectionSelector from './components/CollectionSelector';
import TrackBrowser from './components/TrackBrowser';
import MixStyleSelector from './components/MixStyleSelector';
import MixConfigurator from './components/MixConfigurator';
import ResultsVisualizer from './components/ResultsVisualizer';
import MixPreview from './components/MixPreview';
import HarmonicVisualizer from './components/HarmonicVisualizer';
import ActionPanel from './components/ActionPanel';

/**
 * SmartMixGeneratorRedesign component
 * A redesigned version of the Smart Mix Generator with improved UI/UX
 */
const SmartMixGeneratorRedesign: React.FC<SmartMixGeneratorRedesignProps> = ({ className = '' }) => {
  // Use custom hooks for state management
  const { state, updateState, updateMixConfig } = useGeneratorState();
  const { toast } = useToast();
  const { mutate: saveMix, isPending: isSaving } = useSaveMix();

  // Use collection data hook
  const {
    collections,
    folders,
    filteredTracks,
    isLoadingCollections,
    isLoadingFolders,
    isLoadingTracks,
    handleSelectedCollection,
    handleSelectedFolder,
  } = useCollectionData(
    state.selectedCollectionId,
    state.selectedFolderId,
    state.trackSearchTerm,
    updateState
  );

  // Use mix styles hook
  const { mixStyles, isLoadingStyles } = useMixStyles(filteredTracks);

  // Use mix generation hook
  const { generateMix } = useMixGeneration(state, updateState, filteredTracks);

  // Use track replacements hook
  const {
    replacements,
    isLoadingReplacements,
    loadReplacements,
    handleReplacementSelect
  } = useTrackReplacements(state, updateState);

  // Handle saving the mix
  const handleSavePlaylist = (name: string, description: string) => {
    const mixData = {
      title: name,
      description,
      genre: state.mixConfig.mixStyle,
      tracks: convertTracksForPlaylist(state.result),
      transitions: {},
      isPublic: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Use saveMix mutation
    saveMix(mixData);

    toast({
      title: "Success",
      description: "Mix saved successfully",
    });

    // Close the save modal
    updateState({ saveModalOpen: false });
  };

  // Handle track selection in results
  const handleTrackSelect = (index: number) => {
    updateState({ selectedTrackIndex: index });
  };

  // Handle track replacement
  const handleReplaceTrack = (track: Track) => {
    if (!track || !track.id) {
      console.error('Invalid track provided for replacement');
      return;
    }

    loadReplacements(track);
  };

  // Handle finishing the mix creation
  const handleFinish = () => {
    // In a real implementation, this would navigate to the mix timeline or another page
    toast({
      title: "Mix Created",
      description: "Your mix has been created successfully.",
    });
  };

  // Handle back to configuration
  const handleBackToConfig = () => {
    updateState({ viewMode: 'configure' });
  };

  return (
    <ThemeProvider>
      <div className={`flex h-screen overflow-hidden ${className}`}>
        {/* Left Sidebar - Collection Selection */}
        <div className="w-64 border-r bg-background overflow-y-auto flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-xl font-semibold">Smart Mix Generator</h2>
            <p className="text-sm text-muted-foreground">Create harmonically mixed playlists</p>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            <CollectionSelector
              collections={collections}
              folders={folders}
              selectedCollectionId={state.selectedCollectionId}
              selectedFolderId={state.selectedFolderId}
              isLoadingCollections={isLoadingCollections}
              isLoadingFolders={isLoadingFolders}
              onCollectionSelect={handleSelectedCollection}
              onFolderSelect={handleSelectedFolder}
            />
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top Bar */}
          <div className="border-b p-4 flex justify-between items-center bg-background">
            <div>
              <h1 className="text-2xl font-bold">
                {state.viewMode === 'configure' ? 'Configure Mix' : 'Generated Mix'}
              </h1>
              <p className="text-sm text-muted-foreground">
                {state.viewMode === 'configure' 
                  ? 'Select tracks and configure your mix parameters' 
                  : 'Review and customize your generated mix'}
              </p>
            </div>
            
            {state.viewMode === 'results' && (
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleBackToConfig}>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back to Configuration
                </Button>
                <Button onClick={() => updateState({ saveModalOpen: true })}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Mix
                </Button>
                <Button onClick={handleFinish}>
                  Create Mix
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-hidden">
            {state.viewMode === 'configure' ? (
              <div className="flex h-full">
                {/* Left Panel - Track Browser */}
                <div className="w-1/3 border-r overflow-y-auto">
                  <div className="p-4">
                    <h3 className="text-lg font-semibold mb-4">Available Tracks</h3>
                    <TrackBrowser
                      tracks={filteredTracks}
                      isLoading={isLoadingTracks}
                      searchTerm={state.trackSearchTerm}
                      onSearchChange={(term) => updateState({ trackSearchTerm: term })}
                    />
                  </div>
                </div>

                {/* Right Panel - Configuration */}
                <div className="flex-1 overflow-y-auto p-6">
                  <div className="max-w-3xl mx-auto">
                    <h3 className="text-lg font-semibold mb-4">Mix Configuration</h3>
                    
                    {/* Mix Style Selection */}
                    <div className="mb-8">
                      <h4 className="text-md font-medium mb-2">Select Mix Style</h4>
                      <MixStyleSelector
                        mixStyles={mixStyles}
                        selectedStyleId={state.mixConfig.mixStyle}
                        isLoading={isLoadingStyles}
                        onStyleSelect={(styleId) => updateMixConfig({ mixStyle: styleId })}
                      />
                    </div>

                    <Separator className="my-6" />

                    {/* Mix Configuration */}
                    <MixConfigurator
                      config={state.mixConfig}
                      tracks={filteredTracks}
                      onConfigChange={updateMixConfig}
                    />

                    <div className="mt-8 flex justify-end">
                      <Button 
                        onClick={generateMix}
                        disabled={
                          state.isGenerating || 
                          !state.mixConfig.mixStyle || 
                          !state.selectedCollectionId || 
                          filteredTracks.length === 0
                        }
                      >
                        {state.isGenerating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Generating Mix...
                          </>
                        ) : (
                          <>
                            Generate Mix
                            <ChevronRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex h-full">
                {/* Results View */}
                <div className="flex-1 overflow-y-auto">
                  <Tabs defaultValue="tracks" className="w-full">
                    <div className="px-6 pt-4 border-b">
                      <TabsList className="w-full justify-start">
                        <TabsTrigger value="tracks">Tracks</TabsTrigger>
                        <TabsTrigger value="visualization">Visualization</TabsTrigger>
                        <TabsTrigger value="stats">Mix Stats</TabsTrigger>
                      </TabsList>
                    </div>
                    
                    <div className="p-6">
                      <TabsContent value="tracks" className="mt-0">
                        <ResultsVisualizer
                          result={state.result}
                          selectedTrackIndex={state.selectedTrackIndex}
                          onTrackSelect={handleTrackSelect}
                          onReplaceTrack={handleReplaceTrack}
                        />
                      </TabsContent>
                      
                      <TabsContent value="visualization" className="mt-0">
                        <HarmonicVisualizer 
                          tracks={state.result}
                          mixStats={state.mixStats}
                        />
                      </TabsContent>
                      
                      <TabsContent value="stats" className="mt-0">
                        <ActionPanel
                          mixStats={state.mixStats}
                          result={state.result}
                        />
                      </TabsContent>
                    </div>
                  </Tabs>
                </div>
                
                {/* Preview Panel */}
                {state.selectedTrackIndex !== null && state.result[state.selectedTrackIndex] && (
                  <div className="w-1/3 border-l overflow-y-auto p-4">
                    <h3 className="text-lg font-semibold mb-4">Track Preview</h3>
                    <MixPreview
                      track={state.result[state.selectedTrackIndex]}
                      nextTrack={state.selectedTrackIndex < state.result.length - 1 ? 
                        state.result[state.selectedTrackIndex + 1] : undefined}
                      prevTrack={state.selectedTrackIndex > 0 ? 
                        state.result[state.selectedTrackIndex - 1] : undefined}
                      onNext={() => {
                        if (state.selectedTrackIndex < state.result.length - 1) {
                          updateState({ selectedTrackIndex: state.selectedTrackIndex + 1 });
                        }
                      }}
                      onPrevious={() => {
                        if (state.selectedTrackIndex > 0) {
                          updateState({ selectedTrackIndex: state.selectedTrackIndex - 1 });
                        }
                      }}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Modals */}
        <SaveMixModal
          open={state.saveModalOpen}
          onOpenChange={(open) => updateState({ saveModalOpen: open })}
          tracks={state.result.map((track, index) => ({
            id: track.id,
            title: track.title,
            artist: track.artist,
            bpm: track.bpm,
            key: track.key,
            duration: track.duration,
            energy: track.energy || 5,
            mix_position: index,
            transitionType: track.transitionType
          }))}
          onSave={(mixData) => {
            handleSavePlaylist(mixData.title, mixData.description || '');
          }}
          isSaving={isSaving}
        />

        <TrackReplaceModal
          open={state.isReplaceModalOpen}
          onOpenChange={(open) => updateState({ isReplaceModalOpen: open })}
          track={state.trackToReplace}
          replacements={replacements}
          isLoading={isLoadingReplacements}
          onSelect={handleReplacementSelect}
        />
      </div>
    </ThemeProvider>
  );
};

export default SmartMixGeneratorRedesign;
