import { useState, useEffect, useCallback } from 'react';
import { 
  Track, 
  Folder, 
  Collection, 
  MixStats, 
  MixStyle, 
  MixConfig, 
  TrackReplacement,
  GeneratorState
} from './types';
import { getCollectionTracks, getCollectionFolders } from '@/services/api/collections';
import { getTrackReplacements, ReplacementRequest } from '@/services/api/tracks';
import { getMixStyleCompatibility } from '@/services/api/mixAnalytics';
import { getMixStyles } from '@/services/api/compatibility';
import { generateMix } from '@/services/api/mixAnalytics';
import { useCollections } from '@/hooks/useCollections';
import { useToast } from "@/components/ui/use-toast";
import { getAudioStreamUrl } from '@/services/api/audio';

/**
 * Custom hook for managing the entire generator state
 */
export const useGeneratorState = () => {
  const { toast } = useToast();
  const [state, setState] = useState<GeneratorState>({
    selectedCollectionId: '',
    selectedFolderId: '',
    trackSearchTerm: '',
    mixConfig: {
      mixStyle: '',
      trackCount: 10,
      firstTrackOption: 'random',
      firstTrack: undefined,
      powerBlockSize: 0,
      bpmTrend: 'steady',
    },
    result: [],
    mixStats: null,
    isGenerating: false,
    viewMode: 'configure',
    selectedTrackIndex: null,
    saveModalOpen: false,
    isReplaceModalOpen: false,
    trackToReplace: null
  });

  // Update state helper function
  const updateState = useCallback((updates: Partial<GeneratorState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Update mix config helper function
  const updateMixConfig = useCallback((updates: Partial<MixConfig>) => {
    setState(prev => ({
      ...prev,
      mixConfig: { ...prev.mixConfig, ...updates }
    }));
  }, []);

  return {
    state,
    updateState,
    updateMixConfig
  };
};

/**
 * Hook for managing collection and folder data
 */
export const useCollectionData = (
  selectedCollectionId: string,
  selectedFolderId: string,
  trackSearchTerm: string,
  updateState: (updates: Partial<GeneratorState>) => void
) => {
  const [originalTracks, setOriginalTracks] = useState<Track[]>([]);
  const [folderTracks, setFolderTracks] = useState<Track[]>([]);
  const [filteredTracks, setFilteredTracks] = useState<Track[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);
  const [isLoadingTracks, setIsLoadingTracks] = useState(false);
  const { collections, isLoading: isLoadingCollections } = useCollections();

  // Handler for selecting a collection
  const handleSelectedCollection = useCallback(async (collectionId: string) => {
    console.log('Collection selected:', collectionId);
    updateState({ selectedCollectionId: collectionId });
    setIsLoadingTracks(true);
    setIsLoadingFolders(true);

    try {
      // Fetch tracks for the selected collection
      const tracksResponse = await getCollectionTracks(collectionId);
      
      // Process tracks response
      let tracksArray: Track[] = [];
      if (Array.isArray(tracksResponse)) {
        tracksArray = tracksResponse;
      } else if (tracksResponse.data && Array.isArray(tracksResponse.data)) {
        tracksArray = tracksResponse.data;
      } else if (tracksResponse.tracks && Array.isArray(tracksResponse.tracks)) {
        tracksArray = tracksResponse.tracks;
      }

      // Set the tracks
      setOriginalTracks(tracksArray);
      setFolderTracks(tracksArray);

      // Fetch folders for the selected collection
      const foldersResponse = await getCollectionFolders(collectionId);
      
      // Process folders response
      let foldersArray: Folder[] = [];
      if (Array.isArray(foldersResponse)) {
        foldersArray = foldersResponse;
      } else if (foldersResponse.data && Array.isArray(foldersResponse.data)) {
        foldersArray = foldersResponse.data;
      } else if (foldersResponse.folders && Array.isArray(foldersResponse.folders)) {
        foldersArray = foldersResponse.folders;
      }

      // Add "All Tracks" option
      const allTracksFolder: Folder = {
        id: "ALL",
        name: "All Tracks",
        track_count: tracksArray.length
      };

      setFolders([allTracksFolder, ...foldersArray]);
      updateState({ selectedFolderId: "ALL" });
    } catch (error) {
      console.error('Error loading collection data:', error);
    } finally {
      setIsLoadingTracks(false);
      setIsLoadingFolders(false);
    }
  }, [updateState]);

  // Handler for selecting a folder
  const handleSelectedFolder = useCallback(async (folderId: string) => {
    console.log('Folder selected:', folderId);
    updateState({ selectedFolderId: folderId });
    setIsLoadingTracks(true);

    try {
      if (folderId === "ALL") {
        // If "All Tracks" is selected, use all tracks from the collection
        setFolderTracks(originalTracks);
      } else {
        // Otherwise, fetch tracks for the selected folder
        const response = await getCollectionTracks(selectedCollectionId, folderId);
        
        // Process response
        let tracksArray: Track[] = [];
        if (Array.isArray(response)) {
          tracksArray = response;
        } else if (response.data && Array.isArray(response.data)) {
          tracksArray = response.data;
        } else if (response.tracks && Array.isArray(response.tracks)) {
          tracksArray = response.tracks;
        }

        setFolderTracks(tracksArray);
      }
    } catch (error) {
      console.error('Error loading folder tracks:', error);
      // Fallback to client-side filtering if API call fails
      if (folderId === "ALL") {
        setFolderTracks(originalTracks);
      } else {
        const filteredTracks = originalTracks.filter(track => {
          return track.directory_id === folderId ||
                 (track.file_path && track.file_path.includes(folderId));
        });
        setFolderTracks(filteredTracks);
      }
    } finally {
      setIsLoadingTracks(false);
    }
  }, [selectedCollectionId, originalTracks, updateState]);

  // Filter tracks based on search term
  useEffect(() => {
    // Filter out any tracks without IDs
    const validTracks = folderTracks.filter(track => !!track && !!track.id);

    if (!trackSearchTerm.trim()) {
      setFilteredTracks(validTracks);
      return;
    }

    const searchTermLower = trackSearchTerm.toLowerCase();
    const filtered = validTracks.filter(track => {
      const title = (track.title || '').toLowerCase();
      const artist = (track.artist || '').toLowerCase();
      const album = (track.album || '').toLowerCase();
      const genre = (track.genre || '').toLowerCase();

      return title.includes(searchTermLower) ||
             artist.includes(searchTermLower) ||
             album.includes(searchTermLower) ||
             genre.includes(searchTermLower);
    });

    setFilteredTracks(filtered);
  }, [trackSearchTerm, folderTracks]);

  return {
    collections,
    folders,
    originalTracks,
    folderTracks,
    filteredTracks,
    isLoadingCollections,
    isLoadingFolders,
    isLoadingTracks,
    handleSelectedCollection,
    handleSelectedFolder,
  };
};

/**
 * Hook for managing mix styles
 */
export const useMixStyles = (folderTracks: Track[]) => {
  const [mixStyles, setMixStyles] = useState<MixStyle[]>([]);
  const [isLoadingStyles, setIsLoadingStyles] = useState(false);

  // Fetch mix style compatibility when folderTracks change
  useEffect(() => {
    async function fetchCompatibility() {
      if (!folderTracks.length) {
        return;
      }
      setIsLoadingStyles(true);
      try {
        // First, get all available mix styles for the smart generator
        const allStyles = await getMixStyles('smart');

        // Then, get compatibility scores for these styles with the current tracks
        const trackIds = folderTracks.map((t: any) => t.id);
        const compatibilityScores = await getMixStyleCompatibility(trackIds);

        // Merge the styles with their compatibility scores
        let mergedStyles = allStyles;

        if (compatibilityScores && Array.isArray(compatibilityScores) && compatibilityScores.length > 0) {
          // Create a map of style IDs to compatibility scores
          const scoreMap = new Map();
          compatibilityScores.forEach(score => {
            if (score && score.id) {
              scoreMap.set(score.id.toString(), score);
            }
          });

          // Merge the styles with their scores
          mergedStyles = allStyles.map(style => {
            const styleId = style.id.toString();
            const compatScore = scoreMap.get(styleId);

            return {
              ...style,
              score: compatScore ? compatScore.score : 0.5,
              recommended: compatScore ? compatScore.recommended : false
            };
          });
        }

        // Sort by score (highest first)
        mergedStyles.sort((a, b) => {
          const scoreA = typeof a.score === 'number' ? a.score : 0;
          const scoreB = typeof b.score === 'number' ? b.score : 0;

          // Primary sort by score
          if (scoreB !== scoreA) {
            return scoreB - scoreA;
          }

          // Secondary sort by recommended status
          if (a.recommended && !b.recommended) return -1;
          if (!a.recommended && b.recommended) return 1;

          // Tertiary sort by name for consistent ordering
          return (a.name || '').localeCompare(b.name || '');
        });

        setMixStyles(mergedStyles);
      } catch (e) {
        console.error('Failed to fetch mix styles:', e);
        setMixStyles([]);
      } finally {
        setIsLoadingStyles(false);
      }
    }

    fetchCompatibility();
  }, [folderTracks]);

  return {
    mixStyles,
    isLoadingStyles
  };
};

/**
 * Hook for generating a mix
 */
export const useMixGeneration = (
  state: GeneratorState,
  updateState: (updates: Partial<GeneratorState>) => void,
  filteredTracks: Track[]
) => {
  const { toast } = useToast();

  const generateMixHandler = async () => {
    const { mixConfig, selectedCollectionId, selectedFolderId } = state;
    
    console.log('Form data submitted:', mixConfig);
    updateState({ isGenerating: true });

    // Check if a mix style is selected
    if (!mixConfig.mixStyle) {
      updateState({ isGenerating: false });
      toast({
        title: "Error",
        description: "Please select a mix style before generating.",
        variant: "destructive"
      });
      return;
    }

    if (!selectedCollectionId) {
      updateState({ isGenerating: false });
      toast({
        title: "Error",
        description: "Please select a collection first.",
        variant: "destructive"
      });
      return;
    }

    const payload = {
      tracks: filteredTracks.map((track) => track.id),
      mixStyle: mixConfig.mixStyle,
      trackCount: mixConfig.trackCount,
      firstTrack: mixConfig.firstTrackOption === 'select' ? mixConfig.firstTrack : null,
      powerBlockSize: mixConfig.powerBlockSize,
      bpmTrend: mixConfig.bpmTrend,
    };

    const params: Record<string, string> = {};

    if (selectedCollectionId) {
      params.directory = selectedCollectionId;
    }

    if (selectedFolderId && selectedFolderId !== 'ALL') {
      params.folder_id = selectedFolderId;
    }

    try {
      console.log('Calling API to generate mix with payload:', payload);
      const mixData = await generateMix(payload, params);
      console.log('Mix generation successful, received data:', mixData);

      // Process the mix data to ensure all required properties are present
      const processedMix = mixData.mix.map((track: any, index: number) => {
        // First track doesn't have transition info
        if (index === 0) {
          return {
            ...track,
            transition_style_class: undefined,
            transitionType: undefined,
            audioUrl: track.audioUrl || track.file_path || track.path || getAudioStreamUrl(track.id)
          };
        }

        // Ensure transition properties are present for all other tracks
        return {
          ...track,
          // Use existing values or provide defaults
          transition_style_class: track.transition_style_class || 'beatmatch-mix',
          transitionType: track.transitionType || 'beatmatch',
          audioUrl: track.audioUrl || track.file_path || track.path || getAudioStreamUrl(track.id)
        };
      });

      // Update state with results
      updateState({
        result: processedMix,
        mixStats: mixData.stats,
        viewMode: 'results',
        isGenerating: false
      });
    } catch (error) {
      console.error('Error generating mix:', error);
      toast({
        title: "Error",
        description: "Failed to generate mix. Please try again.",
        variant: "destructive"
      });
      updateState({ isGenerating: false });
    }
  };

  return {
    generateMix: generateMixHandler
  };
};

/**
 * Hook for managing track replacements
 */
export const useTrackReplacements = (
  state: GeneratorState,
  updateState: (updates: Partial<GeneratorState>) => void
) => {
  const [replacements, setReplacements] = useState<TrackReplacement[]>([]);
  const [isLoadingReplacements, setIsLoadingReplacements] = useState(false);
  const { toast } = useToast();

  const loadReplacements = async (track: Track) => {
    if (!track || !track.id) {
      console.error('Invalid track provided for replacement');
      return;
    }

    updateState({ 
      trackToReplace: track,
      isReplaceModalOpen: true 
    });
    setIsLoadingReplacements(true);

    try {
      // Create request payload with track IDs
      const request: ReplacementRequest = {
        track_to_replace_id: track.id,
        current_mix_ids: state.result.map(t => t.id),
        collection_id: state.selectedCollectionId,
        folder_id: state.selectedFolderId !== 'ALL' ? state.selectedFolderId : undefined,
        mix_style_id: state.mixConfig.mixStyle,
        power_block_size: state.mixConfig.powerBlockSize
      };

      // Call API
      const replacementTracks = await getTrackReplacements(request);
      setReplacements(replacementTracks);
    } catch (error) {
      console.error('Failed to get track replacements:', error);
      toast({
        title: "Error loading replacements",
        description: "Request failed. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingReplacements(false);
    }
  };

  const handleReplacementSelect = (replacement: Track) => {
    if (!state.trackToReplace) return;

    // Find the index of the track to replace
    const trackIndex = state.result.findIndex(t => t.id === state.trackToReplace!.id);
    if (trackIndex === -1) return;

    // Create a new array with the replacement
    const newResult = [...state.result];
    newResult[trackIndex] = {
      ...replacement,
      transitionType: state.trackToReplace.transitionType,
      audioUrl: replacement.audioUrl || replacement.file_path || replacement.path || getAudioStreamUrl(replacement.id)
    };

    // Update the result
    updateState({ 
      result: newResult,
      isReplaceModalOpen: false 
    });
  };

  return {
    replacements,
    isLoadingReplacements,
    loadReplacements,
    handleReplacementSelect
  };
};
