// Main component
import SmartMixGeneratorRedesign from './SmartMixGeneratorRedesign';

// Export all components
export { SmartMixGeneratorRedesign };
export { default as CollectionSelector } from './components/CollectionSelector';
export { default as TrackBrowser } from './components/TrackBrowser';
export { default as MixStyleSelector } from './components/MixStyleSelector';
export { default as MixConfigurator } from './components/MixConfigurator';
export { default as ResultsVisualizer } from './components/ResultsVisualizer';
export { default as MixPreview } from './components/MixPreview';
export { default as HarmonicVisualizer } from './components/HarmonicVisualizer';
export { default as ActionPanel } from './components/ActionPanel';

// Export types and utilities
export * from './types';
export * from './utils';
export * from './hooks';

// Default export
export default SmartMixGeneratorRedesign;
