import { Track } from './types';

/**
 * Ensures a track ID is a number
 */
export const ensureNumberId = (id: string | number | undefined): number => {
  if (id === undefined || id === null) {
    return 0;
  }
  return typeof id === 'number' ? id : parseInt(id);
};

/**
 * Format duration in seconds to MM:SS format
 */
export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Get the distance between two musical keys
 */
export const getKeyDistance = (key1: string, key2: string): number => {
  // This is a simplified implementation
  // In a real app, this would calculate the actual distance on the circle of fifths
  if (key1 === key2) return 0;

  // Extract the key number (1-12) and mode (A/B for major/minor)
  const key1Num = parseInt(key1.replace(/[AB]$/, ''));
  const key2Num = parseInt(key2.replace(/[AB]$/, ''));
  const key1Mode = key1.slice(-1);
  const key2Mode = key2.slice(-1);

  // Calculate basic distance
  let distance = Math.abs(key1Num - key2Num);

  // Adjust for circle wrap-around (12 keys total)
  if (distance > 6) {
    distance = 12 - distance;
  }

  // Add 1 to distance if modes are different
  if (key1Mode !== key2Mode) {
    distance += 1;
  }

  return distance;
};

/**
 * Get color based on compatibility between two tracks
 */
export const getCompatibilityColor = (prevTrack: Track, currentTrack: Track): string => {
  if (!prevTrack || !currentTrack) return '';

  // If we have a transition_style_class, use it to determine the color
  if (currentTrack.transition_style_class) {
    const styleClass = currentTrack.transition_style_class.toLowerCase();

    // Map backend transition style classes to frontend colors
    switch (styleClass) {
      case 'perfect-mix': return 'text-green-600';
      case 'adjacent-mix': return 'text-blue-600';
      case 'harmonic-third-mix': return 'text-purple-600';
      case 'diagonal-mix': return 'text-amber-600';
      case 'energy-boost-mix': return 'text-red-600';
      case 'energy-drop-mix': return 'text-indigo-600';
      // Add more cases as needed
    }
  }

  // Fallback to the original logic if no transition_style_class is available
  const keyDiff = getKeyDistance(prevTrack.key, currentTrack.key);
  const bpmDiff = Math.abs(prevTrack.bpm - currentTrack.bpm);

  if (keyDiff === 0 && bpmDiff < 3) return 'text-green-600';
  if (keyDiff <= 1 && bpmDiff < 5) return 'text-green-500';
  if (keyDiff <= 2 && bpmDiff < 8) return 'text-amber-500';
  return 'text-red-500';
};

/**
 * Get background color based on compatibility between two tracks
 */
export const getCompatibilityBgColor = (prevTrack: Track, currentTrack: Track): string => {
  if (!prevTrack || !currentTrack) return '';

  // If we have a transition_style_class, use it to determine the color
  if (currentTrack.transition_style_class) {
    const styleClass = currentTrack.transition_style_class.toLowerCase();

    // Map backend transition style classes to frontend colors
    switch (styleClass) {
      case 'perfect-mix': return 'bg-green-100 dark:bg-green-900/20';
      case 'adjacent-mix': return 'bg-blue-100 dark:bg-blue-900/20';
      case 'harmonic-third-mix': return 'bg-purple-100 dark:bg-purple-900/20';
      case 'diagonal-mix': return 'bg-amber-100 dark:bg-amber-900/20';
      case 'energy-boost-mix': return 'bg-red-100 dark:bg-red-900/20';
      case 'energy-drop-mix': return 'bg-indigo-100 dark:bg-indigo-900/20';
      // Add more cases as needed
    }
  }

  // Fallback to the original logic if no transition_style_class is available
  const keyDiff = getKeyDistance(prevTrack.key, currentTrack.key);
  const bpmDiff = Math.abs(prevTrack.bpm - currentTrack.bpm);

  if (keyDiff === 0 && bpmDiff < 3) return 'bg-green-100 dark:bg-green-900/20';
  if (keyDiff <= 1 && bpmDiff < 5) return 'bg-green-50 dark:bg-green-900/10';
  if (keyDiff <= 2 && bpmDiff < 8) return 'bg-amber-50 dark:bg-amber-900/10';
  return 'bg-red-50 dark:bg-red-900/10';
};

/**
 * Get transition type display name
 */
export const getTransitionTypeName = (transitionType?: string): string => {
  if (!transitionType) return 'Unknown';

  switch (transitionType.toLowerCase()) {
    case 'beatmatch': return 'Beatmatch';
    case 'cut': return 'Cut';
    case 'fade': return 'Fade';
    case 'echo_out': return 'Echo Out';
    case 'filter_fade': return 'Filter Fade';
    case 'power_down': return 'Power Down';
    case 'backspin': return 'Backspin';
    case 'reverb_out': return 'Reverb Out';
    case 'loop_exit': return 'Loop Exit';
    default: return transitionType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }
};

/**
 * Convert tracks to the format expected by the API
 */
export const convertTracksForPlaylist = (tracks: Track[]): any[] => {
  return tracks.map(track => ({
    id: ensureNumberId(track.id),
    duration: typeof track.duration === 'number' ? track.duration : undefined
  }));
};

/**
 * Get a color for a key based on the camelot wheel
 */
export const getKeyColor = (key: string): string => {
  if (!key) return 'bg-gray-200 dark:bg-gray-700';

  // Extract the key number (1-12)
  const keyNum = parseInt(key.replace(/[AB]$/, ''));
  
  // Map key numbers to colors
  const keyColors = [
    'bg-red-500',     // 1
    'bg-orange-500',  // 2
    'bg-yellow-500',  // 3
    'bg-lime-500',    // 4
    'bg-green-500',   // 5
    'bg-emerald-500', // 6
    'bg-teal-500',    // 7
    'bg-cyan-500',    // 8
    'bg-blue-500',    // 9
    'bg-indigo-500',  // 10
    'bg-violet-500',  // 11
    'bg-purple-500',  // 12
  ];

  return keyColors[(keyNum - 1) % 12];
};

/**
 * Get a color for a BPM value
 */
export const getBpmColor = (bpm: number): string => {
  if (bpm < 80) return 'bg-blue-500';
  if (bpm < 100) return 'bg-cyan-500';
  if (bpm < 120) return 'bg-teal-500';
  if (bpm < 140) return 'bg-green-500';
  if (bpm < 160) return 'bg-yellow-500';
  if (bpm < 180) return 'bg-orange-500';
  return 'bg-red-500';
};

/**
 * Get a color for an energy value (1-10)
 */
export const getEnergyColor = (energy: number): string => {
  if (energy <= 2) return 'bg-blue-500';
  if (energy <= 4) return 'bg-cyan-500';
  if (energy <= 6) return 'bg-green-500';
  if (energy <= 8) return 'bg-yellow-500';
  return 'bg-red-500';
};

/**
 * Format a score as a percentage
 */
export const formatScore = (score: number): string => {
  return `${Math.round(score * 100)}%`;
};
