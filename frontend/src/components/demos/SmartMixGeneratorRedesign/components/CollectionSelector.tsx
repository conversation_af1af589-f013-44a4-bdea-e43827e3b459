import React from 'react';
import { Collection, Folder } from '../types';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { FolderOpen, Library, Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface CollectionSelectorProps {
  collections: Collection[];
  folders: Folder[];
  selectedCollectionId: string;
  selectedFolderId: string;
  isLoadingCollections: boolean;
  isLoadingFolders: boolean;
  onCollectionSelect: (id: string) => void;
  onFolderSelect: (id: string) => void;
}

/**
 * CollectionSelector component
 * Allows users to select a collection and folder
 */
const CollectionSelector: React.FC<CollectionSelectorProps> = ({
  collections,
  folders,
  selectedCollectionId,
  selectedFolderId,
  isLoadingCollections,
  isLoadingFolders,
  onCollectionSelect,
  onFolderSelect
}) => {
  return (
    <div className="space-y-6">
      {/* Collection Selection */}
      <div>
        <h3 className="text-sm font-medium mb-2 flex items-center">
          <Library className="h-4 w-4 mr-2" />
          Music Collection
        </h3>
        
        {isLoadingCollections ? (
          <Skeleton className="h-10 w-full" />
        ) : (
          <Select
            value={selectedCollectionId}
            onValueChange={onCollectionSelect}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a collection" />
            </SelectTrigger>
            <SelectContent>
              {collections.map((collection) => (
                <SelectItem key={collection.id} value={collection.id}>
                  {collection.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Folder Selection - Only show if a collection is selected */}
      {selectedCollectionId && (
        <div>
          <h3 className="text-sm font-medium mb-2 flex items-center">
            <FolderOpen className="h-4 w-4 mr-2" />
            Folder
          </h3>
          
          {isLoadingFolders ? (
            <div className="space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : (
            <ScrollArea className="h-[calc(100vh-300px)]">
              <div className="space-y-2 pr-4">
                {folders.map((folder) => (
                  <Card 
                    key={folder.id}
                    className={`cursor-pointer transition-colors hover:bg-accent ${
                      selectedFolderId === folder.id ? 'bg-accent' : ''
                    }`}
                    onClick={() => onFolderSelect(folder.id)}
                  >
                    <CardContent className="p-3 flex justify-between items-center">
                      <div className="flex items-center">
                        <FolderOpen className={`h-4 w-4 mr-2 ${
                          selectedFolderId === folder.id ? 'text-primary' : 'text-muted-foreground'
                        }`} />
                        <span className={`${
                          selectedFolderId === folder.id ? 'font-medium' : ''
                        }`}>
                          {folder.name}
                        </span>
                      </div>
                      {folder.track_count !== undefined && (
                        <Badge variant="outline" className="ml-2">
                          {folder.track_count}
                        </Badge>
                      )}
                    </CardContent>
                  </Card>
                ))}
                
                {folders.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No folders available
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </div>
      )}

      {/* Loading indicator */}
      {(isLoadingCollections || isLoadingFolders) && (
        <div className="flex items-center justify-center text-sm text-muted-foreground">
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Loading...
        </div>
      )}
    </div>
  );
};

export default CollectionSelector;
