import React from 'react';
import { Track, MixStats } from '../types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDuration, formatScore } from '../utils';
import { 
  Clock, 
  Music, 
  Gauge, 
  Zap, 
  ArrowRight, 
  BarChart4, 
  ListMusic 
} from 'lucide-react';

interface ActionPanelProps {
  mixStats: MixStats | null;
  result: Track[];
}

/**
 * ActionPanel component
 * Displays detailed mix statistics and summary
 */
const ActionPanel: React.FC<ActionPanelProps> = ({
  mixStats,
  result
}) => {
  // Calculate total duration
  const totalDuration = result.reduce((total, track) => total + track.duration, 0);
  
  // Count transition types
  const transitionTypes: Record<string, number> = {};
  result.forEach((track, index) => {
    if (index > 0 && track.transitionType) {
      const type = track.transitionType;
      transitionTypes[type] = (transitionTypes[type] || 0) + 1;
    }
  });

  // Calculate BPM range
  const bpms = result.map(track => track.bpm);
  const minBpm = Math.min(...bpms);
  const maxBpm = Math.max(...bpms);

  // Calculate energy range if available
  const energies = result
    .map(track => track.energy)
    .filter((energy): energy is number => energy !== undefined);
  
  const minEnergy = energies.length > 0 ? Math.min(...energies) : undefined;
  const maxEnergy = energies.length > 0 ? Math.max(...energies) : undefined;

  return (
    <div className="space-y-6">
      {/* Mix Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ListMusic className="h-5 w-5 mr-2 text-primary" />
            Mix Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center">
                <Music className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground">Tracks:</span>
                <span className="ml-2 font-medium">{result.length}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground">Total Duration:</span>
                <span className="ml-2 font-medium">{formatDuration(totalDuration)}</span>
              </div>
              <div className="flex items-center">
                <Gauge className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground">BPM Range:</span>
                <span className="ml-2 font-medium">{minBpm.toFixed(1)} - {maxBpm.toFixed(1)}</span>
              </div>
              {minEnergy !== undefined && maxEnergy !== undefined && (
                <div className="flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-muted-foreground">Energy Range:</span>
                  <span className="ml-2 font-medium">{minEnergy} - {maxEnergy}</span>
                </div>
              )}
            </div>
            
            <div>
              <div className="text-sm font-medium mb-2 flex items-center">
                <ArrowRight className="h-4 w-4 mr-1 text-muted-foreground" />
                Transition Types
              </div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(transitionTypes).map(([type, count]) => (
                  <Badge key={type} variant="outline" className="flex items-center gap-1">
                    {type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ')}
                    <span className="ml-1 bg-primary/20 px-1 rounded-sm text-xs">
                      {count}
                    </span>
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mix Quality */}
      {mixStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart4 className="h-5 w-5 mr-2 text-primary" />
              Mix Quality Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <QualityMeter 
                label="Overall Score" 
                value={mixStats.overallScore} 
                description="Overall quality of the mix based on harmony, BPM consistency, and energy flow"
              />
              <QualityMeter 
                label="Harmonic Compatibility" 
                value={mixStats.harmonyScore} 
                description="How well the keys of adjacent tracks work together"
              />
              <QualityMeter 
                label="BPM Consistency" 
                value={mixStats.bpmConsistency} 
                description="Smoothness of tempo changes throughout the mix"
              />
              <QualityMeter 
                label="Energy Flow" 
                value={mixStats.energyFlow} 
                description="How well the energy levels progress throughout the mix"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Music className="h-5 w-5 mr-2 text-primary" />
            Key Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <KeyDistributionChart tracks={result} />
        </CardContent>
      </Card>
    </div>
  );
};

interface QualityMeterProps {
  label: string;
  value: number;
  description: string;
}

/**
 * QualityMeter component
 * Displays a quality metric with a progress bar
 */
const QualityMeter: React.FC<QualityMeterProps> = ({ label, value, description }) => {
  // Determine color based on value
  const getColorClass = (value: number) => {
    if (value >= 0.8) return 'bg-green-500';
    if (value >= 0.6) return 'bg-blue-500';
    if (value >= 0.4) return 'bg-amber-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-1">
      <div className="flex justify-between items-center">
        <div className="font-medium">{label}</div>
        <div className={`font-medium ${
          value >= 0.8 ? 'text-green-600 dark:text-green-400' : 
          value >= 0.6 ? 'text-blue-600 dark:text-blue-400' : 
          value >= 0.4 ? 'text-amber-600 dark:text-amber-400' : 
          'text-red-600 dark:text-red-400'
        }`}>
          {formatScore(value)}
        </div>
      </div>
      <div className="w-full h-2 bg-secondary rounded-full overflow-hidden">
        <div
          className={`h-full rounded-full ${getColorClass(value)}`}
          style={{ width: `${Math.round(value * 100)}%` }}
        />
      </div>
      <div className="text-xs text-muted-foreground">{description}</div>
    </div>
  );
};

interface KeyDistributionChartProps {
  tracks: Track[];
}

/**
 * KeyDistributionChart component
 * Displays a chart showing the distribution of keys in the mix
 */
const KeyDistributionChart: React.FC<KeyDistributionChartProps> = ({ tracks }) => {
  // Count occurrences of each key
  const keyCounts: Record<string, number> = {};
  tracks.forEach(track => {
    keyCounts[track.key] = (keyCounts[track.key] || 0) + 1;
  });

  // Sort keys by Camelot number
  const sortedKeys = Object.keys(keyCounts).sort((a, b) => {
    const aNum = parseInt(a.replace(/[AB]$/, ''));
    const bNum = parseInt(b.replace(/[AB]$/, ''));
    if (aNum !== bNum) return aNum - bNum;
    return a.localeCompare(b);
  });

  // Find the maximum count for scaling
  const maxCount = Math.max(...Object.values(keyCounts));

  return (
    <div className="space-y-2">
      {sortedKeys.map(key => {
        const count = keyCounts[key];
        const percentage = (count / tracks.length) * 100;
        
        // Determine color based on mode (A = major, B = minor)
        const colorClass = key.endsWith('A') 
          ? 'bg-blue-500 dark:bg-blue-600' 
          : 'bg-purple-500 dark:bg-purple-600';
        
        return (
          <div key={key} className="space-y-1">
            <div className="flex justify-between items-center text-sm">
              <div className="font-mono">{key}</div>
              <div>{count} track{count !== 1 ? 's' : ''} ({percentage.toFixed(0)}%)</div>
            </div>
            <div className="w-full h-2 bg-secondary rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full ${colorClass}`}
                style={{ width: `${percentage}%` }}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ActionPanel;
