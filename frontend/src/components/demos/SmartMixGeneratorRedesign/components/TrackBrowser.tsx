import React from 'react';
import { Track } from '../types';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Music, Loader2 } from 'lucide-react';
import { formatDuration, getKeyColor, getBpmColor } from '../utils';
import { formatCoverArtUrl } from '@/utils/apiUrlHelper';
import { Badge } from '@/components/ui/badge';

interface TrackBrowserProps {
  tracks: Track[];
  isLoading: boolean;
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

/**
 * TrackBrowser component
 * Displays a list of tracks with search functionality
 */
const TrackBrowser: React.FC<TrackBrowserProps> = ({
  tracks,
  isLoading,
  searchTerm,
  onSearchChange
}) => {
  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Search tracks..."
          className="pl-8"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>

      {/* Track Count */}
      <div className="text-sm text-muted-foreground">
        {isLoading ? (
          <div className="flex items-center">
            <Loader2 className="h-3 w-3 mr-2 animate-spin" />
            Loading tracks...
          </div>
        ) : (
          <span>{tracks.length} tracks available</span>
        )}
      </div>

      {/* Track List */}
      {isLoading ? (
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="h-16 w-full" />
          ))}
        </div>
      ) : (
        <ScrollArea className="h-[calc(100vh-250px)]">
          <div className="space-y-2 pr-4">
            {tracks.length > 0 ? (
              tracks.map((track) => (
                <TrackItem key={track.id} track={track} />
              ))
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
                <Music className="h-8 w-8 mb-2 opacity-50" />
                <p>No tracks found</p>
                {searchTerm && (
                  <p className="text-sm mt-1">
                    Try adjusting your search or selecting a different folder
                  </p>
                )}
              </div>
            )}
          </div>
        </ScrollArea>
      )}
    </div>
  );
};

interface TrackItemProps {
  track: Track;
}

/**
 * TrackItem component
 * Displays a single track in the browser
 */
const TrackItem: React.FC<TrackItemProps> = ({ track }) => {
  return (
    <div className="border rounded-md p-3 hover:bg-accent/50 transition-colors">
      <div className="flex justify-between items-start">
        <div className="flex items-center flex-1 min-w-0">
          {/* Track artwork */}
          <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0 mr-3">
            {track.coverArtUrl || track.file_path ? (
              <img
                src={formatCoverArtUrl(track.coverArtUrl || track.file_path)}
                alt={`${track.title} cover`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to music icon if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : (
              <div className="w-full h-full bg-muted rounded-md flex items-center justify-center">
                <Music className="h-5 w-5 text-muted-foreground opacity-50" />
              </div>
            )}
            <div className="w-full h-full bg-muted rounded-md flex items-center justify-center hidden">
              <Music className="h-5 w-5 text-muted-foreground opacity-50" />
            </div>
          </div>

          <div className="flex-1 min-w-0">
            <div className="font-medium truncate">{track.title}</div>
            <div className="text-sm text-muted-foreground truncate">{track.artist}</div>
          </div>
        </div>
        <div className="flex items-center space-x-2 ml-2 flex-shrink-0">
          <Badge variant="outline" className={`font-mono ${getKeyColor(track.key)} text-white`}>
            {track.key}
          </Badge>
          <Badge variant="outline" className={`font-mono ${getBpmColor(track.bpm)} text-white`}>
            {track.bpm.toFixed(1)}
          </Badge>
          <Badge variant="outline">
            {formatDuration(track.duration)}
          </Badge>
        </div>
      </div>
      {track.genre && (
        <div className="mt-1 text-xs text-muted-foreground truncate">
          Genre: {track.genre}
        </div>
      )}
    </div>
  );
};

export default TrackBrowser;
