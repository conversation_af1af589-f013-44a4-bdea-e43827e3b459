import React from 'react';
import { MixStyle } from '../types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Star, Music, Disc, Zap, Waves, Shuffle } from 'lucide-react';
import { formatScore } from '../utils';

interface MixStyleSelectorProps {
  mixStyles: MixStyle[];
  selectedStyleId: string;
  isLoading: boolean;
  onStyleSelect: (styleId: string) => void;
}

/**
 * MixStyleSelector component
 * Displays a grid of mix style cards with compatibility scores
 */
const MixStyleSelector: React.FC<MixStyleSelectorProps> = ({
  mixStyles,
  selectedStyleId,
  isLoading,
  onStyleSelect
}) => {
  // Get icon for mix style
  const getStyleIcon = (styleName: string) => {
    const name = styleName.toLowerCase();
    if (name.includes('energy') || name.includes('power')) return <Zap className="h-5 w-5" />;
    if (name.includes('harmonic') || name.includes('key')) return <Music className="h-5 w-5" />;
    if (name.includes('club') || name.includes('dance')) return <Disc className="h-5 w-5" />;
    if (name.includes('chill') || name.includes('ambient')) return <Waves className="h-5 w-5" />;
    if (name.includes('random') || name.includes('eclectic')) return <Shuffle className="h-5 w-5" />;
    return <Music className="h-5 w-5" />;
  };

  return (
    <div>
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="h-40 w-full" />
          ))}
        </div>
      ) : mixStyles.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 text-muted-foreground border rounded-md">
          <Music className="h-8 w-8 mb-2 opacity-50" />
          <p>No mix styles available</p>
          <p className="text-sm mt-1">
            Please select a collection with tracks first
          </p>
        </div>
      ) : (
        <ScrollArea className="h-[300px] pr-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pr-4">
            {mixStyles.map((style) => (
              <StyleCard
                key={style.id}
                style={style}
                isSelected={selectedStyleId === style.id}
                onSelect={() => onStyleSelect(style.id)}
              />
            ))}
          </div>
        </ScrollArea>
      )}
    </div>
  );
};

interface StyleCardProps {
  style: MixStyle;
  isSelected: boolean;
  onSelect: () => void;
}

/**
 * StyleCard component
 * Displays a single mix style card
 */
const StyleCard: React.FC<StyleCardProps> = ({ style, isSelected, onSelect }) => {
  // Get icon for mix style
  const getStyleIcon = (styleName: string) => {
    const name = styleName.toLowerCase();
    if (name.includes('energy') || name.includes('power')) return <Zap className="h-5 w-5 text-amber-500" />;
    if (name.includes('harmonic') || name.includes('key')) return <Music className="h-5 w-5 text-blue-500" />;
    if (name.includes('club') || name.includes('dance')) return <Disc className="h-5 w-5 text-purple-500" />;
    if (name.includes('chill') || name.includes('ambient')) return <Waves className="h-5 w-5 text-teal-500" />;
    if (name.includes('random') || name.includes('eclectic')) return <Shuffle className="h-5 w-5 text-indigo-500" />;
    return <Music className="h-5 w-5 text-primary" />;
  };

  return (
    <Card
      className={`relative cursor-pointer transition-all hover:shadow-md
        ${style.recommended ? 'border-green-500 bg-green-50/60 dark:bg-green-950/20' : 'border-border'}
        ${isSelected ? 'ring-2 ring-primary' : ''}
      `}
      onClick={onSelect}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {style.icon || getStyleIcon(style.name)}
            <CardTitle className="text-base">{style.name}</CardTitle>
          </div>
          <div className="flex gap-1">
            {style.recommended && (
              <Badge className="bg-green-500 text-white flex items-center gap-1 px-2 py-0.5 text-xs">
                <Star size={12} /> Recommended
              </Badge>
            )}
            {isSelected && (
              <Badge className="bg-primary text-white px-2 py-0.5 text-xs">
                Selected
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
          {style.description || 'No description available'}
        </p>
        
        <div className="flex flex-wrap gap-2 mb-2">
          {style.constraints?.bpm_range && (
            <Badge variant="outline" className="text-xs">
              BPM: {style.constraints.bpm_range[0]}–{style.constraints.bpm_range[1]}
            </Badge>
          )}
          
          {style.constraints?.energy_range && (
            <Badge variant="outline" className="text-xs">
              Energy: {style.constraints.energy_range[0]}–{style.constraints.energy_range[1]}
            </Badge>
          )}
        </div>
        
        {style.key_rules && style.key_rules.length > 0 && (
          <div className="text-xs text-muted-foreground mb-2">
            <span className="font-medium">Key Rules:</span> {style.key_rules.join(' + ')}
          </div>
        )}
        
        {typeof style.score === 'number' && (
          <div className="flex items-center gap-2 mt-2">
            <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full ${
                  style.score > 0.7 ? 'bg-green-500' : 
                  style.score > 0.4 ? 'bg-amber-500' : 'bg-red-500'
                }`}
                style={{ width: `${Math.round(style.score * 100)}%` }}
              />
            </div>
            <span className={`text-xs font-medium ${
              style.score > 0.7 ? 'text-green-700 dark:text-green-400' : 
              style.score > 0.4 ? 'text-amber-700 dark:text-amber-400' : 'text-red-700 dark:text-red-400'
            }`}>
              {formatScore(style.score)}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MixStyleSelector;
