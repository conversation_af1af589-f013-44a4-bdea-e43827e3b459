import React from 'react';
import { Track } from '../types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Play, Pause, SkipBack, SkipForward, Music, Volume2 } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { formatDuration, getCompatibilityColor } from '../utils';
import SmartAudioPlayer from '@/components/audio/SmartAudioPlayer';

interface MixPreviewProps {
  track: Track;
  nextTrack?: Track;
  prevTrack?: Track;
  onNext: () => void;
  onPrevious: () => void;
}

/**
 * MixPreview component
 * Displays a preview of the selected track with audio controls
 */
const MixPreview: React.FC<MixPreviewProps> = ({
  track,
  nextTrack,
  prevTrack,
  onNext,
  onPrevious
}) => {
  return (
    <div className="space-y-4">
      {/* Track Info Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{track.title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-muted-foreground">Artist: {track.artist}</div>
            {track.album && (
              <div className="text-muted-foreground">Album: {track.album}</div>
            )}
            {track.genre && (
              <div className="text-muted-foreground">Genre: {track.genre}</div>
            )}
            
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="outline" className="font-mono">
                Key: {track.key}
              </Badge>
              <Badge variant="outline" className="font-mono">
                BPM: {track.bpm.toFixed(1)}
              </Badge>
              <Badge variant="outline">
                Duration: {formatDuration(track.duration)}
              </Badge>
              {track.energy !== undefined && (
                <Badge variant="outline">
                  Energy: {track.energy}/10
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audio Player */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Audio Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <SmartAudioPlayer
            track={track}
            onNext={onNext}
            onPrevious={onPrevious}
          />
        </CardContent>
      </Card>

      {/* Transition Info */}
      {prevTrack && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Previous Transition</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-sm">
                <span className="text-muted-foreground">From: </span>
                <span className="font-medium">{prevTrack.title}</span>
              </div>
              <div className={`text-sm ${getCompatibilityColor(prevTrack, track)}`}>
                {track.transition_style_class 
                  ? track.transition_style_class.replace(/-/g, ' ').replace(/mix/i, '').trim()
                  : track.transitionType}
              </div>
            </div>
            
            <div className="mt-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-muted-foreground">Key change: </span>
                  <span>{prevTrack.key} → {track.key}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">BPM change: </span>
                  <span>{prevTrack.bpm.toFixed(1)} → {track.bpm.toFixed(1)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Next Transition */}
      {nextTrack && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Next Transition</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-sm">
                <span className="text-muted-foreground">To: </span>
                <span className="font-medium">{nextTrack.title}</span>
              </div>
              <div className={`text-sm ${getCompatibilityColor(track, nextTrack)}`}>
                {nextTrack.transition_style_class 
                  ? nextTrack.transition_style_class.replace(/-/g, ' ').replace(/mix/i, '').trim()
                  : nextTrack.transitionType}
              </div>
            </div>
            
            <div className="mt-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-muted-foreground">Key change: </span>
                  <span>{track.key} → {nextTrack.key}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">BPM change: </span>
                  <span>{track.bpm.toFixed(1)} → {nextTrack.bpm.toFixed(1)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Navigation Controls */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onPrevious}
          disabled={!prevTrack}
        >
          <SkipBack className="h-4 w-4 mr-2" />
          Previous Track
        </Button>
        <Button 
          variant="outline" 
          onClick={onNext}
          disabled={!nextTrack}
        >
          Next Track
          <SkipForward className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default MixPreview;
