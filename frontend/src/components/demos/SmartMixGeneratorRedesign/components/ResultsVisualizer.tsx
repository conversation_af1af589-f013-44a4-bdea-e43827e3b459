import React from 'react';
import { Track } from '../types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RefreshCw, Music, ArrowRight } from 'lucide-react';
import { formatDuration, getCompatibilityColor, getCompatibilityBgColor, getTransitionTypeName } from '../utils';
import TrackTransitionBadge from '@/components/tracks/ui/TrackTransitionBadge';
import { formatCoverArtUrl } from '@/utils/apiUrlHelper';

interface ResultsVisualizerProps {
  result: Track[];
  selectedTrackIndex: number | null;
  onTrackSelect: (index: number) => void;
  onReplaceTrack: (track: Track) => void;
}

/**
 * ResultsVisualizer component
 * Displays the generated mix results with track details and transitions
 */
const ResultsVisualizer: React.FC<ResultsVisualizerProps> = ({
  result,
  selectedTrackIndex,
  onTrackSelect,
  onReplaceTrack
}) => {
  if (result.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-12 text-muted-foreground border rounded-md">
        <Music className="h-12 w-12 mb-4 opacity-50" />
        <p className="text-lg font-medium">No mix generated yet</p>
        <p className="text-sm mt-1">
          Configure your mix parameters and click "Generate Mix"
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Generated Mix ({result.length} tracks)</h3>
      
      <ScrollArea className="h-[calc(100vh-250px)]">
        <div className="space-y-1 pr-4">
          {result.map((track, index) => (
            <React.Fragment key={`${track.id}-${index}`}>
              {/* Transition indicator between tracks */}
              {index > 0 && (
                <div className="flex items-center justify-center py-1">
                  <div className="flex items-center">
                    <div className="h-px w-16 bg-border"></div>
                    <span className={`text-xs px-2 ${getCompatibilityColor(result[index - 1], track)}`}>
                      {getTransitionTypeName(track.transitionType)}
                    </span>
                    <div className="h-px w-16 bg-border"></div>
                  </div>
                </div>
              )}
              
              {/* Track card */}
              <TrackCard
                track={track}
                index={index}
                prevTrack={index > 0 ? result[index - 1] : undefined}
                isSelected={selectedTrackIndex === index}
                onSelect={() => onTrackSelect(index)}
                onReplace={() => onReplaceTrack(track)}
              />
            </React.Fragment>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

interface TrackCardProps {
  track: Track;
  index: number;
  prevTrack?: Track;
  isSelected: boolean;
  onSelect: () => void;
  onReplace: () => void;
}

/**
 * TrackCard component
 * Displays a single track in the results list
 */
const TrackCard: React.FC<TrackCardProps> = ({
  track,
  index,
  prevTrack,
  isSelected,
  onSelect,
  onReplace
}) => {
  // Get background color based on compatibility with previous track
  const bgColorClass = prevTrack ? getCompatibilityBgColor(prevTrack, track) : '';
  
  return (
    <Card 
      className={`
        ${isSelected ? 'ring-2 ring-primary' : 'hover:bg-accent/50'} 
        ${bgColorClass}
        transition-all cursor-pointer
      `}
      onClick={onSelect}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="font-medium text-lg w-8 h-8 flex items-center justify-center rounded-full bg-primary/10">
              {index + 1}
            </div>

            {/* Track artwork */}
            <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0 ml-3">
              {track.coverArtUrl || track.file_path ? (
                <img
                  src={formatCoverArtUrl(track.coverArtUrl || track.file_path)}
                  alt={`${track.title} cover`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback to music icon if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : (
                <div className="w-full h-full bg-muted rounded-md flex items-center justify-center">
                  <Music className="h-5 w-5 text-muted-foreground opacity-50" />
                </div>
              )}
              <div className="w-full h-full bg-muted rounded-md flex items-center justify-center hidden">
                <Music className="h-5 w-5 text-muted-foreground opacity-50" />
              </div>
            </div>

            <div className="ml-3">
              <div className="font-medium">{track.title}</div>
              <div className="text-sm text-muted-foreground">{track.artist}</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {index > 0 && (
              <span className={getCompatibilityColor(prevTrack!, track)}>
                <TrackTransitionBadge
                  transitionStyleClass={track.transition_style_class}
                  transitionType={track.transition_style_class ? undefined : track.transitionType}
                />
              </span>
            )}
            <Badge variant="outline" className="font-mono">
              {track.key}
            </Badge>
            <Badge variant="outline" className="font-mono">
              {track.bpm.toFixed(1)}
            </Badge>
            <Badge variant="outline">
              {formatDuration(track.duration)}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onReplace();
              }}
              title="Replace track"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Additional track details */}
        {track.genre && (
          <div className="mt-1 text-xs text-muted-foreground">
            Genre: {track.genre}
          </div>
        )}
        
        {/* Transition details (for tracks after the first one) */}
        {index > 0 && track.transition_style_class && (
          <div className="mt-2 text-xs flex items-center">
            <div className="flex items-center text-muted-foreground">
              <ArrowRight className="h-3 w-3 mr-1" />
              <span className={getCompatibilityColor(prevTrack!, track)}>
                {track.transition_style_class.replace(/-/g, ' ').replace(/mix/i, '').trim()}
              </span>
              <span className="mx-1">transition from</span>
              <span className="font-medium">{prevTrack?.title}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ResultsVisualizer;
