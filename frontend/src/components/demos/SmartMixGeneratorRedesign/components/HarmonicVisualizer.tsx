import React from 'react';
import { Track, MixStats } from '../types';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import HarmonicWheel from '@/components/visualizations/HarmonicWheel';
import { formatScore } from '../utils';

interface HarmonicVisualizerProps {
  tracks: Track[];
  mixStats: MixStats | null;
}

/**
 * HarmonicVisualizer component
 * Displays visualizations of the mix's harmonic properties
 */
const HarmonicVisualizer: React.FC<HarmonicVisualizerProps> = ({
  tracks,
  mixStats
}) => {
  if (tracks.length === 0) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        No tracks to visualize
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Mix Stats Cards */}
      {mixStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatCard 
            title="Overall Score" 
            value={mixStats.overallScore} 
            description="Overall quality of the mix"
          />
          <StatCard 
            title="Harmony" 
            value={mixStats.harmonyScore} 
            description="Key compatibility between tracks"
          />
          <StatCard 
            title="BPM Consistency" 
            value={mixStats.bpmConsistency} 
            description="Tempo flow throughout the mix"
          />
          <StatCard 
            title="Energy Flow" 
            value={mixStats.energyFlow} 
            description="Energy progression and dynamics"
          />
        </div>
      )}

      {/* Visualization Tabs */}
      <Tabs defaultValue="harmonic-wheel">
        <TabsList>
          <TabsTrigger value="harmonic-wheel">Harmonic Wheel</TabsTrigger>
          <TabsTrigger value="key-progression">Key Progression</TabsTrigger>
          <TabsTrigger value="bpm-flow">BPM Flow</TabsTrigger>
        </TabsList>
        
        <TabsContent value="harmonic-wheel" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Harmonic Wheel Visualization</CardTitle>
            </CardHeader>
            <CardContent className="flex justify-center">
              <HarmonicWheel
                tracks={tracks}
                width={400}
                height={400}
                showLabels={true}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="key-progression" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Key Progression</CardTitle>
            </CardHeader>
            <CardContent>
              <KeyProgressionChart tracks={tracks} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="bpm-flow" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">BPM Flow</CardTitle>
            </CardHeader>
            <CardContent>
              <BpmFlowChart tracks={tracks} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

interface StatCardProps {
  title: string;
  value: number;
  description: string;
}

/**
 * StatCard component
 * Displays a single statistic with a progress bar
 */
const StatCard: React.FC<StatCardProps> = ({ title, value, description }) => {
  // Determine color based on value
  const getColorClass = (value: number) => {
    if (value >= 0.8) return 'bg-green-500';
    if (value >= 0.6) return 'bg-blue-500';
    if (value >= 0.4) return 'bg-amber-500';
    return 'bg-red-500';
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatScore(value)}</div>
        <div className="mt-2 w-full h-2 bg-secondary rounded-full overflow-hidden">
          <div
            className={`h-full rounded-full ${getColorClass(value)}`}
            style={{ width: `${Math.round(value * 100)}%` }}
          />
        </div>
        <div className="mt-1 text-xs text-muted-foreground">{description}</div>
      </CardContent>
    </Card>
  );
};

interface KeyProgressionChartProps {
  tracks: Track[];
}

/**
 * KeyProgressionChart component
 * Displays a visualization of the key progression in the mix
 */
const KeyProgressionChart: React.FC<KeyProgressionChartProps> = ({ tracks }) => {
  // Extract key numbers (1-12) from Camelot notation
  const keyNumbers = tracks.map(track => {
    const keyNum = parseInt(track.key.replace(/[AB]$/, ''));
    const keyMode = track.key.slice(-1);
    return { num: keyNum, mode: keyMode, name: track.key };
  });

  // Calculate the height of each bar (max height is 100px)
  const maxHeight = 100;
  
  return (
    <div className="pt-4">
      <div className="flex items-end h-[120px] gap-1">
        {keyNumbers.map((key, index) => {
          // Determine color based on mode (A = major, B = minor)
          const colorClass = key.mode === 'A' 
            ? 'bg-blue-500 dark:bg-blue-600' 
            : 'bg-purple-500 dark:bg-purple-600';
          
          // Calculate height (1-12 mapped to 30-100px)
          const height = 30 + ((key.num - 1) / 11) * 70;
          
          return (
            <div key={index} className="flex flex-col items-center">
              <div 
                className={`w-10 ${colorClass} rounded-t-sm`} 
                style={{ height: `${height}px` }}
                title={`${tracks[index].title} - Key: ${key.name}`}
              />
              <div className="text-xs mt-1 font-mono">{key.name}</div>
              <div className="text-xs text-muted-foreground">{index + 1}</div>
            </div>
          );
        })}
      </div>
      <div className="mt-6 text-sm text-center text-muted-foreground">
        Track position in mix
      </div>
    </div>
  );
};

interface BpmFlowChartProps {
  tracks: Track[];
}

/**
 * BpmFlowChart component
 * Displays a visualization of the BPM flow in the mix
 */
const BpmFlowChart: React.FC<BpmFlowChartProps> = ({ tracks }) => {
  // Find min and max BPM for scaling
  const bpms = tracks.map(track => track.bpm);
  const minBpm = Math.floor(Math.min(...bpms));
  const maxBpm = Math.ceil(Math.max(...bpms));
  const range = maxBpm - minBpm;
  
  // Calculate the height of each bar (max height is 100px)
  const maxHeight = 100;
  
  return (
    <div className="pt-4">
      <div className="flex items-end h-[120px] gap-1">
        {tracks.map((track, index) => {
          // Normalize BPM to a height between 20 and maxHeight
          const height = range === 0 
            ? maxHeight / 2 
            : 20 + ((track.bpm - minBpm) / range) * (maxHeight - 20);
          
          // Determine color based on BPM
          const getColorClass = (bpm: number) => {
            if (bpm < 100) return 'bg-blue-500 dark:bg-blue-600';
            if (bpm < 120) return 'bg-cyan-500 dark:bg-cyan-600';
            if (bpm < 140) return 'bg-green-500 dark:bg-green-600';
            if (bpm < 160) return 'bg-amber-500 dark:bg-amber-600';
            return 'bg-red-500 dark:bg-red-600';
          };
          
          return (
            <div key={index} className="flex flex-col items-center">
              <div 
                className={`w-10 ${getColorClass(track.bpm)} rounded-t-sm`} 
                style={{ height: `${height}px` }}
                title={`${track.title} - BPM: ${track.bpm.toFixed(1)}`}
              />
              <div className="text-xs mt-1">{track.bpm.toFixed(1)}</div>
              <div className="text-xs text-muted-foreground">{index + 1}</div>
            </div>
          );
        })}
      </div>
      <div className="mt-6 text-sm text-center text-muted-foreground">
        Track position in mix
      </div>
    </div>
  );
};

export default HarmonicVisualizer;
