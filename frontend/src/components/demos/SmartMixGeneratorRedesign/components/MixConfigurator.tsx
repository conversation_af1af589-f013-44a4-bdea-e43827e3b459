import React from 'react';
import { MixConfig, Track } from '../types';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  ListMusic, 
  Music, 
  Shuffle, 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Layers 
} from 'lucide-react';

interface MixConfiguratorProps {
  config: MixConfig;
  tracks: Track[];
  onConfigChange: (updates: Partial<MixConfig>) => void;
}

/**
 * MixConfigurator component
 * Allows users to configure mix parameters
 */
const MixConfigurator: React.FC<MixConfiguratorProps> = ({
  config,
  tracks,
  onConfigChange
}) => {
  return (
    <div className="space-y-6">
      {/* Track Count */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <ListMusic className="h-5 w-5 mr-2 text-primary" />
            <CardTitle className="text-base">Number of Tracks</CardTitle>
          </div>
          <CardDescription>
            How many tracks should be in your mix? (3-25)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Slider
              min={3}
              max={25}
              step={1}
              value={[config.trackCount]}
              onValueChange={(value) => onConfigChange({ trackCount: value[0] })}
              className="flex-1"
            />
            <Input
              type="number"
              min={3}
              max={25}
              value={config.trackCount}
              onChange={(e) => onConfigChange({ trackCount: parseInt(e.target.value) || 3 })}
              className="w-20"
            />
          </div>
        </CardContent>
      </Card>

      {/* First Track Selection */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <Music className="h-5 w-5 mr-2 text-primary" />
            <CardTitle className="text-base">First Track Selection</CardTitle>
          </div>
          <CardDescription>
            Choose how to select the first track of your mix
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={config.firstTrackOption}
            onValueChange={(value) => onConfigChange({ firstTrackOption: value as 'random' | 'select' })}
            className="flex flex-col gap-3"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="random" id="random" />
              <Label htmlFor="random" className="flex items-center cursor-pointer">
                <Shuffle className="h-4 w-4 mr-2 text-muted-foreground" />
                Random Selection
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="select" id="select" />
              <Label htmlFor="select" className="flex items-center cursor-pointer">
                <Music className="h-4 w-4 mr-2 text-muted-foreground" />
                Choose Specific Track
              </Label>
            </div>
          </RadioGroup>

          {/* Specific First Track Selection - Only show if "select" is chosen */}
          {config.firstTrackOption === 'select' && (
            <div className="mt-4">
              <Select
                value={config.firstTrack}
                onValueChange={(value) => onConfigChange({ firstTrack: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a track" />
                </SelectTrigger>
                <SelectContent>
                  {tracks.map((track) => (
                    <SelectItem key={track.id} value={track.id}>
                      {track.artist} - {track.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Power Block Size */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <Layers className="h-5 w-5 mr-2 text-primary" />
            <CardTitle className="text-base">Power Blocks</CardTitle>
          </div>
          <CardDescription>
            Power blocks are sequences of tracks in the same key for enhanced mixing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-4">
            <Checkbox
              checked={config.powerBlockSize > 0}
              onCheckedChange={(checked) => {
                onConfigChange({ powerBlockSize: checked ? 3 : 0 });
              }}
              id="power-block"
            />
            <Label htmlFor="power-block" className="cursor-pointer">
              Use Power Blocks
            </Label>
          </div>

          {config.powerBlockSize > 0 && (
            <div className="flex items-center gap-4 mt-2">
              <Slider
                min={2}
                max={5}
                step={1}
                value={[config.powerBlockSize]}
                onValueChange={(value) => onConfigChange({ powerBlockSize: value[0] })}
                className="flex-1"
              />
              <Input
                type="number"
                min={2}
                max={5}
                value={config.powerBlockSize}
                onChange={(e) => onConfigChange({ powerBlockSize: parseInt(e.target.value) || 2 })}
                className="w-20"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* BPM Trend */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-primary" />
            <CardTitle className="text-base">BPM Trend</CardTitle>
          </div>
          <CardDescription>
            How should the tempo evolve throughout the mix?
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={config.bpmTrend}
            onValueChange={(value) => onConfigChange({ bpmTrend: value as 'steady' | 'rising' | 'falling' | 'random' })}
            className="grid grid-cols-2 gap-3"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="steady" id="steady" />
              <Label htmlFor="steady" className="flex items-center cursor-pointer">
                <Minus className="h-4 w-4 mr-2 text-muted-foreground" />
                Steady
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="rising" id="rising" />
              <Label htmlFor="rising" className="flex items-center cursor-pointer">
                <TrendingUp className="h-4 w-4 mr-2 text-muted-foreground" />
                Rising
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="falling" id="falling" />
              <Label htmlFor="falling" className="flex items-center cursor-pointer">
                <TrendingDown className="h-4 w-4 mr-2 text-muted-foreground" />
                Falling
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="random" id="random-bpm" />
              <Label htmlFor="random-bpm" className="flex items-center cursor-pointer">
                <Shuffle className="h-4 w-4 mr-2 text-muted-foreground" />
                Random
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>
    </div>
  );
};

export default MixConfigurator;
