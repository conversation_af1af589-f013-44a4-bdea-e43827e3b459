import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTrackReplacements } from '@/components/mixes/generators/smart/hooks';
import TrackReplaceModal from '@/components/tracks/replacements/TrackReplaceModal';

// Real track data for testing
const mockTrack = {
  id: '1',
  title: '5 - <PERSON><PERSON>',
  artist: '9A',
  key: '9A',
  bpm: 102,
  duration: 278,
  energy: 5,
  genre: 'House',
  album: 'Test Album'
};

const mockMix = [
  mockTrack,
  {
    id: '2',
    title: '6 - Ant Antic - loveisop (Night Version)',
    artist: '2A',
    key: '2A',
    bpm: 107,
    duration: 263,
    energy: 6,
    genre: 'House',
    album: 'Album 2'
  },
  {
    id: '3',
    title: '5 - Dr Nord Boy - Runner',
    artist: '12A',
    key: '12A',
    bpm: 124,
    duration: 311,
    energy: 5,
    genre: 'House',
    album: 'Album 3'
  }
];

const ReplaceTrackDebug: React.FC = () => {
  const [selectedCollectionId] = useState('fc069ace275da98e720fb3f2f18eea18');
  const [selectedFolderId] = useState('ALL');
  
  const {
    trackToReplace,
    isReplaceModalOpen,
    replacements,
    isLoadingReplacements,
    setIsReplaceModalOpen,
    loadReplacements
  } = useTrackReplacements(selectedCollectionId, selectedFolderId);

  const handleTestReplace = () => {
    console.log('🧪 [DEBUG] Testing track replacement...');
    loadReplacements(
      mockTrack,
      mockMix,
      'club', // Use a real mix style ID
      '0'
    );
  };

  const handleReplacementSelect = (replacement: any) => {
    console.log('🧪 [DEBUG] Replacement selected:', replacement);
    setIsReplaceModalOpen(false);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Replace Track Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Test Parameters:</h3>
            <div className="text-sm text-muted-foreground space-y-1">
              <div>Collection ID: {selectedCollectionId}</div>
              <div>Folder ID: {selectedFolderId}</div>
              <div>Track to Replace: {mockTrack.title} by {mockTrack.artist}</div>
              <div>Mix Length: {mockMix.length} tracks</div>
            </div>
          </div>
          
          <Button onClick={handleTestReplace} disabled={isLoadingReplacements}>
            {isLoadingReplacements ? 'Loading...' : 'Test Replace Track'}
          </Button>
          
          <div>
            <h3 className="font-medium mb-2">Status:</h3>
            <div className="text-sm space-y-1">
              <div>Modal Open: {isReplaceModalOpen ? 'Yes' : 'No'}</div>
              <div>Loading: {isLoadingReplacements ? 'Yes' : 'No'}</div>
              <div>Replacements Found: {replacements.length}</div>
              <div>Track to Replace: {trackToReplace?.title || 'None'}</div>
            </div>
          </div>
          
          {replacements.length > 0 && (
            <div>
              <h3 className="font-medium mb-2">Replacements:</h3>
              <div className="text-sm space-y-1">
                {replacements.slice(0, 3).map((r, i) => (
                  <div key={i}>
                    {r.track.title} by {r.track.artist} (Score: {(r.compatibility_score * 100).toFixed(1)}%)
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <TrackReplaceModal
        open={isReplaceModalOpen}
        onOpenChange={setIsReplaceModalOpen}
        track={trackToReplace}
        replacements={replacements}
        isLoading={isLoadingReplacements}
        onSelect={handleReplacementSelect}
      />
    </div>
  );
};

export default ReplaceTrackDebug;
