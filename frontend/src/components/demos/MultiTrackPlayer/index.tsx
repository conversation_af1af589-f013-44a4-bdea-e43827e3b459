import React, { useState, useEffect, useCallback, useRef } from 'react';
import Track from './Track';
import TransitionBlock from './TransitionBlock';
import { Track as TrackType, Transition as TransitionType } from './types';
import * as Tone from 'tone';

const MultiTrackPlayer: React.FC = () => {
  // Global state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [totalDuration, setTotalDuration] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);
  const [activeTrackId, setActiveTrackId] = useState<string | null>(null);

  // Refs for Tone.js
  const transportRef = useRef<ReturnType<typeof Tone.getTransport> | null>(null);

  // Track data with sequential positioning
  const [tracks, setTracks] = useState<TrackType[]>([
    {
      id: 'track1',
      name: 'Track 1',
      url: '/song.mp3',
      startTime: 0, // First track starts at 0
      duration: 0, // Will be set when track is loaded
      endTime: 0,  // Will be calculated
      volume: 80,

      isActive: false,
      isPlaying: false,
      inTransition: false,
      key: '2A',
      bpm: 120,
      color: '#4F46E5' // Indigo
    },
    {
      id: 'track2',
      name: 'Track 2',
      url: '/song2.mp3',
      startTime: 0, // Will be updated when track1 duration is known
      duration: 0,
      endTime: 0,
      volume: 80,

      isActive: false,
      isPlaying: false,
      inTransition: false,
      key: '3B',
      bpm: 128,
      color: '#7C3AED' // Purple
    },
    {
      id: 'track3',
      name: 'Track 3',
      url: '/song3.mp3',
      startTime: 0, // Will be updated when track2 duration is known
      duration: 0,
      endTime: 0,
      volume: 80,

      isActive: false,
      isPlaying: false,
      inTransition: false,
      key: '5A',
      bpm: 124,
      color: '#EC4899' // Pink
    }
  ]);

  // Transitions between tracks
  const [transitions, setTransitions] = useState<TransitionType[]>([]);

  // Make transitions available globally for track components to access
  useEffect(() => {
    (window as any).transitions = transitions;
  }, [transitions]);

  // Initialize Tone.js on user interaction
  const initializeTone = async () => {
    if (isInitialized) return;

    try {
      // Start Tone.js context with user gesture
      await Tone.start();

      // Use the existing Tone.js context instead of creating a new one
      const toneContext = Tone.getContext();

      // Ensure the context is running
      if (toneContext.state !== 'running') {
        await toneContext.resume();
      }

      // Store Transport reference using getTransport
      transportRef.current = Tone.getTransport();

      // Set initial Transport state
      transportRef.current.stop();
      transportRef.current.position = 0;

      // Set volume to ensure audio is audible
      Tone.getDestination().volume.value = 0; // 0dB = normal volume

      // Ensure audio is enabled
      const allAudioElements = document.querySelectorAll('audio');
      allAudioElements.forEach(audio => {
        // Try to briefly play and immediately pause to "unlock" audio
        audio.volume = 0;
        audio.play().catch(() => {}).then(() => {
          audio.pause();
          audio.volume = 1;
        });
      });

      setIsInitialized(true);
    } catch (error) {
      console.error('Error starting Tone.js:', error);
    }
  };

  // Handle play/pause
  const handlePlayPause = () => {
    if (!isInitialized) {
      initializeTone();
      return;
    }

    if (!isAudioLoaded) {
      return;
    }

    if (isPlaying) {
      // Pause playback
      transportRef.current?.pause();
      setIsPlaying(false);
    } else {
      // Start playback
      transportRef.current?.start();
      setIsPlaying(true);
    }
  };

  // Update which track is active based on the current time
  const updateActiveTrack = useCallback((time: number) => {
    setTracks(prevTracks => {
      // First, deactivate all tracks and reset transition state
      let updatedTracks = prevTracks.map(track => ({
        ...track,
        isActive: false,
        inTransition: false
      }));

      // Check if we're in a transition
      const activeTransition = transitions.find(
        t => time >= t.startTime && time < (t.startTime + t.duration)
      );

      if (activeTransition) {
        // We're in a transition - both tracks should be active
        const fromTrackIndex = updatedTracks.findIndex(t => t.id === activeTransition.fromTrackId);
        const toTrackIndex = updatedTracks.findIndex(t => t.id === activeTransition.toTrackId);

        if (fromTrackIndex !== -1 && toTrackIndex !== -1) {
          // Calculate transition progress (0-1)
          const transitionProgress = (time - activeTransition.startTime) / activeTransition.duration;

          // Update from track
          updatedTracks[fromTrackIndex] = {
            ...updatedTracks[fromTrackIndex],
            isActive: true,
            inTransition: true
          };

          // Update to track
          updatedTracks[toTrackIndex] = {
            ...updatedTracks[toTrackIndex],
            isActive: true,
            inTransition: true
          };

          // Set the primary active track based on transition progress
          if (transitionProgress < 0.5) {
            setActiveTrackId(activeTransition.fromTrackId);
          } else {
            setActiveTrackId(activeTransition.toTrackId);
          }

          // Apply volume changes based on transition settings
          // This will be handled in the Track component
        }
      } else {
        // Not in a transition - find which single track should be active
        let activeTrackIndex = -1;
        for (let i = 0; i < updatedTracks.length; i++) {
          const track = updatedTracks[i];
          if (time >= track.startTime && time < track.endTime) {
            activeTrackIndex = i;
            break;
          }
        }

        // If we found an active track, update it
        if (activeTrackIndex !== -1) {
          updatedTracks[activeTrackIndex] = {
            ...updatedTracks[activeTrackIndex],
            isActive: true
          };
          setActiveTrackId(updatedTracks[activeTrackIndex].id);
        } else {
          // If no track is active (e.g., we're at the very end), activate the last track
          if (time >= totalDuration && updatedTracks.length > 0) {
            const lastTrackIndex = updatedTracks.length - 1;
            updatedTracks[lastTrackIndex] = {
              ...updatedTracks[lastTrackIndex],
              isActive: true
            };
            setActiveTrackId(updatedTracks[lastTrackIndex].id);
          } else {
            setActiveTrackId(null);
          }
        }
      }

      return updatedTracks;
    });
  }, [totalDuration, transitions]);

  // Handle seeking in the timeline
  const handleSeek = (position: number) => {
    if (!isInitialized || !transportRef.current) return;

    // Store current play state
    const wasPlaying = isPlaying;

    // Get current active track before seeking
    const currentActiveTrackId = activeTrackId;

    // Check if we're seeking to a transition
    const seekingToTransition = transitions.some(
      t => position >= t.startTime && position < (t.startTime + t.duration)
    );

    // Find which track(s) will be active at the new position
    let newActiveTrackIds: string[] = [];

    if (seekingToTransition) {
      // If seeking to a transition, find the transition and get both track IDs
      const transition = transitions.find(
        t => position >= t.startTime && position < (t.startTime + t.duration)
      );

      if (transition) {
        newActiveTrackIds.push(transition.fromTrackId);
        newActiveTrackIds.push(transition.toTrackId);
      }
    } else {
      // If not in a transition, find the single active track
      for (const track of tracks) {
        if (position >= track.startTime && position < track.endTime) {
          newActiveTrackIds.push(track.id);
          break;
        }
      }
    }

    // Only pause if we're changing tracks (not when seeking within the same track or transition)
    // This prevents unnecessary pausing when clicking within the same track
    const changingTracks =
      currentActiveTrackId !== null &&
      !newActiveTrackIds.includes(currentActiveTrackId) &&
      newActiveTrackIds.length > 0;

    if (wasPlaying && changingTracks) {
      // Pause transport without updating state to avoid re-renders
      transportRef.current.pause();
    }

    // Update Tone.Transport position
    transportRef.current.seconds = position;

    // Update current time display
    setCurrentTime(position);

    // Determine which track should be active at this position
    updateActiveTrack(position);

    // Resume playback if it was playing before and we paused
    if (wasPlaying && changingTracks) {
      // Small delay to ensure seek completes
      setTimeout(() => {
        if (transportRef.current) {
          transportRef.current.start();
        }
      }, 50);
    }
  };

  // Handle track ready - position tracks sequentially
  const handleTrackReady = useCallback((trackId: string, duration: number) => {
    setTracks(prevTracks => {
      // Find the track that's ready
      const trackIndex = prevTracks.findIndex(t => t.id === trackId);
      if (trackIndex === -1) return prevTracks;

      // Create a new array to avoid mutation
      const updatedTracks = [...prevTracks];

      // Update the track's duration and endTime
      updatedTracks[trackIndex] = {
        ...updatedTracks[trackIndex],
        duration,
        endTime: updatedTracks[trackIndex].startTime + duration
      };

      // Recalculate all track positions to ensure proper sequential positioning
      // Start with the first track at position 0
      for (let i = 0; i < updatedTracks.length; i++) {
        if (i === 0) {
          // First track always starts at 0
          updatedTracks[i] = {
            ...updatedTracks[i],
            startTime: 0,
            endTime: updatedTracks[i].duration
          };
        } else {
          // Each subsequent track starts when the previous track ends
          const prevTrack = updatedTracks[i - 1];
          updatedTracks[i] = {
            ...updatedTracks[i],
            startTime: prevTrack.endTime,
            endTime: prevTrack.endTime + updatedTracks[i].duration
          };
        }
      }

      // Calculate total duration based on the last track's endTime
      const lastTrack = updatedTracks[updatedTracks.length - 1];
      const calculatedTotalDuration = lastTrack.startTime + lastTrack.duration;

      // Update total duration state
      setTotalDuration(calculatedTotalDuration);

      // Create transitions between tracks
      for (let i = 1; i < updatedTracks.length; i++) {
        const fromTrack = updatedTracks[i - 1];
        const toTrack = updatedTracks[i];

        // Create a transition with a default duration of 10 seconds
        const transitionDuration = 10;
        const transition: TransitionType = {
          id: `transition-${fromTrack.id}-${toTrack.id}`,
          fromTrackId: fromTrack.id,
          toTrackId: toTrack.id,
          startTime: toTrack.startTime - transitionDuration,
          duration: transitionDuration,
          trackOffset: 0,
          isExpanded: false,

          // Default transition type and point
          type: "beatmatch",
          transitionPoint: 0.5, // 50% through the transition

          // Default crossfade properties
          fadeOutCurve: 'linear',
          fadeInCurve: 'linear',

          // Default volume levels
          fromTrackStartVolume: 80,
          fromTrackEndVolume: 0,
          toTrackStartVolume: 0,
          toTrackEndVolume: 80,

          // Default EQ settings
          eqSettings: {
            lowCut: 0,
            midCut: 0,
            highCut: 0
          },

          // Default effects
          effects: [],

          // Default notes
          notes: ""
        };

        // Add the transition
        setTransitions(prevTransitions => {
          // Check if this transition already exists
          const existingIndex = prevTransitions.findIndex(
            t => t.fromTrackId === fromTrack.id && t.toTrackId === toTrack.id
          );

          if (existingIndex !== -1) {
            // Update existing transition
            const updatedTransitions = [...prevTransitions];
            updatedTransitions[existingIndex] = transition;
            return updatedTransitions;
          } else {
            // Add new transition
            return [...prevTransitions, transition];
          }
        });
      }

      // Mark the first track as active initially
      if (trackIndex === 0) {
        updatedTracks[0] = {
          ...updatedTracks[0],
          isActive: true
        };
        setActiveTrackId(updatedTracks[0].id);
      }

      // Check if all tracks have loaded
      const allTracksLoaded = updatedTracks.every(track => track.duration > 0);
      if (allTracksLoaded) {
        setIsAudioLoaded(true);

        // Find which track should be active at the current time
        const time = currentTime;
        let activeTrackFound = false;

        for (let i = 0; i < updatedTracks.length; i++) {
          const track = updatedTracks[i];
          if (time >= track.startTime && time < track.endTime) {
            // Set this track as active
            updatedTracks[i] = {
              ...updatedTracks[i],
              isActive: true
            };
            setActiveTrackId(updatedTracks[i].id);
            activeTrackFound = true;
            break;
          } else {
            // Ensure this track is not active
            updatedTracks[i] = {
              ...updatedTracks[i],
              isActive: false
            };
          }
        }

        // If no track is active, set the first track as active
        if (!activeTrackFound && updatedTracks.length > 0) {
          updatedTracks[0] = {
            ...updatedTracks[0],
            isActive: true
          };
          setActiveTrackId(updatedTracks[0].id);
        }
      }

      return updatedTracks;
    });
  }, [currentTime]);

  // Handle volume change
  const handleVolumeChange = useCallback((trackId: string, volume: number) => {
    setTracks(prevTracks =>
      prevTracks.map(track =>
        track.id === trackId
          ? { ...track, volume }
          : track
      )
    );
  }, []);

  // Update current time based on Tone.Transport using requestAnimationFrame with throttling
  useEffect(() => {
    if (!isInitialized) return;

    // For throttling updates (only update every 50ms)
    let lastUpdateTime = 0;
    const throttleInterval = 50; // ms between updates

    // Function to update current time from Tone.Transport
    const updateTime = () => {
      const now = performance.now();

      // Get transport time safely
      const transportTime = transportRef.current?.seconds ?? 0;

      // Only update state if enough time has passed (throttling)
      if (now - lastUpdateTime >= throttleInterval) {
        setCurrentTime(transportTime);
        updateActiveTrack(transportTime);
        lastUpdateTime = now;
      }

      // Request next animation frame
      animationRef.current = requestAnimationFrame(updateTime);
    };

    // Start the animation frame loop
    const animationRef = { current: requestAnimationFrame(updateTime) };

    // Clean up animation frame on unmount or when dependencies change
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isInitialized, updateActiveTrack]);

  // Handle timeline click for seeking
  const handleTimelineClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isAudioLoaded || !isInitialized || !transportRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const newTime = clickPosition * totalDuration;

    // Use the same seek handler to ensure consistent behavior
    handleSeek(newTime);
  };

  // Render track blocks in the timeline
  const renderTrackBlocks = () => {
    return tracks.map(track => {
      if (track.duration === 0) return null; // Skip tracks that haven't loaded yet

      // Calculate position and width as percentages
      const startPercent = (track.startTime / totalDuration) * 100;
      const widthPercent = (track.duration / totalDuration) * 100;

      return (
        <div
          key={track.id}
          className={`track-block absolute h-full rounded-md ${track.isActive ? 'opacity-100' : 'opacity-70'}`}
          style={{
            left: `${startPercent}%`,
            width: `${widthPercent}%`,
            backgroundColor: track.color || '#4F46E5',
            top: 0
          }}
          title={`${track.name} (${formatTime(track.startTime)} - ${formatTime(track.endTime)})`}
        >
          <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-xs font-bold whitespace-nowrap">
            {track.name}
          </span>
        </div>
      );
    });
  };

  // Render transition blocks in the timeline
  const renderTransitionBlocks = () => {
    return transitions.map(transition => {
      if (totalDuration === 0) return null; // Skip if total duration is not set yet

      // Calculate position and width as percentages
      const startPercent = (transition.startTime / totalDuration) * 100;
      const widthPercent = (transition.duration / totalDuration) * 100;

      return (
        <div
          key={transition.id}
          className="transition-block absolute h-full bg-gradient-to-r from-purple-500 to-pink-500 opacity-70 rounded-md"
          style={{
            left: `${startPercent}%`,
            width: `${widthPercent}%`,
            top: 0,
            zIndex: 5
          }}
          title={`Transition (${formatTime(transition.startTime)} - ${formatTime(transition.startTime + transition.duration)})`}
        >
          <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-xs font-bold">
            Mix
          </span>
        </div>
      );
    });
  };

  return (
    <div className="multitrack-player">
      <div className="global-controls bg-gray-100 p-4 rounded-md mb-4">
        <div className="flex items-center gap-4 mb-2">
          <button
            onClick={handlePlayPause}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            {isPlaying ? 'Pause' : 'Play'}
          </button>

          <div className="time-display font-mono">
            {formatTime(currentTime)} / {formatTime(totalDuration)}
          </div>

          <div className="active-track ml-4">
            {activeTrackId ? (
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm">
                Active: {tracks.find(t => t.id === activeTrackId)?.name || 'None'}
              </span>
            ) : (
              <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-md text-sm">
                No active track
              </span>
            )}
          </div>
        </div>

        <div
          className="timeline h-12 bg-gray-200 rounded-md cursor-pointer relative mb-2"
          onClick={handleTimelineClick}
        >
          {/* Track blocks */}
          {renderTrackBlocks()}

          {/* Transition blocks */}
          {renderTransitionBlocks()}

          {/* Playhead */}
          <div
            className="playhead absolute top-0 h-full w-0.5 bg-red-500 z-10"
            style={{ left: `${totalDuration > 0 ? (currentTime / totalDuration) * 100 : 0}%` }}
          />

          <div className="timeline-markers absolute top-0 left-0 w-full h-full flex">
            {/* Generate time markers every 30 seconds */}
            {Array.from({ length: Math.ceil(totalDuration / 30) + 1 }).map((_, i) => (
              <div
                key={i}
                className="marker absolute h-full border-l border-gray-400"
                style={{ left: `${(i * 30 / totalDuration) * 100}%` }}
              >
                <span className="absolute -top-6 -left-4 text-xs">
                  {formatTime(i * 30)}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="text-xs text-gray-500 mb-2">
          Click on the timeline to seek. Tracks will play sequentially based on their position.
        </div>
      </div>

      <div className="tracks-and-transitions-container">
        {tracks.map((track, index) => {
          // Find transition that connects this track to the next track
          const nextTrack = tracks[index + 1];
          const transition = nextTrack
            ? transitions.find(t =>
                t.fromTrackId === track.id && t.toTrackId === nextTrack.id
              )
            : null;

          return (
            <React.Fragment key={track.id}>
              {/* Current Track */}
              <Track
                track={track}
                currentTime={currentTime}
                isPlaying={isPlaying && track.isActive}
                onReady={handleTrackReady}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onSeek={handleSeek}
                onVolumeChange={handleVolumeChange}
              />

              {/* Transition to next track (if exists) */}
              {transition && (
                <TransitionBlock
                  key={transition.id}
                  transition={transition}
                  tracks={tracks}
                  currentTime={currentTime}
                  isPlaying={isPlaying}
                  onTransitionChange={(updatedTransition: TransitionType) => {
                    setTransitions(prevTransitions =>
                      prevTransitions.map(t =>
                        t.id === updatedTransition.id ? updatedTransition : t
                      )
                    );
                  }}
                  onSeek={handleSeek}
                />
              )}
            </React.Fragment>
          );
        })}

        {/* Display any transitions that don't fit the sequential pattern */}
        {transitions.filter(t =>
          !tracks.some((track, i) =>
            i < tracks.length - 1 &&
            track.id === t.fromTrackId &&
            tracks[i + 1].id === t.toTrackId
          )
        ).length > 0 && (
          <div className="other-transitions-container mt-8">
            <h2 className="text-xl font-bold mb-4">Other Transitions</h2>
            {transitions.filter(t =>
              !tracks.some((track, i) =>
                i < tracks.length - 1 &&
                track.id === t.fromTrackId &&
                tracks[i + 1].id === t.toTrackId
              )
            ).map(transition => (
              <TransitionBlock
                key={transition.id}
                transition={transition}
                tracks={tracks}
                currentTime={currentTime}
                isPlaying={isPlaying}
                onTransitionChange={(updatedTransition: TransitionType) => {
                  setTransitions(prevTransitions =>
                    prevTransitions.map(t =>
                      t.id === updatedTransition.id ? updatedTransition : t
                    )
                  );
                }}
                onSeek={handleSeek}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to format time
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

export default MultiTrackPlayer;
