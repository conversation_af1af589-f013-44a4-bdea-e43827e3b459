import React, { useRef, useEffect, useState, useCallback } from 'react';
import WaveSurfer from 'wavesurfer.js';
import * as Tone from 'tone';
import { Track as TrackType } from './types';

interface TrackProps {
  track: TrackType;
  isPlaying: boolean;
  currentTime: number; // Current global timeline position
  onReady: (trackId: string, duration: number) => void;
  onPlay: () => void;
  onPause: () => void;
  onSeek: (time: number) => void;
  onVolumeChange: (trackId: string, volume: number) => void;
}

const Track: React.FC<TrackProps> = ({
  track,
  isPlaying,
  currentTime,
  onReady,
  onPlay,
  onPause,
  onSeek,
  onVolumeChange
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const mediaNodeRef = useRef<MediaElementAudioSourceNode | null>(null);
  const gainNodeRef = useRef<Tone.Gain | null>(null);
  const lastPlayStateRef = useRef<boolean>(false);
  const lastPositionRef = useRef<number>(-1); // Track last position to avoid redundant updates
  const animationFrameRef = useRef<number | null>(null); // For requestAnimationFrame
  const lastUpdateTimeRef = useRef<number>(0); // For throttling updates

  const [isInitialized, setIsInitialized] = useState(false);
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);
  const [localIsPlaying, setLocalIsPlaying] = useState(false);

  // Initialize audio element
  useEffect(() => {
    if (!audioRef.current) {
      // Create a new audio element
      const audio = new Audio();
      audio.crossOrigin = 'anonymous';
      audio.preload = 'auto';

      // Set the source
      audio.src = track.url;

      // Store the audio element
      audioRef.current = audio;

      // Audio element created
    }
  }, [track.id, track.url]);

  // Initialize WaveSurfer immediately on component mount and load audio
  useEffect(() => {
    // Only create if we don't have an instance and container is ready
    if (wavesurferRef.current || !containerRef.current) return;

    // Creating WaveSurfer instance

    try {
      // Create WaveSurfer instance
      const wavesurfer = WaveSurfer.create({
        container: containerRef.current,
        height: 120,
        waveColor: '#4F46E5',
        progressColor: '#FF6B6B', // Brighter, more visible progress color
        cursorColor: '#C7D2FE',
        barWidth: 2,
        barGap: 1,
        barRadius: 2,
        dragToSeek: true,
      });

      // Store the instance in ref
      wavesurferRef.current = wavesurfer;

      // Add event handlers
      wavesurfer.on('ready', () => {
        setIsAudioLoaded(true);
        onReady(track.id, wavesurfer.getDuration());

        // Set initial waveform position based on current timeline position
        if (currentTime < track.startTime) {
          wavesurfer.seekTo(0); // Track hasn't started yet
        } else if (currentTime >= track.endTime) {
          wavesurfer.seekTo(1.0); // Track has finished
        } else {
          // Track is currently active
          const trackPosition = currentTime - track.startTime;
          const relativePosition = trackPosition / wavesurfer.getDuration();
          wavesurfer.seekTo(relativePosition);
        }
      });

      wavesurfer.on('play', () => {
        onPlay();
      });

      wavesurfer.on('pause', () => {
        onPause();
      });

      wavesurfer.on('seeking', () => {
        const trackTime = wavesurfer.getCurrentTime();
        // Convert track time to global timeline time
        const globalTime = track.startTime + trackTime;
        onSeek(globalTime);
      });

      // Error handling
      wavesurfer.on('error', (err) => {
        console.error(`Error loading track ${track.id}:`, err);
      });

      // Load the audio automatically
      wavesurfer.load(track.url);
    } catch (error) {
      console.error(`Error creating WaveSurfer for track ${track.id}:`, error);
    }

    // Cleanup on unmount
    return () => {
      if (wavesurferRef.current) {
        try {
          // Clean up WaveSurfer instance
          // First pause playback to avoid race conditions
          wavesurferRef.current.pause();

          // Remove all event listeners first
          wavesurferRef.current.unAll();

          // Destroy immediately
          wavesurferRef.current.destroy();
          wavesurferRef.current = null;
        } catch (error) {
          console.error('Error during wavesurfer cleanup:', error);
        }
      }
    };
  }, [track.id, track.url]); // Include track.id and track.url to ensure proper loading

  // Initialize Tone.js and connect to WaveSurfer
  useEffect(() => {
    const wavesurfer = wavesurferRef.current;
    // Skip if wavesurfer is not ready or already initialized
    if (!wavesurfer || isInitialized) return;

    // Setup connection when WaveSurfer is ready
    const handleReady = async () => {
      try {
        // Set up Tone.js for this track

        // Start Tone.js context if not already started
        await Tone.start();
        // Tone.js context is now active

        // Get the media element from WaveSurfer
        const mediaElement = wavesurfer.getMediaElement();
        if (!mediaElement) {
          console.error(`No media element found for track ${track.id}`);
          return;
        }

        // Media element is available

        try {
          // Check if we already have a media node for this track
          if (!mediaNodeRef.current) {
            // Use the shared Tone context instead of creating a new one
            const toneContext = Tone.getContext();

            try {
              // Create a media element source node
              const mediaNode = toneContext.createMediaElementSource(mediaElement);
              mediaNodeRef.current = mediaNode;

              // Create a gain node for volume control
              const gainNode = new Tone.Gain(track.volume / 100);
              gainNode.connect(Tone.getDestination());
              gainNodeRef.current = gainNode;

              // Connect the media node to the gain node
              mediaNode.connect(gainNode);
            } catch (err) {
              // If media element is already connected, just create a gain node
              console.warn(`Media element already connected, using existing connection for track ${track.id}`);

              // Just create a gain node for volume control
              const gainNode = new Tone.Gain(track.volume / 100);
              gainNode.connect(Tone.getDestination());
              gainNodeRef.current = gainNode;
            }
          }

          setIsInitialized(true);
          // Tone.js is now initialized for this track
        } catch (err) {
          // If we get an error about the media element already being connected,
          // it means we're good to go
          console.warn(`Media element already connected for track ${track.id}:`, err);
          setIsInitialized(true);
        }
      } catch (error) {
        console.error(`Error initializing Tone.js for track ${track.id}:`, error);
      }
    };

    // Add the ready handler
    wavesurfer.on('ready', handleReady);

    // Cleanup
    return () => {
      wavesurfer.un('ready', handleReady);

      if (gainNodeRef.current) {
        try {
          gainNodeRef.current.dispose();
          gainNodeRef.current = null;
        } catch (error) {
          console.error(`Error disposing gain node for track ${track.id}:`, error);
        }
      }

      if (mediaNodeRef.current) {
        try {
          // No need to explicitly disconnect - Tone.js handles this
          mediaNodeRef.current = null;
        } catch (error) {
          console.error(`Error cleaning up media node for track ${track.id}:`, error);
        }
      }
    };
  }, [track.id, track.volume, isInitialized]);

  // Handle volume changes
  useEffect(() => {
    // Only update if we have a gain node and the track is initialized
    if (gainNodeRef.current && isInitialized) {
      try {
        // If track is in a transition, volume will be controlled by the transition logic
        // Otherwise, use the track's volume setting
        if (!track.inTransition) {
          gainNodeRef.current.gain.value = track.volume / 100;
        }
        // Volume updated
      } catch (error) {
        console.error(`Error setting volume for track ${track.id}:`, error);
      }
    }
  }, [track.volume, track.id, isInitialized, track.inTransition]);

  // Improved play/pause handling with optimizations
  useEffect(() => {
    const wavesurfer = wavesurferRef.current;
    if (!wavesurfer || !isAudioLoaded) return;

    // Check if play state has actually changed to avoid unnecessary operations
    const shouldBePlaying = isPlaying && track.isActive;
    const isCurrentlyPlaying = wavesurfer.isPlaying();
    const playStateChanged = shouldBePlaying !== isCurrentlyPlaying;

    // Store current play state for future reference
    const previousPlayState = lastPlayStateRef.current;
    lastPlayStateRef.current = shouldBePlaying;

    // Update local state for UI only if needed
    if (shouldBePlaying !== localIsPlaying) {
      setLocalIsPlaying(shouldBePlaying);
    }

    try {
      // Calculate the position within the track based on global timeline
      const trackPosition = currentTime - track.startTime;
      const inTrackRange = trackPosition >= 0 && trackPosition < track.duration;

      if (shouldBePlaying && inTrackRange) {
        // Calculate relative position for seeking
        const relativePosition = trackPosition / track.duration;

        // Only seek if position has changed significantly or play state changed
        const positionChanged = Math.abs(lastPositionRef.current - relativePosition) > 0.001;

        if (positionChanged || playStateChanged || !previousPlayState) {
          // Set the position within the track if needed
          if (positionChanged) {
            wavesurfer.seekTo(relativePosition);
            lastPositionRef.current = relativePosition;

            // Update audio element position only when necessary
            if (audioRef.current && Math.abs(audioRef.current.currentTime - trackPosition) > 0.1) {
              audioRef.current.currentTime = trackPosition;
            }
          }

          // Start playback only if not already playing and we're actually in play mode
          // Don't try to play if we're just seeking (isPlaying is false)
          if (!isCurrentlyPlaying && isPlaying) {
            // Update wavesurfer visual state
            wavesurfer.play();

            // For audio playback, play immediately for better responsiveness
            // This works because we're now only pausing when changing tracks
            if (audioRef.current && audioRef.current.paused && isPlaying) {
              try {
                audioRef.current.play().catch(err => {
                  // Ignore AbortError as it's expected during rapid seeking
                  if (err.name !== 'AbortError') {
                    console.warn(`Error playing audio for track ${track.id}:`, err);
                  }
                });
              } catch (e) {
                // Catch any synchronous errors
              }
            }

            // Update track state
            track.isPlaying = true;
          }
        }
      } else if (isCurrentlyPlaying) {
        // Only pause if currently playing (avoid unnecessary pause calls)
        wavesurfer.pause();

        // Optimize audio element handling - only pause if playing
        if (audioRef.current && !audioRef.current.paused) {
          audioRef.current.pause();
        }

        // Update track state
        track.isPlaying = false;
      }
    } catch (error) {
      console.error(`Error in play/pause handling for track ${track.id}:`, error);
    }
  }, [isPlaying, isAudioLoaded, track, currentTime, localIsPlaying]);

  // Optimized waveform update function with position tracking and threshold checks
  const updateWaveformPosition = useCallback((time: number) => {
    const wavesurfer = wavesurferRef.current;
    if (!wavesurfer || !isAudioLoaded) return;

    // For tracks that are before the current position, show them as fully played
    if (time >= track.endTime) {
      // Only update if position has changed significantly
      if (Math.abs(lastPositionRef.current - 1.0) > 0.001) {
        wavesurfer.seekTo(1.0);
        lastPositionRef.current = 1.0;
      }
      return;
    }

    // For tracks that haven't started yet, show them at the beginning
    if (time < track.startTime) {
      // Only update if position has changed significantly
      if (Math.abs(lastPositionRef.current - 0) > 0.001) {
        wavesurfer.seekTo(0);
        lastPositionRef.current = 0;
      }
      return;
    }

    // For the current track, show the correct position
    if (time >= track.startTime && time < track.endTime) {
      // Calculate the position within the track
      const trackPosition = time - track.startTime;
      const relativePosition = trackPosition / track.duration;

      // Only update if position has changed significantly (avoid micro-updates)
      if (Math.abs(lastPositionRef.current - relativePosition) > 0.001) {
        wavesurfer.seekTo(relativePosition);
        lastPositionRef.current = relativePosition;
      }
    }
  }, [track.startTime, track.endTime, track.duration, isAudioLoaded]);

  // Handle transition volume automation
  useEffect(() => {
    if (!isInitialized || !gainNodeRef.current || !track.inTransition) return;

    // Find the active transition for this track
    const activeTransition = (window as any).transitions?.find(
      (t: any) =>
        (t.fromTrackId === track.id || t.toTrackId === track.id) &&
        currentTime >= t.startTime &&
        currentTime < (t.startTime + t.duration)
    );

    if (!activeTransition) return;

    // Calculate transition progress (0-1)
    const transitionProgress = (currentTime - activeTransition.startTime) / activeTransition.duration;

    // Apply volume based on whether this is the "from" or "to" track
    if (activeTransition.fromTrackId === track.id) {
      // This is the track we're transitioning from
      let targetVolume;

      // Apply the appropriate fade curve
      if (activeTransition.fadeOutCurve === 'exponential') {
        // Exponential curve (slower at start, faster at end)
        targetVolume = activeTransition.fromTrackStartVolume -
          (activeTransition.fromTrackStartVolume - activeTransition.fromTrackEndVolume) *
          (transitionProgress * transitionProgress);
      } else if (activeTransition.fadeOutCurve === 'sCurve') {
        // S-Curve (slow at start and end, faster in middle)
        const x = transitionProgress;
        const sCurveProgress = x * x * (3 - 2 * x); // Smoothstep function
        targetVolume = activeTransition.fromTrackStartVolume -
          (activeTransition.fromTrackStartVolume - activeTransition.fromTrackEndVolume) *
          sCurveProgress;
      } else {
        // Linear curve (default)
        targetVolume = activeTransition.fromTrackStartVolume -
          (activeTransition.fromTrackStartVolume - activeTransition.fromTrackEndVolume) *
          transitionProgress;
      }

      // Apply the volume
      gainNodeRef.current.gain.value = targetVolume / 100;
    } else if (activeTransition.toTrackId === track.id) {
      // This is the track we're transitioning to
      let targetVolume;

      // Apply the appropriate fade curve
      if (activeTransition.fadeInCurve === 'exponential') {
        // Exponential curve (faster at start, slower at end)
        targetVolume = activeTransition.toTrackStartVolume +
          (activeTransition.toTrackEndVolume - activeTransition.toTrackStartVolume) *
          Math.sqrt(transitionProgress);
      } else if (activeTransition.fadeInCurve === 'sCurve') {
        // S-Curve (slow at start and end, faster in middle)
        const x = transitionProgress;
        const sCurveProgress = x * x * (3 - 2 * x); // Smoothstep function
        targetVolume = activeTransition.toTrackStartVolume +
          (activeTransition.toTrackEndVolume - activeTransition.toTrackStartVolume) *
          sCurveProgress;
      } else {
        // Linear curve (default)
        targetVolume = activeTransition.toTrackStartVolume +
          (activeTransition.toTrackEndVolume - activeTransition.toTrackStartVolume) *
          transitionProgress;
      }

      // Apply the volume
      gainNodeRef.current.gain.value = targetVolume / 100;
    }
  }, [currentTime, track.id, track.inTransition, isInitialized]);

  // Use requestAnimationFrame for smoother waveform updates with throttling
  useEffect(() => {
    if (!isAudioLoaded) return;

    // We need to update all tracks' waveform positions when the timeline changes
    // This ensures tracks before current position are fully filled and tracks after are empty

    // Immediately update the waveform position based on current time
    // This ensures correct state even without animation frames
    updateWaveformPosition(currentTime);

    // Only run continuous animation frames for active or near-active tracks
    // to save performance, but ensure all tracks have correct initial position
    const isNearActive =
      (currentTime >= track.startTime - 5 && currentTime <= track.endTime + 5) || // Within 5 seconds of track bounds
      track.isActive; // Currently active

    if (!isNearActive) {
      // If not near active, cancel any existing animation frame after initial update
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      return;
    }

    const throttleInterval = 50; // ms between updates

    const updateFrame = () => {
      const now = performance.now();

      // Only update if enough time has passed (throttling)
      if (now - lastUpdateTimeRef.current >= throttleInterval) {
        updateWaveformPosition(currentTime);
        lastUpdateTimeRef.current = now;
      }

      // Schedule next frame
      animationFrameRef.current = requestAnimationFrame(updateFrame);
    };

    // Start animation frame loop if not already running
    if (animationFrameRef.current === null) {
      animationFrameRef.current = requestAnimationFrame(updateFrame);
    }

    // Clean up
    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [currentTime, updateWaveformPosition, isAudioLoaded, track.startTime, track.endTime, track.isActive]);



  return (
    <div className={`track-container border rounded-md p-4 mb-4 ${
      track.isActive && track.inTransition
        ? 'bg-purple-50 border-purple-300'
        : track.isActive
          ? 'bg-green-50 border-green-300'
          : 'bg-white'
    }`}>
      <div className="track-header flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <div
            className="track-color-indicator w-4 h-4 rounded-full"
            style={{ backgroundColor: track.color || '#4F46E5' }}
          />
          <h3 className="text-lg font-semibold">{track.name}</h3>
          {track.isActive && track.inTransition ? (
            <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
              In Transition
            </span>
          ) : track.isActive && (
            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </div>
        <div className="track-info flex gap-4">
          {track.key && <span className="track-key text-sm bg-blue-100 px-2 py-1 rounded">Key: {track.key}</span>}
          {track.bpm && <span className="track-bpm text-sm bg-green-100 px-2 py-1 rounded">BPM: {track.bpm}</span>}
        </div>
      </div>

      <div className="track-timeline text-xs text-gray-500 mb-1 flex justify-between">
        <span>Start: {formatTime(track.startTime)}</span>
        <span>Duration: {formatTime(track.duration)}</span>
        <span>End: {formatTime(track.endTime)}</span>
      </div>

      <div
        ref={containerRef}
        className={`waveform-container border rounded-md p-2 ${track.isActive ? 'bg-green-50' : 'bg-gray-50'} min-h-[120px] cursor-pointer`}
        onClick={(e) => {
          // Calculate the relative position within the track
          if (!wavesurferRef.current || !isAudioLoaded) return;

          const rect = e.currentTarget.getBoundingClientRect();
          const clickPosition = (e.clientX - rect.left) / rect.width;

          // Calculate the position within the track
          const trackPosition = clickPosition * track.duration;

          // Calculate the global timeline position
          const globalPosition = track.startTime + trackPosition;

          // Seek to that position in the global timeline
          onSeek(globalPosition);
        }}
      >
        {!isAudioLoaded && (
          <div className="loading-indicator flex items-center justify-center h-full text-gray-500">
            Loading waveform...
          </div>
        )}
      </div>

      <div className="track-controls mt-2">
        <div className="volume-control flex items-center gap-2">
          <label className="text-sm">Volume</label>
          <input
            type="range"
            min="0"
            max="100"
            value={track.volume}
            onChange={(e) => {
              onVolumeChange(track.id, parseInt(e.target.value));
            }}
            className="flex-grow"
          />
          <span className="text-sm">{track.volume}%</span>
        </div>

        <div className="track-status mt-2 text-xs text-gray-500">
          <div className="flex justify-between">
            <span>Position in timeline: {formatTime(track.startTime)} - {formatTime(track.endTime)}</span>
            <span>
              Status: {localIsPlaying ? (
                <span className="text-green-600 font-semibold">Playing</span>
              ) : (
                <span className="text-gray-600">Paused</span>
              )}
            </span>
          </div>
        </div>
      </div>

      {/* Audio element - must be visible for some browsers to work properly */}
      <audio
        ref={audioRef}
        preload="auto"
        style={{ display: 'block', height: '1px', width: '1px', overflow: 'hidden' }}
        src={track.url}
        crossOrigin="anonymous"
        controls={false}
        muted={false}
        loop={false}
      ></audio>
    </div>
  );
};

// Helper function to format time
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

export default Track;
