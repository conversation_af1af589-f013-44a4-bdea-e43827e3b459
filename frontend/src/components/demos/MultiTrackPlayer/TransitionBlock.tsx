import React, { useState, useEffect } from "react";
import { X, ChevronDown, ChevronUp, Save, ArrowDownUp, Loader2, Music, Sliders, Sparkles } from "lucide-react";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
// Note: DualViewTransitionEditor has been consolidated into TransitionEditor with new tabs
// import DualViewTransitionEditor from "../../mixes/timeline/components/editors/DualViewTransitionEditor";
import TransitionSuggesterDialog from "@/components/ai/TransitionSuggesterDialog";
import TimelineSuggestionButton from "@/components/ai/TimelineSuggestionButton";
import CamelotWheel from "@/components/visualizations/CamelotWheel";
import TransitionPreview from "@/components/visualizations/TransitionPreview";
import EnergyFlowVisualizer from "@/components/visualizations/EnergyFlowVisualizer";
import { QuickFeedback } from "@/components/ai/personalization/FeedbackSystem";
import { trackTransitionInteraction } from "@/components/ai/personalization/UserPreferenceCollector";
import { Track as TrackType, Transition as TransitionType } from "./types";

// Adapter interface to match the expected props of the original TransitionBlock
interface TransitionBlockProps {
  transition: TransitionType;
  tracks: TrackType[];
  currentTime: number;
  isPlaying: boolean;
  onTransitionChange: (updatedTransition: TransitionType) => void;
  onSeek: (time: number) => void;
}

const TransitionBlock: React.FC<TransitionBlockProps> = ({
  transition,
  tracks,
  currentTime,
  isPlaying,
  onTransitionChange,
  onSeek
}) => {
  // Find the tracks involved in this transition
  const fromTrack = tracks.find(t => t.id === transition.fromTrackId);
  const toTrack = tracks.find(t => t.id === transition.toTrackId);

  if (!fromTrack || !toTrack) {
    return (
      <div className="transition-error p-4 bg-red-100 text-red-800 rounded-md mb-4">
        Error: Could not find tracks for transition
      </div>
    );
  }

  // Adapt our track model to the expected format
  const adaptedFromTrack = {
    id: fromTrack.id,
    title: fromTrack.name,
    artist: fromTrack.artist || "Unknown Artist",
    duration: fromTrack.duration,
    waveform: fromTrack.waveform,
    color: fromTrack.color,
    bpm: fromTrack.bpm,
    key: fromTrack.key,
    energy: fromTrack.energy,
    genre: fromTrack.genre
  };

  const adaptedToTrack = {
    id: toTrack.id,
    title: toTrack.name,
    artist: toTrack.artist || "Unknown Artist",
    duration: toTrack.duration,
    waveform: toTrack.waveform,
    color: toTrack.color,
    bpm: toTrack.bpm,
    key: toTrack.key,
    energy: toTrack.energy,
    genre: toTrack.genre
  };

  // Adapt our transition model to the expected format
  const adaptedTransition = {
    fromTrackId: transition.fromTrackId,
    toTrackId: transition.toTrackId,
    duration: transition.duration,
    startPoint: transition.transitionPoint || 0.5,
    type: transition.type || "beatmatch",
    notes: transition.notes,
    effectsApplied: transition.effects
  };

  const [isAISuggesterOpen, setIsAISuggesterOpen] = useState(false);

  // Visual appearance based on transition type
  const getBlockStyle = () => {
    switch(adaptedTransition.type) {
      case "beatmatch": return "bg-gradient-to-r from-primary/80 to-secondary/80";
      case "cut": return "bg-gradient-to-r from-destructive/80 to-warning/80";
      case "fade": return "bg-gradient-to-r from-success/80 to-muted/80";
      case "filter": return "bg-gradient-to-r from-info/80 to-primary/80";
      case "echo": return "bg-gradient-to-r from-accent/80 to-muted/80";
      case "custom": return "bg-gradient-to-r from-muted/80 to-foreground/80";
      default: return "bg-muted";
    }
  };

  // Format beats to a readable string
  const formatBeats = (beats: number) => {
    return `${beats} beats`;
  };

  // Handle applying an AI suggestion
  const handleApplySuggestion = (suggestion: any) => {
    // Map the suggestion to transition properties
    const changes: Partial<TransitionType> = {
      type: suggestion.type,
      duration: suggestion.duration || transition.duration,
      transitionPoint: suggestion.startPoint || transition.transitionPoint,
      notes: suggestion.description || transition.notes
    };

    // Track the interaction for personalization
    trackTransitionInteraction({
      ...adaptedTransition,
      ...suggestion
    }, 'apply-suggestion');

    // Apply the changes to the transition
    onTransitionChange({
      ...transition,
      ...changes
    });
  };

  // Handle preview button click
  const handlePreview = () => {
    // Seek to 5 seconds before the transition
    const previewStartTime = Math.max(0, transition.startTime - 5);
    onSeek(previewStartTime);
  };

  // Handle toggle expand
  const handleToggleExpand = () => {
    onTransitionChange({
      ...transition,
      isExpanded: !transition.isExpanded
    });
  };

  // Map changes from the original format to our format
  const handleTransitionChange = (changes: any) => {
    const mappedChanges: Partial<TransitionType> = {};

    if (changes.type) mappedChanges.type = changes.type;
    if (changes.duration) mappedChanges.duration = changes.duration;
    if (changes.startPoint) mappedChanges.transitionPoint = changes.startPoint;
    if (changes.notes) mappedChanges.notes = changes.notes;
    if (changes.effectsApplied) mappedChanges.effects = changes.effectsApplied;

    onTransitionChange({
      ...transition,
      ...mappedChanges
    });
  };

  return (
    <div
      className={`transition-block ${getBlockStyle()} shadow-lg`}
      style={{
        width: "100%",
        height: "44px",
        transition: "all 0.3s ease-out",
        margin: "4px 0",
        position: "relative",
        zIndex: transition.isExpanded ? 50 : 10,
        borderRadius: "0.375rem",
        border: "1px solid var(--border, rgba(255,255,255,0.2))",
      }}
    >
      {!transition.isExpanded ? (
        // Collapsed view - clean bar between tracks
        <div
          className="flex items-center justify-between px-4 py-2 h-full cursor-pointer"
          onClick={handleToggleExpand}
        >
          <div className="flex items-center">
            <ArrowDownUp className="h-4 w-4 text-foreground mr-2" />
            <span className="text-foreground font-medium uppercase text-sm tracking-wider mr-2">
              {adaptedTransition.type} Transition
            </span>
            <Badge variant="outline" className="bg-foreground/20 text-foreground">
              {formatBeats(adaptedTransition.duration)}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {/* AI Suggestion Button */}
            <div onClick={(e) => e.stopPropagation()}>
              <TimelineSuggestionButton
                fromTrack={adaptedFromTrack}
                toTrack={adaptedToTrack}
                currentTransition={adaptedTransition}
                onSuggestionApply={handleApplySuggestion}
                className="bg-foreground/10 text-foreground"
              />
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-foreground hover:bg-foreground/20 bg-foreground/10 rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                handlePreview();
              }}
              disabled={isPlaying}
            >
              {isPlaying ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Loading...
                </>
              ) : (
                "Preview"
              )}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-foreground hover:bg-foreground/20 bg-foreground/10 rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                handleToggleExpand();
              }}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        <>
          {/* Collapsed bar remains visible when expanded */}
          <div
            className="flex items-center justify-between px-4 py-2 h-full cursor-pointer"
          >
            <div className="flex items-center">
              <ArrowDownUp className="h-4 w-4 text-foreground mr-2" />
              <span className="text-foreground font-medium uppercase text-sm tracking-wider mr-2">
                {adaptedTransition.type} Transition
              </span>
              <Badge variant="outline" className="bg-foreground/20 text-foreground">
                {formatBeats(adaptedTransition.duration)}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              {/* AI Suggestion Button */}
              <TimelineSuggestionButton
                fromTrack={adaptedFromTrack}
                toTrack={adaptedToTrack}
                currentTransition={adaptedTransition}
                onSuggestionApply={handleApplySuggestion}
                className="bg-foreground/10 text-foreground"
              />

              <Button
                variant="ghost"
                size="sm"
                className="h-7 text-foreground hover:bg-foreground/20 bg-foreground/10 rounded-full"
                onClick={handlePreview}
                disabled={isPlaying}
              >
                {isPlaying ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Preview"
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-foreground hover:bg-foreground/20 bg-foreground/10 rounded-full"
                onClick={handleToggleExpand}
              >
                <ChevronUp className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Expanded view - full-width overlay */}
          <div
            className="p-4 overflow-auto bg-background rounded-lg border shadow-xl"
            style={{
              position: "absolute",
              width: "100%",
              top: "100%",
              left: 0,
              marginTop: "8px",
              zIndex: 30
            }}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold flex items-center">
                <span className={`inline-block w-3 h-3 rounded-full mr-2`} style={{
                  backgroundColor: adaptedTransition.type === "beatmatch" ? "#818cf8" :
                                 adaptedTransition.type === "cut" ? "#f87171" :
                                 adaptedTransition.type === "fade" ? "#34d399" :
                                 adaptedTransition.type === "filter" ? "#fbbf24" :
                                 adaptedTransition.type === "echo" ? "#a78bfa" : "#94a3b8"
                }}></span>
                {adaptedTransition.type} Transition
              </h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleToggleExpand}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Track info display */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div className="bg-muted p-3 rounded-lg">
                <div className="text-xs uppercase tracking-wider">From</div>
                <div className="text-sm font-medium truncate">{adaptedFromTrack.title}</div>
                <div className="text-xs text-muted-foreground truncate">{adaptedFromTrack.artist}</div>
              </div>

              <div className="bg-muted p-3 rounded-lg">
                <div className="text-xs uppercase tracking-wider">To</div>
                <div className="text-sm font-medium truncate">{adaptedToTrack.title}</div>
                <div className="text-xs text-muted-foreground truncate">{adaptedToTrack.artist}</div>
              </div>
            </div>

            {/* Transition editor tabs */}
            <div className="transition-controls rounded-md mb-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-bold">Edit Transition</h3>
              </div>

              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="w-full mb-4">
                  <TabsTrigger value="basic" className="data-[state=active]:bg-foreground/20">
                    <Music className="mr-2 h-4 w-4" />
                    Basic
                  </TabsTrigger>
                  <TabsTrigger value="advanced" className="data-[state=active]:bg-foreground/20">
                    <Sliders className="mr-2 h-4 w-4" />
                    Advanced
                  </TabsTrigger>
                  <TabsTrigger value="visualizations" className="data-[state=active]:bg-foreground/20">
                    <Sparkles className="mr-2 h-4 w-4" />
                    Visualizations
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="basic">
                  {/* Enhanced Transition Preview */}
                  <div className="mb-6">
                    <TransitionPreview
                      fromTrackWaveform={adaptedFromTrack.waveform || "https://via.placeholder.com/600x100/3498db/ffffff?text=Track+1+Waveform"}
                      toTrackWaveform={adaptedToTrack.waveform || "https://via.placeholder.com/600x100/e74c3c/ffffff?text=Track+2+Waveform"}
                      transitionType={adaptedTransition.type}
                      transitionDuration={adaptedTransition.duration / 4} // Convert beats to seconds (assuming 4 beats per second)
                      width={550}
                      height={150}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {/* Key Relationship Visualization */}
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h4 className="text-sm font-medium mb-3">Key Relationship</h4>
                      <div className="flex flex-col items-center">
                        {adaptedFromTrack.key && adaptedToTrack.key ? (
                          <>
                            <CamelotWheel
                              selectedKey={adaptedFromTrack.key}
                              compatibleKeys={CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3)}
                              size={150}
                              showLabels={false}
                              interactive={false}
                            />
                            <div className="mt-2 text-sm text-center">
                              <p>
                                From <strong>{adaptedFromTrack.key}</strong> to <strong>{adaptedToTrack.key}</strong>
                                {CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ? (
                                  <span className="text-green-500"> (Compatible)</span>
                                ) : (
                                  <span className="text-yellow-500"> (Caution: Keys may clash)</span>
                                )}
                              </p>
                            </div>
                          </>
                        ) : (
                          <div className="text-sm text-muted-foreground">
                            Key information not available for one or both tracks
                          </div>
                        )}
                      </div>
                    </div>

                    {/* BPM and Energy Information */}
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h4 className="text-sm font-medium mb-3">Track Compatibility</h4>

                      {/* BPM Compatibility */}
                      <div className="mb-3">
                        <div className="flex justify-between text-xs mb-1">
                          <span>BPM Difference</span>
                          <span>
                            {adaptedFromTrack.bpm && adaptedToTrack.bpm ? (
                              `${Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm).toFixed(1)} BPM`
                            ) : (
                              'N/A'
                            )}
                          </span>
                        </div>
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          {adaptedFromTrack.bpm && adaptedToTrack.bpm && (
                            <div
                              className={`h-full ${Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 5 ? 'bg-green-500' :
                                Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 10 ? 'bg-yellow-500' : 'bg-red-500'}`}
                              style={{
                                width: `${Math.max(0, 100 - Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) * 5)}%`
                              }}
                            />
                          )}
                        </div>
                      </div>

                      {/* Energy Compatibility */}
                      <div>
                        <div className="flex justify-between text-xs mb-1">
                          <span>Energy Transition</span>
                          <span>
                            {adaptedFromTrack.energy && adaptedToTrack.energy ? (
                              `${adaptedFromTrack.energy} → ${adaptedToTrack.energy}`
                            ) : (
                              'N/A'
                            )}
                          </span>
                        </div>
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          {adaptedFromTrack.energy && adaptedToTrack.energy && (
                            <div
                              className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                              style={{
                                width: '100%',
                                opacity: Math.abs(adaptedFromTrack.energy - adaptedToTrack.energy) < 3 ? 1 : 0.5
                              }}
                            />
                          )}
                        </div>
                      </div>

                      {/* Recommended Transition Type */}
                      {adaptedFromTrack.bpm && adaptedToTrack.bpm && adaptedFromTrack.key && adaptedToTrack.key && (
                        <div className="mt-4 text-xs">
                          <p className="font-medium">Recommended Transition:</p>
                          {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 5 &&
                           CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ? (
                            <Badge className="mt-1 bg-green-500/20 text-green-500 hover:bg-green-500/30">
                              Beatmatch
                            </Badge>
                          ) : Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 10 ? (
                            <Badge className="mt-1 bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30">
                              Echo or Filter
                            </Badge>
                          ) : (
                            <Badge className="mt-1 bg-red-500/20 text-red-500 hover:bg-red-500/30">
                              Cut or Fade
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <Separator className="my-4" />

                  {/* Basic Controls Section */}
                  <div className="space-y-4">
                    {/* Transition type selector */}
                    <div>
                      <div className="text-xs mb-1">Transition Type</div>
                      <Select
                        value={adaptedTransition.type}
                        onValueChange={(value) => {
                          // Track the interaction for personalization
                          trackTransitionInteraction({
                            ...adaptedTransition,
                            type: value as any
                          }, 'edit');

                          handleTransitionChange({ type: value });
                        }}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="beatmatch">Beatmatch</SelectItem>
                          <SelectItem value="cut">Cut</SelectItem>
                          <SelectItem value="fade">Fade</SelectItem>
                          <SelectItem value="filter">Filter</SelectItem>
                          <SelectItem value="echo">Echo</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Slider for transition point */}
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span>Transition Point</span>
                        <span>{Math.round((transition.transitionPoint || 0.5) * 100)}%</span>
                      </div>
                      <Slider
                        value={[(transition.transitionPoint || 0.5) * 100]}
                        min={10}
                        max={90}
                        step={1}
                        onValueChange={(value) => handleTransitionChange({ startPoint: value[0]/100 })}
                      />
                    </div>

                    {/* Duration control */}
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span>Duration (beats)</span>
                        <span>{transition.duration}</span>
                      </div>
                      <Slider
                        value={[transition.duration]}
                        min={4}
                        max={64}
                        step={4}
                        onValueChange={(value) => handleTransitionChange({ duration: value[0] })}
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="advanced">
                  {/* Advanced editor */}
                  <DualViewTransitionEditor
                    fromTrack={adaptedFromTrack}
                    toTrack={adaptedToTrack}
                    transition={{
                      fromTrackId: adaptedFromTrack.id,
                      toTrackId: adaptedToTrack.id,
                      duration: adaptedTransition.duration,
                      startPoint: adaptedTransition.startPoint,
                      type: adaptedTransition.type,
                      notes: adaptedTransition.notes || "",
                      effectsApplied: adaptedTransition.effectsApplied || []
                    }}
                    onTransitionChange={(changes) => {
                      const updatedChanges = {
                        duration: changes.duration || adaptedTransition.duration,
                        startPoint: changes.startPoint || adaptedTransition.startPoint,
                        type: changes.type || adaptedTransition.type,
                        notes: changes.notes || adaptedTransition.notes,
                        effectsApplied: changes.effects || adaptedTransition.effectsApplied
                      };

                      // Track the interaction for personalization
                      trackTransitionInteraction({
                        ...adaptedTransition,
                        ...updatedChanges
                      }, 'edit');

                      handleTransitionChange(updatedChanges);
                    }}
                    onSaveTransition={() => {
                      // Just close the transition editor
                      handleToggleExpand();
                    }}
                    onPreviewTransition={handlePreview}
                    isPlaying={isPlaying}
                  />
                </TabsContent>

                <TabsContent value="visualizations">
                  {/* Visualizations Tab */}
                  <div className="space-y-6">
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h3 className="text-lg font-medium mb-4">Transition Preview</h3>
                      <TransitionPreview
                        fromTrackWaveform={adaptedFromTrack.waveform || "https://via.placeholder.com/600x100/3498db/ffffff?text=Track+1+Waveform"}
                        toTrackWaveform={adaptedToTrack.waveform || "https://via.placeholder.com/600x100/e74c3c/ffffff?text=Track+2+Waveform"}
                        fromTrackAudio={adaptedFromTrack.id ? `/api/tracks/${adaptedFromTrack.id}/preview` : undefined}
                        toTrackAudio={adaptedToTrack.id ? `/api/tracks/${adaptedToTrack.id}/preview` : undefined}
                        transitionType={adaptedTransition.type}
                        transitionDuration={adaptedTransition.duration / 4} // Convert beats to seconds (assuming 4 beats per second)
                        width={550}
                        height={200}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Camelot Wheel */}
                      <div className="bg-muted/30 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-4">Camelot Wheel</h3>
                        <div className="flex flex-col items-center">
                          <CamelotWheel
                            selectedKey={adaptedFromTrack.key || '8B'}
                            compatibleKeys={adaptedFromTrack.key ? CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3) : []}
                            size={200}
                            showLabels={true}
                            interactive={true}
                          />
                          <div className="mt-4 text-sm text-center">
                            <p>
                              From <strong>{adaptedFromTrack.key || 'Unknown'}</strong> to <strong>{adaptedToTrack.key || 'Unknown'}</strong>
                              {adaptedFromTrack.key && adaptedToTrack.key && CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ? (
                                <span className="text-green-500"> (Compatible)</span>
                              ) : adaptedFromTrack.key && adaptedToTrack.key ? (
                                <span className="text-yellow-500"> (Caution: Keys may clash)</span>
                              ) : (
                                <span className="text-muted-foreground"> (Key information incomplete)</span>
                              )}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Energy Flow */}
                      <div className="bg-muted/30 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-4">Energy Flow</h3>
                        <div className="flex flex-col items-center">
                          <EnergyFlowVisualizer
                            energyPoints={[
                              { position: 0, value: adaptedFromTrack.energy || 5 },
                              { position: 0.4, value: Math.max(1, ((adaptedFromTrack.energy || 5) + (adaptedToTrack.energy || 5)) / 2) },
                              { position: 0.6, value: Math.max(1, ((adaptedFromTrack.energy || 5) + (adaptedToTrack.energy || 5)) / 2) },
                              { position: 1, value: adaptedToTrack.energy || 5 }
                            ]}
                            width={250}
                            height={150}
                            animated={true}
                            editable={true}
                            colorScheme="rainbow"
                          />
                          <div className="mt-4 text-sm text-center">
                            <p>
                              Energy transition from <strong>{adaptedFromTrack.energy || 'Unknown'}</strong> to <strong>{adaptedToTrack.energy || 'Unknown'}</strong>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h3 className="text-lg font-medium mb-4">Track Compatibility</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium mb-2">BPM Compatibility</h4>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">{adaptedFromTrack.bpm || 'Unknown'}</Badge>
                            <span className="text-xs">→</span>
                            <Badge variant="outline">{adaptedToTrack.bpm || 'Unknown'}</Badge>

                            {adaptedFromTrack.bpm && adaptedToTrack.bpm && (
                              <Badge
                                className={`ml-auto ${
                                  Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 5 ? 'bg-green-500/20 text-green-500' :
                                  Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 10 ? 'bg-yellow-500/20 text-yellow-500' :
                                  'bg-red-500/20 text-red-500'
                                }`}
                              >
                                {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm).toFixed(1)} BPM diff
                              </Badge>
                            )}
                          </div>

                          <div className="h-2 w-full bg-muted rounded-full overflow-hidden mb-4">
                            {adaptedFromTrack.bpm && adaptedToTrack.bpm && (
                              <div
                                className={`h-full ${
                                  Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 5 ? 'bg-green-500' :
                                  Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 10 ? 'bg-yellow-500' :
                                  'bg-red-500'
                                }`}
                                style={{ width: `${Math.max(0, 100 - Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) * 5)}%` }}
                              />
                            )}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-sm font-medium mb-2">Key Compatibility</h4>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">{adaptedFromTrack.key || 'Unknown'}</Badge>
                            <span className="text-xs">→</span>
                            <Badge variant="outline">{adaptedToTrack.key || 'Unknown'}</Badge>

                            {adaptedFromTrack.key && adaptedToTrack.key && (
                              <Badge
                                className={`ml-auto ${
                                  CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ?
                                  'bg-green-500/20 text-green-500' : 'bg-yellow-500/20 text-yellow-500'
                                }`}
                              >
                                {CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ?
                                  'Compatible' : 'Caution'}
                              </Badge>
                            )}
                          </div>

                          <div className="h-2 w-full bg-muted rounded-full overflow-hidden mb-4">
                            {adaptedFromTrack.key && adaptedToTrack.key && (
                              <div
                                className={`h-full ${
                                  CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ?
                                  'bg-green-500' : 'bg-yellow-500'
                                }`}
                                style={{
                                  width: `${CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) ?
                                    100 : 50}%`
                                }}
                              />
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Recommended Transition Types</h4>
                        <div className="flex flex-wrap gap-2">
                          {adaptedFromTrack.bpm && adaptedToTrack.bpm && adaptedFromTrack.key && adaptedToTrack.key ? (
                            <>
                              {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 5 &&
                               CamelotWheel.getCompatibleKeys(adaptedFromTrack.key, 3).includes(adaptedToTrack.key) && (
                                <Badge className="bg-green-500/20 text-green-500 hover:bg-green-500/30">
                                  Beatmatch (Recommended)
                                </Badge>
                              )}

                              {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 10 && (
                                <Badge className="bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30">
                                  Echo
                                </Badge>
                              )}

                              {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) < 10 && (
                                <Badge className="bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30">
                                  Filter
                                </Badge>
                              )}

                              {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) >= 10 && (
                                <Badge className="bg-red-500/20 text-red-500 hover:bg-red-500/30">
                                  Cut
                                </Badge>
                              )}

                              {Math.abs(adaptedFromTrack.bpm - adaptedToTrack.bpm) >= 10 && (
                                <Badge className="bg-red-500/20 text-red-500 hover:bg-red-500/30">
                                  Fade
                                </Badge>
                              )}
                            </>
                          ) : (
                            <span className="text-sm text-muted-foreground">
                              Insufficient track information to provide recommendations
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <div className="flex justify-between">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  disabled={isPlaying}
                >
                  {isPlaying ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    "Preview"
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsAISuggesterOpen(true)}
                  disabled={isPlaying}
                  className="flex items-center"
                >
                  <Sparkles className="h-3 w-3 mr-1" />
                  AI Suggestions
                </Button>

                {/* Feedback component */}
                <QuickFeedback
                  featureId={`transition-${adaptedFromTrack.id}-${adaptedToTrack.id}`}
                  featureName="Transition"
                  context={`Transition from ${adaptedFromTrack.title} to ${adaptedToTrack.title} using ${adaptedTransition.type}`}
                  onFeedbackGiven={(rating) => {
                    // Track the feedback for personalization
                    trackTransitionInteraction(adaptedTransition, rating === 'positive' ? 'create' : 'edit');
                  }}
                  className="ml-2"
                />
              </div>

              <Button
                variant="default"
                size="sm"
                onClick={handleToggleExpand}
              >
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
            </div>

            {/* AI Transition Suggester Dialog */}
            <TransitionSuggesterDialog
              fromTrack={{
                id: adaptedFromTrack.id,
                title: adaptedFromTrack.title,
                artist: adaptedFromTrack.artist,
                bpm: adaptedFromTrack.bpm || 120,
                key: adaptedFromTrack.key || 'C',
                energy: adaptedFromTrack.energy || 5,
                genre: adaptedFromTrack.genre
              }}
              toTrack={{
                id: adaptedToTrack.id,
                title: adaptedToTrack.title,
                artist: adaptedToTrack.artist,
                bpm: adaptedToTrack.bpm || 120,
                key: adaptedToTrack.key || 'C',
                energy: adaptedToTrack.energy || 5,
                genre: adaptedToTrack.genre
              }}
              onSuggestionApply={handleApplySuggestion}
              open={isAISuggesterOpen}
              onOpenChange={setIsAISuggesterOpen}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default TransitionBlock;
