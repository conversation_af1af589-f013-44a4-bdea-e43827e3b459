export interface Track {
  id: string;
  name: string;
  url: string;
  startTime: number;  // Start time in the timeline (seconds)
  duration: number;   // Duration of the track (seconds)
  endTime: number;    // Computed from startTime + duration
  volume: number;     // 0-100
  isActive: boolean;  // Whether this track should be playing at the current timeline position
  isPlaying: boolean; // Internal state tracking - whether the track is actually playing
  key?: string;       // Optional key information (e.g., "2A")
  bpm?: number;       // Optional BPM information
  color?: string;     // Optional color for the track visualization
  inTransition?: boolean; // Whether this track is currently in a transition
}

export type FadeCurveType = 'linear' | 'exponential' | 'sCurve';

export interface Transition {
  id: string;
  fromTrackId: string;  // ID of the track transitioning from
  toTrackId: string;    // ID of the track transitioning to
  startTime: number;    // When the transition starts in the timeline
  duration: number;     // Duration of the transition
  trackOffset: number;  // How many seconds the second track is offset (can be negative)
  isExpanded: boolean;  // Whether the transition UI is expanded

  // Transition type
  type?: "beatmatch" | "cut" | "fade" | "filter" | "echo" | "custom";

  // Transition point (0-1 percentage through the transition)
  transitionPoint?: number;

  // Crossfade specific properties
  fadeOutCurve: FadeCurveType;  // How the first track fades out
  fadeInCurve: FadeCurveType;   // How the second track fades in

  // Volume levels during transition
  fromTrackStartVolume: number;  // Volume of first track at start of transition (0-100)
  fromTrackEndVolume: number;    // Volume of first track at end of transition (0-100)
  toTrackStartVolume: number;    // Volume of second track at start of transition (0-100)
  toTrackEndVolume: number;      // Volume of second track at end of transition (0-100)

  // EQ settings
  eqSettings?: {
    lowCut: number;
    midCut: number;
    highCut: number;
  };

  // Effects
  effects?: string[];

  // Notes
  notes?: string;

  // Energy information
  energy?: number;
}
