import React, { createContext, useContext, useState, useEffect } from 'react';
import ThemeDeveloperPanel from '@/components/dev-tools/ThemeDeveloperPanel';

interface ThemeDeveloperContextType {
  isEnabled: boolean;
  toggleDeveloperMode: () => void;
  isDeveloperMode: boolean;
}

const ThemeDeveloperContext = createContext<ThemeDeveloperContextType | undefined>(undefined);

interface ThemeDeveloperProviderProps {
  children: React.ReactNode;
  enableInProduction?: boolean;
}

export function ThemeDeveloperProvider({ 
  children, 
  enableInProduction = false 
}: ThemeDeveloperProviderProps) {
  const [isDeveloperMode, setIsDeveloperMode] = useState(false);
  
  // Check if we should enable the developer panel
  const isEnabled = enableInProduction || import.meta.env.DEV;

  // Load developer mode state from localStorage
  useEffect(() => {
    if (isEnabled) {
      const saved = localStorage.getItem('theme-developer-mode');
      if (saved === 'true') {
        setIsDeveloperMode(true);
      }
    }
  }, [isEnabled]);

  // Save developer mode state to localStorage
  useEffect(() => {
    if (isEnabled) {
      localStorage.setItem('theme-developer-mode', isDeveloperMode.toString());
    }
  }, [isDeveloperMode, isEnabled]);

  const toggleDeveloperMode = () => {
    if (isEnabled) {
      setIsDeveloperMode(prev => !prev);
    }
  };

  // Keyboard shortcut to toggle developer mode (Ctrl/Cmd + Shift + T)
  useEffect(() => {
    if (!isEnabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        toggleDeveloperMode();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isEnabled]);

  const contextValue: ThemeDeveloperContextType = {
    isEnabled,
    toggleDeveloperMode,
    isDeveloperMode
  };

  return (
    <ThemeDeveloperContext.Provider value={contextValue}>
      {children}
      {isEnabled && isDeveloperMode && <ThemeDeveloperPanel />}
    </ThemeDeveloperContext.Provider>
  );
}

export function useThemeDeveloper() {
  const context = useContext(ThemeDeveloperContext);
  if (context === undefined) {
    throw new Error('useThemeDeveloper must be used within a ThemeDeveloperProvider');
  }
  return context;
}

// Hook to enable developer mode programmatically
export function useEnableThemeDeveloper() {
  const { toggleDeveloperMode, isEnabled } = useThemeDeveloper();
  
  return () => {
    if (isEnabled) {
      toggleDeveloperMode();
    } else {
      console.warn('Theme Developer Panel is not enabled in production. Set enableInProduction=true to enable.');
    }
  };
}
