import React, { useEffect, useRef, useState } from 'react';
import { MixTimelineNewProvider, useTimelineStore } from '@/components/mixes/timeline/stores/TimelineStore';
import HorizontalTimelinePageContent from '@/components/mixes/timeline/components/horizontal/HorizontalTimelinePageContent';

// Import the same services as the vertical timeline
import timelineCoordinatorEnhanced from '@/components/mixes/timeline/services/TimelineCoordinatorEnhanced';
import enhancedToneAudioEngine from '@/components/mixes/timeline/services/audio/EnhancedToneAudioEngine';
import { useNotifications, NotificationIds } from '@/utils/notificationUtils';

// OPTIMIZED: Import performance monitoring and memory management (same as vertical timeline)
import { TimelinePerformanceMonitor } from '@/components/mixes/timeline/utils/performance';
import waveSurferVisualization from '@/components/mixes/timeline/services/WaveSurferVisualization';

/**
 * HorizontalTimelinePage - Main page for horizontal timeline layout
 *
 * This component provides the same functionality as TimelinePage but with
 * a horizontal layout where tracks are arranged as horizontal lanes instead
 * of a vertical stack.
 *
 * Key differences from vertical timeline:
 * - Tracks arranged horizontally (lanes) instead of vertically (stack)
 * - Time ruler is vertical on the left instead of horizontal on top
 * - Playhead is a vertical line spanning all tracks
 * - Same audio engine, state management, and features
 */
const HorizontalTimelinePage: React.FC = () => {
  // Get the notification utilities
  const notifications = useNotifications();

  // Get store state (same as vertical timeline)
  const {
    isPlaying: storeIsPlaying,
    setIsPlaying: setStoreIsPlaying,
    currentTime: storeCurrentTime,
    setCurrentTime: setStoreCurrentTime,
  } = useTimelineStore();

  // Local state for immediate UI updates (same as vertical timeline)
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  // Track if this component has been initialized
  const componentInitialized = useRef(false);

  // OPTIMIZED: Performance monitoring (same as vertical timeline)
  const performanceMonitor = useRef<TimelinePerformanceMonitor | null>(null);

  // Track user interactions to prevent feedback loops
  const isUserDraggingSliderRef = useRef(false);

  // Initialize the enhanced timeline coordinator (same as vertical timeline)
  useEffect(() => {
    const initializeTimeline = async () => {
      // Prevent double initialization in React StrictMode
      if (componentInitialized.current) {
        console.log('[HorizontalTimelinePage] Already initialized, skipping');
        return;
      }

      try {
        console.log('[HorizontalTimelinePage] Initializing horizontal timeline');

        // Check if audio engine is already initialized
        const isAudioEngineInitialized = enhancedToneAudioEngine.isInitialized();

        if (!isAudioEngineInitialized) {
          console.log('[HorizontalTimelinePage] Initializing audio engine');
          // Initialize the timeline coordinator
          await timelineCoordinatorEnhanced.initialize();

          // Show notification only once using the centralized system (global notification)
          // Use a small delay to avoid React StrictMode double execution
          setTimeout(() => {
            notifications.showInfoOnce(
              NotificationIds.AUDIO_ENGINE_INITIALIZED,
              'Audio Engine Initialized',
              'Enhanced audio engine is ready for horizontal timeline.'
            );
          }, 100);
        } else {
          console.log('[HorizontalTimelinePage] Audio engine already initialized');
        }

        // Set the store in the coordinator
        if ('setStore' in timelineCoordinatorEnhanced) {
          (timelineCoordinatorEnhanced as any).setStore(useTimelineStore);
        } else {
          console.warn('setStore method not found in timelineCoordinatorEnhanced');
        }

        // CRITICAL FIX: Don't clear tracks on initialization - let the store sync handle this
        // The HorizontalTimelinePageContent useEffect will sync store tracks to coordinator
        // timelineCoordinatorEnhanced.setTracks([]); // REMOVED: This was clearing tracks after they were added!

        // CRITICAL FIX: Register for time updates with re-render loop protection (same as vertical timeline)
        let lastLoggedTime = 0;
        const handleTimeUpdate = (time: number) => {
          // Only update if time actually changed significantly (avoid micro-updates)
          const timeDiff = Math.abs(time - currentTime);
          if (timeDiff < 0.1) { // Increased threshold to 100ms to prevent micro-updates
            return;
          }

          // Only log actual seeks/jumps, not normal playback progression
          const timeSinceLastLog = Math.abs(time - lastLoggedTime);
          if (timeDiff > 5.0 && timeSinceLastLog > 1.0) { // Only log jumps >5s and not too frequently
            console.log(`[HorizontalTimelinePage] 🔄 Seek detected: ${time.toFixed(3)}s`);
            lastLoggedTime = time;
          }

          // CRITICAL FIX: Update both time AND playing state (missing from original implementation)
          setCurrentTime(time);
          setStoreCurrentTime(time);

          // CRITICAL FIX: Sync playing state between coordinator and store
          const coordinatorIsPlaying = timelineCoordinatorEnhanced.getIsPlaying();
          if (coordinatorIsPlaying !== isPlaying) {
            setIsPlaying(coordinatorIsPlaying);
            setStoreIsPlaying(coordinatorIsPlaying);
          }

          // Update all WaveSurfer cursor positions to show global timeline position
          // Only update if not currently seeking to avoid conflicts
          if (!isUserDraggingSliderRef.current) {
            waveSurferVisualization.updateAllCursorPositions(time);
          }
        };

        // Store callback reference for proper cleanup
        (window as any).timeUpdateCallback = handleTimeUpdate;
        timelineCoordinatorEnhanced.onTimeUpdate(handleTimeUpdate);

        // OPTIMIZED: Initialize performance monitoring (same as vertical timeline)
        if (!performanceMonitor.current) {
          performanceMonitor.current = TimelinePerformanceMonitor.getInstance();
          performanceMonitor.current.startMonitoring();
          console.log('[HorizontalTimelinePage] Performance monitoring started');
        }

        // EXPOSE SYSTEMS TO WINDOW FOR TESTING AND DEBUGGING
        (window as any).timelineCoordinatorEnhanced = timelineCoordinatorEnhanced;
        (window as any).enhancedToneAudioEngine = enhancedToneAudioEngine;
        (window as any).waveSurferVisualization = waveSurferVisualization;

        console.log('[HorizontalTimelinePage] Exposed timeline systems to window for testing');

        // Mark as initialized
        componentInitialized.current = true;
      } catch (error) {
        console.error('Error initializing horizontal timeline:', error);
      }
    };

    initializeTimeline();

    // OPTIMIZED: Cleanup function with memory management (same as vertical timeline)
    return () => {
      try {
        console.log('[HorizontalTimelinePage] Cleaning up horizontal timeline');

        // Stop playback and clean up resources
        timelineCoordinatorEnhanced.stop();

        // Unregister time update callbacks using stored reference
        const storedCallback = (window as any).timeUpdateCallback;
        if (storedCallback) {
          console.log('[HorizontalTimelinePage] 🧹 CLEANING UP TIME UPDATE CALLBACK');
          timelineCoordinatorEnhanced.offTimeUpdate(storedCallback);
          (window as any).timeUpdateCallback = null;
        }

        // Stop performance monitoring
        if (performanceMonitor.current) {
          performanceMonitor.current.stopMonitoring();
          console.log('[HorizontalTimelinePage] Performance monitoring stopped');
        }

        // Clean up unused waveform instances to prevent memory leaks
        waveSurferVisualization.cleanupUnusedInstances();

        // Reset initialization flag
        componentInitialized.current = false;
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    };
  }, []); // CRITICAL FIX: Empty dependency array - initialization should only happen once

  return (
    <MixTimelineNewProvider>
      <div className="h-screen w-full bg-background overflow-hidden">
        <HorizontalTimelinePageContent />
        {/* Using the same global Toaster from App.tsx */}
      </div>
    </MixTimelineNewProvider>
  );
};

export default HorizontalTimelinePage;
