/*
 * Web Fonts from fontspring.com
 *
 * All OpenType features and all extended glyphs have been removed.
 * Fully installable fonts can be purchased at http://www.fontspring.com
 *
 * The fonts included in this stylesheet are subject to the End User License you purchased
 * from Fontspring. The fonts are protected under domestic and international trademark and 
 * copyright law. You are prohibited from modifying, reverse engineering, duplicating, or
 * distributing this font software.
 *
 * (c) 2010-2016 Fontspring
 *
 *
 *
 *
 * The fonts included are copyrighted by the vendor listed below.
 *
 * Vendor:      Fontfabric
 * License URL: https://www.fontspring.com/licenses/fontfabric/webfont
 *
 *
 */

@font-face {
    font-family: 'nexa_boldregular';
    src: url('Nexa_Free_Bold-webfont.eot');
    src: url('Nexa_Free_Bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('Nexa_Free_Bold-webfont.woff2') format('woff2'),
         url('Nexa_Free_Bold-webfont.woff') format('woff'),
         url('Nexa_Free_Bold-webfont.ttf') format('truetype'),
         url('Nexa_Free_Bold-webfont.svg#nexa_boldregular') format('svg');
    font-weight: normal;
    font-style: normal;

}

