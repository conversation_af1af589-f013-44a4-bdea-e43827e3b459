<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="nexa_boldregular" horiz-adv-x="1533" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="413" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="413" />
<glyph unicode="&#x09;" horiz-adv-x="413" />
<glyph unicode="&#xa0;" horiz-adv-x="413" />
<glyph unicode="!" horiz-adv-x="608" d="M123 147q0 45 18 80t47 53t64 26.5t70 0t64 -26.5t47 -53t18 -80q0 -44 -18 -79t-47 -53t-64 -26t-70 0t-64 26t-47 53t-18 79zM152 375v1057h268v-1057h-268z" />
<glyph unicode="&#x22;" horiz-adv-x="888" d="M131 881v549h227v-549h-227zM496 881v549h229v-549h-229z" />
<glyph unicode="#" horiz-adv-x="1476" d="M29 276v240h217l94 375h-170v235h225l76 308h274l-75 -308h223l78 308h272l-76 -308h148v-237h-207l-96 -373h162v-240h-218l-67 -276h-275l68 276h-225l-68 -276h-272l67 276h-155zM496 502h258l102 405h-258z" />
<glyph unicode="$" horiz-adv-x="1591" d="M74 311l241 103q40 -96 158 -155.5t250 -59.5q72 0 133.5 12.5t112 38.5t79.5 70t29 102q0 49 -28.5 86t-81.5 60t-112.5 37.5t-137.5 23.5q-57 6 -97.5 12t-93 16t-90.5 21t-81 29t-73 40t-59.5 52.5t-47 67t-28 83.5t-10.5 103q0 97 40 174t109 126.5t150.5 79 t175.5 40.5v178h260v-178q70 -11 128 -29.5t115 -50t103.5 -82.5t79.5 -119l-221 -102q-40 66 -140 110.5t-206 44.5q-67 0 -123 -10t-102.5 -32t-73 -60t-26.5 -90q0 -33 12 -59t37 -44t52 -30.5t70 -22t77 -14.5t85 -11q60 -7 100.5 -13t97.5 -17t96 -23t86.5 -31.5 t79.5 -42t64.5 -55t51.5 -71t31 -88.5t12 -109q0 -100 -40 -183.5t-108 -139t-154.5 -88.5t-183.5 -40v-204h-260v194q-183 16 -326 102.5t-212 247.5z" />
<glyph unicode="%" horiz-adv-x="1585" d="M58 1138.5q0 60.5 21.5 118t61.5 103t105 73t145 27.5t145 -27.5t105 -73t61.5 -103t21.5 -118t-21.5 -118t-61.5 -103t-105 -73t-145 -27.5t-145 27.5t-105 73t-61.5 103t-21.5 118zM158 -2l944 1438h289l-942 -1438h-291zM264 1100.5q8 -39.5 42.5 -67t84.5 -27.5 t84.5 27.5t42.5 67t0 79t-42.5 67t-84.5 27.5t-84.5 -27.5t-42.5 -67t0 -79zM836 307.5q0 60.5 21.5 118t61.5 103t105 73t145 27.5t145 -27.5t105 -73t61.5 -103t21.5 -118t-21.5 -118t-61.5 -103t-105 -73t-145 -27.5t-145 27.5t-105 73t-61.5 103t-21.5 118zM1042 266.5 q8 -39.5 42.5 -67t84.5 -27.5t84.5 27.5t42.5 67t0 79t-42.5 67t-84.5 27.5t-84.5 -27.5t-42.5 -67t0 -79z" />
<glyph unicode="&#x26;" horiz-adv-x="1484" d="M115 436q0 93 62 179.5t159 119.5q-91 29 -142 114t-51 177q1 88 33.5 161.5t86 122t125 82.5t147.5 49t155 15t150.5 -12t141 -42t119.5 -75t81 -116t32 -160h-256q0 63 -39.5 103.5t-98.5 56.5t-136 16q-75 0 -132 -19t-87.5 -50t-45 -64.5t-13.5 -67.5q0 -51 29 -89.5 t74 -58.5t88.5 -29t84.5 -9h672v-213h-119v-209q0 -68 32 -112t97 -44v-268q-36 0 -65 3.5t-62.5 15.5t-59 32t-48 55t-35.5 82q-122 -200 -410 -200q-98 0 -179.5 13t-155 45t-124 83t-80 130.5t-30.5 182.5zM381 434q0 -72 44.5 -121.5t109.5 -69.5t145 -20q40 0 88 11 t97 32.5t81.5 62t32.5 91.5v207h-311q-138 0 -212.5 -59.5t-74.5 -133.5z" />
<glyph unicode="'" horiz-adv-x="438" d="M92 883v549h227v-549h-227z" />
<glyph unicode="(" horiz-adv-x="708" d="M59 674q0 265 125 510.5t357 402.5l125 -168q-171 -136 -260 -329.5t-89 -415.5q0 -465 349 -740l-136 -182q-155 102 -263 255t-158 321.5t-50 345.5z" />
<glyph unicode=")" horiz-adv-x="727" d="M25 1419l125 168q232 -157 356.5 -402t124.5 -511q0 -177 -50 -345.5t-158 -321.5t-263 -255l-131 187q344 271 344 735q0 222 -88.5 415.5t-259.5 329.5z" />
<glyph unicode="*" horiz-adv-x="702" d="M-10 1155l57 191l189 -72l-11 192h195l-21 -196l201 76l53 -191l-196 -39l143 -160l-154 -114l-122 178l-121 -178l-156 114l135 160z" />
<glyph unicode="+" horiz-adv-x="958" d="M31 598v227h301v305h248v-305h305v-227h-305v-307h-248v307h-301z" />
<glyph unicode="," horiz-adv-x="509" d="M88 113q0 54 30 89.5t73 43t85.5 -2.5t72.5 -47t30 -90q0 -19 -10.5 -54t-81.5 -253h-142l39 203q-44 8 -70 36.5t-26 74.5z" />
<glyph unicode="-" horiz-adv-x="937" d="M72 553v229h755v-229h-755z" />
<glyph unicode="." horiz-adv-x="495" d="M76 129q0 40 16.5 71.5t42.5 48t57.5 24t62.5 0t57 -24t42.5 -48t16.5 -71.5t-16.5 -71.5t-42.5 -48t-57 -24t-62.5 0t-57.5 24t-42.5 48t-16.5 71.5z" />
<glyph unicode="/" horiz-adv-x="931" d="M-25 -168l682 1737h263l-684 -1737h-261z" />
<glyph unicode="0" horiz-adv-x="1425" d="M61 614v207q0 155 54 280t142.5 202t201.5 118.5t231 41.5t231 -42t201.5 -118.5t142.5 -202t54 -279.5v-207q0 -124 -35 -230t-95.5 -180.5t-141 -127.5t-171 -78t-186.5 -25t-186.5 25t-171 77.5t-141 127t-95.5 180.5t-35 231zM330 614q0 -94 31 -170t81.5 -123 t115.5 -72t132.5 -25t132.5 25t115.5 72t81.5 123t31 170v207q0 93 -31 168t-81.5 121.5t-115.5 71.5t-132.5 25.5t-132.5 -24.5t-115.5 -71t-81.5 -121.5t-31 -169.5v-207z" />
<glyph unicode="1" horiz-adv-x="755" d="M10 1188v246h492v-1205h198v-229h-682v229h215v959h-223z" />
<glyph unicode="2" horiz-adv-x="1341" d="M76 0v377q0 96 33.5 171t89 122.5t132 78.5t157 43.5t169.5 12.5q32 0 61 2.5t66.5 8.5t67.5 19t56.5 32.5t41.5 50.5t15 71q0 112 -90.5 171t-235.5 59q-72 0 -136.5 -22.5t-108 -69.5t-44.5 -109h-268q1 88 33 161t86 123t126 84.5t149.5 50t158.5 15.5q90 0 173 -14.5 t161.5 -49t136 -85.5t92.5 -130.5t35 -177.5q0 -106 -32 -185t-83.5 -126t-129 -75t-155.5 -38t-176 -10q-52 0 -100 -7.5t-99 -26.5t-81.5 -58t-30.5 -94v-123h891v-252h-1161z" />
<glyph unicode="3" horiz-adv-x="1339" d="M23 420h272q0 -52 32 -92.5t82 -62.5t101.5 -33t99.5 -11q55 0 108.5 12t102.5 35.5t79 66t30 97.5q0 193 -305 193h-224v221h207q58 0 108.5 8.5t95 27t70.5 53.5t27 83q0 33 -14.5 64.5t-46.5 61t-92 47t-142 17.5q-51 0 -101 -9.5t-95.5 -29t-74 -55t-28.5 -82.5h-270 q0 89 34.5 162t90.5 120.5t131 79.5t154 46t161 14q83 0 162 -15.5t152.5 -50.5t128 -84.5t87.5 -123.5t34 -162q0 -89 -54.5 -172t-144.5 -111q101 -33 167 -120.5t66 -180.5q0 -101 -37 -181t-96.5 -130.5t-140.5 -83.5t-162.5 -46t-169.5 -13q-85 0 -165.5 14 t-157.5 47.5t-134 82.5t-92 125.5t-36 170.5z" />
<glyph unicode="4" horiz-adv-x="1390" d="M63 547l646 885h411v-897h168v-263h-168v-272h-266v272h-766zM369 535h485v663z" />
<glyph unicode="5" horiz-adv-x="1288" d="M74 432h268q0 -56 29.5 -97t78.5 -62t100 -30.5t107 -9.5q52 0 102 13t95 39t72.5 72.5t27.5 107.5q1 58 -25.5 100.5t-72.5 65t-98 33t-111 10.5h-543v758h1039v-254h-774v-252h278q119 0 219.5 -25.5t182.5 -78.5t128.5 -144t45.5 -213q0 -95 -30.5 -175t-83.5 -135.5 t-124.5 -94.5t-153 -57.5t-169.5 -18.5q-98 0 -182 14t-160 47t-129.5 83.5t-84.5 128t-32 175.5z" />
<glyph unicode="6" horiz-adv-x="1355" d="M59 457v534q0 93 33 171t89.5 132t132.5 92t160.5 56t174.5 18q120 0 223.5 -24.5t186.5 -74t130.5 -133t47.5 -192.5h-270q0 61 -47 105.5t-117 65t-150 20.5q-130 2 -227.5 -64.5t-97.5 -177.5v8v-215q45 69 142 110.5t189 41.5q75 0 144.5 -11t137 -33.5t123 -61 t98 -89.5t66 -123t23.5 -159q0 -114 -52 -207t-137.5 -150.5t-189.5 -88t-215 -30.5q-113 0 -217.5 30.5t-190.5 88t-138 151.5t-52 210zM328 453q1 -113 95 -173.5t234 -60.5q137 0 231.5 61t94.5 173q0 114 -91.5 177.5t-232.5 63.5q-143 0 -237.5 -63t-93.5 -178z" />
<glyph unicode="7" horiz-adv-x="1167" d="M41 1176v258h1053l14 -144l-643 -1290h-311l585 1176h-698z" />
<glyph unicode="8" horiz-adv-x="1390" d="M41 432q2 91 58.5 178t160.5 129q-89 48 -134.5 122.5t-45.5 160.5q0 105 46 189.5t126 137.5t183.5 81t221.5 28q117 0 222.5 -28.5t187.5 -81.5t130 -137.5t48 -188.5q0 -81 -46.5 -157t-137.5 -113q70 -28 120.5 -84.5t73 -116.5t23.5 -119q2 -97 -31.5 -176.5 t-92 -132.5t-139.5 -89t-170.5 -52t-187.5 -15q-96 1 -184 16.5t-168.5 51.5t-139 88.5t-92.5 131.5t-32 177zM324 428q-2 -88 88 -151.5t247 -63.5t247.5 63t88.5 152q-1 68 -48 115.5t-121 68.5t-167 21q-88 0 -161.5 -21t-123 -69t-50.5 -115zM356 1030q3 -91 91.5 -146 t211.5 -55q128 0 214.5 54.5t89.5 146.5q3 90 -83 146.5t-223 56.5q-143 0 -223.5 -61.5t-77.5 -141.5z" />
<glyph unicode="9" horiz-adv-x="1382" d="M59 985q0 92 34 170.5t92 134t134 94.5t161 57.5t173 18.5q113 0 217.5 -30.5t190.5 -87.5t138 -151t52 -210v-535q0 -93 -33 -171t-89.5 -132t-132 -92t-160 -56t-174.5 -18q-96 0 -182 15t-162 48t-131.5 81.5t-88 120t-32.5 159.5h270q0 -60 49 -104.5t120.5 -65 t151.5 -20.5q130 -2 228 64.5t98 177.5v-9v215q-45 -69 -142.5 -110t-189.5 -41q-75 0 -144.5 11t-137 33.5t-123 61t-98 89.5t-66 123t-23.5 159zM328 985q0 -114 91 -178t232 -64q143 0 238 63.5t94 178.5q-1 76 -47.5 130t-119 79t-163.5 25q-137 0 -231 -61t-94 -173z " />
<glyph unicode=":" horiz-adv-x="485" d="M92 129q0 55 30 92.5t73 46.5t85.5 0t72.5 -46.5t30 -92.5q0 -39 -16 -69.5t-41.5 -46.5t-57 -23.5t-62.5 0t-56.5 23.5t-41.5 46.5t-16 69.5zM92 723q0 55 30 92.5t73 46.5t85.5 0t72.5 -46.5t30 -92.5q0 -39 -16 -69.5t-41.5 -46.5t-57 -23.5t-62.5 0t-56.5 23.5 t-41.5 46.5t-16 69.5z" />
<glyph unicode=";" horiz-adv-x="532" d="M88 113q0 54 30 89.5t73 43t85.5 -2.5t72.5 -47t30 -90q0 -19 -10.5 -54t-81.5 -253h-142l39 203q-44 8 -70 36.5t-26 74.5zM92 723q0 55 30 92.5t73 46.5t85.5 0t72.5 -46.5t30 -92.5q0 -39 -16 -69.5t-41.5 -46.5t-57 -23.5t-62.5 0t-56.5 23.5t-41.5 46.5t-16 69.5z " />
<glyph unicode="&#x3c;" horiz-adv-x="792" d="M39 629v30l471 467l162 -159v-13l-324 -311l324 -309v-12l-162 -160z" />
<glyph unicode="=" horiz-adv-x="874" d="M41 338v225h747v-225h-747zM41 694v228h747v-228h-747z" />
<glyph unicode="&#x3e;" horiz-adv-x="813" d="M80 322v12l323 309l-323 311v13l162 159l471 -467v-30l-471 -467z" />
<glyph unicode="?" horiz-adv-x="1269" d="M53 1004q1 110 45.5 195.5t121 137.5t173 78.5t207.5 26.5q89 0 169 -15t151 -49t122 -83t80 -122.5t27 -162.5q0 -71 -21 -128t-53 -92t-77.5 -65.5t-85 -47.5t-85 -38t-69.5 -37q-78 -52 -78 -190v-11h-270v13q1 81 15 144.5t32.5 101.5t53 70.5t58 47.5t66.5 37 q24 13 65 30t65.5 29t51 31t39 45.5t12.5 61.5q0 56 -24 96.5t-65.5 60.5t-85.5 28.5t-97 8.5q-67 0 -127 -23t-100 -70t-41 -109h-275zM379 154q0 45 18 80t47 53t64 26.5t70 0t64 -26.5t47 -53t18 -80q0 -44 -18 -79t-47 -53t-64 -26t-70 0t-64 26t-47 53t-18 79z" />
<glyph unicode="@" horiz-adv-x="1955" d="M88 498q0 157 47 292t128.5 232t190.5 165.5t234.5 102t259.5 33.5q186 0 342.5 -51t274 -149t184 -254t66.5 -355q0 -247 -98.5 -390.5t-276.5 -143.5q-34 0 -67 9t-66 29t-53.5 60.5t-20.5 95.5q-16 -44 -52 -80.5t-79.5 -57t-86 -31.5t-77.5 -11q-224 0 -374 142 t-150 362q0 219 151 364.5t371 145.5q84 0 167.5 -46.5t117.5 -111.5l16 104l193 -26v-703q0 -24 12 -35t28 -6q52 13 83.5 110.5t31.5 219.5q0 132 -30.5 235t-85 172t-135 113.5t-175.5 64t-211 19.5q-94 0 -184 -25t-171 -77t-142 -124t-96.5 -174t-35.5 -220 q0 -129 36 -232t95.5 -170t141 -111.5t167 -63.5t179.5 -19q174 0 291 71l94 -178q-173 -102 -391 -102q-129 0 -248.5 29t-229 92.5t-190 155.5t-128.5 228t-48 300zM653 500q0 -75 30.5 -139t97.5 -106t159 -42q134 0 208.5 84.5t74.5 212.5q0 122 -72.5 201.5 t-208.5 79.5q-135 0 -212 -87t-77 -204z" />
<glyph unicode="A" d="M-47 0l643 1434h295l643 -1434h-295l-121 270h-749l-123 -270h-293zM477 522h533l-267 611z" />
<glyph unicode="B" horiz-adv-x="1398" d="M92 0v1434h680q108 0 197 -25t156 -74.5t104 -130t37 -186.5q0 -88 -46 -161.5t-141 -115.5q59 -18 106 -58t72.5 -87.5t38.5 -92t13 -81.5q0 -116 -41.5 -200t-117 -131.5t-168.5 -69t-210 -21.5h-680zM358 252h414q46 0 89.5 8.5t85.5 26.5t67.5 53.5t25.5 83.5 q0 61 -44.5 107.5t-104.5 68t-119 21.5h-414v-369zM358 858h414q119 0 172 43t53 111q0 27 -11.5 55.5t-36 56.5t-70.5 46t-107 18h-414v-330z" />
<glyph unicode="C" horiz-adv-x="1351" d="M59 707v8q0 111 28 215q29 108 90.5 205t149.5 169.5t214.5 115t277.5 42.5q148 0 283 -55.5t244 -163.5l-181 -174q-69 67 -159 101t-187 34q-125 0 -222.5 -43.5t-154.5 -115.5q-58 -73 -88 -159q-29 -84 -28 -173v-6q1 -93 29 -177t84.5 -154.5t154.5 -112.5t225 -42 q96 0 194 38.5t167 107.5l184 -187q-216 -213 -545 -213q-155 0 -282.5 41.5t-214.5 111.5t-147 165.5t-87.5 201t-28.5 220.5z" />
<glyph unicode="D" horiz-adv-x="1439" d="M82 0v1434h563q143 0 262.5 -39.5t201.5 -106.5t139.5 -158t84.5 -191.5t29 -209.5v-16q0 -102 -24 -199q-26 -104 -82.5 -198t-139 -164t-204 -111t-267.5 -41h-563zM350 260h295q118 0 208 40.5t142 108.5q53 67 77 149q23 75 22 159v14q-2 87 -29.5 164.5t-80 142 t-140 102.5t-199.5 38h-295v-918z" />
<glyph unicode="E" horiz-adv-x="1277" d="M96 0v1434h1073v-263h-804v-329h776v-252h-776v-324h804v-266h-1073z" />
<glyph unicode="F" horiz-adv-x="1216" d="M100 0v1432h1030v-261h-759v-391h719v-250h-719v-530h-271z" />
<glyph unicode="G" horiz-adv-x="1492" d="M43 698q0 154 48.5 292t140 247t238.5 173.5t331 64.5q147 0 285 -56.5t247 -163.5l-168 -172q-71 68 -169.5 106t-194.5 38q-150 0 -263.5 -71t-172 -190.5t-58.5 -267.5q0 -73 17 -140.5t55 -130t94 -108t140.5 -73t187.5 -27.5q90 0 166 25.5t157 89.5v254h-366v241 h614v-604q-110 -127 -249 -192.5t-322 -65.5q-157 0 -285.5 40t-215 108.5t-145.5 162.5t-85.5 199t-26.5 221z" />
<glyph unicode="H" horiz-adv-x="1499" d="M102 -2v1434h271v-605h704v605h269v-1434h-269v580h-704v-580h-271z" />
<glyph unicode="I" horiz-adv-x="524" d="M102 0v1434h269v-1434h-269z" />
<glyph unicode="J" horiz-adv-x="1456" d="M43 596h266q0 -371 363 -371q177 0 268.5 107.5t91.5 279.5v570h-432v252h703v-822q0 -186 -74.5 -330.5t-219 -227.5t-337.5 -83q-138 0 -253 41t-199 118.5t-130.5 197t-46.5 268.5z" />
<glyph unicode="K" horiz-adv-x="1341" d="M100 0v1434h271v-607l544 607h336v-9l-637 -686l691 -723v-16h-334l-600 635v-635h-271z" />
<glyph unicode="L" horiz-adv-x="1198" d="M100 0v1434h271v-1184h737v-250h-1008z" />
<glyph unicode="M" horiz-adv-x="1773" d="M102 -2v1434h312l448 -617l449 617h309v-1434h-270v1040l-467 -622h-54l-456 624v-1042h-271z" />
<glyph unicode="N" horiz-adv-x="1460" d="M121 0v1434h219l702 -889v891h271v-1436h-168v-2l-754 969v-967h-270z" />
<glyph unicode="O" horiz-adv-x="1607" d="M37 707q0 115 28.5 223.5t88.5 205t145.5 169t208 115t266.5 42.5t266 -42.5t208 -115t146 -168.5t88 -204q27 -105 27 -215v-8q-1 -114 -29 -220t-87 -201.5t-143 -166.5t-206.5 -112.5t-269.5 -41.5t-269.5 41t-208 111t-145 164.5t-87 201.5t-27.5 222zM303 702 q2 -74 18.5 -141.5t53 -131.5t89 -111t132.5 -75t178 -28t178 28.5t132.5 76t88.5 111.5t52.5 132t17.5 142v9q-1 70 -15 138q-16 72 -52 138t-89.5 116.5t-134 80.5t-178.5 30t-179 -30.5t-135 -81t-91 -117.5t-53 -139q-14 -64 -13 -130v-17z" />
<glyph unicode="P" horiz-adv-x="1384" d="M100 0v1434q112 0 338 1t338 1q102 0 189 -28.5t148 -77t104.5 -113.5t63.5 -138t20 -150t-20 -150t-63.5 -138t-104.5 -113.5t-148 -77t-189 -28.5h-405v-422h-271zM371 670h405q63 0 113.5 22t81.5 57.5t47.5 81t16.5 93.5t-17 93.5t-48 81t-81.5 57.5t-112.5 22h-405 v-508z" />
<glyph unicode="Q" horiz-adv-x="1601" d="M33 719v8q0 110 27 213q27 108 86.5 204.5t145 168t208.5 113.5t270 42t269 -41t206 -110.5t143 -164t87 -199.5t30 -218v-12q0 -262 -133 -455l170 -168l-190 -190l-174 172q-167 -107 -408 -107q-148 0 -271.5 41.5t-208 112.5t-143.5 167t-86 202.5t-28 220.5z M299 713q1 -72 20 -141.5t58 -132.5t93.5 -110.5t132 -75.5t167.5 -28q117 0 210.5 46t150 121t84.5 166q25 80 25 162q0 11 -1 23q-3 73 -19.5 139.5t-52.5 130t-88 110t-131.5 74.5t-177.5 28q-121 0 -215.5 -45.5t-149 -120.5t-81.5 -163q-25 -83 -25 -170v-13z" />
<glyph unicode="R" horiz-adv-x="1382" d="M102 0v1436q114 0 341 -1t341 -1q123 -1 222.5 -40.5t163 -105.5t97 -151t33.5 -179q0 -78 -19 -146.5t-59.5 -131t-113 -107.5t-170.5 -65l434 -490v-18h-321l-418 479h-260v-479h-271zM373 723h411q121 0 183.5 67t62.5 162t-63 162.5t-183 67.5h-411v-459z" />
<glyph unicode="S" horiz-adv-x="1353" d="M18 315l226 117q45 -113 154.5 -170t248.5 -57q66 0 123.5 12t105 36.5t74.5 67.5t27 99q0 176 -332 211q-69 8 -124 18t-115 27t-106 39t-88.5 55t-69.5 74t-43.5 97.5t-16.5 123.5q0 106 49 188.5t130.5 131t177 73t198.5 25.5q422 0 563 -285l-215 -111 q-39 65 -133.5 112.5t-206.5 47.5q-145 0 -221 -53t-76 -137q0 -45 24.5 -77.5t72.5 -52.5t99 -31t124 -19q70 -9 126.5 -19t118.5 -28t108.5 -41t90 -57.5t71.5 -78t45 -102.5t17 -131q0 -93 -32 -169.5t-88 -129.5t-132.5 -89t-164 -52.5t-185.5 -16.5q-114 0 -207.5 18.5 t-174.5 59t-142.5 109.5t-100.5 165z" />
<glyph unicode="T" horiz-adv-x="1259" d="M16 1188v246h1180v-246h-455v-1188h-270v1188h-455z" />
<glyph unicode="U" horiz-adv-x="1521" d="M100 610v822h269v-822q0 -190 99.5 -292.5t270.5 -102.5q166 0 258.5 105t92.5 290v822h268v-822q0 -156 -46 -278.5t-129.5 -201.5t-196.5 -120t-249 -41q-131 0 -245 40t-202 117.5t-139 202t-51 281.5z" />
<glyph unicode="V" horiz-adv-x="1540" d="M2 1434h303l432 -1117l434 1117h304l-596 -1440h-281z" />
<glyph unicode="W" horiz-adv-x="2230" d="M10 1434h303l338 -1035l348 1035h177l358 -1035l328 1035h303l-516 -1434h-230l-178 451l-151 452l-150 -457l-174 -446h-229z" />
<glyph unicode="X" horiz-adv-x="1443" d="M45 0v8l498 717l-484 696v13h297l359 -535l358 535h297v-13l-485 -696l499 -717v-8h-303l-366 539l-367 -539h-303z" />
<glyph unicode="Y" horiz-adv-x="1429" d="M18 1421v13h322l365 -592l380 592h326v-13l-571 -829v-592h-271v592z" />
<glyph unicode="Z" horiz-adv-x="1200" d="M16 0v156l727 1024h-708v254h1102v-127l-744 -1051h744v-256h-1121z" />
<glyph unicode="[" horiz-adv-x="638" d="M100 -219v1818h494v-221h-242v-1372h242v-225h-494z" />
<glyph unicode="\" horiz-adv-x="935" d="M-10 1569h262l682 -1737h-260z" />
<glyph unicode="]" horiz-adv-x="661" d="M16 6h242v1372h-242v221h494v-1818h-494v225z" />
<glyph unicode="^" horiz-adv-x="1210" d="M2 682v12l487 744h158l504 -742v-14h-274l-308 471l-309 -471h-258z" />
<glyph unicode="_" horiz-adv-x="985" d="M-23 -6h967v-219h-967v219z" />
<glyph unicode="`" horiz-adv-x="485" d="M-14 1421v11h295l151 -279v-12h-221z" />
<glyph unicode="a" horiz-adv-x="1251" d="M27 506q0 169 70.5 290.5t188.5 179.5q116 58 262 58h5q102 0 190.5 -44.5t130.5 -119.5l9 140h239v-1010h-235l-13 147q-40 -83 -136 -129.5t-191 -48.5h-5q-148 1 -262 59q-116 60 -184.5 182.5t-68.5 295.5zM276 506q0 -141 85 -224t214 -83q71 0 128.5 26.5t93 69.5 t54.5 98t19 112t-19 112t-54.5 98t-93 69.5t-128.5 26.5q-129 0 -214 -82t-85 -223z" />
<glyph unicode="b" horiz-adv-x="1280" d="M82 0v1432h250v-560q41 73 142.5 117.5t189.5 44.5q229 0 371.5 -137.5t142.5 -390.5q0 -162 -68.5 -283.5t-186 -183.5t-266.5 -62q-221 0 -325 162l-17 -139h-233zM348 506q0 -133 85.5 -215t207.5 -82t204.5 83.5t82.5 213.5q0 133 -82 214t-205 81q-121 0 -207 -83 t-86 -212z" />
<glyph unicode="c" horiz-adv-x="976" d="M23 504q0 131 42.5 234t116 168t169.5 98.5t208 33.5q110 0 196.5 -34.5t168.5 -114.5l-158 -166q-89 80 -203 80q-127 0 -209 -84t-82 -215q0 -139 82 -217t205 -78q137 0 221 84l168 -164q-86 -85 -180 -122.5t-209 -37.5q-111 0 -207.5 34t-170 99t-116 168t-42.5 234 z" />
<glyph unicode="d" horiz-adv-x="1265" d="M35 506q0 253 142.5 390.5t371.5 137.5q88 0 189.5 -44.5t142.5 -117.5v560h249v-1432h-233l-16 139q-104 -162 -326 -162q-228 0 -374 142.5t-146 386.5zM285 506q0 -130 82 -213.5t204 -83.5t207.5 82t85.5 215q0 129 -86 212t-207 83q-123 0 -204.5 -80.5 t-81.5 -214.5z" />
<glyph unicode="e" horiz-adv-x="1193" d="M37 512q0 233 146.5 380.5t389.5 147.5q261 0 399 -160q115 -133 116 -366q0 -47 -5 -98h-788q12 -96 92 -155.5t209 -59.5q73 0 151 26t119 68l160 -158q-76 -80 -194 -122t-240 -42q-258 0 -406.5 146.5t-148.5 392.5zM299 623h545q-12 96 -82 146t-180 50 q-104 0 -180 -50t-103 -146z" />
<glyph unicode="f" horiz-adv-x="825" d="M-25 776v219h189v68q0 98 31.5 175t85.5 124t122 71.5t146 24.5q135 0 262 -82l-92 -186q-80 53 -154 53q-68 0 -109.5 -45.5t-41.5 -134.5v-68h297v-219h-297v-776h-250v776h-189z" />
<glyph unicode="g" horiz-adv-x="1130" d="M31 -180h248q0 -96 77 -156.5t187 -60.5q111 0 183.5 58t72.5 159q0 66 -38.5 113.5t-95 68.5t-122.5 21q-237 0 -374.5 135.5t-137.5 371.5q0 153 69 271t185 179.5t258 61.5q146 0 231 -57l100 127l185 -139l-113 -144q107 -121 107 -299q0 -51 -10.5 -102 t-33.5 -103.5t-66.5 -98t-102.5 -72.5q117 -59 163 -142.5t46 -191.5q0 -206 -141.5 -328.5t-364.5 -122.5q-146 0 -262 55.5t-183 159t-67 236.5zM279 530q0 -128 75.5 -204t188.5 -76q111 0 186.5 76.5t75.5 203.5t-76 206t-186 79q-112 0 -188 -78t-76 -207z" />
<glyph unicode="h" horiz-adv-x="1189" d="M82 0v1434h250v-562q117 152 315 152q209 0 311.5 -130.5t102.5 -361.5v-532h-250v530q0 132 -62 197.5t-167 65.5q-109 0 -179.5 -80.5t-70.5 -196.5v-516h-250z" />
<glyph unicode="i" horiz-adv-x="475" d="M59 1294q0 40 16.5 71.5t42.5 48t58 24.5q16 4 31 4t31 -4q31 -8 57 -24.5t42.5 -48t16.5 -71.5t-16.5 -71.5t-42.5 -48t-57 -24t-62.5 0t-57.5 24t-42.5 48t-16.5 71.5zM82 0h250v1014h-250v-1014z" />
<glyph unicode="j" horiz-adv-x="458" d="M-231 -559q104 -68 223 -68q166 0 251 109.5t85 302.5v1229h-248v-1229q0 -90 -21 -132.5t-79 -45.5h-10q-50 0 -111 28zM57 1296q0 40 16.5 71.5t42.5 48t58 24.5q15 3 30.5 3t31.5 -3q31 -8 57 -24.5t42.5 -48t16.5 -71.5t-16.5 -71.5t-42.5 -48t-57 -24.5 q-16 -3 -31.5 -3t-30.5 3q-32 8 -58 24.5t-42.5 48t-16.5 71.5z" />
<glyph unicode="k" horiz-adv-x="1054" d="M76 0v1432h250v-828l340 406h299v-15l-412 -460l469 -517v-18h-301l-395 457v-457h-250z" />
<glyph unicode="l" horiz-adv-x="466" d="M82 0v1432h248v-1432h-248z" />
<glyph unicode="m" horiz-adv-x="1835" d="M82 0v1012h231l19 -123q37 71 111 105t155 34q99 0 179 -45t120 -143q64 101 147.5 142.5t190.5 41.5q212 0 320 -124t108 -355v-545h-250v545q0 108 -47 177t-143 69q-98 0 -158.5 -73t-60.5 -177v-541h-250v541q0 106 -54.5 180t-152.5 74q-97 0 -156 -74.5t-59 -179.5 v-541h-250z" />
<glyph unicode="n" horiz-adv-x="1208" d="M76 0v1012h223l16 -138q155 152 332 152q185 0 306.5 -132.5t121.5 -363.5v-530h-250v528q0 120 -63.5 195.5t-179.5 75.5q-113 0 -185.5 -82.5t-72.5 -200.5v-516h-248z" />
<glyph unicode="o" horiz-adv-x="1175" d="M35 504q0 145 62.5 264t182.5 190.5t277 71.5t278.5 -71.5t185.5 -190.5t64 -264q0 -146 -62 -265t-183 -190.5t-281 -71.5q-159 0 -280 71.5t-182.5 190t-61.5 265.5zM285 504q0 -79 29.5 -145t93.5 -109t151 -43q130 0 202.5 87t72.5 210q0 120 -77 209.5t-198 89.5 q-128 0 -201 -88.5t-73 -210.5z" />
<glyph unicode="p" horiz-adv-x="1280" d="M82 -422v1432h233l17 -140q55 80 144.5 121t182.5 41q152 0 271.5 -70t183.5 -190t64 -268q0 -149 -59 -268t-176 -190t-275 -71q-59 0 -118.5 12t-121.5 46t-96 86v-541h-250zM354 504q0 -121 81 -204t206 -83t206 83t81 204q0 123 -78 205t-209 82t-209 -82t-78 -205z " />
<glyph unicode="q" horiz-adv-x="1265" d="M33 504q0 148 64 268t183 190t271 70q93 0 183 -41t145 -121l16 140h233v-1432h-249v541q-34 -52 -96 -86t-121.5 -46t-118.5 -12q-158 0 -275 71t-176 190t-59 268zM283 504q0 -121 80.5 -204t205.5 -83t206 83t81 204q0 123 -78 205t-209 82t-208.5 -82t-77.5 -205z " />
<glyph unicode="r" horiz-adv-x="888" d="M82 0v1010h231l19 -117q53 85 122.5 114t161.5 29q76 0 147.5 -27t113.5 -69l-113 -217q-40 34 -80.5 49.5t-99.5 15.5q-108 0 -180 -62.5t-72 -193.5v-532h-250z" />
<glyph unicode="s" horiz-adv-x="1005" d="M27 145l123 179q52 -58 156 -99t192 -43h8q68 0 120 32q54 34 54 89q0 52 -46.5 76.5t-154.5 30.5q-62 5 -117 16t-111.5 34.5t-97.5 57t-66.5 86.5t-25.5 119q0 83 40 146.5t105 99t136 52.5t145 17q124 0 216.5 -29.5t177.5 -103.5l-140 -164q-95 90 -249 90 q-89 0 -137 -26.5t-48 -73.5q0 -98 189 -110q46 -3 84 -7.5t84 -15t82 -25.5t71.5 -40t58.5 -57t37.5 -77.5t14.5 -101.5q0 -142 -113.5 -237t-320.5 -95q-140 0 -253 37t-214 143z" />
<glyph unicode="t" horiz-adv-x="817" d="M-4 793v215h188v260l250 26v-286h279v-215h-281v-437q0 -70 35.5 -106.5t95.5 -36.5q57 0 121 31l70 -213q-100 -40 -207 -43h-20q-153 0 -245 88q-98 94 -98 280v437h-188z" />
<glyph unicode="u" horiz-adv-x="1226" d="M88 479v531h250v-529q0 -120 64 -195t180 -75q113 0 185.5 82.5t72.5 200.5v516h247v-1012h-223l-16 137q-80 -79 -159.5 -115t-182.5 -36q-186 0 -302 132.5t-116 362.5z" />
<glyph unicode="v" horiz-adv-x="1167" d="M-4 1012h272l140 -334l155 -426l154 422l139 338h272l-430 -1016h-270z" />
<glyph unicode="w" horiz-adv-x="1867" d="M61 1012h279l231 -760l220 760h219l221 -760l231 760h277l-353 -1016h-290l-105 299l-90 338l-90 -338l-104 -299h-291z" />
<glyph unicode="x" horiz-adv-x="1089" d="M8 0v6l352 506l-331 489v9h299l198 -342l199 342h299v-9l-332 -489l352 -506v-6h-299l-219 338l-219 -338h-299z" />
<glyph unicode="y" horiz-adv-x="1148" d="M-6 1010h291l153 -437l97 -284l104 278l184 443h277l-617 -1432h-274l188 438z" />
<glyph unicode="z" horiz-adv-x="935" d="M18 0v104l484 678h-447v228h809v-119l-465 -645h469v-246h-850z" />
<glyph unicode="{" horiz-adv-x="692" d="M47 588v186q37 0 64 11t42 27t23.5 40.5t11 45.5t2.5 48v287q0 200 111.5 289t333.5 49v-195q-99 26 -140.5 -10.5t-41.5 -144.5v-275v-21q0 -37 -0.5 -55.5t-4 -48t-13 -46.5t-24.5 -37t-41 -34t-61 -24q30 -8 53.5 -21t38.5 -24.5t25.5 -32.5t16 -34.5t8 -41.5 t2.5 -41.5v-47.5v-21v-275q0 -108 34.5 -137.5t147.5 -17.5v-195q-222 -40 -333.5 49t-111.5 289v287q0 27 -2.5 48t-11 45.5t-23.5 40.5t-42 27t-64 11z" />
<glyph unicode="|" horiz-adv-x="626" d="M152 -172v1712h241v-1712h-241z" />
<glyph unicode="}" horiz-adv-x="696" d="M-4 -14q113 -12 147.5 17.5t34.5 137.5v275q0 106 8 139q19 82 109 116q12 5 27 9q-30 9 -53.5 22t-38.5 24.5t-25.5 32.5t-16 34.5t-8 41.5t-2.5 42v48v21v275q0 108 -41.5 144.5t-140.5 10.5v195q222 40 333 -49t111 -289v-287q0 -35 5 -61t18.5 -53.5t44 -42.5 t76.5 -15v-186q-46 0 -76.5 -15t-44 -42.5t-18.5 -53.5t-5 -61v-287q0 -200 -111 -289t-333 -49v195z" />
<glyph unicode="~" horiz-adv-x="1060" d="M-8 539q26 93 79 153.5t112 83.5t128 23q53 0 105 -19t88 -43.5t82.5 -52t83.5 -39.5q53 -16 89 14.5t66 104.5l183 -62q-23 -71 -58.5 -126.5t-86.5 -95t-114 -47.5t-141 16q-39 13 -98.5 50.5t-112 65t-94.5 27.5q-93 0 -143 -117z" />
<glyph unicode="&#xa1;" horiz-adv-x="653" d="M123 809q0 44 18 79t47 53t64 26t70 0t64 -26t47 -53t18 -79q0 -45 -18 -80t-47 -53t-64 -26.5t-70 0t-64 26.5t-47 53t-18 80zM154 -475h268v1057h-268v-1057z" />
<glyph unicode="&#xa2;" horiz-adv-x="1120" d="M74 479q0 186 111 330t298 188v150h232v-148q157 -31 272 -143l-145 -162q-40 40 -107 65t-125 25q-119 0 -203.5 -88.5t-84.5 -216.5t84.5 -215.5t203.5 -87.5q54 0 118.5 31.5t107.5 75.5l153 -181q-123 -116 -274 -145v-143h-232v147q-182 41 -295.5 184.5 t-113.5 333.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1288" d="M90 0v256q98 24 145 101t44 188h-189v227h146q-37 146 -37 207q0 225 139 351t383 126q224 0 353.5 -105.5t139.5 -326.5h-247q-9 99 -69.5 147t-174.5 48q-132 0 -192 -65.5t-60 -170.5q0 -67 33 -211h424v-227h-377q7 -84 -24.5 -168.5t-108.5 -120.5h803v-256h-1131z " />
<glyph unicode="&#xa4;" horiz-adv-x="1107" d="M27 328l118 116q-100 198 9 402l-127 121l188 188l119 -121q94 40 200 39t197 -41l123 123l186 -188l-118 -119q47 -99 48 -204.5t-46 -199.5l116 -116l-184 -185l-121 119q-100 -40 -202 -39t-199 39l-119 -121zM315 647q0 -89 60.5 -155t156.5 -66q99 0 157.5 65.5 t58.5 155.5t-58.5 155.5t-157.5 65.5q-96 0 -156.5 -66t-60.5 -155z" />
<glyph unicode="&#xa5;" horiz-adv-x="1472" d="M18 1421v13h322l365 -592l380 592h326v-13l-471 -684h215v-198h-315v-105h315v-194h-315v-240h-271v240h-317v194h317v105h-317v198h221z" />
<glyph unicode="&#xa6;" horiz-adv-x="624" d="M164 -186v755h256v-755h-256zM164 698v756h256v-756h-256z" />
<glyph unicode="&#xa7;" horiz-adv-x="1232" d="M80 160l162 170q70 -76 155.5 -103.5t200.5 -25.5q107 0 183 25.5t79 72.5q3 42 -68.5 67t-185.5 23q-97 -2 -187 15t-167 52.5t-121.5 99.5t-40.5 149q3 60 51 119t131 85q-89 19 -130.5 84t-37.5 135q4 75 42 133.5t93.5 93t129 57t140.5 30.5t136 8q83 0 160 -12 t160 -48t133 -94l-178 -151q-53 50 -119.5 67t-165.5 17q-259 0 -264 -94q-1 -28 21.5 -49t61 -32t79.5 -16.5t83 -5.5q64 0 120.5 -5t119.5 -16.5t112 -35t88.5 -56.5t58.5 -85t13 -117q-6 -62 -55.5 -121t-120.5 -80q87 -26 132 -91.5t42 -127.5q-3 -74 -42.5 -132 t-96.5 -92.5t-131 -57t-139 -30.5t-127 -8q-356 0 -510 183zM350 713q-5 -75 123 -104.5t259.5 1t135.5 101.5q5 80 -123 113.5t-259.5 0.5t-135.5 -112z" />
<glyph unicode="&#xa8;" horiz-adv-x="1046" d="M100 1341q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM610 1341q0 45 21.5 78.5t53 48.5t69 15t69 -15t53 -48.5t21.5 -78.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5z " />
<glyph unicode="&#xa9;" horiz-adv-x="1894" d="M180 725q0 153 52 288.5t147 238t238.5 162t316.5 59.5t315 -59.5t233.5 -162.5t141 -238t49.5 -288t-49.5 -288t-141 -238t-233.5 -162.5t-315 -59.5t-316.5 59.5t-238.5 162t-147 238t-52 288.5zM397 725q0 -155 64 -280t187.5 -199t285.5 -74q122 0 222 43t165 118 t100 175.5t35 216.5t-35 216.5t-100 175.5t-165 118t-222 43q-162 0 -285.5 -74t-187.5 -199t-64 -280zM551 721q0 -184 118.5 -287.5t297.5 -103.5q165 0 297 129l-134 125q-72 -72 -163 -72q-94 0 -160 56.5t-66 152.5q0 103 65 161t161 58q90 0 161 -68l132 121 q-127 127 -293 127q-179 0 -297.5 -107t-118.5 -292z" />
<glyph unicode="&#xaa;" horiz-adv-x="849" d="M6 1104q0 166 97 257t249 89q67 0 124.5 -29t86.5 -78l4 91h187v-662h-185l-6 96q-27 -54 -89.5 -84t-125.5 -32q-153 0 -247.5 92t-94.5 260zM31 477v178h727v-178h-727zM203 1104q0 -79 48 -125.5t120 -46.5q45 0 81 19t54.5 49.5t27 67t0 73t-27 67t-54.5 49.5t-81 19 q-72 0 -120 -46.5t-48 -125.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1009" d="M25 651l284 453h266l-282 -453l282 -454h-266zM385 651l285 453h266l-283 -453l283 -454h-266z" />
<glyph unicode="&#xac;" horiz-adv-x="958" d="M31 569v213h809v-473h-215v260h-594z" />
<glyph unicode="&#xad;" horiz-adv-x="937" d="M72 553v229h755v-229h-755z" />
<glyph unicode="&#xae;" horiz-adv-x="1894" d="M197 719q0 153 49.5 288t141.5 237.5t233.5 162t314.5 59.5t315 -59.5t234 -162t142 -237.5t50 -288t-50 -288t-142 -238t-234 -162.5t-315 -59.5q-139 0 -258 38.5t-206.5 108t-150 163t-93.5 205t-31 233.5zM414 719q1 -156 61 -280t180 -198.5t281 -74.5q122 0 222 43 t165 118t100 175.5t35 216.5t-35 216.5t-100 175.5t-165 118t-222 43q-161 0 -281 -74.5t-180 -198.5t-61 -280zM641 344h199v234h69l178 -234h213v43l-186 229q44 10 78.5 32.5t55 52.5t31.5 66.5t8 74t-14 74.5t-36 69.5t-58 57t-79.5 39t-100.5 14.5h-358v-752zM840 745 v173h159q41 0 65 -27t24 -59.5t-24 -59.5t-65 -27h-159z" />
<glyph unicode="&#xb0;" horiz-adv-x="622" d="M14 1219q0 122 83 191t194 69t192.5 -69t81.5 -191q0 -123 -82 -192t-194 -69q-111 0 -193 69t-82 192zM174.5 1184.5q6.5 -33.5 37.5 -57t77 -23.5t77 23.5t38 57t0 66.5t-38 56.5t-75 23.5q-46 0 -77 -23.5t-38.5 -56.5t-1 -66.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="960" d="M41 39v227h838v-227h-838zM41 655v234h293v303h250v-303h295v-234h-295v-303h-250v303h-293z" />
<glyph unicode="&#xb2;" horiz-adv-x="751" d="M63 811v197q0 61 23.5 104t61 65t83 35.5t91 21.5t83 18t61 30.5t23.5 52.5q0 86 -127 86q-47 0 -80 -19.5t-34 -55.5h-180q1 76 45.5 130.5t108 78.5t136.5 24q144 0 229 -60t85 -184q0 -55 -18 -96t-47.5 -63.5t-67.5 -38.5t-77 -22.5t-77 -15t-67.5 -17t-47.5 -27.5 t-18 -47v-27h405v-170h-594z" />
<glyph unicode="&#xb3;" horiz-adv-x="800" d="M41 1030h188q22 -72 127 -72q53 0 92 20t39 58q0 40 -30 66t-99 26h-116v138h108q65 0 95.5 15.5t31.5 64.5q0 26 -28.5 52.5t-96.5 26.5q-57 0 -91.5 -15t-45.5 -58h-160q0 77 47 130t112 75t140 22t142 -24t114.5 -78.5t48.5 -130.5q0 -48 -25.5 -92t-72.5 -58 q53 -17 85 -61.5t32 -94.5q0 -53 -20.5 -96t-53 -70t-76.5 -45.5t-88 -26t-90 -7.5q-56 0 -108.5 13.5t-98.5 40t-73.5 73.5t-28.5 108z" />
<glyph unicode="&#xb4;" horiz-adv-x="489" d="M-8 1141v12l151 279h295v-11l-225 -280h-221z" />
<glyph unicode="&#xb6;" horiz-adv-x="1712" d="M23 893q0 82 22 160t68 147t111.5 121t157.5 82.5t200 30.5h1038v-254h-190v-1616h-271v1616h-379v-1616h-270v792q-117 12 -212 62.5t-154 125t-90 164.5t-31 185z" />
<glyph unicode="&#xb7;" horiz-adv-x="462" d="M35 678q0 73 48.5 114t107 41t107 -41t48.5 -114t-48.5 -114t-107 -41t-107 41t-48.5 114z" />
<glyph unicode="&#xb8;" horiz-adv-x="782" d="M68 -346l108 92q29 -39 82.5 -56t101.5 -4t60 52q11 37 -17.5 65.5t-71.5 30t-85 -23.5l-60 47l66 192h180l-33 -104q67 -2 114.5 -25t68 -57t29.5 -75t-1 -79q-15 -69 -63.5 -116.5t-113.5 -64t-134 -11t-132 41.5t-99 95z" />
<glyph unicode="&#xb9;" horiz-adv-x="581" d="M109 811v160h102v440h-94v154h274v-594h88v-160h-370z" />
<glyph unicode="&#xba;" horiz-adv-x="765" d="M31 1133v110q0 81 28.5 147t76 106t107.5 62t122.5 22t122.5 -22t107.5 -62t76 -106t28.5 -147v-110q0 -81 -28.5 -147t-76 -106t-107.5 -62t-122.5 -22t-122.5 22t-107.5 62t-76 106t-28.5 147zM207 1128q0 -52 23 -90t58 -55t75.5 -17t75.5 17t58 55t23 90v113 q0 51 -23 88.5t-58 55t-75.5 17.5t-75.5 -17t-58 -54.5t-23 -89.5v-113z" />
<glyph unicode="&#xbb;" horiz-adv-x="942" d="M-6 197l282 454l-282 453h266l285 -453l-285 -454h-266zM354 197l283 454l-283 453h267l284 -453l-284 -454h-267z" />
<glyph unicode="&#xbc;" horiz-adv-x="1566" d="M74 686v156h102v444h-94v154h274v-598h101v-156h-383zM201 -59l821 1507h199l-824 -1507h-196zM817 287l346 465h236v-461h69v-158h-69v-133h-180v133h-394zM1004 291h215v287z" />
<glyph unicode="&#xbd;" horiz-adv-x="1658" d="M74 686v156h102v444h-94v154h274v-598h101v-156h-383zM201 -59l821 1507h199l-824 -1507h-196zM944 -8v196q0 61 23.5 104t61 65t83 36t91 22t83 18t61 30.5t23.5 52.5q0 86 -127 86q-47 0 -80.5 -20t-34.5 -56h-180q1 76 45.5 131t108.5 79t137 24q144 0 228.5 -60 t84.5 -184q0 -55 -18 -96t-47.5 -63.5t-67 -38.5t-77 -22.5t-77 -15.5t-67 -17.5t-47.5 -27.5t-18 -47v-26h405v-170h-594z" />
<glyph unicode="&#xbe;" horiz-adv-x="1730" d="M78 932h180q22 -70 123 -70q51 0 89 19.5t38 56.5q0 38 -29 63t-96 25h-113v133h105q64 0 93 14.5t30 61.5q0 26 -27.5 52t-93.5 26q-55 0 -88 -15t-43 -57h-156q0 74 46 125.5t109 72.5t134 21q73 0 138 -23t110.5 -76t46.5 -126q0 -46 -25.5 -88t-70.5 -55 q52 -16 82 -59.5t30 -90.5q0 -64 -29 -112t-76.5 -74t-100 -38.5t-109.5 -12.5q-43 0 -83.5 7.5t-79.5 25t-68 43.5t-47 65t-19 86zM342 -59l821 1507h199l-823 -1507h-197zM940 287l346 465h236v-461h69v-158h-69v-133h-181v133h-393zM1126 291h215v287z" />
<glyph unicode="&#xbf;" horiz-adv-x="1226" d="M35 45q-2 -112 42 -197.5t122 -136t175 -75.5t210 -25q111 -1 207.5 25.5t172.5 78.5t120.5 137.5t45.5 194.5l-274 2q-3 -92 -84 -147.5t-186 -55.5q-52 0 -96.5 9t-85.5 29.5t-65 60.5t-24 96q0 35 12.5 61.5t39.5 45.5t51.5 31t65.5 29t65 30q43 22 66.5 37t58 47.5 t53 70.5t32.5 101.5t15 144.5v10l-270 2l2 -10q-2 -73 -20 -118t-60 -72q-24 -16 -69.5 -38t-85 -40t-85 -49t-77 -66t-53 -90.5t-21.5 -122.5zM479 899q0 44 18 79t47 52.5t64 26t69.5 0t63.5 -26.5t46.5 -53t17.5 -80q0 -44 -17.5 -79t-46.5 -52.5t-64 -26t-70 0t-64 26.5 t-46.5 53t-17.5 80z" />
<glyph unicode="&#xc0;" d="M-47 0l643 1434h295l643 -1434h-295l-121 270h-749l-123 -270h-293zM397 1862v10h295l152 -279v-12h-221zM477 522h533l-267 611z" />
<glyph unicode="&#xc1;" d="M-47 0h293l123 270h749l121 -270h295l-643 1434h-295zM477 522l266 611l267 -611h-533zM631 1581v12l151 279h295v-10l-225 -281h-221z" />
<glyph unicode="&#xc2;" d="M-47 0l643 1434h295l643 -1434h-295l-121 270h-749l-123 -270h-293zM379 1542v12l303 283h137l303 -283v-12h-254l-118 121l-117 -121h-254zM477 522h533l-267 611z" />
<glyph unicode="&#xc3;" d="M-47 0l643 1434h295l643 -1434h-295l-121 270h-749l-123 -270h-293zM373 1649q88 221 270 180q24 -6 57.5 -21.5t58 -26.5t51.5 -16t50.5 10t42.5 52l168 -80q-44 -116 -115 -161t-149 -23q-25 7 -60 26t-60 31t-53.5 17.5t-54.5 -12.5t-49 -62zM477 522h533l-267 611z " />
<glyph unicode="&#xc4;" d="M-47 0h293l123 270h749l121 -270h295l-643 1434h-295zM342 1688q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM477 522l266 611l267 -611h-533zM852 1688q0 39 16 69.5t41 46.5t55.5 23.5 t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5z" />
<glyph unicode="&#xc5;" d="M-47 0l639 1425q-121 66 -121 209q0 116 81 182t191 66q108 0 189.5 -66t81.5 -182q0 -72 -33 -125t-88 -84l641 -1425h-295l-121 270h-749l-123 -270h-293zM477 522h533l-267 611zM620 1636.5q0 -39.5 32 -72t87 -32.5q46 0 77 21.5t37.5 52t-1.5 61.5t-38.5 52.5 t-74.5 21.5q-56 0 -87.5 -32.5t-31.5 -72z" />
<glyph unicode="&#xc6;" horiz-adv-x="1867" d="M-43 0l909 1434h885v-254h-598v-340h576v-250h-576v-330h604v-260h-858v289h-450l-183 -289h-309zM600 528h299v256l21 316z" />
<glyph unicode="&#xc7;" horiz-adv-x="1351" d="M59 707q-1 114 28 222.5t90.5 205.5t149.5 169.5t214.5 115t277.5 42.5q148 0 283 -55.5t244 -163.5l-181 -174q-69 67 -159 101t-187 34q-125 0 -222.5 -43.5t-155 -115.5t-87 -158.5t-28.5 -179.5t29 -177t84.5 -154.5t154.5 -112.5t225 -42q96 0 194 38.5t167 107.5 l184 -187q-203 -200 -508 -213l-29 -92q67 -2 114.5 -25t68 -56.5t29.5 -74.5t-1 -79q-12 -57 -49 -100.5t-86.5 -65.5t-108 -27t-113.5 8t-105 46.5t-80 82.5l108 92q29 -39 82.5 -56t101.5 -4t60 52q11 37 -17.5 65.5t-71.5 30t-85 -23.5l-60 47l64 190q-156 21 -277.5 90 t-194 170.5t-109.5 220t-38 249.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1267" d="M96 0v1434h1073v-263h-804v-329h776v-252h-776v-324h804v-266h-1073zM344 1872v10h295l152 -278v-13h-222z" />
<glyph unicode="&#xc9;" horiz-adv-x="1267" d="M96 0h1073v266h-804v324h776v252h-776v329h804v263h-1073v-1434zM524 1579v12l152 279h295v-10l-226 -281h-221z" />
<glyph unicode="&#xca;" horiz-adv-x="1267" d="M96 0v1434h1073v-263h-804v-329h776v-252h-776v-324h804v-266h-1073zM311 1579v12l303 283h138l303 -283v-12h-254l-119 121l-117 -121h-254z" />
<glyph unicode="&#xcb;" horiz-adv-x="1277" d="M96 0h1073v266h-804v324h776v252h-776v329h804v263h-1073v-1434zM258 1720q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM768 1720q0 45 21.5 78.5t53 48.5t69 15t69 -15t53 -48.5 t21.5 -78.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="524" d="M-92 1845v10h295l151 -278v-12h-221zM102 0v1434h269v-1434h-269z" />
<glyph unicode="&#xcd;" horiz-adv-x="524" d="M102 0h269v1434h-269v-1434zM117 1565v12l151 278h295v-10l-225 -280h-221z" />
<glyph unicode="&#xce;" horiz-adv-x="524" d="M-131 1552v13l303 282h137l303 -282v-13h-254l-118 121l-117 -121h-254zM102 0v1434h269v-1434h-269z" />
<glyph unicode="&#xcf;" horiz-adv-x="524" d="M-133 1696q0 45 21.5 78.5t53 48.5t69 15t69 -15t53 -48.5t21.5 -78.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM102 0h269v1434h-269v-1434zM305 1696q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45 t-84.5 0t-72 45t-29.5 90.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1497" d="M-25 600v229h144v605h530q176 0 319 -54.5t235 -149t141 -218.5t49 -267q0 -120 -28 -229t-87.5 -203.5t-146 -164t-210 -109t-272.5 -39.5h-530v600h-144zM379 248h270q236 0 356.5 132.5t120.5 356.5q0 217 -122.5 335t-354.5 118h-270v-361h276v-229h-276v-352z" />
<glyph unicode="&#xd1;" horiz-adv-x="1515" d="M121 0v1434h219l702 -889v891h271v-1436h-168v-2l-754 969v-967h-270zM395 1661q88 216 264 176q23 -6 56 -21.5t57.5 -26t51 -15.5t49 10t40.5 51l164 -78q-42 -113 -111.5 -156.5t-146.5 -23.5q-24 7 -57.5 25.5t-57.5 30.5t-52 17t-53 -12.5t-48 -60.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1607" d="M37 707q0 115 28.5 223.5t88.5 205t145.5 169t208 115t266.5 42.5t266 -42.5t208 -115t146 -168.5t88 -204.5t27 -222.5t-29 -220t-87 -201.5t-143 -166.5t-206.5 -112.5t-269.5 -41.5t-269.5 41t-208 111t-145 164.5t-87 201.5t-27.5 222zM303 702q2 -74 18.5 -141.5 t53 -131.5t89 -111t132.5 -75t178 -28t178 28.5t132.5 76t88.5 111.5t52.5 132t17.5 142q1 75 -14.5 146.5t-52 138t-90 117t-134 80.5t-178.5 30t-179 -30.5t-135 -81t-91 -117.5t-52.5 -139t-13.5 -147zM475 1878v10h295l152 -278v-13h-222z" />
<glyph unicode="&#xd3;" horiz-adv-x="1607" d="M37 707q0 -115 27.5 -222t87 -201.5t145 -164.5t208 -111t269.5 -41t269.5 41.5t206.5 112.5t143 166.5t87 201.5t29 220t-27 222.5t-88 204.5t-146 168.5t-208 115t-266 42.5t-266.5 -42.5t-208 -115t-145.5 -169t-88.5 -205t-28.5 -223.5zM303 702q-2 75 13.5 147 t52.5 139t91 117.5t135 81t179 30.5t178.5 -30t134 -80.5t90 -117t52 -138t14.5 -146.5q-1 -74 -17.5 -142t-52.5 -132t-88.5 -111.5t-132.5 -76t-178 -28.5t-178 28t-132.5 75t-89 111t-53 131.5t-18.5 141.5zM657 1587v12l152 279h295v-10l-225 -281h-222z" />
<glyph unicode="&#xd4;" horiz-adv-x="1607" d="M37 707q0 115 28.5 223.5t88.5 205t145.5 169t208 115t266.5 42.5t266 -42.5t208 -115t146 -168.5t88 -204.5t27 -222.5t-29 -220t-87 -201.5t-143 -166.5t-206.5 -112.5t-269.5 -41.5t-269.5 41t-208 111t-145 164.5t-87 201.5t-27.5 222zM303 702q2 -74 18.5 -141.5 t53 -131.5t89 -111t132.5 -75t178 -28t178 28.5t132.5 76t88.5 111.5t52.5 132t17.5 142q1 75 -14.5 146.5t-52 138t-90 117t-134 80.5t-178.5 30t-179 -30.5t-135 -81t-91 -117.5t-52.5 -139t-13.5 -147zM426 1589v13l303 282h137l303 -282v-13h-254l-118 121l-117 -121 h-254z" />
<glyph unicode="&#xd5;" horiz-adv-x="1607" d="M37 707q0 115 28.5 223.5t88.5 205t145.5 169t208 115t266.5 42.5t266 -42.5t208 -115t146 -168.5t88 -204.5t27 -222.5t-29 -220t-87 -201.5t-143 -166.5t-206.5 -112.5t-269.5 -41.5t-269.5 41t-208 111t-145 164.5t-87 201.5t-27.5 222zM303 702q2 -74 18.5 -141.5 t53 -131.5t89 -111t132.5 -75t178 -28t178 28.5t132.5 76t88.5 111.5t52.5 132t17.5 142q1 75 -14.5 146.5t-52 138t-90 117t-134 80.5t-178.5 30t-179 -30.5t-135 -81t-91 -117.5t-52.5 -139t-13.5 -147zM451 1671q88 216 264 176q23 -6 56 -21.5t57.5 -26t51 -15.5t49 10 t40.5 51l164 -78q-42 -113 -112 -156.5t-147 -23.5q-24 7 -57.5 25.5t-57.5 30.5t-52 17t-53 -12.5t-48 -60.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1607" d="M37 707q0 -115 27.5 -222t87 -201.5t145 -164.5t208 -111t269.5 -41t269.5 41.5t206.5 112.5t143 166.5t87 201.5t29 220t-27 222.5t-88 204.5t-146 168.5t-208 115t-266 42.5t-266.5 -42.5t-208 -115t-145.5 -169t-88.5 -205t-28.5 -223.5zM303 702q-2 75 13.5 147 t52.5 139t91 117.5t135 81t179 30.5t178.5 -30t134 -80.5t90 -117t52 -138t14.5 -146.5q-1 -74 -17.5 -142t-52.5 -132t-88.5 -111.5t-132.5 -76t-178 -28.5t-178 28t-132.5 75t-89 111t-53 131.5t-18.5 141.5zM393 1716q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5 t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5zM897 1716q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="911" d="M86 453l201 196l-197 197l166 160l190 -199l199 203l156 -156l-205 -211l211 -209l-156 -149l-207 209l-196 -203z" />
<glyph unicode="&#xd8;" horiz-adv-x="1607" d="M37 707q0 115 28.5 223.5t88.5 205t145.5 169t208 115t266.5 42.5q146 0 279 -47l92 154h262l-160 -266q129 -106 196.5 -264t65.5 -330q-1 -114 -29 -220t-87 -201.5t-143 -166.5t-206.5 -112.5t-269.5 -41.5q-166 0 -299 51l-100 -165h-260l168 278 q-121 103 -183.5 254t-62.5 322zM303 702q2 -95 31 -184t86 -156l502 834q-79 21 -148 21q-98 0 -179 -30.5t-135 -81t-91 -117.5t-52.5 -139t-13.5 -147zM606 238q81 -23 168 -23q98 0 178 28.5t132.5 76t88.5 111.5t52.5 132t17.5 142q2 104 -31 203t-100 171z" />
<glyph unicode="&#xd9;" horiz-adv-x="1521" d="M100 610v822h269v-822q0 -190 99.5 -292.5t270.5 -102.5q166 0 258.5 105t92.5 290v822h268v-822q0 -156 -46 -278.5t-129.5 -201.5t-196.5 -120t-249 -41q-131 0 -245 40t-202 117.5t-139 202t-51 281.5zM444 1878v10h295l152 -278v-13h-221z" />
<glyph unicode="&#xda;" horiz-adv-x="1521" d="M100 610q0 -157 51 -281.5t139 -202t202 -117.5t245 -40q136 0 249 41t196.5 120t129.5 201.5t46 278.5v822h-268v-822q0 -185 -92.5 -290t-258.5 -105q-171 0 -270.5 102.5t-99.5 292.5v822h-269v-822zM608 1595v13l152 278h295v-10l-226 -281h-221z" />
<glyph unicode="&#xdb;" horiz-adv-x="1521" d="M100 610v822h269v-822q0 -190 99.5 -292.5t270.5 -102.5q166 0 258.5 105t92.5 290v822h268v-822q0 -156 -46 -278.5t-129.5 -201.5t-196.5 -120t-249 -41q-131 0 -245 40t-202 117.5t-139 202t-51 281.5zM365 1591v13l303 282h137l303 -282v-13h-254l-119 121l-117 -121 h-253z" />
<glyph unicode="&#xdc;" horiz-adv-x="1521" d="M100 610q0 -157 51 -281.5t139 -202t202 -117.5t245 -40q136 0 249 41t196.5 120t129.5 201.5t46 278.5v822h-268v-822q0 -185 -92.5 -290t-258.5 -105q-171 0 -270.5 102.5t-99.5 292.5v822h-269v-822zM352 1710q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5 t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM862 1710q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1472" d="M18 1421l551 -829v-592h271v592l571 829v13h-326l-380 -592l-365 592h-322v-13zM578 1579v12l151 279h295v-10l-225 -281h-221z" />
<glyph unicode="&#xde;" horiz-adv-x="1384" d="M100 0l2 1432h267v-234h407q127 0 229.5 -41t165.5 -109t97 -154t34.5 -176t-34 -176t-97.5 -154t-165.5 -109t-229.5 -41h-405v-238h-271zM371 479h405q83 0 143.5 35t87.5 87t27 113.5t-27.5 113.5t-88 87t-142.5 35h-405v-471z" />
<glyph unicode="&#xdf;" horiz-adv-x="1290" d="M127 0v1053q0 97 43 175.5t113.5 127t156 74.5t174.5 26q96 0 182 -25.5t154.5 -73.5t109 -126t40.5 -176q0 -88 -58 -158.5t-159 -93.5q86 -20 150.5 -61.5t99.5 -94t52 -105t17 -104.5q0 -93 -25.5 -170t-71.5 -131.5t-109.5 -91.5t-140 -52.5t-163 -13t-178.5 24.5 l-2 227q111 -26 191.5 -21t132 38t76 79t24.5 103q0 78 -49 137t-121.5 87t-153.5 28h-96v238h90q55 0 103 11t86.5 43t40.5 81q3 59 -31.5 99t-82.5 55.5t-104 15.5q-98 0 -160 -45t-65 -125v-482q0 -82 1 -289t1 -284h-268z" />
<glyph unicode="&#xe0;" horiz-adv-x="1251" d="M27 506q0 169 70.5 290.5t188 180t267.5 57.5q102 0 190.5 -44.5t130.5 -119.5l9 140h239v-1010h-235l-13 147q-40 -83 -136 -129.5t-191 -48.5q-151 -1 -267.5 58.5t-184.5 182.5t-68 296zM276 506q0 -141 85 -224t214 -83q71 0 128.5 26.5t93 69.5t54.5 98t19 112 t-19 112t-54.5 98t-93 69.5t-128.5 26.5q-129 0 -214 -82t-85 -223zM287 1442v10h295l151 -278v-13h-221z" />
<glyph unicode="&#xe1;" horiz-adv-x="1251" d="M27 506q0 -173 68 -296t184.5 -182.5t267.5 -58.5q95 2 191 48.5t136 129.5l13 -147h235v1010h-239l-9 -140q-42 75 -130.5 119.5t-190.5 44.5q-150 1 -267.5 -57.5t-188 -180t-70.5 -290.5zM276 506q0 141 85 223t214 82q71 0 128.5 -26.5t93 -69.5t54.5 -98t19 -112 t-19 -112t-54.5 -98t-93 -69.5t-128.5 -26.5q-129 0 -214 83t-85 224zM414 1163v13l151 278h295v-10l-225 -281h-221z" />
<glyph unicode="&#xe2;" horiz-adv-x="1251" d="M27 506q0 169 70.5 290.5t188 180t267.5 57.5q102 0 190.5 -44.5t130.5 -119.5l9 140h239v-1010h-235l-13 147q-40 -83 -136 -129.5t-191 -48.5q-151 -1 -267.5 58.5t-184.5 182.5t-68 296zM244 1165v13l303 282h137l303 -282v-13h-254l-119 121l-116 -121h-254zM276 506 q0 -141 85 -224t214 -83q71 0 128.5 26.5t93 69.5t54.5 98t19 112t-19 112t-54.5 98t-93 69.5t-128.5 26.5q-129 0 -214 -82t-85 -223z" />
<glyph unicode="&#xe3;" horiz-adv-x="1251" d="M27 506q0 169 70.5 290.5t188 180t267.5 57.5q102 0 190.5 -44.5t130.5 -119.5l9 140h239v-1010h-235l-13 147q-40 -83 -136 -129.5t-191 -48.5q-151 -1 -267.5 58.5t-184.5 182.5t-68 296zM274 1282q88 217 265 176q23 -6 56 -21.5t57.5 -26t51 -15.5t49 10t40.5 51 l163 -78q-42 -113 -111.5 -156.5t-146.5 -23.5q-24 7 -57.5 25.5t-57.5 30.5t-52 17t-53 -12.5t-48 -60.5zM276 506q0 -141 85 -224t214 -83q71 0 128.5 26.5t93 69.5t54.5 98t19 112t-19 112t-54.5 98t-93 69.5t-128.5 26.5q-129 0 -214 -82t-85 -223z" />
<glyph unicode="&#xe4;" horiz-adv-x="1251" d="M27 506q0 -173 68 -296t184.5 -182.5t267.5 -58.5q95 2 191 48.5t136 129.5l13 -147h235v1010h-239l-9 -140q-42 75 -130.5 119.5t-190.5 44.5q-150 1 -267.5 -57.5t-188 -180t-70.5 -290.5zM225 1341q0 45 21.5 78.5t53 48.5t69 15t69 -15t53 -48.5t21.5 -78.5 q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5zM276 506q0 141 85 223t214 82q71 0 128.5 -26.5t93 -69.5t54.5 -98t19 -112t-19 -112t-54.5 -98t-93 -69.5t-128.5 -26.5q-129 0 -214 83t-85 224zM735 1341q0 45 21.5 78.5t53 48.5t69 15t69 -15t53 -48.5 t21.5 -78.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1251" d="M27 506q0 169 70.5 290.5t188 180t267.5 57.5q102 0 190.5 -44.5t130.5 -119.5l9 140h239v-1010h-235l-13 147q-40 -83 -136 -129.5t-191 -48.5q-151 -1 -267.5 58.5t-184.5 182.5t-68 296zM276 506q0 -141 85 -224t214 -83q71 0 128.5 26.5t93 69.5t54.5 98t19 112 t-19 112t-54.5 98t-93 69.5t-128.5 26.5q-129 0 -214 -82t-85 -223zM330 1382q0 112 78.5 175t183.5 63t182.5 -63t77.5 -175t-77.5 -174.5t-182.5 -62.5t-183.5 62.5t-78.5 174.5zM473.5 1384.5q0.5 -37.5 31 -69t83.5 -31.5q56 0 86.5 31.5t30 69t-32 69t-84.5 31.5 q-54 0 -84.5 -31.5t-30 -69z" />
<glyph unicode="&#xe6;" horiz-adv-x="2000" d="M14 506q0 169 71 290.5t188.5 180t267.5 57.5q102 0 190.5 -44.5t130.5 -119.5l8 140h189v-127q121 159 350 155q83 -2 162.5 -30.5t146 -81.5t112 -129t60.5 -171.5t-8 -209.5h-788q12 -96 92 -155.5t209 -59.5q73 0 151 26t119 68l160 -158q-76 -80 -194 -122t-240 -42 q-228 0 -332 158l-10 -131h-185l-2 147q-40 -83 -136 -129.5t-191 -48.5q-152 -1 -268 58.5t-184.5 183t-68.5 295.5zM264 506q0 -141 85 -224t214 -83q74 0 134 26.5t97 69.5t57 98t20 112t-20 112t-57 98t-97 69.5t-134 26.5q-129 0 -214 -82t-85 -223zM1098 623h544 q-12 96 -82 146t-180 50q-104 0 -179.5 -50t-102.5 -146z" />
<glyph unicode="&#xe7;" horiz-adv-x="976" d="M23 504q0 131 42.5 234t116 168t169.5 98.5t208 33.5q110 0 196.5 -34.5t168.5 -114.5l-158 -166q-89 80 -203 80q-127 0 -209 -84t-82 -215q0 -139 82 -217t205 -78q137 0 221 84l168 -164q-83 -82 -171.5 -119.5t-196.5 -40.5l-25 -78q67 -2 114.5 -24t68 -55t29.5 -73 t-1 -77q-12 -55 -49 -96.5t-86.5 -63t-108.5 -26.5t-114 8t-105 45t-80 80l109 90q29 -38 82.5 -54.5t101 -4t59.5 50.5q11 37 -17.5 63.5t-71.5 28t-85 -22.5l-59 45l64 183q-174 40 -278.5 171.5t-104.5 344.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1193" d="M37 512q0 233 146.5 380.5t389.5 147.5q261 0 399.5 -160t110.5 -464h-788q12 -96 92 -155.5t209 -59.5q73 0 151 26t119 68l160 -158q-76 -80 -194 -122t-240 -42q-258 0 -406.5 146.5t-148.5 392.5zM272 1450v10h295l152 -278v-13h-221zM299 623h545q-12 96 -82 146 t-180 50q-104 0 -180 -50t-103 -146z" />
<glyph unicode="&#xe9;" horiz-adv-x="1193" d="M37 512q0 -246 148.5 -392.5t406.5 -146.5q122 0 240 42t194 122l-160 158q-41 -42 -119 -68t-151 -26q-129 0 -209 59.5t-92 155.5h788q28 304 -110.5 464t-399.5 160q-243 0 -389.5 -147.5t-146.5 -380.5zM299 623q27 96 103 146t180 50q110 0 180 -50t82 -146h-545z M422 1174v12l151 278h295v-10l-225 -280h-221z" />
<glyph unicode="&#xea;" horiz-adv-x="1193" d="M37 512q0 233 146.5 380.5t389.5 147.5q261 0 399.5 -160t110.5 -464h-788q12 -96 92 -155.5t209 -59.5q73 0 151 26t119 68l160 -158q-76 -80 -194 -122t-240 -42q-258 0 -406.5 146.5t-148.5 392.5zM215 1165v13l303 282h137l303 -282v-13h-253l-119 121l-117 -121 h-254zM299 623h545q-12 96 -82 146t-180 50q-104 0 -180 -50t-103 -146z" />
<glyph unicode="&#xeb;" horiz-adv-x="1193" d="M37 512q0 -246 148.5 -392.5t406.5 -146.5q122 0 240 42t194 122l-160 158q-41 -42 -119 -68t-151 -26q-129 0 -209 59.5t-92 155.5h788q28 304 -110.5 464t-399.5 160q-243 0 -389.5 -147.5t-146.5 -380.5zM229 1313q0 45 21.5 78.5t53 48.5t69 15t69 -15t53 -48.5 t21.5 -78.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5zM299 623q27 96 103 146t180 50q110 0 180 -50t82 -146h-545zM666 1313q0 45 21 78.5t53 48.5t69 15t69 -15t53 -48.5t21 -78.5q0 -67 -44.5 -104.5t-98.5 -37.5t-98.5 37.5t-44.5 104.5z" />
<glyph unicode="&#xec;" horiz-adv-x="487" d="M-8 1419v11h268l86 -291v-13h-203zM82 0v975h262v-975h-262z" />
<glyph unicode="&#xed;" horiz-adv-x="487" d="M82 0h262v975h-262v-975zM88 1126v13l86 291h268v-11l-151 -293h-203z" />
<glyph unicode="&#xee;" horiz-adv-x="487" d="M-100 1130v13l266 299h119l266 -299v-13h-223l-103 127l-102 -127h-223zM82 0v975h262v-975h-262z" />
<glyph unicode="&#xef;" horiz-adv-x="487" d="M-102 1309q0 45 21 78.5t53 48.5t69 15t69 -15t53 -48.5t21 -78.5q0 -67 -44.5 -104.5t-98.5 -37.5t-98.5 37.5t-44.5 104.5zM82 0h262v975h-262v-975zM276 1309q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0 t-72 45t-29.5 90.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1204" d="M66 487q0 85 26 164.5t75 146.5t127.5 115t176.5 66q86 12 176.5 -5t138.5 -59q-25 66 -95.5 127.5t-108.5 71.5l-64 -72l-121 80l54 64q-54 32 -168 47l45 203q150 -20 270 -74l88 104l123 -88l-76 -88q206 -162 290.5 -348t84.5 -428q0 -238 -139.5 -385t-380.5 -147 q-126 0 -227.5 42.5t-164.5 114t-96.5 161t-33.5 187.5zM322 489q0 -116 70.5 -200t195.5 -84t194.5 84t69.5 200q0 73 -28.5 136t-90 105t-145.5 42q-125 0 -195.5 -84.5t-70.5 -198.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1230" d="M76 0v1012h223l16 -138q155 152 332 152q185 0 306.5 -132.5t121.5 -363.5v-530h-250v528q0 120 -63.5 195.5t-179.5 75.5q-113 0 -185.5 -82.5t-72.5 -200.5v-516h-248zM227 1288q88 217 265 176q23 -6 56 -21.5t57 -26t50.5 -15.5t49 10t40.5 51l164 -78 q-42 -113 -111.5 -156.5t-146.5 -23.5q-24 7 -57.5 25.5t-57.5 30.5t-52 17t-53 -12.5t-48 -60.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1175" d="M35 504q0 145 62.5 264t182.5 190.5t277 71.5t278.5 -71.5t185.5 -190.5t64 -264q0 -146 -62 -265t-183 -190.5t-281 -71.5q-159 0 -280 71.5t-182.5 190t-61.5 265.5zM264 1436v10h295l152 -279v-12h-222zM285 504q0 -79 29.5 -145t93.5 -109t151 -43q130 0 202.5 87 t72.5 210q0 120 -77 209.5t-198 89.5q-128 0 -201 -88.5t-73 -210.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1175" d="M35 504q0 -147 61.5 -265.5t182.5 -190t280 -71.5q160 0 281 71.5t183 190.5t62 265q0 145 -64 264t-185.5 190.5t-278.5 71.5t-277 -71.5t-182.5 -190.5t-62.5 -264zM285 504q0 122 73 210.5t201 88.5q121 0 198 -89.5t77 -209.5q0 -123 -72.5 -210t-202.5 -87 q-87 0 -151 43t-93.5 109t-29.5 145zM434 1151v12l152 279h295v-10l-226 -281h-221z" />
<glyph unicode="&#xf4;" horiz-adv-x="1175" d="M35 504q0 145 62.5 264t182.5 190.5t277 71.5t278.5 -71.5t185.5 -190.5t64 -264q0 -146 -62 -265t-183 -190.5t-281 -71.5q-159 0 -280 71.5t-182.5 190t-61.5 265.5zM186 1145v12l303 283h138l303 -283v-12h-254l-119 121l-117 -121h-254zM285 504q0 -79 29.5 -145 t93.5 -109t151 -43q130 0 202.5 87t72.5 210q0 120 -77 209.5t-198 89.5q-128 0 -201 -88.5t-73 -210.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1175" d="M35 504q0 145 62.5 264t182.5 190.5t277 71.5t278.5 -71.5t185.5 -190.5t64 -264q0 -146 -62 -265t-183 -190.5t-281 -71.5q-159 0 -280 71.5t-182.5 190t-61.5 265.5zM223 1274q88 216 264 176q23 -6 56 -21.5t57.5 -26t51 -15.5t49 10t40.5 51l164 -78 q-42 -113 -111.5 -156.5t-146.5 -23.5q-24 7 -57.5 25.5t-57.5 30.5t-52 17t-53 -12.5t-48 -60.5zM285 504q0 -79 29.5 -145t93.5 -109t151 -43q130 0 202.5 87t72.5 210q0 120 -77 209.5t-198 89.5q-128 0 -201 -88.5t-73 -210.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1175" d="M35 504q0 -147 61.5 -265.5t182.5 -190t280 -71.5q160 0 281 71.5t183 190.5t62 265q0 145 -64 264t-185.5 190.5t-278.5 71.5t-277 -71.5t-182.5 -190.5t-62.5 -264zM205 1327q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5 t-72 -45t-84.5 0t-71.5 45t-29.5 90.5zM285 504q0 122 73 210.5t201 88.5q121 0 198 -89.5t77 -209.5q0 -123 -72.5 -210t-202.5 -87q-87 0 -151 43t-93.5 109t-29.5 145zM627 1327q0 45 21 78.5t53 48.5t69 15t69 -15t53 -48.5t21 -78.5q0 -67 -44.5 -104.5t-98.5 -37.5 t-98.5 37.5t-44.5 104.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1060" d="M39 545h915v241h-915v-241zM354 281q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM354 1042q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5 t-71.5 -45t-84.5 0t-72 45t-29.5 90.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1175" d="M35 504q0 145 62.5 264t182.5 190.5t277 71.5q91 0 180 -29l76 129h236l-136 -229q83 -72 127.5 -175t44.5 -222q0 -146 -62 -265t-183 -190.5t-281 -71.5q-81 0 -176 25l-80 -135h-235l137 233q-83 71 -126.5 176t-43.5 228zM285 504q0 -108 51 -182l280 475 q-27 6 -57 6q-128 0 -201 -88.5t-73 -210.5zM506 211q16 -4 53 -4q130 0 202.5 87t72.5 210q0 94 -52 174z" />
<glyph unicode="&#xf9;" horiz-adv-x="1232" d="M88 479v531h250v-529q0 -120 64 -195t180 -75q113 0 185.5 82.5t72.5 200.5v516h247v-1012h-223l-16 137q-80 -79 -159.5 -115t-182.5 -36q-186 0 -302 132.5t-116 362.5zM287 1423v11h295l151 -279v-12h-221z" />
<glyph unicode="&#xfa;" horiz-adv-x="1232" d="M88 479q0 -230 116 -362.5t302 -132.5q103 0 182.5 36t159.5 115l16 -137h223v1012h-247v-516q0 -118 -72.5 -200.5t-185.5 -82.5q-116 0 -180 75t-64 195v529h-250v-531zM463 1143v12l151 279h295v-11l-225 -280h-221z" />
<glyph unicode="&#xfb;" horiz-adv-x="1232" d="M88 479v531h250v-529q0 -120 64 -195t180 -75q113 0 185.5 82.5t72.5 200.5v516h247v-1012h-223l-16 137q-80 -79 -159.5 -115t-182.5 -36q-186 0 -302 132.5t-116 362.5zM236 1147v12l303 283h137l303 -283v-12h-254l-119 121l-117 -121h-253z" />
<glyph unicode="&#xfc;" horiz-adv-x="1232" d="M88 479q0 -230 116 -362.5t302 -132.5q103 0 182.5 36t159.5 115l16 -137h223v1012h-247v-516q0 -118 -72.5 -200.5t-185.5 -82.5q-116 0 -180 75t-64 195v529h-250v-531zM199 1321q0 45 21 78.5t53 48.5t69 15t69 -15t53 -48.5t21 -78.5q0 -67 -44.5 -104.5t-98.5 -37.5 t-98.5 37.5t-44.5 104.5zM709 1321q0 45 21 78.5t53 48.5t69 15t69 -15t53 -48.5t21 -78.5q0 -67 -44.5 -104.5t-98.5 -37.5t-98.5 37.5t-44.5 104.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1148" d="M-6 1010l403 -994l-188 -438h274l617 1432h-277l-184 -443l-104 -278l-97 284l-153 437h-291zM385 1145v12l152 279h294v-11l-225 -280h-221z" />
<glyph unicode="&#xfe;" horiz-adv-x="1277" d="M82 -422v1856h250v-562q48 75 142.5 117.5t187.5 42.5q150 0 265 -58.5t183 -179.5t68 -290q0 -253 -141.5 -391t-370.5 -138q-92 0 -185.5 39.5t-148.5 122.5v-559h-250zM354 504q0 -118 84 -202.5t203 -84.5q123 0 205 77.5t82 209.5t-82 209.5t-205 77.5 q-119 0 -203 -84.5t-84 -202.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1148" d="M-6 1010l403 -994l-188 -438h274l617 1432h-277l-184 -443l-104 -278l-97 284l-153 437h-291zM172 1305q0 39 16 69.5t41 46.5t56 23.5t61.5 0t55.5 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM682 1305q0 45 21.5 78.5t53 48.5 t69 15t69 -15t53 -48.5t21.5 -78.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2117" d="M92 725q0 161 46.5 294.5t136 232.5t229.5 154.5t319 55.5q121 0 236 -32v4h936v-269h-686v-319h657v-256h-657v-320h686v-268h-936q-119 -33 -236 -33q-181 0 -322.5 58t-229.5 161.5t-133.5 239.5t-45.5 297zM362 719q0 -100 29.5 -191.5t86 -165.5t148 -118.5 t205.5 -44.5q139 0 228 41v950q-96 45 -234 45q-114 0 -203.5 -40t-145.5 -110.5t-85 -163.5t-29 -202z" />
<glyph unicode="&#x153;" horiz-adv-x="1943" d="M39 504q0 145 62.5 264.5t182.5 191.5t277 72q126 0 231.5 -53.5t163.5 -149.5q47 92 151 149.5t234 57.5q262 0 400 -158t110 -462h-782q12 -97 89.5 -156t205.5 -59q73 0 151 26t119 68l160 -158q-76 -80 -194 -122t-240 -42q-142 0 -249 53t-167 154 q-139 -203 -381 -203q-159 0 -280 71.5t-182.5 190t-61.5 265.5zM289 504q0 -79 29.5 -145t93.5 -109t151 -43q130 0 202.5 87t72.5 210q0 120 -77 209.5t-198 89.5q-128 0 -201 -88.5t-73 -210.5zM1067 623h545q-12 96 -82 146t-180 50q-104 0 -180 -50t-103 -146z" />
<glyph unicode="&#x178;" horiz-adv-x="1429" d="M18 1421l551 -829v-592h271v592l571 829v13h-326l-380 -592l-365 592h-322v-13zM307 1710q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-71.5 -45t-84.5 0t-72 45t-29.5 90.5zM817 1710q0 39 16 69.5t41 46.5t55.5 23.5t61.5 0 t56 -23.5t41 -46.5t16 -69.5q0 -54 -29.5 -90.5t-72 -45t-84.5 0t-71.5 45t-29.5 90.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="870" d="M45 1169v13l303 282h137l303 -282v-13h-253l-119 121l-117 -121h-254z" />
<glyph unicode="&#x2dc;" horiz-adv-x="813" d="M57 1401q88 217 265 176q23 -6 56 -21.5t57 -26t50.5 -15.5t49 10t40.5 51l164 -78q-42 -113 -111.5 -156.5t-146.5 -23.5q-24 7 -57.5 25.5t-57.5 30.5t-52 17t-53 -12.5t-48 -60.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="944" />
<glyph unicode="&#x2001;" horiz-adv-x="1888" />
<glyph unicode="&#x2002;" horiz-adv-x="944" />
<glyph unicode="&#x2003;" horiz-adv-x="1888" />
<glyph unicode="&#x2004;" horiz-adv-x="629" />
<glyph unicode="&#x2005;" horiz-adv-x="472" />
<glyph unicode="&#x2006;" horiz-adv-x="314" />
<glyph unicode="&#x2007;" horiz-adv-x="314" />
<glyph unicode="&#x2008;" horiz-adv-x="236" />
<glyph unicode="&#x2009;" horiz-adv-x="377" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="937" d="M72 553v229h755v-229h-755z" />
<glyph unicode="&#x2011;" horiz-adv-x="937" d="M72 553v229h755v-229h-755z" />
<glyph unicode="&#x2012;" horiz-adv-x="937" d="M72 553v229h755v-229h-755z" />
<glyph unicode="&#x2013;" horiz-adv-x="1116" d="M43 494v221h969v-221h-969z" />
<glyph unicode="&#x2014;" horiz-adv-x="1275" d="M29 494v221h1165v-221h-1165z" />
<glyph unicode="&#x2018;" horiz-adv-x="436" d="M29 975l159 461h197l-98 -461h-258z" />
<glyph unicode="&#x2019;" horiz-adv-x="413" d="M-4 973l98 461h258l-159 -461h-197z" />
<glyph unicode="&#x201a;" horiz-adv-x="512" d="M51 113q0 54 30 89.5t72.5 43t85.5 -2.5t73 -47t30 -90q0 -19 -10.5 -54t-81.5 -253h-141l38 203q-44 8 -70 36.5t-26 74.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="774" d="M12 971l160 461h197l-99 -461h-258zM358 971l160 461h197l-99 -461h-258z" />
<glyph unicode="&#x201d;" horiz-adv-x="741" d="M-4 973l98 461h258l-159 -461h-197zM342 973l98 461h258l-159 -461h-197z" />
<glyph unicode="&#x201e;" horiz-adv-x="825" d="M51 113q0 54 30 89.5t72.5 43t85.5 -2.5t73 -47t30 -90q0 -19 -10.5 -54t-81.5 -253h-141l38 203q-44 8 -70 36.5t-26 74.5zM438 113q0 54 30 89.5t73 43t85.5 -2.5t72.5 -47t30 -90q0 -19 -10.5 -54t-81.5 -253h-141l39 203q-44 8 -70.5 36.5t-26.5 74.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="729" d="M72 664q0 116 75.5 188t190.5 72q112 0 187 -72.5t75 -187.5t-75 -186.5t-187 -71.5q-116 0 -191 71t-75 187z" />
<glyph unicode="&#x2026;" horiz-adv-x="1486" d="M76 129q0 40 16.5 71.5t42.5 48t57.5 24t62.5 0t57 -24t42.5 -48t16.5 -71.5t-16.5 -71.5t-42.5 -48t-57 -24t-62.5 0t-57.5 24t-42.5 48t-16.5 71.5zM543 129q0 47 22 81t54.5 49.5t71 15.5t71 -15.5t54.5 -49.5t22 -81t-22 -81t-54.5 -49.5t-71 -15.5t-71 15.5 t-54.5 49.5t-22 81zM1010 129q0 47 22 81t54.5 49.5t71 15.5t71 -15.5t54.5 -49.5t22 -81t-22 -81t-54.5 -49.5t-71 -15.5t-71 15.5t-54.5 49.5t-22 81z" />
<glyph unicode="&#x202f;" horiz-adv-x="377" />
<glyph unicode="&#x2039;" horiz-adv-x="663" d="M25 651l284 453h266l-282 -453l282 -454h-266z" />
<glyph unicode="&#x203a;" horiz-adv-x="716" d="M45 197l283 454l-283 453h266l285 -453l-285 -454h-266z" />
<glyph unicode="&#x205f;" horiz-adv-x="472" />
<glyph unicode="&#x20ac;" horiz-adv-x="1157" d="M25 461v198h110v48q0 41 2 61h-112v201h145q66 225 242 359t462 134q122 0 222 -32l-86 -242q-60 16 -136 16q-154 0 -262 -64t-168 -171h467l-45 -201h-483q-2 -20 -2 -61q0 -34 2 -48h451l-41 -198h-355q56 -110 164.5 -175t271.5 -65q85 0 173 31l75 -250 q-116 -35 -248 -35q-154 0 -277 36.5t-208.5 104t-140.5 155.5t-84 198h-139z" />
<glyph unicode="&#x2122;" horiz-adv-x="1511" d="M27 1272v164h585v-164h-196v-434h-195v434h-194zM678 838v600h205l161 -199l162 199h205v-600h-199v178l15 174l-164 -219h-41l-160 219l15 -174v-178h-199z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1013" d="M0 0v1014h1014v-1014h-1014z" />
<glyph unicode="&#xfb01;" horiz-adv-x="1163" d="M-8 797v213h188v53q0 138 76 236.5t192 137.5t260 23t273 -92l-92 -186q-108 66 -215.5 73t-175.5 -45t-68 -147v-53h531v-1010h-250v797h-281v-797h-250v797h-188z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1167" d="M-12 776v219h176v68q0 98 31.5 175t85.5 124t122 71.5t146 24.5q105 0 189 -22q112 -29 225 -95v-1341h-248v1223q-68 37 -150 37q-66 0 -108.5 -53.5t-42.5 -143.5v-68h215v-219h-215v-776h-250v776h-176z" />
<hkern u1="&#x22;" u2="&#x178;" k="-31" />
<hkern u1="&#x22;" u2="&#xeb;" k="-31" />
<hkern u1="&#x22;" u2="&#xea;" k="-31" />
<hkern u1="&#x22;" u2="&#xe9;" k="-31" />
<hkern u1="&#x22;" u2="&#xe8;" k="-31" />
<hkern u1="&#x22;" u2="&#xe6;" k="96" />
<hkern u1="&#x22;" u2="&#xe5;" k="96" />
<hkern u1="&#x22;" u2="&#xe4;" k="96" />
<hkern u1="&#x22;" u2="&#xe3;" k="96" />
<hkern u1="&#x22;" u2="&#xe2;" k="96" />
<hkern u1="&#x22;" u2="&#xe1;" k="96" />
<hkern u1="&#x22;" u2="&#xe0;" k="96" />
<hkern u1="&#x22;" u2="&#xdd;" k="-31" />
<hkern u1="&#x22;" u2="&#xc7;" k="-33" />
<hkern u1="&#x22;" u2="&#xc6;" k="96" />
<hkern u1="&#x22;" u2="&#xc5;" k="96" />
<hkern u1="&#x22;" u2="&#xc4;" k="96" />
<hkern u1="&#x22;" u2="&#xc3;" k="96" />
<hkern u1="&#x22;" u2="&#xc2;" k="96" />
<hkern u1="&#x22;" u2="&#xc1;" k="96" />
<hkern u1="&#x22;" u2="&#xc0;" k="96" />
<hkern u1="&#x22;" u2="t" k="-33" />
<hkern u1="&#x22;" u2="r" k="-31" />
<hkern u1="&#x22;" u2="p" k="-33" />
<hkern u1="&#x22;" u2="l" k="-31" />
<hkern u1="&#x22;" u2="h" k="-33" />
<hkern u1="&#x22;" u2="f" k="-33" />
<hkern u1="&#x22;" u2="e" k="-31" />
<hkern u1="&#x22;" u2="b" k="-31" />
<hkern u1="&#x22;" u2="a" k="96" />
<hkern u1="&#x22;" u2="Z" k="14" />
<hkern u1="&#x22;" u2="Y" k="-31" />
<hkern u1="&#x22;" u2="T" k="-31" />
<hkern u1="&#x22;" u2="M" k="-33" />
<hkern u1="&#x22;" u2="J" k="63" />
<hkern u1="&#x22;" u2="C" k="-33" />
<hkern u1="&#x22;" u2="B" k="-31" />
<hkern u1="&#x22;" u2="A" k="96" />
<hkern u1="&#x23;" u2="&#x39;" k="-66" />
<hkern u1="&#x23;" u2="&#x35;" k="-49" />
<hkern u1="&#x23;" u2="&#x32;" k="-31" />
<hkern u1="&#x23;" u2="&#x31;" k="-31" />
<hkern u1="&#x23;" u2="&#x30;" k="-31" />
<hkern u1="&#x24;" u2="&#x38;" k="-31" />
<hkern u1="&#x24;" u2="&#x36;" k="-33" />
<hkern u1="&#x25;" u2="&#x38;" k="-33" />
<hkern u1="&#x25;" u2="&#x36;" k="-49" />
<hkern u1="&#x25;" u2="&#x35;" k="-31" />
<hkern u1="&#x25;" u2="&#x32;" k="-33" />
<hkern u1="&#x25;" u2="&#x31;" k="66" />
<hkern u1="&#x26;" u2="&#x178;" k="303" />
<hkern u1="&#x26;" u2="&#x153;" k="96" />
<hkern u1="&#x26;" u2="&#x152;" k="49" />
<hkern u1="&#x26;" u2="&#xff;" k="270" />
<hkern u1="&#x26;" u2="&#xfd;" k="270" />
<hkern u1="&#x26;" u2="&#xfc;" k="31" />
<hkern u1="&#x26;" u2="&#xfb;" k="31" />
<hkern u1="&#x26;" u2="&#xfa;" k="31" />
<hkern u1="&#x26;" u2="&#xf9;" k="31" />
<hkern u1="&#x26;" u2="&#xf8;" k="96" />
<hkern u1="&#x26;" u2="&#xf6;" k="96" />
<hkern u1="&#x26;" u2="&#xf5;" k="96" />
<hkern u1="&#x26;" u2="&#xf4;" k="96" />
<hkern u1="&#x26;" u2="&#xf3;" k="96" />
<hkern u1="&#x26;" u2="&#xf2;" k="96" />
<hkern u1="&#x26;" u2="&#xe7;" k="66" />
<hkern u1="&#x26;" u2="&#xe6;" k="-47" />
<hkern u1="&#x26;" u2="&#xe5;" k="-47" />
<hkern u1="&#x26;" u2="&#xe4;" k="-47" />
<hkern u1="&#x26;" u2="&#xe3;" k="-47" />
<hkern u1="&#x26;" u2="&#xe2;" k="-47" />
<hkern u1="&#x26;" u2="&#xe1;" k="-47" />
<hkern u1="&#x26;" u2="&#xe0;" k="-47" />
<hkern u1="&#x26;" u2="&#xdd;" k="303" />
<hkern u1="&#x26;" u2="&#xdc;" k="209" />
<hkern u1="&#x26;" u2="&#xdb;" k="209" />
<hkern u1="&#x26;" u2="&#xda;" k="209" />
<hkern u1="&#x26;" u2="&#xd9;" k="209" />
<hkern u1="&#x26;" u2="&#xd8;" k="49" />
<hkern u1="&#x26;" u2="&#xd6;" k="49" />
<hkern u1="&#x26;" u2="&#xd5;" k="49" />
<hkern u1="&#x26;" u2="&#xd4;" k="49" />
<hkern u1="&#x26;" u2="&#xd3;" k="49" />
<hkern u1="&#x26;" u2="&#xd2;" k="49" />
<hkern u1="&#x26;" u2="&#xcb;" k="-80" />
<hkern u1="&#x26;" u2="&#xca;" k="-80" />
<hkern u1="&#x26;" u2="&#xc9;" k="-80" />
<hkern u1="&#x26;" u2="&#xc8;" k="-80" />
<hkern u1="&#x26;" u2="&#xc7;" k="66" />
<hkern u1="&#x26;" u2="z" k="16" />
<hkern u1="&#x26;" u2="y" k="270" />
<hkern u1="&#x26;" u2="x" k="-16" />
<hkern u1="&#x26;" u2="w" k="193" />
<hkern u1="&#x26;" u2="v" k="176" />
<hkern u1="&#x26;" u2="u" k="31" />
<hkern u1="&#x26;" u2="t" k="242" />
<hkern u1="&#x26;" u2="s" k="31" />
<hkern u1="&#x26;" u2="q" k="80" />
<hkern u1="&#x26;" u2="o" k="96" />
<hkern u1="&#x26;" u2="j" k="-47" />
<hkern u1="&#x26;" u2="g" k="63" />
<hkern u1="&#x26;" u2="d" k="-16" />
<hkern u1="&#x26;" u2="c" k="66" />
<hkern u1="&#x26;" u2="b" k="-16" />
<hkern u1="&#x26;" u2="a" k="-47" />
<hkern u1="&#x26;" u2="Y" k="303" />
<hkern u1="&#x26;" u2="W" k="207" />
<hkern u1="&#x26;" u2="V" k="-33" />
<hkern u1="&#x26;" u2="U" k="209" />
<hkern u1="&#x26;" u2="T" k="256" />
<hkern u1="&#x26;" u2="S" k="145" />
<hkern u1="&#x26;" u2="R" k="176" />
<hkern u1="&#x26;" u2="Q" k="47" />
<hkern u1="&#x26;" u2="P" k="209" />
<hkern u1="&#x26;" u2="O" k="49" />
<hkern u1="&#x26;" u2="F" k="-47" />
<hkern u1="&#x26;" u2="E" k="-80" />
<hkern u1="&#x26;" u2="C" k="66" />
<hkern u1="&#x26;" u2="B" k="-16" />
<hkern u1="&#x26;" u2="&#x39;" k="113" />
<hkern u1="&#x26;" u2="&#x38;" k="31" />
<hkern u1="&#x26;" u2="&#x35;" k="16" />
<hkern u1="&#x26;" u2="&#x34;" k="47" />
<hkern u1="&#x26;" u2="&#x32;" k="-33" />
<hkern u1="&#x26;" u2="&#x31;" k="190" />
<hkern u1="&#x26;" u2="&#x30;" k="47" />
<hkern u1="&#x27;" u2="t" k="-43" />
<hkern u1="&#x2c;" u2="&#xef;" k="-33" />
<hkern u1="&#x2c;" u2="&#xee;" k="-33" />
<hkern u1="&#x2c;" u2="&#xed;" k="-33" />
<hkern u1="&#x2c;" u2="&#xec;" k="-33" />
<hkern u1="&#x2c;" u2="&#xeb;" k="-47" />
<hkern u1="&#x2c;" u2="&#xea;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe9;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe8;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe7;" k="33" />
<hkern u1="&#x2c;" u2="&#xe6;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe5;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe4;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe3;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe2;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe1;" k="-47" />
<hkern u1="&#x2c;" u2="&#xe0;" k="-47" />
<hkern u1="&#x2c;" u2="z" k="-49" />
<hkern u1="&#x2c;" u2="m" k="-31" />
<hkern u1="&#x2c;" u2="l" k="-33" />
<hkern u1="&#x2c;" u2="k" k="-47" />
<hkern u1="&#x2c;" u2="j" k="-80" />
<hkern u1="&#x2c;" u2="i" k="-33" />
<hkern u1="&#x2c;" u2="h" k="-16" />
<hkern u1="&#x2c;" u2="e" k="-47" />
<hkern u1="&#x2c;" u2="d" k="-47" />
<hkern u1="&#x2c;" u2="c" k="33" />
<hkern u1="&#x2c;" u2="a" k="-47" />
<hkern u1="&#x2c;" u2="Z" k="-63" />
<hkern u1="&#x2c;" u2="V" k="-31" />
<hkern u1="&#x2c;" u2="H" k="-31" />
<hkern u1="&#x2e;" u2="&#x178;" k="240" />
<hkern u1="&#x2e;" u2="&#x153;" k="63" />
<hkern u1="&#x2e;" u2="&#xff;" k="225" />
<hkern u1="&#x2e;" u2="&#xfd;" k="225" />
<hkern u1="&#x2e;" u2="&#xf8;" k="63" />
<hkern u1="&#x2e;" u2="&#xf6;" k="63" />
<hkern u1="&#x2e;" u2="&#xf5;" k="63" />
<hkern u1="&#x2e;" u2="&#xf4;" k="63" />
<hkern u1="&#x2e;" u2="&#xf3;" k="63" />
<hkern u1="&#x2e;" u2="&#xf2;" k="63" />
<hkern u1="&#x2e;" u2="&#xeb;" k="-33" />
<hkern u1="&#x2e;" u2="&#xea;" k="-33" />
<hkern u1="&#x2e;" u2="&#xe9;" k="-33" />
<hkern u1="&#x2e;" u2="&#xe8;" k="-33" />
<hkern u1="&#x2e;" u2="&#xe7;" k="31" />
<hkern u1="&#x2e;" u2="&#xe6;" k="-49" />
<hkern u1="&#x2e;" u2="&#xe5;" k="-49" />
<hkern u1="&#x2e;" u2="&#xe4;" k="-49" />
<hkern u1="&#x2e;" u2="&#xe3;" k="-49" />
<hkern u1="&#x2e;" u2="&#xe2;" k="-49" />
<hkern u1="&#x2e;" u2="&#xe1;" k="-49" />
<hkern u1="&#x2e;" u2="&#xe0;" k="-49" />
<hkern u1="&#x2e;" u2="&#xdd;" k="240" />
<hkern u1="&#x2e;" u2="&#xc7;" k="47" />
<hkern u1="&#x2e;" u2="z" k="-49" />
<hkern u1="&#x2e;" u2="y" k="225" />
<hkern u1="&#x2e;" u2="x" k="-33" />
<hkern u1="&#x2e;" u2="w" k="66" />
<hkern u1="&#x2e;" u2="v" k="176" />
<hkern u1="&#x2e;" u2="t" k="160" />
<hkern u1="&#x2e;" u2="q" k="47" />
<hkern u1="&#x2e;" u2="p" k="-63" />
<hkern u1="&#x2e;" u2="o" k="63" />
<hkern u1="&#x2e;" u2="l" k="-66" />
<hkern u1="&#x2e;" u2="k" k="-47" />
<hkern u1="&#x2e;" u2="j" k="-63" />
<hkern u1="&#x2e;" u2="h" k="-47" />
<hkern u1="&#x2e;" u2="g" k="33" />
<hkern u1="&#x2e;" u2="e" k="-33" />
<hkern u1="&#x2e;" u2="d" k="-49" />
<hkern u1="&#x2e;" u2="c" k="31" />
<hkern u1="&#x2e;" u2="b" k="-47" />
<hkern u1="&#x2e;" u2="a" k="-49" />
<hkern u1="&#x2e;" u2="Z" k="-80" />
<hkern u1="&#x2e;" u2="Y" k="240" />
<hkern u1="&#x2e;" u2="V" k="-47" />
<hkern u1="&#x2e;" u2="T" k="143" />
<hkern u1="&#x2e;" u2="C" k="47" />
<hkern u1="&#x2e;" u2="B" k="-47" />
<hkern u1="&#x2f;" u2="&#x178;" k="-96" />
<hkern u1="&#x2f;" u2="&#x153;" k="16" />
<hkern u1="&#x2f;" u2="&#xff;" k="-113" />
<hkern u1="&#x2f;" u2="&#xfd;" k="-113" />
<hkern u1="&#x2f;" u2="&#xfc;" k="-80" />
<hkern u1="&#x2f;" u2="&#xfb;" k="-80" />
<hkern u1="&#x2f;" u2="&#xfa;" k="-80" />
<hkern u1="&#x2f;" u2="&#xf9;" k="-80" />
<hkern u1="&#x2f;" u2="&#xf8;" k="16" />
<hkern u1="&#x2f;" u2="&#xf6;" k="16" />
<hkern u1="&#x2f;" u2="&#xf5;" k="16" />
<hkern u1="&#x2f;" u2="&#xf4;" k="16" />
<hkern u1="&#x2f;" u2="&#xf3;" k="16" />
<hkern u1="&#x2f;" u2="&#xf2;" k="16" />
<hkern u1="&#x2f;" u2="&#xef;" k="-66" />
<hkern u1="&#x2f;" u2="&#xee;" k="-66" />
<hkern u1="&#x2f;" u2="&#xed;" k="-66" />
<hkern u1="&#x2f;" u2="&#xec;" k="-66" />
<hkern u1="&#x2f;" u2="&#xeb;" k="-63" />
<hkern u1="&#x2f;" u2="&#xea;" k="-63" />
<hkern u1="&#x2f;" u2="&#xe9;" k="-63" />
<hkern u1="&#x2f;" u2="&#xe8;" k="-63" />
<hkern u1="&#x2f;" u2="&#xe7;" k="16" />
<hkern u1="&#x2f;" u2="&#xe6;" k="-113" />
<hkern u1="&#x2f;" u2="&#xe5;" k="-113" />
<hkern u1="&#x2f;" u2="&#xe4;" k="-113" />
<hkern u1="&#x2f;" u2="&#xe3;" k="-113" />
<hkern u1="&#x2f;" u2="&#xe2;" k="-113" />
<hkern u1="&#x2f;" u2="&#xe1;" k="-113" />
<hkern u1="&#x2f;" u2="&#xe0;" k="-113" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-96" />
<hkern u1="&#x2f;" u2="&#xdc;" k="-96" />
<hkern u1="&#x2f;" u2="&#xdb;" k="-96" />
<hkern u1="&#x2f;" u2="&#xda;" k="-96" />
<hkern u1="&#x2f;" u2="&#xd9;" k="-96" />
<hkern u1="&#x2f;" u2="&#xc6;" k="129" />
<hkern u1="&#x2f;" u2="&#xc5;" k="129" />
<hkern u1="&#x2f;" u2="&#xc4;" k="129" />
<hkern u1="&#x2f;" u2="&#xc3;" k="129" />
<hkern u1="&#x2f;" u2="&#xc2;" k="129" />
<hkern u1="&#x2f;" u2="&#xc1;" k="129" />
<hkern u1="&#x2f;" u2="&#xc0;" k="129" />
<hkern u1="&#x2f;" u2="z" k="-80" />
<hkern u1="&#x2f;" u2="y" k="-113" />
<hkern u1="&#x2f;" u2="x" k="-63" />
<hkern u1="&#x2f;" u2="w" k="-113" />
<hkern u1="&#x2f;" u2="v" k="-80" />
<hkern u1="&#x2f;" u2="u" k="-80" />
<hkern u1="&#x2f;" u2="t" k="-113" />
<hkern u1="&#x2f;" u2="q" k="33" />
<hkern u1="&#x2f;" u2="p" k="-63" />
<hkern u1="&#x2f;" u2="o" k="16" />
<hkern u1="&#x2f;" u2="m" k="-49" />
<hkern u1="&#x2f;" u2="l" k="-66" />
<hkern u1="&#x2f;" u2="k" k="-80" />
<hkern u1="&#x2f;" u2="i" k="-66" />
<hkern u1="&#x2f;" u2="h" k="-63" />
<hkern u1="&#x2f;" u2="g" k="-16" />
<hkern u1="&#x2f;" u2="f" k="-47" />
<hkern u1="&#x2f;" u2="e" k="-63" />
<hkern u1="&#x2f;" u2="d" k="-113" />
<hkern u1="&#x2f;" u2="c" k="16" />
<hkern u1="&#x2f;" u2="b" k="-80" />
<hkern u1="&#x2f;" u2="a" k="-113" />
<hkern u1="&#x2f;" u2="Z" k="-47" />
<hkern u1="&#x2f;" u2="Y" k="-96" />
<hkern u1="&#x2f;" u2="X" k="-80" />
<hkern u1="&#x2f;" u2="W" k="-80" />
<hkern u1="&#x2f;" u2="V" k="-96" />
<hkern u1="&#x2f;" u2="U" k="-96" />
<hkern u1="&#x2f;" u2="T" k="-80" />
<hkern u1="&#x2f;" u2="M" k="-96" />
<hkern u1="&#x2f;" u2="B" k="-96" />
<hkern u1="&#x2f;" u2="A" k="129" />
<hkern u1="&#x30;" u2="&#x37;" k="106" />
<hkern u1="&#x30;" u2="&#x36;" k="-43" />
<hkern u1="&#x30;" u2="&#x35;" k="-20" />
<hkern u1="&#x30;" u2="&#x33;" k="-20" />
<hkern u1="&#x30;" u2="&#x32;" k="63" />
<hkern u1="&#x31;" u2="&#x39;" k="-43" />
<hkern u1="&#x31;" u2="&#x38;" k="-43" />
<hkern u1="&#x31;" u2="&#x36;" k="-63" />
<hkern u1="&#x31;" u2="&#x35;" k="-43" />
<hkern u1="&#x31;" u2="&#x34;" k="-23" />
<hkern u1="&#x31;" u2="&#x33;" k="-43" />
<hkern u1="&#x31;" u2="&#x32;" k="-20" />
<hkern u1="&#x32;" u2="&#x37;" k="63" />
<hkern u1="&#x32;" u2="&#x35;" k="20" />
<hkern u1="&#x32;" u2="&#x34;" k="63" />
<hkern u1="&#x33;" u2="&#x39;" k="43" />
<hkern u1="&#x33;" u2="&#x37;" k="86" />
<hkern u1="&#x34;" u2="&#x38;" k="-43" />
<hkern u1="&#x34;" u2="&#x37;" k="106" />
<hkern u1="&#x35;" u2="&#x39;" k="63" />
<hkern u1="&#x35;" u2="&#x37;" k="106" />
<hkern u1="&#x36;" u2="&#x38;" k="-20" />
<hkern u1="&#x36;" u2="&#x37;" k="106" />
<hkern u1="&#x37;" u2="&#x39;" k="20" />
<hkern u1="&#x37;" u2="&#x38;" k="66" />
<hkern u1="&#x3a;" u2="&#xff;" k="96" />
<hkern u1="&#x3a;" u2="&#xfd;" k="96" />
<hkern u1="&#x3a;" u2="&#xfc;" k="-49" />
<hkern u1="&#x3a;" u2="&#xfb;" k="-49" />
<hkern u1="&#x3a;" u2="&#xfa;" k="-49" />
<hkern u1="&#x3a;" u2="&#xf9;" k="-49" />
<hkern u1="&#x3a;" u2="&#xdc;" k="-63" />
<hkern u1="&#x3a;" u2="&#xdb;" k="-63" />
<hkern u1="&#x3a;" u2="&#xda;" k="-63" />
<hkern u1="&#x3a;" u2="&#xd9;" k="-63" />
<hkern u1="&#x3a;" u2="y" k="96" />
<hkern u1="&#x3a;" u2="v" k="47" />
<hkern u1="&#x3a;" u2="u" k="-49" />
<hkern u1="&#x3a;" u2="s" k="-63" />
<hkern u1="&#x3a;" u2="g" k="-33" />
<hkern u1="&#x3a;" u2="Z" k="-63" />
<hkern u1="&#x3a;" u2="U" k="-63" />
<hkern u1="&#x3b;" u2="&#xff;" k="63" />
<hkern u1="&#x3b;" u2="&#xfd;" k="63" />
<hkern u1="&#x3b;" u2="&#xcb;" k="-63" />
<hkern u1="&#x3b;" u2="&#xca;" k="-63" />
<hkern u1="&#x3b;" u2="&#xc9;" k="-63" />
<hkern u1="&#x3b;" u2="&#xc8;" k="-63" />
<hkern u1="&#x3b;" u2="y" k="63" />
<hkern u1="&#x3b;" u2="g" k="-47" />
<hkern u1="&#x3b;" u2="V" k="-96" />
<hkern u1="&#x3b;" u2="T" k="-80" />
<hkern u1="&#x3b;" u2="Q" k="-63" />
<hkern u1="&#x3b;" u2="E" k="-63" />
<hkern u1="&#x3f;" u2="&#xe6;" k="96" />
<hkern u1="&#x3f;" u2="&#xe5;" k="96" />
<hkern u1="&#x3f;" u2="&#xe4;" k="96" />
<hkern u1="&#x3f;" u2="&#xe3;" k="96" />
<hkern u1="&#x3f;" u2="&#xe2;" k="96" />
<hkern u1="&#x3f;" u2="&#xe1;" k="96" />
<hkern u1="&#x3f;" u2="&#xe0;" k="96" />
<hkern u1="&#x3f;" u2="a" k="96" />
<hkern u1="&#x3f;" u2="J" k="80" />
<hkern u1="&#x3f;" u2="B" k="-33" />
<hkern u1="&#x40;" u2="&#xff;" k="14" />
<hkern u1="&#x40;" u2="&#xfd;" k="14" />
<hkern u1="&#x40;" u2="&#xf1;" k="-31" />
<hkern u1="&#x40;" u2="&#xe6;" k="63" />
<hkern u1="&#x40;" u2="&#xe5;" k="63" />
<hkern u1="&#x40;" u2="&#xe4;" k="63" />
<hkern u1="&#x40;" u2="&#xe3;" k="63" />
<hkern u1="&#x40;" u2="&#xe2;" k="63" />
<hkern u1="&#x40;" u2="&#xe1;" k="63" />
<hkern u1="&#x40;" u2="&#xe0;" k="63" />
<hkern u1="&#x40;" u2="&#xdc;" k="47" />
<hkern u1="&#x40;" u2="&#xdb;" k="47" />
<hkern u1="&#x40;" u2="&#xda;" k="47" />
<hkern u1="&#x40;" u2="&#xd9;" k="47" />
<hkern u1="&#x40;" u2="y" k="14" />
<hkern u1="&#x40;" u2="r" k="-80" />
<hkern u1="&#x40;" u2="p" k="-47" />
<hkern u1="&#x40;" u2="n" k="-31" />
<hkern u1="&#x40;" u2="j" k="96" />
<hkern u1="&#x40;" u2="g" k="80" />
<hkern u1="&#x40;" u2="a" k="63" />
<hkern u1="&#x40;" u2="V" k="63" />
<hkern u1="&#x40;" u2="U" k="47" />
<hkern u1="&#x40;" u2="T" k="47" />
<hkern u1="&#x40;" u2="R" k="16" />
<hkern u1="&#x40;" u2="P" k="63" />
<hkern u1="&#x40;" u2="F" k="80" />
<hkern u1="A" u2="x" k="-66" />
<hkern u1="A" u2="w" k="106" />
<hkern u1="A" u2="v" k="106" />
<hkern u1="A" u2="t" k="84" />
<hkern u1="A" u2="q" k="41" />
<hkern u1="A" u2="g" k="-43" />
<hkern u1="A" u2="f" k="41" />
<hkern u1="A" u2="d" k="43" />
<hkern u1="A" u2="W" k="190" />
<hkern u1="A" u2="V" k="213" />
<hkern u1="A" u2="T" k="213" />
<hkern u1="A" u2="R" k="-20" />
<hkern u1="A" u2="Q" k="43" />
<hkern u1="A" u2="J" k="86" />
<hkern u1="A" u2="G" k="43" />
<hkern u1="B" u2="&#x178;" k="86" />
<hkern u1="B" u2="&#x152;" k="-23" />
<hkern u1="B" u2="&#xde;" k="-41" />
<hkern u1="B" u2="&#xdd;" k="86" />
<hkern u1="B" u2="&#xd8;" k="-23" />
<hkern u1="B" u2="&#xd6;" k="-23" />
<hkern u1="B" u2="&#xd5;" k="-23" />
<hkern u1="B" u2="&#xd4;" k="-23" />
<hkern u1="B" u2="&#xd3;" k="-23" />
<hkern u1="B" u2="&#xd2;" k="-23" />
<hkern u1="B" u2="v" k="20" />
<hkern u1="B" u2="h" k="-20" />
<hkern u1="B" u2="g" k="-20" />
<hkern u1="B" u2="Y" k="86" />
<hkern u1="B" u2="W" k="86" />
<hkern u1="B" u2="V" k="63" />
<hkern u1="B" u2="S" k="-43" />
<hkern u1="B" u2="Q" k="-43" />
<hkern u1="B" u2="O" k="-23" />
<hkern u1="B" u2="J" k="-43" />
<hkern u1="B" u2="G" k="-23" />
<hkern u1="B" u2="D" k="-41" />
<hkern u1="C" u2="w" k="-43" />
<hkern u1="C" u2="t" k="-43" />
<hkern u1="C" u2="m" k="-43" />
<hkern u1="C" u2="h" k="-43" />
<hkern u1="C" u2="g" k="-20" />
<hkern u1="C" u2="T" k="-43" />
<hkern u1="C" u2="R" k="-63" />
<hkern u1="C" u2="P" k="-43" />
<hkern u1="C" u2="M" k="-43" />
<hkern u1="C" u2="K" k="-66" />
<hkern u1="C" u2="H" k="-63" />
<hkern u1="D" u2="w" k="-43" />
<hkern u1="D" u2="m" k="-43" />
<hkern u1="D" u2="j" k="-43" />
<hkern u1="D" u2="h" k="-20" />
<hkern u1="D" u2="X" k="106" />
<hkern u1="D" u2="W" k="63" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="T" k="63" />
<hkern u1="D" u2="Q" k="-43" />
<hkern u1="D" u2="F" k="-43" />
<hkern u1="E" u2="x" k="-43" />
<hkern u1="E" u2="j" k="-84" />
<hkern u1="E" u2="W" k="-20" />
<hkern u1="E" u2="V" k="-23" />
<hkern u1="E" u2="T" k="-84" />
<hkern u1="E" u2="J" k="-23" />
<hkern u1="F" u2="&#x178;" k="-63" />
<hkern u1="F" u2="&#x152;" k="-43" />
<hkern u1="F" u2="&#xdd;" k="-63" />
<hkern u1="F" u2="&#xd8;" k="-43" />
<hkern u1="F" u2="&#xd6;" k="-43" />
<hkern u1="F" u2="&#xd5;" k="-43" />
<hkern u1="F" u2="&#xd4;" k="-43" />
<hkern u1="F" u2="&#xd3;" k="-43" />
<hkern u1="F" u2="&#xd2;" k="-43" />
<hkern u1="F" u2="&#xcf;" k="-20" />
<hkern u1="F" u2="&#xce;" k="-20" />
<hkern u1="F" u2="&#xcd;" k="-20" />
<hkern u1="F" u2="&#xcc;" k="-20" />
<hkern u1="F" u2="f" k="-43" />
<hkern u1="F" u2="Y" k="-63" />
<hkern u1="F" u2="V" k="-63" />
<hkern u1="F" u2="T" k="-63" />
<hkern u1="F" u2="O" k="-43" />
<hkern u1="F" u2="I" k="-20" />
<hkern u1="F" u2="G" k="-43" />
<hkern u1="G" u2="&#x178;" k="86" />
<hkern u1="G" u2="&#xff;" k="20" />
<hkern u1="G" u2="&#xfd;" k="20" />
<hkern u1="G" u2="&#xeb;" k="-23" />
<hkern u1="G" u2="&#xea;" k="-23" />
<hkern u1="G" u2="&#xe9;" k="-23" />
<hkern u1="G" u2="&#xe8;" k="-23" />
<hkern u1="G" u2="&#xdd;" k="86" />
<hkern u1="G" u2="y" k="20" />
<hkern u1="G" u2="e" k="-23" />
<hkern u1="G" u2="Y" k="86" />
<hkern u1="G" u2="W" k="84" />
<hkern u1="G" u2="V" k="63" />
<hkern u1="G" u2="T" k="86" />
<hkern u1="G" u2="R" k="20" />
<hkern u1="I" u2="W" k="-43" />
<hkern u1="I" u2="T" k="-66" />
<hkern u1="I" u2="J" k="-20" />
<hkern u1="J" u2="&#x153;" k="20" />
<hkern u1="J" u2="&#xfc;" k="23" />
<hkern u1="J" u2="&#xfb;" k="23" />
<hkern u1="J" u2="&#xfa;" k="23" />
<hkern u1="J" u2="&#xf9;" k="23" />
<hkern u1="J" u2="&#xf8;" k="20" />
<hkern u1="J" u2="&#xf6;" k="20" />
<hkern u1="J" u2="&#xf5;" k="20" />
<hkern u1="J" u2="&#xf4;" k="20" />
<hkern u1="J" u2="&#xf3;" k="20" />
<hkern u1="J" u2="&#xf2;" k="20" />
<hkern u1="J" u2="&#xf1;" k="20" />
<hkern u1="J" u2="&#xeb;" k="20" />
<hkern u1="J" u2="&#xea;" k="20" />
<hkern u1="J" u2="&#xe9;" k="20" />
<hkern u1="J" u2="&#xe8;" k="20" />
<hkern u1="J" u2="&#xe6;" k="43" />
<hkern u1="J" u2="&#xe5;" k="43" />
<hkern u1="J" u2="&#xe4;" k="43" />
<hkern u1="J" u2="&#xe3;" k="43" />
<hkern u1="J" u2="&#xe2;" k="43" />
<hkern u1="J" u2="&#xe1;" k="43" />
<hkern u1="J" u2="&#xe0;" k="43" />
<hkern u1="J" u2="u" k="23" />
<hkern u1="J" u2="o" k="20" />
<hkern u1="J" u2="n" k="20" />
<hkern u1="J" u2="j" k="41" />
<hkern u1="J" u2="g" k="41" />
<hkern u1="J" u2="e" k="20" />
<hkern u1="J" u2="a" k="43" />
<hkern u1="J" u2="X" k="84" />
<hkern u1="K" u2="&#x153;" k="86" />
<hkern u1="K" u2="&#x152;" k="127" />
<hkern u1="K" u2="&#xff;" k="86" />
<hkern u1="K" u2="&#xfd;" k="86" />
<hkern u1="K" u2="&#xfc;" k="66" />
<hkern u1="K" u2="&#xfb;" k="66" />
<hkern u1="K" u2="&#xfa;" k="66" />
<hkern u1="K" u2="&#xf9;" k="66" />
<hkern u1="K" u2="&#xf8;" k="86" />
<hkern u1="K" u2="&#xf6;" k="86" />
<hkern u1="K" u2="&#xf5;" k="86" />
<hkern u1="K" u2="&#xf4;" k="86" />
<hkern u1="K" u2="&#xf3;" k="86" />
<hkern u1="K" u2="&#xf2;" k="86" />
<hkern u1="K" u2="&#xf1;" k="43" />
<hkern u1="K" u2="&#xeb;" k="63" />
<hkern u1="K" u2="&#xea;" k="63" />
<hkern u1="K" u2="&#xe9;" k="63" />
<hkern u1="K" u2="&#xe8;" k="63" />
<hkern u1="K" u2="&#xe7;" k="63" />
<hkern u1="K" u2="&#xe6;" k="41" />
<hkern u1="K" u2="&#xe5;" k="41" />
<hkern u1="K" u2="&#xe4;" k="41" />
<hkern u1="K" u2="&#xe3;" k="41" />
<hkern u1="K" u2="&#xe2;" k="41" />
<hkern u1="K" u2="&#xe1;" k="41" />
<hkern u1="K" u2="&#xe0;" k="41" />
<hkern u1="K" u2="&#xdc;" k="66" />
<hkern u1="K" u2="&#xdb;" k="66" />
<hkern u1="K" u2="&#xda;" k="66" />
<hkern u1="K" u2="&#xd9;" k="66" />
<hkern u1="K" u2="&#xd8;" k="127" />
<hkern u1="K" u2="&#xd6;" k="127" />
<hkern u1="K" u2="&#xd5;" k="127" />
<hkern u1="K" u2="&#xd4;" k="127" />
<hkern u1="K" u2="&#xd3;" k="127" />
<hkern u1="K" u2="&#xd2;" k="127" />
<hkern u1="K" u2="z" k="43" />
<hkern u1="K" u2="y" k="86" />
<hkern u1="K" u2="w" k="106" />
<hkern u1="K" u2="v" k="106" />
<hkern u1="K" u2="u" k="66" />
<hkern u1="K" u2="t" k="63" />
<hkern u1="K" u2="s" k="43" />
<hkern u1="K" u2="q" k="63" />
<hkern u1="K" u2="o" k="86" />
<hkern u1="K" u2="n" k="43" />
<hkern u1="K" u2="m" k="43" />
<hkern u1="K" u2="g" k="-41" />
<hkern u1="K" u2="e" k="63" />
<hkern u1="K" u2="d" k="63" />
<hkern u1="K" u2="c" k="63" />
<hkern u1="K" u2="a" k="41" />
<hkern u1="K" u2="U" k="66" />
<hkern u1="K" u2="R" k="23" />
<hkern u1="K" u2="Q" k="86" />
<hkern u1="K" u2="O" k="127" />
<hkern u1="L" u2="g" k="-43" />
<hkern u1="L" u2="W" k="213" />
<hkern u1="L" u2="V" k="213" />
<hkern u1="L" u2="T" k="213" />
<hkern u1="M" u2="&#xe6;" k="-20" />
<hkern u1="M" u2="&#xe5;" k="-20" />
<hkern u1="M" u2="&#xe4;" k="-20" />
<hkern u1="M" u2="&#xe3;" k="-20" />
<hkern u1="M" u2="&#xe2;" k="-20" />
<hkern u1="M" u2="&#xe1;" k="-20" />
<hkern u1="M" u2="&#xe0;" k="-20" />
<hkern u1="M" u2="a" k="-20" />
<hkern u1="M" u2="W" k="43" />
<hkern u1="O" u2="b" k="-20" />
<hkern u1="O" u2="X" k="86" />
<hkern u1="O" u2="W" k="63" />
<hkern u1="O" u2="V" k="66" />
<hkern u1="O" u2="T" k="84" />
<hkern u1="O" u2="Q" k="-43" />
<hkern u1="P" u2="&#x178;" k="20" />
<hkern u1="P" u2="&#x153;" k="43" />
<hkern u1="P" u2="&#xff;" k="-43" />
<hkern u1="P" u2="&#xfd;" k="-43" />
<hkern u1="P" u2="&#xf8;" k="43" />
<hkern u1="P" u2="&#xf6;" k="43" />
<hkern u1="P" u2="&#xf5;" k="43" />
<hkern u1="P" u2="&#xf4;" k="43" />
<hkern u1="P" u2="&#xf3;" k="43" />
<hkern u1="P" u2="&#xf2;" k="43" />
<hkern u1="P" u2="&#xeb;" k="43" />
<hkern u1="P" u2="&#xea;" k="43" />
<hkern u1="P" u2="&#xe9;" k="43" />
<hkern u1="P" u2="&#xe8;" k="43" />
<hkern u1="P" u2="&#xe7;" k="63" />
<hkern u1="P" u2="&#xe6;" k="43" />
<hkern u1="P" u2="&#xe5;" k="43" />
<hkern u1="P" u2="&#xe4;" k="43" />
<hkern u1="P" u2="&#xe3;" k="43" />
<hkern u1="P" u2="&#xe2;" k="43" />
<hkern u1="P" u2="&#xe1;" k="43" />
<hkern u1="P" u2="&#xe0;" k="43" />
<hkern u1="P" u2="&#xdd;" k="20" />
<hkern u1="P" u2="y" k="-43" />
<hkern u1="P" u2="w" k="-43" />
<hkern u1="P" u2="v" k="-43" />
<hkern u1="P" u2="t" k="-23" />
<hkern u1="P" u2="r" k="43" />
<hkern u1="P" u2="q" k="66" />
<hkern u1="P" u2="o" k="43" />
<hkern u1="P" u2="g" k="41" />
<hkern u1="P" u2="e" k="43" />
<hkern u1="P" u2="d" k="63" />
<hkern u1="P" u2="c" k="63" />
<hkern u1="P" u2="a" k="43" />
<hkern u1="P" u2="Y" k="20" />
<hkern u1="P" u2="X" k="86" />
<hkern u1="P" u2="W" k="23" />
<hkern u1="Q" u2="&#x178;" k="84" />
<hkern u1="Q" u2="&#xdd;" k="84" />
<hkern u1="Q" u2="Y" k="84" />
<hkern u1="Q" u2="W" k="43" />
<hkern u1="Q" u2="V" k="63" />
<hkern u1="Q" u2="T" k="84" />
<hkern u1="R" u2="&#xff;" k="-84" />
<hkern u1="R" u2="&#xfd;" k="-84" />
<hkern u1="R" u2="&#xdc;" k="-43" />
<hkern u1="R" u2="&#xdb;" k="-43" />
<hkern u1="R" u2="&#xda;" k="-43" />
<hkern u1="R" u2="&#xd9;" k="-43" />
<hkern u1="R" u2="y" k="-84" />
<hkern u1="R" u2="w" k="-43" />
<hkern u1="R" u2="r" k="-20" />
<hkern u1="R" u2="j" k="-86" />
<hkern u1="R" u2="h" k="-23" />
<hkern u1="R" u2="Z" k="-20" />
<hkern u1="R" u2="U" k="-43" />
<hkern u1="R" u2="T" k="-63" />
<hkern u1="R" u2="S" k="-63" />
<hkern u1="S" u2="w" k="20" />
<hkern u1="S" u2="v" k="23" />
<hkern u1="S" u2="t" k="-23" />
<hkern u1="S" u2="r" k="-41" />
<hkern u1="S" u2="q" k="-41" />
<hkern u1="S" u2="p" k="-20" />
<hkern u1="S" u2="m" k="-20" />
<hkern u1="S" u2="k" k="-23" />
<hkern u1="S" u2="h" k="-20" />
<hkern u1="S" u2="g" k="-63" />
<hkern u1="T" u2="&#x178;" k="-86" />
<hkern u1="T" u2="&#x153;" k="276" />
<hkern u1="T" u2="&#xff;" k="190" />
<hkern u1="T" u2="&#xfd;" k="190" />
<hkern u1="T" u2="&#xfc;" k="233" />
<hkern u1="T" u2="&#xfb;" k="233" />
<hkern u1="T" u2="&#xfa;" k="233" />
<hkern u1="T" u2="&#xf9;" k="233" />
<hkern u1="T" u2="&#xf8;" k="276" />
<hkern u1="T" u2="&#xf6;" k="276" />
<hkern u1="T" u2="&#xf5;" k="276" />
<hkern u1="T" u2="&#xf4;" k="276" />
<hkern u1="T" u2="&#xf3;" k="276" />
<hkern u1="T" u2="&#xf2;" k="276" />
<hkern u1="T" u2="&#xf1;" k="236" />
<hkern u1="T" u2="&#xef;" k="20" />
<hkern u1="T" u2="&#xee;" k="20" />
<hkern u1="T" u2="&#xed;" k="20" />
<hkern u1="T" u2="&#xec;" k="20" />
<hkern u1="T" u2="&#xeb;" k="276" />
<hkern u1="T" u2="&#xea;" k="276" />
<hkern u1="T" u2="&#xe9;" k="276" />
<hkern u1="T" u2="&#xe8;" k="276" />
<hkern u1="T" u2="&#xe7;" k="256" />
<hkern u1="T" u2="&#xe6;" k="256" />
<hkern u1="T" u2="&#xe5;" k="256" />
<hkern u1="T" u2="&#xe4;" k="256" />
<hkern u1="T" u2="&#xe3;" k="256" />
<hkern u1="T" u2="&#xe2;" k="256" />
<hkern u1="T" u2="&#xe1;" k="256" />
<hkern u1="T" u2="&#xe0;" k="256" />
<hkern u1="T" u2="&#xdd;" k="-86" />
<hkern u1="T" u2="&#xdc;" k="-43" />
<hkern u1="T" u2="&#xdb;" k="-43" />
<hkern u1="T" u2="&#xda;" k="-43" />
<hkern u1="T" u2="&#xd9;" k="-43" />
<hkern u1="T" u2="z" k="170" />
<hkern u1="T" u2="y" k="190" />
<hkern u1="T" u2="x" k="193" />
<hkern u1="T" u2="w" k="190" />
<hkern u1="T" u2="v" k="213" />
<hkern u1="T" u2="u" k="233" />
<hkern u1="T" u2="t" k="41" />
<hkern u1="T" u2="s" k="256" />
<hkern u1="T" u2="r" k="213" />
<hkern u1="T" u2="q" k="256" />
<hkern u1="T" u2="p" k="233" />
<hkern u1="T" u2="o" k="276" />
<hkern u1="T" u2="n" k="236" />
<hkern u1="T" u2="m" k="233" />
<hkern u1="T" u2="i" k="20" />
<hkern u1="T" u2="g" k="256" />
<hkern u1="T" u2="f" k="106" />
<hkern u1="T" u2="e" k="276" />
<hkern u1="T" u2="d" k="236" />
<hkern u1="T" u2="c" k="256" />
<hkern u1="T" u2="a" k="256" />
<hkern u1="T" u2="Z" k="-43" />
<hkern u1="T" u2="Y" k="-86" />
<hkern u1="T" u2="W" k="-66" />
<hkern u1="T" u2="V" k="-66" />
<hkern u1="T" u2="U" k="-43" />
<hkern u1="T" u2="&#x2e;" k="129" />
<hkern u1="U" u2="x" k="43" />
<hkern u1="U" u2="w" k="23" />
<hkern u1="U" u2="v" k="20" />
<hkern u1="U" u2="r" k="43" />
<hkern u1="U" u2="q" k="43" />
<hkern u1="U" u2="p" k="23" />
<hkern u1="U" u2="m" k="23" />
<hkern u1="U" u2="k" k="20" />
<hkern u1="U" u2="h" k="23" />
<hkern u1="U" u2="g" k="43" />
<hkern u1="U" u2="d" k="43" />
<hkern u1="U" u2="X" k="63" />
<hkern u1="U" u2="W" k="66" />
<hkern u1="V" u2="&#x153;" k="193" />
<hkern u1="V" u2="&#xff;" k="63" />
<hkern u1="V" u2="&#xfd;" k="63" />
<hkern u1="V" u2="&#xfc;" k="150" />
<hkern u1="V" u2="&#xfb;" k="150" />
<hkern u1="V" u2="&#xfa;" k="150" />
<hkern u1="V" u2="&#xf9;" k="150" />
<hkern u1="V" u2="&#xf8;" k="193" />
<hkern u1="V" u2="&#xf6;" k="193" />
<hkern u1="V" u2="&#xf5;" k="193" />
<hkern u1="V" u2="&#xf4;" k="193" />
<hkern u1="V" u2="&#xf3;" k="193" />
<hkern u1="V" u2="&#xf2;" k="193" />
<hkern u1="V" u2="&#xf1;" k="150" />
<hkern u1="V" u2="&#xef;" k="41" />
<hkern u1="V" u2="&#xee;" k="41" />
<hkern u1="V" u2="&#xed;" k="41" />
<hkern u1="V" u2="&#xec;" k="41" />
<hkern u1="V" u2="&#xeb;" k="193" />
<hkern u1="V" u2="&#xea;" k="193" />
<hkern u1="V" u2="&#xe9;" k="193" />
<hkern u1="V" u2="&#xe8;" k="193" />
<hkern u1="V" u2="&#xe7;" k="172" />
<hkern u1="V" u2="&#xe6;" k="172" />
<hkern u1="V" u2="&#xe5;" k="172" />
<hkern u1="V" u2="&#xe4;" k="172" />
<hkern u1="V" u2="&#xe3;" k="172" />
<hkern u1="V" u2="&#xe2;" k="172" />
<hkern u1="V" u2="&#xe1;" k="172" />
<hkern u1="V" u2="&#xe0;" k="172" />
<hkern u1="V" u2="z" k="150" />
<hkern u1="V" u2="y" k="63" />
<hkern u1="V" u2="x" k="63" />
<hkern u1="V" u2="w" k="63" />
<hkern u1="V" u2="v" k="86" />
<hkern u1="V" u2="u" k="150" />
<hkern u1="V" u2="t" k="86" />
<hkern u1="V" u2="s" k="150" />
<hkern u1="V" u2="r" k="150" />
<hkern u1="V" u2="q" k="170" />
<hkern u1="V" u2="p" k="150" />
<hkern u1="V" u2="o" k="193" />
<hkern u1="V" u2="n" k="150" />
<hkern u1="V" u2="m" k="150" />
<hkern u1="V" u2="k" k="43" />
<hkern u1="V" u2="i" k="41" />
<hkern u1="V" u2="h" k="43" />
<hkern u1="V" u2="g" k="213" />
<hkern u1="V" u2="f" k="84" />
<hkern u1="V" u2="e" k="193" />
<hkern u1="V" u2="d" k="170" />
<hkern u1="V" u2="c" k="172" />
<hkern u1="V" u2="a" k="172" />
<hkern u1="W" u2="&#x178;" k="-86" />
<hkern u1="W" u2="&#x153;" k="193" />
<hkern u1="W" u2="&#xff;" k="63" />
<hkern u1="W" u2="&#xfd;" k="63" />
<hkern u1="W" u2="&#xfc;" k="129" />
<hkern u1="W" u2="&#xfb;" k="129" />
<hkern u1="W" u2="&#xfa;" k="129" />
<hkern u1="W" u2="&#xf9;" k="129" />
<hkern u1="W" u2="&#xf8;" k="193" />
<hkern u1="W" u2="&#xf6;" k="193" />
<hkern u1="W" u2="&#xf5;" k="193" />
<hkern u1="W" u2="&#xf4;" k="193" />
<hkern u1="W" u2="&#xf3;" k="193" />
<hkern u1="W" u2="&#xf2;" k="193" />
<hkern u1="W" u2="&#xf1;" k="106" />
<hkern u1="W" u2="&#xef;" k="23" />
<hkern u1="W" u2="&#xee;" k="23" />
<hkern u1="W" u2="&#xed;" k="23" />
<hkern u1="W" u2="&#xec;" k="23" />
<hkern u1="W" u2="&#xeb;" k="170" />
<hkern u1="W" u2="&#xea;" k="170" />
<hkern u1="W" u2="&#xe9;" k="170" />
<hkern u1="W" u2="&#xe8;" k="170" />
<hkern u1="W" u2="&#xe7;" k="172" />
<hkern u1="W" u2="&#xe6;" k="170" />
<hkern u1="W" u2="&#xe5;" k="170" />
<hkern u1="W" u2="&#xe4;" k="170" />
<hkern u1="W" u2="&#xe3;" k="170" />
<hkern u1="W" u2="&#xe2;" k="170" />
<hkern u1="W" u2="&#xe1;" k="170" />
<hkern u1="W" u2="&#xe0;" k="170" />
<hkern u1="W" u2="&#xdd;" k="-86" />
<hkern u1="W" u2="z" k="106" />
<hkern u1="W" u2="y" k="63" />
<hkern u1="W" u2="x" k="84" />
<hkern u1="W" u2="w" k="86" />
<hkern u1="W" u2="u" k="129" />
<hkern u1="W" u2="t" k="66" />
<hkern u1="W" u2="s" k="106" />
<hkern u1="W" u2="r" k="106" />
<hkern u1="W" u2="q" k="170" />
<hkern u1="W" u2="p" k="106" />
<hkern u1="W" u2="o" k="193" />
<hkern u1="W" u2="n" k="106" />
<hkern u1="W" u2="m" k="106" />
<hkern u1="W" u2="i" k="23" />
<hkern u1="W" u2="h" k="43" />
<hkern u1="W" u2="g" k="172" />
<hkern u1="W" u2="e" k="170" />
<hkern u1="W" u2="d" k="170" />
<hkern u1="W" u2="c" k="172" />
<hkern u1="W" u2="a" k="170" />
<hkern u1="W" u2="Y" k="-86" />
<hkern u1="X" u2="&#x153;" k="43" />
<hkern u1="X" u2="&#xff;" k="63" />
<hkern u1="X" u2="&#xfd;" k="63" />
<hkern u1="X" u2="&#xf8;" k="43" />
<hkern u1="X" u2="&#xf6;" k="43" />
<hkern u1="X" u2="&#xf5;" k="43" />
<hkern u1="X" u2="&#xf4;" k="43" />
<hkern u1="X" u2="&#xf3;" k="43" />
<hkern u1="X" u2="&#xf2;" k="43" />
<hkern u1="X" u2="&#xeb;" k="41" />
<hkern u1="X" u2="&#xea;" k="41" />
<hkern u1="X" u2="&#xe9;" k="41" />
<hkern u1="X" u2="&#xe8;" k="41" />
<hkern u1="X" u2="y" k="63" />
<hkern u1="X" u2="w" k="43" />
<hkern u1="X" u2="v" k="41" />
<hkern u1="X" u2="t" k="43" />
<hkern u1="X" u2="o" k="43" />
<hkern u1="X" u2="j" k="-109" />
<hkern u1="X" u2="g" k="-43" />
<hkern u1="X" u2="e" k="41" />
<hkern u1="X" u2="d" k="41" />
<hkern u1="Y" u2="x" k="127" />
<hkern u1="Y" u2="w" k="106" />
<hkern u1="Y" u2="v" k="106" />
<hkern u1="Y" u2="r" k="172" />
<hkern u1="Y" u2="q" k="236" />
<hkern u1="Y" u2="p" k="172" />
<hkern u1="Y" u2="m" k="170" />
<hkern u1="Y" u2="k" k="63" />
<hkern u1="Y" u2="h" k="63" />
<hkern u1="Y" u2="g" k="236" />
<hkern u1="Y" u2="f" k="63" />
<hkern u1="Y" u2="d" k="233" />
<hkern u1="Z" u2="g" k="-66" />
<hkern u1="a" u2="w" k="66" />
<hkern u1="a" u2="v" k="63" />
<hkern u1="a" u2="t" k="43" />
<hkern u1="a" u2="j" k="-43" />
<hkern u1="a" u2="b" k="20" />
<hkern u1="b" u2="&#xff;" k="43" />
<hkern u1="b" u2="&#xfd;" k="43" />
<hkern u1="b" u2="z" k="43" />
<hkern u1="b" u2="y" k="43" />
<hkern u1="b" u2="w" k="43" />
<hkern u1="b" u2="v" k="41" />
<hkern u1="b" u2="t" k="41" />
<hkern u1="b" u2="m" k="20" />
<hkern u1="c" u2="q" k="-41" />
<hkern u1="c" u2="g" k="-23" />
<hkern u1="d" u2="&#xfc;" k="23" />
<hkern u1="d" u2="&#xfb;" k="23" />
<hkern u1="d" u2="&#xfa;" k="23" />
<hkern u1="d" u2="&#xf9;" k="23" />
<hkern u1="d" u2="&#xeb;" k="-23" />
<hkern u1="d" u2="&#xea;" k="-23" />
<hkern u1="d" u2="&#xe9;" k="-23" />
<hkern u1="d" u2="&#xe8;" k="-23" />
<hkern u1="d" u2="w" k="43" />
<hkern u1="d" u2="u" k="23" />
<hkern u1="d" u2="j" k="-43" />
<hkern u1="d" u2="e" k="-23" />
<hkern u1="e" u2="x" k="20" />
<hkern u1="e" u2="w" k="41" />
<hkern u1="e" u2="v" k="23" />
<hkern u1="e" u2="t" k="43" />
<hkern u1="e" u2="m" k="20" />
<hkern u1="e" u2="k" k="20" />
<hkern u1="e" u2="h" k="20" />
<hkern u1="f" u2="&#x178;" k="-43" />
<hkern u1="f" u2="&#x153;" k="84" />
<hkern u1="f" u2="&#xfc;" k="43" />
<hkern u1="f" u2="&#xfb;" k="43" />
<hkern u1="f" u2="&#xfa;" k="43" />
<hkern u1="f" u2="&#xf9;" k="43" />
<hkern u1="f" u2="&#xf8;" k="84" />
<hkern u1="f" u2="&#xf6;" k="84" />
<hkern u1="f" u2="&#xf5;" k="84" />
<hkern u1="f" u2="&#xf4;" k="84" />
<hkern u1="f" u2="&#xf3;" k="84" />
<hkern u1="f" u2="&#xf2;" k="84" />
<hkern u1="f" u2="&#xf1;" k="63" />
<hkern u1="f" u2="&#xef;" k="20" />
<hkern u1="f" u2="&#xee;" k="20" />
<hkern u1="f" u2="&#xed;" k="20" />
<hkern u1="f" u2="&#xec;" k="20" />
<hkern u1="f" u2="&#xdd;" k="-43" />
<hkern u1="f" u2="x" k="43" />
<hkern u1="f" u2="u" k="43" />
<hkern u1="f" u2="s" k="63" />
<hkern u1="f" u2="r" k="20" />
<hkern u1="f" u2="o" k="84" />
<hkern u1="f" u2="n" k="63" />
<hkern u1="f" u2="i" k="20" />
<hkern u1="f" u2="g" k="86" />
<hkern u1="f" u2="Y" k="-43" />
<hkern u1="f" u2="W" k="-84" />
<hkern u1="f" u2="V" k="-63" />
<hkern u1="f" u2="T" k="-43" />
<hkern u1="g" u2="&#xff;" k="-20" />
<hkern u1="g" u2="&#xfd;" k="-20" />
<hkern u1="g" u2="y" k="-20" />
<hkern u1="g" u2="x" k="-63" />
<hkern u1="g" u2="w" k="-23" />
<hkern u1="g" u2="v" k="-20" />
<hkern u1="g" u2="j" k="-256" />
<hkern u1="g" u2="h" k="-20" />
<hkern u1="h" u2="&#xff;" k="43" />
<hkern u1="h" u2="&#xfd;" k="43" />
<hkern u1="h" u2="&#xfc;" k="20" />
<hkern u1="h" u2="&#xfb;" k="20" />
<hkern u1="h" u2="&#xfa;" k="20" />
<hkern u1="h" u2="&#xf9;" k="20" />
<hkern u1="h" u2="y" k="43" />
<hkern u1="h" u2="w" k="63" />
<hkern u1="h" u2="v" k="63" />
<hkern u1="h" u2="u" k="20" />
<hkern u1="i" u2="x" k="-43" />
<hkern u1="i" u2="J" k="-86" />
<hkern u1="j" u2="K" k="-23" />
<hkern u1="k" u2="&#x153;" k="43" />
<hkern u1="k" u2="&#xff;" k="-43" />
<hkern u1="k" u2="&#xfd;" k="-43" />
<hkern u1="k" u2="&#xf8;" k="43" />
<hkern u1="k" u2="&#xf6;" k="43" />
<hkern u1="k" u2="&#xf5;" k="43" />
<hkern u1="k" u2="&#xf4;" k="43" />
<hkern u1="k" u2="&#xf3;" k="43" />
<hkern u1="k" u2="&#xf2;" k="43" />
<hkern u1="k" u2="y" k="-43" />
<hkern u1="k" u2="v" k="-43" />
<hkern u1="k" u2="o" k="43" />
<hkern u1="m" u2="&#xff;" k="43" />
<hkern u1="m" u2="&#xfd;" k="43" />
<hkern u1="m" u2="&#xfc;" k="43" />
<hkern u1="m" u2="&#xfb;" k="43" />
<hkern u1="m" u2="&#xfa;" k="43" />
<hkern u1="m" u2="&#xf9;" k="43" />
<hkern u1="m" u2="y" k="43" />
<hkern u1="m" u2="w" k="43" />
<hkern u1="m" u2="v" k="43" />
<hkern u1="m" u2="u" k="43" />
<hkern u1="m" u2="t" k="43" />
<hkern u1="m" u2="p" k="20" />
<hkern u1="n" u2="w" k="43" />
<hkern u1="n" u2="v" k="63" />
<hkern u1="n" u2="t" k="43" />
<hkern u1="o" u2="x" k="20" />
<hkern u1="o" u2="w" k="43" />
<hkern u1="o" u2="v" k="20" />
<hkern u1="o" u2="t" k="20" />
<hkern u1="q" u2="&#xfc;" k="20" />
<hkern u1="q" u2="&#xfb;" k="20" />
<hkern u1="q" u2="&#xfa;" k="20" />
<hkern u1="q" u2="&#xf9;" k="20" />
<hkern u1="q" u2="u" k="20" />
<hkern u1="r" u2="&#xff;" k="-63" />
<hkern u1="r" u2="&#xfd;" k="-63" />
<hkern u1="r" u2="&#xfc;" k="-20" />
<hkern u1="r" u2="&#xfb;" k="-20" />
<hkern u1="r" u2="&#xfa;" k="-20" />
<hkern u1="r" u2="&#xf9;" k="-20" />
<hkern u1="r" u2="y" k="-63" />
<hkern u1="r" u2="x" k="-43" />
<hkern u1="r" u2="w" k="-43" />
<hkern u1="r" u2="v" k="-63" />
<hkern u1="r" u2="u" k="-20" />
<hkern u1="r" u2="t" k="-63" />
<hkern u1="t" u2="&#xff;" k="-23" />
<hkern u1="t" u2="&#xfd;" k="-23" />
<hkern u1="t" u2="y" k="-23" />
<hkern u1="v" u2="&#xff;" k="-43" />
<hkern u1="v" u2="&#xfd;" k="-43" />
<hkern u1="v" u2="y" k="-43" />
<hkern u1="v" u2="x" k="-43" />
<hkern u1="v" u2="w" k="-63" />
<hkern u1="w" u2="&#xff;" k="-23" />
<hkern u1="w" u2="&#xfd;" k="-23" />
<hkern u1="w" u2="z" k="43" />
<hkern u1="w" u2="y" k="-23" />
<hkern u1="w" u2="x" k="-43" />
<hkern u1="x" u2="&#xff;" k="-66" />
<hkern u1="x" u2="&#xfd;" k="-66" />
<hkern u1="x" u2="y" k="-66" />
<hkern u1="&#xc0;" u2="x" k="-66" />
<hkern u1="&#xc0;" u2="w" k="106" />
<hkern u1="&#xc0;" u2="v" k="106" />
<hkern u1="&#xc0;" u2="t" k="84" />
<hkern u1="&#xc0;" u2="q" k="41" />
<hkern u1="&#xc0;" u2="g" k="-43" />
<hkern u1="&#xc0;" u2="f" k="41" />
<hkern u1="&#xc0;" u2="d" k="43" />
<hkern u1="&#xc0;" u2="W" k="190" />
<hkern u1="&#xc0;" u2="V" k="213" />
<hkern u1="&#xc0;" u2="T" k="213" />
<hkern u1="&#xc0;" u2="R" k="-20" />
<hkern u1="&#xc0;" u2="Q" k="43" />
<hkern u1="&#xc0;" u2="J" k="86" />
<hkern u1="&#xc0;" u2="G" k="43" />
<hkern u1="&#xc1;" u2="x" k="-66" />
<hkern u1="&#xc1;" u2="w" k="106" />
<hkern u1="&#xc1;" u2="v" k="106" />
<hkern u1="&#xc1;" u2="t" k="84" />
<hkern u1="&#xc1;" u2="q" k="41" />
<hkern u1="&#xc1;" u2="g" k="-43" />
<hkern u1="&#xc1;" u2="f" k="41" />
<hkern u1="&#xc1;" u2="d" k="43" />
<hkern u1="&#xc1;" u2="W" k="190" />
<hkern u1="&#xc1;" u2="V" k="213" />
<hkern u1="&#xc1;" u2="T" k="213" />
<hkern u1="&#xc1;" u2="R" k="-20" />
<hkern u1="&#xc1;" u2="Q" k="43" />
<hkern u1="&#xc1;" u2="J" k="86" />
<hkern u1="&#xc1;" u2="G" k="43" />
<hkern u1="&#xc2;" u2="x" k="-66" />
<hkern u1="&#xc2;" u2="w" k="106" />
<hkern u1="&#xc2;" u2="v" k="106" />
<hkern u1="&#xc2;" u2="t" k="84" />
<hkern u1="&#xc2;" u2="q" k="41" />
<hkern u1="&#xc2;" u2="g" k="-43" />
<hkern u1="&#xc2;" u2="f" k="41" />
<hkern u1="&#xc2;" u2="d" k="43" />
<hkern u1="&#xc2;" u2="W" k="190" />
<hkern u1="&#xc2;" u2="V" k="213" />
<hkern u1="&#xc2;" u2="T" k="213" />
<hkern u1="&#xc2;" u2="R" k="-20" />
<hkern u1="&#xc2;" u2="Q" k="43" />
<hkern u1="&#xc2;" u2="J" k="86" />
<hkern u1="&#xc2;" u2="G" k="43" />
<hkern u1="&#xc3;" u2="x" k="-66" />
<hkern u1="&#xc3;" u2="w" k="106" />
<hkern u1="&#xc3;" u2="v" k="106" />
<hkern u1="&#xc3;" u2="t" k="84" />
<hkern u1="&#xc3;" u2="q" k="41" />
<hkern u1="&#xc3;" u2="g" k="-43" />
<hkern u1="&#xc3;" u2="f" k="41" />
<hkern u1="&#xc3;" u2="d" k="43" />
<hkern u1="&#xc3;" u2="W" k="190" />
<hkern u1="&#xc3;" u2="V" k="213" />
<hkern u1="&#xc3;" u2="T" k="213" />
<hkern u1="&#xc3;" u2="R" k="-20" />
<hkern u1="&#xc3;" u2="Q" k="43" />
<hkern u1="&#xc3;" u2="J" k="86" />
<hkern u1="&#xc3;" u2="G" k="43" />
<hkern u1="&#xc4;" u2="x" k="-66" />
<hkern u1="&#xc4;" u2="w" k="106" />
<hkern u1="&#xc4;" u2="v" k="106" />
<hkern u1="&#xc4;" u2="t" k="84" />
<hkern u1="&#xc4;" u2="q" k="41" />
<hkern u1="&#xc4;" u2="g" k="-43" />
<hkern u1="&#xc4;" u2="f" k="41" />
<hkern u1="&#xc4;" u2="d" k="43" />
<hkern u1="&#xc4;" u2="W" k="190" />
<hkern u1="&#xc4;" u2="V" k="213" />
<hkern u1="&#xc4;" u2="T" k="213" />
<hkern u1="&#xc4;" u2="R" k="-20" />
<hkern u1="&#xc4;" u2="Q" k="43" />
<hkern u1="&#xc4;" u2="J" k="86" />
<hkern u1="&#xc4;" u2="G" k="43" />
<hkern u1="&#xc5;" u2="x" k="-66" />
<hkern u1="&#xc5;" u2="w" k="106" />
<hkern u1="&#xc5;" u2="v" k="106" />
<hkern u1="&#xc5;" u2="t" k="84" />
<hkern u1="&#xc5;" u2="q" k="41" />
<hkern u1="&#xc5;" u2="g" k="-43" />
<hkern u1="&#xc5;" u2="f" k="41" />
<hkern u1="&#xc5;" u2="d" k="43" />
<hkern u1="&#xc5;" u2="W" k="190" />
<hkern u1="&#xc5;" u2="V" k="213" />
<hkern u1="&#xc5;" u2="T" k="213" />
<hkern u1="&#xc5;" u2="R" k="-20" />
<hkern u1="&#xc5;" u2="Q" k="43" />
<hkern u1="&#xc5;" u2="J" k="86" />
<hkern u1="&#xc5;" u2="G" k="43" />
<hkern u1="&#xc6;" u2="&#x178;" k="-43" />
<hkern u1="&#xc6;" u2="&#xdd;" k="-43" />
<hkern u1="&#xc6;" u2="&#xd1;" k="-43" />
<hkern u1="&#xc6;" u2="x" k="-43" />
<hkern u1="&#xc6;" u2="s" k="-20" />
<hkern u1="&#xc6;" u2="j" k="-84" />
<hkern u1="&#xc6;" u2="Y" k="-43" />
<hkern u1="&#xc6;" u2="W" k="-20" />
<hkern u1="&#xc6;" u2="V" k="-23" />
<hkern u1="&#xc6;" u2="T" k="-84" />
<hkern u1="&#xc6;" u2="S" k="-43" />
<hkern u1="&#xc6;" u2="N" k="-43" />
<hkern u1="&#xc6;" u2="J" k="-23" />
<hkern u1="&#xc7;" u2="w" k="-43" />
<hkern u1="&#xc7;" u2="t" k="-43" />
<hkern u1="&#xc7;" u2="m" k="-43" />
<hkern u1="&#xc7;" u2="h" k="-43" />
<hkern u1="&#xc7;" u2="g" k="-20" />
<hkern u1="&#xc7;" u2="T" k="-43" />
<hkern u1="&#xc7;" u2="R" k="-63" />
<hkern u1="&#xc7;" u2="P" k="-43" />
<hkern u1="&#xc7;" u2="M" k="-43" />
<hkern u1="&#xc7;" u2="K" k="-66" />
<hkern u1="&#xc7;" u2="H" k="-63" />
<hkern u1="&#xc8;" u2="x" k="-43" />
<hkern u1="&#xc8;" u2="j" k="-84" />
<hkern u1="&#xc8;" u2="W" k="-20" />
<hkern u1="&#xc8;" u2="V" k="-23" />
<hkern u1="&#xc8;" u2="T" k="-84" />
<hkern u1="&#xc8;" u2="J" k="-23" />
<hkern u1="&#xc9;" u2="x" k="-43" />
<hkern u1="&#xc9;" u2="j" k="-84" />
<hkern u1="&#xc9;" u2="W" k="-20" />
<hkern u1="&#xc9;" u2="V" k="-23" />
<hkern u1="&#xc9;" u2="T" k="-84" />
<hkern u1="&#xc9;" u2="J" k="-23" />
<hkern u1="&#xca;" u2="x" k="-43" />
<hkern u1="&#xca;" u2="j" k="-84" />
<hkern u1="&#xca;" u2="W" k="-20" />
<hkern u1="&#xca;" u2="V" k="-23" />
<hkern u1="&#xca;" u2="T" k="-84" />
<hkern u1="&#xca;" u2="J" k="-23" />
<hkern u1="&#xcb;" u2="x" k="-43" />
<hkern u1="&#xcb;" u2="j" k="-84" />
<hkern u1="&#xcb;" u2="W" k="-20" />
<hkern u1="&#xcb;" u2="V" k="-23" />
<hkern u1="&#xcb;" u2="T" k="-84" />
<hkern u1="&#xcb;" u2="J" k="-23" />
<hkern u1="&#xcc;" u2="W" k="-43" />
<hkern u1="&#xcc;" u2="T" k="-66" />
<hkern u1="&#xcc;" u2="J" k="-20" />
<hkern u1="&#xcd;" u2="W" k="-43" />
<hkern u1="&#xcd;" u2="T" k="-66" />
<hkern u1="&#xcd;" u2="J" k="-20" />
<hkern u1="&#xce;" u2="W" k="-43" />
<hkern u1="&#xce;" u2="T" k="-66" />
<hkern u1="&#xce;" u2="J" k="-20" />
<hkern u1="&#xcf;" u2="W" k="-43" />
<hkern u1="&#xcf;" u2="T" k="-66" />
<hkern u1="&#xcf;" u2="J" k="-20" />
<hkern u1="&#xd2;" u2="b" k="-20" />
<hkern u1="&#xd2;" u2="X" k="86" />
<hkern u1="&#xd2;" u2="W" k="63" />
<hkern u1="&#xd2;" u2="V" k="66" />
<hkern u1="&#xd2;" u2="T" k="84" />
<hkern u1="&#xd2;" u2="Q" k="-43" />
<hkern u1="&#xd3;" u2="b" k="-20" />
<hkern u1="&#xd3;" u2="X" k="86" />
<hkern u1="&#xd3;" u2="W" k="63" />
<hkern u1="&#xd3;" u2="V" k="66" />
<hkern u1="&#xd3;" u2="T" k="84" />
<hkern u1="&#xd3;" u2="Q" k="-43" />
<hkern u1="&#xd4;" u2="b" k="-20" />
<hkern u1="&#xd4;" u2="X" k="86" />
<hkern u1="&#xd4;" u2="W" k="63" />
<hkern u1="&#xd4;" u2="V" k="66" />
<hkern u1="&#xd4;" u2="T" k="84" />
<hkern u1="&#xd4;" u2="Q" k="-43" />
<hkern u1="&#xd5;" u2="b" k="-20" />
<hkern u1="&#xd5;" u2="X" k="86" />
<hkern u1="&#xd5;" u2="W" k="63" />
<hkern u1="&#xd5;" u2="V" k="66" />
<hkern u1="&#xd5;" u2="T" k="84" />
<hkern u1="&#xd5;" u2="Q" k="-43" />
<hkern u1="&#xd6;" u2="b" k="-20" />
<hkern u1="&#xd6;" u2="X" k="86" />
<hkern u1="&#xd6;" u2="W" k="63" />
<hkern u1="&#xd6;" u2="V" k="66" />
<hkern u1="&#xd6;" u2="T" k="84" />
<hkern u1="&#xd6;" u2="Q" k="-43" />
<hkern u1="&#xd8;" u2="b" k="-20" />
<hkern u1="&#xd8;" u2="X" k="86" />
<hkern u1="&#xd8;" u2="W" k="63" />
<hkern u1="&#xd8;" u2="V" k="66" />
<hkern u1="&#xd8;" u2="T" k="84" />
<hkern u1="&#xd8;" u2="Q" k="-43" />
<hkern u1="&#xd9;" u2="x" k="43" />
<hkern u1="&#xd9;" u2="w" k="23" />
<hkern u1="&#xd9;" u2="v" k="20" />
<hkern u1="&#xd9;" u2="r" k="43" />
<hkern u1="&#xd9;" u2="q" k="43" />
<hkern u1="&#xd9;" u2="p" k="23" />
<hkern u1="&#xd9;" u2="m" k="23" />
<hkern u1="&#xd9;" u2="k" k="20" />
<hkern u1="&#xd9;" u2="h" k="23" />
<hkern u1="&#xd9;" u2="g" k="43" />
<hkern u1="&#xd9;" u2="d" k="43" />
<hkern u1="&#xd9;" u2="X" k="63" />
<hkern u1="&#xd9;" u2="W" k="66" />
<hkern u1="&#xda;" u2="x" k="43" />
<hkern u1="&#xda;" u2="w" k="23" />
<hkern u1="&#xda;" u2="v" k="20" />
<hkern u1="&#xda;" u2="r" k="43" />
<hkern u1="&#xda;" u2="q" k="43" />
<hkern u1="&#xda;" u2="p" k="23" />
<hkern u1="&#xda;" u2="m" k="23" />
<hkern u1="&#xda;" u2="k" k="20" />
<hkern u1="&#xda;" u2="h" k="23" />
<hkern u1="&#xda;" u2="g" k="43" />
<hkern u1="&#xda;" u2="d" k="43" />
<hkern u1="&#xda;" u2="X" k="63" />
<hkern u1="&#xda;" u2="W" k="66" />
<hkern u1="&#xdb;" u2="x" k="43" />
<hkern u1="&#xdb;" u2="w" k="23" />
<hkern u1="&#xdb;" u2="v" k="20" />
<hkern u1="&#xdb;" u2="r" k="43" />
<hkern u1="&#xdb;" u2="q" k="43" />
<hkern u1="&#xdb;" u2="p" k="23" />
<hkern u1="&#xdb;" u2="m" k="23" />
<hkern u1="&#xdb;" u2="k" k="20" />
<hkern u1="&#xdb;" u2="h" k="23" />
<hkern u1="&#xdb;" u2="g" k="43" />
<hkern u1="&#xdb;" u2="d" k="43" />
<hkern u1="&#xdb;" u2="X" k="63" />
<hkern u1="&#xdb;" u2="W" k="66" />
<hkern u1="&#xdc;" u2="x" k="43" />
<hkern u1="&#xdc;" u2="w" k="23" />
<hkern u1="&#xdc;" u2="v" k="20" />
<hkern u1="&#xdc;" u2="r" k="43" />
<hkern u1="&#xdc;" u2="q" k="43" />
<hkern u1="&#xdc;" u2="p" k="23" />
<hkern u1="&#xdc;" u2="m" k="23" />
<hkern u1="&#xdc;" u2="k" k="20" />
<hkern u1="&#xdc;" u2="h" k="23" />
<hkern u1="&#xdc;" u2="g" k="43" />
<hkern u1="&#xdc;" u2="d" k="43" />
<hkern u1="&#xdc;" u2="X" k="63" />
<hkern u1="&#xdc;" u2="W" k="66" />
<hkern u1="&#xdd;" u2="&#x153;" k="236" />
<hkern u1="&#xdd;" u2="&#xff;" k="66" />
<hkern u1="&#xdd;" u2="&#xfd;" k="66" />
<hkern u1="&#xdd;" u2="&#xfc;" k="190" />
<hkern u1="&#xdd;" u2="&#xfb;" k="190" />
<hkern u1="&#xdd;" u2="&#xfa;" k="190" />
<hkern u1="&#xdd;" u2="&#xf9;" k="190" />
<hkern u1="&#xdd;" u2="&#xf8;" k="236" />
<hkern u1="&#xdd;" u2="&#xf6;" k="236" />
<hkern u1="&#xdd;" u2="&#xf5;" k="236" />
<hkern u1="&#xdd;" u2="&#xf4;" k="236" />
<hkern u1="&#xdd;" u2="&#xf3;" k="236" />
<hkern u1="&#xdd;" u2="&#xf2;" k="236" />
<hkern u1="&#xdd;" u2="&#xf1;" k="172" />
<hkern u1="&#xdd;" u2="&#xef;" k="41" />
<hkern u1="&#xdd;" u2="&#xee;" k="41" />
<hkern u1="&#xdd;" u2="&#xed;" k="41" />
<hkern u1="&#xdd;" u2="&#xec;" k="41" />
<hkern u1="&#xdd;" u2="&#xeb;" k="276" />
<hkern u1="&#xdd;" u2="&#xea;" k="276" />
<hkern u1="&#xdd;" u2="&#xe9;" k="276" />
<hkern u1="&#xdd;" u2="&#xe8;" k="276" />
<hkern u1="&#xdd;" u2="&#xe7;" k="236" />
<hkern u1="&#xdd;" u2="&#xe6;" k="256" />
<hkern u1="&#xdd;" u2="&#xe5;" k="256" />
<hkern u1="&#xdd;" u2="&#xe4;" k="256" />
<hkern u1="&#xdd;" u2="&#xe3;" k="256" />
<hkern u1="&#xdd;" u2="&#xe2;" k="256" />
<hkern u1="&#xdd;" u2="&#xe1;" k="256" />
<hkern u1="&#xdd;" u2="&#xe0;" k="256" />
<hkern u1="&#xdd;" u2="z" k="170" />
<hkern u1="&#xdd;" u2="y" k="66" />
<hkern u1="&#xdd;" u2="x" k="127" />
<hkern u1="&#xdd;" u2="w" k="106" />
<hkern u1="&#xdd;" u2="v" k="106" />
<hkern u1="&#xdd;" u2="u" k="190" />
<hkern u1="&#xdd;" u2="s" k="213" />
<hkern u1="&#xdd;" u2="r" k="172" />
<hkern u1="&#xdd;" u2="q" k="236" />
<hkern u1="&#xdd;" u2="p" k="172" />
<hkern u1="&#xdd;" u2="o" k="236" />
<hkern u1="&#xdd;" u2="n" k="172" />
<hkern u1="&#xdd;" u2="m" k="170" />
<hkern u1="&#xdd;" u2="k" k="63" />
<hkern u1="&#xdd;" u2="i" k="41" />
<hkern u1="&#xdd;" u2="h" k="63" />
<hkern u1="&#xdd;" u2="g" k="236" />
<hkern u1="&#xdd;" u2="f" k="63" />
<hkern u1="&#xdd;" u2="e" k="276" />
<hkern u1="&#xdd;" u2="d" k="233" />
<hkern u1="&#xdd;" u2="c" k="236" />
<hkern u1="&#xdd;" u2="a" k="256" />
<hkern u1="&#xde;" u2="w" k="-43" />
<hkern u1="&#xde;" u2="m" k="-43" />
<hkern u1="&#xde;" u2="j" k="-43" />
<hkern u1="&#xde;" u2="h" k="-20" />
<hkern u1="&#xde;" u2="X" k="106" />
<hkern u1="&#xde;" u2="W" k="63" />
<hkern u1="&#xde;" u2="V" k="20" />
<hkern u1="&#xde;" u2="T" k="63" />
<hkern u1="&#xde;" u2="Q" k="-43" />
<hkern u1="&#xde;" u2="F" k="-43" />
<hkern u1="&#xe0;" u2="w" k="66" />
<hkern u1="&#xe0;" u2="v" k="63" />
<hkern u1="&#xe0;" u2="t" k="43" />
<hkern u1="&#xe0;" u2="j" k="-43" />
<hkern u1="&#xe0;" u2="b" k="20" />
<hkern u1="&#xe1;" u2="w" k="66" />
<hkern u1="&#xe1;" u2="v" k="63" />
<hkern u1="&#xe1;" u2="t" k="43" />
<hkern u1="&#xe1;" u2="j" k="-43" />
<hkern u1="&#xe1;" u2="b" k="20" />
<hkern u1="&#xe2;" u2="w" k="66" />
<hkern u1="&#xe2;" u2="v" k="63" />
<hkern u1="&#xe2;" u2="t" k="43" />
<hkern u1="&#xe2;" u2="j" k="-43" />
<hkern u1="&#xe2;" u2="b" k="20" />
<hkern u1="&#xe3;" u2="w" k="66" />
<hkern u1="&#xe3;" u2="v" k="63" />
<hkern u1="&#xe3;" u2="t" k="43" />
<hkern u1="&#xe3;" u2="j" k="-43" />
<hkern u1="&#xe3;" u2="b" k="20" />
<hkern u1="&#xe4;" u2="w" k="66" />
<hkern u1="&#xe4;" u2="v" k="63" />
<hkern u1="&#xe4;" u2="t" k="43" />
<hkern u1="&#xe4;" u2="j" k="-43" />
<hkern u1="&#xe4;" u2="b" k="20" />
<hkern u1="&#xe5;" u2="w" k="66" />
<hkern u1="&#xe5;" u2="v" k="63" />
<hkern u1="&#xe5;" u2="t" k="43" />
<hkern u1="&#xe5;" u2="j" k="-43" />
<hkern u1="&#xe5;" u2="b" k="20" />
<hkern u1="&#xe6;" u2="&#x153;" k="16" />
<hkern u1="&#xe6;" u2="&#xff;" k="33" />
<hkern u1="&#xe6;" u2="&#xfd;" k="33" />
<hkern u1="&#xe6;" u2="&#xfc;" k="20" />
<hkern u1="&#xe6;" u2="&#xfb;" k="20" />
<hkern u1="&#xe6;" u2="&#xfa;" k="20" />
<hkern u1="&#xe6;" u2="&#xf9;" k="20" />
<hkern u1="&#xe6;" u2="&#xf8;" k="16" />
<hkern u1="&#xe6;" u2="&#xf6;" k="16" />
<hkern u1="&#xe6;" u2="&#xf5;" k="16" />
<hkern u1="&#xe6;" u2="&#xf4;" k="16" />
<hkern u1="&#xe6;" u2="&#xf3;" k="16" />
<hkern u1="&#xe6;" u2="&#xf2;" k="16" />
<hkern u1="&#xe6;" u2="&#xf1;" k="31" />
<hkern u1="&#xe6;" u2="&#xef;" k="33" />
<hkern u1="&#xe6;" u2="&#xee;" k="33" />
<hkern u1="&#xe6;" u2="&#xed;" k="33" />
<hkern u1="&#xe6;" u2="&#xec;" k="33" />
<hkern u1="&#xe6;" u2="z" k="47" />
<hkern u1="&#xe6;" u2="y" k="33" />
<hkern u1="&#xe6;" u2="x" k="80" />
<hkern u1="&#xe6;" u2="w" k="16" />
<hkern u1="&#xe6;" u2="v" k="33" />
<hkern u1="&#xe6;" u2="u" k="20" />
<hkern u1="&#xe6;" u2="t" k="47" />
<hkern u1="&#xe6;" u2="s" k="33" />
<hkern u1="&#xe6;" u2="r" k="16" />
<hkern u1="&#xe6;" u2="q" k="20" />
<hkern u1="&#xe6;" u2="p" k="20" />
<hkern u1="&#xe6;" u2="o" k="16" />
<hkern u1="&#xe6;" u2="n" k="31" />
<hkern u1="&#xe6;" u2="m" k="16" />
<hkern u1="&#xe6;" u2="k" k="43" />
<hkern u1="&#xe6;" u2="j" k="47" />
<hkern u1="&#xe6;" u2="i" k="33" />
<hkern u1="&#xe6;" u2="h" k="20" />
<hkern u1="&#xe6;" u2="g" k="47" />
<hkern u1="&#xe6;" u2="f" k="20" />
<hkern u1="&#xe6;" u2="d" k="16" />
<hkern u1="&#xe6;" u2="b" k="16" />
<hkern u1="&#xe7;" u2="q" k="-41" />
<hkern u1="&#xe7;" u2="g" k="-23" />
<hkern u1="&#xe8;" u2="x" k="20" />
<hkern u1="&#xe8;" u2="w" k="41" />
<hkern u1="&#xe8;" u2="v" k="23" />
<hkern u1="&#xe8;" u2="t" k="43" />
<hkern u1="&#xe8;" u2="m" k="20" />
<hkern u1="&#xe8;" u2="k" k="20" />
<hkern u1="&#xe8;" u2="h" k="20" />
<hkern u1="&#xe9;" u2="x" k="20" />
<hkern u1="&#xe9;" u2="w" k="41" />
<hkern u1="&#xe9;" u2="v" k="23" />
<hkern u1="&#xe9;" u2="t" k="43" />
<hkern u1="&#xe9;" u2="m" k="20" />
<hkern u1="&#xe9;" u2="k" k="20" />
<hkern u1="&#xe9;" u2="h" k="20" />
<hkern u1="&#xea;" u2="x" k="20" />
<hkern u1="&#xea;" u2="w" k="41" />
<hkern u1="&#xea;" u2="v" k="23" />
<hkern u1="&#xea;" u2="t" k="43" />
<hkern u1="&#xea;" u2="m" k="20" />
<hkern u1="&#xea;" u2="k" k="20" />
<hkern u1="&#xea;" u2="h" k="20" />
<hkern u1="&#xeb;" u2="x" k="20" />
<hkern u1="&#xeb;" u2="w" k="41" />
<hkern u1="&#xeb;" u2="v" k="23" />
<hkern u1="&#xeb;" u2="t" k="43" />
<hkern u1="&#xeb;" u2="m" k="20" />
<hkern u1="&#xeb;" u2="k" k="20" />
<hkern u1="&#xeb;" u2="h" k="20" />
<hkern u1="&#xec;" u2="x" k="-43" />
<hkern u1="&#xec;" u2="J" k="-86" />
<hkern u1="&#xed;" u2="x" k="-43" />
<hkern u1="&#xed;" u2="J" k="-86" />
<hkern u1="&#xee;" u2="x" k="-43" />
<hkern u1="&#xee;" u2="J" k="-86" />
<hkern u1="&#xef;" u2="x" k="-43" />
<hkern u1="&#xef;" u2="J" k="-86" />
<hkern u1="&#xf1;" u2="w" k="43" />
<hkern u1="&#xf1;" u2="v" k="63" />
<hkern u1="&#xf1;" u2="t" k="43" />
<hkern u1="&#xf2;" u2="x" k="20" />
<hkern u1="&#xf2;" u2="w" k="43" />
<hkern u1="&#xf2;" u2="v" k="20" />
<hkern u1="&#xf2;" u2="t" k="20" />
<hkern u1="&#xf3;" u2="x" k="20" />
<hkern u1="&#xf3;" u2="w" k="43" />
<hkern u1="&#xf3;" u2="v" k="20" />
<hkern u1="&#xf3;" u2="t" k="20" />
<hkern u1="&#xf4;" u2="x" k="20" />
<hkern u1="&#xf4;" u2="w" k="43" />
<hkern u1="&#xf4;" u2="v" k="20" />
<hkern u1="&#xf4;" u2="t" k="20" />
<hkern u1="&#xf5;" u2="x" k="20" />
<hkern u1="&#xf5;" u2="w" k="43" />
<hkern u1="&#xf5;" u2="v" k="20" />
<hkern u1="&#xf5;" u2="t" k="20" />
<hkern u1="&#xf6;" u2="x" k="20" />
<hkern u1="&#xf6;" u2="w" k="43" />
<hkern u1="&#xf6;" u2="v" k="20" />
<hkern u1="&#xf6;" u2="t" k="20" />
<hkern u1="&#xf8;" u2="x" k="20" />
<hkern u1="&#xf8;" u2="w" k="43" />
<hkern u1="&#xf8;" u2="v" k="20" />
<hkern u1="&#xf8;" u2="t" k="20" />
<hkern u1="&#x152;" u2="&#x178;" k="-43" />
<hkern u1="&#x152;" u2="&#xdd;" k="-43" />
<hkern u1="&#x152;" u2="&#xd1;" k="-43" />
<hkern u1="&#x152;" u2="x" k="-43" />
<hkern u1="&#x152;" u2="s" k="-20" />
<hkern u1="&#x152;" u2="j" k="-84" />
<hkern u1="&#x152;" u2="Y" k="-43" />
<hkern u1="&#x152;" u2="W" k="-20" />
<hkern u1="&#x152;" u2="V" k="-23" />
<hkern u1="&#x152;" u2="T" k="-84" />
<hkern u1="&#x152;" u2="S" k="-43" />
<hkern u1="&#x152;" u2="N" k="-43" />
<hkern u1="&#x152;" u2="J" k="-23" />
<hkern u1="&#x153;" u2="&#xff;" k="43" />
<hkern u1="&#x153;" u2="&#xfd;" k="43" />
<hkern u1="&#x153;" u2="y" k="43" />
<hkern u1="&#x153;" u2="x" k="20" />
<hkern u1="&#x153;" u2="w" k="41" />
<hkern u1="&#x153;" u2="v" k="23" />
<hkern u1="&#x153;" u2="t" k="43" />
<hkern u1="&#x153;" u2="m" k="20" />
<hkern u1="&#x153;" u2="k" k="20" />
<hkern u1="&#x153;" u2="h" k="20" />
<hkern u1="&#x178;" u2="x" k="127" />
<hkern u1="&#x178;" u2="w" k="106" />
<hkern u1="&#x178;" u2="v" k="106" />
<hkern u1="&#x178;" u2="r" k="172" />
<hkern u1="&#x178;" u2="q" k="236" />
<hkern u1="&#x178;" u2="p" k="172" />
<hkern u1="&#x178;" u2="m" k="170" />
<hkern u1="&#x178;" u2="k" k="63" />
<hkern u1="&#x178;" u2="h" k="63" />
<hkern u1="&#x178;" u2="g" k="236" />
<hkern u1="&#x178;" u2="f" k="63" />
<hkern u1="&#x178;" u2="d" k="233" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="-63" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="-23" />
<hkern g1="C,Ccedilla" 	g2="D,Thorn" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="L" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-63" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="l" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-23" />
<hkern g1="C,Ccedilla" 	g2="n,ntilde" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-63" />
<hkern g1="C,Ccedilla" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="-66" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="-86" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="N,Ntilde" 	k="-43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="-43" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-43" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="Z" 	k="-43" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="S" 	k="-41" />
<hkern g1="N,Ntilde" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="N,Ntilde" 	g2="S" 	k="-23" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-23" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="43" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="S" 	k="-41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="c,ccedilla" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="63" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="43" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="23" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="n,ntilde" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="256" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="213" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="190" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="276" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="236" />
<hkern g1="S" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-66" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-43" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="S" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-66" />
<hkern g1="S" 	g2="l" 	k="-20" />
<hkern g1="S" 	g2="n,ntilde" 	k="-20" />
<hkern g1="S" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-41" />
<hkern g1="S" 	g2="z" 	k="-41" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-43" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-20" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="43" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="z" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="c,ccedilla" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="D,Thorn" 	k="-106" />
<hkern g1="c,ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-41" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="43" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-23" />
<hkern g1="n,ntilde" 	g2="y,yacute,ydieresis" 	k="63" />
<hkern g1="n,ntilde" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="43" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-41" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="84" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="84" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="256" />
<hkern g1="L" 	g2="Z" 	k="-43" />
<hkern g1="l" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="D,Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="D,Thorn" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="D,Thorn" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-43" />
<hkern g1="D,Thorn" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="D,Thorn" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="D,Thorn" 	g2="Z" 	k="43" />
<hkern g1="D,Thorn" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-43" />
<hkern g1="D,Thorn" 	g2="n,ntilde" 	k="-23" />
<hkern g1="D,Thorn" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="D,Thorn" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="-23" />
<hkern g1="p,thorn" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
</font>
</defs></svg> 