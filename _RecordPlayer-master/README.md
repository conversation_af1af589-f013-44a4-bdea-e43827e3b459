# Interactive Record Player

An experimental template with an interactive old school record player powered by the [Web Audio API](https://www.w3.org/TR/webaudio/).

[Article on Codrops](http://tympanus.net/codrops/?p=27200)

[Demo](http://tympanus.net/Development/RecordPlayer/)

## License

Integrate or build upon it for free in your personal or commercial projects. Don't republish, redistribute or sell "as-is". 

Read more here: [License](http://tympanus.net/codrops/licensing/)

## Credits

*   Acoustic impulse response data from [OpenAIR: The Open Acoustic Impulse Response Library](http://www.openairlib.net/) licensed under [Attribution Share Alike Creative Commons license](http://creativecommons.org/licenses/by-sa/3.0/)
*   [Abbey Load](http://stuartmemo.com/abbey-load/) by [<PERSON>](http://stuartmemo.com/) licensed under [MIT](https://opensource.org/licenses/MIT)
*   Audio visualization made with [Visualizations with Web Audio API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API/Visualizations_with_Web_Audio_API) and [Voice-change-o-matic](https://github.com/mdn/voice-change-o-matic/blob/gh-pages/scripts/app.js#L123-L167)
*   Animations powered by Michael Villar's [Dynamics.js](http://dynamicsjs.com/)
*   All remixes by [Ivan Chew](http://ccmixter.org/people/ramblinglibrarian/profile) © 2016 Licensed under a Creative Commons Attribution (3.0) license [CC BY 3.0](https://creativecommons.org/licenses/by/3.0/). More free remixes: [http://dig.ccmixter.org/free](http://dig.ccmixter.org/free)
*   [Vinyl NoiseSFX](http://logic-pro-expert.com/logic-pro-blog/2014/08/18/free-vinyl-noise-samples-99sounds.html#.V1Lfu5MrLfY) by Chia: All the sounds included in Vinyl Noise SFX have been recorded and edited by Chia. These samples are the property of Chia and they are hosted on 99Sounds with his permission. You are hereby granted a license to use these samples for both non-commercial and commercial purposes, provided that the samples are not used as part of another sample library or a virtual instrument.
*   Records icon made by [Madebyoliver](http://www.flaticon.com/authors/madebyoliver "Madebyoliver") from [www.flaticon.com](http://www.flaticon.com "Flaticon"), licensed under [CC 3.0 BY](http://creativecommons.org/licenses/by/3.0/ "Creative Commons BY 3.0")
*   Stadium, cathedral, guitar icons made by [Freepik](http://www.flaticon.com/authors/freepik "Freepik") from [www.flaticon.com](http://www.flaticon.com "Flaticon"), licensed under [CC 3.0 BY](http://creativecommons.org/licenses/by/3.0/ "Creative Commons BY 3.0")
*   [Getting Started with Web Audio API](http://www.html5rocks.com/en/tutorials/webaudio/intro/) by Boris Smus on HTML5Rocks

## Misc

Follow Codrops: [Twitter](http://www.twitter.com/codrops), [Facebook](http://www.facebook.com/pages/Codrops/159107397912), [Google+](https://plus.google.com/101095823814290637419), [GitHub](https://github.com/codrops), [Pinterest](http://www.pinterest.com/codrops/)

[© Codrops 2016](http://www.codrops.com)





