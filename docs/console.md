mix-styles:48 🛡️ Service Worker protection installed
mix-styles:21 Found 0 service worker registrations to remove
mix-styles:39 Found 0 caches to clear
mix-styles:56  GET https://api.tempolabs.ai/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js net::ERR_CONNECTION_REFUSED
client:495 [vite] connecting...
client:618 [vite] connected.
chunk-6RFYUUFA.js?v=8e078108:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
client.ts:11 API settings: {API_BASE_URL: 'http://localhost:8000', API_VERSION: '/api/v1', API_URL: 'http://localhost:8000', baseURL: '/api/v1', origin: 'https://localhost:5173', …}
tone.js?v=8e078108:9657  * Tone.js v15.1.22 * 
EnhancedToneAudioEngine.ts:93 [EnhancedToneAudioEngine] Initialized with advanced effects chain
WaveSurferVisualization.ts:35 [WaveSurferVisualization] Initialized
mix-styles:88 Removing error message element: <script>​…​</script>​
mix-styles:107 Cleaned error messages, refreshing data...
App.tsx:131 Current auth token: test-token-for-development
AIProvider.tsx:120 AIProvider Warning: UserPreferencesProvider might not be properly set up.
(anonymous) @ AIProvider.tsx:120
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
commitPassiveMountOnFiber @ chunk-6RFYUUFA.js?v=8e078108:18156
commitPassiveMountEffects_complete @ chunk-6RFYUUFA.js?v=8e078108:18129
commitPassiveMountEffects_begin @ chunk-6RFYUUFA.js?v=8e078108:18119
commitPassiveMountEffects @ chunk-6RFYUUFA.js?v=8e078108:18109
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19490
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:19328
workLoop @ chunk-6RFYUUFA.js?v=8e078108:197
flushWork @ chunk-6RFYUUFA.js?v=8e078108:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js?v=8e078108:384
AIProvider.tsx:125 To fix this warning, ensure UserPreferencesProvider is a parent of AIProvider in the component tree:<UserPreferencesProvider>  <AIProvider>    {/* Your components */}  </AIProvider></UserPreferencesProvider>
react-router-dom.js?v=8e078108:4394 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=8e078108:4394
logDeprecation @ react-router-dom.js?v=8e078108:4397
logV6DeprecationWarnings @ react-router-dom.js?v=8e078108:4400
(anonymous) @ react-router-dom.js?v=8e078108:5272
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
commitPassiveMountOnFiber @ chunk-6RFYUUFA.js?v=8e078108:18156
commitPassiveMountEffects_complete @ chunk-6RFYUUFA.js?v=8e078108:18129
commitPassiveMountEffects_begin @ chunk-6RFYUUFA.js?v=8e078108:18119
commitPassiveMountEffects @ chunk-6RFYUUFA.js?v=8e078108:18109
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19490
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:19328
workLoop @ chunk-6RFYUUFA.js?v=8e078108:197
flushWork @ chunk-6RFYUUFA.js?v=8e078108:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js?v=8e078108:384
react-router-dom.js?v=8e078108:4394 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=8e078108:4394
logDeprecation @ react-router-dom.js?v=8e078108:4397
logV6DeprecationWarnings @ react-router-dom.js?v=8e078108:4403
(anonymous) @ react-router-dom.js?v=8e078108:5272
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
commitPassiveMountOnFiber @ chunk-6RFYUUFA.js?v=8e078108:18156
commitPassiveMountEffects_complete @ chunk-6RFYUUFA.js?v=8e078108:18129
commitPassiveMountEffects_begin @ chunk-6RFYUUFA.js?v=8e078108:18119
commitPassiveMountEffects @ chunk-6RFYUUFA.js?v=8e078108:18109
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19490
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:19328
workLoop @ chunk-6RFYUUFA.js?v=8e078108:197
flushWork @ chunk-6RFYUUFA.js?v=8e078108:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js?v=8e078108:384
App.tsx:131 Current auth token: test-token-for-development
AIProvider.tsx:120 AIProvider Warning: UserPreferencesProvider might not be properly set up.
(anonymous) @ AIProvider.tsx:120
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
invokePassiveEffectMountInDEV @ chunk-6RFYUUFA.js?v=8e078108:18324
invokeEffectsInDev @ chunk-6RFYUUFA.js?v=8e078108:19701
commitDoubleInvokeEffectsInDEV @ chunk-6RFYUUFA.js?v=8e078108:19686
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19503
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:19328
workLoop @ chunk-6RFYUUFA.js?v=8e078108:197
flushWork @ chunk-6RFYUUFA.js?v=8e078108:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js?v=8e078108:384
AIProvider.tsx:125 To fix this warning, ensure UserPreferencesProvider is a parent of AIProvider in the component tree:<UserPreferencesProvider>  <AIProvider>    {/* Your components */}  </AIProvider></UserPreferencesProvider>
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /ai/mcp/status
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /ai/mcp/status
client.ts:84 Making API request to: /ai/mcp/status
client.ts:89 API Request details: {url: '/ai/mcp/status', method: 'get', params: 'none'}
client.ts:84 Making API request to: /ai/mcp/status
client.ts:89 API Request details: {url: '/ai/mcp/status', method: 'get', params: 'none'}
inject.js:22 pageshow:  PageTransitionEvent {isTrusted: true, persisted: false, type: 'pageshow', target: document, currentTarget: Window, …}
mix-styles:1 Unchecked runtime.lastError: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
mix-styles:1 Unchecked runtime.lastError: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
Home.tsx:48 Home component - Authentication state: {isAuthenticated: false, user: 'No user'}
Home.tsx:48 Home component - Authentication state: {isAuthenticated: false, user: 'No user'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: mix-styles
client.ts:84 Making API request to: /mix-styles
client.ts:89 API Request details: {url: '/mix-styles', method: 'get', params: Array(2)}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/1
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/2
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/3
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/4
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/5
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/6
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/7
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/8
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/9
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/10
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/11
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/12
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/13
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/14
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/15
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/16
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/17
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /style-documentation/18
client.ts:84 Making API request to: /style-documentation/1
client.ts:89 API Request details: {url: '/style-documentation/1', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/2
client.ts:89 API Request details: {url: '/style-documentation/2', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/3
client.ts:89 API Request details: {url: '/style-documentation/3', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/4
client.ts:89 API Request details: {url: '/style-documentation/4', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/5
client.ts:89 API Request details: {url: '/style-documentation/5', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/6
client.ts:89 API Request details: {url: '/style-documentation/6', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/7
client.ts:89 API Request details: {url: '/style-documentation/7', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/8
client.ts:89 API Request details: {url: '/style-documentation/8', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/9
client.ts:89 API Request details: {url: '/style-documentation/9', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/10
client.ts:89 API Request details: {url: '/style-documentation/10', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/11
client.ts:89 API Request details: {url: '/style-documentation/11', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/12
client.ts:89 API Request details: {url: '/style-documentation/12', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/13
client.ts:89 API Request details: {url: '/style-documentation/13', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/14
client.ts:89 API Request details: {url: '/style-documentation/14', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/15
client.ts:89 API Request details: {url: '/style-documentation/15', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/16
client.ts:89 API Request details: {url: '/style-documentation/16', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/17
client.ts:89 API Request details: {url: '/style-documentation/17', method: 'get', params: 'none'}
client.ts:84 Making API request to: /style-documentation/18
client.ts:89 API Request details: {url: '/style-documentation/18', method: 'get', params: 'none'}
ProtectedRoute.tsx:26 Loading timeout reached, forcing render
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:86 Starting collections fetch
collections.ts:34 Requesting collections from: /collections
playlists.ts:28 Requesting playlists from: /playlists
hooks.ts:273 folderTracks changed: []
hooks.ts:275 No folder tracks available, skipping API call
hooks.ts:273 folderTracks changed: []
hooks.ts:275 No folder tracks available, skipping API call
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /playlists
client.ts:84 Making API request to: /collections
client.ts:89 API Request details: {url: '/collections', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists
client.ts:89 API Request details: {url: '/playlists', method: 'get', params: 'none'}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 0, isLoading: true, errorMessage: undefined}
playlists.ts:40 Backend returned an array of playlists, transforming to expected format
collections.ts:38 Collection API raw response: {status: 200, headers: AxiosHeaders, data: '{"collections":[{"name":"NEW IV","collection_type"…5da98e720fb3f2f18eea18","track_count":21,"crea...'}
collections.ts:51 Collections response shape: {hasCollectionsArray: true, collectionsType: 'array', collectionsLength: 9, hasTotalCount: true, hasActiveCount: true}
useCollections.ts:88 Backend response type: object
useCollections.ts:89 Backend response structure: (3) ['collections', 'total_count', 'active_count']
useCollections.ts:100 Using collections array format with 9 items
useCollections.ts:37 Mapping collection object: {name: 'NEW IV', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW IV', is_active: true, id: 'fc069ace275da98e720fb3f2f18eea18', …}
useCollections.ts:37 Mapping collection object: {name: 'TECH', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/TECH', is_active: true, id: 'e7dc083254ae1365f1b6b4b1fddd3a88', …}
useCollections.ts:37 Mapping collection object: {name: 'NORMAL META', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NORMAL META', is_active: true, id: '0aebf3e1d01938879931f68f997959ab', …}
useCollections.ts:37 Mapping collection object: {name: 'TEST', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/TEST', is_active: true, id: 'cc86bd68d516f392add193017e8b3776', …}
useCollections.ts:37 Mapping collection object: {name: 'WEIRD', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD', is_active: true, id: '8129b5031bf5396be75b88b36db8f556', …}
useCollections.ts:37 Mapping collection object: {name: 'NEW VI', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW VI', is_active: true, id: '78b2dc7752df315389e753dcc674db20', …}
useCollections.ts:37 Mapping collection object: {name: 'NEW V', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V', is_active: true, id: '29237db64e4cb7b9518401efe48be509', …}
useCollections.ts:37 Mapping collection object: {name: 'NEW VII', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII', is_active: true, id: '78bba0932f8d58459c07fd308ad534fe', …}
useCollections.ts:37 Mapping collection object: {name: 'NEW', collection_type: 'directory', path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW', is_active: true, id: 'bdf248c64712c276703596837010b800', …}
useCollections.ts:111 Mapped collections: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
ProtectedRoute.tsx:40 Development mode: bypassing authentication check
Home.tsx:48 Home component - Authentication state: {isAuthenticated: false, user: 'No user'}
Home.tsx:48 Home component - Authentication state: {isAuthenticated: false, user: 'No user'}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
hooks.ts:273 folderTracks changed: []
hooks.ts:275 No folder tracks available, skipping API call
hooks.ts:273 folderTracks changed: []
hooks.ts:275 No folder tracks available, skipping API call
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useEnhancedCollectionData.ts:124 Source selected: all-collections
collections.ts:378 Fetching tracks for collection fc069ace275da98e720fb3f2f18eea18, page 1, limit 1000
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/fc069ace275da98e720fb3f2f18eea18/tracks
 Making API request to: /collections/fc069ace275da98e720fb3f2f18eea18/tracks
 API Request details: {url: '/collections/fc069ace275da98e720fb3f2f18eea18/tracks', method: 'get', params: Array(2)}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 Select is changing from uncontrolled to controlled. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.
(anonymous) @ chunk-24BRHPTI.js:42
commitHookEffectListMount @ chunk-6RFYUUFA.js:16915
commitPassiveMountOnFiber @ chunk-6RFYUUFA.js:18156
commitPassiveMountEffects_complete @ chunk-6RFYUUFA.js:18129
commitPassiveMountEffects_begin @ chunk-6RFYUUFA.js:18119
commitPassiveMountEffects @ chunk-6RFYUUFA.js:18109
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js:19490
flushPassiveEffects @ chunk-6RFYUUFA.js:19447
performSyncWorkOnRoot @ chunk-6RFYUUFA.js:18868
flushSyncCallbacks @ chunk-6RFYUUFA.js:9119
commitRootImpl @ chunk-6RFYUUFA.js:19432
commitRoot @ chunk-6RFYUUFA.js:19277
finishConcurrentRender @ chunk-6RFYUUFA.js:18805
performConcurrentWorkOnRoot @ chunk-6RFYUUFA.js:18718
workLoop @ chunk-6RFYUUFA.js:197
flushWork @ chunk-6RFYUUFA.js:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js:384
collections.ts:383 Received 21 tracks, total: 21
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection e7dc083254ae1365f1b6b4b1fddd3a88, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks
client.ts:84 Making API request to: /collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks
client.ts:89 API Request details: {url: '/collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 24 tracks, total: 24
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection 0aebf3e1d01938879931f68f997959ab, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/0aebf3e1d01938879931f68f997959ab/tracks
client.ts:84 Making API request to: /collections/0aebf3e1d01938879931f68f997959ab/tracks
client.ts:89 API Request details: {url: '/collections/0aebf3e1d01938879931f68f997959ab/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 5 tracks, total: 5
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection cc86bd68d516f392add193017e8b3776, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/cc86bd68d516f392add193017e8b3776/tracks
client.ts:84 Making API request to: /collections/cc86bd68d516f392add193017e8b3776/tracks
client.ts:89 API Request details: {url: '/collections/cc86bd68d516f392add193017e8b3776/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 2 tracks, total: 2
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection 8129b5031bf5396be75b88b36db8f556, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 159 tracks, total: 159
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection 78b2dc7752df315389e753dcc674db20, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
client.ts:84 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
client.ts:89 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 32 tracks, total: 32
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection 29237db64e4cb7b9518401efe48be509, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 159 tracks, total: 159
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection 78bba0932f8d58459c07fd308ad534fe, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:84 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:89 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 56 tracks, total: 56
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
collections.ts:378 Fetching tracks for collection bdf248c64712c276703596837010b800, page 1, limit 1000
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: Array(2)}
collections.ts:383 Received 160 tracks, total: 160
collections.ts:384 Page 1 of 1
collections.ts:390 Mixed in Key data present: Yes
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
chunk-24BRHPTI.js?v=8e078108:42 Select is changing from uncontrolled to controlled. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.
(anonymous) @ chunk-24BRHPTI.js?v=8e078108:42
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
commitPassiveMountOnFiber @ chunk-6RFYUUFA.js?v=8e078108:18156
commitPassiveMountEffects_complete @ chunk-6RFYUUFA.js?v=8e078108:18129
commitPassiveMountEffects_begin @ chunk-6RFYUUFA.js?v=8e078108:18119
commitPassiveMountEffects @ chunk-6RFYUUFA.js?v=8e078108:18109
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19490
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
performSyncWorkOnRoot @ chunk-6RFYUUFA.js?v=8e078108:18868
flushSyncCallbacks @ chunk-6RFYUUFA.js?v=8e078108:9119
commitRootImpl @ chunk-6RFYUUFA.js?v=8e078108:19432
commitRoot @ chunk-6RFYUUFA.js?v=8e078108:19277
finishConcurrentRender @ chunk-6RFYUUFA.js?v=8e078108:18805
performConcurrentWorkOnRoot @ chunk-6RFYUUFA.js?v=8e078108:18718
workLoop @ chunk-6RFYUUFA.js?v=8e078108:197
flushWork @ chunk-6RFYUUFA.js?v=8e078108:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js?v=8e078108:384
hooks.ts:273 folderTracks changed: (618) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, …]
hooks.ts:281 Fetching mix styles for smart generator
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: mix-styles
client.ts:84 Making API request to: /mix-styles
client.ts:89 API Request details: {url: '/mix-styles', method: 'get', params: Array(2)}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
hooks.ts:283 Fetched all mix styles: (18) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
hooks.ts:287 Sending track IDs to API: (618) [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, …]
hooks.ts:289 Calling getMixStyleCompatibility with trackIds: (618) [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, …]
mixAnalytics.ts:158 getMixStyleCompatibility called with trackIds: (618) [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, …]
mixAnalytics.ts:162 Sending payload to /api/v1/mix-styles/compatibility: {track_ids: Array(618)}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /api/v1/mix-styles/compatibility
client.ts:84 Making API request to: /mix-styles/compatibility
client.ts:89 API Request details: {url: '/mix-styles/compatibility', method: 'post', params: 'none'}
mixAnalytics.ts:164 API response from styles/compatibility: {data: Array(5), status: 200, statusText: 'OK', headers: AxiosHeaders, config: {…}, …}
hooks.ts:291 Received mix style compatibility scores: (5) [{…}, {…}, {…}, {…}, {…}]
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
CardStyleSelector.tsx:52 Mix style selected: 5
MiddlePanel.tsx:24 Mix style selected: 5
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
useCollections.ts:168 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 🔍 [Smart Mix V2] Selection validation: {selectedCollectionId: 'all-collections', selectedSources: Array(0), hasSelection: 'all-collections', filteredTracksCount: 618}
 Generating mix with form data: {mixStyle: '5', trackCount: 10, firstTrackOption: 'random', firstTrack: undefined, powerBlockSize: 0, …}
 Form data submitted: {mixStyle: '5', trackCount: 10, firstTrackOption: 'random', firstTrack: undefined, powerBlockSize: 0, …}
 🔍 [Smart Mix V2] DEBUG - Collection and Track Info:
 Selected Collection ID: all-collections
 Selected Folder ID: ALL
 Filtered Tracks Count: 618
 First 3 Filtered Tracks: (3) [{…}, {…}, {…}]
 🚀 [Smart Mix V2] Calling API to generate mix with payload: {tracks: Array(618), mixStyle: '5', trackCount: 10, firstTrack: null, powerBlockSize: 0, …}
 🚀 [Smart Mix V2] API params: {}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /analysis/generate-mix
 Making API request to: /analysis/generate-mix
 API Request details: {url: '/analysis/generate-mix', method: 'post', params: 'none'}
 ✅ [Smart Mix V2] Mix generation successful, received data: {mix: Array(10), stats: {…}, status: 'success'}
 🔍 [Smart Mix V2] DEBUG - Returned Track Analysis:
 Total tracks returned: 10
 First 3 returned tracks: (3) [{…}, {…}, {…}]
 Processing track 1: {id: 58, title: '5 - Cormac - Perfect Time', transition_type: undefined, transitionType: undefined, transition_style_class: undefined, …}
 Processing track 2: {id: 255, title: '6 - Japanese Limo Boys - Maji Maji', transition_type: 'perfect', transitionType: undefined, transition_style_class: 'perfect-mix', …}
 Processing track 3: {id: 286, title: '6 - Kubebe - Reggae Zuzu', transition_type: 'diagonal_mix', transitionType: undefined, transition_style_class: 'diagonal-mix', …}
 Processing track 4: {id: 410, title: '5 - Ambient Babestation Meltdown; Borai - Braindance', transition_type: 'energy_drop', transitionType: undefined, transition_style_class: 'energy-drop-mix', …}
 Processing track 5: {id: 390, title: '5 - Lasser Drakar - Dream Basss', transition_type: 'energy_boost', transitionType: undefined, transition_style_class: 'energy-boost-mix', …}
 Processing track 6: {id: 205, title: '6 - Richard Rossa - Funk Tahini', transition_type: 'perfect', transitionType: undefined, transition_style_class: 'perfect-mix', …}
 Processing track 7: {id: 135, title: '5 - Bufi - La Cumbia De Las Frecuencias', transition_type: 'energy_boost', transitionType: undefined, transition_style_class: 'energy-boost-mix', …}
 Processing track 8: {id: 341, title: '4 - Concret; Chelsea Hines - In The Air', transition_type: 'energy_drop', transitionType: undefined, transition_style_class: 'energy-drop-mix', …}
 Processing track 9: {id: 270, title: '6 - IntiNahual - Kunyaza', transition_type: 'energy_drop', transitionType: undefined, transition_style_class: 'energy-drop-mix', …}
 Processing track 10: {id: 318, title: '6 - Fred Berthet; Jessica Baucher - Sève et Adam', transition_type: 'energy_drop', transitionType: undefined, transition_style_class: 'energy-drop-mix', …}
 Processed mix data with transition info: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: diagonal_mix (diagonal-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: diagonal_mix (diagonal-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: diagonal_mix (diagonal-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: diagonal_mix (diagonal-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: perfect (perfect-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: perfect (perfect-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: perfect (perfect-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: perfect (perfect-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_boost (energy-boost-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
trackDisplayUtils.ts:71 Using backend transition data: energy_drop (energy-drop-mix)
trackDisplayUtils.ts:135 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
trackDisplayUtils.ts:181 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
useSmartMixV2State.ts:316 🎯 [Smart Mix V2] Raw API tracks received: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
useSmartMixV2State.ts:319 🔍 [Smart Mix V2] DEBUG - Track Analysis Before Timeline Transformation:
useSmartMixV2State.ts:321 Track 1: {id: 58, title: '5 - Cormac - Perfect Time', artist: '6B', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3', …}
useSmartMixV2State.ts:321 Track 2: {id: 255, title: '6 - Japanese Limo Boys - Maji Maji', artist: '6B', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac', …}
useSmartMixV2State.ts:321 Track 3: {id: 286, title: '6 - Kubebe - Reggae Zuzu', artist: '7A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac', …}
useSmartMixV2State.ts:321 Track 4: {id: 410, title: '5 - Ambient Babestation Meltdown; Borai - Braindance', artist: '6A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII/…ent Babestation Meltdown; Borai - Braindance.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII/…ent Babestation Meltdown; Borai - Braindance.flac', …}
useSmartMixV2State.ts:321 Track 5: {id: 390, title: '5 - Lasser Drakar - Dream Basss', artist: '7A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 5 - Lasser Drakar - Dream Basss.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 5 - Lasser Drakar - Dream Basss.flac', …}
 Track 6: {id: 205, title: '6 - Richard Rossa - Funk Tahini', artist: '7A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/7A - 6 - Richard Rossa - Funk Tahini.mp3', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/7A - 6 - Richard Rossa - Funk Tahini.mp3', …}
 Track 7: {id: 135, title: '5 - Bufi - La Cumbia De Las Frecuencias', artist: '8A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/8A - 5 - Bufi - La Cumbia De Las Frecuencias.mp3', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/8A - 5 - Bufi - La Cumbia De Las Frecuencias.mp3', …}
 Track 8: {id: 341, title: '4 - Concret; Chelsea Hines - In The Air', artist: '7A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 4 - Concret; Chelsea Hines - In The Air.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 4 - Concret; Chelsea Hines - In The Air.flac', …}
 Track 9: {id: 270, title: '6 - IntiNahual - Kunyaza', artist: '6A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6A - 6 - IntiNahual - Kunyaza.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6A - 6 - IntiNahual - Kunyaza.flac', …}
 Track 10: {id: 318, title: '6 - Fred Berthet; Jessica Baucher - Sève et Adam', artist: '5A', filePath: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/5A…red Berthet; Jessica Baucher - Sève et Adam.flac', file_path: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/5A…red Berthet; Jessica Baucher - Sève et Adam.flac', …}
 ✅ [Smart Mix V2] Successfully imported processTrack and color calculation functions
 [SmartMixGeneratorV2] Using file_path for track 58: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 58: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 1 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 1: #00FF00 (key: 6B)
 ✅ [SmartMixGeneratorV2] Transformed track 1/10: {id: 58, title: '5 - Cormac - Perfect Time', artist: '6B', duration: 257, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3', …}
 [SmartMixGeneratorV2] Using file_path for track 255: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 255: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 2 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 2: #00FF00 (key: 6B)
 ✅ [SmartMixGeneratorV2] Transformed track 2/10: {id: 255, title: '6 - Japanese Limo Boys - Maji Maji', artist: '6B', duration: 274, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac', …}
 [SmartMixGeneratorV2] Using file_path for track 286: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 286: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 3 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 3: #8000FF (key: 7A)
 ✅ [SmartMixGeneratorV2] Transformed track 3/10: {id: 286, title: '6 - Kubebe - Reggae Zuzu', artist: '7A', duration: 255, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac', …}
 [SmartMixGeneratorV2] Using file_path for track 410: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII/6A - 5 - Ambient Babestation Meltdown; Borai - Braindance.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 410: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII/6A - 5 - Ambient Babestation Meltdown; Borai - Braindance.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 4 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 4: #0080FF (key: 6A)
 ✅ [SmartMixGeneratorV2] Transformed track 4/10: {id: 410, title: '5 - Ambient Babestation Meltdown; Borai - Braindance', artist: '6A', duration: 204, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII/…ent Babestation Meltdown; Borai - Braindance.flac', …}
 [SmartMixGeneratorV2] Using file_path for track 390: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 5 - Lasser Drakar - Dream Basss.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 390: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 5 - Lasser Drakar - Dream Basss.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 5 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 5: #FF8000 (key: 7A)
 ✅ [SmartMixGeneratorV2] Transformed track 5/10: {id: 390, title: '5 - Lasser Drakar - Dream Basss', artist: '7A', duration: 71, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 5 - Lasser Drakar - Dream Basss.flac', …}
 [SmartMixGeneratorV2] Using file_path for track 205: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/7A - 6 - Richard Rossa - Funk Tahini.mp3
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 205: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/7A - 6 - Richard Rossa - Funk Tahini.mp3
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 6 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 6: #00FF00 (key: 7A)
 ✅ [SmartMixGeneratorV2] Transformed track 6/10: {id: 205, title: '6 - Richard Rossa - Funk Tahini', artist: '7A', duration: 480, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/7A - 6 - Richard Rossa - Funk Tahini.mp3', …}
 [SmartMixGeneratorV2] Using file_path for track 135: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/8A - 5 - Bufi - La Cumbia De Las Frecuencias.mp3
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 135: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/8A - 5 - Bufi - La Cumbia De Las Frecuencias.mp3
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 7 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 7: #FF8000 (key: 8A)
 ✅ [SmartMixGeneratorV2] Transformed track 7/10: {id: 135, title: '5 - Bufi - La Cumbia De Las Frecuencias', artist: '8A', duration: 290, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/8A - 5 - Bufi - La Cumbia De Las Frecuencias.mp3', …}
 [SmartMixGeneratorV2] Using file_path for track 341: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 4 - Concret; Chelsea Hines - In The Air.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 341: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 4 - Concret; Chelsea Hines - In The Air.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 8 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 8: #0080FF (key: 7A)
 ✅ [SmartMixGeneratorV2] Transformed track 8/10: {id: 341, title: '4 - Concret; Chelsea Hines - In The Air', artist: '7A', duration: 237, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 4 - Concret; Chelsea Hines - In The Air.flac', …}
 [SmartMixGeneratorV2] Using file_path for track 270: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6A - 6 - IntiNahual - Kunyaza.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 270: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6A - 6 - IntiNahual - Kunyaza.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 9 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 9: #0080FF (key: 6A)
 ✅ [SmartMixGeneratorV2] Transformed track 9/10: {id: 270, title: '6 - IntiNahual - Kunyaza', artist: '6A', duration: 381, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6A - 6 - IntiNahual - Kunyaza.flac', …}
 [SmartMixGeneratorV2] Using file_path for track 318: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/5A - 6 - Fred Berthet; Jessica Baucher - Sève et Adam.flac
 ✅ [SmartMixGeneratorV2] Audio URL resolved for track 318: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/5A - 6 - Fred Berthet; Jessica Baucher - Sève et Adam.flac
 🧪 [CRITICAL TEST] Skipping processTrack for Smart Mix track 10 to test if processing is the issue
 🎨 [SmartMixGeneratorV2] Pre-calculated harmonic color for track 10: #0080FF (key: 5A)
 ✅ [SmartMixGeneratorV2] Transformed track 10/10: {id: 318, title: '6 - Fred Berthet; Jessica Baucher - Sève et Adam', artist: '5A', duration: 373, audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/5A…red Berthet; Jessica Baucher - Sève et Adam.flac', …}
 🎯 [Smart Mix V2] Final timeline tracks: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 🔍 [Smart Mix V2] DEBUG - Final Track Analysis for Timeline:
 Total tracks being sent to timeline: 10
 📊 [Smart Mix V2] Validation Summary: {totalTracks: 10, tracksWithValidIds: 10, tracksWithValidTitles: 10, tracksWithValidAudioUrls: 10, tracksWithColors: 10, …}
 Final Track 1: {id: 58, title: '5 - Cormac - Perfect Time', artist: '6B', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3', hasValidAudioUrl: true, …}
 Final Track 2: {id: 255, title: '6 - Japanese Limo Boys - Maji Maji', artist: '6B', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac', hasValidAudioUrl: true, …}
 Final Track 3: {id: 286, title: '6 - Kubebe - Reggae Zuzu', artist: '7A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac', hasValidAudioUrl: true, …}
 Final Track 4: {id: 410, title: '5 - Ambient Babestation Meltdown; Borai - Braindance', artist: '6A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW VII/…ent Babestation Meltdown; Borai - Braindance.flac', hasValidAudioUrl: true, …}
 Final Track 5: {id: 390, title: '5 - Lasser Drakar - Dream Basss', artist: '7A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 5 - Lasser Drakar - Dream Basss.flac', hasValidAudioUrl: true, …}
 Final Track 6: {id: 205, title: '6 - Richard Rossa - Funk Tahini', artist: '7A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/7A - 6 - Richard Rossa - Funk Tahini.mp3', hasValidAudioUrl: true, …}
 Final Track 7: {id: 135, title: '5 - Bufi - La Cumbia De Las Frecuencias', artist: '8A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/8A - 5 - Bufi - La Cumbia De Las Frecuencias.mp3', hasValidAudioUrl: true, …}
 Final Track 8: {id: 341, title: '4 - Concret; Chelsea Hines - In The Air', artist: '7A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 4 - Concret; Chelsea Hines - In The Air.flac', hasValidAudioUrl: true, …}
 Final Track 9: {id: 270, title: '6 - IntiNahual - Kunyaza', artist: '6A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6A - 6 - IntiNahual - Kunyaza.flac', hasValidAudioUrl: true, …}
 Final Track 10: {id: 318, title: '6 - Fred Berthet; Jessica Baucher - Sève et Adam', artist: '5A', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/5A…red Berthet; Jessica Baucher - Sève et Adam.flac', hasValidAudioUrl: true, …}
 ✅ [Smart Mix V2] All tracks passed final validation, proceeding to timeline
 🎯 [SEQUENTIAL] [SmartMixGeneratorV2Page] Starting sequential track addition for 10 tracks
 🎯 [SEQUENTIAL] Starting to add 10 tracks one by one
 [TimelineStore] DEBUG: setTracks called with 0 tracks
 🔍 [TimelineStore] Incoming tracks validation:
 📊 [TimelineStore] Incoming validation summary: {totalTracks: 0, tracksWithValidIds: 0, tracksWithValidTitles: 0, tracksWithValidAudioUrls: 0, tracksWithColors: 0, …}
 [TimelineStore] DEBUG: Tracks unchanged, skipping update
 🎯 [SEQUENTIAL] Adding track 1/10: 5 - Cormac - Perfect Time
 SEQUENTIAL_SMART_MIX_TRACK: Adding track 58 (5 - Cormac - Perfect Time) - AudioUrl: /Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3
 🚀🚀🚀 [TimelineStore] ADDING TRACK: 58 - 5 - Cormac - Perfect Time 🚀🚀🚀
 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (200ms) - Audio engine: false, Waveform: false
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 useCollections hook state: {hasData: true, dataLength: 9, isLoading: false, errorMessage: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Compatibility scores for track 58: {overall: 0.5, harmony: 0.5, energy: 0.5, genre: 0.4, hasBackendScores: false, …}
 Energy flow for track 58: {current: 50, previous: undefined, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: undefined}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 255: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 255: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: diagonal_mix (diagonal-mix)
 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: diagonal_mix (diagonal-mix)
 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: diagonal_mix (diagonal-mix)
 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: diagonal_mix (diagonal-mix)
 Compatibility scores for track 286: {overall: 0.8, harmony: 0.8, energy: 0.8, genre: 0.6400000000000001, hasBackendScores: false, …}
 Energy flow for track 286: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 410: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 410: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 390: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 390: {current: 50, previous: 50, trend: 'steady', rawEnergy: 5, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: perfect (perfect-mix)
 Compatibility scores for track 205: {overall: 1, harmony: 1, energy: 1, genre: 0.8, hasBackendScores: false, …}
 Energy flow for track 205: {current: 60, previous: 50, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 5}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_boost (energy-boost-mix)
 Compatibility scores for track 135: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 135: {current: 50, previous: 60, trend: 'falling', rawEnergy: 5, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 341: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 341: {current: 40, previous: 50, trend: 'falling', rawEnergy: 4, rawPreviousEnergy: 5}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 270: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 270: {current: 60, previous: 40, trend: 'rising', rawEnergy: 6, rawPreviousEnergy: 4}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 Using backend transition data: energy_drop (energy-drop-mix)
 Compatibility scores for track 318: {overall: 0.85, harmony: 0.85, energy: 0.85, genre: 0.68, hasBackendScores: false, …}
 Energy flow for track 318: {current: 60, previous: 60, trend: 'steady', rawEnergy: 6, rawPreviousEnergy: 6}
 [HorizontalTimelineMain] Timeline dimensions: 0x120 (1 tracks × 120px)
 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
 [HorizontalTimelineMain] Timeline dimensions: 0x120 (1 tracks × 120px)
 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
 Requesting collections from: /collections
 Requesting playlists from: /playlists
 Requesting collections from: /collections
 Requesting playlists from: /playlists
 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 58 - 5 - Cormac - Perfect Time 🔥🔥🔥
 🔍 [HorizontalTrackLane] TRACK DATA: {id: 58, title: '5 - Cormac - Perfect Time', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3', color: '#00FF00', hasAudioUrl: true, …}
 [HorizontalTrackLane] Starting mount effect for track 58 (5 - Cormac - Perfect Time)
 [HorizontalTrackLane] Loading track 58 (5 - Cormac - Perfect Time)
 [TimelinePerformanceMonitor] Started monitoring
 [HorizontalTimelineMain] Performance monitoring started
 [HorizontalTimelinePage] Initializing horizontal timeline
 [HorizontalTimelinePage] Initializing audio engine
 [HorizontalTrackLane] Unmounting track 58
 [HorizontalTrackLane] StrictMode cleanup or track ID change - skipping unload for track 58
 [TimelinePerformanceMonitor] Stopped monitoring
 [HorizontalTimelineMain] Performance monitoring stopped
 [HorizontalTimelinePage] Cleaning up horizontal timeline
 Requesting collections from: /collections
 Requesting playlists from: /playlists
 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 58 - 5 - Cormac - Perfect Time 🔥🔥🔥
 🔍 [HorizontalTrackLane] TRACK DATA: {id: 58, title: '5 - Cormac - Perfect Time', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/WEIRD/6B - 5 - Cormac - Perfect Time.mp3', color: '#00FF00', hasAudioUrl: true, …}
 [HorizontalTrackLane] Starting mount effect for track 58 (5 - Cormac - Perfect Time)
 [HorizontalTrackLane] Loading track 58 (5 - Cormac - Perfect Time)
 [HorizontalTimelinePage] Initializing horizontal timeline
 [HorizontalTimelinePage] Initializing audio engine
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /settings
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /settings
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /settings
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /tracks
 Making API request to: /collections
 API Request details: {url: '/collections', method: 'get', params: 'none'}
 Making API request to: /playlists
 API Request details: {url: '/playlists', method: 'get', params: 'none'}
 Making API request to: /settings
 API Request details: {url: '/settings', method: 'get', params: 'none'}
 Making API request to: /collections
 API Request details: {url: '/collections', method: 'get', params: 'none'}
 Making API request to: /playlists
 API Request details: {url: '/playlists', method: 'get', params: 'none'}
 Making API request to: /settings
 API Request details: {url: '/settings', method: 'get', params: 'none'}
 Making API request to: /tracks
 API Request details: {url: '/tracks', method: 'get', params: Array(0)}
 [AudioOptimizer] Applying professional audio optimizations...
 [AudioOptimizer] Calculated optimal buffer size: 32768 (Memory: 8GB, Cores: 10)
 [AudioOptimizer] Using buffer size: 32768
 [AudioOptimizer] AudioWorklet available - using low-latency configuration
 [AudioOptimizer] Professional audio optimizations applied successfully
 Making API request to: /collections
 API Request details: {url: '/collections', method: 'get', params: 'none'}
 Making API request to: /playlists
 API Request details: {url: '/playlists', method: 'get', params: 'none'}
 Making API request to: /settings
 API Request details: {url: '/settings', method: 'get', params: 'none'}
 Making API request to: /tracks
 API Request details: {url: '/tracks', method: 'get', params: Array(0)}
 [AudioOptimizer] Audio context already optimized
 [AudioOptimizer] Performance monitoring started
 [TimelinePerformanceMonitor] Started monitoring
 [EnhancedToneAudioEngine] Audio context started with comprehensive optimizations
 [AudioOptimizer] Performance monitoring started
 [EnhancedToneAudioEngine] Audio context started with comprehensive optimizations
 [TimelineCoordinatorEnhanced] Initialized
 [TimelineCoordinatorEnhanced] Initialized
 [HorizontalTimelinePage] Performance monitoring started
 [HorizontalTimelinePage] Exposed timeline systems to window for testing
 [HorizontalTimelinePage] Exposed timeline systems to window for testing
 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x120 (1 tracks × 120px)
 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x120 (1 tracks × 120px)
 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
 [HorizontalTimelinePage] Cleaning up horizontal timeline
 [TimelinePerformanceMonitor] Stopped monitoring
 [HorizontalTimelinePage] Performance monitoring stopped
 [HorizontalTimelinePage] Already initialized, skipping
 Collection API raw response: {status: 200, headers: AxiosHeaders, data: '{"collections":[{"name":"NEW IV","collection_type"…5da98e720fb3f2f18eea18","track_count":21,"crea...'}
 Collections response shape: {hasCollectionsArray: true, collectionsType: 'array', collectionsLength: 9, hasTotalCount: true, hasActiveCount: true}
 Collections response: {collections: Array(9), total_count: 9, active_count: 9}
 Extracted collections: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 Backend returned an array of playlists, transforming to expected format
 Backend returned an array of playlists, transforming to expected format
 Collection API raw response: {status: 200, headers: AxiosHeaders, data: '{"collections":[{"name":"NEW IV","collection_type"…5da98e720fb3f2f18eea18","track_count":21,"crea...'}
 Collections response shape: {hasCollectionsArray: true, collectionsType: 'array', collectionsLength: 9, hasTotalCount: true, hasActiveCount: true}
 Collections response: {collections: Array(9), total_count: 9, active_count: 9}
 Extracted collections: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/fc069ace275da98e720fb3f2f18eea18/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/0aebf3e1d01938879931f68f997959ab/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/cc86bd68d516f392add193017e8b3776/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/2/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/fc069ace275da98e720fb3f2f18eea18/tracks
 API Request details: {url: '/collections/fc069ace275da98e720fb3f2f18eea18/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks
 API Request details: {url: '/collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/0aebf3e1d01938879931f68f997959ab/tracks
 API Request details: {url: '/collections/0aebf3e1d01938879931f68f997959ab/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/cc86bd68d516f392add193017e8b3776/tracks
 API Request details: {url: '/collections/cc86bd68d516f392add193017e8b3776/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/2/tracks
 API Request details: {url: '/playlists/2/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 [HorizontalTrackLane] Container not ready for track 58, retrying...
(anonymous) @ HorizontalTrackLane.tsx:227
setTimeout
(anonymous) @ HorizontalTrackLane.tsx:222
commitHookEffectListMount @ chunk-6RFYUUFA.js:16915
invokePassiveEffectMountInDEV @ chunk-6RFYUUFA.js:18324
invokeEffectsInDev @ chunk-6RFYUUFA.js:19701
commitDoubleInvokeEffectsInDEV @ chunk-6RFYUUFA.js:19686
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js:19503
flushPassiveEffects @ chunk-6RFYUUFA.js:19447
performSyncWorkOnRoot @ chunk-6RFYUUFA.js:18868
flushSyncCallbacks @ chunk-6RFYUUFA.js:9119
commitRootImpl @ chunk-6RFYUUFA.js:19432
commitRoot @ chunk-6RFYUUFA.js:19277
finishConcurrentRender @ chunk-6RFYUUFA.js:18805
performConcurrentWorkOnRoot @ chunk-6RFYUUFA.js:18718
workLoop @ chunk-6RFYUUFA.js:197
flushWork @ chunk-6RFYUUFA.js:176
performWorkUntilDeadline @ chunk-6RFYUUFA.js:384
 Backend returned an array of playlists, transforming to expected format
 Collection API raw response: {status: 200, headers: AxiosHeaders, data: '{"collections":[{"name":"NEW IV","collection_type"…5da98e720fb3f2f18eea18","track_count":21,"crea...'}
 Collections response shape: {hasCollectionsArray: true, collectionsType: 'array', collectionsLength: 9, hasTotalCount: true, hasActiveCount: true}
 Collections response: {collections: Array(9), total_count: 9, active_count: 9}
 Extracted collections: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/fc069ace275da98e720fb3f2f18eea18/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/0aebf3e1d01938879931f68f997959ab/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/cc86bd68d516f392add193017e8b3776/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/2/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/fc069ace275da98e720fb3f2f18eea18/tracks
 API Request details: {url: '/collections/fc069ace275da98e720fb3f2f18eea18/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks
 API Request details: {url: '/collections/e7dc083254ae1365f1b6b4b1fddd3a88/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/0aebf3e1d01938879931f68f997959ab/tracks
 API Request details: {url: '/collections/0aebf3e1d01938879931f68f997959ab/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/cc86bd68d516f392add193017e8b3776/tracks
 API Request details: {url: '/collections/cc86bd68d516f392add193017e8b3776/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/2/tracks
 API Request details: {url: '/playlists/2/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/0aebf3e1d01938879931f68f997959ab/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/cc86bd68d516f392add193017e8b3776/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/2/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/0aebf3e1d01938879931f68f997959ab/tracks
 API Request details: {url: '/collections/0aebf3e1d01938879931f68f997959ab/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/cc86bd68d516f392add193017e8b3776/tracks
 API Request details: {url: '/collections/cc86bd68d516f392add193017e8b3776/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/2/tracks
 API Request details: {url: '/playlists/2/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/cc86bd68d516f392add193017e8b3776/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/2/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/cc86bd68d516f392add193017e8b3776/tracks
 API Request details: {url: '/collections/cc86bd68d516f392add193017e8b3776/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/2/tracks
 API Request details: {url: '/playlists/2/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/2/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/2/tracks
 API Request details: {url: '/playlists/2/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (400ms) - Audio engine: false, Waveform: false
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78b2dc7752df315389e753dcc674db20/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78b2dc7752df315389e753dcc674db20/tracks
 API Request details: {url: '/collections/78b2dc7752df315389e753dcc674db20/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/4/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/8/tracks
 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/4/tracks
 API Request details: {url: '/playlists/4/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/6/tracks
 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/7/tracks
 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
 Making API request to: /playlists/8/tracks
 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/6/tracks
 Development mode: Setting test auth token in request interceptor
 Adding test auth token to request: /playlists/7/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /playlists/8/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:89 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists/6/tracks
client.ts:89 API Request details: {url: '/playlists/6/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists/7/tracks
client.ts:89 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists/8/tracks
client.ts:89 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /playlists/7/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /playlists/8/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:89 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists/7/tracks
client.ts:89 API Request details: {url: '/playlists/7/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists/8/tracks
client.ts:89 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /playlists/8/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:89 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /playlists/8/tracks
client.ts:89 API Request details: {url: '/playlists/8/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/78bba0932f8d58459c07fd308ad534fe/tracks
client.ts:89 API Request details: {url: '/collections/78bba0932f8d58459c07fd308ad534fe/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x120 (1 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x120 (1 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelinePage.tsx:96 [HorizontalTimelinePage] Cleaning up horizontal timeline
HorizontalTimelinePage.tsx:101 [HorizontalTimelinePage] Performance monitoring stopped
HorizontalTimelinePage.tsx:42 [HorizontalTimelinePage] Already initialized, skipping
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:84 Making API request to: /collections/8129b5031bf5396be75b88b36db8f556/tracks
client.ts:89 API Request details: {url: '/collections/8129b5031bf5396be75b88b36db8f556/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:84 Making API request to: /collections/bdf248c64712c276703596837010b800/tracks
client.ts:89 API Request details: {url: '/collections/bdf248c64712c276703596837010b800/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
client.ts:119 Development mode: Setting test auth token in request interceptor
client.ts:127 Adding test auth token to request: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:84 Making API request to: /collections/29237db64e4cb7b9518401efe48be509/tracks
client.ts:89 API Request details: {url: '/collections/29237db64e4cb7b9518401efe48be509/tracks', method: 'get', params: 'none'}
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (1000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (1200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (1400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (1600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (1800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (2000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (2200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (2400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (2600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (2800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (3000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (3200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (3400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (3600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (3800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (4000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (4200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (4400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (4600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (4800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (5000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (5200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (5400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (5600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (5800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (6000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (6200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (6400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (6600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (6800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (7000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (7200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (7400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (7600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (7800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (8000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (8200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (8400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (8600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (8800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (9000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (9200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (9400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (9600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (9800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (10000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (10200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (10400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (10600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (10800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (11000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (11200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (11400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (11600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (11800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (12000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (12200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (12400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (12600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (12800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (13000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (13200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (13400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (13600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (13800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (14000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (14200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (14400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (14600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (14800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 58 to load... (15000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:106 ⚠️ [SEQUENTIAL] Track 58 loading timeout after 15000ms - Audio engine: false, Waveform: false
checkLoaded @ SmartMixGeneratorV2Page.tsx:106
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
SmartMixGeneratorV2Page.tsx:40 ✅ [SEQUENTIAL] Track 1/10 added and loaded successfully
SmartMixGeneratorV2Page.tsx:26 🎯 [SEQUENTIAL] Adding track 2/10: 6 - Japanese Limo Boys - Maji Maji
SmartMixGeneratorV2Page.tsx:29 SEQUENTIAL_SMART_MIX_TRACK: Adding track 255 (6 - Japanese Limo Boys - Maji Maji) - AudioUrl: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac
TimelineStore.tsx:272 🚀🚀🚀 [TimelineStore] ADDING TRACK: 255 - 6 - Japanese Limo Boys - Maji Maji 🚀🚀🚀
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (200ms) - Audio engine: false, Waveform: false
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x240 (2 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 255 (6 - Japanese Limo Boys - Maji Maji) positioned at top: 120px (index: 1)
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x240 (2 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 255 (6 - Japanese Limo Boys - Maji Maji) positioned at top: 120px (index: 1)
HorizontalTrackLane.tsx:78 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 255 - 6 - Japanese Limo Boys - Maji Maji 🔥🔥🔥
HorizontalTrackLane.tsx:79 🔍 [HorizontalTrackLane] TRACK DATA: {id: 255, title: '6 - Japanese Limo Boys - Maji Maji', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac', color: '#00FF00', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:122 [HorizontalTrackLane] Starting mount effect for track 255 (6 - Japanese Limo Boys - Maji Maji)
HorizontalTrackLane.tsx:169 [HorizontalTrackLane] Loading track 255 (6 - Japanese Limo Boys - Maji Maji)
HorizontalTrackLane.tsx:285 [HorizontalTrackLane] Unmounting track 255
HorizontalTrackLane.tsx:301 [HorizontalTrackLane] StrictMode cleanup or track ID change - skipping unload for track 255
HorizontalTrackLane.tsx:78 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 255 - 6 - Japanese Limo Boys - Maji Maji 🔥🔥🔥
HorizontalTrackLane.tsx:79 🔍 [HorizontalTrackLane] TRACK DATA: {id: 255, title: '6 - Japanese Limo Boys - Maji Maji', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/6B - 6 - Japanese Limo Boys - Maji Maji.flac', color: '#00FF00', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:122 [HorizontalTrackLane] Starting mount effect for track 255 (6 - Japanese Limo Boys - Maji Maji)
HorizontalTrackLane.tsx:169 [HorizontalTrackLane] Loading track 255 (6 - Japanese Limo Boys - Maji Maji)
HorizontalTrackLane.tsx:272 [HorizontalTrackLane] Container not ready for track 255, retrying...
(anonymous) @ HorizontalTrackLane.tsx:272
setTimeout
(anonymous) @ HorizontalTrackLane.tsx:267
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
invokePassiveEffectMountInDEV @ chunk-6RFYUUFA.js?v=8e078108:18324
invokeEffectsInDev @ chunk-6RFYUUFA.js?v=8e078108:19701
commitDoubleInvokeEffectsInDEV @ chunk-6RFYUUFA.js?v=8e078108:19686
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19503
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
commitRootImpl @ chunk-6RFYUUFA.js?v=8e078108:19416
commitRoot @ chunk-6RFYUUFA.js?v=8e078108:19277
performSyncWorkOnRoot @ chunk-6RFYUUFA.js?v=8e078108:18895
flushSyncCallbacks @ chunk-6RFYUUFA.js?v=8e078108:9119
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:18627
setTimeout
(anonymous) @ SmartMixGeneratorV2Page.tsx:38
addTracksSequentially @ SmartMixGeneratorV2Page.tsx:38
await in addTracksSequentially
handleGenerateMix @ SmartMixGeneratorV2Page.tsx:127
handleFinish @ useSmartMixV2State.ts:565
await in handleFinish
handleOpenInTimelineClick @ SmartMixGeneratorV2.tsx:78
onClick @ SmartMixGeneratorV2Page.tsx:212
callCallback2 @ chunk-6RFYUUFA.js?v=8e078108:3674
invokeGuardedCallbackDev @ chunk-6RFYUUFA.js?v=8e078108:3699
invokeGuardedCallback @ chunk-6RFYUUFA.js?v=8e078108:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-6RFYUUFA.js?v=8e078108:3736
executeDispatch @ chunk-6RFYUUFA.js?v=8e078108:7014
processDispatchQueueItemsInOrder @ chunk-6RFYUUFA.js?v=8e078108:7034
processDispatchQueue @ chunk-6RFYUUFA.js?v=8e078108:7043
dispatchEventsForPlugins @ chunk-6RFYUUFA.js?v=8e078108:7051
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:7174
batchedUpdates$1 @ chunk-6RFYUUFA.js?v=8e078108:18913
batchedUpdates @ chunk-6RFYUUFA.js?v=8e078108:3579
dispatchEventForPluginEventSystem @ chunk-6RFYUUFA.js?v=8e078108:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-6RFYUUFA.js?v=8e078108:5478
dispatchEvent @ chunk-6RFYUUFA.js?v=8e078108:5472
dispatchDiscreteEvent @ chunk-6RFYUUFA.js?v=8e078108:5449
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (1000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (1200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (1400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (1600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (1800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (2000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (2200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (2400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (2600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (2800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (3000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (3200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (3400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (3600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (3800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (4000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (4200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (4400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (4600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (4800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (5000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (5200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (5400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (5600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (5800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (6000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (6200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (6400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (6600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (6800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (7000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (7200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (7400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (7600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (7800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (8000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (8200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (8400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (8600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (8800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (9000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (9200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (9400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (9600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (9800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (10000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (10200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (10400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (10600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (10800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (11000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (11200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (11400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (11600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (11800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (12000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (12200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (12400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (12600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (12800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (13000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (13200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (13400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (13600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (13800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (14000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (14200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (14400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (14600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (14800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 255 to load... (15000ms) - Audio engine: false, Waveform: false
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x240 (2 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 255 (6 - Japanese Limo Boys - Maji Maji) positioned at top: 120px (index: 1)
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x240 (2 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 255 (6 - Japanese Limo Boys - Maji Maji) positioned at top: 120px (index: 1)
HorizontalTimelinePage.tsx:96 [HorizontalTimelinePage] Cleaning up horizontal timeline
HorizontalTimelinePage.tsx:101 [HorizontalTimelinePage] Performance monitoring stopped
HorizontalTimelinePage.tsx:42 [HorizontalTimelinePage] Already initialized, skipping
SmartMixGeneratorV2Page.tsx:106 ⚠️ [SEQUENTIAL] Track 255 loading timeout after 15000ms - Audio engine: false, Waveform: false
checkLoaded @ SmartMixGeneratorV2Page.tsx:106
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
setTimeout
checkLoaded @ SmartMixGeneratorV2Page.tsx:111
SmartMixGeneratorV2Page.tsx:40 ✅ [SEQUENTIAL] Track 2/10 added and loaded successfully
SmartMixGeneratorV2Page.tsx:26 🎯 [SEQUENTIAL] Adding track 3/10: 6 - Kubebe - Reggae Zuzu
SmartMixGeneratorV2Page.tsx:29 SEQUENTIAL_SMART_MIX_TRACK: Adding track 286 (6 - Kubebe - Reggae Zuzu) - AudioUrl: /Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac
TimelineStore.tsx:272 🚀🚀🚀 [TimelineStore] ADDING TRACK: 286 - 6 - Kubebe - Reggae Zuzu 🚀🚀🚀
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (200ms) - Audio engine: false, Waveform: false
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x360 (3 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 255 (6 - Japanese Limo Boys - Maji Maji) positioned at top: 120px (index: 1)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 286 (6 - Kubebe - Reggae Zuzu) positioned at top: 240px (index: 2)
HorizontalTimelineMain.tsx:73 [HorizontalTimelineMain] Timeline dimensions: 1019.296875x360 (3 tracks × 120px)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 58 (5 - Cormac - Perfect Time) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 255 (6 - Japanese Limo Boys - Maji Maji) positioned at top: 120px (index: 1)
HorizontalTimelineMain.tsx:244 [HorizontalTimelineMain] Track 286 (6 - Kubebe - Reggae Zuzu) positioned at top: 240px (index: 2)
HorizontalTrackLane.tsx:78 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 286 - 6 - Kubebe - Reggae Zuzu 🔥🔥🔥
HorizontalTrackLane.tsx:79 🔍 [HorizontalTrackLane] TRACK DATA: {id: 286, title: '6 - Kubebe - Reggae Zuzu', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac', color: '#8000FF', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:122 [HorizontalTrackLane] Starting mount effect for track 286 (6 - Kubebe - Reggae Zuzu)
HorizontalTrackLane.tsx:169 [HorizontalTrackLane] Loading track 286 (6 - Kubebe - Reggae Zuzu)
HorizontalTrackLane.tsx:285 [HorizontalTrackLane] Unmounting track 286
HorizontalTrackLane.tsx:301 [HorizontalTrackLane] StrictMode cleanup or track ID change - skipping unload for track 286
HorizontalTrackLane.tsx:78 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 286 - 6 - Kubebe - Reggae Zuzu 🔥🔥🔥
HorizontalTrackLane.tsx:79 🔍 [HorizontalTrackLane] TRACK DATA: {id: 286, title: '6 - Kubebe - Reggae Zuzu', audioUrl: '/Users/<USER>/Documents/DENON DJ LIBRARY/NEW V/7A - 6 - Kubebe - Reggae Zuzu.flac', color: '#8000FF', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:122 [HorizontalTrackLane] Starting mount effect for track 286 (6 - Kubebe - Reggae Zuzu)
HorizontalTrackLane.tsx:169 [HorizontalTrackLane] Loading track 286 (6 - Kubebe - Reggae Zuzu)
HorizontalTrackLane.tsx:272 [HorizontalTrackLane] Container not ready for track 286, retrying...
(anonymous) @ HorizontalTrackLane.tsx:272
setTimeout
(anonymous) @ HorizontalTrackLane.tsx:267
commitHookEffectListMount @ chunk-6RFYUUFA.js?v=8e078108:16915
invokePassiveEffectMountInDEV @ chunk-6RFYUUFA.js?v=8e078108:18324
invokeEffectsInDev @ chunk-6RFYUUFA.js?v=8e078108:19701
commitDoubleInvokeEffectsInDEV @ chunk-6RFYUUFA.js?v=8e078108:19686
flushPassiveEffectsImpl @ chunk-6RFYUUFA.js?v=8e078108:19503
flushPassiveEffects @ chunk-6RFYUUFA.js?v=8e078108:19447
commitRootImpl @ chunk-6RFYUUFA.js?v=8e078108:19416
commitRoot @ chunk-6RFYUUFA.js?v=8e078108:19277
performSyncWorkOnRoot @ chunk-6RFYUUFA.js?v=8e078108:18895
flushSyncCallbacks @ chunk-6RFYUUFA.js?v=8e078108:9119
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:18627
setTimeout
(anonymous) @ SmartMixGeneratorV2Page.tsx:38
addTracksSequentially @ SmartMixGeneratorV2Page.tsx:38
await in addTracksSequentially
handleGenerateMix @ SmartMixGeneratorV2Page.tsx:127
handleFinish @ useSmartMixV2State.ts:565
await in handleFinish
handleOpenInTimelineClick @ SmartMixGeneratorV2.tsx:78
onClick @ SmartMixGeneratorV2Page.tsx:212
callCallback2 @ chunk-6RFYUUFA.js?v=8e078108:3674
invokeGuardedCallbackDev @ chunk-6RFYUUFA.js?v=8e078108:3699
invokeGuardedCallback @ chunk-6RFYUUFA.js?v=8e078108:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-6RFYUUFA.js?v=8e078108:3736
executeDispatch @ chunk-6RFYUUFA.js?v=8e078108:7014
processDispatchQueueItemsInOrder @ chunk-6RFYUUFA.js?v=8e078108:7034
processDispatchQueue @ chunk-6RFYUUFA.js?v=8e078108:7043
dispatchEventsForPlugins @ chunk-6RFYUUFA.js?v=8e078108:7051
(anonymous) @ chunk-6RFYUUFA.js?v=8e078108:7174
batchedUpdates$1 @ chunk-6RFYUUFA.js?v=8e078108:18913
batchedUpdates @ chunk-6RFYUUFA.js?v=8e078108:3579
dispatchEventForPluginEventSystem @ chunk-6RFYUUFA.js?v=8e078108:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-6RFYUUFA.js?v=8e078108:5478
dispatchEvent @ chunk-6RFYUUFA.js?v=8e078108:5472
dispatchDiscreteEvent @ chunk-6RFYUUFA.js?v=8e078108:5449
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (1000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (1200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (1400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (1600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (1800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (2000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (2200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (2400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (2600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (2800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (3000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (3200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (3400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (3600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (3800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (4000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (4200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (4400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (4600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (4800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (5000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (5200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (5400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (5600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (5800ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (6000ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (6200ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (6400ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (6600ms) - Audio engine: false, Waveform: false
SmartMixGeneratorV2Page.tsx:110 ⏳ [SEQUENTIAL] Waiting for track 286 to load... (6800ms) - Audio engine: false, Waveform: false
