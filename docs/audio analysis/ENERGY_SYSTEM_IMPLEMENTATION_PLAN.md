# Energy System - Current State & Implementation Plan

**Status Legend**: ✅ Implemented | 🚧 Partial | ❌ Not Implemented | 📋 Planned

## Executive Summary

This document explains the current energy system implementation and plans for enhancement. The system currently uses simple numeric energy values (1-10 scale) with plans to expand to multi-dimensional energy profiling.

## Current Implementation ✅

### Database Schema (Fully Implemented)
- **`Track.energy`**: Integer (1-10) - Active energy value used throughout app
- **`Track.mixed_in_key_energy`**: Integer (4-7) - Energy from Mixed in Key metadata
- **`Track.librosa_energy`**: Integer (1-10) - Energy calculated by librosa analysis
- **`Track.energy_source`**: String - Tracks source: "Mixed in Key", "Librosa", "Default"
- **`TrackAnalysis.analysis_data`**: JSON - Stores detailed analysis results

### Current Energy Calculation ✅

**Simple Librosa Energy** (`_analyze_energy_levels()` method):
```python
# Current implementation in audio_analyzer.py
rms = librosa.feature.rms(y=y)[0]
centroid = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
onset_env = librosa.onset.onset_strength(y=y, sr=sr)

# Combine features (0.5 RMS + 0.3 centroid + 0.2 onset)
energy_raw = 0.5 * rms_norm + 0.3 * centroid_norm + 0.2 * onset_norm
energy_level = int(np.clip(np.mean(energy_raw) * 10, 1, 10))
```

### Current Energy Flow ✅
```
Mixed in Key metadata → Track.mixed_in_key_energy (4-7 scale)
                    ↓
                Track.energy (priority: Mixed in Key > Librosa > Default=5)
                    ↓
            UI displays: "Energy 6" with source attribution
                    ↓
        Mix generation uses Track.energy for transitions
```

### Current Usage in App ✅

**Mix Generation** (`mix_service.py`):
- Energy transitions: Prefers smooth energy changes between tracks
- Energy scoring: `energy_score = max(0, 10 - energy_diff)`
- Used in track selection algorithms

**UI Components**:
- Track details panels show energy with source
- Collection browser displays energy badges
- Energy editor for mix style creation (`DraggableEnergyEditor.tsx`)

## Enhanced Energy System 🚧

### Current Enhanced Implementation (Partially Done)

**Multi-Dimensional Energy Profile** (`_analyze_energy_profile()` method):
- ✅ **Method exists** in `audio_analyzer.py`
- ✅ **Arousal calculation** implemented (`_calculate_arousal_enhanced()`)
- ✅ **Valence calculation** implemented (`_calculate_valence_enhanced()`)
- ✅ **Texture analysis** implemented (`_analyze_texture_advanced()`)
- ✅ **Musical characteristics** implemented (`_analyze_musical_characteristics()`)
- ✅ **Perceptual features** implemented (`_analyze_perceptual_features()`)
- ❌ **Not called** from main analysis pipeline
- ❌ **Not stored** in database
- ❌ **Not displayed** in UI

### Enhanced Energy Profile Structure 📋
```json
{
  "energy_profile": {
    "arousal": 0.8,           // Calm/Energetic (-1 to +1)
    "valence": 0.6,           // Sad/Happy (-1 to +1)
    "dominance": 0.4,         // Submissive/Aggressive (-1 to +1)
    "danceability": 0.85,     // Rhythm-based metric (0-1)
    "brightness": 0.7,        // Dark/Bright sound (0-1)
    "texture": "clean",       // clean/gritty/noisy/bright/dark
    "mood_tags": ["uplifting", "driving", "bright"],
    "legacy_energy": 6,       // Backward compatibility
    "confidence": 0.82,       // Analysis confidence
    "musical_characteristics": {
      "acousticness": 0.15,   // Live vs electronic
      "instrumentalness": 0.92, // Vocal vs instrumental
      "speechiness": 0.08     // Rap/spoken word detection
    },
    "perceptual_features": {
      "perceived_loudness": 0.75,
      "roughness": 0.23,
      "attack_speed": 0.68,
      "decay_rate": 0.45
    }
  }
}
```

## What's Missing to Activate Enhanced Energy 🚧

### Critical Gap: Integration Not Active

The enhanced energy analysis code exists but is **not integrated** into the main analysis pipeline:

**Current Analysis Flow**:
```python
# In _analyze_with_librosa() method:
results["energy"] = self._analyze_energy_levels(y, sr)  # ✅ Called
# self._analyze_energy_profile(y, sr)  # ❌ NOT called
```

**To Activate Enhanced Energy**:
1. **Call the method**: Add `energy_profile = self._analyze_energy_profile(y, sr)` to analysis pipeline
2. **Store results**: Add energy profile to `analysis_data` JSON in database
3. **Update UI**: Display enhanced energy data in track panels
4. **Update mix generation**: Use enhanced energy for better track matching

### Quick Activation Steps

**Step 1: Enable in Analysis Pipeline**
```python
# In audio_analyzer.py, _analyze_with_librosa() method:
# Add after line ~3520:
if self.enhanced_energy_analysis:  # Feature flag
    energy_profile = self._analyze_energy_profile(y, sr)
    results["energy_profile"] = energy_profile
```

**Step 2: Add Feature Flag**
```python
# In AudioAnalyzer.__init__():
self.enhanced_energy_analysis = True  # Enable enhanced analysis
```

**Step 3: Update UI to Display Enhanced Data**
- Modify TrackDetailsPanel to show mood tags
- Add energy profile visualization
- Display arousal/valence coordinates

## Implementation Phases 📋

### Phase 1: Activate Existing Code (1 week)

**Integrate existing enhanced analysis methods into main pipeline**

**Current Status**: ✅ All methods implemented but not called

**Required Changes**:
1. **Enable in analysis pipeline** - Add feature flag and method call
2. **Store in database** - Save energy profile to `analysis_data` JSON
3. **Update UI components** - Display enhanced energy information

### Phase 2: UI Integration (1 week)

**Update Track Display Components**:
- TrackDetailsPanel: Show mood tags and energy profile
- Collection browser: Display enhanced energy information
- Mix timeline: Use enhanced energy for better visualization

### Phase 3: Mix Generation Enhancement (1 week)

**Improve Mix Algorithms**:
- Use arousal/valence for better track transitions
- Consider dominance for mix flow dynamics
- Implement mood-based track selection

## Current Energy Understanding 🤔

### What Energy Means in Your App

**Simple Energy (Current)**:
- **Scale**: 1-10 (librosa) or 4-7 (Mixed in Key)
- **Calculation**: RMS energy + spectral centroid + onset strength
- **Usage**: Mix transitions, track selection
- **Display**: "Energy 6" with source attribution

**Enhanced Energy (Available but not active)**:
- **Arousal**: How energetic/calm (-1 to +1)
- **Valence**: How positive/negative (-1 to +1)
- **Dominance**: How aggressive/submissive (-1 to +1)
- **Texture**: Sound character (clean/gritty/bright/dark)
- **Mood Tags**: DJ-friendly descriptions (uplifting, driving, etc.)

### How Energy is Used in Mix Generation

**Current Usage** (`mix_service.py`):
```python
# Energy transition scoring
energy_diff = abs(current_energy - candidate_energy)
energy_score = max(0, 10 - energy_diff)  # Prefer similar energy levels
final_score = (base_score * 0.7) + (energy_score * 0.3)
```

**Potential Enhanced Usage**:
- Better transition matching using arousal/valence coordinates
- Mood-based track selection ("find uplifting tracks")
- Energy flow visualization in timeline
- Smarter mix progression using dominance

## Technical Implementation Details ✅

### All Enhanced Methods Already Implemented

**Enhanced Analysis Methods in `audio_analyzer.py`**:
- ✅ `_analyze_energy_profile()` - Main enhanced analysis method
- ✅ `_calculate_arousal_enhanced()` - Energy level calculation
- ✅ `_calculate_valence_enhanced()` - Mood positivity calculation
- ✅ `_analyze_texture_advanced()` - Sound texture analysis
- ✅ `_calculate_dominance()` - Aggressive/submissive analysis
- ✅ `_analyze_musical_characteristics()` - Acousticness, instrumentalness, speechiness
- ✅ `_analyze_perceptual_features()` - Loudness, roughness, attack/decay
- ✅ `_generate_mood_tags()` - DJ-friendly mood descriptions
- ✅ `_get_tempo_ensemble()` - Multi-method tempo detection

**All methods use pure librosa** - no additional dependencies required.

## Next Steps to Activate Enhanced Energy 🚀

### Option 1: Quick Activation (Recommended)

**1. Enable in Analysis Pipeline** (5 minutes):
```python
# In audio_analyzer.py, _analyze_with_librosa() method, add:
if True:  # Feature flag - set to True to enable
    energy_profile = self._analyze_energy_profile(y, sr)
    results["energy_profile"] = energy_profile
```

**2. Test with One Track**:
- Run analysis on a test track
- Check `TrackAnalysis.analysis_data` contains `energy_profile`
- Verify mood tags and arousal/valence values make sense

**3. Update UI** (optional):
- Add mood tags display to TrackDetailsPanel
- Show arousal/valence in track info

### Option 2: Keep Simple Energy System

**Current system works well for**:
- Basic mix transitions
- Track energy comparison
- Simple energy-based selection

**Enhanced system adds value for**:
- More nuanced track matching
- Mood-based track discovery
- Better mix flow visualization
- Professional DJ features

## Questions to Clarify 🤔

1. **Do you want to activate enhanced energy analysis?**
   - Pro: More sophisticated track analysis and matching
   - Con: More complex, analysis takes longer

2. **How do you envision using energy in mix generation?**
   - Current: Simple energy difference scoring
   - Enhanced: Mood-based transitions, energy flow curves

3. **What energy information should be visible to users?**
   - Current: "Energy 6 (Mixed in Key)"
   - Enhanced: "Energy 6 • Uplifting, Driving • Positive mood"

4. **Should energy analysis be automatic or optional?**
   - Automatic: All tracks get enhanced analysis
   - Optional: User chooses when to run enhanced analysis

Let me know your preferences and I can help implement the specific approach you want!
---

## Summary

**Current State**: Simple energy system (1-10 scale) works well for basic DJ mixing needs.

**Enhanced System**: Sophisticated multi-dimensional analysis is implemented but not active.

**Decision Needed**: Whether to activate enhanced energy analysis or keep the current simple system.

**All code is ready** - just needs integration into the analysis pipeline and UI updates.
