# Audio Analysis Technical Implementation Guide

This document provides technical details for developers working with the audio analysis system.

**Status**: ✅ Production-ready with performance optimizations

## Architecture Overview

### Multi-Algorithm Analysis Framework

The system uses an **ensemble approach** for maximum accuracy:
- Run multiple algorithms simultaneously
- Cross-validate results between methods
- Use weighted voting for final decisions
- Implement confidence scoring for each result

### Audio Processing Pipeline

```
Raw Audio → Normalization → Resampling → Format Standardization → Analysis Ready
```

**Key Steps**:
1. **Normalize audio levels** (-23 LUFS target)
2. **Resample** to consistent sample rate (44.1kHz or 48kHz)
3. **Convert to mono** for analysis (preserve stereo for output)
4. **Apply pre-emphasis filter** for high-frequency content
5. **Harmonic/percussive separation** using librosa

## Enhanced BPM Detection System

### Multi-Method Ensemble Detection

**Method 1: Standard Beat Tracking**
```python
tempo1, beats1 = librosa.beat.beat_track(y=y, sr=sr, hop_length=512)
```

**Method 2: Onset-Based Tempo**
```python
onset_env = librosa.onset.onset_strength(y=y, sr=sr)
tempo2 = librosa.beat.tempo(onset_envelope=onset_env, sr=sr)[0]
```

**Method 3: Tempogram Analysis**
```python
tempogram = librosa.feature.tempogram(y=y, sr=sr)
tempo3 = librosa.beat.tempo(tempogram=tempogram, sr=sr)[0]
```

**Method 4+: Enhanced Onset Detection**
```python
# Multiple onset detection methods with different features
onset_env_alt = librosa.onset.onset_strength(y=y, sr=sr, feature=librosa.feature.spectral_centroid)
tempo4 = librosa.beat.tempo(onset_envelope=onset_env_alt, sr=sr)[0]
```

### Ensemble Voting Logic

**Outlier Removal**:
- Use Modified Z-score (more robust than standard deviation)
- Remove tempos that deviate significantly from median

**Weighted Voting**:
- Weight methods based on historical accuracy
- Consider confidence scores from each method
- Use clustering to identify consensus

**Confidence Scoring**:
- **Method Agreement**: How well methods agree
- **Cluster Strength**: Tightness of tempo clustering
- **Temporal Stability**: Consistency across track sections

## Key Detection Enhancement

### Chroma-Based Analysis

```python
# Enhanced chroma features
chroma = librosa.feature.chroma_stft(y=y, sr=sr)

# Key templates (major/minor)
major_template = np.array([1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1])
minor_template = np.array([1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0])

# Template matching for all 24 keys
key_correlations = []
for shift in range(12):
    major_corr = np.corrcoef(chroma_mean, np.roll(major_template, shift))[0, 1]
    minor_corr = np.corrcoef(chroma_mean, np.roll(minor_template, shift))[0, 1]
    key_correlations.extend([major_corr, minor_corr])
```

### Confidence Assessment

- **Correlation strength**: How well chroma matches key template
- **Harmonic stability**: Consistency of harmonic content
- **Tonal clarity**: Presence of clear tonal center

## Segment Detection Algorithm

### Structural Analysis

**Novelty-Based Segmentation**:
```python
# Compute chroma features
chroma = librosa.feature.chroma_stft(y=y, sr=sr)

# Self-similarity matrix
similarity = np.dot(chroma.T, chroma)

# Novelty function
novelty = librosa.segment.recurrence_to_lag(similarity)

# Peak picking for segment boundaries
boundaries = librosa.util.peak_pick(novelty, pre_max=20, post_max=20, 
                                   pre_avg=100, post_avg=100, delta=0.1)
```

**Segment Classification**:
- **Intro**: Low energy, building complexity
- **Verse**: Moderate energy, stable rhythm
- **Chorus**: High energy, full instrumentation
- **Bridge**: Transitional, different harmonic content
- **Outro**: Decreasing energy, fade characteristics

## Performance Optimizations

### Current Optimization Status

**Speed Improvements**:
- **Intelligent caching** of intermediate results
- **Optimized librosa usage** for DJ-specific needs
- **Memory optimization** for large files
- **Simplified rhythm analysis** for performance

**Quality Features**:
- **Multi-method BPM detection** with ensemble voting
- **Enhanced key detection** with template matching
- **Essential DJ functionality** preserved and optimized

### Optimization Techniques

**Caching Strategy**:
- Cache processed audio segments
- Store intermediate analysis results
- Reuse computations across methods

**Memory Management**:
- Process audio in chunks for large files
- Release intermediate arrays promptly
- Use memory-mapped files for very large audio

**Algorithm Selection**:
- Skip computationally expensive methods for simple tracks
- Use fast methods first, detailed analysis only when needed
- Adaptive quality based on track characteristics

## Data Storage Schema

### TrackAnalysis.analysis_data JSON Structure

```json
{
  "tempo": 128.5,
  "tempo_confidence": 0.95,
  "tempo_methods": {
    "beat_track": 128.2,
    "onset_strength": 128.8,
    "tempogram": 128.1,
    "percussive": 128.9,
    "hop_1024": 128.3,
    "autocorr": 128.7
  },
  "key": "Am",
  "key_confidence": 0.87,
  "segments": [
    {
      "start": 0.0,
      "end": 32.5,
      "label": "intro",
      "confidence": 0.92
    }
  ],
  "beat_grid": {
    "beats": [0.5, 1.0, 1.5, 2.0],
    "confidence": 0.94,
    "method": "ensemble"
  },
  "energy": 6,
  "energy_source": "librosa",
  "analysis_version": "2025.06",
  "processing_time": 12.3
}
```

## API Integration

### Analysis Endpoints

**Beat Grid Extraction**:
```
POST /api/v1/beat-grid/extract/{trackId}
Response: { beats: number[], confidence: number, tempo: number }
```

**Segment Analysis**:
```
Note: Integrated into main analysis pipeline
Available through existing track analysis endpoints
```

**Full Analysis**:
```
GET /tracks/{trackId}/analysis/full-data
Response: Complete analysis_data JSON
```

## Error Handling & Validation

### Quality Validation

**BPM Validation**:
- Check tempo is within reasonable range (60-200 BPM)
- Verify consistency across track sections
- Flag potential tempo doubling/halving errors

**Key Validation**:
- Ensure key correlation meets minimum threshold
- Check for tonal vs atonal content
- Validate against harmonic progression patterns

**Segment Validation**:
- Verify segment boundaries make musical sense
- Check minimum/maximum segment lengths
- Ensure segments cover entire track

### Retry Logic

**Failed Analysis Recovery**:
1. Retry with different parameters
2. Fall back to simpler algorithms
3. Use cached results from previous analysis
4. Provide partial results with confidence indicators

This technical guide provides the foundation for understanding and extending the audio analysis system while maintaining its professional-grade accuracy and performance.
