# AI Technical Documentation

This document provides technical documentation for the AI integration currently implemented in the DJ Mix Constructor application.

**Status Legend**: ✅ Implemented | 🚧 Partial | ❌ Not Implemented

## Table of Contents

1. [Architecture Overview](#architecture-overview) ✅
2. [Backend Components](#backend-components) ✅
3. [Frontend Components](#frontend-components) ✅
4. [API Endpoints](#api-endpoints) ✅
5. [Monitoring System](#monitoring-system) ✅
6. [Extension Points](#extension-points) ✅
7. [Non-Functional Components](#non-functional-components) ❌

## Architecture Overview ✅

The AI integration follows a layered architecture with provider abstraction:

### Current Architecture

```
Frontend (React/TypeScript)
├── AI Components (AIAssistant, GlobalAIAssistant, etc.)
├── AI Providers (AIProvider, MultiModalAIProvider contexts)
├── AI Services (aiService, multimodalAIService)
└── Cache Layer (AIResponseCache)

Backend (Python/FastAPI)
├── API Routes (/api/v1/ai/*, /api/v1/multimodal-ai/*)
├── AI Providers (Gemini, OpenAI, Anthropic, OpenRouter)
├── Support Services (PromptManager, ParameterManager)
└── Monitoring (Analytics, Optimization)
```

### Key Design Principles

- **Provider Abstraction**: Common interfaces (`AIProvider`, `MultiModalAIProvider`)
- **Caching**: Response caching for performance
- **Monitoring**: Usage tracking and optimization
- **Error Handling**: Fallback to dummy providers when API keys missing

### Data Flow

1. User interacts with AI components (chat, file upload)
2. Frontend services call backend API endpoints
3. Backend routes use configured AI providers
4. Providers make API calls to external AI services
5. Responses cached and returned to frontend
6. Monitoring data collected throughout the process

## Backend Components ✅

### AI Providers

**Base Classes**:
- `AIProvider` (`backend/services/ai/base.py`): Abstract interface for text-based AI
- `MultiModalAIProvider` (`backend/services/ai/multimodal_base.py`): Abstract interface for multimodal AI

**Implemented Providers**:
- `GeminiProvider` (`backend/services/ai/gemini_provider.py`): Google's Gemini models
- `OpenAIProvider` (`backend/services/ai/openai_provider.py`): OpenAI's GPT models
- `AnthropicProvider` (`backend/services/ai/anthropic_provider.py`): Anthropic's Claude models
- `OpenRouterProvider` (`backend/services/ai/openrouter_provider.py`): OpenRouter gateway
- `GeminiMultiModalProvider` (`backend/services/ai/gemini_multimodal_provider.py`): Gemini Vision
- `OpenAIMultiModalProvider` (`backend/services/ai/openai_multimodal_provider.py`): GPT-4 Vision + DALL-E

**Factory Functions**:
- `get_ai_provider()`: Returns configured text AI provider
- `get_multimodal_provider()`: Returns configured multimodal AI provider
- Falls back to `DummyProvider` when API keys are missing

### Support Services

**Configuration Management**:
- `PromptManager` (`backend/services/ai/prompt_manager.py`): System prompt management
- `ParameterManager` (`backend/services/ai/parameter_manager.py`): Feature parameter management
- `AdvancedSettingsManager` (`backend/services/ai/advanced_settings_manager.py`): Advanced settings

**Monitoring Services**:
- `AIDataCollector` (`backend/services/monitoring/ai_data_collector.py`): Usage data collection
- `AIAnalyticsService` (`backend/services/monitoring/ai_analytics_service.py`): Analytics aggregation
- `AIOptimizationService` (`backend/services/monitoring/ai_optimization_service.py`): Optimization recommendations
- `AISettingsImpactAnalyzer` (`backend/services/monitoring/ai_settings_impact_analyzer.py`): Settings impact analysis

## API Endpoints ✅

### Text-based AI (`backend/routes/ai_api.py`)
- `POST /api/v1/ai/generate-style`: Generate mix style parameters
- `POST /api/v1/ai/generate-documentation`: Generate style documentation
- `POST /api/v1/ai/transition-suggestions`: Get transition suggestions
- `POST /api/v1/ai/analyze-collection`: Analyze music collection
- `POST /api/v1/ai/answer-question`: General Q&A

### Multimodal AI (`backend/routes/multimodal_ai.py`)
- `POST /api/v1/multimodal-ai/analyze-image`: Analyze uploaded images
- `POST /api/v1/multimodal-ai/analyze-audio`: Analyze uploaded audio
- `POST /api/v1/multimodal-ai/generate-image`: Generate images (if supported)
- `POST /api/v1/multimodal-ai/analyze-album-artwork`: Analyze album artwork
- `POST /api/v1/multimodal-ai/analyze-track-features`: Analyze track features

### AI Settings (`backend/routes/ai_settings.py`)
- `GET/POST /api/v1/ai-settings`: Get/update AI configuration
- `POST /api/v1/ai-settings/test-connection`: Test provider connection
- `POST /api/v1/ai-settings/check-api-key`: Validate API key
- `GET/POST /api/v1/ai-settings/system-prompts`: Manage system prompts

### Monitoring (`backend/routes/ai_monitoring.py`)
- `GET /api/v1/ai-monitoring/metrics`: Get performance metrics
- `POST /api/v1/ai-monitoring/feedback`: Submit user feedback
- `GET /api/v1/ai-monitoring/usage`: Get usage statistics

### Optimization (`backend/routes/ai_optimization.py`)
- `GET /api/v1/ai-optimization/recommendations`: Get optimization recommendations
- `POST /api/v1/ai-optimization/apply/{recommendation_id}`: Apply recommendation

## Frontend Components ✅

### React Context Providers

**AIProvider** (`frontend/src/providers/AIProvider.tsx`):
- Provides text-based AI features via `useAI()` hook
- Manages loading states and errors
- Handles response caching
- Methods: `generateStyleParameters`, `analyzeCollection`, `answerQuestion`

**MultiModalAIProvider** (`frontend/src/providers/MultiModalAIProvider.tsx`):
- Provides multimodal AI features via `useMultiModalAI()` hook
- Handles file uploads and processing
- Methods: `analyzeImage`, `analyzeAudio`, `generateImage`

**GlobalAIAssistantProvider** (`frontend/src/providers/GlobalAIAssistantProvider.tsx`):
- Manages global assistant state via `useGlobalAIAssistant()` hook
- Controls assistant visibility and context

### UI Components

**Core AI Components**:
- `AIAssistant.tsx`: Basic chat interface with file upload
- `EnhancedAIAssistant.tsx`: Advanced chat with voice, commands, suggestions
- `GlobalAIAssistant.tsx`: Floating assistant button

**Supporting Components**:
- `RichResponseRenderer.tsx`: Formats AI responses with syntax highlighting
- `VoiceInteraction.tsx`: Speech-to-text and text-to-speech
- `SlashCommands.tsx`: Command system with autocomplete
- `SmartSuggestions.tsx`: Context-aware suggestions
- `AIFeedback.tsx`: User feedback collection

### Frontend Services

**aiService** (`frontend/src/services/ai/aiService.ts`):
- API client for text-based AI endpoints
- Error handling and request formatting
- Methods match backend AI endpoints

**multimodalAIService** (`frontend/src/services/ai/multimodalAIService.ts`):
- API client for multimodal AI endpoints
- File upload handling
- Image/audio analysis methods

**AIResponseCache** (`frontend/src/services/cache/AIResponseCache.ts`):
- Local storage caching with TTL
- Cache statistics and management
- Performance optimization

## Monitoring System ✅

### Data Collection

**AIDataCollector** (`backend/services/monitoring/ai_data_collector.py`):
- Collects usage metrics (response time, token count, success rate)
- Tracks user feedback and satisfaction ratings
- Supports file-based and database storage
- Thread-safe data access

### Analytics & Optimization

**AIAnalyticsService** (`backend/services/monitoring/ai_analytics_service.py`):
- Aggregates monitoring data
- Calculates performance metrics
- Generates usage reports with filtering

**AIOptimizationService** (`backend/services/monitoring/ai_optimization_service.py`):
- Generates optimization recommendations
- Analyzes settings impact on performance
- Provides A/B testing framework

### Metrics Tracked

- **Performance**: Response time, success rate, token usage
- **Usage**: Feature frequency, user engagement patterns
- **Satisfaction**: User ratings, feedback comments
- **Settings**: Configuration usage and impact analysis

## Extension Points ✅

### Adding New AI Providers

1. **Create Provider Class**:
   ```python
   class NewProvider(AIProvider):
       async def generate_text(self, prompt: str, temperature: Optional[float] = None) -> str:
           # Implementation
   ```

2. **Update Factory Function**:
   ```python
   # In backend/services/ai/base.py
   def get_ai_provider(model_name: Optional[str] = None) -> AIProvider:
       if provider == "new_provider":
           return NewProvider(api_key=settings.NEW_PROVIDER_API_KEY)
   ```

3. **Add Configuration**:
   - Add API key to `backend/config.py`
   - Update frontend provider selection UI
   - Add to available models list

### Adding New AI Features

1. **Backend Implementation**:
   - Add method to provider interfaces
   - Implement in all provider classes
   - Create API endpoint in appropriate router
   - Add monitoring/optimization support

2. **Frontend Implementation**:
   - Add method to frontend service
   - Create UI components
   - Update context providers
   - Add to slash commands if applicable

### Configuration Management

**Settings Storage**:
- `backend/data/custom_parameters.json`: Feature parameters
- `backend/data/custom_prompts.json`: System prompts
- `backend/data/custom_advanced_settings.json`: Advanced settings
- `backend/data/ai_settings.json`: Provider/model selection

**Adding New Settings**:
1. Add default to `backend/config.py`
2. Update appropriate manager service
3. Add to API endpoints
4. Update frontend UI

## Non-Functional Components ❌

### MCP (Model Context Protocol)

**Status**: Routes exist but dependencies missing

**Files Present But Non-Functional**:
- `backend/services/ai/mcp_*.py` - MCP server/client implementation
- `backend/routes/ai_mcp*.py` - MCP API routes
- Various MCP-related components

**Missing Dependencies**:
- `mcp>=1.2.0` not in requirements.txt
- `fastmcp` not in requirements.txt

**Impact**: All MCP-related functionality will fail at runtime

### Advanced Audio Analysis

**Status**: Depends on non-functional MCP

**Files Present**:
- `backend/services/ai/mcp_audio_analysis.py`
- Related librosa integration code

**Issue**: Documented as "COMPLETED ✅" but depends on MCP infrastructure

---

## Development Guidelines

### Best Practices

1. **Error Handling**: Always implement fallbacks (DummyProvider pattern)
2. **Monitoring**: Add monitoring to new AI features
3. **Caching**: Implement caching for expensive operations
4. **Testing**: Test with multiple providers and edge cases
5. **Documentation**: Update both user and technical docs

### Performance Considerations

- Enable response caching for better performance
- Monitor token usage to control costs
- Use appropriate models for different tasks
- Implement request timeouts and retries

### Security

- Store API keys securely (environment variables)
- Validate all user inputs before sending to AI providers
- Implement rate limiting for API endpoints
- Log security-relevant events

This document reflects the actual implemented AI architecture in the codebase.

## Extension Points

Key extension points for adding new AI features:

### Adding a New AI Provider

1. Create a new provider class that implements `AIProvider` or `MultiModalAIProvider`
2. Add the provider to the provider factory in `backend/services/ai/base.py`
3. Update the provider selection in the settings UI

### Adding a New AI Feature

1. Add new methods to the appropriate provider interfaces
2. Implement the methods in each provider class
3. Create new API endpoints in the appropriate router
4. Add frontend services to call the new endpoints
5. Create UI components to expose the feature to users

### Customizing System Prompts

1. Add the new prompt to `DEFAULT_SYSTEM_PROMPTS` in `backend/config.py`
2. Update the prompt manager to handle the new prompt
3. Add the prompt to the system prompts UI

### Adding New Monitoring Metrics

1. Update the data collector to track the new metrics
2. Add aggregation methods to the analytics service
3. Update the dashboard UI to display the new metrics

### Adding New Settings

To add a new setting:

1. Add default value to `backend/config.py`
2. Update the appropriate manager service
3. Add metadata for the setting
4. Update API endpoints if needed
5. Update frontend service
6. Update UI components

### Data Storage

Custom settings are stored in JSON files:

- `backend/data/custom_parameters.json`: Custom feature parameters
- `backend/data/custom_prompts.json`: Custom system prompts
- `backend/data/custom_advanced_settings.json`: Custom advanced settings

### Best Practices for Developers

1. **Default Values**: Always provide sensible default values
2. **Validation**: Validate settings before using them
3. **Fallbacks**: Implement fallbacks for when settings are invalid
4. **Documentation**: Document all settings and their effects
5. **Testing**: Test settings with different AI providers
6. **Performance**: Monitor performance impact of settings
7. **User Experience**: Consider user experience when designing settings UI
