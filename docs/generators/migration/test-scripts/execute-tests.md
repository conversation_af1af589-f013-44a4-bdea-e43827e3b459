# Test Execution Guide

## 🧪 **How to Execute Migration Validation Tests**

### **Prerequisites**
1. V2 Modular Generator page loaded
2. Browser developer tools open
3. Network connection available
4. Test data/collections available

### **Step 1: Load Automated Test Script**

Copy and paste this code into the browser console:

```javascript
// Load the automated validation script
const script = document.createElement('script');
script.textContent = `
// Paste the entire content of automated-validation.js here
// Or load it from a URL if hosted
`;
document.head.appendChild(script);

// Alternative: Load from file (if accessible)
fetch('/docs/generators/migration/test-scripts/automated-validation.js')
  .then(response => response.text())
  .then(code => {
    const script = document.createElement('script');
    script.textContent = code;
    document.head.appendChild(script);
    console.log('✅ Automated tests loaded successfully');
  })
  .catch(error => {
    console.error('❌ Failed to load test script:', error);
    console.log('💡 Copy and paste the script content manually');
  });
```

### **Step 2: Execute Tests**

Once the script is loaded, run the tests:

```javascript
// Execute all automated tests
MigrationTests.runAllTests().then(results => {
  console.log('🎉 Test execution completed!');
  console.log('Results:', results);
});

// Or run individual test suites
await MigrationTests.MixStyleTests.testMixStyleLoading();
await MigrationTests.CollectionTests.testCollectionLoading();
await MigrationTests.BlockTests.testBlockLibrary();
```

### **Step 3: Load Performance Benchmarks**

```javascript
// Load performance benchmark script
fetch('/docs/generators/migration/test-scripts/performance-benchmark.js')
  .then(response => response.text())
  .then(code => {
    const script = document.createElement('script');
    script.textContent = code;
    document.head.appendChild(script);
    console.log('✅ Performance benchmarks loaded');
  });
```

### **Step 4: Execute Performance Tests**

```javascript
// Run performance benchmarks
PerformanceBenchmarks.runPerformanceBenchmarks().then(results => {
  console.log('🚀 Performance benchmarking completed!');
  console.log('Results:', results);
});
```

### **Step 5: Manual Validation Checklist**

Execute these manual tests in order:

#### **Basic Functionality Test**
1. ✅ Page loads without errors
2. ✅ All UI panels are visible
3. ✅ Collections dropdown populates
4. ✅ Mix styles dropdown populates
5. ✅ Block library shows blocks
6. ✅ Canvas area is interactive

#### **Core Workflow Test**
1. Select a collection → ✅ Folders load
2. Select "All Tracks" folder → ✅ Tracks load
3. Choose a mix style → ✅ Selection updates
4. Add a block → ✅ Block appears in canvas
5. Click track position → ✅ Recommendations appear
6. Assign a track → ✅ Track assigned successfully

#### **Error Handling Test**
1. Disconnect network → Try collection selection → ✅ Error handled gracefully
2. Select invalid data → ✅ Validation messages appear
3. Reconnect network → Retry operation → ✅ Recovery works

#### **Performance Test**
1. Open DevTools Performance tab
2. Start recording
3. Execute core workflow
4. Stop recording
5. ✅ No performance issues detected

### **Expected Results**

#### **Automated Tests**
- **Pass Rate**: >95%
- **Failed Tests**: <3
- **Skipped Tests**: Acceptable for missing features
- **Error Rate**: <5%

#### **Performance Benchmarks**
- **Mix Style Loading**: <500ms
- **Collection Selection**: <3000ms
- **Track Recommendations**: <2000ms (cache) / <5000ms (fresh)
- **Block Addition**: <1000ms
- **Auto-Population**: <5000ms

#### **Manual Tests**
- **All workflows complete**: ✅
- **Error handling works**: ✅
- **Performance acceptable**: ✅
- **User experience smooth**: ✅

### **Troubleshooting**

#### **Common Issues**

1. **Script Loading Fails**
   ```javascript
   // Manual script injection
   const testCode = `/* paste automated-validation.js content here */`;
   eval(testCode);
   ```

2. **Tests Timeout**
   ```javascript
   // Increase timeout
   TEST_CONFIG.timeout = 30000; // 30 seconds
   ```

3. **Elements Not Found**
   ```javascript
   // Check if page is fully loaded
   document.readyState === 'complete'
   
   // Wait for specific elements
   await TestUtils.waitForElement('your-selector', 15000);
   ```

4. **API Errors**
   ```javascript
   // Check network tab for failed requests
   // Verify backend services are running
   // Check CORS settings
   ```

### **Test Data Requirements**

For comprehensive testing, ensure:
- ✅ At least 2 collections available
- ✅ Collections contain tracks with Camelot keys
- ✅ Mix styles are configured
- ✅ Backend services are accessible
- ✅ Network connection is stable

### **Reporting Issues**

When reporting test failures, include:
1. **Browser**: Chrome/Firefox/Safari version
2. **Console Logs**: Full error messages
3. **Network Tab**: Failed API calls
4. **Steps to Reproduce**: Exact sequence
5. **Expected vs Actual**: What should happen vs what happened

### **Success Criteria**

The migration is considered successful when:
- ✅ **Automated Tests**: >95% pass rate
- ✅ **Performance**: Equal or better than V1
- ✅ **Manual Tests**: All critical workflows work
- ✅ **Error Handling**: Graceful degradation
- ✅ **User Experience**: Smooth and intuitive

---

## 📊 **Quick Test Execution**

For a rapid validation, run this one-liner:

```javascript
// Quick validation (paste in console)
(async () => {
  console.log('🧪 Quick Migration Validation');
  
  // Check basic elements
  const checks = [
    { name: 'Mix Style Selector', selector: '[data-testid*="mix-style"], select, .mix-style' },
    { name: 'Collection Selector', selector: '[data-testid*="collection"], select, .collection' },
    { name: 'Block Library', selector: '[data-testid*="block"], .block-library, .blocks' },
    { name: 'Canvas Area', selector: '[data-testid*="canvas"], .canvas, .timeline' }
  ];
  
  let passed = 0;
  for (const check of checks) {
    const element = document.querySelector(check.selector);
    if (element) {
      console.log(`✅ ${check.name}: Found`);
      passed++;
    } else {
      console.log(`❌ ${check.name}: Not found`);
    }
  }
  
  console.log(`\n📊 Quick Check: ${passed}/${checks.length} elements found`);
  
  if (passed === checks.length) {
    console.log('🎉 Basic validation PASSED - Ready for full testing');
  } else {
    console.log('⚠️ Basic validation FAILED - Check page loading');
  }
})();
```

This will quickly verify that the essential elements are present and the page is ready for comprehensive testing.
