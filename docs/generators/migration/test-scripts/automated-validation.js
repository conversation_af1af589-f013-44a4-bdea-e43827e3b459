/**
 * Automated Validation Script for Modular Generator V1 → V2 Migration
 * 
 * This script validates core functionality and feature parity
 * Run in browser console on both V1 and V2 pages for comparison
 * 
 * PHASE 3: Testing & Validation - Automated test execution
 */

// Test configuration
const TEST_CONFIG = {
  timeout: 10000, // 10 seconds
  retryAttempts: 3,
  verbose: true
};

// Test results storage
let testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  errors: [],
  details: []
};

/**
 * Test utility functions
 */
const TestUtils = {
  log: (message, type = 'info') => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    if (TEST_CONFIG.verbose) {
      testResults.details.push(logMessage);
    }
  },

  assert: (condition, message) => {
    if (condition) {
      TestUtils.log(`✅ PASS: ${message}`, 'pass');
      testResults.passed++;
      return true;
    } else {
      TestUtils.log(`❌ FAIL: ${message}`, 'fail');
      testResults.failed++;
      testResults.errors.push(message);
      return false;
    }
  },

  skip: (message) => {
    TestUtils.log(`⏭️ SKIP: ${message}`, 'skip');
    testResults.skipped++;
  },

  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  waitForElement: async (selector, timeout = TEST_CONFIG.timeout) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      const element = document.querySelector(selector);
      if (element) return element;
      await TestUtils.wait(100);
    }
    throw new Error(`Element ${selector} not found within ${timeout}ms`);
  },

  waitForCondition: async (condition, timeout = TEST_CONFIG.timeout) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      if (await condition()) return true;
      await TestUtils.wait(100);
    }
    throw new Error(`Condition not met within ${timeout}ms`);
  }
};

/**
 * Test Suite 1: Mix Style System Validation
 */
const MixStyleTests = {
  async testMixStyleLoading() {
    TestUtils.log('Testing Mix Style Loading...');
    
    try {
      // Check if mix style selector exists
      const selector = await TestUtils.waitForElement('[data-testid="mix-style-selector"], select[value*="style"], .mix-style-dropdown');
      TestUtils.assert(selector !== null, 'Mix style selector element exists');
      
      // Check if styles are loaded
      await TestUtils.waitForCondition(async () => {
        const options = selector.querySelectorAll('option, [role="option"]');
        return options.length > 1; // Should have "No Style" + actual styles
      });
      
      const options = selector.querySelectorAll('option, [role="option"]');
      TestUtils.assert(options.length >= 6, `Mix styles loaded (found ${options.length} options)`);
      
      // Check for expected style names
      const optionTexts = Array.from(options).map(opt => opt.textContent.toLowerCase());
      const expectedStyles = ['modular flexible', 'club', 'underground', 'downtempo', 'progressive', 'minimal'];
      
      let foundStyles = 0;
      expectedStyles.forEach(style => {
        if (optionTexts.some(text => text.includes(style))) {
          foundStyles++;
        }
      });
      
      TestUtils.assert(foundStyles >= 4, `Expected mix styles found (${foundStyles}/6)`);
      
    } catch (error) {
      TestUtils.assert(false, `Mix style loading failed: ${error.message}`);
    }
  },

  async testMixStyleSelection() {
    TestUtils.log('Testing Mix Style Selection...');
    
    try {
      const selector = document.querySelector('[data-testid="mix-style-selector"], select[value*="style"], .mix-style-dropdown');
      if (!selector) {
        TestUtils.skip('Mix style selector not found');
        return;
      }
      
      // Test style selection
      const options = selector.querySelectorAll('option, [role="option"]');
      if (options.length > 1) {
        // Select first non-default option
        const firstStyle = options[1];
        firstStyle.click();
        
        await TestUtils.wait(500); // Wait for state update
        
        TestUtils.assert(true, 'Mix style selection completed without errors');
      } else {
        TestUtils.skip('No mix style options available for selection');
      }
      
    } catch (error) {
      TestUtils.assert(false, `Mix style selection failed: ${error.message}`);
    }
  }
};

/**
 * Test Suite 2: Collection Handling Validation
 */
const CollectionTests = {
  async testCollectionLoading() {
    TestUtils.log('Testing Collection Loading...');
    
    try {
      // Check if collection selector exists
      const selector = await TestUtils.waitForElement('[data-testid="collection-selector"], select[value*="collection"], .collection-dropdown');
      TestUtils.assert(selector !== null, 'Collection selector element exists');
      
      // Check if collections are loaded
      await TestUtils.waitForCondition(async () => {
        const options = selector.querySelectorAll('option, [role="option"]');
        return options.length > 0;
      });
      
      const options = selector.querySelectorAll('option, [role="option"]');
      TestUtils.assert(options.length > 0, `Collections loaded (found ${options.length} collections)`);
      
    } catch (error) {
      TestUtils.assert(false, `Collection loading failed: ${error.message}`);
    }
  },

  async testCollectionSelection() {
    TestUtils.log('Testing Collection Selection...');
    
    try {
      const selector = document.querySelector('[data-testid="collection-selector"], select[value*="collection"], .collection-dropdown');
      if (!selector) {
        TestUtils.skip('Collection selector not found');
        return;
      }
      
      const options = selector.querySelectorAll('option, [role="option"]');
      if (options.length > 0) {
        // Select first collection
        const firstCollection = options[0];
        firstCollection.click();
        
        await TestUtils.wait(2000); // Wait for collection loading
        
        TestUtils.assert(true, 'Collection selection completed');
        
        // Check if folder selector appears
        await TestUtils.waitForCondition(async () => {
          const folderSelector = document.querySelector('[data-testid="folder-selector"], select[value*="folder"], .folder-dropdown');
          return folderSelector !== null;
        }, 5000);
        
        TestUtils.assert(true, 'Folder selector appeared after collection selection');
        
      } else {
        TestUtils.skip('No collections available for selection');
      }
      
    } catch (error) {
      TestUtils.assert(false, `Collection selection failed: ${error.message}`);
    }
  }
};

/**
 * Test Suite 3: Block Management Validation
 */
const BlockTests = {
  async testBlockLibrary() {
    TestUtils.log('Testing Block Library...');
    
    try {
      // Check if block library exists
      const blockLibrary = await TestUtils.waitForElement('[data-testid="block-library"], .block-library, .blocks-panel');
      TestUtils.assert(blockLibrary !== null, 'Block library element exists');
      
      // Check if blocks are available
      const blocks = blockLibrary.querySelectorAll('[data-testid*="block"], .block-item, .harmonic-block');
      TestUtils.assert(blocks.length > 0, `Blocks available in library (found ${blocks.length} blocks)`);
      
      // Check for expected block types
      const blockTexts = Array.from(blocks).map(block => block.textContent.toLowerCase());
      const expectedTypes = ['eb', 'pt', 'ep', 'er', 'op', 'cl'];
      
      let foundTypes = 0;
      expectedTypes.forEach(type => {
        if (blockTexts.some(text => text.includes(type))) {
          foundTypes++;
        }
      });
      
      TestUtils.assert(foundTypes >= 3, `Expected block types found (${foundTypes}/6)`);
      
    } catch (error) {
      TestUtils.assert(false, `Block library test failed: ${error.message}`);
    }
  },

  async testBlockAddition() {
    TestUtils.log('Testing Block Addition...');
    
    try {
      const blockLibrary = document.querySelector('[data-testid="block-library"], .block-library, .blocks-panel');
      if (!blockLibrary) {
        TestUtils.skip('Block library not found');
        return;
      }
      
      const blocks = blockLibrary.querySelectorAll('[data-testid*="block"], .block-item, .harmonic-block');
      if (blocks.length > 0) {
        // Try to add first block
        const firstBlock = blocks[0];
        firstBlock.click();
        
        await TestUtils.wait(1000); // Wait for block addition
        
        // Check if block appeared in canvas
        const canvas = document.querySelector('[data-testid="mix-canvas"], .mix-canvas, .timeline');
        if (canvas) {
          const canvasBlocks = canvas.querySelectorAll('[data-testid*="block"], .block, .mix-block');
          TestUtils.assert(canvasBlocks.length > 0, 'Block added to canvas successfully');
        } else {
          TestUtils.skip('Mix canvas not found for validation');
        }
        
      } else {
        TestUtils.skip('No blocks available for addition');
      }
      
    } catch (error) {
      TestUtils.assert(false, `Block addition failed: ${error.message}`);
    }
  }
};

/**
 * Test Suite 4: Performance Validation
 */
const PerformanceTests = {
  async testRecommendationPerformance() {
    TestUtils.log('Testing Recommendation Performance...');
    
    try {
      // Look for performance logs in console
      const originalLog = console.log;
      let performanceLogs = [];
      
      console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('PHASE2') || message.includes('recommendation') || message.includes('ms')) {
          performanceLogs.push(message);
        }
        originalLog.apply(console, args);
      };
      
      // Trigger a recommendation request if possible
      const canvas = document.querySelector('[data-testid="mix-canvas"], .mix-canvas, .timeline');
      if (canvas) {
        const positions = canvas.querySelectorAll('[data-testid*="position"], .position, .track-position');
        if (positions.length > 0) {
          positions[0].click();
          await TestUtils.wait(3000); // Wait for recommendations
        }
      }
      
      // Restore console.log
      console.log = originalLog;
      
      TestUtils.assert(performanceLogs.length > 0, `Performance monitoring active (${performanceLogs.length} logs found)`);
      
      // Check for performance indicators
      const hasPerformanceData = performanceLogs.some(log => 
        log.includes('ms') || log.includes('cache') || log.includes('timing')
      );
      
      TestUtils.assert(hasPerformanceData, 'Performance data being tracked');
      
    } catch (error) {
      TestUtils.assert(false, `Performance testing failed: ${error.message}`);
    }
  }
};

/**
 * Test Suite 5: Error Handling Validation
 */
const ErrorHandlingTests = {
  async testErrorRecovery() {
    TestUtils.log('Testing Error Handling...');
    
    try {
      // Check if error handling is active by looking for error-related logs
      const originalError = console.error;
      let errorLogs = [];
      
      console.error = function(...args) {
        const message = args.join(' ');
        errorLogs.push(message);
        originalError.apply(console, args);
      };
      
      // Wait a bit to see if any errors occur naturally
      await TestUtils.wait(2000);
      
      // Restore console.error
      console.error = originalError;
      
      // Check if error handling infrastructure is present
      const hasErrorHandling = window.errorHandler || 
                              document.querySelector('[data-testid*="error"], .error-message, .toast') ||
                              errorLogs.some(log => log.includes('PHASE2') || log.includes('error'));
      
      TestUtils.assert(hasErrorHandling, 'Error handling infrastructure present');
      
    } catch (error) {
      TestUtils.assert(false, `Error handling test failed: ${error.message}`);
    }
  }
};

/**
 * Main test execution function
 */
async function runAllTests() {
  TestUtils.log('🧪 Starting Comprehensive Migration Validation Tests');
  TestUtils.log('================================================');
  
  // Reset test results
  testResults = { passed: 0, failed: 0, skipped: 0, errors: [], details: [] };
  
  try {
    // Test Suite 1: Mix Style System
    TestUtils.log('\n📋 Test Suite 1: Mix Style System');
    await MixStyleTests.testMixStyleLoading();
    await MixStyleTests.testMixStyleSelection();
    
    // Test Suite 2: Collection Handling
    TestUtils.log('\n📋 Test Suite 2: Collection Handling');
    await CollectionTests.testCollectionLoading();
    await CollectionTests.testCollectionSelection();
    
    // Test Suite 3: Block Management
    TestUtils.log('\n📋 Test Suite 3: Block Management');
    await BlockTests.testBlockLibrary();
    await BlockTests.testBlockAddition();
    
    // Test Suite 4: Performance
    TestUtils.log('\n📋 Test Suite 4: Performance Validation');
    await PerformanceTests.testRecommendationPerformance();
    
    // Test Suite 5: Error Handling
    TestUtils.log('\n📋 Test Suite 5: Error Handling');
    await ErrorHandlingTests.testErrorRecovery();
    
  } catch (error) {
    TestUtils.log(`❌ Test execution error: ${error.message}`, 'error');
    testResults.failed++;
  }
  
  // Generate test report
  TestUtils.log('\n📊 Test Results Summary');
  TestUtils.log('======================');
  TestUtils.log(`✅ Passed: ${testResults.passed}`);
  TestUtils.log(`❌ Failed: ${testResults.failed}`);
  TestUtils.log(`⏭️ Skipped: ${testResults.skipped}`);
  TestUtils.log(`📊 Total: ${testResults.passed + testResults.failed + testResults.skipped}`);
  
  if (testResults.failed > 0) {
    TestUtils.log('\n❌ Failed Tests:');
    testResults.errors.forEach(error => TestUtils.log(`  - ${error}`));
  }
  
  const successRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
  TestUtils.log(`\n📈 Success Rate: ${successRate.toFixed(1)}%`);
  
  if (successRate >= 95) {
    TestUtils.log('🎉 MIGRATION VALIDATION SUCCESSFUL!', 'success');
  } else if (successRate >= 80) {
    TestUtils.log('⚠️ MIGRATION VALIDATION PARTIAL - Review failed tests', 'warning');
  } else {
    TestUtils.log('❌ MIGRATION VALIDATION FAILED - Significant issues found', 'error');
  }
  
  return testResults;
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, TestUtils, testResults };
} else {
  // Browser environment - make available globally
  window.MigrationTests = { runAllTests, TestUtils, testResults };
}

// Auto-run if in browser and not in module
if (typeof window !== 'undefined' && typeof module === 'undefined') {
  console.log('🧪 Migration Validation Tests Loaded');
  console.log('Run: MigrationTests.runAllTests() to execute all tests');
  console.log('Or run individual test suites from MigrationTests object');
}
