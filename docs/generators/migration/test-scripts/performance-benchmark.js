/**
 * Performance Benchmark Script for V1 vs V2 Comparison
 * 
 * This script measures and compares performance between V1 and V2
 * Run on both versions to generate comparative analysis
 * 
 * PHASE 3: Testing & Validation - Performance benchmarking
 */

// Benchmark configuration
const BENCHMARK_CONFIG = {
  iterations: 5,
  warmupRuns: 2,
  timeout: 30000,
  collectMemoryStats: true,
  collectNetworkStats: true
};

// Performance metrics storage
let benchmarkResults = {
  version: 'unknown',
  timestamp: new Date().toISOString(),
  metrics: {},
  comparisons: {},
  recommendations: []
};

/**
 * Performance measurement utilities
 */
const PerfUtils = {
  log: (message, data = null) => {
    console.log(`[BENCHMARK] ${message}`, data || '');
  },

  measure: async (name, operation, iterations = 1) => {
    const measurements = [];
    
    // Warmup runs
    for (let i = 0; i < BENCHMARK_CONFIG.warmupRuns; i++) {
      try {
        await operation();
      } catch (error) {
        PerfUtils.log(`Warmup ${i + 1} failed for ${name}: ${error.message}`);
      }
    }
    
    // Actual measurements
    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      const startMemory = BENCHMARK_CONFIG.collectMemoryStats ? performance.memory?.usedJSHeapSize : null;
      
      try {
        await operation();
        
        const endTime = performance.now();
        const endMemory = BENCHMARK_CONFIG.collectMemoryStats ? performance.memory?.usedJSHeapSize : null;
        
        measurements.push({
          duration: endTime - startTime,
          memoryDelta: startMemory && endMemory ? endMemory - startMemory : null,
          success: true
        });
        
      } catch (error) {
        measurements.push({
          duration: null,
          memoryDelta: null,
          success: false,
          error: error.message
        });
      }
    }
    
    return PerfUtils.analyzeMetrics(name, measurements);
  },

  analyzeMetrics: (name, measurements) => {
    const successful = measurements.filter(m => m.success);
    const failed = measurements.filter(m => !m.success);
    
    if (successful.length === 0) {
      return {
        name,
        status: 'failed',
        successRate: 0,
        errors: failed.map(f => f.error)
      };
    }
    
    const durations = successful.map(m => m.duration);
    const memoryDeltas = successful.map(m => m.memoryDelta).filter(m => m !== null);
    
    return {
      name,
      status: 'success',
      successRate: (successful.length / measurements.length) * 100,
      duration: {
        min: Math.min(...durations),
        max: Math.max(...durations),
        avg: durations.reduce((a, b) => a + b, 0) / durations.length,
        median: durations.sort((a, b) => a - b)[Math.floor(durations.length / 2)]
      },
      memory: memoryDeltas.length > 0 ? {
        min: Math.min(...memoryDeltas),
        max: Math.max(...memoryDeltas),
        avg: memoryDeltas.reduce((a, b) => a + b, 0) / memoryDeltas.length
      } : null,
      iterations: measurements.length,
      failures: failed.length
    };
  },

  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  waitForElement: async (selector, timeout = 10000) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      const element = document.querySelector(selector);
      if (element) return element;
      await PerfUtils.wait(100);
    }
    throw new Error(`Element ${selector} not found`);
  },

  detectVersion: () => {
    // Try to detect if this is V1 or V2
    if (document.querySelector('[data-testid*="v2"], .modular-generator-redesign')) {
      return 'V2';
    } else if (document.querySelector('.modular-blocks-creator, [data-testid*="v1"]')) {
      return 'V1';
    } else {
      return 'Unknown';
    }
  }
};

/**
 * Benchmark Test Suites
 */
const BenchmarkSuites = {
  /**
   * Mix Style Loading Performance
   */
  async testMixStyleLoading() {
    PerfUtils.log('Benchmarking Mix Style Loading...');
    
    return await PerfUtils.measure('mix-style-loading', async () => {
      // Find and interact with mix style selector
      const selector = await PerfUtils.waitForElement(
        '[data-testid="mix-style-selector"], select[value*="style"], .mix-style-dropdown'
      );
      
      // Measure time to load options
      const startTime = performance.now();
      
      // Wait for options to be populated
      await PerfUtils.waitForElement('option, [role="option"]');
      
      const options = selector.querySelectorAll('option, [role="option"]');
      if (options.length < 2) {
        throw new Error('Mix styles not loaded properly');
      }
      
      return performance.now() - startTime;
    }, BENCHMARK_CONFIG.iterations);
  },

  /**
   * Collection Selection Performance
   */
  async testCollectionSelection() {
    PerfUtils.log('Benchmarking Collection Selection...');
    
    return await PerfUtils.measure('collection-selection', async () => {
      const selector = await PerfUtils.waitForElement(
        '[data-testid="collection-selector"], select[value*="collection"], .collection-dropdown'
      );
      
      const options = selector.querySelectorAll('option, [role="option"]');
      if (options.length === 0) {
        throw new Error('No collections available');
      }
      
      // Select first collection and measure response time
      const startTime = performance.now();
      options[0].click();
      
      // Wait for folder selector to appear (indicates collection loaded)
      await PerfUtils.waitForElement(
        '[data-testid="folder-selector"], select[value*="folder"], .folder-dropdown',
        15000
      );
      
      return performance.now() - startTime;
    }, Math.min(BENCHMARK_CONFIG.iterations, 3)); // Limit to 3 iterations for API calls
  },

  /**
   * Track Recommendation Performance
   */
  async testRecommendationPerformance() {
    PerfUtils.log('Benchmarking Track Recommendations...');
    
    return await PerfUtils.measure('track-recommendations', async () => {
      // Find a track position to click
      const canvas = await PerfUtils.waitForElement(
        '[data-testid="mix-canvas"], .mix-canvas, .timeline'
      );
      
      const positions = canvas.querySelectorAll(
        '[data-testid*="position"], .position, .track-position'
      );
      
      if (positions.length === 0) {
        throw new Error('No track positions available');
      }
      
      // Click position and measure recommendation loading time
      const startTime = performance.now();
      positions[0].click();
      
      // Wait for recommendations to appear
      await PerfUtils.waitForElement(
        '[data-testid*="recommendation"], .recommendation, .track-item',
        10000
      );
      
      return performance.now() - startTime;
    }, Math.min(BENCHMARK_CONFIG.iterations, 3));
  },

  /**
   * Block Addition Performance
   */
  async testBlockAddition() {
    PerfUtils.log('Benchmarking Block Addition...');
    
    return await PerfUtils.measure('block-addition', async () => {
      const blockLibrary = await PerfUtils.waitForElement(
        '[data-testid="block-library"], .block-library, .blocks-panel'
      );
      
      const blocks = blockLibrary.querySelectorAll(
        '[data-testid*="block"], .block-item, .harmonic-block'
      );
      
      if (blocks.length === 0) {
        throw new Error('No blocks available');
      }
      
      // Add block and measure time
      const startTime = performance.now();
      blocks[0].click();
      
      // Wait for block to appear in canvas
      await PerfUtils.wait(1000); // Give time for block addition
      
      return performance.now() - startTime;
    }, BENCHMARK_CONFIG.iterations);
  },

  /**
   * Auto-Population Performance
   */
  async testAutoPopulation() {
    PerfUtils.log('Benchmarking Auto-Population...');
    
    return await PerfUtils.measure('auto-population', async () => {
      // Look for auto-populate toggle
      const autoPopulateToggle = document.querySelector(
        '[data-testid*="auto-populate"], input[type="checkbox"], .auto-populate'
      );
      
      if (!autoPopulateToggle) {
        throw new Error('Auto-populate toggle not found');
      }
      
      // Enable auto-populate if not already enabled
      if (!autoPopulateToggle.checked) {
        autoPopulateToggle.click();
        await PerfUtils.wait(500);
      }
      
      // Add a block and measure auto-population time
      const blockLibrary = await PerfUtils.waitForElement(
        '[data-testid="block-library"], .block-library, .blocks-panel'
      );
      
      const blocks = blockLibrary.querySelectorAll(
        '[data-testid*="block"], .block-item, .harmonic-block'
      );
      
      if (blocks.length === 0) {
        throw new Error('No blocks available');
      }
      
      const startTime = performance.now();
      blocks[0].click();
      
      // Wait for auto-population to complete
      await PerfUtils.wait(3000); // Give time for auto-population
      
      return performance.now() - startTime;
    }, Math.min(BENCHMARK_CONFIG.iterations, 2)); // Limit iterations for heavy operations
  }
};

/**
 * Main benchmark execution
 */
async function runPerformanceBenchmarks() {
  PerfUtils.log('🚀 Starting Performance Benchmarks');
  PerfUtils.log('==================================');
  
  // Detect version
  benchmarkResults.version = PerfUtils.detectVersion();
  PerfUtils.log(`Detected Version: ${benchmarkResults.version}`);
  
  // Collect initial system info
  benchmarkResults.systemInfo = {
    userAgent: navigator.userAgent,
    memory: performance.memory ? {
      usedJSHeapSize: performance.memory.usedJSHeapSize,
      totalJSHeapSize: performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
    } : null,
    timing: performance.timing ? {
      domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
      pageLoad: performance.timing.loadEventEnd - performance.timing.navigationStart
    } : null
  };
  
  try {
    // Run benchmark suites
    benchmarkResults.metrics.mixStyleLoading = await BenchmarkSuites.testMixStyleLoading();
    await PerfUtils.wait(1000);
    
    benchmarkResults.metrics.collectionSelection = await BenchmarkSuites.testCollectionSelection();
    await PerfUtils.wait(1000);
    
    benchmarkResults.metrics.trackRecommendations = await BenchmarkSuites.testRecommendationPerformance();
    await PerfUtils.wait(1000);
    
    benchmarkResults.metrics.blockAddition = await BenchmarkSuites.testBlockAddition();
    await PerfUtils.wait(1000);
    
    benchmarkResults.metrics.autoPopulation = await BenchmarkSuites.testAutoPopulation();
    
  } catch (error) {
    PerfUtils.log(`❌ Benchmark execution error: ${error.message}`);
    benchmarkResults.error = error.message;
  }
  
  // Generate performance report
  generatePerformanceReport();
  
  return benchmarkResults;
}

/**
 * Generate performance report
 */
function generatePerformanceReport() {
  PerfUtils.log('\n📊 Performance Benchmark Results');
  PerfUtils.log('================================');
  PerfUtils.log(`Version: ${benchmarkResults.version}`);
  PerfUtils.log(`Timestamp: ${benchmarkResults.timestamp}`);
  
  Object.entries(benchmarkResults.metrics).forEach(([name, metrics]) => {
    if (metrics.status === 'success') {
      PerfUtils.log(`\n✅ ${name}:`);
      PerfUtils.log(`  Average: ${metrics.duration.avg.toFixed(2)}ms`);
      PerfUtils.log(`  Min: ${metrics.duration.min.toFixed(2)}ms`);
      PerfUtils.log(`  Max: ${metrics.duration.max.toFixed(2)}ms`);
      PerfUtils.log(`  Success Rate: ${metrics.successRate.toFixed(1)}%`);
      
      if (metrics.memory) {
        PerfUtils.log(`  Memory Delta: ${(metrics.memory.avg / 1024 / 1024).toFixed(2)}MB avg`);
      }
    } else {
      PerfUtils.log(`\n❌ ${name}: FAILED`);
      if (metrics.errors) {
        metrics.errors.forEach(error => PerfUtils.log(`    Error: ${error}`));
      }
    }
  });
  
  // Performance recommendations
  generateRecommendations();
}

/**
 * Generate performance recommendations
 */
function generateRecommendations() {
  const recommendations = [];
  
  Object.entries(benchmarkResults.metrics).forEach(([name, metrics]) => {
    if (metrics.status === 'success') {
      const avgTime = metrics.duration.avg;
      
      if (name === 'trackRecommendations' && avgTime > 2000) {
        recommendations.push(`Track recommendations are slow (${avgTime.toFixed(0)}ms). Consider implementing caching.`);
      }
      
      if (name === 'collectionSelection' && avgTime > 3000) {
        recommendations.push(`Collection selection is slow (${avgTime.toFixed(0)}ms). Consider optimizing API calls.`);
      }
      
      if (name === 'autoPopulation' && avgTime > 5000) {
        recommendations.push(`Auto-population is slow (${avgTime.toFixed(0)}ms). Consider background processing.`);
      }
      
      if (metrics.memory && metrics.memory.avg > 10 * 1024 * 1024) {
        recommendations.push(`${name} uses significant memory (${(metrics.memory.avg / 1024 / 1024).toFixed(1)}MB). Consider optimization.`);
      }
    }
  });
  
  if (recommendations.length > 0) {
    PerfUtils.log('\n💡 Performance Recommendations:');
    recommendations.forEach(rec => PerfUtils.log(`  - ${rec}`));
  } else {
    PerfUtils.log('\n🎉 Performance looks good! No specific recommendations.');
  }
  
  benchmarkResults.recommendations = recommendations;
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runPerformanceBenchmarks, benchmarkResults, PerfUtils };
} else {
  // Browser environment
  window.PerformanceBenchmarks = { runPerformanceBenchmarks, benchmarkResults, PerfUtils };
}

// Auto-setup in browser
if (typeof window !== 'undefined' && typeof module === 'undefined') {
  console.log('🚀 Performance Benchmarks Loaded');
  console.log('Run: PerformanceBenchmarks.runPerformanceBenchmarks() to start benchmarking');
}
