# Version 1 (Production) - Complete Feature Inventory

## 🎯 Core Architecture

### Main Component
- **File**: `frontend/src/components/mixes/generators/modular/ModularBlocksCreator.tsx`
- **Lines**: 1,017 lines
- **Type**: Monolithic component with integrated functionality

### Component Structure
```
ModularBlocksCreator (Main Container)
├── TopBar (Collection/Mix Style Selection)
├── BlockLibraryPanel (Block Selection)
├── MixCanvas (Visual Layout)
├── DetailsPanel (Track Recommendations)
├── EnergyCurveVisualization
├── HarmonicPathVisualization
└── CustomBlockDialog
```

## 🔧 State Management (Critical to Port)

### Collection State
- `selectedCollectionId: string` - Currently selected collection
- `selectedFolderId: string` - Currently selected folder
- `collectionTracks: Track[]` - Tracks from selected collection
- `folders: any[]` - Available folders in collection
- `isLoadingTracks: boolean` - Track loading state
- `isLoadingFolders: boolean` - Folder loading state

### Mix Style State (CRITICAL - Missing in V2)
- `mixStyles: any[]` - Available mix styles
- `selectedMixStyleId: string` - Currently selected mix style
- `isLoadingMixStyles: boolean` - Mix style loading state
- **Hardcoded Fallback**: 6 predefined mix styles with energy patterns

### Block Management State
- `mixBlocks: HarmonicBlock[]` - Current mix blocks
- `mixBridges: TransitionBridge[]` - Transition bridges
- `customBlocks: HarmonicBlock[]` - User-created blocks
- `selectedBlockId: string | null` - Currently selected block
- `selectedPositionId: string | null` - Currently selected position

### UI State
- `setDuration: number` - Mix duration (60 minutes default)
- `selectedTemplate: string` - Template selection
- `showEnergyCurve: boolean` - Energy visualization toggle
- `showHarmonicPath: boolean` - Harmonic visualization toggle
- `autoPopulate: boolean` - Auto-population toggle
- `zoomLevel: number` - Canvas zoom level
- `viewMode: "horizontal" | "vertical"` - Layout mode
- `trackRecommendations: Track[]` - Current recommendations

### Custom Block Creation State
- `newBlockName: string`
- `newBlockCode: string`
- `newBlockType: string`
- `newBlockKeyPattern: string[]`
- `newBlockEnergyProfile: number[]`
- `newBlockDuration: number`
- `newBlockColor: string`
- `newBlockGenreAffinity: string[]`

## 🌐 API Integration (CRITICAL - Must Port All)

### Collection APIs
```typescript
// Collection selection handler
handleCollectionSelect(collectionId: string)
├── getAllTracks() // For "all" selection
├── getCollectionTracks(collectionId)
└── getCollectionFolders(collectionId)

// Folder selection handler  
handleFolderSelect(folderId: string)
└── getCollectionTracks(collectionId, 1, 100, folderId)
```

### Track Recommendation System (CORE FEATURE)
```typescript
// Main recommendation logic in useEffect
fetchRecommendations()
├── Backend API Path (when collection selected):
│   └── getCompatibleTracks({
│       collection_id: selectedCollectionId,
│       key: position.key,
│       energy: position.energy,
│       folder_id: selectedFolderId,
│       mix_style_id: selectedMixStyleId,
│       generator_type: "modular",
│       limit: 10
│   })
└── Frontend Fallback Path:
    └── useLocalFiltering(position)
        ├── Key compatibility check
        ├── Energy sorting
        └── Top 10 selection
```

### Auto-Population System (CORE FEATURE)
```typescript
// Block auto-population in addBlockToMix()
autoPopulate && selectedCollectionId !== "all"
└── populateBlock({
    key_pattern: newBlock.keyPattern,
    energy_profile: newBlock.energyProfile,
    collection_id: selectedCollectionId,
    folder_id: selectedFolderId,
    mix_style_id: selectedMixStyleId,
    genre_affinity: newBlock.genreAffinity,
    generator_type: "modular"
})
```

## 🔗 Backend Endpoints (MUST PRESERVE)

### 1. Compatible Tracks API
- **Endpoint**: `GET /api/v1/modular/compatible-tracks`
- **Parameters**:
  - `collection_id: string`
  - `key: string` 
  - `energy: number`
  - `folder_id?: string`
  - `genre?: string`
  - `limit: number = 10`
- **Processing**: CamelotRules engine + weighted scoring
- **Response**: Scored and sorted compatible tracks

### 2. Populate Block API  
- **Endpoint**: `POST /api/v1/modular/populate-block`
- **Parameters**:
  - `collection_id: string`
  - `block: { keyPattern, energyProfile, genreAffinity }`
  - `folder_id?: string`
- **Processing**: Multi-position track matching
- **Response**: Populated positions with top 5 tracks each

## 🧮 Backend Logic (CRITICAL ALGORITHMS)

### CamelotRules Engine
```python
# Compatibility scoring algorithm
compatibility_score = camelot_rules.check_compatibility(key, track.key)
energy_score = 1.0 - (energy_diff / 100.0)
genre_score = 1.0 if genre_match else 0.5

# Weighted overall score
overall_score = (
    compatibility_score * 0.6 +  # Key compatibility (60%)
    energy_score * 0.3 +         # Energy match (30%)
    genre_score * 0.1            # Genre match (10%)
)
```

### Database Filtering
- Collection-based filtering: `Track.directory_id == collection_id`
- Folder-based filtering: `Track.folder_id == folder_id`
- Key validation: Skip tracks without key information
- Compatibility threshold: Only include tracks with score >= 0.5

## 📊 Data Structures (MUST MAINTAIN)

### HarmonicBlock Interface
```typescript
interface HarmonicBlock {
  id: string;
  code: string;
  name: string;
  type: "EB" | "PT" | "EP" | "ER" | "OP" | "CL" | "QS" | "CUSTOM";
  keyPattern: string[];
  energyProfile: number[];
  duration: number;
  trackPositions: TrackPosition[];
  color: string;
  genreAffinity?: string[];
  isCustom?: boolean;
}
```

### Track Recommendation Response
```typescript
interface TrackResponse {
  id: string;
  title: string;
  artist: string;
  key: string;
  bpm: number;
  energy: number;
  genre: string;
  duration: number;
  compatibility_score: number;
  energy_score: number;
  overall_score: number;
  transition_type: string;
}
```

## 🎛️ Mix Style Integration (MISSING IN V2)

### Hardcoded Mix Styles
1. **Modular Flexible**: Default flexible style
2. **Club Mix**: High-energy club progression
3. **Underground**: Underground-oriented style  
4. **Downtempo**: Low-energy progression
5. **Progressive**: Progressive build-up
6. **Minimal**: Minimal techno style

### Mix Style Properties
- `key_rules: string[]` - Allowed key transitions
- `energy_pattern: number[]` - Energy progression template
- `min_bpm/max_bpm: number` - BPM constraints
- `generator_types: string[]` - Compatible generators

## 🔄 Event Handlers (MUST PORT)

### Critical Functions
- `handleCollectionSelect(collectionId)` - Collection switching
- `handleFolderSelect(folderId)` - Folder switching  
- `handleMixStyleSelect(styleId)` - Mix style selection
- `addBlockToMix(blockId)` - Block addition with auto-population
- `assignTrackToPosition(trackId)` - Manual track assignment
- `useLocalFiltering(position)` - Frontend fallback logic

## ⚠️ Critical Dependencies

### Hooks
- `useCollections()` - Collection data management
- `useTemplateLoader()` - Template loading logic

### Services  
- `getCompatibleTracks()` - Track recommendation service
- `populateBlock()` - Block population service
- `getCollectionTracks()` - Collection track fetching
- `getCollectionFolders()` - Folder fetching
- `getMixStyles()` - Mix style fetching

### Utils
- `getKeyCompatibility()` - Key compatibility checking
- `getBlockTypeColor()` - Block color assignment

---

**Status**: 🟡 Complete Inventory
**Critical Missing in V2**: Mix Style Integration, Backend API Integration, CamelotRules Engine
**Next**: Create V2 Feature Inventory for comparison
