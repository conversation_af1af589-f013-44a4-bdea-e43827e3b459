timeline-horizontal:48 🛡️ Service Worker protection installed
timeline-horizontal:21 Found 0 service worker registrations to remove
timeline-horizontal:39 Found 0 caches to clear
timeline-horizontal:56  GET https://api.tempolabs.ai/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js net::ERR_CONNECTION_REFUSED
timeline-horizontal:88 Removing error message element: <script>​…​</script>​
timeline-horizontal:107 Cleaned error messages, refreshing data...
timeline-horizontal:1 Unchecked runtime.lastError: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
timeline-horizontal:1 Unchecked runtime.lastError: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 0, effectiveDuration: 60, pixelsPerSecond: 1, timelineWidth: 60, timelineHeight: 200, …}
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 0, effectiveDuration: 60, pixelsPerSecond: 1, timelineWidth: 60, timelineHeight: 200, …}
HorizontalTimelineMain.tsx:148 [HorizontalTimelineMain] Performance monitoring started
HorizontalTimelinePageContent.tsx:220 [HorizontalTimelinePageContent] Initialized coordinator and registered time update callback
HorizontalTimelinePageContent.tsx:231 [HorizontalTimelinePageContent] DEBUG: useEffect tracks - length: 0
HorizontalTimelinePageContent.tsx:235 [HorizontalTimelinePageContent] DEBUG: Calling timelineCoordinatorEnhanced.setTracks with 0 tracks
HorizontalTimelinePageContent.tsx:242 [HorizontalTimelinePageContent] DEBUG: Coordinator now has 0 tracks after sync
HorizontalTimelinePage.tsx:62 [HorizontalTimelinePage] Initializing horizontal timeline
HorizontalTimelinePage.tsx:68 [HorizontalTimelinePage] Initializing audio engine
HorizontalTimelineMain.tsx:154 [HorizontalTimelineMain] Performance monitoring stopped
HorizontalTimelinePageContent.tsx:225 [HorizontalTimelinePageContent] Unregistered time update callback
HorizontalTimelinePage.tsx:160 [HorizontalTimelinePage] Cleaning up horizontal timeline
HorizontalTimelinePageContent.tsx:220 [HorizontalTimelinePageContent] Initialized coordinator and registered time update callback
HorizontalTimelinePageContent.tsx:231 [HorizontalTimelinePageContent] DEBUG: useEffect tracks - length: 0
HorizontalTimelinePageContent.tsx:235 [HorizontalTimelinePageContent] DEBUG: Calling timelineCoordinatorEnhanced.setTracks with 0 tracks
HorizontalTimelinePageContent.tsx:242 [HorizontalTimelinePageContent] DEBUG: Coordinator now has 0 tracks after sync
HorizontalTimelinePage.tsx:62 [HorizontalTimelinePage] Initializing horizontal timeline
HorizontalTimelinePage.tsx:68 [HorizontalTimelinePage] Initializing audio engine
HorizontalTimelinePage.tsx:138 [HorizontalTimelinePage] Performance monitoring started
HorizontalTimelinePage.tsx:146 [HorizontalTimelinePage] Exposed timeline systems to window for testing
HorizontalTimelinePage.tsx:146 [HorizontalTimelinePage] Exposed timeline systems to window for testing
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 0, effectiveDuration: 60, pixelsPerSecond: 1, timelineWidth: 60, timelineHeight: 200, …}
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 0, effectiveDuration: 60, pixelsPerSecond: 1, timelineWidth: 60, timelineHeight: 200, …}
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 0, effectiveDuration: 278, pixelsPerSecond: 1, timelineWidth: 278, timelineHeight: 200, …}
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 1 (5 - Tutti Fluido - Cinza) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 0, effectiveDuration: 278, pixelsPerSecond: 1, timelineWidth: 278, timelineHeight: 200, …}
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 1 (5 - Tutti Fluido - Cinza) positioned at top: 0px (index: 0)
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=undefineds, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=undefineds, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:125 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 1 - 5 - Tutti Fluido - Cinza 🔥🔥🔥
HorizontalTrackLane.tsx:126 🔍 [HorizontalTrackLane] TRACK DATA: {id: 1, title: '5 - Tutti Fluido - Cinza', audioUrl: '/api/v1/tracks/stream?path=%2FUsers%2Fflorian%2FDo…F9A%20-%205%20-%20Tutti%20Fluido%20-%20Cinza.flac', color: '#ef4444', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:139 🎵 [HorizontalTrackLane] AUDIO ENGINE STATUS: {trackId: '1', isInAudioEngine: false, hasWaveform: false, audioEngineTrackCount: 'unknown'}
HorizontalTrackLane.tsx:157 [HorizontalTrackLane] Starting mount effect for track 1 (5 - Tutti Fluido - Cinza)
HorizontalTrackLane.tsx:204 [HorizontalTrackLane] Loading track 1 (5 - Tutti Fluido - Cinza)
HorizontalTimelinePageContent.tsx:231 [HorizontalTimelinePageContent] DEBUG: useEffect tracks - length: 1
HorizontalTimelinePageContent.tsx:235 [HorizontalTimelinePageContent] DEBUG: Calling timelineCoordinatorEnhanced.setTracks with 1 tracks
HorizontalTimelinePageContent.tsx:242 [HorizontalTimelinePageContent] DEBUG: Coordinator now has 1 tracks after sync
HorizontalTrackLane.tsx:325 [HorizontalTrackLane] Unmounting track 1
HorizontalTrackLane.tsx:341 [HorizontalTrackLane] StrictMode cleanup or track ID change - skipping unload for track 1
HorizontalTrackLane.tsx:125 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 1 - 5 - Tutti Fluido - Cinza 🔥🔥🔥
HorizontalTrackLane.tsx:126 🔍 [HorizontalTrackLane] TRACK DATA: {id: 1, title: '5 - Tutti Fluido - Cinza', audioUrl: '/api/v1/tracks/stream?path=%2FUsers%2Fflorian%2FDo…F9A%20-%205%20-%20Tutti%20Fluido%20-%20Cinza.flac', color: '#ef4444', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:139 🎵 [HorizontalTrackLane] AUDIO ENGINE STATUS: {trackId: '1', isInAudioEngine: false, hasWaveform: false, audioEngineTrackCount: 'unknown'}
HorizontalTrackLane.tsx:157 [HorizontalTrackLane] Starting mount effect for track 1 (5 - Tutti Fluido - Cinza)
HorizontalTrackLane.tsx:204 [HorizontalTrackLane] Loading track 1 (5 - Tutti Fluido - Cinza)
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:223 SEQUENTIAL_TRACK_LOAD_START: Track 1 (5 - Tutti Fluido - Cinza)
HorizontalTrackLane.tsx:226 [HorizontalTrackLane] Cleaning up any existing WaveSurfer instances for track 1
HorizontalTrackLane.tsx:249 🎵🎵🎵 [HorizontalTrackLane] CALLING LOADTRACKWITHBEATALIGNMENT FOR TRACK 1 (BEAT GRID ENABLED) 🎵🎵🎵
HorizontalTrackLane.tsx:253 🎯 [HorizontalTrackLane] Track 1 loaded with beat alignment successfully
HorizontalTrackLane.tsx:265 SEQUENTIAL_TRACK_LOAD_COMPLETE: Track 1 (5 - Tutti Fluido - Cinza) - Audio: true, Waveform: true, Ready: true
HorizontalTrackLane.tsx:269 [HorizontalTrackLane-1] DEBUG: Setting isWaveformLoaded=true, isLoading=false
HorizontalTrackLane.tsx:281 [HorizontalTrackLane] Synced track 1 zoom to 1px/s
HorizontalTrackLane.tsx:284 ✅✅✅ [HorizontalTrackLane] Successfully loaded track 1 (5 - Tutti Fluido - Cinza) ✅✅✅
HorizontalTrackLane.tsx:289 🎵 [HorizontalTrackLane] FINAL AUDIO ENGINE STATUS: {trackId: '1', finalIsInAudioEngine: true, finalHasWaveform: true, audioEngineTrackCount: 'unknown'}
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 278, effectiveDuration: 379, pixelsPerSecond: 1, timelineWidth: 379, timelineHeight: 240, …}
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 1 (5 - Tutti Fluido - Cinza) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 8 (5 - PedroCambulla - Orientation) positioned at top: 120px (index: 1)
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 278, effectiveDuration: 379, pixelsPerSecond: 1, timelineWidth: 379, timelineHeight: 240, …}
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 1 (5 - Tutti Fluido - Cinza) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 8 (5 - PedroCambulla - Orientation) positioned at top: 120px (index: 1)
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=undefineds, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=undefineds, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:125 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 8 - 5 - PedroCambulla - Orientation 🔥🔥🔥
HorizontalTrackLane.tsx:126 🔍 [HorizontalTrackLane] TRACK DATA: {id: 8, title: '5 - PedroCambulla - Orientation', audioUrl: '/api/v1/tracks/stream?path=%2FUsers%2Fflorian%2FDo…0-%205%20-%20PedroCambulla%20-%20Orientation.flac', color: '#3b82f6', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:139 🎵 [HorizontalTrackLane] AUDIO ENGINE STATUS: {trackId: '8', isInAudioEngine: false, hasWaveform: false, audioEngineTrackCount: 'unknown'}
HorizontalTrackLane.tsx:157 [HorizontalTrackLane] Starting mount effect for track 8 (5 - PedroCambulla - Orientation)
HorizontalTrackLane.tsx:204 [HorizontalTrackLane] Loading track 8 (5 - PedroCambulla - Orientation)
HorizontalTimelinePageContent.tsx:231 [HorizontalTimelinePageContent] DEBUG: useEffect tracks - length: 2
HorizontalTimelinePageContent.tsx:235 [HorizontalTimelinePageContent] DEBUG: Calling timelineCoordinatorEnhanced.setTracks with 2 tracks
HorizontalTimelinePageContent.tsx:242 [HorizontalTimelinePageContent] DEBUG: Coordinator now has 2 tracks after sync
HorizontalTrackLane.tsx:325 [HorizontalTrackLane] Unmounting track 8
HorizontalTrackLane.tsx:341 [HorizontalTrackLane] StrictMode cleanup or track ID change - skipping unload for track 8
HorizontalTrackLane.tsx:125 🔥🔥🔥 [HorizontalTrackLane] USEEFFECT TRIGGERED FOR TRACK 8 - 5 - PedroCambulla - Orientation 🔥🔥🔥
HorizontalTrackLane.tsx:126 🔍 [HorizontalTrackLane] TRACK DATA: {id: 8, title: '5 - PedroCambulla - Orientation', audioUrl: '/api/v1/tracks/stream?path=%2FUsers%2Fflorian%2FDo…0-%205%20-%20PedroCambulla%20-%20Orientation.flac', color: '#3b82f6', hasAudioUrl: true, …}
HorizontalTrackLane.tsx:139 🎵 [HorizontalTrackLane] AUDIO ENGINE STATUS: {trackId: '8', isInAudioEngine: false, hasWaveform: false, audioEngineTrackCount: 'unknown'}
HorizontalTrackLane.tsx:157 [HorizontalTrackLane] Starting mount effect for track 8 (5 - PedroCambulla - Orientation)
HorizontalTrackLane.tsx:204 [HorizontalTrackLane] Loading track 8 (5 - PedroCambulla - Orientation)
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=278s, snapToGrid=true, masterBPM=120, trackStartX=278px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=278s, snapToGrid=true, masterBPM=120, trackStartX=278px
HorizontalTrackLane.tsx:223 SEQUENTIAL_TRACK_LOAD_START: Track 8 (5 - PedroCambulla - Orientation)
HorizontalTrackLane.tsx:226 [HorizontalTrackLane] Cleaning up any existing WaveSurfer instances for track 8
HorizontalTrackLane.tsx:249 🎵🎵🎵 [HorizontalTrackLane] CALLING LOADTRACKWITHBEATALIGNMENT FOR TRACK 8 (BEAT GRID ENABLED) 🎵🎵🎵
HorizontalTrackLane.tsx:253 🎯 [HorizontalTrackLane] Track 8 loaded with beat alignment successfully
HorizontalTrackLane.tsx:265 SEQUENTIAL_TRACK_LOAD_COMPLETE: Track 8 (5 - PedroCambulla - Orientation) - Audio: true, Waveform: true, Ready: true
HorizontalTrackLane.tsx:269 [HorizontalTrackLane-8] DEBUG: Setting isWaveformLoaded=true, isLoading=false
HorizontalTrackLane.tsx:281 [HorizontalTrackLane] Synced track 8 zoom to 1px/s
HorizontalTrackLane.tsx:284 ✅✅✅ [HorizontalTrackLane] Successfully loaded track 8 (5 - PedroCambulla - Orientation) ✅✅✅
HorizontalTrackLane.tsx:289 🎵 [HorizontalTrackLane] FINAL AUDIO ENGINE STATUS: {trackId: '8', finalIsInAudioEngine: true, finalHasWaveform: true, audioEngineTrackCount: 'unknown'}
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=278s, snapToGrid=true, masterBPM=120, trackStartX=278px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=278s, snapToGrid=true, masterBPM=120, trackStartX=278px
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 657, effectiveDuration: 657, pixelsPerSecond: 1, timelineWidth: 657, timelineHeight: 240, …}
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 1 (5 - Tutti Fluido - Cinza) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 8 (5 - PedroCambulla - Orientation) positioned at top: 120px (index: 1)
HorizontalTimelineMain.tsx:126 [HorizontalTimelineMain] RENDER STATE: {totalDuration: 657, effectiveDuration: 657, pixelsPerSecond: 1, timelineWidth: 657, timelineHeight: 240, …}
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 1 (5 - Tutti Fluido - Cinza) positioned at top: 0px (index: 0)
HorizontalTimelineMain.tsx:485 [HorizontalTimelineMain] Track 8 (5 - PedroCambulla - Orientation) positioned at top: 120px (index: 1)
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 1 (5 - Tutti Fluido - Cinza): duration=278s, startTime=0s, snapToGrid=true, masterBPM=120, trackStartX=0px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=278s, snapToGrid=true, masterBPM=120, trackStartX=278px
HorizontalTrackLane.tsx:114 [HorizontalTrackLane] PROFESSIONAL POSITIONING - Track 8 (5 - PedroCambulla - Orientation): duration=379s, startTime=278s, snapToGrid=true, masterBPM=120, trackStartX=278px
HorizontalTimelinePageContent.tsx:261 [HorizontalTimelinePageContent] Memory cleanup - Active waveforms: 0, Memory: 211MB
