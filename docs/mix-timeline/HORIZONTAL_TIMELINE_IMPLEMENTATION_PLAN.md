# Horizontal Timeline Implementation Plan

*Last Updated: January 25, 2025*
*Status: 🚨 CRITICAL BUGS - Core Features Implemented But Not Functional*

**⚠️ URGENT: See `HORIZONTAL_TIMELINE_CURRENT_STATUS.md` and `NEXT_TASK_PROMPT.md` for critical issues requiring immediate attention.**

---

## 🎯 **IMPLEMENTATION OVERVIEW**

### **Objective** 🚧 IN PROGRESS
Building a horizontal timeline that maintains 100% feature parity with the current vertical timeline while allowing parallel development without breaking existing functionality.

### **Strategy** 🚧 EXECUTING
1. ✅ **Parallel Development**: Built alongside existing vertical timeline
2. ✅ **Service Reuse**: Used all existing services without modification
3. 🚧 **Layout Transformation**: Basic layout complete, adding advanced features
4. 🚧 **Gradual Migration**: Basic version deployed, adding full feature set

### **Current Status**
- ✅ **Demo Complete**: `/direct-demos/horizontal-timeline`
- ✅ **Real Implementation**: `/timeline-horizontal`
- ✅ **Smart Mix Integration**: Generates tracks and opens in horizontal timeline
- ✅ **Core Features Implemented**: Grid, ruler, dragging, zoom, scroll controls
- ✅ **Beat Matching Architecture**: Backend/frontend services exist
- 🚨 **CRITICAL ISSUES**: 6 major bugs preventing functionality (see status docs)

---

## 📋 **PHASE 1: BASIC TRACK LOADING** ✅ COMPLETE

### **Step 1.1: Create Demo Component** ✅ COMPLETE
**File**: `frontend/src/components/demos/HorizontalTimelineDemo.tsx`
- ✅ Created interactive horizontal timeline demo
- ✅ Implemented zoom controls (10px/s to 200px/s)
- ✅ Added click-to-seek playhead functionality
- ✅ Created track lane system with mock data
- ✅ Added vertical time ruler with markers

### **Step 1.2: Add Demo Route** ✅ COMPLETE
**File**: `frontend/src/App.tsx`
```typescript
// Added demo route for testing
<Route path="/direct-demos/horizontal-timeline" element={<HorizontalTimelineDemo />} />
```

### **Step 1.3: Core Components & Track Loading** ✅ COMPLETE
- ✅ HorizontalTimelinePage exists at `/timeline-horizontal`
- ✅ All horizontal components created (HorizontalTimelineMain, HorizontalTrackLane, VerticalTimeRuler, HorizontalPlayhead)
- ✅ Modified Smart Mix Generator to navigate to horizontal timeline
- ✅ **CRITICAL FIX**: Solved WaveSurfer container mounting issues
- ✅ **CRITICAL FIX**: Fixed track loading to match vertical timeline exactly
- ✅ **CRITICAL FIX**: Container always rendered, loading states as overlays
- ✅ Tracks load successfully with waveforms in horizontal lanes
- ✅ Basic audio playback integration working

---

## 📋 **PHASE 2: COMPLETE TIMELINE FEATURES** 🚧 IN PROGRESS

### **Missing Features to Implement**
Based on the architecture docs, we need to add:

#### **2.1: Master Grid & Global Project BPM** ✅ COMPLETE
- ✅ Global BPM display and controls (Professional Timeline Header)
- ✅ Master grid overlay on timeline (Professional Timeline Grid)
- ✅ BPM synchronization indicators (Beat/Bar ruler with BPM integration)
- ✅ Professional beat/bar ruler (Shows beat positions like 1.1, 1.2, 1.3, 1.4)
- ✅ Grid density controls (1/4, 1/8, 1/16 note divisions)
- ✅ Snap-to-grid system with visual feedback

#### **2.2: Marker Lane** ✅ COMPLETE
- ✅ Dedicated lane for timeline markers above tracks
- ✅ Add/edit/delete markers functionality
- ✅ Marker visualization and interaction
- ✅ Click to seek, drag to reposition
- ✅ Professional DAW-style appearance

#### **2.3: Tempo-Mapping Lane** ✅ COMPLETE
- ✅ Visual tempo changes across timeline with curve visualization
- ✅ BPM curve visualization with smooth interpolation
- ✅ Tempo adjustment controls with drag-and-drop points
- ✅ Integration with master BPM system
- ✅ Professional automation lane interface

#### **2.4: Group/Stem Lanes Expansion** ❌ TODO
- Expandable track groups
- Stem separation within tracks
- Group controls and management

#### **2.5: Floating Control Bar** ✅ COMPLETE
- ✅ Transport controls (play/pause/stop) - Integrated in Professional Timeline Header
- ✅ Master BPM controls with live editing
- ✅ Timeline navigation controls
- ✅ Grid and snap controls
- ✅ Master volume and effects controls
- ✅ Professional master audio processing

#### **2.6: Advanced Audio Clip Features** ✅ COMPLETE
- ✅ Beat grids and alignment (BeatGridRegions with Phase 3 beat alignment)
- ✅ Cue points (CuePointRegions with draggable markers)
- ✅ Segment regions (SegmentRegions for track structure)
- ✅ Professional beat grid visualization
- ✅ Timeline loops (Ableton-style loop brackets for manual mixing)
- ✅ Track loops (LoopRegions for individual track loops)
- ✅ Audio effects and processing integration

#### **2.7: Minimap Navigation** ❌ TODO
- Overview of entire timeline
- Click-to-navigate functionality
- Viewport indicator

#### **2.8: Track Interaction & Selection** ✅ COMPLETE
- ✅ Track selection states (visual feedback with ring and background)
- ✅ Context menus (right-click with time-aware actions)
- ✅ Track controls (mute/unmute, remove, volume, play/pause)
- ✅ Right sidebar integration (TrackDetailsPanel shows selected track)
- ❌ Drag and drop reordering (TODO: needs implementation)

### **Current Implementation Status**
- ✅ **Basic Structure**: Horizontal layout with track lanes
- ✅ **Track Loading**: WaveSurfer integration working
- ✅ **Track Interaction**: Selection, controls, context menus, right sidebar
- ✅ **Professional Timeline Foundation**: Complete professional DAW-style system
  - ✅ Professional Beat/Bar Ruler (shows 1.1, 1.2, 1.3, 1.4, 2.1, etc.)
  - ✅ Master BPM Controls (live editing, fine adjustment)
  - ✅ Professional Grid System (1/4, 1/8, 1/16 note divisions)
  - ✅ Snap-to-Grid System (visual feedback and snapping)
  - ✅ Transport Controls (play/pause/stop/seek)
  - ✅ Master Volume & Effects (professional audio processing)
- ✅ **Track-Level Features**: Professional audio visualization
  - ✅ Beat Grid Regions (individual track beat grids)
  - ✅ Segment Regions (track structure visualization)
  - ✅ Cue Point Regions (draggable markers)
  - ✅ Loop Regions (track-specific loops)
  - ✅ Beat Alignment Integration (Phase 3 system)
- ✅ **Timeline-Level Features**: Professional timeline functionality
  - ✅ Timeline Loop Brackets (Ableton-style loop system for manual mixing)
  - ✅ Marker Lane (dedicated timeline markers above tracks)
  - ✅ Tempo Mapping Lane (BPM automation with curve visualization)
- 🚧 **Advanced Features**: Only group/stem lanes remaining

### **Next Priority Implementation Order**
1. **🎯 IMMEDIATE**: Track interaction & selection (most critical for usability)
2. **🎯 HIGH**: Beat grids, cue points, loops (core DJ features)
3. **🎯 HIGH**: Floating control bar (transport controls)
4. **🎯 MEDIUM**: Master grid & BPM controls
5. **🎯 MEDIUM**: Marker lane and tempo mapping
6. **🎯 LOW**: Group/stem lanes and minimap

---

## 📋 **PHASE 3: IMMEDIATE NEXT STEPS** 🚧 READY TO START

### **Step 3.1: Track Interaction & Selection** ❌ TODO
**Priority**: 🔥 CRITICAL - Users need to interact with tracks

**Implementation Tasks**:
- Add track selection states and visual feedback
- Implement track context menus (same as vertical timeline)
- Add track controls (mute/solo/volume sliders)
- Enable drag and drop track reordering
- Add track removal functionality

**Files to Modify**:
- `HorizontalTrackLane.tsx` - Add selection states and controls
- `HorizontalTimelineMain.tsx` - Add track management logic

### **Step 1.2: Create HorizontalTimelinePage Component**
**File**: `frontend/src/pages/HorizontalTimelinePage.tsx`

```typescript
import React from 'react';
import { MixTimelineNewProvider } from '@/components/mixes/timeline/stores/TimelineStore';
import HorizontalTimelinePageContent from '@/components/mixes/timeline/components/horizontal/HorizontalTimelinePageContent';

const HorizontalTimelinePage: React.FC = () => {
  return (
    <MixTimelineNewProvider>
      <div className="h-screen w-full bg-background overflow-hidden">
        <HorizontalTimelinePageContent />
      </div>
    </MixTimelineNewProvider>
  );
};

export default HorizontalTimelinePage;
```

### **Step 1.3: Create Component Directory Structure**
```bash
mkdir -p frontend/src/components/mixes/timeline/components/horizontal
```

**Directory Structure**:
```
frontend/src/components/mixes/timeline/components/horizontal/
├── HorizontalTimelinePageContent.tsx
├── HorizontalTrackList.tsx
├── HorizontalTrackItem.tsx
├── VerticalTimeRuler.tsx
└── HorizontalPlayhead.tsx
```

### **Step 1.4: Create HorizontalTimelinePageContent**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/HorizontalTimelinePageContent.tsx`

Copy from `TimelinePage.tsx` and modify layout:
- Change main layout from vertical to horizontal
- Replace TimeRuler with VerticalTimeRuler
- Replace TrackList with HorizontalTrackList
- Keep ALL existing logic and state management

---

## 📋 **PHASE 2: VERTICAL TIME RULER**

### **Step 2.1: Create VerticalTimeRuler Component**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/VerticalTimeRuler.tsx`

```typescript
import React, { useRef } from 'react';

interface VerticalTimeRulerProps {
  totalDuration: number;
  currentTime: number;
  onSeek: (time: number) => void;
}

const VerticalTimeRuler: React.FC<VerticalTimeRulerProps> = ({
  totalDuration,
  currentTime,
  onSeek
}) => {
  const rulerRef = useRef<HTMLDivElement>(null);
  const pixelsPerSecond = 100; // Same scale as horizontal

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleRulerClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!rulerRef.current) return;
    const rect = rulerRef.current.getBoundingClientRect();
    const clickY = e.clientY - rect.top; // Y coordinate instead of X
    const time = clickY / pixelsPerSecond;
    onSeek(time);
  };

  const generateVerticalTicks = () => {
    if (totalDuration <= 0) return [];
    const ticks = [];
    const tickInterval = 10; // 10 second intervals

    for (let time = 0; time <= totalDuration; time += tickInterval) {
      const position = time * pixelsPerSecond;
      const isMajor = time % 60 === 0;

      ticks.push(
        <div
          key={time}
          className={`absolute left-0 w-${isMajor ? 4 : 2} border-t border-muted-foreground`}
          style={{ top: `${position}px` }}
        >
          {isMajor && (
            <div className="absolute left-4 top-1 text-xs text-muted-foreground transform -rotate-90 origin-left">
              {formatTime(time)}
            </div>
          )}
        </div>
      );
    }
    return ticks;
  };

  return (
    <div className="w-20 h-full border-r overflow-y-auto overflow-x-hidden">
      <div
        ref={rulerRef}
        className="relative w-full bg-background"
        style={{ height: `${Math.max(totalDuration * pixelsPerSecond, window.innerHeight || 800)}px` }}
        onClick={handleRulerClick}
      >
        {generateVerticalTicks()}
        {/* Current time indicator - horizontal line */}
        <div
          className="absolute left-0 w-full border-t-2 border-primary z-10 pointer-events-none"
          style={{ top: `${currentTime * pixelsPerSecond}px` }}
        />
      </div>
    </div>
  );
};

export default VerticalTimeRuler;
```

### **Step 2.2: Add Master Grid System**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/MasterGrid.tsx`

```typescript
import React from 'react';
import { useTimelineStore } from '../../stores/TimelineStore';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';

interface MasterGridProps {
  totalDuration: number;
  pixelsPerSecond: number;
}

const MasterGrid: React.FC<MasterGridProps> = ({ totalDuration, pixelsPerSecond }) => {
  // Get Master BPM from coordinator
  const masterBPM = timelineCoordinatorEnhanced.getMasterBPM() || 120;

  // Calculate grid spacing based on BPM
  const beatsPerSecond = masterBPM / 60;
  const pixelsPerBeat = pixelsPerSecond / beatsPerSecond;
  const pixelsPerBar = pixelsPerBeat * 4; // Assuming 4/4 time

  const generateGridLines = () => {
    const lines = [];
    const totalPixels = totalDuration * pixelsPerSecond;

    // Beat lines (lighter)
    for (let x = 0; x <= totalPixels; x += pixelsPerBeat) {
      lines.push(
        <div
          key={`beat-${x}`}
          className="absolute top-0 bottom-0 w-px bg-muted-foreground/20"
          style={{ left: `${x}px` }}
        />
      );
    }

    // Bar lines (stronger)
    for (let x = 0; x <= totalPixels; x += pixelsPerBar) {
      lines.push(
        <div
          key={`bar-${x}`}
          className="absolute top-0 bottom-0 w-px bg-muted-foreground/40"
          style={{ left: `${x}px` }}
        />
      );
    }

    return lines;
  };

  return (
    <div className="absolute inset-0 pointer-events-none">
      {generateGridLines()}
    </div>
  );
};

export default MasterGrid;
```

### **Step 2.3: Test Vertical Time Ruler**
1. Add VerticalTimeRuler to HorizontalTimelinePageContent
2. Test time display and seeking functionality
3. Verify current time indicator updates correctly
4. Test Master Grid integration with existing Master BPM

---

## 📋 **PHASE 3: HORIZONTAL TRACK LIST**

### **Step 3.1: Create HorizontalTrackList Component**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/HorizontalTrackList.tsx`

Copy from `TrackList.tsx` and modify:
- Change flex direction from column to row
- Update drag & drop direction to horizontal
- Keep ALL existing logic for track management

```typescript
import React, { useCallback, memo } from 'react';
import { useTimelineStore } from '../../stores/TimelineStore';
import HorizontalTrackItem from './HorizontalTrackItem';
import { DragDropContext, Droppable, DropResult } from 'react-beautiful-dnd';

const HorizontalTrackList: React.FC = memo(() => {
  const {
    tracks,
    moveTrack,
    selectedTrackId,
    setSelectedTrackId,
  } = useTimelineStore();

  const handleDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;
    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;
    moveTrack(sourceIndex, destinationIndex);
  }, [moveTrack]);

  return (
    <div className="flex-1 overflow-auto">
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="tracks" direction="horizontal">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="flex flex-row h-full" // Changed from flex-col
            >
              {tracks.length === 0 ? (
                <div className="flex-1 flex items-center justify-center p-8">
                  <p className="text-muted-foreground">
                    No tracks added yet. Use the track selector to add tracks.
                  </p>
                </div>
              ) : (
                tracks.map((track, index) => (
                  <HorizontalTrackItem
                    key={`${track.id}-${index}`}
                    track={track}
                    index={index}
                    isSelected={selectedTrackId === track.id.toString()}
                    onSelect={() => setSelectedTrackId(track.id.toString())}
                  />
                ))
              )}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
});

export default HorizontalTrackList;
```

### **Step 3.2: Test Horizontal Track List**
1. Add HorizontalTrackList to main layout
2. Test empty state display
3. Verify drag & drop functionality works horizontally

---

## 📋 **PHASE 4: HORIZONTAL TRACK ITEM**

### **Step 4.1: Create HorizontalTrackItem Component**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/HorizontalTrackItem.tsx`

Copy from `TrackItem.tsx` and modify layout:
- Change from horizontal card to vertical lane
- Move track info to header section
- Expand waveform to full lane height
- Keep ALL existing logic and functionality

```typescript
import React, { useRef, useEffect, useState, memo } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { Track } from '@/types/api/tracks';
import { Music, Trash2, Volume2, VolumeX, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { useTimelineStore } from '../../stores/TimelineStore';
import timelineCoordinatorEnhanced from '../../services/TimelineCoordinatorEnhanced';
import enhancedToneAudioEngine from '../../services/audio/EnhancedToneAudioEngine';
import { cleanupWaveSurferInstanceForTrack } from '@/utils/wavesurferCleanup';
import TrackContextMenu from '../core/TrackContextMenu';
import BeatGridRegions from '../editors/BeatGridRegions';
import SegmentRegions from '../editors/SegmentRegions';
import SpectrogramView from '../visualization/SpectrogramView';

interface HorizontalTrackItemProps {
  track: Track;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
}

const HorizontalTrackItem: React.FC<HorizontalTrackItemProps> = memo(({
  track,
  index,
  isSelected,
  onSelect
}) => {
  // Copy ALL existing state and logic from TrackItem.tsx
  const { removeTrack, showSpectrogram } = useTimelineStore();
  const waveformRef = useRef<HTMLDivElement>(null);
  const [isWaveformLoaded, setIsWaveformLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  // Copy ALL existing useEffect and handlers from TrackItem.tsx
  // ... (all existing logic)

  return (
    <Draggable draggableId={track.id.toString()} index={index}>
      {(provided) => (
        <TrackContextMenu trackId={track.id.toString()}>
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            className={`w-80 h-full border-r flex flex-col ${isSelected ? 'bg-muted' : 'bg-card'}`}
            onClick={onSelect}
          >
            {/* Track Header (Vertical) */}
            <div className="h-24 p-2 border-b bg-card flex-shrink-0">
              <div className="flex items-start gap-2">
                <div {...provided.dragHandleProps} className="cursor-grab p-1">
                  <Music size={16} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">{track.title}</div>
                  <div className="text-xs text-muted-foreground truncate">{track.artist}</div>
                  <div className="text-xs text-muted-foreground">
                    {track.bpm} BPM • {track.key || 'N/A'}
                  </div>
                </div>
              </div>
              
              {/* Track Controls */}
              <div className="flex gap-1 mt-2">
                <button onClick={handleMuteToggle} className="p-1 rounded hover:bg-background">
                  {isMuted ? <VolumeX size={14} /> : <Volume2 size={14} />}
                </button>
                <button onClick={handleZoomOut} className="p-1 rounded hover:bg-background">
                  <ZoomOut size={14} />
                </button>
                <button onClick={handleZoomReset} className="p-1 rounded hover:bg-background">
                  <RotateCcw size={14} />
                </button>
                <button onClick={handleZoomIn} className="p-1 rounded hover:bg-background">
                  <ZoomIn size={14} />
                </button>
                <button onClick={handleRemove} className="p-1 rounded hover:bg-background text-destructive">
                  <Trash2 size={14} />
                </button>
              </div>
            </div>

            {/* Waveform Area (Full Height) */}
            <div className="flex-1 relative overflow-hidden">
              <div
                ref={waveformRef}
                className="w-full h-full cursor-pointer"
                data-track-id={track.id.toString()}
                data-waveform-container="true"
                onClick={handleWaveformClick}
              >
                {/* Loading indicator */}
                {!isWaveformLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
                    <div className="text-xs text-muted-foreground animate-pulse">
                      Loading waveform...
                    </div>
                  </div>
                )}

                {/* Track visualization overlays */}
                <BeatGridRegions trackId={track.id.toString()} />
                <SegmentRegions trackId={track.id.toString()} />
              </div>

              {/* Spectrogram overlay */}
              {showSpectrogram && (
                <div className="absolute bottom-0 left-0 right-0 h-20">
                  <SpectrogramView trackId={track.id.toString()} height={80} />
                </div>
              )}
            </div>
          </div>
        </TrackContextMenu>
      )}
    </Draggable>
  );
});

export default HorizontalTrackItem;
```

### **Step 4.2: Test Track Item Integration**
1. Load tracks into horizontal timeline
2. Verify waveform loading and display
3. Test all track controls and interactions
4. Confirm beat grids and segments display correctly

---

## 📋 **PHASE 5: PLAYHEAD & NAVIGATION**

### **Step 5.1: Create HorizontalPlayhead Component**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/HorizontalPlayhead.tsx`

```typescript
import React from 'react';

interface HorizontalPlayheadProps {
  currentTime: number;
  totalDuration: number;
  onSeek: (time: number) => void;
}

const HorizontalPlayhead: React.FC<HorizontalPlayheadProps> = ({
  currentTime,
  totalDuration,
  onSeek
}) => {
  const pixelsPerSecond = 100;
  const position = currentTime * pixelsPerSecond;

  return (
    <div
      className="absolute top-0 bottom-0 w-0.5 bg-primary z-50 pointer-events-none"
      style={{ left: `${position}px` }}
    >
      {/* Playhead handle */}
      <div className="absolute -top-2 -left-2 w-4 h-4 bg-primary rounded-full pointer-events-auto cursor-grab" />
    </div>
  );
};

export default HorizontalPlayhead;
```

### **Step 5.2: Create Floating Control Bar**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/FloatingControlBar.tsx`

```typescript
import React from 'react';
import { ZoomIn, ZoomOut, Magnet, Navigation } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';

interface FloatingControlBarProps {
  horizontalZoom: number;
  verticalZoom: number;
  snapMode: 'grid' | 'transients' | 'markers' | 'cuepoints' | 'off';
  followPlayhead: boolean;
  onHorizontalZoomChange: (zoom: number) => void;
  onVerticalZoomChange: (zoom: number) => void;
  onSnapModeChange: (mode: string) => void;
  onFollowPlayheadToggle: () => void;
}

const FloatingControlBar: React.FC<FloatingControlBarProps> = ({
  horizontalZoom,
  verticalZoom,
  snapMode,
  followPlayhead,
  onHorizontalZoomChange,
  onVerticalZoomChange,
  onSnapModeChange,
  onFollowPlayheadToggle
}) => {
  return (
    <div className="fixed bottom-4 right-4 bg-background/90 backdrop-blur-sm border rounded-lg p-3 shadow-lg">
      <div className="flex items-center gap-3">
        {/* Horizontal Zoom */}
        <div className="flex items-center gap-2">
          <span className="text-xs">H:</span>
          <Slider
            value={[horizontalZoom]}
            min={10}
            max={200}
            step={5}
            onValueChange={(value) => onHorizontalZoomChange(value[0])}
            className="w-16"
          />
        </div>

        {/* Vertical Zoom */}
        <div className="flex items-center gap-2">
          <span className="text-xs">V:</span>
          <Slider
            value={[verticalZoom]}
            min={50}
            max={200}
            step={10}
            onValueChange={(value) => onVerticalZoomChange(value[0])}
            className="w-16"
          />
        </div>

        {/* Snap Toggle */}
        <Button
          variant={snapMode !== 'off' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onSnapModeChange(snapMode === 'off' ? 'grid' : 'off')}
        >
          <Magnet className="h-4 w-4" />
        </Button>

        {/* Follow Playhead */}
        <Button
          variant={followPlayhead ? 'default' : 'outline'}
          size="sm"
          onClick={onFollowPlayheadToggle}
        >
          <Navigation className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default FloatingControlBar;
```

### **Step 5.3: Integrate Playhead and Controls**
1. Add HorizontalPlayhead to main timeline container
2. Position as overlay spanning all track lanes
3. Add FloatingControlBar to timeline
4. Test seeking functionality and zoom controls
5. Verify playhead updates during playback
6. Test snap-to-grid functionality

---

## 📋 **PHASE 6: ENHANCED TRACK FEATURES** 🔄 PLANNED

### **Step 6.1: Master Grid System**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/MasterGrid.tsx`

```typescript
interface MasterGridProps {
  pixelsPerSecond: number;
  totalDuration: number;
  masterBPM: number;
  timelineHeight: number;
}

const MasterGrid: React.FC<MasterGridProps> = ({
  pixelsPerSecond,
  totalDuration,
  masterBPM,
  timelineHeight
}) => {
  const beatsPerSecond = masterBPM / 60;
  const pixelsPerBeat = pixelsPerSecond / beatsPerSecond;

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Vertical grid lines for beats */}
      {Array.from({ length: Math.ceil(totalDuration * beatsPerSecond) }).map((_, i) => (
        <div
          key={i}
          className="absolute top-0 bottom-0 w-px bg-grid-line opacity-30"
          style={{ left: `${i * pixelsPerBeat}px` }}
        />
      ))}
    </div>
  );
};
```

### **Step 6.2: Track Lane Headers**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/TrackLaneHeader.tsx`

```typescript
interface TrackLaneHeaderProps {
  track: Track;
  isSelected: boolean;
  onSelect: () => void;
  onReorder: (dragIndex: number, dropIndex: number) => void;
}

const TrackLaneHeader: React.FC<TrackLaneHeaderProps> = ({
  track,
  isSelected,
  onSelect,
  onReorder
}) => {
  return (
    <div className="w-48 h-full border-r bg-background flex flex-col p-2">
      {/* Track info display */}
      <div className="flex-1">
        <h3 className="font-medium text-sm truncate">{track.title}</h3>
        <p className="text-xs text-muted-foreground truncate">{track.artist}</p>
        <div className="flex gap-2 mt-1">
          <Badge variant="outline" className="text-xs">{track.bpm} BPM</Badge>
          <Badge variant="outline" className="text-xs">{track.key}</Badge>
        </div>
      </div>

      {/* Track controls */}
      <div className="flex gap-1 mt-2">
        <Button size="sm" variant="ghost">
          <Volume2 className="h-3 w-3" />
        </Button>
        <Button size="sm" variant="ghost">
          <Settings className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};
```

### **Step 6.3: Direct Manipulation Handles**
- Add trim handles for track start/end points
- Implement time-stretch handles with Alt+drag
- Add fade-in/fade-out curve handles
- Create volume automation "rubber band" line

### **Step 6.4: Testing Enhanced Features**
1. Test master grid alignment with BPM changes
2. Verify track lane header functionality
3. Test direct manipulation handles
4. Validate performance with enhanced features

---

## 📋 **PHASE 7: ADVANCED NAVIGATION** 🔄 PLANNED

### **Step 7.1: Floating Control Bar**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/FloatingControlBar.tsx`

```typescript
interface FloatingControlBarProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  snapMode: 'grid' | 'transients' | 'markers' | 'cue' | 'off';
  onSnapModeChange: (mode: string) => void;
  followPlayhead: boolean;
  onFollowPlayheadToggle: () => void;
}

const FloatingControlBar: React.FC<FloatingControlBarProps> = ({
  onZoomIn,
  onZoomOut,
  onResetZoom,
  snapMode,
  onSnapModeChange,
  followPlayhead,
  onFollowPlayheadToggle
}) => {
  return (
    <div className="fixed bottom-4 right-4 bg-background/90 backdrop-blur-sm border rounded-lg p-2 flex gap-2 z-50">
      {/* Zoom controls */}
      <div className="flex gap-1">
        <Button size="sm" variant="ghost" onClick={onZoomOut}>
          <ZoomOut className="h-3 w-3" />
        </Button>
        <Button size="sm" variant="ghost" onClick={onResetZoom}>
          <RotateCcw className="h-3 w-3" />
        </Button>
        <Button size="sm" variant="ghost" onClick={onZoomIn}>
          <ZoomIn className="h-3 w-3" />
        </Button>
      </div>

      {/* Snap mode selector */}
      <Select value={snapMode} onValueChange={onSnapModeChange}>
        <SelectTrigger className="w-24 h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="grid">Grid</SelectItem>
          <SelectItem value="transients">Transients</SelectItem>
          <SelectItem value="markers">Markers</SelectItem>
          <SelectItem value="cue">Cue Points</SelectItem>
          <SelectItem value="off">Off</SelectItem>
        </SelectContent>
      </Select>

      {/* Follow playhead toggle */}
      <Button
        size="sm"
        variant={followPlayhead ? "default" : "ghost"}
        onClick={onFollowPlayheadToggle}
      >
        <Navigation className="h-3 w-3" />
      </Button>
    </div>
  );
};
```

### **Step 7.2: Loop Brace System**
- Implement click and drag in time ruler to define loop regions
- Add fine-tune handles for precise 1/32nd note adjustments
- Create visual loop brace spanning all track lanes
- Add loop region highlighting across timeline

### **Step 7.3: Timeline Scrub Ruler**
- Create thin strip above main timeline area
- Implement click and drag for audio preview without moving playhead
- Add instant audio audition at any timeline position

### **Step 7.4: Session Management**
- Implement session-wide visual undo tree (modal panel)
- Create branching tree of all user actions
- Add non-destructive revert to any previous state
- Implement session snapshot bookmarks (save/recall zoom, loop, markers)

---

## 📋 **PHASE 8: PROFESSIONAL DJ FEATURES** 🔄 PLANNED

### **Step 8.1: Tempo-Mapping Lane**
**File**: `frontend/src/components/mixes/timeline/components/horizontal/TempoMappingLane.tsx`

```typescript
interface TempoMappingLaneProps {
  totalDuration: number;
  pixelsPerSecond: number;
  tempoPoints: TempoPoint[];
  onTempoPointAdd: (time: number, bpm: number) => void;
  onTempoPointUpdate: (id: string, bpm: number) => void;
  onTempoPointRemove: (id: string) => void;
}

const TempoMappingLane: React.FC<TempoMappingLaneProps> = ({
  totalDuration,
  pixelsPerSecond,
  tempoPoints,
  onTempoPointAdd,
  onTempoPointUpdate,
  onTempoPointRemove
}) => {
  return (
    <div className="h-16 border-b bg-muted/30 relative">
      <div className="absolute inset-0 flex items-center">
        <span className="text-xs text-muted-foreground ml-2">Tempo</span>

        {/* Tempo automation curve */}
        <svg className="absolute inset-0 w-full h-full">
          {/* Draw tempo curve between points */}
          {tempoPoints.map((point, index) => (
            <g key={point.id}>
              {/* Tempo point */}
              <circle
                cx={point.time * pixelsPerSecond}
                cy="50%"
                r="4"
                className="fill-primary cursor-pointer"
                onClick={() => onTempoPointUpdate(point.id, point.bpm)}
              />

              {/* Tempo line to next point */}
              {index < tempoPoints.length - 1 && (
                <line
                  x1={point.time * pixelsPerSecond}
                  y1="50%"
                  x2={tempoPoints[index + 1].time * pixelsPerSecond}
                  y2="50%"
                  className="stroke-primary stroke-2"
                />
              )}
            </g>
          ))}
        </svg>
      </div>
    </div>
  );
};
```

### **Step 8.2: EQ Hand-Off Visualization**
- Create three automation lanes for Low, Mid, High frequencies
- Implement visual representation of how EQ transitions between tracks
- Add drawable curves for custom EQ transitions

### **Step 8.3: Advanced Transition Features**
- Implement sequential track transitions with visual overlap regions
- Add copy/paste transitions with right-click context menu
- Create crossfade shape presets (Dip, Smooth, Power)

### **Step 8.4: Beat Alignment Integration**
- Add visual indicators for beat-aligned track starts
- Implement automatic beat boundary snapping when enabled
- Preserve existing beatmatching functionality

---

## 📋 **PHASE 9: TESTING & INTEGRATION**

### **Step 6.1: Smart Mix Generator Testing**
1. Generate mix using Smart Mix Generator
2. Navigate to horizontal timeline
3. Verify all tracks load correctly
4. Test all features work identically

### **Step 6.2: Feature Parity Testing**
1. Load same mix in both vertical and horizontal timelines
2. Compare functionality side-by-side
3. Verify audio performance is identical
4. Test all track features (beat grids, segments, etc.)

### **Step 6.3: Performance Testing**
1. Load large mixes (10+ tracks)
2. Monitor memory usage and performance
3. Test scrolling and zoom performance
4. Verify no regressions from vertical timeline

---

## 🚀 **DEPLOYMENT PLAN**

### **Step 7.1: Feature Flag Implementation**
```typescript
// Add feature flag for horizontal timeline
const useHorizontalTimeline = localStorage.getItem('horizontal-timeline') === 'true';
```

### **Step 7.2: Navigation Updates**
Update Smart Mix Generator to navigate to horizontal timeline:
```typescript
const targetRoute = useHorizontalTimeline ? '/timeline-horizontal' : '/timeline';
navigate(targetRoute);
```

### **Step 7.3: User Testing**
1. Deploy as optional feature
2. Gather user feedback
3. Iterate based on feedback
4. Gradually migrate users

---

## ✅ **SUCCESS CRITERIA**

- [ ] Horizontal timeline loads without errors
- [ ] All tracks display correctly in horizontal lanes
- [ ] Smart Mix Generator integration works
- [ ] Audio playback is identical to vertical timeline
- [ ] All track features work (beat grids, segments, etc.)
- [ ] Performance is equivalent to vertical timeline
- [ ] User can switch between vertical and horizontal layouts

---

*This implementation plan ensures a smooth transition to horizontal timeline while maintaining all existing functionality and allowing parallel development.*
