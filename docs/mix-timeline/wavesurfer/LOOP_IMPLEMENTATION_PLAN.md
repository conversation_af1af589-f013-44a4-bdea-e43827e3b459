# Loop Implementation Plan for Professional Beat Grid Demo

**⚠️ NOTE: This document describes the implementation in the Professional Beat Grid Demo. The current Mix Timeline only has loop panels working - waveform visualization is not yet implemented.**

## Overview

This document outlines the plan for implementing loops in the Professional Beat Grid Demo using WaveSurfer.js's Regions plugin. This implementation will follow the same approach used for cue points and segments, with proper state management and debounced rendering to prevent duplication issues.

## Goals

1. Create a native WaveSurfer.js implementation of loops using the Regions plugin
2. Implement UI for adding, editing, and removing loops
3. Support different loop types with distinct styling
4. Allow activating/deactivating loops during playback
5. Provide options for customizing appearance and behavior
6. Implement keyboard shortcuts for loop management

## Implementation Details

### 1. Data Structure

```typescript
export enum LoopType {
  STANDARD = 'standard',
  JUMP = 'jump',
  ROLL = 'roll',
  ECHO = 'echo',
  CUSTOM = 'custom'
}

export interface Loop {
  id: string;
  startTime: number;
  endTime: number;
  type: LoopType;
  active: boolean;
  label?: string;
  color?: string;
}
```

### 2. Component Structure

1. **LoopOverlay**: Main component for rendering loops using WaveSurfer.js Regions
2. **LoopEditor**: UI for adding, editing, and removing loops
3. **LoopList**: Displays a list of all loops with options to edit, remove, or activate/deactivate
4. **AddLoopDialog**: Dialog for adding a new loop
5. **EditLoopDialog**: Dialog for editing an existing loop

### 3. WaveSurfer.js Integration

We'll use the Regions plugin to create loops as regions with distinct styling:

```typescript
// Create a region for a loop
const region = regionsPlugin.addRegion({
  start: loop.startTime,
  end: loop.endTime,
  drag: true, // Allow dragging
  resize: true, // Allow resizing
  color: getLoopColor(loop.type, loop.active, loop.color),
  id: `loop-${loop.id}`,
  content: loop.label || getLoopLabel(loop.type),
  attributes: {
    type: 'loop',
    loopType: loop.type,
    active: loop.active ? 'true' : 'false'
  }
});
```

### 4. CSS Styling

```css
/* Base style for loop regions */
.wavesurfer-region[data-type="loop"] {
  background-color: currentColor !important;
  border: 2px dashed currentColor !important;
  z-index: 6 !important;
}

/* Style for active loops */
.wavesurfer-region[data-type="loop"][data-active="true"] {
  border-style: solid !important;
  z-index: 7 !important;
}

/* Style for loop labels */
.wavesurfer-region[data-type="loop"] .wavesurfer-region-content {
  color: #ffffff;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
}

/* Specific styles for different loop types */
.wavesurfer-region[data-loop-type="standard"] {
  color: rgba(0, 128, 255, 0.3) !important;
}

.wavesurfer-region[data-loop-type="standard"][data-active="true"] {
  color: rgba(0, 128, 255, 0.6) !important;
}

/* Additional styles for other loop types... */
```

## Implementation Steps

### Phase 1: Core Functionality

1. Add loop state to the Professional Beat Grid Demo:
   ```typescript
   const [loops, setLoops] = useState<Loop[]>([]);
   const [showLoops, setShowLoops] = useState<boolean>(true);
   const loopRegionsRef = useRef<any[]>([]);
   ```

2. Create functions for adding, updating, and removing loops:
   ```typescript
   const addLoop = (startTime: number, endTime: number, type: LoopType, label?: string, color?: string) => {
     const newLoop: Loop = {
       id: uuidv4(),
       startTime,
       endTime,
       type,
       active: false,
       label,
       color
     };
     setLoops(prev => [...prev, newLoop]);
   };

   const updateLoop = (id: string, updates: Partial<Loop>) => {
     setLoops(prev => prev.map(loop => loop.id === id ? { ...loop, ...updates } : loop));
   };

   const removeLoop = (id: string) => {
     setLoops(prev => prev.filter(loop => loop.id !== id));
   };

   const toggleLoopActive = (id: string) => {
     setLoops(prev => prev.map(loop =>
       loop.id === id ? { ...loop, active: !loop.active } :
       // Deactivate other loops if this one is being activated
       loop.active && !prev.find(l => l.id === id)?.active ? { ...loop, active: false } : loop
     ));
   };
   ```

3. Implement a robust approach for rendering loops that prevents duplication:
   ```typescript
   const rebuildLoops = () => {
     if (!wavesurferRef.current || !regionsPluginRef.current) return;

     // Remove all existing loop regions
     const allRegions = regionsPluginRef.current.getRegions();
     Object.values(allRegions).forEach(region => {
       if (region.id.startsWith('loop-')) {
         region.remove();
       }
     });

     // Clear our reference array
     loopRegionsRef.current = [];

     // Only create regions if loops should be shown
     if (!showLoops) return;

     // Create new regions for all loops
     loops.forEach(loop => {
       const region = regionsPluginRef.current!.addRegion({
         start: loop.startTime,
         end: loop.endTime,
         drag: true,
         resize: true,
         color: getLoopColor(loop.type, loop.active, loop.color),
         id: `loop-${loop.id}`,
         content: loop.label || getLoopLabel(loop.type),
         attributes: {
           type: 'loop',
           loopType: loop.type,
           active: loop.active ? 'true' : 'false'
         }
       });

       // Add event listeners for interaction
       region.on('update-end', () => {
         updateLoop(loop.id, { startTime: region.start, endTime: region.end });
       });

       region.on('click', () => {
         toggleLoopActive(loop.id);
       });

       loopRegionsRef.current.push(region);
     });
   };
   ```

4. Add a debounced effect to prevent rapid rebuilds:
   ```typescript
   useEffect(() => {
     if (!isReady) return;

     const timer = setTimeout(() => {
       rebuildLoops();
     }, 50); // 50ms debounce

     return () => clearTimeout(timer);
   }, [loops, showLoops, isReady]);
   ```

5. Add helper functions for loop styling:
   ```typescript
   const getLoopColor = (type: LoopType, active: boolean, customColor?: string): string => {
     if (customColor) return customColor;

     const opacity = active ? 0.6 : 0.3;

     switch (type) {
       case LoopType.STANDARD: return `rgba(0, 128, 255, ${opacity})`;
       case LoopType.JUMP: return `rgba(255, 128, 0, ${opacity})`;
       case LoopType.ROLL: return `rgba(128, 0, 255, ${opacity})`;
       case LoopType.ECHO: return `rgba(0, 255, 128, ${opacity})`;
       case LoopType.CUSTOM: return `rgba(128, 128, 128, ${opacity})`;
       default: return `rgba(128, 128, 128, ${opacity})`;
     }
   };

   const getLoopLabel = (type: LoopType): string => {
     switch (type) {
       case LoopType.STANDARD: return 'Loop';
       case LoopType.JUMP: return 'Jump Loop';
       case LoopType.ROLL: return 'Roll Loop';
       case LoopType.ECHO: return 'Echo Loop';
       case LoopType.CUSTOM: return 'Custom Loop';
       default: return 'Loop';
     }
   };
   ```

### Phase 2: User Interface

1. Add UI controls for loop management:
   - Button to add a loop at the current playhead position
   - Dialog for selecting loop type, start/end times, and label
   - List of existing loops with edit, delete, and activate/deactivate options
   - Context menu for right-clicking on the waveform to add a loop
   - 'L' key shortcut to add a loop at the current playhead position

2. Create the AddLoopDialog component:
   ```tsx
   const AddLoopDialog: React.FC<{
     isOpen: boolean;
     onClose: () => void;
     onAdd: (type: LoopType, startTime: number, endTime: number, label?: string, color?: string) => void;
     currentTime: number;
   }> = ({ isOpen, onClose, onAdd, currentTime }) => {
     const [type, setType] = useState<LoopType>(LoopType.STANDARD);
     const [startTime, setStartTime] = useState<number>(currentTime);
     const [endTime, setEndTime] = useState<number>(currentTime + 4); // Default 4 seconds for loops
     const [label, setLabel] = useState<string>('');
     const [color, setColor] = useState<string>('');

     // Reset form when dialog opens
     useEffect(() => {
       if (isOpen) {
         setType(LoopType.STANDARD);
         setStartTime(currentTime);
         setEndTime(currentTime + 4);
         setLabel('');
         setColor('');
       }
     }, [isOpen, currentTime]);

     const handleSubmit = (e: React.FormEvent) => {
       e.preventDefault();
       onAdd(type, startTime, endTime, label || undefined, color || undefined);
       onClose();
     };

     return (
       <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
         <DialogContent>
           {/* Dialog content with form fields */}
         </DialogContent>
       </Dialog>
     );
   };
   ```

3. Create the EditLoopDialog component:
   ```tsx
   const EditLoopDialog: React.FC<{
     isOpen: boolean;
     onClose: () => void;
     onUpdate: (id: string, updates: Partial<Loop>) => void;
     loop: Loop | null;
   }> = ({ isOpen, onClose, onUpdate, loop }) => {
     const [type, setType] = useState<LoopType>(LoopType.STANDARD);
     const [startTime, setStartTime] = useState<number>(0);
     const [endTime, setEndTime] = useState<number>(0);
     const [active, setActive] = useState<boolean>(false);
     const [label, setLabel] = useState<string>('');
     const [color, setColor] = useState<string>('');

     // Update form when loop changes
     useEffect(() => {
       if (loop) {
         setType(loop.type);
         setStartTime(loop.startTime);
         setEndTime(loop.endTime);
         setActive(loop.active);
         setLabel(loop.label || '');
         setColor(loop.color || '');
       }
     }, [loop]);

     const handleSubmit = (e: React.FormEvent) => {
       e.preventDefault();
       if (!loop) return;

       onUpdate(loop.id, {
         type,
         startTime,
         endTime,
         active,
         label: label || undefined,
         color: color || undefined
       });
       onClose();
     };

     return (
       <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
         <DialogContent>
           {/* Dialog content with form fields */}
         </DialogContent>
       </Dialog>
     );
   };
   ```

4. Create the LoopList component:
   ```tsx
   const LoopList: React.FC<{
     loops: Loop[];
     onEdit: (id: string) => void;
     onDelete: (id: string) => void;
     onSeek: (time: number) => void;
     onToggleActive: (id: string) => void;
   }> = ({ loops, onEdit, onDelete, onSeek, onToggleActive }) => {
     return (
       <div className="space-y-2">
         <h3 className="text-sm font-medium">Loops</h3>
         {loops.length === 0 ? (
           <p className="text-sm text-muted-foreground">No loops added yet.</p>
         ) : (
           <div className="space-y-1">
             {loops
               .sort((a, b) => a.startTime - b.startTime)
               .map((loop) => (
                 <div
                   key={loop.id}
                   className="flex items-center justify-between p-2 rounded-md bg-muted/50 hover:bg-muted"
                 >
                   <div className="flex items-center gap-2">
                     <div
                       className="w-3 h-3 rounded-full"
                       style={{ backgroundColor: getLoopColor(loop.type, loop.active, loop.color) }}
                     />
                     <span className="text-sm font-medium">
                       {loop.label || getLoopLabel(loop.type)}
                     </span>
                     <span className="text-xs text-muted-foreground">
                       {formatTime(loop.startTime)} - {formatTime(loop.endTime)}
                     </span>
                     <span className={`text-xs px-1 rounded ${loop.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                       {loop.active ? 'Active' : 'Inactive'}
                     </span>
                   </div>
                   <div className="flex items-center gap-1">
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onSeek(loop.startTime)}
                       title="Seek to loop start"
                     >
                       <Play className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant={loop.active ? "default" : "ghost"}
                       onClick={() => onToggleActive(loop.id)}
                       title={loop.active ? "Deactivate loop" : "Activate loop"}
                     >
                       <Repeat className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onEdit(loop.id)}
                       title="Edit loop"
                     >
                       <Edit className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onDelete(loop.id)}
                       title="Delete loop"
                     >
                       <Trash className="h-3 w-3" />
                     </Button>
                   </div>
                 </div>
               ))}
           </div>
         )}
       </div>
     );
   };
   ```

5. Update the context menu to include loop options:
   ```tsx
   // Add to WaveformContextMenu props
   interface WaveformContextMenuProps {
     // Existing props...
     onAddLoop: () => void;
   }

   // Add to WaveformContextMenu component
   <Button
     variant="ghost"
     size="sm"
     className="w-full justify-start text-sm"
     onClick={onAddLoop}
   >
     <Repeat className="h-4 w-4 mr-2" />
     Add Loop
   </Button>
   ```

### Phase 3: Integration and Testing

1. Integrate all components into the Professional Beat Grid Demo:
   - Add loop management panel to the right sidebar
   - Implement context menu for loop operations
   - Add keyboard shortcuts for common loop actions

2. Add event listeners for keyboard shortcuts:
   - `L` key to add a loop at the current playhead position
   - Right-click context menu for adding loops at specific positions

3. Test all functionality:
   - Adding loops via button, context menu, and keyboard shortcut
   - Editing loops via the edit dialog
   - Removing loops via the delete button
   - Activating/deactivating loops via the toggle button
   - Dragging loop boundaries to resize them
   - Seeking to loops by clicking on them in the list

### Phase 4: Refinement

1. Improve user experience:
   - Add visual feedback for loop operations
   - Improve loop styling for better visibility
   - Add tooltips for all loop controls
   - Implement proper error handling for loop operations

2. Optimize performance:
   - Implement debounced rendering to prevent UI freezes
   - Add proper cleanup of event listeners and regions
   - Optimize state updates to minimize re-renders

## Current Status and Next Steps

### Completed Features ✅

1. ✅ Core loop data structures and state management
2. ✅ Robust loop rendering with WaveSurfer.js Regions
3. ✅ UI for adding, editing, and removing loops
4. ✅ Context menu for loop operations
5. ✅ Keyboard shortcuts for common loop actions (L key)
6. ✅ Loop list with sorting and filtering
7. ✅ Loop styling based on type and active state with pulsing animation
8. ✅ Draggable loop boundaries for resizing
9. ✅ Loop activation/deactivation during playback
10. ✅ Ctrl/Cmd+click functionality for loop activation
11. ✅ Actual audio looping functionality with timeupdate and finish event handlers

### Implementation Timeline

1. ✅ **Day 1**: Implemented core data structures and state management
2. ✅ **Day 2**: Created UI components and dialogs
3. ✅ **Day 3**: Implemented loop rendering and interaction
4. ✅ **Day 4**: Added keyboard shortcuts and context menu integration
5. ✅ **Day 5**: Tested and refined the implementation

## Integration with Audio Engine

The audio engine integration has been completed:

1. ✅ Implemented a dual approach for loop monitoring:
   - Primary: Direct event listener for timeupdate events
   - Backup: Interval-based monitoring for additional reliability
2. ✅ Added proper handling of loop activation/deactivation during playback
3. ✅ Implemented finish event handling to restart active loops when the track ends
4. ✅ Ensured proper synchronization between the visual representation and the audio playback
5. ✅ Added visual feedback for active loops with pulsing animation
6. ✅ Implemented proper cleanup of event listeners and intervals

### Technical Implementation Details

The loop monitoring system uses a combination of approaches for maximum reliability:

1. **Event-based monitoring**: Uses WaveSurfer's timeupdate event for precise timing
2. **Interval-based backup**: Uses a short interval (20ms) as a fallback mechanism
3. **Finish event handling**: Handles the case when the track reaches its end

```typescript
// Set up active loop monitoring
const setupActiveLoop = (loop: Loop) => {
  // Clear any existing loop monitoring
  clearActiveLoop();

  if (!wavesurferRef.current) return;

  // First, add a direct event listener for timeupdate
  const handleTimeUpdate = (currentTime: number) => {
    // Check if we've passed the end of the loop
    if (currentTime >= loop.endTime) {
      // Find the loop again to make sure it's still active
      const activeLoop = loops.find(l => l.id === loop.id && l.active);

      if (activeLoop) {
        // Jump back to the start of the loop
        wavesurferRef.current.setTime(loop.startTime);
      }
    }
  };

  // Add the event listener
  wavesurferRef.current.on('timeupdate', handleTimeUpdate);

  // Also set up an interval as a backup mechanism
  const intervalId = window.setInterval(() => {
    if (!wavesurferRef.current || !wavesurferRef.current.isPlaying()) return;

    const currentTime = wavesurferRef.current.getCurrentTime();

    if (currentTime >= loop.endTime) {
      const activeLoop = loops.find(l => l.id === loop.id && l.active);

      if (activeLoop) {
        wavesurferRef.current.setTime(loop.startTime);
      } else {
        clearActiveLoop();
      }
    }
  }, 20);

  // Store references for cleanup
  loopMonitoringRef.current = intervalId;
  loopEventListenerRef.current = handleTimeUpdate;
};
```

## Conclusion

The loop functionality has been successfully implemented in the Professional Beat Grid Demo. By following the same pattern used for segments and cue points, we've created a consistent and maintainable implementation that integrates seamlessly with the existing codebase.

The implementation includes:
- Visual representation of loops using WaveSurfer.js Regions
- UI for adding, editing, and removing loops
- Keyboard shortcuts and context menu integration
- Actual audio looping functionality with reliable monitoring
- Visual feedback for active loops with pulsing animation

The next step is to integrate this functionality into the main Mix Timeline component, leveraging the same approach and code patterns established in the Professional Beat Grid Demo.
