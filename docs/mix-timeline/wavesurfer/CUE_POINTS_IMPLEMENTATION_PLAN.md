# Cue Point Implementation Plan for Professional Beat Grid Demo

**⚠️ NOTE: This document describes the implementation in the Professional Beat Grid Demo. The current Mix Timeline only has cue point panels working - waveform visualization is not yet implemented.**

## Overview

This document outlines the plan for implementing cue points in the Professional Beat Grid Demo using WaveSurfer.js's Regions plugin. Once implemented and tested, this component will be integrated into the Mix Timeline.

## Goals

1. Create a native WaveSurfer.js implementation of cue points using the Regions plugin
2. Implement UI for adding, editing, and removing cue points
3. Support different cue point types with distinct styling
4. Allow dragging cue points for repositioning
5. Provide options for customizing appearance and behavior

## Implementation Details

### 1. Data Structure

```typescript
interface CuePoint {
  id: string;
  time: number;
  type: CuePointType;
  label?: string;
  color?: string;
}

enum CuePointType {
  INTRO_START = 'intro_start',
  INTRO_END = 'intro_end',
  OUTRO_START = 'outro_start',
  OUTRO_END = 'outro_end',
  DROP = 'drop',
  BREAKDOWN = 'breakdown',
  CUSTOM = 'custom'
}
```

### 2. Component Structure

1. **CuePointOverlay**: Main component for rendering cue points using WaveSurfer.js Regions
2. **CuePointEditor**: UI for adding, editing, and removing cue points
3. **CuePointList**: Displays a list of all cue points with options to edit or remove
4. **AddCuePointDialog**: Dialog for adding a new cue point
5. **EditCuePointDialog**: Dialog for editing an existing cue point

### 3. WaveSurfer.js Integration

We'll use the Regions plugin to create cue points as thin regions with distinct styling:

```typescript
// Create a region for a cue point
const region = regionsPlugin.addRegion({
  start: cuePoint.time,
  end: cuePoint.time + 0.01, // Small width to ensure visibility
  drag: true, // Allow dragging
  resize: false, // Don't allow resizing
  color: getCuePointColor(cuePoint.type, cuePoint.color),
  id: `cue-${cuePoint.id}`,
  content: cuePoint.label || getCuePointLabel(cuePoint.type),
  attributes: {
    type: 'cue-point',
    cuePointType: cuePoint.type
  }
});
```

### 4. CSS Styling

```css
/* Base style for cue point regions */
.wavesurfer-region[data-type="cue-point"] {
  border-left: 2px solid currentColor !important;
  background-color: transparent !important;
  width: 0 !important;
  height: 100% !important;
  z-index: 10 !important;
}

/* Style for cue point labels */
.wavesurfer-region[data-type="cue-point"] .wavesurfer-region-content {
  color: #ffffff;
  position: absolute;
  top: -20px;
  left: -5px;
  font-size: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
}

/* Specific styles for different cue point types */
.wavesurfer-region[data-cue-point-type="intro_start"] {
  border-left-color: #4CAF50 !important;
}

.wavesurfer-region[data-cue-point-type="intro_end"] {
  border-left-color: #8BC34A !important;
}

.wavesurfer-region[data-cue-point-type="outro_start"] {
  border-left-color: #FF9800 !important;
}

.wavesurfer-region[data-cue-point-type="outro_end"] {
  border-left-color: #F44336 !important;
}

.wavesurfer-region[data-cue-point-type="drop"] {
  border-left-color: #E91E63 !important;
}

.wavesurfer-region[data-cue-point-type="breakdown"] {
  border-left-color: #9C27B0 !important;
}

.wavesurfer-region[data-cue-point-type="custom"] {
  border-left-color: #2196F3 !important;
}
```

## Implementation Steps

### Phase 1: Core Functionality ✅

1. Added cue point state to the Professional Beat Grid Demo:
   ```typescript
   const [cuePoints, setCuePoints] = useState<CuePoint[]>([]);
   const cuePointRegionsRef = useRef<any[]>([]);
   ```

2. Created functions for adding, updating, and removing cue points with improved state management:
   ```typescript
   const addCuePoint = (time: number, type: CuePointType, label?: string, color?: string) => {
     if (!wavesurferRef.current || !regionsPluginRef.current) return;

     const newCuePoint: CuePoint = {
       id: uuidv4(),
       time,
       type,
       label,
       color
     };

     // Just update the state - the effect will handle rendering
     setCuePoints(prev => [...prev, newCuePoint]);

     console.log(`[ProfessionalBeatGridDemo] Added cue point at ${formatTime(time)}`);
   };

   const updateCuePoint = (id: string, updates: Partial<CuePoint>) => {
     // Just update the state - the effect will handle rendering
     setCuePoints(prev => prev.map(cp => cp.id === id ? { ...cp, ...updates } : cp));

     console.log(`[ProfessionalBeatGridDemo] Updated cue point ${id}`);
   };

   const removeCuePoint = (id: string) => {
     // Just update the state - the effect will handle rendering
     setCuePoints(prev => prev.filter(cp => cp.id !== id));

     console.log(`[ProfessionalBeatGridDemo] Removed cue point ${id}`);
   };
   ```

3. Implemented a robust approach for rendering cue points that prevents duplication:
   ```typescript
   // Completely rebuild all cue points
   const rebuildCuePoints = () => {
     if (!wavesurferRef.current || !regionsPluginRef.current) return;

     console.log('[ProfessionalBeatGridDemo] Rebuilding all cue points');

     // Remove all existing cue point regions
     const allRegions = regionsPluginRef.current.getRegions();
     Object.values(allRegions).forEach(region => {
       if (region.id.startsWith('cue-')) {
         region.remove();
       }
     });

     // Clear our reference array
     cuePointRegionsRef.current = [];

     // Only create regions if cue points should be shown
     if (!showCuePoints) return;

     // Create new regions for all cue points
     cuePoints.forEach(cuePoint => {
       const region = regionsPluginRef.current!.addRegion({
         start: cuePoint.time,
         end: cuePoint.time + 0.01, // Small width to ensure visibility
         drag: true, // Allow dragging
         resize: false, // Don't allow resizing
         color: getCuePointColor(cuePoint.type, cuePoint.color),
         id: `cue-${cuePoint.id}`,
         content: cuePoint.label || getCuePointLabel(cuePoint.type),
         attributes: {
           type: 'cue-point',
           cuePointType: cuePoint.type
         }
       });

       // Add event listener for drag end to update cue point position
       region.on('update-end', () => {
         console.log(`Cue point ${cuePoint.id} moved to ${region.start}`);

         // Update the cue point in state without triggering a rebuild
         setCuePoints(prev =>
           prev.map(cp =>
             cp.id === cuePoint.id
               ? { ...cp, time: region.start }
               : cp
           )
         );
       });

       // Add event listener for click to seek to cue point
       region.on('click', () => {
         if (wavesurferRef.current) {
           wavesurferRef.current.seekTo(region.start / wavesurferRef.current.getDuration());
         }
       });

       cuePointRegionsRef.current.push(region);
     });

     console.log(`[ProfessionalBeatGridDemo] Created ${cuePointRegionsRef.current.length} cue point regions`);
   };
   ```

4. Added a debounced effect to prevent rapid rebuilds:
   ```typescript
   // Update cue points when they change (with debounce)
   useEffect(() => {
     if (!isReady) return;

     // Use a debounce to prevent rapid rebuilds
     const timer = setTimeout(() => {
       rebuildCuePoints();
     }, 50); // 50ms debounce

     return () => clearTimeout(timer);
   }, [cuePoints, showCuePoints, isReady]);
   ```

4. Add helper functions for cue point styling:
   ```typescript
   const getCuePointColor = (type: CuePointType, customColor?: string): string => {
     if (customColor) return customColor;

     switch (type) {
       case CuePointType.INTRO_START: return 'rgba(76, 175, 80, 0.9)';
       case CuePointType.INTRO_END: return 'rgba(139, 195, 74, 0.9)';
       case CuePointType.OUTRO_START: return 'rgba(255, 152, 0, 0.9)';
       case CuePointType.OUTRO_END: return 'rgba(244, 67, 54, 0.9)';
       case CuePointType.DROP: return 'rgba(233, 30, 99, 0.9)';
       case CuePointType.BREAKDOWN: return 'rgba(156, 39, 176, 0.9)';
       case CuePointType.CUSTOM: return 'rgba(33, 150, 243, 0.9)';
       default: return 'rgba(33, 150, 243, 0.9)';
     }
   };

   const getCuePointLabel = (type: CuePointType): string => {
     switch (type) {
       case CuePointType.INTRO_START: return 'Intro Start';
       case CuePointType.INTRO_END: return 'Intro End';
       case CuePointType.OUTRO_START: return 'Outro Start';
       case CuePointType.OUTRO_END: return 'Outro End';
       case CuePointType.DROP: return 'Drop';
       case CuePointType.BREAKDOWN: return 'Breakdown';
       case CuePointType.CUSTOM: return 'Cue';
       default: return 'Cue';
     }
   };
   ```

5. Add CSS styles for cue points:
   ```typescript
   const addCuePointStyles = () => {
     const styleId = 'cue-point-styles';
     let styleEl = document.getElementById(styleId);

     if (!styleEl) {
       styleEl = document.createElement('style');
       styleEl.id = styleId;
       document.head.appendChild(styleEl);
     }

     styleEl.textContent = `
       /* Base style for cue point regions */
       .wavesurfer-region[data-type="cue-point"] {
         border-left: 2px solid currentColor !important;
         background-color: transparent !important;
         width: 0 !important;
         height: 100% !important;
         z-index: 10 !important;
       }

       /* Style for cue point labels */
       .wavesurfer-region[data-type="cue-point"] .wavesurfer-region-content {
         color: #ffffff;
         position: absolute;
         top: -20px;
         left: -5px;
         font-size: 10px;
         background-color: rgba(0, 0, 0, 0.7);
         padding: 2px 4px;
         border-radius: 2px;
         white-space: nowrap;
       }
     `;
   };
   ```

### Phase 2: User Interface

1. Add UI controls for cue point management:
   - Button to add a cue point at the current playhead position
   - Dialog for selecting cue point type and label
   - List of existing cue points with edit and delete options
   - Context menu for right-clicking on the waveform to add a cue point

2. Create the AddCuePointDialog component:
   ```tsx
   const AddCuePointDialog: React.FC<{
     isOpen: boolean;
     onClose: () => void;
     onAdd: (type: CuePointType, label?: string, color?: string) => void;
     currentTime: number;
   }> = ({ isOpen, onClose, onAdd, currentTime }) => {
     const [type, setType] = useState<CuePointType>(CuePointType.CUSTOM);
     const [label, setLabel] = useState<string>('');
     const [color, setColor] = useState<string>('');

     const handleSubmit = (e: React.FormEvent) => {
       e.preventDefault();
       onAdd(type, label || undefined, color || undefined);
       onClose();
     };

     return (
       <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent>
           <DialogHeader>
             <DialogTitle>Add Cue Point</DialogTitle>
             <DialogDescription>
               Add a cue point at {formatTime(currentTime)}
             </DialogDescription>
           </DialogHeader>
           <form onSubmit={handleSubmit}>
             <div className="space-y-4">
               <div className="space-y-2">
                 <Label htmlFor="cuePointType">Type</Label>
                 <Select
                   value={type}
                   onValueChange={(value) => setType(value as CuePointType)}
                 >
                   <SelectTrigger>
                     <SelectValue placeholder="Select type" />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value={CuePointType.INTRO_START}>Intro Start</SelectItem>
                     <SelectItem value={CuePointType.INTRO_END}>Intro End</SelectItem>
                     <SelectItem value={CuePointType.OUTRO_START}>Outro Start</SelectItem>
                     <SelectItem value={CuePointType.OUTRO_END}>Outro End</SelectItem>
                     <SelectItem value={CuePointType.DROP}>Drop</SelectItem>
                     <SelectItem value={CuePointType.BREAKDOWN}>Breakdown</SelectItem>
                     <SelectItem value={CuePointType.CUSTOM}>Custom</SelectItem>
                   </SelectContent>
                 </Select>
               </div>

               <div className="space-y-2">
                 <Label htmlFor="label">Label (optional)</Label>
                 <Input
                   id="label"
                   value={label}
                   onChange={(e) => setLabel(e.target.value)}
                   placeholder="Enter label"
                 />
               </div>

               <div className="space-y-2">
                 <Label htmlFor="color">Color (optional)</Label>
                 <div className="flex gap-2">
                   <Input
                     type="color"
                     id="color"
                     value={color}
                     onChange={(e) => setColor(e.target.value)}
                     className="w-12 h-8 p-1"
                   />
                   <Input
                     type="text"
                     value={color}
                     onChange={(e) => setColor(e.target.value)}
                     placeholder="e.g. #ff0000 or rgba(255,0,0,0.9)"
                   />
                 </div>
               </div>
             </div>

             <DialogFooter className="mt-4">
               <Button type="button" variant="outline" onClick={onClose}>
                 Cancel
               </Button>
               <Button type="submit">Add Cue Point</Button>
             </DialogFooter>
           </form>
         </DialogContent>
       </Dialog>
     );
   };
   ```

3. Create the CuePointList component:
   ```tsx
   const CuePointList: React.FC<{
     cuePoints: CuePoint[];
     onEdit: (id: string) => void;
     onDelete: (id: string) => void;
     onSeek: (time: number) => void;
   }> = ({ cuePoints, onEdit, onDelete, onSeek }) => {
     return (
       <div className="space-y-2">
         <h3 className="text-sm font-medium">Cue Points</h3>
         {cuePoints.length === 0 ? (
           <p className="text-sm text-muted-foreground">No cue points added yet.</p>
         ) : (
           <div className="space-y-1">
             {cuePoints
               .sort((a, b) => a.time - b.time)
               .map((cuePoint) => (
                 <div
                   key={cuePoint.id}
                   className="flex items-center justify-between p-2 rounded-md bg-muted/50 hover:bg-muted"
                 >
                   <div className="flex items-center gap-2">
                     <div
                       className="w-3 h-3 rounded-full"
                       style={{ backgroundColor: getCuePointColor(cuePoint.type, cuePoint.color) }}
                     />
                     <span className="text-sm font-medium">
                       {cuePoint.label || getCuePointLabel(cuePoint.type)}
                     </span>
                     <span className="text-xs text-muted-foreground">
                       {formatTime(cuePoint.time)}
                     </span>
                   </div>
                   <div className="flex items-center gap-1">
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onSeek(cuePoint.time)}
                       title="Seek to cue point"
                     >
                       <Play className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onEdit(cuePoint.id)}
                       title="Edit cue point"
                     >
                       <Edit className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onDelete(cuePoint.id)}
                       title="Delete cue point"
                     >
                       <Trash className="h-3 w-3" />
                     </Button>
                   </div>
                 </div>
               ))}
           </div>
         )}
       </div>
     );
   };
   ```

4. Add context menu for the waveform:
   ```tsx
   const WaveformContextMenu: React.FC<{
     children: React.ReactNode;
     onAddCuePoint: (time: number) => void;
   }> = ({ children, onAddCuePoint }) => {
     const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
     const [clickTime, setClickTime] = useState<number>(0);

     const handleContextMenu = (e: React.MouseEvent) => {
       e.preventDefault();

       if (!wavesurferRef.current) return;

       // Calculate the time at the click position
       const rect = e.currentTarget.getBoundingClientRect();
       const relX = e.clientX - rect.left;
       const duration = wavesurferRef.current.getDuration();
       const time = (relX / rect.width) * duration;

       setClickTime(time);
       setContextMenuPosition({ x: e.clientX, y: e.clientY });
     };

     return (
       <div onContextMenu={handleContextMenu}>
         {children}

         {contextMenuPosition && (
           <div
             className="absolute bg-background border rounded-md shadow-md p-1 z-50"
             style={{
               left: `${contextMenuPosition.x}px`,
               top: `${contextMenuPosition.y}px`,
             }}
           >
             <Button
               variant="ghost"
               className="w-full justify-start text-sm"
               onClick={() => {
                 onAddCuePoint(clickTime);
                 setContextMenuPosition(null);
               }}
             >
               <Plus className="h-4 w-4 mr-2" />
               Add Cue Point
             </Button>
           </div>
         )}
       </div>
     );
   };
   ```

### Phase 3: Integration and Testing ✅

1. ✅ Integrated all components into the Professional Beat Grid Demo
2. ✅ Added event listeners for keyboard shortcuts:
   - `C` to add a cue point at the current playhead position
   - Right-click context menu for adding cue points at specific positions

3. ✅ Tested all functionality:
   - ✅ Adding cue points via button, context menu, and keyboard shortcut
   - ✅ Editing cue points via the edit dialog
   - ✅ Removing cue points via the delete button
   - ✅ Dragging cue points to reposition them without duplication
   - ✅ Seeking to cue points by clicking on them in the list
   - ✅ Fixed issues with duplicate cue points during dragging operations

## Next Steps After Implementation

1. Refine the UI based on user feedback
2. Add more advanced features:
   - Cue point hotkeys (1-9) for quick navigation
   - Cue point groups for organizing related cue points
   - Cue point actions (e.g., trigger effects, change playback rate)
3. Prepare for integration into the Mix Timeline:
   - Create a standalone CuePointOverlay component
   - Ensure proper state management for cue points
   - Add API for interacting with cue points from other components

## Conclusion

✅ We have successfully implemented a comprehensive cue point system in the Professional Beat Grid Demo using WaveSurfer.js's Regions plugin. The implementation includes:

1. A robust state management approach that prevents duplication issues
2. Debounced rendering to prevent performance issues during rapid updates
3. Direct state updates during drag operations to avoid circular updates
4. Complete UI for adding, editing, and removing cue points
5. Keyboard shortcuts and context menu options for improved usability
6. Color-coded visualization for different cue point types

The implementation is now ready to be integrated into the Mix Timeline component. The approach we've taken ensures that cue points are properly managed and visualized without duplication or performance issues.
