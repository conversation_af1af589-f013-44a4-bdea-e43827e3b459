# Segment Implementation Plan for Professional Beat Grid Demo

**⚠️ NOTE: This document describes the implementation in the Professional Beat Grid Demo. The current Mix Timeline only has segment panels working - waveform visualization is not yet implemented.**

## Overview

This document outlines the plan for implementing segments in the Professional Beat Grid Demo using WaveSurfer.js's Regions plugin. This implementation will follow the same approach used for cue points, with proper state management and debounced rendering to prevent duplication issues.

## Goals

1. Create a native WaveSurfer.js implementation of segments using the Regions plugin
2. Implement UI for adding, editing, and removing segments
3. Support different segment types with distinct styling
4. Allow dragging segment boundaries for resizing
5. Provide options for customizing appearance and behavior

## Implementation Details

### 1. Data Structure

```typescript
interface Segment {
  id: string;
  startTime: number;
  endTime: number;
  type: SegmentType;
  label?: string;
  color?: string;
}

enum SegmentType {
  INTRO = 'intro',
  VERSE = 'verse',
  CHORUS = 'chorus',
  BRIDGE = 'bridge',
  BREAKDOWN = 'breakdown',
  DROP = 'drop',
  OUTRO = 'outro',
  CUSTOM = 'custom'
}
```

### 2. Component Structure

1. **SegmentOverlay**: Main component for rendering segments using WaveSurfer.js Regions
2. **SegmentEditor**: UI for adding, editing, and removing segments
3. **SegmentList**: Displays a list of all segments with options to edit or remove
4. **AddSegmentDialog**: Dialog for adding a new segment
5. **EditSegmentDialog**: Dialog for editing an existing segment

### 3. WaveSurfer.js Integration

We'll use the Regions plugin to create segments as regions with distinct styling:

```typescript
// Create a region for a segment
const region = regionsPlugin.addRegion({
  start: segment.startTime,
  end: segment.endTime,
  drag: true, // Allow dragging
  resize: true, // Allow resizing
  color: getSegmentColor(segment.type, segment.color),
  id: `segment-${segment.id}`,
  content: segment.label || getSegmentLabel(segment.type),
  attributes: {
    type: 'segment',
    segmentType: segment.type
  }
});
```

### 4. CSS Styling

```css
/* Base style for segment regions */
.wavesurfer-region[data-type="segment"] {
  background-color: rgba(0, 0, 0, 0.1) !important;
  border: 1px solid currentColor !important;
  z-index: 5 !important;
}

/* Style for segment labels */
.wavesurfer-region[data-type="segment"] .wavesurfer-region-content {
  color: #ffffff;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
}

/* Specific styles for different segment types */
.wavesurfer-region[data-segment-type="intro"] {
  border-color: #4CAF50 !important;
}

.wavesurfer-region[data-segment-type="verse"] {
  border-color: #2196F3 !important;
}

.wavesurfer-region[data-segment-type="chorus"] {
  border-color: #9C27B0 !important;
}

.wavesurfer-region[data-segment-type="bridge"] {
  border-color: #FF9800 !important;
}

.wavesurfer-region[data-segment-type="breakdown"] {
  border-color: #E91E63 !important;
}

.wavesurfer-region[data-segment-type="drop"] {
  border-color: #F44336 !important;
}

.wavesurfer-region[data-segment-type="outro"] {
  border-color: #795548 !important;
}

.wavesurfer-region[data-segment-type="custom"] {
  border-color: #607D8B !important;
}
```

## Implementation Steps

### Phase 1: Core Functionality (✅ Implemented)

1. Added segment state to the Professional Beat Grid Demo:
   ```typescript
   const [segments, setSegments] = useState<Segment[]>([]);
   const segmentRegionsRef = useRef<any[]>([]);
   ```

2. Created functions for adding, updating, and removing segments with improved state management:
   ```typescript
   const addSegment = (startTime: number, endTime: number, type: SegmentType, label?: string, color?: string) => {
     if (!wavesurferRef.current || !regionsPluginRef.current) return;

     const newSegment: Segment = {
       id: uuidv4(),
       startTime,
       endTime,
       type,
       label,
       color
     };

     // Just update the state - the effect will handle rendering
     setSegments(prev => [...prev, newSegment]);

     console.log(`[ProfessionalBeatGridDemo] Added segment from ${formatTime(startTime)} to ${formatTime(endTime)}`);
   };

   const updateSegment = (id: string, updates: Partial<Segment>) => {
     // Just update the state - the effect will handle rendering
     setSegments(prev => prev.map(seg => seg.id === id ? { ...seg, ...updates } : seg));

     console.log(`[ProfessionalBeatGridDemo] Updated segment ${id}`);
   };

   const removeSegment = (id: string) => {
     // Just update the state - the effect will handle rendering
     setSegments(prev => prev.filter(seg => seg.id !== id));

     console.log(`[ProfessionalBeatGridDemo] Removed segment ${id}`);
   };
   ```

3. Implemented a robust approach for rendering segments that prevents duplication:
   ```typescript
   // Completely rebuild all segments
   const rebuildSegments = () => {
     if (!wavesurferRef.current || !regionsPluginRef.current) return;

     console.log('[ProfessionalBeatGridDemo] Rebuilding all segments');

     // Remove all existing segment regions
     const allRegions = regionsPluginRef.current.getRegions();
     Object.values(allRegions).forEach(region => {
       if (region.id.startsWith('segment-')) {
         region.remove();
       }
     });

     // Clear our reference array
     segmentRegionsRef.current = [];

     // Only create regions if segments should be shown
     if (!showSegments) return;

     // Create new regions for all segments
     segments.forEach(segment => {
       const region = regionsPluginRef.current!.addRegion({
         start: segment.startTime,
         end: segment.endTime,
         drag: true, // Allow dragging
         resize: true, // Allow resizing
         color: getSegmentColor(segment.type, segment.color),
         id: `segment-${segment.id}`,
         content: segment.label || getSegmentLabel(segment.type),
         attributes: {
           type: 'segment',
           segmentType: segment.type
         }
       });

       // Add event listener for drag/resize end to update segment position
       region.on('update-end', () => {
         console.log(`Segment ${segment.id} updated to ${region.start}-${region.end}`);

         // Update the segment in state without triggering a rebuild
         setSegments(prev =>
           prev.map(seg =>
             seg.id === segment.id
               ? { ...seg, startTime: region.start, endTime: region.end }
               : seg
           )
         );
       });

       // Add event listener for click to seek to segment
       region.on('click', () => {
         if (wavesurferRef.current) {
           wavesurferRef.current.seekTo(region.start / wavesurferRef.current.getDuration());
         }
       });

       segmentRegionsRef.current.push(region);
     });

     console.log(`[ProfessionalBeatGridDemo] Created ${segmentRegionsRef.current.length} segment regions`);
   };
   ```

4. Added a debounced effect to prevent rapid rebuilds:
   ```typescript
   // Update segments when they change (with debounce)
   useEffect(() => {
     if (!isReady) return;

     // Use a debounce to prevent rapid rebuilds
     const timer = setTimeout(() => {
       rebuildSegments();
     }, 50); // 50ms debounce

     return () => clearTimeout(timer);
   }, [segments, showSegments, isReady]);
   ```

5. Added helper functions for segment styling:
   ```typescript
   const getSegmentColor = (type: SegmentType, customColor?: string): string => {
     if (customColor) return customColor;

     switch (type) {
       case SegmentType.INTRO: return 'rgba(76, 175, 80, 0.2)';
       case SegmentType.VERSE: return 'rgba(33, 150, 243, 0.2)';
       case SegmentType.CHORUS: return 'rgba(156, 39, 176, 0.2)';
       case SegmentType.BRIDGE: return 'rgba(255, 152, 0, 0.2)';
       case SegmentType.BREAKDOWN: return 'rgba(233, 30, 99, 0.2)';
       case SegmentType.DROP: return 'rgba(244, 67, 54, 0.2)';
       case SegmentType.OUTRO: return 'rgba(121, 85, 72, 0.2)';
       case SegmentType.CUSTOM: return 'rgba(96, 125, 139, 0.2)';
       default: return 'rgba(96, 125, 139, 0.2)';
     }
   };

   const getSegmentLabel = (type: SegmentType): string => {
     switch (type) {
       case SegmentType.INTRO: return 'Intro';
       case SegmentType.VERSE: return 'Verse';
       case SegmentType.CHORUS: return 'Chorus';
       case SegmentType.BRIDGE: return 'Bridge';
       case SegmentType.BREAKDOWN: return 'Breakdown';
       case SegmentType.DROP: return 'Drop';
       case SegmentType.OUTRO: return 'Outro';
       case SegmentType.CUSTOM: return 'Custom';
       default: return 'Segment';
     }
   };
   ```

### Phase 2: User Interface (✅ Implemented)

1. Added UI controls for segment management:
   - ✅ Button to add a segment at the current playhead position
   - ✅ Dialog for selecting segment type, start/end times, and label
   - ✅ List of existing segments with edit and delete options
   - ✅ Context menu for right-clicking on the waveform to add a segment
   - ✅ Double-click on waveform to create a quick segment
   - ✅ 'S' key shortcut to add a segment at the current playhead position

2. Created the AddSegmentDialog component:
   ```tsx
   const AddSegmentDialog: React.FC<{
     isOpen: boolean;
     onClose: () => void;
     onAdd: (type: SegmentType, startTime: number, endTime: number, label?: string, color?: string) => void;
     currentTime: number;
   }> = ({ isOpen, onClose, onAdd, currentTime }) => {
     const [type, setType] = useState<SegmentType>(SegmentType.CUSTOM);
     const [startTime, setStartTime] = useState<number>(currentTime);
     const [endTime, setEndTime] = useState<number>(currentTime + 10); // Default 10 seconds
     const [label, setLabel] = useState<string>('');
     const [color, setColor] = useState<string>('');

     // Reset form when dialog opens
     useEffect(() => {
       if (isOpen) {
         setType(SegmentType.CUSTOM);
         setStartTime(currentTime);
         setEndTime(currentTime + 10);
         setLabel('');
         setColor('');
       }
     }, [isOpen, currentTime]);

     const handleSubmit = (e: React.FormEvent) => {
       e.preventDefault();
       onAdd(type, startTime, endTime, label || undefined, color || undefined);
       onClose();
     };

     return (
       <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
         <DialogContent>
           <DialogHeader>
             <DialogTitle>Add Segment</DialogTitle>
             <DialogDescription>
               Add a segment starting at {formatTime(startTime)}
             </DialogDescription>
           </DialogHeader>
           <form onSubmit={handleSubmit}>
             <div className="space-y-4 py-4">
               <div className="space-y-2">
                 <Label htmlFor="segmentType">Type</Label>
                 <Select
                   value={type}
                   onValueChange={(value) => setType(value as SegmentType)}
                 >
                   <SelectTrigger>
                     <SelectValue placeholder="Select type" />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value={SegmentType.INTRO}>Intro</SelectItem>
                     <SelectItem value={SegmentType.VERSE}>Verse</SelectItem>
                     <SelectItem value={SegmentType.CHORUS}>Chorus</SelectItem>
                     <SelectItem value={SegmentType.BRIDGE}>Bridge</SelectItem>
                     <SelectItem value={SegmentType.BREAKDOWN}>Breakdown</SelectItem>
                     <SelectItem value={SegmentType.DROP}>Drop</SelectItem>
                     <SelectItem value={SegmentType.OUTRO}>Outro</SelectItem>
                     <SelectItem value={SegmentType.CUSTOM}>Custom</SelectItem>
                   </SelectContent>
                 </Select>
               </div>

               <div className="space-y-2">
                 <Label htmlFor="startTime">Start Time</Label>
                 <div className="flex items-center gap-2">
                   <Input
                     id="startTime"
                     type="number"
                     step="0.01"
                     value={startTime}
                     onChange={(e) => setStartTime(parseFloat(e.target.value))}
                   />
                   <span>{formatTime(startTime)}</span>
                 </div>
               </div>

               <div className="space-y-2">
                 <Label htmlFor="endTime">End Time</Label>
                 <div className="flex items-center gap-2">
                   <Input
                     id="endTime"
                     type="number"
                     step="0.01"
                     value={endTime}
                     onChange={(e) => setEndTime(parseFloat(e.target.value))}
                   />
                   <span>{formatTime(endTime)}</span>
                 </div>
               </div>

               <div className="space-y-2">
                 <Label htmlFor="label">Label (optional)</Label>
                 <Input
                   id="label"
                   value={label}
                   onChange={(e) => setLabel(e.target.value)}
                   placeholder="Enter label"
                 />
               </div>

               <div className="space-y-2">
                 <Label htmlFor="color">Color (optional)</Label>
                 <div className="flex gap-2">
                   <Input
                     type="color"
                     id="color"
                     value={color}
                     onChange={(e) => setColor(e.target.value)}
                     className="w-12 h-8 p-1"
                   />
                   <Input
                     type="text"
                     value={color}
                     onChange={(e) => setColor(e.target.value)}
                     placeholder="e.g. #ff0000 or rgba(255,0,0,0.2)"
                   />
                 </div>
               </div>
             </div>

             <DialogFooter>
               <Button type="button" variant="outline" onClick={onClose}>
                 Cancel
               </Button>
               <Button type="submit">Add Segment</Button>
             </DialogFooter>
           </form>
         </DialogContent>
       </Dialog>
     );
   };
   ```

3. Created the SegmentList component:
   ```tsx
   const SegmentList: React.FC<{
     segments: Segment[];
     onEdit: (id: string) => void;
     onDelete: (id: string) => void;
     onSeek: (time: number) => void;
   }> = ({ segments, onEdit, onDelete, onSeek }) => {
     return (
       <div className="space-y-2">
         <h3 className="text-sm font-medium">Segments</h3>
         {segments.length === 0 ? (
           <p className="text-sm text-muted-foreground">No segments added yet.</p>
         ) : (
           <div className="space-y-1">
             {segments
               .sort((a, b) => a.startTime - b.startTime)
               .map((segment) => (
                 <div
                   key={segment.id}
                   className="flex items-center justify-between p-2 rounded-md bg-muted/50 hover:bg-muted"
                 >
                   <div className="flex items-center gap-2">
                     <div
                       className="w-3 h-3 rounded-full"
                       style={{ backgroundColor: getSegmentColor(segment.type, segment.color) }}
                     />
                     <span className="text-sm font-medium">
                       {segment.label || getSegmentLabel(segment.type)}
                     </span>
                     <span className="text-xs text-muted-foreground">
                       {formatTime(segment.startTime)} - {formatTime(segment.endTime)}
                     </span>
                   </div>
                   <div className="flex items-center gap-1">
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onSeek(segment.startTime)}
                       title="Seek to segment start"
                     >
                       <Play className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onEdit(segment.id)}
                       title="Edit segment"
                     >
                       <Edit className="h-3 w-3" />
                     </Button>
                     <Button
                       size="icon"
                       variant="ghost"
                       onClick={() => onDelete(segment.id)}
                       title="Delete segment"
                     >
                       <Trash className="h-3 w-3" />
                     </Button>
                   </div>
                 </div>
               ))}
           </div>
         )}
       </div>
     );
   };
   ```

### Phase 3: Integration and Testing (✅ Implemented)

1. Integrated all components into the Professional Beat Grid Demo:
   - ✅ Added segment management panel to the right sidebar
   - ✅ Implemented context menu for segment operations
   - ✅ Added keyboard shortcuts for common segment actions

2. Added event listeners for keyboard shortcuts:
   - ✅ `S` key to add a segment at the current playhead position
   - ✅ Right-click context menu for adding segments at specific positions
   - ✅ Double-click for quick segment creation

3. Tested all functionality:
   - ✅ Adding segments via button, context menu, keyboard shortcut, and double-click
   - ✅ Editing segments via the edit dialog
   - ✅ Removing segments via the delete button
   - ✅ Dragging segment boundaries to resize them
   - ✅ Seeking to segments by clicking on them in the list

### Phase 4: Refinement (✅ Implemented)

1. Improved user experience:
   - ✅ Added visual feedback for segment operations
   - ✅ Improved segment styling for better visibility
   - ✅ Added tooltips for all segment controls
   - ✅ Implemented proper error handling for segment operations

2. Optimized performance:
   - ✅ Implemented debounced rendering to prevent UI freezes
   - ✅ Added proper cleanup of event listeners and regions
   - ✅ Optimized state updates to minimize re-renders

## Current Status and Next Steps

### Implemented Features

1. ✅ Core segment data structures and state management
2. ✅ Robust segment rendering with WaveSurfer.js Regions
3. ✅ UI for adding, editing, and removing segments
4. ✅ Context menu for segment operations
5. ✅ Keyboard shortcuts for common segment actions
6. ✅ Double-click for quick segment creation
7. ✅ Segment list with sorting and filtering
8. ✅ Segment styling based on type
9. ✅ Draggable segment boundaries for resizing

### Removed Features

1. ❌ Alt+click two-point segment creation - This approach was causing issues and was removed in favor of simpler methods

### Next Steps

1. Refine the UI based on user feedback
2. Add more advanced features:
   - Segment templates for common patterns
   - Segment groups for organizing related segments
   - Segment actions (e.g., trigger effects, change playback rate)
3. Prepare for integration into the Mix Timeline:
   - Create a standalone SegmentOverlay component
   - Ensure proper state management for segments
   - Add API for interacting with segments from other components
