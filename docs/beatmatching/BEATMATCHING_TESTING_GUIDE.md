# Beatmatching System Testing Guide

## How to Test the Beatmatching System

### Step 1: Open Timeline Page
1. Go to `https://localhost:5173/timeline`
2. Open browser console (F12 → Console tab)

### Step 2: Basic System Test
Run this in the console:
```javascript
testBeatmatching()
```

**Expected Output:**
```
🎵 BEATMATCHING TEST STARTING...

=== TEST 1: Master BPM Methods ===
✅ Master BPM set to: 120
BPM 115: ratio=1.043, compatible=true
BPM 120: ratio=1.000, compatible=true
BPM 125: ratio=0.960, compatible=true
BPM 110: ratio=1.091, compatible=true
BPM 130: ratio=0.923, compatible=false

=== TEST 2: BPM Tolerance ===
Current tolerance: ±6 BPM

=== TEST 3: Beatmatching Status ===
Beatmatching enabled: true

🎵 BEATMATCHING TEST COMPLETED
```

### Step 3: Check Current Timeline State
```javascript
getTimelineInfo()
```

This will show you all tracks currently in the timeline and their beatmatching status.

### Step 4: Test Adding Tracks with Different BPMs
```javascript
// Add a track that should be compatible (within ±6 BPM of 120)
simulateAddTrack(125)

// Add a track that should be incompatible (outside ±6 BPM of 120)
simulateAddTrack(135)

// Check the results
getTimelineInfo()
```

### Step 5: Test Master BPM Changes (RECOMMENDED)
This tests the complete beatmatching system including loaded tracks:
```javascript
testMasterBPMChange()
```

This will:
- Show current track states and playback rates
- Change Master BPM to 110, 120, 130, 125 every 3 seconds
- Show how tracks adapt to each new Master BPM

### Step 6: Test Playback Rate on Real Tracks
If you have tracks loaded in the timeline:
```javascript
testPlaybackRate()
```

This will cycle through different playback rates (0.8x, 1.0x, 1.2x, 1.5x) every 2 seconds.

## What to Look For

### ✅ Success Indicators
1. **Master BPM is set** when first track is added
2. **Stretch ratios are calculated** correctly (120 BPM ÷ track BPM)
3. **Compatibility checks work** (±6 BPM tolerance)
4. **Console shows beatmatching logs** with 🎵 emoji
5. **Playback rate is applied** to media elements
6. **Track properties are updated** (originalBPM, effectiveBPM, stretchRatio)

### ❌ Failure Indicators
1. **No console logs** with 🎵 emoji
2. **Master BPM stays null** after adding tracks
3. **Stretch ratios are always 1.0**
4. **Media element playbackRate stays 1.0**
5. **JavaScript errors** in console

## Advanced Testing

### Test Real Track Addition
1. Use the track selector to add a real track
2. Watch console for beatmatching logs
3. Check if playback rate is applied:
```javascript
// After adding a track, check its playback rate
getTimelineInfo()
```

### Test Different BPM Tolerances
```javascript
// Set stricter tolerance
timelineCoordinatorEnhanced.setBPMTolerance(3)

// Test compatibility
timelineCoordinatorEnhanced.isWithinAcceptableRange(125) // Should be false now

// Reset to default
timelineCoordinatorEnhanced.setBPMTolerance(6)
```

### Test Manual Playback Rate
```javascript
// Get first track ID
const tracks = timelineCoordinatorEnhanced.getTracks()
if (tracks.length > 0) {
  const trackId = tracks[0].id.toString()
  
  // Set different playback rates
  timelineCoordinatorEnhanced.setTrackPlaybackRate(trackId, 1.2) // 20% faster
  timelineCoordinatorEnhanced.setTrackPlaybackRate(trackId, 0.8) // 20% slower
  timelineCoordinatorEnhanced.setTrackPlaybackRate(trackId, 1.0) // Normal
}
```

## Troubleshooting

### If No Logs Appear
1. Check if timeline page loaded correctly
2. Verify console is showing all log levels
3. Try refreshing the page

### If Playback Rate Doesn't Change
1. Make sure tracks are actually loaded (not just added to timeline)
2. Check if WaveSurfer instances exist
3. Verify media elements are created

### If Beatmatching Doesn't Work
1. Check if tracks have valid BPM values
2. Verify master BPM is set
3. Ensure beatmatching is enabled

## Expected Behavior

### When Adding First Track
- Master BPM should be set to track's BPM
- Track should have stretchRatio = 1.0 (no stretching needed)
- Console should show master BPM setup

### When Adding Compatible Track (within ±6 BPM)
- Stretch ratio should be calculated (masterBPM / trackBPM)
- Track should be added with beatmatching applied
- Playback rate should be set when track loads

### When Adding Incompatible Track (outside ±6 BPM)
- Warning should appear in console
- Track should be added without beatmatching
- Stretch ratio should be 1.0

## Next Steps After Testing

If beatmatching is working:
- Move to UI implementation (master BPM controls)
- Add visual compatibility indicators
- Test with real music tracks

If beatmatching is not working:
- Check console errors
- Verify track BPM data
- Debug step by step through the workflow
