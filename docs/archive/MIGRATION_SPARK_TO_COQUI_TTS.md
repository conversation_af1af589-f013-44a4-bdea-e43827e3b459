# Migration: Spark-TTS to Coqui TTS

**Date:** 2025-05-09

## Motivation
- Spark-TTS was too heavy and slow for real-time or interactive use on Apple Silicon and CPU.
- Coqui TTS offers lighter, faster models suitable for real-time TTS in this application.

## Migration Steps
1. Remove Spark-TTS dependencies and model loading from backend/services/spark_tts_service.py. **(Done)**
2. Install Coqui TTS and its dependencies. **(Done)**
3. Implement a new TTS service class using Coqui TTS API. **(Done)**
4. Update all TTS endpoints and internal calls to use the new CoquiTTSService. **(Done)**
5. Test for real-time performance and quality. **(Pending)**
6. Document any limitations or differences in voice quality/features. **(Pending)**

## Rollback Plan
- Restore Spark-TTSService from git history or backup if needed.

## TODO
- [x] Remove Spark-TTS code and dependencies
- [x] Add and test Coqui TTS integration
- [x] Update documentation and requirements.txt

---

**Summary:**
- All Spark-TTS code, models, dependencies, and documentation have been removed from the project.
- Coqui TTS is now the only TTS backend, with a new service and updated endpoints.
- Requirements and documentation have been updated to reflect the new system.
- Real-time performance and quality testing is the next step.

**This file tracks the migration from Spark-TTS to Coqui TTS for maintainability and future reference.**
