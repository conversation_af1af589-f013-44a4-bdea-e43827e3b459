# WaveSurfer Timeline Blinking Fix

## Problem Description
WaveSurfer waveforms in the timeline were experiencing visual blinking/flickering when tracks were added or during interaction. DOM inspector showed rapid style changes in shadow DOM elements, specifically:
- `<div class="scroll" part="scroll" style="overflow-x: hidden;">`
- `<div class="wrapper" part="wrapper" style="width: 100%;">`
- Width values rapidly switching between fixed px and percentage values

## Root Causes Identified

### 1. **WaveSurfer Configuration Issues**
- `responsive: true` + `fillParent: true` combination causing layout thrashing
- WaveSurfer instances being resized constantly due to container size changes

### 2. **React Re-render Cycles**
- TrackList memoization dependency on `tracks.map(t => t.id).join(',')` causing frequent re-computation
- Panel resize callbacks triggering unnecessary state updates
- TrackItem components re-rendering due to unstable dependencies

### 3. **Layout Instability**
- Container size changes triggering WaveSurfer re-initialization
- Lack of fixed dimensions causing layout shifts

## Fixes Applied

### 1. **WaveSurfer Configuration Optimization**
**File:** `frontend/src/components/mixes/timeline/services/WaveSurferVisualization.ts`

```typescript
// BEFORE (causing blinking)
responsive: true,
fillParent: true,

// AFTER (stable)
responsive: false,        // Disable responsive to prevent layout thrashing
fillParent: false,       // Disable fillParent to prevent container size conflicts
scrollParent: true,      // Enable horizontal scrolling instead
hideScrollbar: false,    // Show scrollbar for navigation
autoCenter: false,       // Disable auto-center to allow manual positioning
```

### 2. **TrackList Memoization Optimization**
**File:** `frontend/src/components/mixes/timeline/components/core/TrackList.tsx`

```typescript
// BEFORE (frequent re-computation)
const memoizedTrackItems = useMemo(() => {
  // ...
}, [tracks.length, tracks.map(t => t.id).join(','), selectedTrackId, handleTrackSelect]);

// AFTER (stable dependencies)
const trackIds = useMemo(() => tracks.map(t => t.id), [tracks]);
const trackIdsString = useMemo(() => trackIds.join(','), [trackIds]);

const memoizedTrackItems = useMemo(() => {
  // ...
}, [tracks.length, trackIdsString, selectedTrackId, handleTrackSelect]);
```

### 3. **TrackItem Memo Optimization**
**File:** `frontend/src/components/mixes/timeline/components/core/TrackItem.tsx`

```typescript
// Added custom memo comparison function
const TrackItem = memo(({ track, index, isSelected, onSelect }) => {
  // Component implementation
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  const trackIdSame = prevProps.track.id === nextProps.track.id;
  const selectedSame = prevProps.isSelected === nextProps.isSelected;
  const indexSame = prevProps.index === nextProps.index;
  
  return trackIdSame && selectedSame && indexSame;
});
```

### 4. **Panel Resize Optimization**
**File:** `frontend/src/pages/TimelinePage.tsx`

```typescript
// BEFORE (frequent updates)
onLayout={(sizes) => {
  setLeftPanelSize(sizes[0]);
  setRightPanelSize(sizes[2]);
}}

// AFTER (threshold-based updates)
onLayout={(sizes) => {
  const newLeftSize = sizes[0];
  const newRightSize = sizes[2];
  
  // Only update if size changed significantly (>1% difference)
  if (Math.abs(newLeftSize - leftPanelSize) > 1) {
    setLeftPanelSize(newLeftSize);
  }
  if (Math.abs(newRightSize - rightPanelSize) > 1) {
    setRightPanelSize(newRightSize);
  }
}}
```

### 5. **Container Stability**
**File:** `frontend/src/components/mixes/timeline/components/core/TrackItem.tsx`

```typescript
// Fixed container dimensions to prevent layout shifts
<div
  ref={waveformRef}
  style={{
    minWidth: '800px',     // Fixed minimum width
    width: '100%',
    height: '80px',        // Fixed height
    position: 'relative',
    overflow: 'hidden'     // Prevent content overflow
  }}
>
```

### 6. **Resize Observer Implementation**
Added efficient resize handling with debouncing:

```typescript
// Set up resize observer for efficient container size change handling
resizeObserverRef.current = new ResizeObserver((entries) => {
  clearTimeout(loadTimeoutRef.current!);
  loadTimeoutRef.current = setTimeout(() => {
    // Handle resize with debouncing
  }, 100);
});
```

## Expected Results

1. **Eliminated Visual Blinking**: WaveSurfer waveforms should no longer flicker when tracks are added
2. **Stable Layout**: Fixed container dimensions prevent layout shifts
3. **Improved Performance**: Reduced unnecessary re-renders and DOM manipulations
4. **Maintained Functionality**: All existing features (zoom, pan, beat grids, segments) preserved

## Testing Recommendations

1. Add multiple tracks to timeline and verify no blinking occurs
2. Resize panels and confirm waveforms remain stable
3. Test zoom/pan functionality works correctly
4. Verify beat grids and segments still display properly
5. Check that audio playback remains synchronized

## Monitoring

Watch console logs for:
- `[TrackItem] 🔥 RE-RENDERING` messages (should be minimal)
- `[MemoizedTrackItem] MEMO CHECK` comparisons
- Container resize events in browser dev tools

The fixes maintain the dual WaveSurfer+Tone.js architecture while eliminating the visual blinking issues.
