# Beatport API Integration Plan

## Overview

This document outlines a plan to integrate the Beatport API (v4) to extract genre information and other metadata for tracks in the DJ Mix Constructor application. This integration will solve the issue of empty genre distribution in analytics by fetching official genre classifications from Beatport.

## Goals

1. Retrieve accurate genre information for tracks
2. Fetch additional metadata (key, BPM, release date, label, artwork, etc.)
3. Enable batch processing for collections
4. Provide a user-friendly interface for metadata updates
5. Enhance analytics with complete genre distribution data

## Implementation Plan

### 1. Set Up Beatport API Authentication

```python
# backend/services/beatport_service.py
import requests
import logging
from typing import Dict, Any, Optional, List
from backend.config import settings

logger = logging.getLogger(__name__)

class BeatportService:
    """Service for interacting with the Beatport API"""

    BASE_URL = "https://api.beatport.com/v4"

    def __init__(self):
        self.client_id = settings.BEATPORT_CLIENT_ID
        self.client_secret = settings.BEATPORT_CLIENT_SECRET
        self.access_token = None
        self.token_expires_at = 0

    async def _ensure_token(self):
        """Ensure we have a valid access token"""
        import time

        # Check if token is still valid
        if self.access_token and time.time() < self.token_expires_at:
            return

        # Get new token
        auth_url = f"{self.BASE_URL}/auth/o/token/"
        response = requests.post(
            auth_url,
            data={
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "client_credentials"
            }
        )

        if response.status_code != 200:
            logger.error(f"Failed to get Beatport token: {response.text}")
            raise Exception(f"Failed to authenticate with Beatport API: {response.status_code}")

        data = response.json()
        self.access_token = data["access_token"]
        # Set expiry time with a small buffer
        self.token_expires_at = time.time() + data["expires_in"] - 60

        logger.info("Successfully obtained Beatport API token")
```

### 2. Implement Track Search and Metadata Retrieval

```python
    async def search_track(self, artist: str, title: str) -> Optional[Dict[str, Any]]:
        """
        Search for a track on Beatport by artist and title

        Args:
            artist: Track artist
            title: Track title

        Returns:
            Track data if found, None otherwise
        """
        await self._ensure_token()

        # Clean up search terms
        artist = artist.strip()
        title = title.strip()

        # Search for the track
        search_url = f"{self.BASE_URL}/catalog/search/tracks"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        params = {
            "q": f"{artist} {title}",
            "per_page": 5  # Limit results to top 5 matches
        }

        response = requests.get(search_url, headers=headers, params=params)

        if response.status_code != 200:
            logger.error(f"Failed to search Beatport: {response.text}")
            return None

        data = response.json()
        results = data.get("results", [])

        if not results:
            logger.info(f"No results found for '{artist} - {title}'")
            return None

        # Find the best match
        best_match = None
        for track in results:
            track_artist = track.get("artists", [{}])[0].get("name", "").lower()
            track_title = track.get("name", "").lower()

            if artist.lower() in track_artist and title.lower() in track_title:
                best_match = track
                break

        if not best_match:
            # If no exact match, take the first result
            best_match = results[0]

        return self._extract_track_metadata(best_match)

    def _extract_track_metadata(self, track_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract comprehensive metadata from Beatport track data

        Args:
            track_data: Raw track data from Beatport API

        Returns:
            Cleaned metadata dictionary
        """
        # Extract artists with proper roles
        artists = []
        remixers = []
        featured = []

        for artist in track_data.get("artists", []):
            artist_info = {
                "id": artist.get("id"),
                "name": artist.get("name", ""),
                "url": artist.get("url", "")
            }

            if artist.get("type") == "remixer":
                remixers.append(artist_info)
            elif artist.get("type") == "featuring":
                featured.append(artist_info)
            else:
                artists.append(artist_info)

        # Extract all genres
        genres = []
        if "genres" in track_data and track_data["genres"]:
            genres = [{"id": genre.get("id"), "name": genre.get("name")}
                     for genre in track_data["genres"] if genre.get("name")]

        # Extract key information
        key_data = track_data.get("key", {})
        camelot_key = key_data.get("camelot_number", "") + key_data.get("camelot_letter", "")

        # Extract release information
        release = track_data.get("release", {})

        # Extract artwork in different sizes
        artwork = {}
        if "images" in track_data:
            for size in ["small", "medium", "large"]:
                if size in track_data["images"]:
                    artwork[size] = track_data["images"][size].get("url")

        # Format artist string
        artist_string = ", ".join([a.get("name", "") for a in artists])
        if remixers:
            remix_string = ", ".join([r.get("name", "") for r in remixers])
            artist_string += f" (Remix by {remix_string})"
        if featured:
            feat_string = ", ".join([f.get("name", "") for f in featured])
            artist_string += f" feat. {feat_string}"

        return {
            # Basic track info
            "id": track_data.get("id"),
            "title": track_data.get("name", ""),
            "mix_name": track_data.get("mix_name", ""),
            "artist": artist_string,

            # Artist information
            "artists": artists,
            "remixers": remixers,
            "featured_artists": featured,

            # Genre information
            "genre": genres[0].get("name") if genres else None,  # Primary genre
            "sub_genres": [g.get("name") for g in genres[1:]] if len(genres) > 1 else [],

            # Musical attributes
            "bpm": track_data.get("bpm", 0),
            "key": camelot_key,
            "traditional_key": key_data.get("name", ""),
            "energy": track_data.get("energy", 0),  # If available

            # Release information
            "release_date": track_data.get("publish_date"),
            "release_name": release.get("name") if release else None,
            "catalog_number": release.get("catalog_number") if release else None,
            "label": track_data.get("label", {}).get("name") if "label" in track_data else None,

            # Media
            "artwork": artwork,
            "coverArtUrl": artwork.get("medium") if artwork else None,  # Match your Track interface

            # Links
            "beatport_url": track_data.get("url"),

            # Additional metadata
            "exclusive": track_data.get("exclusive", False),
            "new_release": track_data.get("new_release", False),
            "chart_positions": track_data.get("chart_positions", []),
            "release_type": track_data.get("type", ""),
        }
```

### 3. Create a Batch Processing Service for Updating Track Metadata

```python
    async def update_track_metadata(self, track_id: int, db) -> Dict[str, Any]:
        """
        Update a track's metadata using Beatport

        Args:
            track_id: ID of the track to update
            db: Database session

        Returns:
            Updated track data
        """
        from backend.models.track import Track
        from backend.models.track_analysis import TrackAnalysis
        import json

        # Get track from database
        track = db.query(Track).filter(Track.id == track_id).first()
        if not track:
            raise ValueError(f"Track with ID {track_id} not found")

        # Search for track on Beatport
        metadata = await self.search_track(track.artist, track.title)
        if not metadata:
            logger.warning(f"No Beatport data found for track {track.artist} - {track.title}")
            return {"success": False, "message": "No Beatport data found"}

        # Track the fields we update
        updated_fields = {}

        # Update track with new metadata
        if metadata.get("genre") and not track.genre:
            track.genre = metadata["genre"]
            updated_fields["genre"] = metadata["genre"]

        if metadata.get("bpm") and not track.bpm:
            track.bpm = metadata["bpm"]
            updated_fields["bpm"] = metadata["bpm"]

        if metadata.get("key") and not track.key:
            track.key = metadata["key"]
            updated_fields["key"] = metadata["key"]

        # Save additional metadata in the analysis data
        # Create analysis record if it doesn't exist
        if not track.analysis:
            track.analysis = TrackAnalysis(
                track_id=track.id,
                status="completed",
                analysis_data={}
            )

        # Get existing analysis data or initialize empty dict
        analysis_data = track.analysis.analysis_data or {}

        # Add Beatport metadata to analysis data
        analysis_data["beatport"] = {
            "id": metadata.get("id"),
            "title": metadata.get("title"),
            "mix_name": metadata.get("mix_name"),
            "artists": metadata.get("artists"),
            "remixers": metadata.get("remixers"),
            "featured_artists": metadata.get("featured_artists"),
            "genre": metadata.get("genre"),
            "sub_genres": metadata.get("sub_genres", []),
            "bpm": metadata.get("bpm"),
            "key": metadata.get("key"),
            "traditional_key": metadata.get("traditional_key"),
            "energy": metadata.get("energy"),
            "release_date": metadata.get("release_date"),
            "release_name": metadata.get("release_name"),
            "catalog_number": metadata.get("catalog_number"),
            "label": metadata.get("label"),
            "artwork": metadata.get("artwork"),
            "beatport_url": metadata.get("beatport_url"),
            "exclusive": metadata.get("exclusive"),
            "new_release": metadata.get("new_release"),
            "chart_positions": metadata.get("chart_positions"),
            "release_type": metadata.get("release_type"),
            "updated_at": datetime.utcnow().isoformat()
        }

        # Update the analysis data
        track.analysis.analysis_data = analysis_data
        updated_fields["analysis_data"] = "Updated with Beatport metadata"

        # Update coverArtUrl if missing and available from Beatport
        if metadata.get("coverArtUrl") and not hasattr(track, 'coverArtUrl'):
            # Store in analysis data since Track model doesn't have coverArtUrl field
            analysis_data["coverArtUrl"] = metadata.get("coverArtUrl")
            updated_fields["coverArtUrl"] = metadata.get("coverArtUrl")

        # Commit changes
        db.commit()

        return {
            "success": True,
            "track_id": track_id,
            "updated_fields": updated_fields,
            "beatport_id": metadata.get("id")
        }
```

### 4. Create API Endpoints for Beatport Integration

```python
# backend/routes/beatport.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from backend.db.database import get_db
from backend.services.beatport_service import BeatportService

router = APIRouter(
    prefix="/beatport",
    tags=["beatport"],
    responses={404: {"description": "Not found"}},
)

beatport_service = BeatportService()

@router.post("/update-track/{track_id}")
async def update_track_metadata(track_id: int, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Update a track's metadata using Beatport
    """
    try:
        result = await beatport_service.update_track_metadata(track_id, db)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating track: {str(e)}")

@router.post("/update-collection/{collection_id}")
async def update_collection_metadata(collection_id: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Update metadata for all tracks in a collection
    """
    try:
        result = await beatport_service.batch_update_collection(collection_id, db)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating collection: {str(e)}")

@router.get("/search")
async def search_track(artist: str, title: str) -> Dict[str, Any]:
    """
    Search for a track on Beatport
    """
    try:
        result = await beatport_service.search_track(artist, title)
        if not result:
            return {"success": False, "message": "Track not found"}
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching Beatport: {str(e)}")
```

### 5. Frontend Integration

#### API Service

```typescript
// frontend/src/services/api/beatport.ts
import { apiClient } from '@/lib/client';
import { Track } from '@/types/api/tracks';

/**
 * Update a track's metadata using Beatport
 */
export const updateTrackMetadata = async (trackId: number | string) => {
  const response = await apiClient.post<{
    success: boolean;
    track_id: number;
    updated_fields: Record<string, any>;
    beatport_id?: string;
    message?: string;
  }>(`/api/v1/beatport/update-track/${trackId}`);
  return response.data;
};

/**
 * Update metadata for all tracks in a collection
 */
export const updateCollectionMetadata = async (collectionId: string) => {
  const response = await apiClient.post<{
    success: boolean;
    results: {
      total: number;
      updated: number;
      failed: number;
      skipped: number;
    };
    message?: string;
  }>(`/api/v1/beatport/update-collection/${collectionId}`);
  return response.data;
};

/**
 * Search for a track on Beatport
 */
export const searchTrack = async (artist: string, title: string) => {
  const response = await apiClient.get<{
    success: boolean;
    data?: Record<string, any>;
    message?: string;
  }>(`/api/v1/beatport/search`, {
    params: { artist, title }
  });
  return response.data;
};
```

#### UI Components

```tsx
// frontend/src/components/beatport/BeatportMetadataPanel.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { updateTrackMetadata, searchTrack } from '@/services/api/beatport';
import { useToast } from '@/components/ui/use-toast';
import { Track } from '@/types/api/tracks';
import { Loader2, Check, AlertCircle, Music, Tag, Clock, Calendar } from 'lucide-react';

interface BeatportMetadataPanelProps {
  track: Track;
  onMetadataUpdated?: () => void;
}

export const BeatportMetadataPanel: React.FC<BeatportMetadataPanelProps> = ({
  track,
  onMetadataUpdated
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [beatportData, setBeatportData] = useState<any>(null);
  const [updateResult, setUpdateResult] = useState<any>(null);
  const { toast } = useToast();

  // Check if track already has Beatport data
  const hasBeatportData = track.analysis?.analysis_data?.beatport;

  const handleSearchBeatport = async () => {
    setIsLoading(true);
    try {
      const result = await searchTrack(track.artist, track.title);

      if (result.success && result.data) {
        setBeatportData(result.data);
        toast({
          title: "Track Found on Beatport",
          description: `Found "${result.data.title}" by ${result.data.artist}`,
          variant: "default",
        });
      } else {
        toast({
          title: "Track Not Found",
          description: result.message || "Could not find track on Beatport",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while searching Beatport",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateMetadata = async () => {
    setIsLoading(true);
    try {
      const result = await updateTrackMetadata(track.id);
      setUpdateResult(result);

      if (result.success) {
        toast({
          title: "Track Updated",
          description: `Successfully updated track metadata from Beatport`,
          variant: "default",
        });

        // Notify parent component to refresh track data
        if (onMetadataUpdated) {
          onMetadataUpdated();
        }
      } else {
        toast({
          title: "Update Failed",
          description: result.message || "Failed to update track metadata",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while updating track metadata",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Beatport Metadata
        </CardTitle>
        <CardDescription>
          Update track metadata from Beatport including genre, key, and BPM
        </CardDescription>
      </CardHeader>

      <CardContent>
        {hasBeatportData ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">This track has Beatport metadata</span>
            </div>

            <Tabs defaultValue="metadata">
              <TabsList>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
                <TabsTrigger value="artwork">Artwork</TabsTrigger>
                <TabsTrigger value="links">Links</TabsTrigger>
              </TabsList>

              <TabsContent value="metadata" className="space-y-4 pt-4">
                {track.analysis?.analysis_data?.beatport?.genre && (
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Genre:</span>
                    <Badge variant="outline">{track.analysis.analysis_data.beatport.genre}</Badge>

                    {track.analysis.analysis_data.beatport.sub_genres?.map((genre: string) => (
                      <Badge key={genre} variant="outline" className="bg-muted">{genre}</Badge>
                    ))}
                  </div>
                )}

                {track.analysis?.analysis_data?.beatport?.label && (
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Label:</span>
                    <span className="text-sm">{track.analysis.analysis_data.beatport.label}</span>
                  </div>
                )}

                {track.analysis?.analysis_data?.beatport?.release_date && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Released:</span>
                    <span className="text-sm">
                      {new Date(track.analysis.analysis_data.beatport.release_date).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="artwork" className="pt-4">
                {track.analysis?.analysis_data?.beatport?.artwork?.medium ? (
                  <div className="flex justify-center">
                    <img
                      src={track.analysis.analysis_data.beatport.artwork.medium}
                      alt={`${track.title} artwork`}
                      className="max-w-full h-auto rounded-md shadow-md"
                    />
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground">
                    No artwork available from Beatport
                  </div>
                )}
              </TabsContent>

              <TabsContent value="links" className="pt-4">
                {track.analysis?.analysis_data?.beatport?.beatport_url ? (
                  <div className="flex flex-col gap-2">
                    <a
                      href={track.analysis.analysis_data.beatport.beatport_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline flex items-center gap-2"
                    >
                      <Music className="h-4 w-4" />
                      View on Beatport
                    </a>
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground">
                    No links available
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <Button
              onClick={handleUpdateMetadata}
              disabled={isLoading}
              variant="outline"
              className="mt-4"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Refresh Metadata"
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-amber-500" />
              <span className="text-sm">This track has no Beatport metadata</span>
            </div>

            <Button
              onClick={handleUpdateMetadata}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update from Beatport"
              )}
            </Button>

            {beatportData && (
              <div className="mt-4 p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2">Found on Beatport:</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Title:</span>
                    <span className="text-sm">{beatportData.title}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Artist:</span>
                    <span className="text-sm">{beatportData.artist}</span>
                  </div>
                  {beatportData.genre && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Genre:</span>
                      <Badge variant="outline">{beatportData.genre}</Badge>
                    </div>
                  )}
                </div>
              </div>
            )}

            {updateResult && (
              <div className="mt-4 p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2">Update Results:</h3>
                <div className="space-y-2">
                  {Object.entries(updateResult.updated_fields || {}).map(([field, value]) => (
                    <div key={field} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">{field}:</span>
                      <span className="text-sm">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="text-xs text-muted-foreground">
        Data provided by Beatport API. Results may vary based on track information.
      </CardFooter>
    </Card>
  );
};
```

```tsx
// frontend/src/components/beatport/BeatportCollectionUpdater.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { updateCollectionMetadata } from '@/services/api/beatport';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Music, AlertCircle, Check, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface BeatportCollectionUpdaterProps {
  collectionId: string;
  collectionName: string;
  onUpdateComplete?: () => void;
}

export const BeatportCollectionUpdater: React.FC<BeatportCollectionUpdaterProps> = ({
  collectionId,
  collectionName,
  onUpdateComplete
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<any>(null);
  const { toast } = useToast();

  const handleUpdateCollection = async () => {
    if (!confirm(`This will update metadata for all tracks in "${collectionName}" from Beatport. This may take some time. Continue?`)) {
      return;
    }

    setIsLoading(true);
    setProgress(10); // Start progress

    try {
      const result = await updateCollectionMetadata(collectionId);
      setResults(result);
      setProgress(100); // Complete progress

      if (result.success) {
        toast({
          title: "Collection Update Complete",
          description: `Updated ${result.results.updated} of ${result.results.total} tracks`,
          variant: "default",
        });

        // Notify parent component
        if (onUpdateComplete) {
          onUpdateComplete();
        }
      } else {
        toast({
          title: "Update Failed",
          description: result.message || "Failed to update collection metadata",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while updating collection metadata",
        variant: "destructive",
      });
      setProgress(0); // Reset progress on error
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Beatport Collection Update
        </CardTitle>
        <CardDescription>
          Update metadata for all tracks in "{collectionName}" from Beatport
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Alert className="mb-4">
          <Info className="h-4 w-4" />
          <AlertTitle>Batch Update</AlertTitle>
          <AlertDescription>
            This will search Beatport for each track in your collection and update metadata including genre, key, BPM, and artwork.
          </AlertDescription>
        </Alert>

        <Button
          onClick={handleUpdateCollection}
          disabled={isLoading}
          className="w-full mb-4"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Updating Collection...
            </>
          ) : (
            "Update All Tracks from Beatport"
          )}
        </Button>

        {isLoading && (
          <div className="space-y-2 mt-4">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Processing...</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {results && (
          <div className="mt-4 p-4 bg-muted rounded-md">
            <h3 className="font-medium mb-2">Update Results:</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Total Tracks:</span>
                <span className="text-sm">{results.results.total}</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Updated:</span>
                <span className="text-sm">{results.results.updated}</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-amber-500" />
                <span className="text-sm font-medium">Skipped:</span>
                <span className="text-sm">{results.results.skipped}</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Failed:</span>
                <span className="text-sm">{results.results.failed}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="text-xs text-muted-foreground">
        Note: Beatport API has rate limits. Large collections may take some time to process.
      </CardFooter>
    </Card>
  );
};
```

## Implementation Steps

1. **Register for Beatport API Access**:
   - Go to https://api.beatport.com/v4/docs/
   - Register for an API key to get your client ID and client secret

2. **Set Up Environment Variables**:
   - Add `BEATPORT_CLIENT_ID` and `BEATPORT_CLIENT_SECRET` to your environment variables

3. **Implement the Backend Service**:
   - Create the `backend/services/beatport_service.py` file with the BeatportService class
   - Implement the API endpoints in `backend/routes/beatport.py`
   - Register the router in `main.py`

4. **Implement the Frontend Service**:
   - Create the frontend API service in `frontend/src/services/api/beatport.ts`
   - Create UI components:
     - `frontend/src/components/beatport/BeatportMetadataPanel.tsx` for single track updates
     - `frontend/src/components/beatport/BeatportCollectionUpdater.tsx` for collection updates

5. **Add UI Integration**:
   - Add the BeatportMetadataPanel component to your track details page (TrackInfoPanel)
   - Add the BeatportCollectionUpdater component to your collection view
   - Add a "Update from Beatport" option to track context menus

## Benefits

1. **Accurate Genre Information**: Get official genre classifications from Beatport, solving the empty genre distribution issue in analytics
2. **Enhanced Metadata**: Retrieve key, BPM, release date, label information, and high-quality artwork
3. **Consistent Data**: Ensure consistent metadata across your collection with industry-standard information
4. **Improved Analytics**: Complete genre distribution charts and better insights into your collection
5. **Professional Presentation**: Display official artwork, label information, and release details in your UI

## Analytics Integration

The Beatport integration will directly enhance your analytics in several ways:

### 1. Genre Distribution

```typescript
// Example of how genre data will flow into analytics
// In backend/routes/analysis.py

// Before Beatport integration
key_distribution = {}
for track in tracks:
    key = track.key or 'Unknown'
    key_distribution[key] = key_distribution.get(key, 0) + 1

// After Beatport integration
genre_distribution = {}
for track in tracks:
    genre = track.genre or 'Unknown'
    genre_distribution[genre] = genre_distribution.get(genre, 0) + 1
```

With proper genre data from Beatport, your genre distribution chart in `SingleItemAnalysis.tsx` will now display meaningful information instead of being empty.

### 2. Label Analysis

You can add a new distribution chart for record labels:

```typescript
// New label distribution chart in SingleItemAnalysis.tsx
{(analysisData.label_distribution) && (
  <DistributionChart
    title="Label Distribution"
    data={Object.entries(analysisData.label_distribution || {}).map(([label, count]) => ({
      label,
      count: typeof count === 'number' ? count : 0
    })) as DistributionItem[]}
    dataKey="label"
    valueKey="count"
    color="#4C9AFF"
  />
)}
```

### 3. Release Date Timeline

You can create a timeline visualization showing when tracks in your collection were released:

```typescript
// New release date timeline component
<ReleaseDateTimeline
  data={tracks.map(track => ({
    id: track.id,
    title: track.title,
    artist: track.artist,
    releaseDate: track.analysis?.analysis_data?.beatport?.release_date
  }))}
/>
```

## Considerations

1. **Rate Limiting**: Beatport API has rate limits (typically around 60 requests per minute), so batch processing is implemented with delays between requests
2. **Matching Accuracy**: Track matching relies on artist and title, which may not always be exact - the implementation includes fuzzy matching logic to improve accuracy
3. **API Key Security**: Store your Beatport API credentials securely in environment variables, never in client-side code
4. **Fallback Strategy**: For tracks that can't be found on Beatport, the system will preserve existing metadata and log the failure for review
5. **Performance**: The batch update process for large collections may take several minutes - the UI provides progress indicators and allows cancellation
6. **Storage**: Additional metadata will increase your database size slightly, but the benefits outweigh the storage costs

## Integration with Existing Components

The Beatport integration is designed to work seamlessly with your existing components:

1. **TrackInfoPanel**: The BeatportMetadataPanel will be added as a new tab in your existing TrackInfoPanel
2. **Analytics**: Genre data will automatically flow into your existing analytics components
3. **Collection View**: The BeatportCollectionUpdater will be added as a new action in your collection management UI
4. **Context Menus**: "Update from Beatport" will be added to your track context menus

## Resources

- [Beatport API Documentation](https://api.beatport.com/v4/docs/)
- [Beatport Developer Portal](https://www.beatport.com/developer)
- [Beatport Genre List](https://www.beatport.com/genres)
- [Camelot Wheel Key Notation](https://mixedinkey.com/camelot-wheel/)
