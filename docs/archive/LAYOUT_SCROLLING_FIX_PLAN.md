# Layout Scrolling Fix Implementation Plan

## Overview
Fix the Enhanced Timeline layout so each section has proper independent scrolling behavior instead of everything scrolling together as one big group.

## Current Problems
- [ ] Everything scrolls together as one unit
- [ ] No independent scrolling areas
- [ ] Footer not properly fixed at bottom
- [ ] Missing ScrollArea components for themed scrollbars
- [ ] No proper height constraints

## Implementation Steps

### Phase 1: Footer Fixed Positioning ✅
- [x] Make EnhancedTimelineFooter truly fixed at bottom
- [x] Remove mb-14 margin approach
- [x] Use fixed/absolute positioning
- [x] Ensure footer stays accessible during resize

### Phase 2: Main Timeline Area Scrolling ✅
- [x] Add ScrollArea to main timeline workspace
- [x] Implement proper height constraints
- [x] Add both horizontal and vertical scrolling
- [x] Ensure timeline tracks scroll independently

### Phase 3: Mix Structure Overview Fixed Height ✅
- [x] Make MixStructureOverview fixed height component
- [x] Keep horizontal scroll only if needed
- [x] Prevent it from expanding vertically
- [x] Maintain current functionality

### Phase 4: Right Sidebar Scrolling ✅
- [x] Add ScrollArea to TrackDetailsPanel
- [x] Implement independent vertical scrolling
- [x] Ensure tabs and content scroll properly
- [x] Use themed scrollbars

### Phase 5: TopNavbar Fixed Position ✅
- [x] Ensure TopNavbar stays fixed at top
- [x] No scrolling behavior needed
- [x] Maintain all current functionality

### Phase 6: TrackSelectorPanel Scrolling (LAST) 🔄
- [ ] Add ScrollArea to TrackSelectorPanel
- [ ] Implement independent vertical scrolling
- [ ] Ensure TrackBrowser scrolls properly
- [ ] Use themed scrollbars

## Layout Structure Target

```
EnhancedRestructuredMixTimelinePage
├── Fixed TopNavbar (no scroll)
├── Flex Main Content Area
│   ├── ScrollArea TrackSelectorPanel (independent scroll)
│   ├── Main Timeline Area
│   │   ├── Fixed MixStructureOverview (horizontal scroll only)
│   │   └── ScrollArea Timeline Workspace (both directions)
│   └── ScrollArea TrackDetailsPanel (independent scroll)
└── Fixed Footer (always visible)
```

## Technical Details

### Height Constraints
- Main container: `h-screen` or `h-full`
- Content area: `flex-1` with proper overflow handling
- Scrollable areas: Defined height with `overflow-auto`

### ScrollArea Usage
- Use shadcn ScrollArea component
- Apply to: TrackSelectorPanel, Timeline, TrackDetailsPanel
- Themed scrollbars matching design system

### Footer Positioning
- Use `fixed bottom-0` positioning
- Ensure z-index for proper layering
- Account for footer height in main content

## Files to Modify
1. `frontend/src/pages/EnhancedRestructuredMixTimelinePage.tsx` - Main layout
2. `frontend/src/components/mixes/enhanced-restructured/EnhancedTimelineFooter.tsx` - Footer positioning
3. `frontend/src/components/mixes/enhanced-restructured/MixStructureOverview.tsx` - Fixed height
4. `frontend/src/components/mixes/enhanced-restructured/TrackDetailsPanel.tsx` - Scrolling
5. `frontend/src/components/mixes/enhanced-restructured/TrackSelectorPanel.tsx` - Scrolling (LAST)

## Success Criteria
- [ ] Footer always visible and accessible
- [ ] Each panel scrolls independently
- [ ] No global page scrolling
- [ ] Proper themed scrollbars
- [ ] Responsive behavior maintained
- [ ] All existing functionality preserved
