# AI Monitoring Complete Guide

This document provides a comprehensive guide to the AI monitoring system implemented in DJ Mix Constructor.

## Overview

The AI monitoring system collects, analyzes, and visualizes data about AI usage in the application. It helps optimize AI settings and improve the user experience by providing insights into performance, user satisfaction, and settings usage.

## Components

### Backend

#### Data Collection

- **AIDataCollector**: Collects and stores data about AI usage
  - Logs requests, responses, errors, and user feedback
  - Supports both file and database storage
  - Automatically tracks usage statistics

- **AIRequestLoggerMiddleware**: Automatically logs AI requests
  - Intercepts requests to AI endpoints
  - Extracts request and response data for analytics
  - Measures response time and success rate

#### Data Analysis

- **AIAnalyticsService**: Analyzes the collected data
  - Provides performance metrics, user satisfaction metrics, and settings usage statistics
  - Supports filtering by feature and time range
  - Handles both file and database data sources

- **AISettingsImpactAnalyzer**: Analyzes the impact of settings on performance and satisfaction
  - Calculates correlation between settings and metrics
  - Provides A/B testing capabilities
  - Generates impact scores and recommendations

#### Data Storage

- **Database Models**:
  - `AIRequest`: Stores AI requests and responses
  - `AIFeedback`: Stores user feedback on AI responses
  - `AIUsageStats`: Stores aggregated usage statistics
  - `AIOptimizationHistory`: Stores optimization history

- **AIDBService**: Provides database access for AI monitoring
  - Stores and retrieves monitoring data
  - Aggregates data for analytics
  - Manages data retention

#### API Endpoints

- `/api/v1/ai-monitoring/performance`: Get performance metrics
- `/api/v1/ai-monitoring/satisfaction`: Get user satisfaction metrics
- `/api/v1/ai-monitoring/settings-usage`: Get settings usage statistics
- `/api/v1/ai-monitoring/settings-performance`: Get settings performance impact
- `/api/v1/ai-monitoring/settings-satisfaction`: Get settings satisfaction impact
- `/api/v1/ai-monitoring/ab-test`: Get A/B test results
- `/api/v1/ai-monitoring/feedback`: Submit user feedback
- `/api/v1/ai-monitoring/request`: Log AI requests

### Frontend

#### Monitoring Service

- **aiSettingsMonitoringService**: Communicates with the backend API
  - Provides methods for getting metrics and submitting feedback
  - Handles errors and provides fallback data
  - Supports time-based filtering

#### Dashboard Components

- **PerformanceMetricsCard**: Displays performance metrics
  - Shows total requests, success rate, response time, and token count
  - Visualizes metrics over time
  - Supports filtering by feature and time range

- **UserSatisfactionCard**: Displays user satisfaction metrics
  - Shows average rating and feedback count
  - Visualizes rating distribution
  - Supports filtering by feature and time range

- **SettingsUsageCard**: Displays settings usage statistics
  - Shows feature usage, model usage, and parameter usage
  - Visualizes usage distribution
  - Supports filtering by time range

- **SettingsPerformanceImpactCard**: Displays settings performance impact
  - Shows correlation between settings and performance
  - Visualizes impact on response time and success rate
  - Supports filtering by feature, parameter, and time range

- **SettingsSatisfactionImpactCard**: Displays settings satisfaction impact
  - Shows correlation between settings and satisfaction
  - Visualizes impact on user ratings
  - Supports filtering by feature, parameter, and time range

- **ABTestingCard**: Displays A/B test results
  - Compares performance and satisfaction between variants
  - Shows confidence level and winner
  - Supports filtering by feature, parameter, and time range

- **OptimizationRecommendationsCard**: Displays optimization recommendations
  - Shows recommended settings changes
  - Provides expected impact and confidence
  - Allows applying recommendations

## Data Flow

1. User makes an AI request (e.g., generates style parameters)
2. Request is intercepted by the middleware
3. AI provider processes the request and returns a response
4. Response is logged by the middleware
5. User provides feedback on the response
6. Feedback is logged by the monitoring service
7. Dashboard retrieves and displays the collected data
8. Impact analyzer processes the data to generate insights
9. Optimization recommendations are generated based on the insights

## Data Storage Options

The system supports two storage options:

1. **File Storage**:
   - Data is stored in JSON files in the `data/monitoring` directory
   - Simple and lightweight, good for development
   - No database dependencies
   - Files: `ai_requests.json`, `ai_feedback.json`, `ai_usage.json`

2. **Database Storage**:
   - Data is stored in a relational database
   - Better performance and scalability for production
   - Supports complex queries and aggregations
   - Tables: `ai_requests`, `ai_feedback`, `ai_usage_stats`, `ai_optimization_history`

The storage option can be configured by setting the `USE_DATABASE` flag in the `AIDataCollector` and `AIAnalyticsService` classes.

### Database Schema

#### AIRequest Table
- `id`: Primary key
- `request_id`: Unique identifier for the request
- `timestamp`: When the request was made
- `feature_id`: The feature that made the request
- `provider`: The AI provider used
- `model`: The model used
- `parameters`: JSON object of parameters
- `prompt_length`: Length of the prompt
- `response_length`: Length of the response
- `response_time_ms`: Response time in milliseconds
- `token_count`: Number of tokens used
- `success`: Whether the request was successful
- `error_type`: Type of error if the request failed
- `error_message`: Error message if the request failed
- `user_id`: ID of the user who made the request
- `metadata`: Additional metadata about the request

#### AIFeedback Table
- `id`: Primary key
- `feedback_id`: Unique identifier for the feedback
- `request_id`: ID of the request this feedback is for (foreign key)
- `timestamp`: When the feedback was given
- `feature_id`: The feature that received feedback
- `rating`: User rating (1-5)
- `feedback_text`: Text feedback
- `user_id`: ID of the user who provided feedback
- `metadata`: Additional metadata about the feedback

#### AIUsageStats Table
- `id`: Primary key
- `timestamp`: When the stats were updated
- `feature_id`: The feature being tracked
- `provider`: The AI provider
- `model`: The model
- `parameter_name`: The parameter name
- `parameter_value`: The parameter value
- `total_requests`: Total number of requests
- `successful_requests`: Number of successful requests
- `avg_response_time_ms`: Average response time
- `avg_token_count`: Average token count
- `avg_rating`: Average user rating
- `total_feedback`: Total number of feedback entries

#### AIOptimizationHistory Table
- `id`: Primary key
- `timestamp`: When the optimization was applied
- `feature_id`: The feature that was optimized
- `parameter_name`: The parameter that was changed
- `old_value`: The old parameter value
- `new_value`: The new parameter value
- `reason`: The reason for the change
- `impact_score`: The expected impact score
- `applied_by`: Who applied the optimization

### Data Retention Policies

The system implements the following data retention policies:

1. **File Storage**:
   - Limits each file to a maximum of 10,000 entries
   - When the limit is reached, the oldest entries are removed

2. **Database Storage**:
   - Raw request and feedback data is kept for 90 days
   - Aggregated usage statistics are kept indefinitely
   - Optimization history is kept indefinitely

These policies can be configured in the `AIDBService` class.

## Metrics

### Performance Metrics
- Total requests
- Success rate
- Average response time
- Average token count
- Requests per feature
- Requests per model
- Error types

### User Satisfaction Metrics
- Total feedback
- Average rating
- Rating distribution
- Feedback per feature

### Settings Usage Statistics
- Feature usage
- Model usage
- Parameter usage

### Settings Impact Metrics
- Impact score
- Correlation coefficient
- Performance comparison
- Satisfaction comparison
- A/B test results

## Usage

### Logging AI Requests

```python
from backend.services.monitoring.ai_data_collector import AIDataCollector

AIDataCollector.log_request(
    request_id="req-123",
    feature_id="style_generation",
    provider="openai",
    model="gpt-4",
    parameters={"temperature": 0.7},
    prompt="Generate a style for deep house",
    response="Here's a style for deep house...",
    response_time_ms=250,
    token_count=150,
    success=True
)
```

### Logging User Feedback

```python
from backend.services.monitoring.ai_data_collector import AIDataCollector

AIDataCollector.log_feedback(
    feedback_id="fb-123",
    request_id="req-123",
    feature_id="style_generation",
    rating=4,
    feedback_text="Good suggestion, but could be more specific"
)
```

### Getting Performance Metrics

```python
from backend.services.monitoring.ai_analytics_service import AIAnalyticsService

metrics = AIAnalyticsService.get_performance_metrics(
    feature_id="style_generation",
    time_range_hours=24
)
```

### Analyzing Settings Impact

```python
from backend.services.monitoring.ai_settings_impact_analyzer import AISettingsImpactAnalyzer

impact = AISettingsImpactAnalyzer.analyze_parameter_performance_impact(
    feature_id="style_generation",
    param_name="temperature",
    time_range_hours=24
)
```

### Running A/B Tests

```python
from backend.services.monitoring.ai_settings_impact_analyzer import AISettingsImpactAnalyzer

results = AISettingsImpactAnalyzer.get_ab_test_results(
    feature_id="style_generation",
    param_name="temperature",
    variant_a="0.5",
    variant_b="0.7",
    time_range_hours=24
)
```

## Dashboard

The AI Analytics Dashboard is accessible at `/ai-analytics` and provides three main tabs:

1. **Overview**: Performance and satisfaction metrics
2. **Settings Analysis**: Settings usage and impact analysis
3. **Optimization**: Optimization recommendations

Users can filter data by time range (1 hour, 24 hours, 7 days, 30 days) and refresh the data manually.

## Best Practices

1. **Log all AI requests**: Make sure all AI requests are logged for comprehensive analytics
2. **Collect user feedback**: Encourage users to provide feedback on AI responses
3. **Use database storage in production**: For better performance and scalability
4. **Regularly analyze settings impact**: To optimize AI settings
5. **Run A/B tests for important changes**: To validate the impact of settings changes
6. **Monitor performance over time**: To detect issues and trends
7. **Apply optimization recommendations**: To improve AI performance and user satisfaction

## Conclusion

The AI monitoring system provides valuable insights into AI usage, performance, and user satisfaction. It helps optimize AI settings and improve the user experience by providing data-driven recommendations.
