For developing a DJ mix creator app in Python with professional-grade features like pitch shifting, tempo adjustment, and real-time audio manipulation, the following open-source libraries are highly recommended:

⸻

🎚️ 1. python-stretch
	•	Overview: A Python wrapper for the Signalsmith Stretch C++ library, offering high-quality pitch shifting and time stretching.
	•	Key Features:
	•	Supports mono and multichannel audio.
	•	Seamless integration with NumPy arrays, facilitating compatibility with libraries like librosa.
	•	Utilized in the audiomentations library for audio data augmentation.
	•	Installation: pip install python-stretch
	•	Documentation: PyPI - python-stretch ￼ ￼

⸻

🎛️ 2. Aupyom
	•	Overview: A pure Python library designed for real-time audio time-scale and pitch modification, ideal for DJ applications.
	•	Key Features:
	•	Real-time pitch shifting and time-stretching capabilities.
	•	Simple API for quick integration and sound mixing.
	•	Supports live modification of audio streams.
	•	Installation: pip install aupyom
	•	Documentation: GitHub - aupyom ￼ ￼

⸻

🔊 3. AudioStretchy
	•	Overview: A Python wrapper around the audio-stretch C library, providing fast, high-quality time-stretching without altering pitch.
	•	Key Features:
	•	Adjustable stretching ratio from 0.25 to 4.0.
	•	Supports WAV and MP3 files.
	•	Cross-platform compatibility: Windows, macOS, and Linux.
	•	Installation: pip install audiostretchy
	•	Documentation: PyPI - audiostretchy ￼ ￼

⸻

🎵 4. PyTSMod
	•	Overview: An open-source library implementing various Time-Scale Modification (TSM) algorithms in Python.
	•	Key Features:
	•	Includes OLA, WSOLA, TD-PSOLA, and Phase Vocoder algorithms.
	•	Suitable for precise control over time-stretching and pitch-shifting.
	•	Installation: pip install pytsmod
	•	Documentation: PyPI - pytsmod ￼

⸻

🧠 5. torch-pitch-shift & torch-time-stretch
	•	Overview: PyTorch-based libraries for efficient pitch shifting and time stretching, leveraging GPU acceleration.
	•	Key Features:
	•	Fast processing suitable for data augmentation and real-time applications.
	•	CUDA support for enhanced performance.
	•	Installation:
	•	pip install torch-pitch-shift
	•	pip install torch-time-stretch
	•	Documentation:
	•	PyPI - torch-pitch-shift
	•	PyPI - torch-time-stretch ￼ ￼ ￼

⸻

Summary Table:

Library	Best For	Key Features	Installation Command
python-stretch	High-quality pitch/time adjustments	Multichannel support, NumPy integration	pip install python-stretch
Aupyom	Real-time audio manipulation	Simple API, live pitch/time modifications	pip install aupyom
AudioStretchy	Fast time-stretching without pitch change	Adjustable ratio, WAV/MP3 support	pip install audiostretchy
PyTSMod	Precise TSM algorithm implementations	Multiple TSM algorithms (OLA, WSOLA, etc.)	pip install pytsmod
torch-pitch-shift & torch-time-stretch	GPU-accelerated processing	Fast pitch/time adjustments using PyTorch	See above

For a DJ mix creator app, combining python-stretch for high-quality pitch and time adjustments with Aupyom for real-time audio manipulation would provide a robust foundation. If GPU acceleration is a priority, integrating torch-pitch-shift and torch-time-stretch can enhance performance. ￼ ￼

If you need assistance with integrating these libraries or further guidance on building specific features for your app, feel free to ask!