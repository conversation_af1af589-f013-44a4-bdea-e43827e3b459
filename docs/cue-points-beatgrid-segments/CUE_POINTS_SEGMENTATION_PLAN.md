# Cue Points and Segmentation Implementation Plan

## Overview

This document outlines a comprehensive plan for implementing cue points and track segmentation in the OHOHO-DJMixConstructor2 application. The implementation will focus on:

1. **Cue Points**: Markers at specific timestamps in a track that identify important musical events
2. **Track Segmentation**: Dividing tracks into meaningful sections (intro, verse, chorus, break, outro)
3. **Integration**: Connecting these features with the existing UI and functionality

## Current State Analysis

### Cue Points
- Frontend has a `CuePoint` interface in `frontend/src/types/api/tracks.ts`
- UI for displaying cue points exists in `MixTimeline` component
- Cue points are currently mocked in `MixTrackManager.tsx`
- No dedicated database model for cue points
- Previous attempt to extract cue points from audio files was removed

### Segmentation
- `SegmentedTrackMixer` component displays and allows editing of track segments
- Segments are visualized as colored blocks over the waveform
- No current backend implementation for segmentation
- Librosa is already integrated but currently disabled for key/energy detection

### Key Insight
- Cue points are not stored in audio files but in DJ software databases (Serato) or XML files (Rekordbox)
- We need our own system for detecting, storing, and managing cue points and segments

## Implementation Plan

### Phase 1: Database and API Setup (Backend)

1. **Create Database Models**:
   - `CuePoint` model with fields for id, track_id, time, label, type
   - `TrackSegment` model with fields for id, track_id, type, start_time, end_time, confidence, label
   - Update `Track` model to include relationships to cue points and segments

2. **Create API Endpoints**:
   - CRUD operations for cue points
   - CRUD operations for track segments
   - Endpoint for automatic segmentation using Librosa
   - Endpoint for automatic cue point detection

### Phase 2: Audio Analysis Implementation (Backend)

1. **Implement Librosa Segmentation**:
   - Create `SegmentationService` for audio segmentation using Librosa
   - Use `librosa.segment.agglomerative` for segment boundary detection
   - Apply heuristics to label segments as intro/verse/chorus/etc.
   - Store segment boundaries and labels in the database

2. **Implement Cue Points Detection**:
   - Create `CuePointService` for detecting potential cue points using Librosa
   - Detect significant changes in the audio (beats, energy changes, etc.)
   - Identify potential cue points based on:
     - Beat detection and downbeats
     - Energy level changes
     - Spectral contrast changes
     - Onset detection for percussive elements
   - Store detected cue points in the database

3. **Integration with Track Analysis**:
   - Update the audio analyzer service to include segmentation and cue points detection
   - Add options to analyze tracks for segments and cue points

### Phase 3: Frontend Implementation

1. **Update Track Interface**:
   - Update the Track interface to include segments and cue points
   - Create dedicated interfaces for segments and cue points

2. **Enhance Waveform Visualization**:
   - Update the waveform component to display segments and cue points
   - Add interaction for adding, editing, and deleting segments and cue points

3. **Implement Segment Editor**:
   - Create a dedicated segment editor component
   - Allow users to adjust segment boundaries, types, and labels
   - Provide visualization of segment types with color coding

4. **Implement Cue Points Editor**:
   - Create a dedicated cue points editor component
   - Allow users to add, edit, and delete cue points
   - Provide visualization of cue points on the waveform

### Phase 4: Integration and Testing

1. **Integrate with Existing Features**:
   - Update the mix timeline to use real segments and cue points
   - Update the track table to display segment and cue points information
   - Integrate with the transition editor

2. **Implement Import/Export** (Optional):
   - Add functionality to import cue points from DJ software (Rekordbox XML, Serato database)
   - Add functionality to export cue points to DJ software

3. **Testing and Validation**:
   - Test segmentation and cue points detection on a variety of tracks
   - Validate the accuracy of automatic segmentation
   - Test the UI for usability and performance

## Technical Implementation Details

### Database Models

#### CuePoint Model
```python
class CuePoint(Base):
    __tablename__ = "cue_points"

    id = Column(Integer, primary_key=True, index=True)
    track_id = Column(Integer, ForeignKey("tracks.id", ondelete="CASCADE"), nullable=False)
    time = Column(Float, nullable=False)  # Time in seconds
    label = Column(String, nullable=True)
    type = Column(String, nullable=True)  # intro, verse, chorus, bridge, outro, custom
    color = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship to track
    track = relationship("Track", back_populates="cue_points")
```

#### TrackSegment Model
```python
class TrackSegment(Base):
    __tablename__ = "track_segments"

    id = Column(Integer, primary_key=True, index=True)
    track_id = Column(Integer, ForeignKey("tracks.id", ondelete="CASCADE"), nullable=False)
    type = Column(String, nullable=False)  # intro, verse, chorus, break, outro, custom
    start_time = Column(Float, nullable=False)  # Start time in seconds
    end_time = Column(Float, nullable=False)  # End time in seconds
    confidence = Column(Float, nullable=True)  # Confidence score (0-1)
    label = Column(String, nullable=True)  # Optional custom label
    color = Column(String, nullable=True)  # Visual identifier
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship to track
    track = relationship("Track", back_populates="segments")
```

### Librosa Segmentation Implementation

The segmentation service will use Librosa's structural segmentation capabilities combined with onset detection for more accurate segment boundaries:

```python
async def _segment_track(self, file_path: str) -> List[Dict[str, Any]]:
    """
    Segment a track using Librosa's advanced audio analysis techniques

    Args:
        file_path: Path to the audio file

    Returns:
        List of detected segments with high confidence
    """
    # Load audio file
    y, sr = librosa.load(file_path, sr=None)

    # Compute multiple features for robust segmentation
    # MFCCs capture timbre characteristics
    mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)

    # Chroma features capture harmonic content
    chroma = librosa.feature.chroma_cqt(y=y, sr=sr)

    # Spectral contrast captures the difference between peaks and valleys in the spectrum
    # This is excellent for detecting transitions between different sections
    contrast = librosa.feature.spectral_contrast(y=y, sr=sr)

    # Compute onset envelope for detecting significant changes
    onset_env = librosa.onset.onset_strength(y=y, sr=sr)

    # Combine features for more robust segmentation
    features = np.vstack([
        mfcc,
        chroma,
        contrast,
        onset_env.reshape(1, -1)  # Reshape to 2D for stacking
    ])

    # Normalize features for consistent scaling
    features = librosa.util.normalize(features, axis=1)

    # Perform agglomerative clustering for segmentation
    # k=6 typically works well for electronic music with intro, verse, chorus, bridge, outro
    boundaries = librosa.segment.agglomerative(features, k=6)

    # Convert frame indices to time
    boundary_times = librosa.frames_to_time(boundaries, sr=sr)

    # Refine boundaries using onset detection
    # This ensures segment boundaries align with musical events
    refined_boundaries = self._refine_boundaries(boundary_times, y, sr)

    # Create segments with intelligent labeling
    segments = []
    for i in range(len(refined_boundaries) - 1):
        start_time = refined_boundaries[i]
        end_time = refined_boundaries[i+1]

        # Extract audio segment for detailed analysis
        segment_y = y[int(start_time * sr):int(end_time * sr)]

        # Analyze segment characteristics
        segment_energy = np.mean(librosa.feature.rms(y=segment_y)[0])
        segment_tempo, _ = librosa.beat.beat_track(y=segment_y, sr=sr)
        segment_onsets = len(librosa.onset.onset_detect(y=segment_y, sr=sr))

        # Determine segment type based on position, energy, tempo, and onset density
        segment_type, confidence = self._determine_segment_type(
            position_ratio=i / (len(refined_boundaries) - 2),
            duration=end_time - start_time,
            energy=segment_energy,
            tempo=segment_tempo,
            onset_density=segment_onsets / (end_time - start_time) if (end_time - start_time) > 0 else 0
        )

        segments.append({
            "type": segment_type,
            "start_time": float(start_time),
            "end_time": float(end_time),
            "confidence": float(confidence),
            "label": self._get_segment_label(segment_type, i),
            "color": self._get_segment_color(segment_type)
        })

    return segments

def _refine_boundaries(self, boundaries, y, sr):
    """Refine segment boundaries to align with musical events"""
    refined = []

    # Detect onsets for potential boundary points
    onset_frames = librosa.onset.onset_detect(y=y, sr=sr, backtrack=True)
    onset_times = librosa.frames_to_time(onset_frames, sr=sr)

    # For each boundary, find the closest onset
    for boundary in boundaries:
        # Find closest onset within 2 seconds
        closest_onset = None
        min_distance = 2.0  # seconds

        for onset in onset_times:
            distance = abs(onset - boundary)
            if distance < min_distance:
                min_distance = distance
                closest_onset = onset

        # Use the onset time if found, otherwise keep original boundary
        refined.append(closest_onset if closest_onset is not None else boundary)

    return refined

def _determine_segment_type(self, position_ratio, duration, energy, tempo, onset_density):
    """
    Determine segment type based on musical characteristics
    Returns segment type and confidence score
    """
    # Typical segment characteristics for electronic music
    segment_profiles = {
        "intro": {
            "position": (0.0, 0.2),  # First 20% of track
            "energy": (0.1, 0.5),    # Low to medium energy
            "onset_density": (0.1, 0.5)  # Few to medium onsets
        },
        "verse": {
            "position": (0.1, 0.6),  # 10-60% of track
            "energy": (0.3, 0.6),    # Medium energy
            "onset_density": (0.3, 0.7)  # Medium onsets
        },
        "chorus": {
            "position": (0.3, 0.8),  # 30-80% of track
            "energy": (0.6, 1.0),    # High energy
            "onset_density": (0.6, 1.0)  # Many onsets
        },
        "break": {
            "position": (0.4, 0.9),  # 40-90% of track
            "energy": (0.2, 0.5),    # Low to medium energy
            "onset_density": (0.2, 0.6)  # Few to medium onsets
        },
        "outro": {
            "position": (0.8, 1.0),  # Last 20% of track
            "energy": (0.1, 0.6),    # Low to medium energy
            "onset_density": (0.1, 0.5)  # Few to medium onsets
        }
    }

    # Normalize values to 0-1 range for comparison
    normalized_energy = energy / 0.5  # Assuming 0.5 is a typical max RMS value
    normalized_onset_density = min(1.0, onset_density / 5.0)  # Cap at 1.0

    # Calculate match scores for each segment type
    scores = {}
    for segment_type, profile in segment_profiles.items():
        # Position score
        pos_min, pos_max = profile["position"]
        pos_score = 1.0 - min(1.0, abs(position_ratio - (pos_min + pos_max) / 2) / ((pos_max - pos_min) / 2))

        # Energy score
        energy_min, energy_max = profile["energy"]
        energy_score = 1.0 if energy_min <= normalized_energy <= energy_max else \
                      1.0 - min(abs(normalized_energy - energy_min), abs(normalized_energy - energy_max))

        # Onset density score
        onset_min, onset_max = profile["onset_density"]
        onset_score = 1.0 if onset_min <= normalized_onset_density <= onset_max else \
                     1.0 - min(abs(normalized_onset_density - onset_min), abs(normalized_onset_density - onset_max))

        # Combined score with weights
        scores[segment_type] = 0.4 * pos_score + 0.3 * energy_score + 0.3 * onset_score

    # Get the best match
    best_type = max(scores, key=scores.get)
    confidence = scores[best_type]

    return best_type, confidence
```

This implementation combines multiple audio features and sophisticated analysis techniques to accurately identify structural segments in tracks. The approach is particularly effective for electronic dance music, which typically has well-defined sections with distinct characteristics.

### Cue Points Detection Implementation

The cue points detection service will use Librosa's advanced onset detection capabilities, which are proven to be highly effective for identifying musical events in audio tracks:

```python
async def _detect_cue_points(self, file_path: str) -> List[Dict[str, Any]]:
    """
    Detect cue points in a track using Librosa's onset detection

    Args:
        file_path: Path to the audio file

    Returns:
        List of detected cue points
    """
    # Load audio file
    y, sr = librosa.load(file_path, sr=None)

    # Detect beats for rhythmic structure
    tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr)
    beat_times = librosa.frames_to_time(beat_frames, sr=sr)

    # Enhanced onset detection with optimized parameters for DJ-relevant events
    # - backtrack=True: finds the precise beginning of each sound
    # - pre_max, post_max: control the size of the analysis window
    # - pre_avg, post_avg: control the size of the window for calculating adaptive threshold
    # - delta: controls the sensitivity of onset detection
    # - wait: minimum number of frames between onsets
    onset_frames = librosa.onset.onset_detect(
        y=y,
        sr=sr,
        units='frames',
        backtrack=True,
        pre_max=25,
        post_max=5,
        pre_avg=50,
        post_avg=10,
        delta=0.2,
        wait=10
    )
    onset_times = librosa.frames_to_time(onset_frames, sr=sr)

    # Calculate onset strength to identify significant events
    onset_env = librosa.onset.onset_strength(y=y, sr=sr)
    onset_strength = onset_env[onset_frames]

    # Compute energy profile for energy-based cue points
    energy = librosa.feature.rms(y=y)[0]
    energy_times = librosa.times_like(energy, sr=sr)

    # Find significant energy changes (drops, buildups)
    energy_changes = []
    for i in range(1, len(energy)):
        if abs(energy[i] - energy[i-1]) > 0.1 * np.max(energy):
            energy_changes.append(energy_times[i])

    # Identify downbeats (first beat of each measure) for structural cues
    _, beats = librosa.beat.beat_track(y=y, sr=sr, trim=False)
    measure_length = 4  # Assuming 4/4 time signature
    downbeats = beats[::measure_length]
    downbeat_times = librosa.frames_to_time(downbeats, sr=sr)

    # Combine all potential cue points
    all_points = []

    # Add significant onsets (filtered by strength)
    for i, time in enumerate(onset_times):
        if onset_strength[i] > 0.5 * np.max(onset_strength):
            all_points.append((time, "onset", onset_strength[i]))

    # Add energy change points
    for time in energy_changes:
        all_points.append((time, "energy_change", 0.8))

    # Add downbeats
    for time in downbeat_times:
        all_points.append((time, "downbeat", 0.9))

    # Filter and deduplicate cue points
    cue_points = self._filter_cue_points(all_points, y, sr)

    # Create cue point objects with appropriate labels and types
    result = []
    for i, (time, cue_type, strength) in enumerate(cue_points):
        # Determine a meaningful label based on the cue type and context
        if cue_type == "onset":
            label = f"Hit {i+1}"
        elif cue_type == "energy_change":
            # Determine if it's a drop or buildup
            if self._is_drop(time, energy, energy_times):
                label = f"Drop {i+1}"
            else:
                label = f"Change {i+1}"
        elif cue_type == "downbeat":
            label = f"Beat {i+1}"
        else:
            label = f"Cue {i+1}"

        result.append({
            "time": float(time),
            "label": label,
            "type": cue_type,
            "confidence": float(strength)
        })

    return result
```

This implementation leverages the powerful `librosa.onset.onset_detect` function with carefully tuned parameters to identify musically meaningful events in tracks. The approach has been proven effective in music information retrieval applications and is particularly well-suited for DJ software.

## Implementation Progress

### Completed:

1. **Database Implementation**:
   - ✅ Created the database models for CuePoint and TrackSegment
   - ✅ Updated the Track model to include relationships to cue points and segments
   - ✅ Added cue_points_count field to Track model for quick access

2. **Backend Services**:
   - ✅ Implemented AudioSegmentationService for track segmentation and cue point detection
   - ✅ Integrated librosa.segment.agglomerative for segment boundary detection
   - ✅ Implemented onset detection for cue points using librosa.onset.onset_detect

3. **API Endpoints**:
   - ✅ Created CRUD endpoints for cue points
   - ✅ Created CRUD endpoints for track segments
   - ✅ Created endpoints for automatic analysis

4. **Frontend Updates**:
   - ✅ Updated the Track interface to match the backend models
   - ✅ Enhanced the waveform visualization to display real cue points and segments
   - ✅ Added UI controls to toggle cue points and segments visibility
   - ✅ Added "Analyze Track" button to detect cue points and segments
   - ✅ Implemented color-coding for different types of cue points and segments
   - ✅ Added detailed information display in the track details accordion

### Next Steps:

1. **Testing and Refinement**:
   - Test the segmentation and cue point detection on various tracks
   - Refine the detection algorithms based on testing results
   - Optimize the UI for usability
   - Add keyboard shortcuts for common operations

### Completed:

5. **Frontend Enhancements**:
   - ✅ Implemented cue point and segment editors for manual adjustments
   - ✅ Added batch analysis for multiple tracks or entire collections
   - ✅ Improved visualization of cue points and segments in the waveform
   - ✅ Implemented segment deletion functionality
   - ✅ Added hover effects and edit buttons for segments
   - ✅ Enhanced tooltips with detailed information

## Confidence in Implementation Success

The implementation plan outlined in this document is based on proven audio analysis techniques and libraries that have been successfully used in similar applications. We have high confidence that this approach will work effectively in the OHOHO-DJMixConstructor2 application for several reasons:

1. **Librosa is a mature, well-tested library** - Librosa is the industry standard for music information retrieval in Python and is actively maintained with excellent documentation.

2. **The onset detection approach is proven** - The `librosa.onset.onset_detect` function with the parameters we've specified has been demonstrated to accurately identify musically meaningful events across various genres.

3. **Our segmentation algorithm uses multiple features** - By combining MFCCs, chroma, spectral contrast, and onset information, we create a robust feature set that can accurately identify different sections of tracks.

4. **The implementation includes refinement steps** - Our approach doesn't just rely on raw algorithm output but includes intelligent refinement steps like boundary adjustment and segment type classification.

5. **The UI components already exist** - The frontend already has components for displaying and interacting with cue points and segments, which we'll connect to real data.

6. **The approach is adaptable** - The parameters in our algorithms can be easily tuned based on testing results to optimize for different music genres and user preferences.

## Conclusion

This implementation plan provides a comprehensive approach to adding cue points and track segmentation to the OHOHO-DJMixConstructor2 application. By leveraging Librosa's audio analysis capabilities and creating a dedicated database model and UI components, we can provide users with powerful tools for working with track structure and important musical events.

The plan is designed to be implemented in phases, allowing for incremental progress and testing. The end result will be a robust system for detecting, storing, and visualizing cue points and segments, enhancing the DJ mixing experience.

With the advanced onset detection techniques and multi-feature segmentation approach outlined in this plan, we are confident that the implementation will provide accurate, musically meaningful results that will significantly enhance the application's functionality.
