# Smart Mix Generator Redesign - Task Breakdown

## Overview
Redesign the Smart Mix Generator from a 3-step wizard to a streamlined single-page layout with 3 panels: Controls | Style Selection | Results Preview.

## Current State
- ✅ Working 3-step implementation at `/smart-mix-generator`
- ✅ All components exist and are functional
- ✅ Backend API integration working
- ✅ Form validation and error handling implemented

## Target Layout
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ SMART MIX GENERATOR              [GENERATE MIX] [SAVE MIX] [OPEN IN TIMELINE] │
├─────────────┬─────────────────┬─────────────────────────────────────────────┤
│ LEFT (~25%) │ MIDDLE (~35%)   │ RIGHT (~40%)                                │
│             │                 │                                             │
│ ┌─────────┐ │ STYLE CARDS     │ GENERATED MIX                               │
│ │CHOOSE   │ │                 │                                             │
│ │COLLECT. │ │ ┌─────────────┐ │ ┌─────┬─────┬─────┬─────┐                   │
│ │[Weird▼] │ │ │ Downtempo   │ │ │Over-│Harm-│ BPM │Enrgy│                   │
│ └─────────┘ │ └─────────────┘ │ │all  │ony  │Cons.│Flow │                   │
│             │ ┌─────────────┐ │ └─────┴─────┴─────┴─────┘                   │
│ ┌─────────┐ │ │ Dramatic    │ │                                             │
│ │CHOOSE   │ │ │ [SELECTED]  │ │ TRACKS (10)                                 │
│ │FOLDER   │ │ └─────────────┘ │                                             │
│ │[     ▼] │ │ ┌─────────────┐ │ ┌─────────────────────────────────────────┐ │
│ └─────────┘ │ │ Lounge      │ │ │ 1. Apollonia - Un Vrais <PERSON>      │ │
│             │ └─────────────┘ │ │    7A  120  8:42  ○                     │ │
│ ┌─────────┐ │ ┌─────────────┐ │ │ 2. Apollonia - Un Vrais <PERSON>is      │ │
│ │SETTINGS │ │ │ Underground │ │ │    7A  120  8:42  ○                     │ │
│ │         │ │ └─────────────┘ │ │                                         │ │
│ │Track    │ │                 │ │              [SCROLL]                   │ │
│ │count    │ │    [SCROLL]     │ │                                         │ │
│ │[●──] 10 │ │                 │ │                                         │ │
│ │         │ │                 │ │                                         │ │
│ │First    │ │                 │ │                                         │ │
│ │Track    │ │                 │ │                                         │ │
│ │Selection│ │                 │ │                                         │ │
│ │         │ │                 │ │                                         │ │
│ │Power    │ │                 │ │                                         │ │
│ │blocks   │ │                 │ │                                         │ │
│ │         │ │                 │ │                                         │ │
│ │BPM trend│ │                 │ │                                         │ │
│ │         │ │                 │ │                                         │ │
│ │Other    │ │                 │ │                                         │ │
│ │settings │ │                 │ │                                         │ │
│ │         │ │                 │ │                                         │ │
│ │[SCROLL] │ │                 │ │                                         │ │
│ └─────────┘ │                 │ └─────────────────────────────────────────┘ │
└─────────────┴─────────────────┴─────────────────────────────────────────────┘
```

## Layout Specifications

### Header Bar:
- **Left**: "SMART MIX GENERATOR" title
- **Right**: Three action buttons: [GENERATE MIX] [SAVE MIX] [OPEN IN TIMELINE]

### Three Panel Layout (100% width):
- **Left Panel (~25%)**: Collection selection + Settings (2 separate blocks)
- **Middle Panel (~35%)**: Style cards (vertical scrollable list)
- **Right Panel (~40%)**: Mix stats (4 in a row) + Track list

### Panel Content:
- **Left**: Collection dropdown, Folder dropdown, Settings block (all scrollable)
- **Middle**: Style cards with selection state (scrollable)
- **Right**: 4 mix stats cards + track list with existing track components (scrollable)

## Implementation Plan

### Phase 1: Create Skeleton Layout
- [ ] Create `/smart-mix-generator-v2` route and page
- [ ] Set up header with title + 3 action buttons
- [ ] Create 3-panel layout with correct proportions (25% | 35% | 40%)
- [ ] Implement independent scrolling for each panel
- [ ] Add proper responsive behavior

### Phase 2: Integrate Existing Components (NO RECREATION)
- [ ] **Left Panel**: Reuse collection/folder selectors from CollectionStep
- [ ] **Left Panel**: Reuse settings form elements from ConfigurationStep
- [ ] **Middle Panel**: Reuse MixStyleSelector component in cards mode
- [ ] **Right Panel**: Reuse mix stats and track list from ResultsStep
- [ ] Maintain all existing functionality and styling

### Phase 3: State Management & Logic
- [ ] Consolidate all existing hooks (useCollectionData, useMixStyles, etc.)
- [ ] Connect all panels to work together seamlessly
- [ ] Ensure all form validation and API calls work
- [ ] Implement real-time updates between panels

### Phase 4: Polish & Testing
- [ ] Test all user flows and edge cases
- [ ] Ensure responsive design works
- [ ] Performance optimization
- [ ] Accessibility compliance

## Components to Reuse (NO RECREATION)

### From Current Implementation:
1. **useCollectionData** hook - Collection/folder loading
2. **useMixStyles** hook - Style loading with compatibility
3. **useMixResults** hook - Results management
4. **useTrackReplacements** hook - Track replacement functionality
5. **MixStyleSelector** component - Style cards display
6. **CollectionStep** components - Collection/folder selectors
7. **ConfigurationStep** form elements - Settings controls
8. **ResultsStep** components - Results display
9. **TrackReplaceModal** - Track replacement modal
10. **SaveMixModal** - Mix saving functionality

### UI Components:
- All shadcn/ui components (Card, Button, Form, etc.)
- Existing form validation (react-hook-form + zod)
- All existing styling patterns

## File Structure
```
/pages/SmartMixGeneratorV2Page.tsx          # New page with header + 3-panel layout
/components/mixes/generators/smart-v2/      # New layout components
  ├── SmartMixGeneratorV2.tsx              # Main component orchestrator
  ├── panels/
  │   ├── LeftPanel.tsx                     # Collection + Settings (25% width)
  │   ├── MiddlePanel.tsx                   # Style cards (35% width)
  │   └── RightPanel.tsx                    # Mix stats + Track list (40% width)
  └── hooks/
      └── useSmartMixV2State.ts             # Consolidated state management
```

## Success Criteria
- [ ] All existing functionality preserved
- [ ] Single-page layout with 3 panels
- [ ] Responsive design works on all screen sizes
- [ ] Performance is equal or better than current
- [ ] No breaking changes to existing APIs
- [ ] Original `/smart-mix-generator` remains functional

## Technical Requirements
- Use existing theme and styling patterns
- Maintain all current API integrations
- Preserve all form validation logic
- Keep all error handling mechanisms
- Ensure accessibility standards are met

## Timeline Estimate
- Phase 1: 2-3 hours (Layout structure)
- Phase 2: 3-4 hours (Component integration)
- Phase 3: 2-3 hours (State management)
- Phase 4: 1-2 hours (Polish & testing)

**Total: 8-12 hours of development time**
