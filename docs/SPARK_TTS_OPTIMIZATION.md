# Spark-TTS Optimization Guide

This document describes the optimizations made to the Spark-TTS service to improve performance and responsiveness.

## Optimizations Implemented

1. **Model Quantization**
   - 8-bit quantization for faster inference on CUDA devices
   - Reduces memory usage and speeds up inference

2. **Batch Processing**
   - Processes multiple TTS requests in batches
   - Improves throughput for multiple concurrent requests

3. **Enhanced Caching**
   - Improved caching mechanism with fuzzy matching
   - Uses shorter text for cache keys to increase cache hits
   - Provides cache hit statistics via the performance endpoint

4. **Text Chunking**
   - Splits long text into smaller chunks for processing
   - Processes chunks in parallel and concatenates results
   - Prevents memory issues with very long texts

5. **Mixed Precision Inference**
   - Uses FP16 precision on CUDA devices for faster inference
   - Automatically falls back to FP32 on CPU

6. **Optimized Generation Parameters**
   - Reduced max_new_tokens for faster generation
   - Optimized top_k, top_p, and temperature settings
   - Disabled beam search for faster generation

7. **Performance Monitoring**
   - Added performance statistics endpoint
   - Tracks cache hit rate, processing times, and queue status

## Using the Optimized Service

### Installation

1. Install the required dependencies:
   ```bash
   pip install -r backend/requirements-tts.txt
   ```

2. Download the Spark-TTS model:
   ```python
   from huggingface_hub import snapshot_download
   snapshot_download("SparkAudio/Spark-TTS-0.5B", local_dir="pretrained_models/Spark-TTS-0.5B")
   ```

### Configuration

You can configure the service using environment variables:

- `SPARK_TTS_MODEL_DIR`: Path to the model directory (default: "pretrained_models/Spark-TTS-0.5B")
- `SPARK_TTS_DEVICE_ID`: GPU device ID to use (default: 0)

### Performance Monitoring

Monitor the performance of the TTS service using the `/api/v1/tts/performance` endpoint:

```bash
curl http://localhost:8000/api/v1/tts/performance
```

Example response:
```json
{
  "total_requests": 42,
  "cache_hits": 15,
  "cache_hit_rate": 0.357,
  "avg_processing_time_seconds": 1.23,
  "queue_length": 2,
  "is_processing_batch": true,
  "device": "cuda:0",
  "batch_size": 4
}
```

## Troubleshooting

### Slow Performance

If the service is still slow:

1. **Check GPU Usage**: Make sure the GPU is being utilized properly
2. **Increase Cache Size**: Consider increasing the cache directory size
3. **Adjust Batch Size**: Try different batch sizes (2, 4, or 8)
4. **Check Text Length**: Very long texts will be slower to process

### Memory Issues

If you encounter memory issues:

1. **Reduce Batch Size**: Set a smaller batch size
2. **Enable Text Chunking**: Make sure long texts are being chunked
3. **Use Model Quantization**: Ensure 8-bit quantization is enabled

## Further Optimization Ideas

1. **GPU Kernel Optimization**: Custom CUDA kernels for specific operations
2. **Streaming Audio**: Stream audio as it's being generated
3. **Pruned Models**: Use smaller, pruned models for faster inference
4. **Distributed Processing**: Distribute processing across multiple GPUs
5. **Pre-compute Common Phrases**: Pre-generate common phrases and cache them
6. **Resource Management and Kill Switch**: Implement model unloading when not in use

### Kill Switch Implementation Plan

To reduce resource usage and prevent high CPU/GPU utilization when the TTS service is not actively being used, a kill switch feature should be implemented:

1. **Backend Implementation**:
   ```python
   # Add to SparkTTSService class
   def unload_model(self):
       """Unload the model from memory to free up resources."""
       if hasattr(self, 'model'):
           # Delete model and move to CPU first to free GPU memory
           self.model = self.model.cpu()
           self.model = None
           # Clear CUDA cache if available
           if torch.cuda.is_available():
               torch.cuda.empty_cache()
           logger.info("TTS model unloaded from memory")
       return {"status": "success", "message": "Model unloaded"}

   def load_model(self):
       """Reload the model if it was unloaded."""
       if not hasattr(self, 'model') or self.model is None:
           self._initialize_model()
           logger.info("TTS model reloaded")
       return {"status": "success", "message": "Model loaded"}
   ```

2. **API Endpoints**:
   ```python
   # Add to tts.py router
   @router.post("/unload")
   async def unload_tts_model(tts_service: SparkTTSService = Depends(get_tts_service)):
       """Unload the TTS model to free up resources."""
       return tts_service.unload_model()

   @router.post("/load")
   async def load_tts_model(tts_service: SparkTTSService = Depends(get_tts_service)):
       """Load the TTS model if it was unloaded."""
       return tts_service.load_model()
   ```

3. **Automatic Timeout**:
   ```python
   # Add to SparkTTSService __init__
   self.last_used_time = time.time()
   self.inactivity_timeout = 300  # 5 minutes

   # Add background task to check for inactivity
   async def check_inactivity(self):
       """Check for inactivity and unload model if inactive for too long."""
       while True:
           await asyncio.sleep(60)  # Check every minute
           if hasattr(self, 'model') and self.model is not None:
               if time.time() - self.last_used_time > self.inactivity_timeout:
                   logger.info(f"Model inactive for {self.inactivity_timeout} seconds, unloading")
                   self.unload_model()

   # Start the background task in __init__
   asyncio.create_task(self.check_inactivity())
   ```

4. **Frontend Integration**:
   ```typescript
   // Add to TTSSettings.tsx
   const [ttsEnabled, setTtsEnabled] = useState(true);

   const handleToggleTTS = async () => {
     try {
       if (ttsEnabled) {
         await api.post('/api/v1/tts/unload');
         setTtsEnabled(false);
       } else {
         await api.post('/api/v1/tts/load');
         setTtsEnabled(true);
       }
     } catch (error) {
       console.error('Error toggling TTS:', error);
     }
   };

   // Add toggle switch to UI
   <Switch
     checked={ttsEnabled}
     onChange={handleToggleTTS}
     label="Enable Text-to-Speech"
   />
   ```

This implementation will allow users to manually unload the model when not needed and will automatically unload it after a period of inactivity, helping to reduce resource usage and prevent fan noise.
