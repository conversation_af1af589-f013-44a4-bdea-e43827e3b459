# Project File Tree

## Frontend

```
frontend/
├── .DS_Store
├── components.json
├── index.html
├── jest.config.cjs
├── jest.config.js
├── package-lock.json
├── package.json
├── postcss.config.js
├── README.md
├── tailwind.config.js
├── tsconfig.json
├── tsconfig.node.json
├── vite.config.ts
├── vitest.config.ts
├── vitest.workspace.ts
├── public/
│   ├── .DS_Store
│   ├── component-tester.html
│   ├── song.mp3
│   ├── song2.mp3
│   ├── song3.mp3
│   ├── song4.mp3
│   ├── song5.mp3
│   ├── song6.mp3
│   ├── song7.mp3
│   ├── song8.mp3
│   ├── song9.mp3
│   ├── song10.mp3
│   └── demo-audio/
│       └── demo.wav
├── src/
│   ├── .DS_Store
│   ├── App.tsx
│   ├── IdeaCapture.tsx
│   ├── index.css
│   ├── main.tsx
│   ├── SRC.md
│   ├── vite-env.d.ts
│   ├── components/
│   │   ├── .DS_Store
│   │   ├── SuccessToast.stories.tsx
│   │   ├── SuccessToast.tsx
│   │   ├── ai/
│   │   ├── analytics/
│   │   ├── audio/
│   │   ├── auth/
│   │   ├── camelot-wheel/
│   │   ├── charts/
│   │   ├── collections/
│   │   ├── cover-generator/
│   │   ├── dashboard/
│   │   ├── demos/
│   │   ├── layout/
│   │   ├── mix-styles/
│   │   ├── mixes/
│   │   ├── monitoring/
│   │   ├── MusicLibraryBrowser/
│   │   ├── preferences/
│   │   ├── settings/
│   │   ├── tracks/
│   │   ├── ui/
│   │   ├── undo-redo/
│   │   ├── utils/
│   │   └── visualizations/
│   ├── contexts/
│   │   ├── GlobalAIAssistantContext.ts
│   ├── hooks/
│   │   ├── HOOKS.md
│   │   ├── useAnimation.ts
│   │   ├── useApplicationUndoRedo.ts
│   │   ├── useAutomaticSuggestions.ts
│   │   ├── useBeatGridCache.ts
│   │   ├── useCollections.ts
│   │   ├── useCollectionsAndPlaylists.ts
│   │   ├── useColumnResize.ts
│   │   ├── useIsMobile.ts
│   │   ├── useLocalStorage.ts
│   │   ├── useMixes.ts
│   │   ├── useMixStyles.ts
│   │   ├── useScrollbarVisibility.ts
│   │   ├── useStyleAnalytics.ts
│   │   └── useTransitionSuggestions.ts
│   ├── lib/
│   │   ├── .DS_Store
│   │   ├── LIB.md
│   │   ├── utils.ts
│   │   ├── WaveformSingleton.ts
│   │   ├── wavesurfer-plugins.ts
│   │   ├── audio/
│   │   └── waveform/
│   ├── pages/
│   │   ├── AIAnalyticsDashboardPage.tsx
│   │   ├── AIFeaturesDemoPage.tsx
│   │   ├── AISettingsPage.tsx
│   │   ├── BatchCollectionAnalyzerPage.tsx
│   │   ├── BranchingPathsPage.tsx
│   │   ├── CoverGeneratorPage.tsx
│   │   ├── EnhancedAnalytics.stories.tsx
│   │   ├── EnhancedAnalytics.tsx
│   │   ├── EnhancedBranchingPathsPage.tsx
│   │   ├── FeedbackAnalysisDemoPage.tsx
│   │   ├── GuidedJourneyPage.tsx
│   │   ├── Home.stories.tsx
│   │   ├── Home.tsx
│   │   ├── ManualMixPage.tsx
│   │   ├── MCPServersPage.tsx
│   │   ├── MixFlowAnalysisDemoPage.tsx
│   │   ├── MixGeneratorGalleryPage.tsx
│   │   ├── MixStylesPage.tsx
│   │   ├── ModularHarmonicBlocksPage.tsx
│   │   ├── MusicLibraryPage.tsx
│   │   ├── PAGES.md
│   │   ├── PreferencesPage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── SettingsPage.stories.tsx
│   │   ├── SettingsPage.tsx
│   │   ├── SmartMixGeneratorPage.tsx
│   │   ├── SmartMixGeneratorV2Page.tsx
│   │   ├── StyleAnalyticsPage.tsx
│   │   ├── StyleUsagePage.tsx
│   │   ├── StyleVersionHistoryPage.tsx
│   │   ├── TimelinePage.tsx
│   │   ├── ToneWaveSurferDemoPage.tsx
│   │   ├── TransitionVisualizationDemoPage.tsx
│   │   ├── TTSSettingsPage.tsx
│   │   ├── TutorialSettingsPage.tsx
│   │   ├── UserPreferencesDemoPage.tsx
│   │   ├── VisualizationsDemoPage.tsx
│   │   ├── WaveSurferTestPage.tsx
│   │   └── demos/
│   ├── providers/
│   │   ├── AIProvider.tsx
│   │   ├── AnimationProvider.tsx
│   │   ├── ApplicationContextProvider.tsx
│   │   ├── AuthContext.tsx
│   │   ├── CollectionProvider.tsx
│   │   ├── GlobalAIAssistantProvider.tsx
│   │   ├── GlobalUndoRedoProvider.tsx
│   │   ├── MCPProvider.tsx
│   │   ├── MultiModalAIProvider.tsx
│   │   ├── PROVIDERS.md
│   │   ├── QueryProvider.tsx
│   │   ├── ThemeContext.tsx
│   │   ├── ToastProvider.tsx
│   │   ├── TransitionProvider.tsx
│   │   ├── TutorialProvider.tsx
│   │   ├── UserPreferencesProvider.tsx
│   │   ├── VoiceCommandProvider.tsx
│   │   └── withUserPreferences.tsx
│   ├── services/
│   │   ├── beatAlignmentService.ts
│   │   ├── beatBoundarySnappingService.ts
│   │   ├── beatGridQualityService.ts
│   │   ├── constants.ts
│   │   ├── index.ts
│   │   ├── qualityAssessmentService.ts
│   │   ├── SERVICES.md
│   │   ├── styleAnalyticsService.ts
│   │   ├── styleRecommendationService.ts
│   │   ├── styleUsageService.ts
│   │   ├── test_quality_assessment.ts
│   │   ├── timeStretchingService.ts
│   │   ├── userPreferencesService.ts
│   │   ├── visualToMusicMapping.ts
│   │   ├── api/
│   │   ├── cache/
│   │   └── monitoring/
│   ├── stories/
│   │   ├── Analytics.stories.tsx
│   │   ├── Audio.stories.tsx
│   │   ├── Auth.stories.tsx
│   │   ├── button.css
│   │   ├── Button.stories.ts
│   │   ├── Button.tsx
│   │   ├── Configure.mdx
│   │   ├── Dashboard.stories.tsx
│   │   ├── Demos.stories.tsx
│   │   ├── DesignSystem.stories.tsx
│   │   ├── header.css
│   │   ├── Header.stories.ts
│   │   ├── Header.tsx
│   │   ├── Layout.stories.tsx
│   │   ├── Mixes.stories.tsx
│   │   ├── MusicLibraryBrowser.stories.tsx
│   │   ├── page.css
│   │   ├── Page.stories.ts
│   │   ├── Page.tsx
│   │   ├── Settings.stories.tsx
│   │   ├── Tracks.stories.tsx
│   │   ├── UI.stories.tsx
│   │   ├── UIComponents.stories.tsx
│   │   ├── UIPrimitives.stories.tsx
│   │   ├── Utils.stories.tsx
│   │   ├── Visualizations.stories.tsx
│   │   └── assets/
│   ├── styles/
│   │   └── SPACING-STANDARDS.md
│   ├── test/
│   ├── types/
│   └── utils/
```

## Backend

```
backend/
├── __init__.py
├── __main__.py
├── .DS_Store
├── .env
├── =0.4.2
├── =0.4.9
├── =1.3.0
├── beat_grid_1.json
├── config.py
├── cue_points_extraction.log
├── dependencies.py
├── main.py
├── music_library.db
├── README.md
├── requirements-tts.txt
├── requirements.txt
├── test_cache_management.py
├── test_comprehensive_time_stretching.py
├── test_coqui_tts.py
├── test_enhanced_audio_processor.py
├── test_enhanced_beatgrid.py
├── test_enhanced_processor_simple.py
├── test_phase2_validation.py
├── test_python_stretch_simple.py
├── test_time_stretching_api.py
├── adapters/
│   ├── __init__.py
│   ├── .DS_Store
│   ├── adapter_factory.py
│   ├── base_adapter.py
│   ├── modular_adapter.py
│   └── smart_mix_adapter.py
├── cache/
│   └── tts/
├── data/
│   ├── ai_settings_monitoring.json
│   ├── ai_settings.json
│   ├── app_settings.json
│   ├── default_mix_cards.json
│   ├── mcp_servers.json
│   └── playlists/
├── db/
│   ├── __init__.py
│   ├── .DS_Store
│   ├── add_modular_style.py
│   ├── base.py
│   ├── database.py
│   ├── DB.md
│   ├── dj_mix_constructor.db
│   ├── harmonymix.db
│   ├── harmonymix.db.bak
│   ├── init_db.py
│   ├── migrate_mix_styles_version_to_text.sql
│   ├── seed_default_mix_cards.py
│   ├── session.py
│   ├── update_mix_styles.py
│   ├── update_schema.py
│   ├── migrations/
│   ├── models/
│   └── seed/
├── docs/
│   └── SPARK_TTS_OPTIMIZATION.md
├── lib/
│   └── sparktts
├── middleware/
│   └── ai_request_logger.py
├── migrations/
│   ├── .DS_Store
│   ├── add_enhanced_beat_grid_columns.py
│   ├── add_source_tracking_fields.py
│   └── migrate_hardcoded_styles.py
├── models/
│   ├── __init__.py
│   ├── .DS_Store
│   ├── analysis.py
│   ├── beat_grid.py
│   ├── collection.py
│   ├── cue_point.py
│   ├── export_job.py
│   ├── folder.py
│   ├── genre.py
│   ├── health_score.py
│   ├── mix_style.py
│   ├── mix_track.py
│   ├── mix.py
│   ├── MODELS.md
│   ├── playlist_track.py
│   ├── playlist.py
│   ├── style_analytics.py
│   ├── style_template.py
│   ├── suggestion_interaction.py
│   ├── track_analysis.py
│   ├── track_segment.py
│   ├── track.py
│   ├── transition.py
│   ├── user_preferences.py
│   └── user.py
├── pretrained_models/
│   └── Spark-TTS-0.5B/
├── repositories/
│   ├── __init__.py
│   ├── .DS_Store
│   ├── collection_repository.py
│   ├── playlist_repository.py
│   ├── REPOSITORIES.md
│   └── track_repository.py
├── routers/
├── routes/
│   ├── __init__.py
│   ├── .DS_Store
│   ├── ai_api.py
│   ├── ai_mcp_async.py
│   ├── ai_mcp_optimization.py
│   ├── ai_mcp_registry.py
│   ├── ai_mcp.py
│   ├── ai_monitoring.py
│   ├── ai_optimization.py
│   ├── ai_settings.py
│   ├── analysis.py
│   ├── app_settings.py
│   ├── audio.py
│   ├── auth.py
│   ├── beat_alignment.py
│   ├── beat_grid.py
│   ├── collection.py
│   ├── compatibility.py
│   ├── cue_points.py
│   ├── directory_import.py
│   ├── folder.py
│   ├── ideas.py
│   ├── mcp.py
│   ├── mix_styles.py
│   ├── mix_timeline.py
│   ├── mix.py
│   ├── modular.py
│   ├── multimodal_ai.py
│   ├── playlists.py
│   ├── ROUTES.md
│   ├── style_analytics.py
│   ├── style_compatibility.py
│   ├── style_documentation.py
│   ├── style_recommendations.py
│   ├── style_templates.py
│   ├── style_usage.py
│   ├── styles.py
│   ├── test_mix_styles_import.py
│   ├── time_stretching.py
│   ├── track_analysis_management.py
│   ├── track_segments.py
│   ├── tracks.py
│   ├── transition.py
│   ├── tts.py
│   ├── user_preferences.py
│   └── ai/
├── schemas/
│   ├── __init__.py
│   ├── .DS_Store
│   ├── analysis.py
│   ├── auth.py
│   ├── beat_grid.py
│   ├── collection.py
│   ├── cue_point.py
│   ├── folder.py
│   ├── mix_timeline.py
│   ├── mix.py
│   ├── playlist.py
│   ├── SCHEMAS.md
│   ├── style_documentation.py
│   ├── style_recommendations.py
│   ├── style_sharing.py
│   ├── style_usage.py
│   ├── track_analysis.py
│   ├── track_segment.py
│   ├── track.py
│   ├── transition.py
│   └── user_preferences.py
├── scripts/
│   ├── add_cue_points_column.py
│   ├── add_genre_field.py
│   ├── add_librosa_fields.py
│   ├── add_mixed_in_key_fields.py
│   ├── add_version_column.py
│   ├── extract_beat_grids_for_all_tracks.py
│   ├── extract_mixed_in_key_metadata.py
│   ├── optimize_tts_performance.py
│   ├── remove_cue_points_column.py
│   ├── reset_tracks_database.py
│   ├── test_cue_points_extraction.py
│   ├── update_db_schema.py
│   ├── update_mix_styles_sql.py
│   ├── update_mix_styles.py
│   └── update_track_model.py
├── services/
│   └── .DS_Store
│   └── AUDIO_ANALYZER.md
├── static/
├── templates/
├── tests/
├── uploads/
└── utils/
